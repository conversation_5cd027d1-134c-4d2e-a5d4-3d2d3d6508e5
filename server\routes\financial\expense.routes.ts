import { Express, Response } from 'express';
import { storage } from '../../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../../middleware/auth';
import { insertExpenseSchema } from '@shared/schema';
import { ZodError } from 'zod';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

// Expense filters interface
interface ExpenseFilters {
  startDate?: string;
  endDate?: string;
  type?: string;
  branchId?: number;
}

export function registerExpenseRoutes(app: Express): void {
  // Get all expenses for a company
  app.get('/api/companies/:companyId/expenses', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract filter parameters
      const filters: ExpenseFilters = {};

      if (req.query.startDate && typeof req.query.startDate === 'string') {
        filters.startDate = req.query.startDate;
      }

      if (req.query.endDate && typeof req.query.endDate === 'string') {
        filters.endDate = req.query.endDate;
      }

      if (req.query.type && typeof req.query.type === 'string') {
        filters.type = req.query.type;
      }

      if (req.query.branchId && typeof req.query.branchId === 'string') {
        filters.branchId = parseInt(req.query.branchId);
      }

      const expenses = await storage.getExpenses(companyId, filters);
      return res.json(expenses);
    } catch (error) {
      console.error('Error fetching expenses:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get expense by ID
  app.get('/api/expenses/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const expenseId = parseInt(req.params.id);
      
      const expense = await storage.getExpenseById(expenseId);
      
      if (!expense) {
        return res.status(404).json({ message: 'Expense not found' });
      }
      
      // Check if user has access to this expense's company
      if (req.user!.role !== 'saas_admin' && expense.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === expense.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this expense' });
        }
      }

      return res.json(expense);
    } catch (error) {
      console.error(`Error fetching expense ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create expense
  app.post('/api/companies/:companyId/expenses', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Add company_id to the request body
      const dataWithCompanyId = {
        ...req.body,
        company_id: companyId
      };

      // Validate expense data with company_id added
      const result = insertExpenseSchema.safeParse(dataWithCompanyId);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid expense data',
          errors: formatZodError(result.error)
        });
      }

      // Create expense
      const expense = await storage.createExpense(result.data);
      return res.status(201).json(expense);
    } catch (error) {
      console.error('Error creating expense:', error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Update expense
  app.patch('/api/expenses/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const expenseId = parseInt(req.params.id);
      
      // Get existing expense
      const existingExpense = await storage.getExpenseById(expenseId);
      
      if (!existingExpense) {
        return res.status(404).json({ message: 'Expense not found' });
      }
      
      // Check if user has access to this expense's company
      if (req.user!.role !== 'saas_admin' && existingExpense.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === existingExpense.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this expense' });
        }
      }
      
      // Validate the update data
      const result = insertExpenseSchema.partial().safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid expense data',
          errors: formatZodError(result.error)
        });
      }
      
      // Don't allow changing company_id
      delete result.data.company_id;
      
      const updatedExpense = await storage.updateExpense(expenseId, result.data);
      return res.json(updatedExpense);
    } catch (error) {
      console.error(`Error updating expense ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Delete expense
  app.delete('/api/expenses/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const expenseId = parseInt(req.params.id);
      
      // Get existing expense
      const existingExpense = await storage.getExpenseById(expenseId);
      
      if (!existingExpense) {
        return res.status(404).json({ message: 'Expense not found' });
      }
      
      // Check if user has access to this expense's company
      if (req.user!.role !== 'saas_admin' && existingExpense.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === existingExpense.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this expense' });
        }
      }
      
      await storage.deleteExpense(expenseId);
      return res.status(204).send();
    } catch (error) {
      console.error(`Error deleting expense ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });
}
