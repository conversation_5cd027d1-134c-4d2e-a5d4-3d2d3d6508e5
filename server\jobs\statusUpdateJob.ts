/**
 * Status Update Job
 *
 * This job runs daily to update collection statuses based on dates.
 * It uses the CollectionStatusService to:
 * - Update "Pending" collections to "Due" when the scheduled date arrives
 * - Update "Due" collections to "Overdue" when they are past due
 */

import { collectionStatusService } from '../services/collectionStatusService';
import errorLogger from '../utils/errorLogger';

export async function runStatusUpdateJob(): Promise<void> {
  try {
    errorLogger.logInfo('Starting collection status update job', 'status-update-job');

    const result = await collectionStatusService.updateCollectionStatuses();

    errorLogger.logInfo(
      `Collection status update job completed: ${result.updated} collections updated, ${result.errors} errors`,
      'status-update-job',
      { updated: result.updated, errors: result.errors }
    );
  } catch (error) {
    errorLogger.logError('Error running status update job', 'status-update-job', error as Error);
  }
}

// If this file is run directly (e.g., via node jobs/statusUpdateJob.js)
// Console logging is appropriate for standalone script execution
if (require.main === module) {
  runStatusUpdateJob()
    .then(() => {
      console.log('Status update job completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Status update job failed:', error);
      process.exit(1);
    });
}
