-- Migration: 012_temporary_permissions.sql
-- Description: Add temporary permission elevation system with approval workflow and emergency access

-- Create enum for elevation request status
CREATE TYPE elevation_status AS ENUM ('pending', 'approved', 'denied', 'expired', 'revoked');

-- Create enum for elevation priority
CREATE TYPE elevation_priority AS ENUM ('low', 'medium', 'high', 'emergency');

-- Create temporary permissions table for time-limited elevated access
CREATE TABLE temporary_permissions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  permission_id INTEGER NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  granted_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  granted_at TIMESTAMP DEFAULT NOW() NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  revoked_at TIMESTAMP,
  revoked_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
  revoke_reason TEXT,
  is_emergency BOOLEAN DEFAULT FALSE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT check_expires_after_granted CHECK (expires_at > granted_at),
  CONSTRAINT check_revoked_after_granted CHECK (revoked_at IS NULL OR revoked_at >= granted_at)
);

-- Create permission elevation requests table for approval workflow
CREATE TABLE permission_elevation_requests (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  requested_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  permission_id INTEGER NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  justification TEXT,
  priority elevation_priority DEFAULT 'medium' NOT NULL,
  duration_hours INTEGER NOT NULL DEFAULT 24,
  status elevation_status DEFAULT 'pending' NOT NULL,
  requested_at TIMESTAMP DEFAULT NOW() NOT NULL,
  reviewed_at TIMESTAMP,
  reviewed_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
  review_notes TEXT,
  approved_until TIMESTAMP,
  is_emergency BOOLEAN DEFAULT FALSE NOT NULL,
  emergency_contact TEXT,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT check_duration_positive CHECK (duration_hours > 0),
  CONSTRAINT check_duration_reasonable CHECK (duration_hours <= 8760), -- Max 1 year
  CONSTRAINT check_reviewed_when_not_pending CHECK (
    (status = 'pending') OR 
    (status != 'pending' AND reviewed_at IS NOT NULL AND reviewed_by IS NOT NULL)
  )
);

-- Create emergency access logs table for tracking emergency access usage
CREATE TABLE emergency_access_logs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  permission_code TEXT NOT NULL,
  action_performed TEXT NOT NULL,
  emergency_reason TEXT NOT NULL,
  access_granted_at TIMESTAMP DEFAULT NOW() NOT NULL,
  session_id TEXT,
  ip_address INET,
  user_agent TEXT,
  resource_accessed TEXT,
  additional_context JSONB,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create indexes for performance
CREATE INDEX idx_temporary_permissions_user_id ON temporary_permissions(user_id);
CREATE INDEX idx_temporary_permissions_expires_at ON temporary_permissions(expires_at);
CREATE INDEX idx_temporary_permissions_active ON temporary_permissions(user_id, permission_id) 
  WHERE revoked_at IS NULL AND expires_at > NOW();

CREATE INDEX idx_elevation_requests_user_id ON permission_elevation_requests(user_id);
CREATE INDEX idx_elevation_requests_status ON permission_elevation_requests(status);
CREATE INDEX idx_elevation_requests_priority ON permission_elevation_requests(priority);
CREATE INDEX idx_elevation_requests_pending ON permission_elevation_requests(status, requested_at) 
  WHERE status = 'pending';

CREATE INDEX idx_emergency_access_logs_user_id ON emergency_access_logs(user_id);
CREATE INDEX idx_emergency_access_logs_timestamp ON emergency_access_logs(access_granted_at);

-- Add comments for documentation
COMMENT ON TABLE temporary_permissions IS 'Time-limited elevated permissions granted to users';
COMMENT ON COLUMN temporary_permissions.is_emergency IS 'Whether this permission was granted for emergency access';
COMMENT ON COLUMN temporary_permissions.expires_at IS 'When this temporary permission expires';
COMMENT ON COLUMN temporary_permissions.revoked_at IS 'When this permission was manually revoked (if applicable)';

COMMENT ON TABLE permission_elevation_requests IS 'Requests for temporary permission elevation with approval workflow';
COMMENT ON COLUMN permission_elevation_requests.priority IS 'Priority level of the elevation request';
COMMENT ON COLUMN permission_elevation_requests.duration_hours IS 'Requested duration in hours';
COMMENT ON COLUMN permission_elevation_requests.is_emergency IS 'Whether this is an emergency access request';

COMMENT ON TABLE emergency_access_logs IS 'Audit log for emergency access usage';
COMMENT ON COLUMN emergency_access_logs.permission_code IS 'Permission code used for emergency access';
COMMENT ON COLUMN emergency_access_logs.action_performed IS 'Description of the action performed';

-- Create function to automatically expire temporary permissions
CREATE OR REPLACE FUNCTION cleanup_expired_temporary_permissions()
RETURNS INTEGER AS $$
DECLARE
  expired_count INTEGER;
BEGIN
  -- Count expired permissions that haven't been cleaned up
  SELECT COUNT(*) INTO expired_count
  FROM temporary_permissions
  WHERE expires_at <= NOW() AND revoked_at IS NULL;
  
  -- Update expired permissions to mark them as revoked
  UPDATE temporary_permissions
  SET 
    revoked_at = NOW(),
    revoke_reason = 'Automatically expired',
    updated_at = NOW()
  WHERE expires_at <= NOW() AND revoked_at IS NULL;
  
  RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to automatically update elevation request status
CREATE OR REPLACE FUNCTION update_elevation_request_status()
RETURNS TRIGGER AS $$
BEGIN
  -- If approved_until is set and has passed, mark as expired
  IF NEW.approved_until IS NOT NULL AND NEW.approved_until <= NOW() AND NEW.status = 'approved' THEN
    NEW.status = 'expired';
    NEW.updated_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic updates
CREATE TRIGGER update_temporary_permissions_updated_at 
  BEFORE UPDATE ON temporary_permissions 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_elevation_requests_updated_at 
  BEFORE UPDATE ON permission_elevation_requests 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER check_elevation_request_expiry
  BEFORE UPDATE ON permission_elevation_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_elevation_request_status();

-- Insert some example emergency permissions for demonstration
INSERT INTO permissions (code, name, description, category) VALUES
('emergency_loan_override', 'Emergency Loan Override', 'Override loan approval limits in emergency situations', 'emergency'),
('emergency_system_access', 'Emergency System Access', 'Full system access during emergencies', 'emergency'),
('emergency_data_export', 'Emergency Data Export', 'Export critical data during emergencies', 'emergency')
ON CONFLICT (code) DO NOTHING;

-- Create a view for active temporary permissions
CREATE VIEW active_temporary_permissions AS
SELECT 
  tp.*,
  u.username,
  u.email,
  p.code as permission_code,
  p.name as permission_name,
  gb.username as granted_by_username
FROM temporary_permissions tp
JOIN users u ON tp.user_id = u.id
JOIN permissions p ON tp.permission_id = p.id
JOIN users gb ON tp.granted_by = gb.id
WHERE tp.revoked_at IS NULL AND tp.expires_at > NOW();

-- Create a view for pending elevation requests
CREATE VIEW pending_elevation_requests AS
SELECT 
  per.*,
  u.username,
  u.email,
  rb.username as requested_by_username,
  p.code as permission_code,
  p.name as permission_name
FROM permission_elevation_requests per
JOIN users u ON per.user_id = u.id
JOIN users rb ON per.requested_by = rb.id
JOIN permissions p ON per.permission_id = p.id
WHERE per.status = 'pending'
ORDER BY 
  CASE per.priority 
    WHEN 'emergency' THEN 1
    WHEN 'high' THEN 2
    WHEN 'medium' THEN 3
    WHEN 'low' THEN 4
  END,
  per.requested_at ASC;
