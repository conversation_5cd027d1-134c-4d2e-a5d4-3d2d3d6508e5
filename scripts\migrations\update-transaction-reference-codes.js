#!/usr/bin/env node
/**
 * Migration Script: Update Transaction Reference Codes
 * Purpose: Update all existing transactions to have an empty value in the transaction_reference_code column
 * Usage: node scripts/migrations/update-transaction-reference-codes.js
 */

import { runMigration, logMigrationStats } from '../utils/migration-runner.js';

await runMigration('Update Transaction Reference Codes', async (pool, { dryRun }) => {
  // First, check if there are any transactions in the database
  const countResult = await pool.query('SELECT COUNT(*) FROM transactions');
  const transactionCount = parseInt(countResult.rows[0].count);
  
  console.log(`Found ${transactionCount} transactions in the database`);

  if (transactionCount === 0) {
    console.log('No transactions to update. Exiting.');
    return;
  }

  if (dryRun) {
    // Check how many transactions would be updated
    const nullCountResult = await pool.query(`
      SELECT COUNT(*) FROM transactions WHERE transaction_reference_code IS NULL
    `);
    const nullCount = parseInt(nullCountResult.rows[0].count);
    
    console.log(`Would update ${nullCount} transactions with NULL transaction_reference_code to empty string`);
    logMigrationStats({
      'Total transactions': transactionCount,
      'Transactions with NULL reference code': nullCount,
      'Transactions that would be updated': nullCount
    });
    return;
  }

  // Update all transactions to have an empty value in the transaction_reference_code column
  const updateResult = await pool.query(`
    UPDATE transactions
    SET transaction_reference_code = ''
    WHERE transaction_reference_code IS NULL
    RETURNING id
  `);

  const updatedCount = updateResult.rows.length;
  console.log(`Updated ${updatedCount} transactions with empty transaction_reference_code`);

  // Verify the update
  const verifyResult = await pool.query(`
    SELECT COUNT(*) FROM transactions WHERE transaction_reference_code = ''
  `);

  const verifiedCount = parseInt(verifyResult.rows[0].count);
  console.log(`Verified ${verifiedCount} transactions now have empty transaction_reference_code`);

  logMigrationStats({
    'Total transactions': transactionCount,
    'Transactions updated': updatedCount,
    'Transactions with empty reference code': verifiedCount
  });
});
