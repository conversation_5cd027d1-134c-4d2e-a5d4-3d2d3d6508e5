// Script to assign admin permissions to all owner users
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function assignAdminPermissionsToAll() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Assigning admin permissions to all owner users...');

    // Get all owner users
    const usersResult = await client.query(`
      SELECT id, email, company_id
      FROM users
      WHERE role = 'owner'
      ORDER BY id
    `);

    console.log('\n📋 Company owner users found:');
    usersResult.rows.forEach(user => {
      console.log(`  - ID: ${user.id}, Email: ${user.email}, Company: ${user.company_id}`);
    });
    
    // Process each company owner user
    for (const user of usersResult.rows) {
      console.log(`\n👤 Processing user: ${user.email} (Company: ${user.company_id})`);
      
      // Check if Admin role exists for this company
      let adminRoleResult = await client.query(`
        SELECT id FROM custom_roles 
        WHERE name = 'Admin' AND company_id = $1
      `, [user.company_id]);
      
      let adminRoleId;
      if (adminRoleResult.rows.length === 0) {
        console.log(`  🆕 Creating Admin role for company ${user.company_id}...`);
        const createRoleResult = await client.query(`
          INSERT INTO custom_roles (name, description, company_id, created_at, updated_at)
          VALUES ('Admin', 'Full administrative access', $1, NOW(), NOW())
          RETURNING id
        `, [user.company_id]);
        adminRoleId = createRoleResult.rows[0].id;
        console.log(`  ✅ Created Admin role with ID: ${adminRoleId}`);
        
        // Assign all basic permissions to this new admin role
        const essentialPermissions = [
          'user_view', 'user_create', 'user_edit', 'user_delete',
          'role_view', 'role_create', 'role_edit', 'role_delete',
          'group_view', 'group_create', 'group_edit', 'group_delete',
          'permission_view', 'permission_assign',
          'customer_view', 'customer_create', 'customer_edit',
          'loan_view', 'loan_create', 'loan_edit',
          'collection_view', 'collection_create', 'collection_edit'
        ];
        
        for (const permCode of essentialPermissions) {
          const permResult = await client.query(`
            SELECT id FROM permissions WHERE code = $1
          `, [permCode]);
          
          if (permResult.rows.length > 0) {
            const permId = permResult.rows[0].id;
            
            await client.query(`
              INSERT INTO role_permissions (role_id, permission_id, created_at)
              VALUES ($1, $2, NOW())
              ON CONFLICT DO NOTHING
            `, [adminRoleId, permId]);
          }
        }
        console.log(`  ✅ Assigned permissions to Admin role`);
      } else {
        adminRoleId = adminRoleResult.rows[0].id;
        console.log(`  ✅ Found existing Admin role with ID: ${adminRoleId}`);
      }
      
      // Assign admin role to user if not already assigned
      const existingUserRoleResult = await client.query(`
        SELECT id FROM user_roles 
        WHERE user_id = $1 AND role_id = $2
      `, [user.id, adminRoleId]);
      
      if (existingUserRoleResult.rows.length === 0) {
        await client.query(`
          INSERT INTO user_roles (user_id, role_id, created_at)
          VALUES ($1, $2, NOW())
        `, [user.id, adminRoleId]);
        console.log(`  ✅ Assigned Admin role to ${user.email}`);
      } else {
        console.log(`  ⏭️  ${user.email} already has Admin role`);
      }
    }
    
    console.log('\n🎉 All company owner users now have admin permissions!');
    console.log('\n💡 Please refresh your browser and try accessing the features again.');
    
  } catch (error) {
    console.error('❌ Error assigning permissions:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
assignAdminPermissionsToAll()
  .then(() => {
    console.log('\n✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
