import { Express, Response } from 'express';
import { authMiddleware, requirePermission, AuthRequest } from '../middleware/auth';
import { 
  permissions, 
  permissionConditions, 
  insertPermissionConditionSchema 
} from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { db } from '../db';

export function registerPermissionConditionsRoutes(app: Express): void {
  
  // Get all conditions for a permission by code
  app.get('/api/permissions/:permissionCode/conditions', 
    authMiddleware, 
    requirePermission('permission_view'), 
    async (req: AuthRequest, res: Response) => {
      try {
        const { permissionCode } = req.params;

        // Get permission ID from code
        const [permission] = await db
          .select({ id: permissions.id })
          .from(permissions)
          .where(eq(permissions.code, permissionCode))
          .limit(1);

        if (!permission) {
          return res.status(404).json({ message: 'Permission not found' });
        }

        // Get all conditions for this permission
        const conditions = await db
          .select()
          .from(permissionConditions)
          .where(eq(permissionConditions.permission_id, permission.id))
          .orderBy(permissionConditions.priority);

        return res.json(conditions);
      } catch (error) {
        console.error('Error fetching permission conditions:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Get all conditions for a permission by ID
  app.get('/api/permissions/:permissionId/conditions-by-id', 
    authMiddleware, 
    requirePermission('permission_view'), 
    async (req: AuthRequest, res: Response) => {
      try {
        const permissionId = parseInt(req.params.permissionId);

        if (isNaN(permissionId)) {
          return res.status(400).json({ message: 'Invalid permission ID' });
        }

        const conditions = await db
          .select()
          .from(permissionConditions)
          .where(eq(permissionConditions.permission_id, permissionId))
          .orderBy(permissionConditions.priority);

        return res.json(conditions);
      } catch (error) {
        console.error('Error fetching permission conditions:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Create a new permission condition
  app.post('/api/permission-conditions', 
    authMiddleware, 
    requirePermission('permission_assign'), 
    async (req: AuthRequest, res: Response) => {
      try {
        const result = insertPermissionConditionSchema.safeParse(req.body);

        if (!result.success) {
          return res.status(400).json({ 
            message: 'Invalid input', 
            errors: result.error.errors 
          });
        }

        // Verify permission exists
        const [permission] = await db
          .select()
          .from(permissions)
          .where(eq(permissions.id, result.data.permission_id))
          .limit(1);

        if (!permission) {
          return res.status(404).json({ message: 'Permission not found' });
        }

        // Create the condition
        const [condition] = await db
          .insert(permissionConditions)
          .values(result.data)
          .returning();

        return res.status(201).json(condition);
      } catch (error) {
        console.error('Error creating permission condition:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Update a permission condition
  app.put('/api/permission-conditions/:id', 
    authMiddleware, 
    requirePermission('permission_assign'), 
    async (req: AuthRequest, res: Response) => {
      try {
        const conditionId = parseInt(req.params.id);

        if (isNaN(conditionId)) {
          return res.status(400).json({ message: 'Invalid condition ID' });
        }

        const result = insertPermissionConditionSchema.partial().safeParse(req.body);

        if (!result.success) {
          return res.status(400).json({ 
            message: 'Invalid input', 
            errors: result.error.errors 
          });
        }

        // Check if condition exists
        const [existingCondition] = await db
          .select()
          .from(permissionConditions)
          .where(eq(permissionConditions.id, conditionId))
          .limit(1);

        if (!existingCondition) {
          return res.status(404).json({ message: 'Permission condition not found' });
        }

        // Update the condition
        const [updatedCondition] = await db
          .update(permissionConditions)
          .set({
            ...result.data,
            updated_at: new Date()
          })
          .where(eq(permissionConditions.id, conditionId))
          .returning();

        return res.json(updatedCondition);
      } catch (error) {
        console.error('Error updating permission condition:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Delete a permission condition
  app.delete('/api/permission-conditions/:id', 
    authMiddleware, 
    requirePermission('permission_assign'), 
    async (req: AuthRequest, res: Response) => {
      try {
        const conditionId = parseInt(req.params.id);

        if (isNaN(conditionId)) {
          return res.status(400).json({ message: 'Invalid condition ID' });
        }

        // Check if condition exists
        const [existingCondition] = await db
          .select()
          .from(permissionConditions)
          .where(eq(permissionConditions.id, conditionId))
          .limit(1);

        if (!existingCondition) {
          return res.status(404).json({ message: 'Permission condition not found' });
        }

        // Delete the condition
        await db
          .delete(permissionConditions)
          .where(eq(permissionConditions.id, conditionId));

        return res.json({ message: 'Permission condition deleted successfully' });
      } catch (error) {
        console.error('Error deleting permission condition:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Bulk save conditions for a permission
  app.post('/api/permissions/:permissionCode/conditions/bulk', 
    authMiddleware, 
    requirePermission('permission_assign'), 
    async (req: AuthRequest, res: Response) => {
      try {
        const { permissionCode } = req.params;
        const { conditions } = req.body;

        if (!Array.isArray(conditions)) {
          return res.status(400).json({ message: 'Conditions must be an array' });
        }

        // Get permission ID from code
        const [permission] = await db
          .select({ id: permissions.id })
          .from(permissions)
          .where(eq(permissions.code, permissionCode))
          .limit(1);

        if (!permission) {
          return res.status(404).json({ message: 'Permission not found' });
        }

        // Start transaction
        await db.transaction(async (tx) => {
          // Delete existing conditions for this permission
          await tx
            .delete(permissionConditions)
            .where(eq(permissionConditions.permission_id, permission.id));

          // Insert new conditions if any
          if (conditions.length > 0) {
            const conditionsToInsert = conditions.map((condition: any) => ({
              ...condition,
              permission_id: permission.id,
              id: undefined // Remove ID to let database generate new ones
            }));

            // Validate each condition
            for (const condition of conditionsToInsert) {
              const result = insertPermissionConditionSchema.safeParse(condition);
              if (!result.success) {
                throw new Error(`Invalid condition: ${result.error.errors[0].message}`);
              }
            }

            await tx
              .insert(permissionConditions)
              .values(conditionsToInsert);
          }
        });

        // Fetch and return the updated conditions
        const updatedConditions = await db
          .select()
          .from(permissionConditions)
          .where(eq(permissionConditions.permission_id, permission.id))
          .orderBy(permissionConditions.priority);

        return res.json({
          message: 'Permission conditions saved successfully',
          conditions: updatedConditions
        });
      } catch (error) {
        console.error('Error bulk saving permission conditions:', error);
        return res.status(500).json({ 
          message: 'Server error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  );

  // Get condition types and their schemas
  app.get('/api/permission-conditions/types', 
    authMiddleware, 
    requirePermission('permission_view'), 
    async (req: AuthRequest, res: Response) => {
      try {
        const conditionTypes = [
          {
            type: 'time',
            name: 'Time-based',
            description: 'Restrict access by time and days',
            schema: {
              start_time: { type: 'string', format: 'time', description: 'Start time (HH:MM)' },
              end_time: { type: 'string', format: 'time', description: 'End time (HH:MM)' },
              days: { type: 'array', items: { type: 'string' }, description: 'Allowed days of week' },
              timezone: { type: 'string', description: 'Timezone for time evaluation' }
            }
          },
          {
            type: 'location',
            name: 'Location-based',
            description: 'Restrict access by IP or geography',
            schema: {
              allowed_ip_ranges: { type: 'array', items: { type: 'string' }, description: 'Allowed IP ranges (CIDR)' },
              allowed_countries: { type: 'array', items: { type: 'string' }, description: 'Allowed country codes' },
              blocked_countries: { type: 'array', items: { type: 'string' }, description: 'Blocked country codes' },
              require_vpn: { type: 'boolean', description: 'Require VPN connection' }
            }
          },
          {
            type: 'amount',
            name: 'Amount-based',
            description: 'Restrict based on transaction amounts',
            schema: {
              min_amount: { type: 'number', description: 'Minimum allowed amount' },
              max_amount: { type: 'number', description: 'Maximum allowed amount' },
              currency: { type: 'string', description: 'Currency code' }
            }
          },
          {
            type: 'approval',
            name: 'Approval-based',
            description: 'Require approval for certain operations',
            schema: {
              requires_approval: { type: 'boolean', description: 'Whether approval is required' },
              approval_threshold: { type: 'number', description: 'Amount threshold for approval' },
              auto_approve_below: { type: 'number', description: 'Auto-approve below this amount' },
              approver_roles: { type: 'array', items: { type: 'string' }, description: 'Roles that can approve' }
            }
          },
          {
            type: 'device',
            name: 'Device-based',
            description: 'Restrict access by device type',
            schema: {
              allowed_device_types: { type: 'array', items: { type: 'string' }, description: 'Allowed device types' },
              blocked_device_types: { type: 'array', items: { type: 'string' }, description: 'Blocked device types' },
              require_registered_device: { type: 'boolean', description: 'Require device registration' },
              max_devices_per_user: { type: 'number', description: 'Maximum devices per user' }
            }
          },
          {
            type: 'session',
            name: 'Session-based',
            description: 'Require fresh authentication or MFA',
            schema: {
              max_session_age: { type: 'number', description: 'Maximum session age in seconds' },
              require_mfa: { type: 'boolean', description: 'Require multi-factor authentication' },
              require_fresh_auth: { type: 'boolean', description: 'Require fresh authentication' },
              max_idle_time: { type: 'number', description: 'Maximum idle time in seconds' }
            }
          }
        ];

        return res.json(conditionTypes);
      } catch (error) {
        console.error('Error fetching condition types:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );
}
