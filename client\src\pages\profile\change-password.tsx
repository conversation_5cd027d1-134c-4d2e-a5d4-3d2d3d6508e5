import React, { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Loader2, Save, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';

interface PasswordPolicy {
  min_length: number;
  require_uppercase: boolean;
  require_lowercase: boolean;
  require_number: boolean;
  require_special: boolean;
}

interface PasswordStatus {
  expired: boolean;
  expiration_days: number;
  policy: PasswordPolicy;
}

export default function ChangePassword() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [passwordStatus, setPasswordStatus] = useState<PasswordStatus | null>(null);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Fetch password status
  useEffect(() => {
    const fetchPasswordStatus = async () => {
      try {
        const res = await apiRequest('GET', '/api/auth/password-status');
        const data = await res.json();
        setPasswordStatus(data);
      } catch (error) {
        console.error('Error fetching password status:', error);
      }
    };

    fetchPasswordStatus();
  }, []);

  // Handle form changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({ ...prev, [name]: value }));

    if (name === 'newPassword') {
      validatePassword(value);
    }
  };

  // Validate password against policy
  const validatePassword = (password: string) => {
    if (!passwordStatus) return;

    const errors: string[] = [];
    let strength = 0;

    // Check minimum length
    if (password.length < passwordStatus.policy.min_length) {
      errors.push(`Password must be at least ${passwordStatus.policy.min_length} characters long`);
    } else {
      strength += 20;
    }

    // Check for uppercase letters
    if (passwordStatus.policy.require_uppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else if (passwordStatus.policy.require_uppercase) {
      strength += 20;
    }

    // Check for lowercase letters
    if (passwordStatus.policy.require_lowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else if (passwordStatus.policy.require_lowercase) {
      strength += 20;
    }

    // Check for numbers
    if (passwordStatus.policy.require_number && !/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    } else if (passwordStatus.policy.require_number) {
      strength += 20;
    }

    // Check for special characters
    if (passwordStatus.policy.require_special && !/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    } else if (passwordStatus.policy.require_special) {
      strength += 20;
    }

    setValidationErrors(errors);
    setPasswordStrength(strength);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validationErrors.length > 0) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the password validation errors before submitting.',
        variant: 'destructive',
      });
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: 'Passwords do not match',
        description: 'New password and confirmation password must match.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const res = await apiRequest('POST', '/api/auth/change-password', {
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword,
        confirmPassword: passwordForm.confirmPassword,
      });

      if (res.ok) {
        toast({
          title: 'Password Changed',
          description: 'Your password has been changed successfully.',
        });

        // Reset form
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        setPasswordStrength(0);
      } else {
        const errorData = await res.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to change password.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error changing password:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get strength color
  const getStrengthColor = () => {
    if (passwordStrength < 40) return 'bg-red-500';
    if (passwordStrength < 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Get strength label
  const getStrengthLabel = () => {
    if (passwordStrength < 40) return 'Weak';
    if (passwordStrength < 80) return 'Medium';
    return 'Strong';
  };

  return (
    <div className="container mx-auto py-6">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Change Password</CardTitle>
          <CardDescription>Update your password to keep your account secure.</CardDescription>
        </CardHeader>

        {passwordStatus?.expired && (
          <div className="px-6">
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Password Expired</AlertTitle>
              <AlertDescription>
                Your password has expired. Please change it to continue using the application.
              </AlertDescription>
            </Alert>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Current Password</Label>
              <Input
                id="currentPassword"
                name="currentPassword"
                type="password"
                value={passwordForm.currentPassword}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <Input
                id="newPassword"
                name="newPassword"
                type="password"
                value={passwordForm.newPassword}
                onChange={handleInputChange}
                required
              />

              {passwordForm.newPassword && (
                <div className="mt-2">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs">Password Strength: {getStrengthLabel()}</span>
                    <span className="text-xs">{passwordStrength}%</span>
                  </div>
                  <Progress value={passwordStrength} className={getStrengthColor()} />
                </div>
              )}

              {validationErrors.length > 0 && (
                <div className="mt-2 space-y-1">
                  {validationErrors.map((error, index) => (
                    <p key={index} className="text-xs text-red-500 flex items-center">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      {error}
                    </p>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={passwordForm.confirmPassword}
                onChange={handleInputChange}
                required
              />

              {passwordForm.confirmPassword && passwordForm.newPassword !== passwordForm.confirmPassword && (
                <p className="text-xs text-red-500 flex items-center mt-1">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Passwords do not match
                </p>
              )}
            </div>

            {passwordStatus && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Password Requirements</AlertTitle>
                <AlertDescription>
                  <ul className="text-xs list-disc pl-5 mt-2">
                    <li>At least {passwordStatus.policy.min_length} characters long</li>
                    {passwordStatus.policy.require_uppercase && <li>At least one uppercase letter</li>}
                    {passwordStatus.policy.require_lowercase && <li>At least one lowercase letter</li>}
                    {passwordStatus.policy.require_number && <li>At least one number</li>}
                    {passwordStatus.policy.require_special && <li>At least one special character</li>}
                    <li>Passwords expire after {passwordStatus.expiration_days} days</li>
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>

          <CardFooter>
            <Button type="submit" disabled={isLoading || validationErrors.length > 0}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Changing Password...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Change Password
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
