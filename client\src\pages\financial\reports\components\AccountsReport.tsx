import React, { useState } from 'react';
import { format } from 'date-fns';
import { useReportData } from '../hooks/useReportData';
import { AccountBalanceReport } from '../types';
import { DateRangePicker } from './DateRangePicker';
import { ReportDownloadButton } from './ReportDownloadButton';
import { ReportCard } from './ReportCard';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { LineChart } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface AccountsReportProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  onDateRangeChange: (range: { from: Date; to: Date }) => void;
}

export function AccountsReport({
  dateRange,
  onDateRangeChange,
}: AccountsReportProps) {
  const [accountTypeFilter, setAccountTypeFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');

  const { data, isLoading, isError, refetch } = useReportData<AccountBalanceReport[]>({
    reportType: 'accounts',
    startDate: dateRange.from,
    endDate: dateRange.to,
  });

  // Filter accounts based on search term and account type
  const filteredAccounts = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.filter((account) => {
      if (!account || typeof account !== 'object') return false;

      const matchesSearch =
        searchTerm === '' ||
        (account.accountName && account.accountName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (account.accountCode && account.accountCode.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesType =
        accountTypeFilter === 'all' || account.accountType === accountTypeFilter;

      return matchesSearch && matchesType;
    });
  }, [data, searchTerm, accountTypeFilter]);

  // Get unique account types for filter
  const accountTypes = React.useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    const types = new Set<string>();
    data.forEach((account) => {
      if (account && account.accountType) {
        types.add(account.accountType);
      }
    });

    return Array.from(types);
  }, [data]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <DateRangePicker
          dateRange={dateRange}
          onDateRangeChange={onDateRangeChange}
        />
        <div className="flex gap-2">
          <ReportDownloadButton
            reportType="accounts"
            format="pdf"
            startDate={dateRange.from}
            endDate={dateRange.to}
            disabled={isLoading || isError}
          />
          <ReportDownloadButton
            reportType="accounts"
            format="csv"
            startDate={dateRange.from}
            endDate={dateRange.to}
            disabled={isLoading || isError}
          />
        </div>
      </div>

      <ReportCard
        title="Account Balances"
        description={`For the period ${format(dateRange.from, 'LLL dd, y')} to ${format(dateRange.to, 'LLL dd, y')}`}
        isLoading={isLoading}
        isError={isError}
        onRetry={() => refetch()}
      >
        {data && Array.isArray(data) && (
          <div className="space-y-6">
            {/* Filters */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-1/3">
                <Input
                  placeholder="Search accounts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="w-full md:w-1/3">
                <Select
                  value={accountTypeFilter}
                  onValueChange={setAccountTypeFilter}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by account type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Account Types</SelectItem>
                    {accountTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Summary */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center">
                <LineChart className="h-5 w-5 text-blue-500 mr-2" />
                <h3 className="text-sm font-medium">
                  Showing {filteredAccounts.length} of {data && Array.isArray(data) ? data.length : 0} accounts
                </h3>
              </div>
            </div>

            {/* Accounts Table */}
            <div className="border rounded-md">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Code</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Balance</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debits</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credits</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Closing Balance</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAccounts.map((account, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{account.accountCode || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{account.accountName || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{account.accountType || 'N/A'}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">${(account.openingBalance || 0).toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">${(account.totalDebits || 0).toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">${(account.totalCredits || 0).toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900">${(account.closingBalance || 0).toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredAccounts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No accounts found matching your filters.
              </div>
            )}
          </div>
        )}
      </ReportCard>
    </div>
  );
}
