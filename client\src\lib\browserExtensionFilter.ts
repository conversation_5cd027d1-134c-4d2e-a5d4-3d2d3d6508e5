/**
 * Browser Extension Error Filter
 * 
 * This module helps filter out errors and console noise from browser extensions
 * and external scripts that are not part of our application.
 */

/**
 * Common browser extension error patterns
 */
export const BROWSER_EXTENSION_PATTERNS = [
  // Chrome extension errors
  'chrome-extension://',
  'chrome.runtime',
  'chrome.tabs',
  'chrome.storage',
  
  // Firefox extension errors
  'moz-extension://',
  'browser.runtime',
  'browser.tabs',
  
  // Safari extension errors
  'safari-extension://',
  'safari.extension',
  
  // Edge extension errors
  'ms-browser-extension://',
  
  // Common extension-related errors
  'Extension context invalidated',
  'Could not establish connection',
  'Receiving end does not exist',
  'A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received',
  'The message port closed before a response was received',
  'Cannot access contents of',
  'Script error.',
  
  // Content script injection errors
  'Unchecked runtime.lastError',
  'The extensions gallery cannot be scripted',
  'Cannot access a chrome://',
  'Cannot access a chrome-extension://',
];

/**
 * External service patterns that should be filtered
 */
export const EXTERNAL_SERVICE_PATTERNS = [
  // Replit development environment
  'replit.com',
  'replit-dev-banner',
  'replit.dev',
  
  // Analytics and tracking services
  'google-analytics.com',
  'googletagmanager.com',
  'facebook.com/tr',
  'doubleclick.net',
  
  // CDN and external resources
  'cdn.jsdelivr.net',
  'unpkg.com',
  'cdnjs.cloudflare.com',
  
  // Social media widgets
  'platform.twitter.com',
  'connect.facebook.net',
  'apis.google.com',
];

/**
 * Network error patterns that are typically not actionable
 */
export const NETWORK_ERROR_PATTERNS = [
  'Failed to fetch',
  'NetworkError',
  'ERR_NETWORK_CHANGED',
  'ERR_INTERNET_DISCONNECTED',
  'ERR_CONNECTION_RESET',
  'ERR_CONNECTION_REFUSED',
  'ERR_NAME_NOT_RESOLVED',
  'net::ERR_FAILED',
  'CORS policy',
  'Cross-Origin Request Blocked',
];

/**
 * Check if an error originates from a browser extension
 */
export function isBrowserExtensionError(
  message: string, 
  source?: string, 
  stack?: string
): boolean {
  const textToCheck = [message, source, stack].filter(Boolean).join(' ').toLowerCase();
  
  return BROWSER_EXTENSION_PATTERNS.some(pattern => 
    textToCheck.includes(pattern.toLowerCase())
  );
}

/**
 * Check if an error originates from an external service
 */
export function isExternalServiceError(
  message: string, 
  source?: string, 
  stack?: string
): boolean {
  const textToCheck = [message, source, stack].filter(Boolean).join(' ').toLowerCase();
  
  return EXTERNAL_SERVICE_PATTERNS.some(pattern => 
    textToCheck.includes(pattern.toLowerCase())
  );
}

/**
 * Check if an error is a network error that's not actionable
 */
export function isNetworkError(
  message: string, 
  source?: string, 
  stack?: string
): boolean {
  const textToCheck = [message, source, stack].filter(Boolean).join(' ').toLowerCase();
  
  return NETWORK_ERROR_PATTERNS.some(pattern => 
    textToCheck.includes(pattern.toLowerCase())
  );
}

/**
 * Main function to determine if an error should be filtered out
 */
export function shouldFilterError(
  message: string, 
  source?: string, 
  stack?: string,
  options: {
    filterExtensions?: boolean;
    filterExternalServices?: boolean;
    filterNetworkErrors?: boolean;
  } = {}
): boolean {
  const {
    filterExtensions = true,
    filterExternalServices = true,
    filterNetworkErrors = true
  } = options;

  if (filterExtensions && isBrowserExtensionError(message, source, stack)) {
    return true;
  }

  if (filterExternalServices && isExternalServiceError(message, source, stack)) {
    return true;
  }

  if (filterNetworkErrors && isNetworkError(message, source, stack)) {
    return true;
  }

  return false;
}

/**
 * Enhanced console override to filter extension noise
 */
export function setupConsoleFiltering(): void {
  // Only filter in production or when explicitly enabled
  const shouldFilter = process.env.NODE_ENV === 'production' || 
                      localStorage.getItem('filter-extension-errors') === 'true';
  
  if (!shouldFilter) {
    return;
  }

  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  console.error = (...args: any[]) => {
    const message = args.join(' ');
    if (!shouldFilterError(message)) {
      originalConsoleError.apply(console, args);
    }
  };

  console.warn = (...args: any[]) => {
    const message = args.join(' ');
    if (!shouldFilterError(message)) {
      originalConsoleWarn.apply(console, args);
    }
  };
}

/**
 * Detect if browser extensions are present
 */
export function detectBrowserExtensions(): string[] {
  const extensions: string[] = [];
  
  // Check for common extension APIs
  if (typeof (window as any).chrome?.runtime !== 'undefined') {
    extensions.push('Chrome Extension API detected');
  }
  
  if (typeof (window as any).browser?.runtime !== 'undefined') {
    extensions.push('WebExtensions API detected');
  }
  
  // Check for extension-injected elements
  const extensionElements = document.querySelectorAll('[data-extension], [class*="extension"], [id*="extension"]');
  if (extensionElements.length > 0) {
    extensions.push(`${extensionElements.length} extension elements detected`);
  }
  
  return extensions;
}

/**
 * Log browser extension detection results
 */
export function logExtensionDetection(): void {
  const extensions = detectBrowserExtensions();
  if (extensions.length > 0) {
    console.debug('Browser extensions detected:', extensions);
    console.debug('Extension-related errors will be filtered from console output');
  }
}
