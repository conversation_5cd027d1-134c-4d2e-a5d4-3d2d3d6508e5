import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Shield, 
  Smartphone, 
  Mail, 
  Key, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Settings,
  Download,
  RefreshCw
} from 'lucide-react';
import { MFASetup } from '../auth/MFASetup';

interface SecuritySettingsProps {
  userId?: number;
}

interface MFAStatus {
  enabled: boolean;
  hasBackupCodes: boolean;
  backupCodesCount: number;
  setupDate?: string;
}

interface SecuritySettings {
  emailVerified: boolean;
  mfaEnabled: boolean;
  lastPasswordChange?: string;
  loginNotifications: boolean;
  securityAlerts: boolean;
}

export function SecuritySettings({ userId }: SecuritySettingsProps) {
  const [mfaStatus, setMfaStatus] = useState<MFAStatus | null>(null);
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings | null>(null);
  const [showMFASetup, setShowMFASetup] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchSecurityStatus();
  }, [userId]);

  const fetchSecurityStatus = async () => {
    try {
      setLoading(true);
      
      // Fetch MFA status
      const mfaResponse = await fetch('/api/auth/mfa/status', {
        credentials: 'include'
      });
      
      if (mfaResponse.ok) {
        const mfaResult = await mfaResponse.json();
        if (mfaResult.success) {
          setMfaStatus(mfaResult.data);
        }
      }

      // Fetch general security settings
      const settingsResponse = await fetch('/api/auth/security-settings', {
        credentials: 'include'
      });
      
      if (settingsResponse.ok) {
        const settingsResult = await settingsResponse.json();
        if (settingsResult.success) {
          setSecuritySettings(settingsResult.data);
        }
      }
    } catch (err) {
      setError('Failed to load security settings');
    } finally {
      setLoading(false);
    }
  };

  const handleMFAToggle = async () => {
    if (mfaStatus?.enabled) {
      // Disable MFA
      try {
        const response = await fetch('/api/auth/mfa/disable', {
          method: 'POST',
          credentials: 'include'
        });
        
        const result = await response.json();
        if (result.success) {
          setSuccess('Two-factor authentication has been disabled');
          fetchSecurityStatus();
        } else {
          setError(result.message || 'Failed to disable MFA');
        }
      } catch (err) {
        setError('Network error. Please try again.');
      }
    } else {
      // Enable MFA
      setShowMFASetup(true);
    }
  };

  const handleMFASetupComplete = () => {
    setShowMFASetup(false);
    setSuccess('Two-factor authentication has been enabled successfully');
    fetchSecurityStatus();
  };

  const regenerateBackupCodes = async () => {
    try {
      const response = await fetch('/api/auth/mfa/regenerate-backup-codes', {
        method: 'POST',
        credentials: 'include'
      });
      
      const result = await response.json();
      if (result.success) {
        setSuccess('New backup codes have been generated');
        fetchSecurityStatus();
        
        // Download the new codes
        const codes = result.data.backupCodes;
        const content = `TrackFina MFA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\n${codes.join('\n')}\n\nKeep these codes safe! Each code can only be used once.`;
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'trackfina-backup-codes.txt';
        a.click();
        URL.revokeObjectURL(url);
      } else {
        setError(result.message || 'Failed to regenerate backup codes');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    }
  };

  const updateNotificationSettings = async (setting: string, value: boolean) => {
    try {
      const response = await fetch('/api/auth/security-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ [setting]: value })
      });
      
      const result = await response.json();
      if (result.success) {
        setSecuritySettings(prev => prev ? { ...prev, [setting]: value } : null);
        setSuccess('Settings updated successfully');
      } else {
        setError(result.message || 'Failed to update settings');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (showMFASetup) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2 mb-6">
          <Settings className="h-6 w-6" />
          <h2 className="text-2xl font-bold">Security Settings</h2>
        </div>
        <MFASetup 
          onSetupComplete={handleMFASetupComplete}
          onCancel={() => setShowMFASetup(false)}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Settings className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Security Settings</h2>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Two-Factor Authentication */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Smartphone className="h-5 w-5" />
              <div>
                <CardTitle>Two-Factor Authentication</CardTitle>
                <CardDescription>
                  Add an extra layer of security to your account
                </CardDescription>
              </div>
            </div>
            <Badge variant={mfaStatus?.enabled ? "default" : "secondary"}>
              {mfaStatus?.enabled ? "Enabled" : "Disabled"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Enable Two-Factor Authentication</p>
              <p className="text-sm text-gray-600">
                Require a code from your phone in addition to your password
              </p>
            </div>
            <Switch
              checked={mfaStatus?.enabled || false}
              onCheckedChange={handleMFAToggle}
            />
          </div>

          {mfaStatus?.enabled && (
            <>
              <Separator />
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Backup Codes</p>
                    <p className="text-sm text-gray-600">
                      {mfaStatus.backupCodesCount} backup codes remaining
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={regenerateBackupCodes}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate
                  </Button>
                </div>
                
                {mfaStatus.setupDate && (
                  <p className="text-xs text-gray-500">
                    Enabled on {new Date(mfaStatus.setupDate).toLocaleDateString()}
                  </p>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Email Verification */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5" />
              <div>
                <CardTitle>Email Verification</CardTitle>
                <CardDescription>
                  Verify your email address for account security
                </CardDescription>
              </div>
            </div>
            <Badge variant={securitySettings?.emailVerified ? "default" : "destructive"}>
              {securitySettings?.emailVerified ? "Verified" : "Unverified"}
            </Badge>
          </div>
        </CardHeader>
        {!securitySettings?.emailVerified && (
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-red-600">Email not verified</p>
                <p className="text-sm text-gray-600">
                  Please check your email and click the verification link
                </p>
              </div>
              <Button variant="outline" size="sm">
                Resend Email
              </Button>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Password Security */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Key className="h-5 w-5" />
            <div>
              <CardTitle>Password Security</CardTitle>
              <CardDescription>
                Manage your password and security preferences
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Password</p>
              <p className="text-sm text-gray-600">
                {securitySettings?.lastPasswordChange 
                  ? `Last changed ${new Date(securitySettings.lastPasswordChange).toLocaleDateString()}`
                  : 'Change your password regularly for better security'
                }
              </p>
            </div>
            <Button variant="outline" size="sm">
              Change Password
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Shield className="h-5 w-5" />
            <div>
              <CardTitle>Security Notifications</CardTitle>
              <CardDescription>
                Get notified about important security events
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="login-notifications">Login Notifications</Label>
              <p className="text-sm text-gray-600">
                Get notified when someone logs into your account
              </p>
            </div>
            <Switch
              id="login-notifications"
              checked={securitySettings?.loginNotifications || false}
              onCheckedChange={(checked) => updateNotificationSettings('loginNotifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="security-alerts">Security Alerts</Label>
              <p className="text-sm text-gray-600">
                Get notified about suspicious activity and security updates
              </p>
            </div>
            <Switch
              id="security-alerts"
              checked={securitySettings?.securityAlerts || false}
              onCheckedChange={(checked) => updateNotificationSettings('securityAlerts', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
