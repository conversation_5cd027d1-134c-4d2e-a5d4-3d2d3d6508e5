import { db } from '../db';
import {
  userSessions, sessionPolicies, sessionActivityLogs, trustedDevices, users,
  type UserSession, type InsertUserSession, type SessionPolicy, type TrustedDevice,
  type SessionInfo, type SessionValidationResult, type Device<PERSON>ingerprint, type SessionSecurityContext
} from '@shared/schema';
import { eq, and, or, desc, count, gte, lte, inArray, ne, sql } from 'drizzle-orm';
import crypto from 'crypto';

export class EnhancedSessionService {
  /**
   * Create a new user session with device tracking and security policies
   */
  async createSession(params: {
    userId: number;
    companyId?: number;
    deviceFingerprint?: string;
    deviceType: 'desktop' | 'laptop' | 'mobile' | 'tablet' | 'unknown';
    deviceName?: string;
    userAgent?: string;
    ipAddress?: string;
    location?: {
      country?: string;
      region?: string;
      city?: string;
    };
    mfaVerified?: boolean;
    freshAuth?: boolean;
  }): Promise<{ session: UserSession; requiresAction?: string }> {
    const sessionId = this.generateSessionId();

    // Get session policy for user's role
    const user = await db.select().from(users).where(eq(users.id, params.userId)).limit(1);
    if (!user.length) {
      throw new Error('User not found');
    }

    const policy = await this.getSessionPolicy(params.companyId, user[0].role);

    // Check concurrent session limits
    await this.enforceSessionLimits(params.userId, params.deviceType, policy);

    // Calculate session timeouts based on policy
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (policy.absolute_timeout_seconds * 1000));

    // Check if device is trusted
    const trustedDevice = params.deviceFingerprint
      ? await this.getTrustedDevice(params.userId, params.deviceFingerprint)
      : null;

    // Determine if MFA is required
    const requiresMfa = policy.require_mfa_for_new_device && !trustedDevice?.is_trusted;

    // Create session record
    const sessionData: InsertUserSession = {
      session_id: sessionId,
      user_id: params.userId,
      company_id: params.companyId,
      device_fingerprint: params.deviceFingerprint,
      device_type: params.deviceType,
      device_name: params.deviceName,
      user_agent: params.userAgent,
      ip_address: params.ipAddress,
      location_country: params.location?.country,
      location_region: params.location?.region,
      location_city: params.location?.city,
      expires_at: expiresAt,
      idle_timeout: policy.idle_timeout_seconds,
      absolute_timeout: policy.absolute_timeout_seconds,
      is_trusted_device: trustedDevice?.is_trusted || false,
      requires_mfa: requiresMfa,
      mfa_verified: params.mfaVerified || false,
      fresh_auth: params.freshAuth !== false,
      session_data: {},
      security_flags: {}
    };

    const [session] = await db.insert(userSessions).values(sessionData).returning();

    // Log session creation
    await this.logSessionActivity({
      sessionId,
      userId: params.userId,
      companyId: params.companyId,
      activityType: 'login',
      activityDescription: 'User session created',
      ipAddress: params.ipAddress,
      userAgent: params.userAgent,
      metadata: {
        deviceType: params.deviceType,
        deviceFingerprint: params.deviceFingerprint,
        isTrustedDevice: trustedDevice?.is_trusted || false
      }
    });

    // Update trusted device if exists
    if (trustedDevice) {
      await this.updateDeviceActivity(trustedDevice.id, params.ipAddress, params.location);
    }

    let requiresAction: string | undefined;
    if (requiresMfa && !params.mfaVerified) {
      requiresAction = 'mfa';
    }

    return { session, requiresAction };
  }

  /**
   * Validate an existing session
   */
  async validateSession(sessionId: string, ipAddress?: string): Promise<SessionValidationResult> {
    const session = await db.select()
      .from(userSessions)
      .where(and(
        eq(userSessions.session_id, sessionId),
        eq(userSessions.status, 'active')
      ))
      .limit(1);

    if (!session.length) {
      return { isValid: false, reason: 'Session not found or inactive' };
    }

    const sessionData = session[0];
    const now = new Date();

    // Check if session has expired
    if (sessionData.expires_at && sessionData.expires_at < now) {
      await this.terminateSession(sessionId, 'Session expired');
      return { isValid: false, reason: 'Session expired', requiresAction: 'logout' };
    }

    // Check idle timeout
    const idleTime = (now.getTime() - sessionData.last_activity.getTime()) / 1000;
    if (sessionData.idle_timeout && idleTime > sessionData.idle_timeout) {
      await this.terminateSession(sessionId, 'Session idle timeout');
      return { isValid: false, reason: 'Session idle timeout', requiresAction: 'logout' };
    }

    // Check if MFA is required but not verified
    if (sessionData.requires_mfa && !sessionData.mfa_verified) {
      return { isValid: false, reason: 'MFA verification required', requiresAction: 'mfa' };
    }

    // Update last activity
    await this.updateSessionActivity(sessionId, ipAddress);

    return { isValid: true, session: sessionData };
  }

  /**
   * Update session activity timestamp
   */
  async updateSessionActivity(sessionId: string, ipAddress?: string): Promise<void> {
    await db.update(userSessions)
      .set({
        last_activity: new Date(),
        ip_address: ipAddress || undefined,
        updated_at: new Date()
      })
      .where(eq(userSessions.session_id, sessionId));
  }

  /**
   * Terminate a session
   */
  async terminateSession(sessionId: string, reason: string, terminatedBy?: number): Promise<void> {
    const [session] = await db.update(userSessions)
      .set({
        status: 'terminated',
        terminated_at: new Date(),
        terminated_by: terminatedBy,
        termination_reason: reason,
        updated_at: new Date()
      })
      .where(eq(userSessions.session_id, sessionId))
      .returning();

    if (session) {
      await this.logSessionActivity({
        sessionId,
        userId: session.user_id,
        companyId: session.company_id,
        activityType: 'logout',
        activityDescription: `Session terminated: ${reason}`,
        metadata: { terminatedBy, reason }
      });
    }
  }

  /**
   * Get active sessions for a user
   */
  async getUserActiveSessions(userId: number): Promise<UserSession[]> {
    return await db.select()
      .from(userSessions)
      .where(and(
        eq(userSessions.user_id, userId),
        eq(userSessions.status, 'active')
      ))
      .orderBy(desc(userSessions.last_activity));
  }

  /**
   * Terminate all sessions for a user except current
   */
  async terminateOtherSessions(userId: number, currentSessionId: string, reason: string = 'Terminated by user'): Promise<number> {
    const sessions = await db.update(userSessions)
      .set({
        status: 'terminated',
        terminated_at: new Date(),
        termination_reason: reason,
        updated_at: new Date()
      })
      .where(and(
        eq(userSessions.user_id, userId),
        eq(userSessions.status, 'active'),
        ne(userSessions.session_id, currentSessionId)
      ))
      .returning();

    // Log terminations
    for (const session of sessions) {
      await this.logSessionActivity({
        sessionId: session.session_id,
        userId: session.user_id,
        companyId: session.company_id,
        activityType: 'logout',
        activityDescription: `Session terminated: ${reason}`,
        metadata: { reason, terminatedByUser: true }
      });
    }

    return sessions.length;
  }

  /**
   * Generate a secure session ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Get session policy for user role and company
   */
  private async getSessionPolicy(companyId?: number, role?: string): Promise<SessionPolicy> {
    // Try to get company-specific policy first, then role-specific, then default
    const policies = await db.select()
      .from(sessionPolicies)
      .where(and(
        eq(sessionPolicies.is_active, true),
        or(
          and(eq(sessionPolicies.company_id, companyId || 0), eq(sessionPolicies.role, role || '')),
          and(eq(sessionPolicies.company_id, companyId || 0), eq(sessionPolicies.role, null)),
          and(eq(sessionPolicies.company_id, null), eq(sessionPolicies.role, role || '')),
          and(eq(sessionPolicies.company_id, null), eq(sessionPolicies.role, null))
        )
      ))
      .orderBy(desc(sessionPolicies.priority));

    if (policies.length > 0) {
      return policies[0];
    }

    // Return default policy if none found
    return {
      id: 0,
      company_id: null,
      role: null,
      policy_name: 'Default Policy',
      max_concurrent_sessions: 5,
      max_sessions_per_device_type: { desktop: 3, laptop: 3, mobile: 2, tablet: 2 },
      timeout_policy: 'hybrid',
      idle_timeout_seconds: 3600,
      absolute_timeout_seconds: 86400,
      rolling_timeout_seconds: 28800,
      allow_mobile_access: true,
      require_device_registration: false,
      trusted_device_timeout_days: 30,
      require_mfa_for_new_device: false,
      require_fresh_auth_for_sensitive: false,
      auto_logout_on_suspicious_activity: true,
      allowed_countries: null,
      blocked_countries: null,
      require_vpn: false,
      is_active: true,
      priority: 0,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  /**
   * Enforce concurrent session limits
   */
  private async enforceSessionLimits(userId: number, deviceType: string, policy: SessionPolicy): Promise<void> {
    const activeSessions = await this.getUserActiveSessions(userId);

    // Check total concurrent sessions
    if (activeSessions.length >= policy.max_concurrent_sessions) {
      // Terminate oldest session
      const oldestSession = activeSessions[activeSessions.length - 1];
      await this.terminateSession(oldestSession.session_id, 'Concurrent session limit exceeded');
    }

    // Check device type specific limits
    const deviceTypeLimits = policy.max_sessions_per_device_type as any;
    const deviceLimit = deviceTypeLimits[deviceType] || 2;
    const deviceSessions = activeSessions.filter(s => s.device_type === deviceType);

    if (deviceSessions.length >= deviceLimit) {
      // Terminate oldest session for this device type
      const oldestDeviceSession = deviceSessions[deviceSessions.length - 1];
      await this.terminateSession(oldestDeviceSession.session_id, `${deviceType} session limit exceeded`);
    }
  }

  /**
   * Get trusted device information
   */
  private async getTrustedDevice(userId: number, deviceFingerprint: string): Promise<TrustedDevice | null> {
    const devices = await db.select()
      .from(trustedDevices)
      .where(and(
        eq(trustedDevices.user_id, userId),
        eq(trustedDevices.device_fingerprint, deviceFingerprint),
        eq(trustedDevices.is_blocked, false)
      ))
      .limit(1);

    return devices.length > 0 ? devices[0] : null;
  }

  /**
   * Update device activity
   */
  private async updateDeviceActivity(deviceId: number, ipAddress?: string, location?: { country?: string; region?: string; city?: string }): Promise<void> {
    await db.update(trustedDevices)
      .set({
        last_seen: new Date(),
        last_ip_address: ipAddress,
        last_location_country: location?.country,
        last_location_region: location?.region,
        last_location_city: location?.city,
        login_count: sql`${trustedDevices.login_count} + 1`,
        last_login: new Date(),
        updated_at: new Date()
      })
      .where(eq(trustedDevices.id, deviceId));
  }

  /**
   * Log session activity
   */
  private async logSessionActivity(params: {
    sessionId: string;
    userId: number;
    companyId?: number;
    activityType: string;
    activityDescription?: string;
    endpoint?: string;
    method?: string;
    statusCode?: number;
    ipAddress?: string;
    userAgent?: string;
    referer?: string;
    requestId?: string;
    riskScore?: number;
    securityFlags?: any;
    anomalyDetected?: boolean;
    responseTimeMs?: number;
    metadata?: any;
  }): Promise<void> {
    await db.insert(sessionActivityLogs).values({
      session_id: params.sessionId,
      user_id: params.userId,
      company_id: params.companyId,
      activity_type: params.activityType,
      activity_description: params.activityDescription,
      endpoint: params.endpoint,
      method: params.method,
      status_code: params.statusCode,
      ip_address: params.ipAddress,
      user_agent: params.userAgent,
      referer: params.referer,
      request_id: params.requestId,
      risk_score: params.riskScore || 0,
      security_flags: params.securityFlags || {},
      anomaly_detected: params.anomalyDetected || false,
      response_time_ms: params.responseTimeMs,
      metadata: params.metadata || {}
    });
  }

  /**
   * Register or update a trusted device
   */
  async registerTrustedDevice(params: {
    userId: number;
    deviceFingerprint: string;
    deviceName?: string;
    deviceType: 'desktop' | 'laptop' | 'mobile' | 'tablet' | 'unknown';
    userAgent?: string;
    ipAddress?: string;
    location?: { country?: string; region?: string; city?: string };
    trustedBy?: number;
  }): Promise<TrustedDevice> {
    const existing = await this.getTrustedDevice(params.userId, params.deviceFingerprint);

    if (existing) {
      // Update existing device
      const [updated] = await db.update(trustedDevices)
        .set({
          device_name: params.deviceName || existing.device_name,
          device_type: params.deviceType,
          user_agent: params.userAgent || existing.user_agent,
          last_ip_address: params.ipAddress || existing.last_ip_address,
          last_location_country: params.location?.country || existing.last_location_country,
          last_location_region: params.location?.region || existing.last_location_region,
          last_location_city: params.location?.city || existing.last_location_city,
          is_trusted: true,
          trusted_at: new Date(),
          trusted_by: params.trustedBy,
          last_seen: new Date(),
          updated_at: new Date()
        })
        .where(eq(trustedDevices.id, existing.id))
        .returning();

      return updated;
    } else {
      // Create new trusted device
      const [created] = await db.insert(trustedDevices).values({
        user_id: params.userId,
        device_fingerprint: params.deviceFingerprint,
        device_name: params.deviceName,
        device_type: params.deviceType,
        user_agent: params.userAgent,
        last_ip_address: params.ipAddress,
        last_location_country: params.location?.country,
        last_location_region: params.location?.region,
        last_location_city: params.location?.city,
        is_trusted: true,
        trust_level: 50, // Default trust level
        trusted_at: new Date(),
        trusted_by: params.trustedBy,
        login_count: 1,
        last_login: new Date()
      }).returning();

      return created;
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const now = new Date();

    const expiredSessions = await db.update(userSessions)
      .set({
        status: 'expired',
        terminated_at: now,
        termination_reason: 'Session expired',
        updated_at: now
      })
      .where(and(
        eq(userSessions.status, 'active'),
        or(
          lte(userSessions.expires_at, now),
          lte(sql`${userSessions.last_activity} + INTERVAL '1 second' * ${userSessions.idle_timeout}`, now)
        )
      ))
      .returning();

    // Log cleanup activities
    for (const session of expiredSessions) {
      await this.logSessionActivity({
        sessionId: session.session_id,
        userId: session.user_id,
        companyId: session.company_id,
        activityType: 'timeout',
        activityDescription: 'Session automatically expired',
        metadata: { cleanupJob: true }
      });
    }

    return expiredSessions.length;
  }

  /**
   * Get session statistics for a user or company
   */
  async getSessionStatistics(params: { userId?: number; companyId?: number; days?: number }): Promise<{
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
    terminatedSessions: number;
    averageSessionDuration: number;
    deviceBreakdown: Record<string, number>;
    locationBreakdown: Record<string, number>;
  }> {
    const days = params.days || 30;
    const since = new Date(Date.now() - (days * 24 * 60 * 60 * 1000));

    let whereCondition = gte(userSessions.created_at, since);

    if (params.userId) {
      whereCondition = and(whereCondition, eq(userSessions.user_id, params.userId));
    } else if (params.companyId) {
      whereCondition = and(whereCondition, eq(userSessions.company_id, params.companyId));
    }

    const sessions = await db.select().from(userSessions).where(whereCondition);

    const stats = {
      totalSessions: sessions.length,
      activeSessions: sessions.filter(s => s.status === 'active').length,
      expiredSessions: sessions.filter(s => s.status === 'expired').length,
      terminatedSessions: sessions.filter(s => s.status === 'terminated').length,
      averageSessionDuration: 0,
      deviceBreakdown: {} as Record<string, number>,
      locationBreakdown: {} as Record<string, number>
    };

    // Calculate average session duration
    const completedSessions = sessions.filter(s => s.terminated_at);
    if (completedSessions.length > 0) {
      const totalDuration = completedSessions.reduce((sum, session) => {
        const duration = session.terminated_at!.getTime() - session.created_at.getTime();
        return sum + duration;
      }, 0);
      stats.averageSessionDuration = totalDuration / completedSessions.length / 1000; // in seconds
    }

    // Device breakdown
    sessions.forEach(session => {
      const deviceType = session.device_type || 'unknown';
      stats.deviceBreakdown[deviceType] = (stats.deviceBreakdown[deviceType] || 0) + 1;
    });

    // Location breakdown
    sessions.forEach(session => {
      const country = session.location_country || 'unknown';
      stats.locationBreakdown[country] = (stats.locationBreakdown[country] || 0) + 1;
    });

    return stats;
  }
}

// Export singleton instance
export const enhancedSessionService = new EnhancedSessionService();
