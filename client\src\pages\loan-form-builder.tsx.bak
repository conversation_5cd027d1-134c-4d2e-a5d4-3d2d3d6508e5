import React, { useState, useEffect } from "react";
import { He<PERSON><PERSON> } from "react-helmet";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useCompany } from "@/lib/companies";
import { DynamicFormBuilder } from "@/components/form-builder/DynamicFormBuilder";
// Import the FormField and FormTemplate types from the DynamicFormBuilder component
import type { 
  FormField, 
  FormTemplate, 
  FormFieldOption, 
  ConditionalRule 
} from "@/components/form-builder/DynamicFormBuilder";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { PlusCircle, Settings, Copy, Trash2, CheckCircle, ArrowLeft, Upload, Cloud, FileDown, FilePlus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Spinner } from "@/components/ui/spinner";
import errorLogger from "@/lib/errorLogger";
import SetupLoanConfig from "@/scripts/setup-loan-config";

// Define types for database models
interface FormTemplateDB {
  id: number;
  company_id: number;
  branch_id: number | null;
  name: string;
  description: string | null;
  category: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FormFieldDB {
  id: number;
  template_id: number;
  name: string;
  label: string;
  field_type: string;
  is_required: boolean;
  order: number;
  placeholder: string | null;
  help_text: string | null;
  default_value: string | null;
  options: FormFieldOption[] | null;
  validation_rules: any | null;
  created_at: string;
  updated_at: string;
}

interface LoanConfigurationDB {
  id: number;
  company_id: number;
  template_id: number;
  branch_id: number | null;
  loan_type: string;
  is_active: boolean;
  order: number;
  created_at: string;
  updated_at: string;
  template?: FormTemplateDB;
}

// Sample templates as fallback
const SAMPLE_TEMPLATES: FormTemplate[] = [];

// Convert database template to FormTemplate format
const convertDbTemplateToFormTemplate = async (dbTemplate: FormTemplateDB, dbFields: FormFieldDB[] = []): Promise<FormTemplate> => {
  // Map database fields to our FormField format
  const formFields: FormField[] = dbFields.map(field => ({
    id: `field_${field.id}`,
    type: field.field_type as FormField['type'],
    label: field.label,
    placeholder: field.placeholder || undefined,
    helpText: field.help_text || undefined,
    required: field.is_required,
    options: field.options || undefined,
    validations: field.validation_rules || undefined,
    defaultValue: field.default_value || undefined,
    isSystem: field.name.startsWith('loan_'),
    styling: {
      width: 'full'
    }
  }));

  return {
    id: dbTemplate.id.toString(),
    name: dbTemplate.name,
    description: dbTemplate.description || undefined,
    fields: formFields.length > 0 ? formFields : [],
    isActive: dbTemplate.is_active,
    category: dbTemplate.category || undefined,
    companyId: dbTemplate.company_id,
    branchId: dbTemplate.branch_id
  };
};

// Convert FormTemplate to database format for saving
const convertFormTemplateToDb = (formTemplate: FormTemplate): { 
  template: Partial<FormTemplateDB>,
  fields: Partial<FormFieldDB>[]
} => {
  // Extract basic template data
  const templateData: Partial<FormTemplateDB> = {
    name: formTemplate.name,
    description: formTemplate.description || null,
    category: formTemplate.category || null,
    is_active: formTemplate.isActive !== undefined ? formTemplate.isActive : true,
    company_id: formTemplate.companyId,
    branch_id: formTemplate.branchId
  };

  // Extract fields data
  const fieldsData: Partial<FormFieldDB>[] = formTemplate.fields.map((field, index) => ({
    name: field.id.replace('field_', ''),
    label: field.label,
    field_type: field.type,
    is_required: field.required,
    order: index,
    placeholder: field.placeholder || null,
    help_text: field.helpText || null,
    default_value: field.defaultValue || null,
    options: field.options || null,
    validation_rules: field.validations || null
  }));

  return {
    template: templateData,
    fields: fieldsData
  };
};

export default function LoanFormBuilderPage() {
  const { toast } = useToast();
  const { currentCompany } = useCompany();
  const companyId = currentCompany?.company_id; // Only fetch templates if we have a valid company ID
  const [showLoanConfigSetup, setShowLoanConfigSetup] = useState<boolean>(false);
  const queryClient = useQueryClient();
  const [, navigate] = useLocation(); // Using useLocation from wouter
  const [templates, setTemplates] = useState<FormTemplate[]>([]); // Initialize with empty array
  const [activeTab, setActiveTab] = useState<string>("templates"); // 'templates' or 'builder'
  const [editingTemplate, setEditingTemplate] = useState<FormTemplate | undefined>(undefined);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const [showImportDialog, setShowImportDialog] = useState<boolean>(false);
  const [showCloudImportDialog, setShowCloudImportDialog] = useState<boolean>(false);
  const [importJsonText, setImportJsonText] = useState<string>("");
  const [importUrl, setImportUrl] = useState<string>("");
  const [templateToDelete, setTemplateToDelete] = useState<FormTemplate | undefined>(undefined);
  const [importLoading, setImportLoading] = useState<boolean>(false);
  
  // Effect to check if loan configurations exist
  useEffect(() => {
    if (companyId) {
      // Fetch loan configurations for personal type
      fetch(`/api/companies/${companyId}/loan-configurations/loan-type/personal`)
        .then(response => response.json())
        .then(data => {
          // If no configurations exist, show the setup
          if (!data || data.length === 0) {
            setShowLoanConfigSetup(true);
          } else {
            setShowLoanConfigSetup(false);
          }
        })
        .catch(error => {
          console.error("Error checking loan configurations:", error);
          // If there's an error, we might want to show the setup anyway
          setShowLoanConfigSetup(true);
        });
    }
  }, [companyId]);
  
  // Fetch form templates from the API
  const { data: dbTemplates, isLoading: isLoadingTemplates } = useQuery<FormTemplateDB[]>({
    queryKey: [`/api/companies/${companyId}/form-templates`],
    enabled: !!companyId,
  });
  
  // Function to fetch fields for a specific template
  const fetchFieldsForTemplate = async (templateId: number) => {
    try {
      const response = await fetch(`/api/form-templates/${templateId}/fields`);
      if (!response.ok) {
        throw new Error(`Failed to fetch fields: ${response.statusText}`);
      }
      return await response.json() as FormFieldDB[];
    } catch (error) {
      errorLogger.error(`Error fetching fields for template ${templateId}`, 'LoanFormBuilder', error);
      return [] as FormFieldDB[];
    }
  };
  
  // Load templates when API data changes
  useEffect(() => {
    const loadTemplates = async () => {
      if (!dbTemplates || dbTemplates.length === 0) return;
      
      try {
        const loadedTemplates: FormTemplate[] = [];
        
        // Process each template and fetch its fields
        for (const dbTemplate of dbTemplates) {
          const fields = await fetchFieldsForTemplate(dbTemplate.id);
          const formTemplate = await convertDbTemplateToFormTemplate(dbTemplate, fields);
          loadedTemplates.push(formTemplate);
        }
        
        setTemplates(loadedTemplates);
      } catch (error) {
        errorLogger.error("Error loading templates", 'LoanFormBuilder', error);
        toast({
          title: "Error loading templates",
          description: "There was a problem loading your templates. Please try again.",
          variant: "destructive"
        });
      }
    };
    
    loadTemplates();
  }, [dbTemplates, toast]);

  // Define mutations for saving and updating templates
  const createTemplateMutation = useMutation({
    mutationFn: async (template: FormTemplate) => {
      const { template: templateData, fields } = convertFormTemplateToDb(template);
      
      // Create a new template in the database
      const response = await fetch(`/api/companies/${companyId}/form-templates`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(templateData)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create template: ${response.statusText}`);
      }
      
      const newTemplate = await response.json() as FormTemplateDB;
      
      // Create fields for the template
      const fieldsPromises = fields.map(field => 
        fetch(`/api/form-templates/${newTemplate.id}/fields`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...field, template_id: newTemplate.id })
        })
      );
      
      await Promise.all(fieldsPromises);
      
      return newTemplate;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/form-templates`] });
      setActiveTab("templates");
      toast({
        title: "Template created",
        description: "Your form template has been created successfully.",
      });
    },
    onError: (error) => {
      errorLogger.error("Error creating template", "LoanFormBuilder", error);
      toast({
        title: "Error creating template",
        description: "There was a problem creating your template. Please try again.",
        variant: "destructive"
      });
    }
  });
  
  const updateTemplateMutation = useMutation({
    mutationFn: async (template: FormTemplate) => {
      if (!template.id) throw new Error("Template ID is required for updates");
      
      const templateId = parseInt(template.id);
      const { template: templateData, fields } = convertFormTemplateToDb(template);
      
      // Update template in the database
      const response = await fetch(`/api/companies/${companyId}/form-templates/${templateId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(templateData)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update template: ${response.statusText}`);
      }
      
      // Fetch existing fields to determine which to update/create/delete
      const existingFields = await fetchFieldsForTemplate(templateId);
      
      // Update or create fields
      for (const field of fields) {
        const fieldId = field.name?.includes('field_') 
          ? parseInt(field.name.replace('field_', '')) 
          : undefined;
        
        if (fieldId && existingFields.some(f => f.id === fieldId)) {
          // Update existing field
          await fetch(`/api/form-fields/${fieldId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ...field, template_id: templateId })
          });
        } else {
          // Create new field
          await fetch(`/api/form-templates/${templateId}/fields`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ...field, template_id: templateId })
          });
        }
      }
      
      // TODO: Handle field deletion if needed
      
      return template;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/form-templates`] });
      setEditingTemplate(undefined);
      setActiveTab("templates");
      toast({
        title: "Template updated",
        description: "Your form template has been updated successfully.",
      });
    },
    onError: (error) => {
      errorLogger.error("Error updating template", "LoanFormBuilder", error);
      toast({
        title: "Error updating template",
        description: "There was a problem updating your template. Please try again.",
        variant: "destructive"
      });
    }
  });
  
  // Function to handle saving a template (create or update)
  const handleSaveTemplate = (template: FormTemplate) => {
    try {
      // If editing an existing template, update it
      if (editingTemplate && editingTemplate.id) {
        updateTemplateMutation.mutate({
          ...template,
          id: editingTemplate.id,
          companyId: companyId || 0 // Default to 0 when undefined
        });
      } else {
        // If creating a new template, add it to the list
        createTemplateMutation.mutate({
          ...template,
          isActive: true,
          companyId: companyId || 0 // Default to 0 when undefined
        });
      }
    } catch (error) {
      errorLogger.error("Error saving template", "LoanFormBuilder", error);
      toast({
        title: "Error saving template",
        description: "There was an unexpected problem saving your template.",
        variant: "destructive"
      });
    }
  };

  // Function to create a new template
  const handleCreateTemplate = () => {
    setEditingTemplate(undefined);
    setActiveTab("builder");
  };

  // Function to edit an existing template
  const handleEditTemplate = (template: FormTemplate) => {
    setEditingTemplate(template);
    setActiveTab("builder");
  };

  // Function to duplicate a template
  const handleDuplicateTemplate = (template: FormTemplate) => {
    if (!template.id || !template.name) return;
    
    const duplicatedTemplate = {
      ...template,
      id: `template-${Date.now()}`,
      name: `${template.name} (Copy)`,
    };
    
    setTemplates(prev => [...prev, duplicatedTemplate]);
    
    toast({
      title: "Template duplicated",
      description: `${template.name} has been duplicated.`,
    });
  };

  // Function to set a template up for deletion
  const handleDeleteClick = (template: FormTemplate) => {
    setTemplateToDelete(template);
    setShowDeleteDialog(true);
  };

  // Define delete mutation
  const deleteTemplateMutation = useMutation({
    mutationFn: async (templateId: string) => {
      const id = parseInt(templateId);
      if (isNaN(id)) throw new Error("Invalid template ID");
      
      const response = await fetch(`/api/companies/${companyId}/form-templates/${id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete template: ${response.statusText}`);
      }
      
      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/form-templates`] });
      toast({
        title: "Template deleted",
        description: `${templateToDelete?.name || 'Template'} has been deleted successfully.`,
      });
    },
    onError: (error) => {
      errorLogger.error("Error deleting template", "LoanFormBuilder", error);
      toast({
        title: "Error deleting template",
        description: "There was a problem deleting the template. Please try again.",
        variant: "destructive"
      });
    }
  });
  
  // Function to confirm template deletion
  const handleConfirmDelete = () => {
    if (templateToDelete && templateToDelete.id) {
      deleteTemplateMutation.mutate(templateToDelete.id);
      setShowDeleteDialog(false);
      setTemplateToDelete(undefined);
    }
  };

  // Define toggle active mutation
  const toggleActiveMutation = useMutation({
    mutationFn: async (templateData: { id: string, isActive: boolean }) => {
      const id = parseInt(templateData.id);
      if (isNaN(id)) throw new Error("Invalid template ID");
      
      const response = await fetch(`/api/companies/${companyId}/form-templates/${id}/toggle-active`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: templateData.isActive })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to toggle template status: ${response.statusText}`);
      }
      
      return { id, isActive: templateData.isActive };
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/form-templates`] });
      toast({
        title: data.isActive ? "Template activated" : "Template deactivated",
        description: `The template is now ${data.isActive ? 'active' : 'inactive'}.`,
      });
    },
    onError: (error) => {
      errorLogger.error("Error toggling template status", "LoanFormBuilder", error);
      toast({
        title: "Status update failed",
        description: "There was a problem updating the template status. Please try again.",
        variant: "destructive"
      });
    }
  });
  
  // Function to toggle the active status of a template
  const toggleTemplateActive = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      const newStatus = !template.isActive;
      toggleActiveMutation.mutate({ id: templateId, isActive: newStatus });
    }
  };

  // Function to map a template to a loan type (in a real implementation)
  const handleMapTemplate = (templateId: string) => {
    toast({
      title: "Template mapped",
      description: "This template has been mapped to selected loan types.",
    });
  };

  // Function to export a template to JSON
  const handleExportTemplate = (template: FormTemplate) => {
    try {
      // Create a JSON string of the template
      const jsonData = JSON.stringify(template, null, 2);
      
      // Create a blob with the JSON data
      const blob = new Blob([jsonData], { type: 'application/json' });
      
      // Create a URL for the blob
      const url = URL.createObjectURL(blob);
      
      // Create a temporary anchor element to trigger the download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name?.replace(/\s+/g, '_').toLowerCase() || 'template'}_export.json`;
      
      // Trigger the download
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Template exported",
        description: "The template has been exported successfully.",
      });
    } catch (error) {
      errorLogger.error('Error exporting template', 'LoanFormBuilder', error);
      toast({
        title: "Export failed",
        description: "There was an error exporting the template.",
        variant: "destructive"
      });
    }
  };

  // Define import mutation
  const importTemplateMutation = useMutation({
    mutationFn: async (template: FormTemplate) => {
      // Make sure we have a new template without an existing ID
      const templateToImport = {
        ...template,
        companyId: companyId || 0, // Default to 0 when undefined
        isActive: true
      };
      
      // Use the create template mutation logic
      const { template: templateData, fields } = convertFormTemplateToDb(templateToImport);
      
      // Create a new template in the database
      const response = await fetch(`/api/companies/${companyId}/form-templates`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(templateData)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to import template: ${response.statusText}`);
      }
      
      const newTemplate = await response.json() as FormTemplateDB;
      
      // Create fields for the template
      const fieldsPromises = fields.map(field => 
        fetch(`/api/form-templates/${newTemplate.id}/fields`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...field, template_id: newTemplate.id })
        })
      );
      
      await Promise.all(fieldsPromises);
      
      return newTemplate;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/form-templates`] });
      
      toast({
        title: "Template imported",
        description: `${variables.name} has been imported successfully.`,
      });
      
      // Close dialogs and reset inputs
      setShowImportDialog(false);
      setShowCloudImportDialog(false);
      setImportJsonText("");
      setImportUrl("");
    },
    onError: (error) => {
      errorLogger.error("Error importing template", "LoanFormBuilder", error);
      toast({
        title: "Import failed",
        description: "There was a problem importing the template to the database. Please try again.",
        variant: "destructive"
      });
    }
  });
  
  // Function to handle importing a template from JSON
  const handleImportTemplate = () => {
    try {
      if (!importJsonText.trim()) {
        toast({
          title: "Import failed",
          description: "Please enter valid JSON data.",
          variant: "destructive"
        });
        return;
      }
      
      // Parse the JSON
      const templateData = JSON.parse(importJsonText);
      
      // Validate the template structure (basic validation)
      if (!templateData.name || !Array.isArray(templateData.fields)) {
        toast({
          title: "Invalid template format",
          description: "The JSON does not contain a valid template structure.",
          variant: "destructive"
        });
        return;
      }
      
      // Use the import mutation to save the template to the database
      importTemplateMutation.mutate(templateData);
    } catch (error) {
      errorLogger.error('Error parsing import data', 'LoanFormBuilder', error);
      toast({
        title: "Import failed",
        description: "There was an error parsing the JSON data. Please make sure it's valid.",
        variant: "destructive"
      });
    }
  };

  // Function to handle importing a template from a cloud URL
  const handleCloudImport = async () => {
    if (!importUrl.trim()) {
      toast({
        title: "Import failed",
        description: "Please enter a valid URL.",
        variant: "destructive"
      });
      return;
    }

    setImportLoading(true);

    try {
      // Fetch the template from the provided URL
      const response = await fetch(importUrl);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch template: ${response.statusText}`);
      }
      
      const templateData = await response.json();
      
      // Validate the template structure
      if (!templateData.name || !Array.isArray(templateData.fields)) {
        toast({
          title: "Invalid template format",
          description: "The JSON from the URL does not contain a valid template structure.",
          variant: "destructive"
        });
        setImportLoading(false);
        return;
      }
      
      // Use the import mutation to save the template to the database
      await importTemplateMutation.mutateAsync(templateData);
      
      // The import mutation will handle toast notifications and dialog closing
    } catch (error) {
      errorLogger.error('Error importing template from cloud', 'LoanFormBuilder', error);
      toast({
        title: "Cloud import failed",
        description: "There was an error fetching or parsing the template from the provided URL.",
        variant: "destructive"
      });
    } finally {
      setImportLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <Helmet>
        <title>Loan Templates | TrackFina</title>
      </Helmet>
      
      {/* Show the loan config setup dialog if needed */}
      {showLoanConfigSetup && (
        <div className="mb-8">
          <SetupLoanConfig />
        </div>
      )}
      
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate("/loans")} className="text-sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Loans
          </Button>
          <h1 className="text-2xl font-bold">Loan Templates</h1>
        </div>
        <div className="flex space-x-2">
          {activeTab === "builder" && (
            <Button variant="outline" onClick={() => setActiveTab("templates")}>
              Back to Templates
            </Button>
          )}
          {activeTab === "templates" && (
            <>
              {/* Desktop buttons */}
              <div className="hidden sm:flex space-x-2">
                <Button 
                  variant="outline" 
                  onClick={() => setShowImportDialog(true)}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Import
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowCloudImportDialog(true)}
                >
                  <Cloud className="h-4 w-4 mr-2" />
                  Import from Cloud
                </Button>
              </div>
              
              {/* Mobile dropdown */}
              <div className="sm:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Options
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
                      <Upload className="h-4 w-4 mr-2" />
                      Import Template
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setShowCloudImportDialog(true)}>
                      <Cloud className="h-4 w-4 mr-2" />
                      Import from Cloud
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              
              <Button onClick={handleCreateTemplate}>
                <PlusCircle className="h-4 w-4 mr-2" />
                Create New Template
              </Button>
            </>
          )}
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6 hidden">
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="builder">Form Builder</TabsTrigger>
        </TabsList>
        
        {/* Templates List */}
        <TabsContent value="templates" className="space-y-6">
          {isLoadingTemplates ? (
            <div className="flex flex-col items-center justify-center p-12">
              <Spinner size="lg" />
              <p className="mt-4 text-muted-foreground">Loading templates...</p>
            </div>
          ) : templates.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-12 border rounded-lg bg-muted/10">
              <PlusCircle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No templates found</h3>
              <p className="text-muted-foreground text-center mb-4">
                Create your first loan template to start configuring your loan types
              </p>
              <Button onClick={handleCreateTemplate}>
                <PlusCircle className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {templates.map((template) => (
                <Card 
                  key={template.id} 
                  className={`overflow-hidden transition-shadow hover:shadow-md ${!template.isActive ? 'opacity-70' : ''}`}
                >
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{template.name}</CardTitle>
                        {template.description && (
                          <CardDescription className="mt-1">
                            {template.description}
                          </CardDescription>
                        )}
                      </div>
                      {template.isActive ? (
                        <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs flex items-center">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Active
                        </span>
                      ) : (
                        <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                          Inactive
                        </span>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="pb-4">
                    <div className="text-sm text-gray-500 mb-3">
                      {template.fields.length} fields • 
                      Created {new Date().toLocaleDateString()}
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-4">
                      {Array.from(new Set(template.fields.map(f => f.type))).map(type => (
                        <span 
                          key={type} 
                          className="bg-blue-50 text-blue-700 px-2 py-0.5 rounded text-xs"
                        >
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </span>
                      ))}
                    </div>
                    
                    <div className="flex justify-between space-x-2">
                      <div className="space-x-1">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleEditTemplate(template)}
                        >
                          <Settings className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleDuplicateTemplate(template)}
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Duplicate
                        </Button>
                      </div>
                      <div className="space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleExportTemplate(template)}
                        >
                          <FileDown className="h-4 w-4 mr-1" />
                          Export
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => template.id && toggleTemplateActive(template.id)}
                        >
                          {template.isActive ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteClick(template)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                    
                    <Button 
                      className="w-full mt-4"
                      variant="default"
                      onClick={() => navigate(`/loans/create?template=${template.id}`)}
                    >
                      Use Template for New Loan
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
        
        {/* Form Builder */}
        <TabsContent value="builder">
          <Card>
            <CardContent className="pt-6">
              <DynamicFormBuilder
                initialTemplate={editingTemplate}
                onSave={handleSaveTemplate}
                companyId={companyId || 0} // Default to 0 when undefined, will be validated in component
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the template "{templateToDelete?.name}"? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Template</DialogTitle>
            <DialogDescription>
              Paste your template JSON below to import it.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid w-full gap-2">
              <label htmlFor="import-json" className="text-sm font-medium">
                Template JSON
              </label>
              <textarea
                id="import-json"
                rows={10}
                className="w-full p-3 text-sm rounded-md border border-input bg-background resize-none"
                placeholder='{"name": "Template Name", "description": "Description", "fields": [...]}'
                value={importJsonText}
                onChange={(e) => setImportJsonText(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                Make sure the JSON includes a name and fields array. All imported templates will be assigned a new ID.
              </p>
            </div>
          </div>
          
          <DialogFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => {
              setShowImportDialog(false);
              setImportJsonText('');
            }}>
              Cancel
            </Button>
            <Button 
              onClick={handleImportTemplate}
            >
              Import Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cloud Import Dialog */}
      <Dialog open={showCloudImportDialog} onOpenChange={setShowCloudImportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import from Cloud</DialogTitle>
            <DialogDescription>
              Import template from a cloud URL that returns a valid template JSON.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid w-full gap-2">
              <label htmlFor="import-url" className="text-sm font-medium">
                Template URL
              </label>
              <input
                id="import-url"
                type="url"
                className="w-full p-3 text-sm rounded-md border border-input bg-background"
                placeholder="https://example.com/templates/loan-template.json"
                value={importUrl}
                onChange={(e) => setImportUrl(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                The URL should return a JSON response with a template structure (name and fields array).
              </p>
            </div>
          </div>
          
          <DialogFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => {
              setShowCloudImportDialog(false);
              setImportUrl('');
            }}>
              Cancel
            </Button>
            <Button 
              onClick={handleCloudImport}
              disabled={importLoading}
            >
              {importLoading ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></span>
                  Importing...
                </>
              ) : (
                'Import from Cloud'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}