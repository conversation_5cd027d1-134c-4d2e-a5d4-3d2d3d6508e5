/**
 * Client-side logging utility for TrackFina application
 * Provides consistent logging across the application with severity levels and context
 * Production-optimized to reduce console noise while preserving essential logging
 */

type LogLevel = 'info' | 'warn' | 'error' | 'debug';
type LogContext = string;

interface LogOptions {
  context?: LogContext;
  data?: any;
}

// Define contexts that should be logged even in production for business operations
const PRODUCTION_LOG_CONTEXTS = [
  'auth-error',
  'api-error',
  'payment-error',
  'loan-error',
  'data-corruption',
  'security-violation',
  'critical-business-operation',
  'user-management-error',
  'role-management-error',
  'permission-error'
];

// Define contexts that should be suppressed in production (routine operations)
const VERBOSE_CONTEXTS = [
  'user-interaction',
  'date-filter',
  'date-range-change',
  'api-request',
  'force-refresh',
  'query-refetch',
  'component-loading',
  'data-fetch',
  'user-management',
  'role-fetch',
  'permission-fetch',
  'group-fetch',
  'user-creation',
  'role-creation'
];

class Logger {
  private isDevMode: boolean;

  constructor() {
    this.isDevMode = import.meta.env.DEV || false;
  }

  /**
   * Log informational messages (filtered for production)
   */
  info(message: string, options?: LogOptions): void {
    // In production, only log critical business operations
    if (this.isDevMode || this.shouldLogInProduction(options?.context)) {
      this.log('info', message, options);
    }
  }

  /**
   * Log warning messages (always logged)
   */
  warn(message: string, options?: LogOptions): void {
    this.log('warn', message, options);
  }

  /**
   * Log error messages (always logged)
   */
  error(message: string, error?: Error, options?: LogOptions): void {
    const combinedOptions = {
      ...options,
      data: {
        ...(options?.data || {}),
        error: error ? {
          message: error.message,
          stack: error.stack,
          name: error.name
        } : undefined
      }
    };

    this.log('error', message, combinedOptions);
  }

  /**
   * Log debug messages (only in development)
   */
  debug(message: string, options?: LogOptions): void {
    if (this.isDevMode) {
      this.log('debug', message, options);
    }
  }

  /**
   * Determine if a log should be shown in production based on context
   */
  private shouldLogInProduction(context?: string): boolean {
    if (!context) return false;

    // Always log production-critical contexts
    if (PRODUCTION_LOG_CONTEXTS.includes(context)) {
      return true;
    }

    // Suppress verbose contexts in production
    if (VERBOSE_CONTEXTS.includes(context)) {
      return false;
    }

    // Default: log unknown contexts in development only
    return false;
  }

  /**
   * Internal logging method with production-optimized formatting
   */
  private log(level: LogLevel, message: string, options?: LogOptions): void {
    const context = options?.context ? `[${options.context}]` : '';

    // In production, use simpler formatting to reduce noise
    if (this.isDevMode) {
      const timestamp = new Date().toISOString();
      const prefix = `${timestamp} ${level.toUpperCase()} ${context}`;

      switch (level) {
        case 'info':
          console.info(`${prefix} ${message}`, options?.data || '');
          break;
        case 'warn':
          console.warn(`${prefix} ${message}`, options?.data || '');
          break;
        case 'error':
          console.error(`${prefix} ${message}`, options?.data || '');
          break;
        case 'debug':
          console.debug(`${prefix} ${message}`, options?.data || '');
          break;
      }
    } else {
      // Production logging: simpler format, essential info only
      const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';

      switch (level) {
        case 'info':
          console.info(`${prefix} ${context} ${message}`);
          break;
        case 'warn':
          console.warn(`${prefix} ${context} ${message}`, options?.data || '');
          break;
        case 'error':
          console.error(`${prefix} ${context} ${message}`, options?.data || '');
          break;
        case 'debug':
          // Debug messages never shown in production
          break;
      }
    }
  }
}

// Create singleton instance
const logger = new Logger();
export default logger;