#!/usr/bin/env node
/**
 * Migration Script: Update Customer Reference Codes
 * Purpose: Update all existing customers to have an empty value in the customer_reference_code column
 * Usage: node scripts/migrations/update-customer-reference-codes.js
 */

import { runMigration, logMigrationStats } from '../utils/migration-runner.js';

await runMigration('Update Customer Reference Codes', async (pool, { dryRun }) => {
  // First, check if there are any customers in the database
  const countResult = await pool.query('SELECT COUNT(*) FROM customers');
  const customerCount = parseInt(countResult.rows[0].count);
  
  console.log(`Found ${customerCount} customers in the database`);

  if (customerCount === 0) {
    console.log('No customers to update. Exiting.');
    return;
  }

  if (dryRun) {
    // Check how many customers would be updated
    const nullCountResult = await pool.query(`
      SELECT COUNT(*) FROM customers WHERE customer_reference_code IS NULL
    `);
    const nullCount = parseInt(nullCountResult.rows[0].count);
    
    console.log(`Would update ${nullCount} customers with NULL customer_reference_code to empty string`);
    logMigrationStats({
      'Total customers': customerCount,
      'Customers with NULL reference code': nullCount,
      'Customers that would be updated': nullCount
    });
    return;
  }

  // Update all customers to have a placeholder in the customer_reference_code column
  // This will be replaced with company-specific codes later
  const updateResult = await pool.query(`
    UPDATE customers
    SET customer_reference_code = ''
    WHERE customer_reference_code IS NULL
    RETURNING id
  `);

  const updatedCount = updateResult.rows.length;
  console.log(`Updated ${updatedCount} customers with empty customer_reference_code`);

  // Verify the update
  const verifyResult = await pool.query(`
    SELECT COUNT(*) FROM customers WHERE customer_reference_code = ''
  `);

  const verifiedCount = parseInt(verifyResult.rows[0].count);
  console.log(`Verified ${verifiedCount} customers now have empty customer_reference_code`);

  logMigrationStats({
    'Total customers': customerCount,
    'Customers updated': updatedCount,
    'Customers with empty reference code': verifiedCount
  });
});
