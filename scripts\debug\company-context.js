#!/usr/bin/env node
/**
 * Debug Script: Company Context Checker
 * Purpose: Debug script to check company context issues in browser console
 * Usage: Copy and paste this script into browser console to debug company ID issues
 * 
 * This script helps diagnose and fix common company context mismatches between
 * JWT tokens, localStorage data, and React Query cache.
 */

// Debug script to check company context issues
// Run this in the browser console to debug company ID issues

console.log("=== COMPANY CONTEXT DEBUG ===");

// 1. Check localStorage data
console.log("1. LocalStorage Data:");
console.log("auth_token:", localStorage.getItem('auth_token'));
console.log("user_data:", localStorage.getItem('user_data'));
console.log("selected_company:", localStorage.getItem('selected_company'));

// Check all localStorage keys for any company-related data
console.log("\nAll localStorage keys:");
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key && (key.includes('company') || key.includes('Company'))) {
    console.log(`${key}:`, localStorage.getItem(key));
  }
}

// 2. Parse and decode JWT token
const token = localStorage.getItem('auth_token');
if (token) {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    console.log("2. JWT Token Payload:", payload);
    console.log("   - userId:", payload.userId);
    console.log("   - role:", payload.role);
    console.log("   - companyId:", payload.companyId);
  } catch (e) {
    console.error("Error decoding JWT token:", e);
  }
} else {
  console.log("2. No JWT token found");
}

// 3. Check user data
const userData = localStorage.getItem('user_data');
if (userData) {
  try {
    const user = JSON.parse(userData);
    console.log("3. User Data:", user);
    console.log("   - id:", user.id);
    console.log("   - company_id:", user.company_id);
    console.log("   - company_name:", user.company_name);
  } catch (e) {
    console.error("Error parsing user data:", e);
  }
} else {
  console.log("3. No user data found");
}

// 4. Check company context
const selectedCompany = localStorage.getItem('selected_company');
if (selectedCompany) {
  try {
    const company = JSON.parse(selectedCompany);
    console.log("4. Selected Company:", company);
  } catch (e) {
    console.error("Error parsing selected company:", e);
  }
} else {
  console.log("4. No selected company found");
}

// 5. Check current page context (if useContextData is available)
if (window.React && window.React.useContext) {
  console.log("5. React context available - check component state");
} else {
  console.log("5. React context not available in console");
}

console.log("=== END DEBUG ===");

// Instructions
console.log("\nTo fix company ID issues:");
console.log("1. If JWT token has wrong companyId, you need to switch companies or re-login");
console.log("2. If user_data has wrong company_id, clear localStorage and re-login");
console.log("3. If selected_company is wrong, use company switcher to select correct company");

// Quick fix functions
console.log("\n=== QUICK FIX FUNCTIONS ===");
console.log("Run these functions to fix common issues:");

window.debugCompanyFix = {
  clearAll: () => {
    localStorage.clear();
    sessionStorage.clear();
    console.log("✅ Cleared all storage. Please refresh and login again.");
  },

  clearCompanyData: () => {
    const keys = ['selected_company', 'user_data', 'auth_token'];
    keys.forEach(key => localStorage.removeItem(key));
    console.log("✅ Cleared company-related data. Please refresh and login again.");
  },

  forceCompanyId: (newCompanyId) => {
    const userData = localStorage.getItem('user_data');
    if (userData) {
      const user = JSON.parse(userData);
      const oldCompanyId = user.company_id;
      user.company_id = newCompanyId;
      localStorage.setItem('user_data', JSON.stringify(user));
      console.log(`✅ Updated user company_id from ${oldCompanyId} to ${newCompanyId}. Please refresh the page.`);
    } else {
      console.log("❌ No user data found to update.");
    }
  },

  syncCompanyData: () => {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    if (token && userData) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const user = JSON.parse(userData);

        console.log("Current state:");
        console.log("- JWT companyId:", payload.companyId);
        console.log("- User company_id:", user.company_id);

        if (payload.companyId !== user.company_id) {
          console.log("❌ MISMATCH DETECTED!");
          console.log("Syncing user data to match JWT token...");

          user.company_id = payload.companyId;
          localStorage.setItem('user_data', JSON.stringify(user));
          console.log(`✅ Synced user company_id to ${payload.companyId}. Please refresh the page.`);
        } else {
          console.log("✅ JWT and user data are in sync.");
        }
      } catch (e) {
        console.log("❌ Error parsing token or user data:", e);
      }
    } else {
      console.log("❌ Missing token or user data");
    }
  },

  clearReactQueryCache: () => {
    if (window.queryClient) {
      window.queryClient.clear();
      console.log("✅ Cleared React Query cache. Data will be refetched.");
    } else {
      console.log("❌ React Query client not found. Try refreshing the page.");
    }
  },

  invalidateCompanyQueries: () => {
    if (window.queryClient) {
      // Invalidate all company-related queries
      window.queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      window.queryClient.invalidateQueries({ queryKey: ['companies'] });
      console.log("✅ Invalidated company-related queries. Data will be refetched.");
    } else {
      console.log("❌ React Query client not found. Try refreshing the page.");
    }
  },

  fullReset: () => {
    // Clear all storage
    localStorage.clear();
    sessionStorage.clear();

    // Clear React Query cache if available
    if (window.queryClient) {
      window.queryClient.clear();
    }

    console.log("✅ Full reset complete. Please refresh and login again.");
  },

  checkCurrentState: () => {
    const token = localStorage.getItem('auth_token');
    const userData = localStorage.getItem('user_data');

    console.log("=== DETAILED STATE CHECK ===");

    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        console.log("✅ JWT Token:");
        console.log("   - companyId:", payload.companyId);
        console.log("   - userId:", payload.userId);
        console.log("   - role:", payload.role);
      } catch (e) {
        console.log("❌ Invalid JWT token");
      }
    } else {
      console.log("❌ No JWT token found");
    }

    if (userData) {
      try {
        const user = JSON.parse(userData);
        console.log("✅ User Data:");
        console.log("   - company_id:", user.company_id);
        console.log("   - id:", user.id);
        console.log("   - username:", user.username);
        console.log("   - company_name:", user.company_name);
      } catch (e) {
        console.log("❌ Invalid user data");
      }
    } else {
      console.log("❌ No user data found");
    }

    // Check if there's a mismatch
    if (token && userData) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const user = JSON.parse(userData);

        if (payload.companyId !== user.company_id) {
          console.log("🚨 CRITICAL MISMATCH:");
          console.log(`   JWT says company ${payload.companyId}, but user data says ${user.company_id}`);
          console.log("   This will cause API calls to use the wrong company!");
        } else {
          console.log("✅ JWT and user data match - company ID:", payload.companyId);
        }
      } catch (e) {
        console.log("❌ Error comparing JWT and user data");
      }
    }

    console.log("=== END STATE CHECK ===");
  },

  diagnoseCompanyIssue: () => {
    console.log("=== COMPANY ISSUE DIAGNOSIS ===");

    // Check what the current API calls are using
    console.log("1. Check browser Network tab for recent API calls");
    console.log("   Look for URLs like: /api/companies/X/...");
    console.log("   The X should match your expected company ID");

    // Check the current state
    debugCompanyFix.checkCurrentState();

    // Check localStorage for any cached company selections
    const allKeys = Object.keys(localStorage);
    const companyKeys = allKeys.filter(key =>
      key.toLowerCase().includes('company') ||
      key.toLowerCase().includes('selected')
    );

    if (companyKeys.length > 0) {
      console.log("2. Found company-related localStorage keys:");
      companyKeys.forEach(key => {
        console.log(`   ${key}:`, localStorage.getItem(key));
      });
    }

    console.log("3. RECOMMENDED ACTIONS:");
    console.log("   a) If JWT and user data don't match: run debugCompanyFix.syncCompanyData()");
    console.log("   b) If they match but API calls use wrong company: run debugCompanyFix.clearReactQueryCache()");
    console.log("   c) If still broken: run debugCompanyFix.fullReset() and re-login");

    console.log("=== END DIAGNOSIS ===");
  }
};

console.log("Available functions:");
console.log("- debugCompanyFix.clearAll() - Clear all storage");
console.log("- debugCompanyFix.clearCompanyData() - Clear only company data");
console.log("- debugCompanyFix.forceCompanyId(X) - Force company ID to X (replace X with your company ID)");
console.log("- debugCompanyFix.syncCompanyData() - Sync user data with JWT token");
console.log("- debugCompanyFix.clearReactQueryCache() - Clear React Query cache");
console.log("- debugCompanyFix.invalidateCompanyQueries() - Invalidate company queries");
console.log("- debugCompanyFix.fullReset() - Complete reset (storage + cache)");
console.log("- debugCompanyFix.checkCurrentState() - Check current state");
console.log("- debugCompanyFix.diagnoseCompanyIssue() - Full diagnosis of company issues");

console.log("\n🔧 RECOMMENDED STEPS:");
console.log("1. Run debugCompanyFix.diagnoseCompanyIssue() for complete analysis");
console.log("2. Follow the recommended actions from the diagnosis");
console.log("3. If still having issues, run debugCompanyFix.fullReset() and re-login");
