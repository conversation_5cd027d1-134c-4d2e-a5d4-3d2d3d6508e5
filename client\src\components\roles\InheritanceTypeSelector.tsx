import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ArrowDown, ArrowRight, X, Info } from 'lucide-react';

export type InheritanceType = 'inherit' | 'override' | 'deny';

interface InheritanceTypeSelectorProps {
  value: InheritanceType;
  onChange: (value: InheritanceType) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showTooltip?: boolean;
}

interface InheritanceTypeDisplayProps {
  type: InheritanceType;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showTooltip?: boolean;
}

const inheritanceTypeConfig = {
  inherit: {
    label: 'Inherit',
    description: 'Child role inherits all permissions from parent role',
    icon: ArrowDown,
    color: 'bg-green-100 text-green-800 border-green-200',
    variant: 'default' as const,
  },
  override: {
    label: 'Override',
    description: 'Child role permissions override parent permissions when conflicts occur',
    icon: ArrowRight,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    variant: 'secondary' as const,
  },
  deny: {
    label: 'Deny',
    description: 'Child role explicitly denies permissions from parent role',
    icon: X,
    color: 'bg-red-100 text-red-800 border-red-200',
    variant: 'destructive' as const,
  },
};

export function InheritanceTypeDisplay({ 
  type, 
  size = 'md', 
  showIcon = true, 
  showTooltip = true 
}: InheritanceTypeDisplayProps) {
  const config = inheritanceTypeConfig[type];
  const Icon = config.icon;
  
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-sm px-2 py-1',
    lg: 'text-base px-3 py-1.5',
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const badge = (
    <Badge 
      variant={config.variant}
      className={`${sizeClasses[size]} ${config.color} flex items-center gap-1`}
    >
      {showIcon && <Icon className={iconSizes[size]} />}
      {config.label}
    </Badge>
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {badge}
        </TooltipTrigger>
        <TooltipContent>
          <div className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            <span>{config.description}</span>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function InheritanceTypeSelector({ 
  value, 
  onChange, 
  disabled = false, 
  size = 'md',
  showIcon = true,
  showTooltip = true 
}: InheritanceTypeSelectorProps) {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12',
  };

  return (
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className={`w-full ${sizeClasses[size]}`}>
        <SelectValue>
          <InheritanceTypeDisplay 
            type={value} 
            size={size} 
            showIcon={showIcon} 
            showTooltip={false} 
          />
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {Object.entries(inheritanceTypeConfig).map(([type, config]) => (
          <SelectItem key={type} value={type}>
            <div className="flex items-center gap-2">
              <config.icon className="h-4 w-4" />
              <div className="flex flex-col">
                <span className="font-medium">{config.label}</span>
                {showTooltip && (
                  <span className="text-xs text-muted-foreground">
                    {config.description}
                  </span>
                )}
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

// Utility function to get inheritance type configuration
export function getInheritanceTypeConfig(type: InheritanceType) {
  return inheritanceTypeConfig[type];
}

// Utility function to get all inheritance types
export function getAllInheritanceTypes(): InheritanceType[] {
  return Object.keys(inheritanceTypeConfig) as InheritanceType[];
}
