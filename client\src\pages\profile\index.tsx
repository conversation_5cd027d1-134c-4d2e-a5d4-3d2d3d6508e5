import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/lib/auth";
import { CalendarIcon, UserIcon, MailIcon, AtSignIcon, BadgeCheck, Building2, Loader2, KeyRound } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ChangePassword from "./change-password";

export default function Profile() {
  const { getCurrentUser, isAuthenticated } = useAuth();
  const user = getCurrentUser();
  const [activeTab, setActiveTab] = useState("profile");
  
  const userInitials = user?.full_name
    ? user.full_name.split(" ").map((name) => name[0]).join("").substring(0, 2).toUpperCase()
    : "U";
  
  // Fetch user details including join date from API using React Query
  const { 
    data: userData,
    isLoading,
    error
  } = useQuery({
    queryKey: [`/api/users/${user?.id}`],
    enabled: !!user?.id && isAuthenticated()
  });
  
  const formattedJoinDate = userData?.created_at 
    ? new Date(userData.created_at).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "N/A";
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">My Profile</h1>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile" className="flex items-center">
            <UserIcon className="mr-2 h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center">
            <KeyRound className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Profile Card */}
            <Card className="md:col-span-1">
              <CardHeader className="pb-2">
                <CardTitle>Profile</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col items-center text-center">
                <div className="relative">
                  <Avatar className="h-24 w-24 mb-4">
                    <AvatarFallback className="text-lg">{userInitials}</AvatarFallback>
                  </Avatar>
                  {user?.role && (
                    <Badge className="absolute -bottom-2 left-1/2 transform -translate-x-1/2" variant="outline">
                      {user.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  )}
                </div>
                <h2 className="text-xl font-semibold mt-2">{user?.full_name}</h2>
                <p className="text-muted-foreground text-sm">{user?.email}</p>
                
                {isLoading ? (
                  <div className="flex justify-center mt-4">
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <div className="w-full mt-6 space-y-4">
                    <div className="flex items-start">
                      <AtSignIcon className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                      <div className="flex-1 text-left">
                        <p className="text-sm font-medium">Username</p>
                        <p className="text-sm text-muted-foreground">{user?.username}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <MailIcon className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                      <div className="flex-1 text-left">
                        <p className="text-sm font-medium">Email</p>
                        <p className="text-sm text-muted-foreground">{user?.email}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <Building2 className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                      <div className="flex-1 text-left">
                        <p className="text-sm font-medium">Company</p>
                        <p className="text-sm text-muted-foreground">{userData?.company?.name || 'N/A'}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <CalendarIcon className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                      <div className="flex-1 text-left">
                        <p className="text-sm font-medium">Joined</p>
                        <p className="text-sm text-muted-foreground">{formattedJoinDate}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <BadgeCheck className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                      <div className="flex-1 text-left">
                        <p className="text-sm font-medium">Status</p>
                        <Badge variant={user?.active ? "default" : "secondary"}>
                          {user?.active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Account Details */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Account Details</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium">Personal Information</h3>
                      <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Full Name</p>
                          <p className="text-base">{user?.full_name}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Username</p>
                          <p className="text-base">{user?.username}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Email</p>
                          <p className="text-base">{user?.email}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Role</p>
                          <p className="text-base capitalize">{user?.role?.replace('_', ' ')}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium">Company Information</h3>
                      <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Company</p>
                          <p className="text-base">{userData?.company?.name || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Company ID</p>
                          <p className="text-base">{userData?.company?.id || 'N/A'}</p>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium">Account Information</h3>
                      <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Account Status</p>
                          <Badge variant={user?.active ? "default" : "secondary"} className="mt-1">
                            {user?.active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Joined Date</p>
                          <p className="text-base">{formattedJoinDate}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Last Login</p>
                          <p className="text-base">
                            {userData?.last_login_at 
                              ? new Date(userData.last_login_at).toLocaleString() 
                              : 'N/A'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="security">
          <ChangePassword />
        </TabsContent>
      </Tabs>
    </div>
  );
}
