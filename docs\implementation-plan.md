# User Management Implementation Plan - Phase 6 Security Enhancements

## Executive Summary

This document provides a detailed implementation plan for completing the remaining 5% of user management functionality, focusing on critical security enhancements that will bring the system to 100% completion.

## Phase 6: Security Enhancements - Detailed Implementation

### Priority 1: Multi-Factor Authentication (MFA) Implementation

#### 6.1.1 Database Schema Updates

**File**: `server/migrations/019_mfa_system.sql`
```sql
-- MFA settings table
CREATE TABLE IF NOT EXISTS "user_mfa_settings" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "is_enabled" BOOLEAN DEFAULT false,
  "secret" TEXT, -- TOTP secret
  "backup_codes" JSONB DEFAULT '[]', -- Array of backup codes
  "last_used_at" TIMESTAMP,
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW(),
  UNIQUE("user_id")
);

-- MFA verification attempts
CREATE TABLE IF NOT EXISTS "mfa_verification_attempts" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "attempt_type" VARCHAR(20) NOT NULL, -- 'totp', 'backup_code'
  "success" BOOLEAN NOT NULL,
  "ip_address" INET,
  "user_agent" TEXT,
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Add MFA policy to companies
ALTER TABLE "companies" ADD COLUMN IF NOT EXISTS "mfa_required" BOOLEAN DEFAULT false;
ALTER TABLE "companies" ADD COLUMN IF NOT EXISTS "mfa_grace_period_days" INTEGER DEFAULT 30;
```

#### 6.1.2 MFA Service Implementation

**File**: `server/services/mfaService.ts`
```typescript
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import crypto from 'crypto';
import { db } from '../db';
import { userMfaSettings, mfaVerificationAttempts } from '@shared/schema';
import { eq } from 'drizzle-orm';

export class MFAService {
  async generateMFASecret(userId: number, userEmail: string): Promise<{
    secret: string;
    qrCodeUrl: string;
    backupCodes: string[];
  }> {
    const secret = speakeasy.generateSecret({
      name: `TrackFina (${userEmail})`,
      issuer: 'TrackFina',
      length: 32
    });

    const backupCodes = this.generateBackupCodes();
    
    // Store in database
    await db.insert(userMfaSettings).values({
      user_id: userId,
      secret: secret.base32,
      backup_codes: backupCodes,
      is_enabled: false
    }).onConflictDoUpdate({
      target: userMfaSettings.user_id,
      set: {
        secret: secret.base32,
        backup_codes: backupCodes,
        updated_at: new Date()
      }
    });

    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

    return {
      secret: secret.base32,
      qrCodeUrl,
      backupCodes
    };
  }

  async verifyMFAToken(userId: number, token: string, ipAddress?: string, userAgent?: string): Promise<boolean> {
    const [mfaSettings] = await db
      .select()
      .from(userMfaSettings)
      .where(eq(userMfaSettings.user_id, userId));

    if (!mfaSettings || !mfaSettings.secret) {
      return false;
    }

    let isValid = false;
    let attemptType = 'totp';

    // Try TOTP verification
    if (token.length === 6) {
      isValid = speakeasy.totp.verify({
        secret: mfaSettings.secret,
        encoding: 'base32',
        token,
        window: 2 // Allow 2 time steps of variance
      });
    }

    // Try backup code verification if TOTP failed
    if (!isValid && token.length === 8) {
      const backupCodes = mfaSettings.backup_codes as string[];
      if (backupCodes.includes(token)) {
        isValid = true;
        attemptType = 'backup_code';
        
        // Remove used backup code
        const updatedCodes = backupCodes.filter(code => code !== token);
        await db.update(userMfaSettings)
          .set({ backup_codes: updatedCodes })
          .where(eq(userMfaSettings.user_id, userId));
      }
    }

    // Log verification attempt
    await db.insert(mfaVerificationAttempts).values({
      user_id: userId,
      attempt_type: attemptType,
      success: isValid,
      ip_address: ipAddress,
      user_agent: userAgent
    });

    if (isValid) {
      await db.update(userMfaSettings)
        .set({ last_used_at: new Date() })
        .where(eq(userMfaSettings.user_id, userId));
    }

    return isValid;
  }

  async enableMFA(userId: number): Promise<void> {
    await db.update(userMfaSettings)
      .set({ is_enabled: true, updated_at: new Date() })
      .where(eq(userMfaSettings.user_id, userId));
  }

  async disableMFA(userId: number): Promise<void> {
    await db.update(userMfaSettings)
      .set({ is_enabled: false, updated_at: new Date() })
      .where(eq(userMfaSettings.user_id, userId));
  }

  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return codes;
  }

  async isMFARequired(userId: number): Promise<boolean> {
    // Check company MFA policy
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      with: { company: true }
    });

    return user?.company?.mfa_required || false;
  }
}

export const mfaService = new MFAService();
```

#### 6.1.3 API Routes for MFA

**File**: `server/routes/mfa.routes.ts`
```typescript
import { Express, Request, Response } from 'express';
import { authMiddleware, AuthRequest } from '../middleware/auth';
import { mfaService } from '../services/mfaService';
import { z } from 'zod';

const setupMFASchema = z.object({
  email: z.string().email()
});

const verifyMFASchema = z.object({
  token: z.string().min(6).max(8)
});

export function registerMFARoutes(app: Express): void {
  // Setup MFA
  app.post('/api/auth/mfa/setup', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = setupMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const mfaData = await mfaService.generateMFASecret(req.user.id, result.data.email);
      
      return res.json({
        qrCodeUrl: mfaData.qrCodeUrl,
        backupCodes: mfaData.backupCodes,
        message: 'MFA setup initiated. Scan QR code with authenticator app.'
      });
    } catch (error) {
      console.error('MFA setup error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Verify and enable MFA
  app.post('/api/auth/mfa/verify-setup', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = verifyMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const isValid = await mfaService.verifyMFAToken(
        req.user.id, 
        result.data.token,
        req.ip,
        req.get('User-Agent')
      );

      if (!isValid) {
        return res.status(400).json({ message: 'Invalid MFA token' });
      }

      await mfaService.enableMFA(req.user.id);

      return res.json({ message: 'MFA enabled successfully' });
    } catch (error) {
      console.error('MFA verification error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Verify MFA during login
  app.post('/api/auth/mfa/verify', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = verifyMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const isValid = await mfaService.verifyMFAToken(
        req.user.id, 
        result.data.token,
        req.ip,
        req.get('User-Agent')
      );

      if (!isValid) {
        return res.status(400).json({ message: 'Invalid MFA token' });
      }

      return res.json({ message: 'MFA verification successful' });
    } catch (error) {
      console.error('MFA verification error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Disable MFA
  app.post('/api/auth/mfa/disable', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = verifyMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const isValid = await mfaService.verifyMFAToken(
        req.user.id, 
        result.data.token,
        req.ip,
        req.get('User-Agent')
      );

      if (!isValid) {
        return res.status(400).json({ message: 'Invalid MFA token' });
      }

      await mfaService.disableMFA(req.user.id);

      return res.json({ message: 'MFA disabled successfully' });
    } catch (error) {
      console.error('MFA disable error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get MFA status
  app.get('/api/auth/mfa/status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const isRequired = await mfaService.isMFARequired(req.user.id);
      
      return res.json({ 
        required: isRequired,
        message: isRequired ? 'MFA is required for your account' : 'MFA is optional'
      });
    } catch (error) {
      console.error('MFA status error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
```

### Priority 2: Email Verification System

#### 6.2.1 Email Verification Service

**File**: `server/services/emailVerificationService.ts`
```typescript
import crypto from 'crypto';
import { db } from '../db';
import { users, emailVerificationTokens } from '@shared/schema';
import { eq, and, gt } from 'drizzle-orm';

export class EmailVerificationService {
  async generateVerificationToken(userId: number): Promise<string> {
    const token = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await db.insert(emailVerificationTokens).values({
      user_id: userId,
      token,
      expires_at: expiresAt
    });

    return token;
  }

  async verifyEmail(token: string): Promise<{ success: boolean; message: string }> {
    const [verificationRecord] = await db
      .select()
      .from(emailVerificationTokens)
      .where(
        and(
          eq(emailVerificationTokens.token, token),
          gt(emailVerificationTokens.expires_at, new Date())
        )
      );

    if (!verificationRecord) {
      return { success: false, message: 'Invalid or expired verification token' };
    }

    // Update user as verified
    await db.update(users)
      .set({ email_verified: true })
      .where(eq(users.id, verificationRecord.user_id));

    // Delete used token
    await db.delete(emailVerificationTokens)
      .where(eq(emailVerificationTokens.id, verificationRecord.id));

    return { success: true, message: 'Email verified successfully' };
  }

  async sendVerificationEmail(userId: number, email: string): Promise<void> {
    const token = await this.generateVerificationToken(userId);
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;

    // In production, integrate with email service (SendGrid, AWS SES, etc.)
    console.log(`Verification email for ${email}: ${verificationUrl}`);
    
    // TODO: Implement actual email sending
    // await emailService.send({
    //   to: email,
    //   subject: 'Verify your email address',
    //   template: 'email-verification',
    //   data: { verificationUrl }
    // });
  }
}

export const emailVerificationService = new EmailVerificationService();
```

## Next Steps

1. **Review and approve** this implementation plan
2. **Set up development environment** with required dependencies
3. **Begin Phase 6 implementation** starting with MFA system
4. **Conduct thorough testing** of each component
5. **Deploy incrementally** with feature flags for gradual rollout

## Dependencies to Install

```bash
npm install speakeasy qrcode
npm install --save-dev @types/speakeasy @types/qrcode
```

## Estimated Timeline

- **Week 11**: Complete Phase 6 security enhancements
- **Week 12**: Optional Phase 7 UX enhancements
- **Total effort**: 20-30 hours for complete implementation

This implementation plan provides the foundation for completing the remaining user management features while maintaining the high code quality standards established in the existing codebase.
