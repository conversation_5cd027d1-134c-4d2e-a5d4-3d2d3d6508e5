import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Clock, AlertTriangle, ArrowUp, ArrowRight, Bell, UserPlus, CheckCircle, Trash2, Plus } from 'lucide-react';
import { ApproverSelector } from './ApproverSelector';

export type EscalationAction = 'notify' | 'reassign' | 'auto_approve' | 'escalate_to_role';

export interface EscalationRule {
  id?: number;
  timeoutHours: number;
  action: EscalationAction;
  targetRoles?: number[];
  targetUsers?: number[];
  notificationMessage?: string;
  isActive: boolean;
  priority: 'low' | 'medium' | 'high' | 'emergency';
}

interface EscalationRuleEditorProps {
  rules: EscalationRule[];
  onChange: (rules: EscalationRule[]) => void;
  companyId?: number;
  disabled?: boolean;
  maxRules?: number;
}

const escalationActionConfig = {
  notify: {
    label: 'Notify Only',
    description: 'Send notification but keep current approvers',
    icon: Bell,
    color: 'bg-blue-100 text-blue-800',
    requiresTargets: true,
  },
  reassign: {
    label: 'Reassign',
    description: 'Transfer approval responsibility to new approvers',
    icon: UserPlus,
    color: 'bg-yellow-100 text-yellow-800',
    requiresTargets: true,
  },
  auto_approve: {
    label: 'Auto Approve',
    description: 'Automatically approve the step',
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800',
    requiresTargets: false,
  },
  escalate_to_role: {
    label: 'Escalate to Role',
    description: 'Escalate to higher-level roles',
    icon: ArrowUp,
    color: 'bg-red-100 text-red-800',
    requiresTargets: true,
  },
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  emergency: { label: 'Emergency', color: 'bg-red-100 text-red-800' },
};

export function EscalationRuleEditor({
  rules,
  onChange,
  companyId,
  disabled = false,
  maxRules = 5,
}: EscalationRuleEditorProps) {
  const [editingRule, setEditingRule] = useState<EscalationRule | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const canAddMore = rules.length < maxRules;

  const handleAddRule = () => {
    const newRule: EscalationRule = {
      timeoutHours: 24,
      action: 'notify',
      targetRoles: [],
      targetUsers: [],
      notificationMessage: '',
      isActive: true,
      priority: 'medium',
    };
    setEditingRule(newRule);
    setIsCreating(true);
  };

  const handleEditRule = (rule: EscalationRule) => {
    setEditingRule({ ...rule });
    setIsCreating(false);
  };

  const handleSaveRule = () => {
    if (!editingRule) return;

    if (isCreating) {
      const newRules = [...rules, { ...editingRule, id: Date.now() }];
      onChange(newRules);
    } else {
      const newRules = rules.map(rule =>
        rule.id === editingRule.id ? editingRule : rule
      );
      onChange(newRules);
    }

    setEditingRule(null);
    setIsCreating(false);
  };

  const handleCancelEdit = () => {
    setEditingRule(null);
    setIsCreating(false);
  };

  const handleDeleteRule = (ruleId: number) => {
    const newRules = rules.filter(rule => rule.id !== ruleId);
    onChange(newRules);
  };

  const handleToggleRule = (ruleId: number) => {
    const newRules = rules.map(rule =>
      rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule
    );
    onChange(newRules);
  };

  const updateEditingRule = (updates: Partial<EscalationRule>) => {
    if (!editingRule) return;
    setEditingRule({ ...editingRule, ...updates });
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Escalation Rules</h3>
          <p className="text-sm text-muted-foreground">
            Configure automatic escalation when approvals timeout
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {rules.length} / {maxRules} rules
          </Badge>
          {canAddMore && (
            <Button
              onClick={handleAddRule}
              disabled={disabled || !!editingRule}
              size="sm"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Rule
            </Button>
          )}
        </div>
      </div>

      {/* Existing Rules */}
      {rules.length > 0 && (
        <div className="space-y-3">
          {rules.map((rule) => (
            <Card key={rule.id} className={`${!rule.isActive ? 'opacity-60' : ''}`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{rule.timeoutHours}h</span>
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    <Badge className={escalationActionConfig[rule.action].color}>
                      {escalationActionConfig[rule.action].label}
                    </Badge>
                    <Badge variant="outline" className={priorityConfig[rule.priority].color}>
                      {priorityConfig[rule.priority].label}
                    </Badge>
                    {!rule.isActive && (
                      <Badge variant="secondary">Disabled</Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={rule.isActive}
                      onCheckedChange={() => rule.id && handleToggleRule(rule.id)}
                      disabled={disabled}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditRule(rule)}
                      disabled={disabled || !!editingRule}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => rule.id && handleDeleteRule(rule.id)}
                      disabled={disabled}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {rule.notificationMessage && (
                  <p className="text-sm text-muted-foreground mt-2">
                    "{rule.notificationMessage}"
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Rule Editor */}
      {editingRule && (
        <Card className="border-2 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              {isCreating ? 'Create Escalation Rule' : 'Edit Escalation Rule'}
            </CardTitle>
            <CardDescription>
              Configure when and how to escalate this workflow step
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Settings */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="timeout">Timeout (hours)</Label>
                <Input
                  id="timeout"
                  type="number"
                  min="1"
                  max="168"
                  value={editingRule.timeoutHours}
                  onChange={(e) => updateEditingRule({ timeoutHours: parseInt(e.target.value) || 1 })}
                />
              </div>
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={editingRule.priority}
                  onValueChange={(value: any) => updateEditingRule({ priority: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(priorityConfig).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        <Badge variant="outline" className={config.color}>
                          {config.label}
                        </Badge>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Escalation Action */}
            <div>
              <Label htmlFor="action">Escalation Action</Label>
              <Select
                value={editingRule.action}
                onValueChange={(value: EscalationAction) => updateEditingRule({ action: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(escalationActionConfig).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center gap-2">
                        <config.icon className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{config.label}</div>
                          <div className="text-xs text-muted-foreground">{config.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Target Selection */}
            {escalationActionConfig[editingRule.action].requiresTargets && (
              <div>
                <Label>Escalation Targets</Label>
                <ApproverSelector
                  selectedRoles={editingRule.targetRoles || []}
                  selectedUsers={editingRule.targetUsers || []}
                  onRolesChange={(roles) => updateEditingRule({ targetRoles: roles })}
                  onUsersChange={(users) => updateEditingRule({ targetUsers: users })}
                  companyId={companyId}
                  showCounts={false}
                />
              </div>
            )}

            {/* Notification Message */}
            <div>
              <Label htmlFor="message">Notification Message (Optional)</Label>
              <Textarea
                id="message"
                placeholder="Custom message to include in escalation notifications..."
                value={editingRule.notificationMessage || ''}
                onChange={(e) => updateEditingRule({ notificationMessage: e.target.value })}
                rows={3}
              />
            </div>

            {/* Active Toggle */}
            <div className="flex items-center space-x-2">
              <Switch
                checked={editingRule.isActive}
                onCheckedChange={(checked) => updateEditingRule({ isActive: checked })}
              />
              <Label>Enable this escalation rule</Label>
            </div>

            <Separator />

            {/* Actions */}
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancelEdit}>
                Cancel
              </Button>
              <Button onClick={handleSaveRule}>
                {isCreating ? 'Create Rule' : 'Save Changes'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {rules.length === 0 && !editingRule && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Clock className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Escalation Rules</h3>
            <p className="text-muted-foreground text-center mb-4">
              Add escalation rules to automatically handle approval timeouts
            </p>
            {canAddMore && (
              <Button onClick={handleAddRule} disabled={disabled}>
                <Plus className="h-4 w-4 mr-1" />
                Add First Rule
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
