import PDFDocument from 'pdfkit';
import { Collection, Payment, Customer } from '@shared/schema';
import * as fs from 'fs';
import errorLogger from './errorLogger';

export class PDFGenerator {
  static async generateReceipt(
    payment: Payment, 
    collection: Collection, 
    customer: Customer
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({
          size: 'A4',
          margins: { top: 50, bottom: 50, left: 50, right: 50 }
        });
        
        const chunks: Buffer[] = [];
        doc.on('data', chunk => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        
        // Receipt header
        doc.fontSize(20).font('Helvetica-Bold').text('PAYMENT RECEIPT', { align: 'center' });
        doc.fontSize(12).font('Helvetica').text(`Receipt No: ${payment.receipt_number}`, { align: 'center' });
        doc.moveDown();
        
        // Draw a horizontal line
        doc.moveTo(50, doc.y).lineTo(doc.page.width - 50, doc.y).stroke();
        doc.moveDown();
        
        // Payment details
        doc.fontSize(16).font('Helvetica-Bold').text('Payment Details', { underline: true });
        doc.moveDown(0.5);
        doc.fontSize(12).font('Helvetica');
        doc.text(`Amount Paid: ₹${parseFloat(payment.amount.toString()).toLocaleString()}`);
        doc.text(`Payment Date: ${new Date(payment.payment_date).toLocaleDateString()}`);
        doc.text(`Payment Method: ${payment.payment_method.toUpperCase()}`);
        if (payment.fine_amount && parseFloat(payment.fine_amount.toString()) > 0) {
          doc.text(`Fine Amount: ₹${parseFloat(payment.fine_amount.toString()).toLocaleString()}`);
        }
        doc.moveDown();
        
        // Customer details
        doc.fontSize(16).font('Helvetica-Bold').text('Customer Details', { underline: true });
        doc.moveDown(0.5);
        doc.fontSize(12).font('Helvetica');
        doc.text(`Name: ${customer.full_name}`);
        doc.text(`Phone: ${customer.phone}`);
        if (customer.email) {
          doc.text(`Email: ${customer.email}`);
        }
        doc.moveDown();
        
        // Loan details
        doc.fontSize(16).font('Helvetica-Bold').text('Loan Details', { underline: true });
        doc.moveDown(0.5);
        doc.fontSize(12).font('Helvetica');
        doc.text(`Loan ID: ${collection.loan_id}`);
        doc.text(`Schedule Date: ${new Date(collection.scheduled_date).toLocaleDateString()}`);
        doc.text(`Collection Date: ${new Date(collection.collection_date || collection.scheduled_date).toLocaleDateString()}`);
        doc.moveDown();
        
        // Add notes if available
        if (payment.notes) {
          doc.fontSize(16).font('Helvetica-Bold').text('Notes', { underline: true });
          doc.moveDown(0.5);
          doc.fontSize(12).font('Helvetica');
          doc.text(payment.notes);
          doc.moveDown();
        }
        
        // Footer
        doc.moveDown(2);
        doc.fontSize(10).text('This is a computer-generated receipt and doesn\'t require a signature.', { align: 'center' });
        
        // Add current date at the bottom
        const currentDate = new Date().toLocaleDateString();
        doc.moveDown();
        doc.fontSize(10).text(`Generated on: ${currentDate}`, { align: 'center' });
        
        errorLogger.logInfo(`Generated PDF receipt for payment ID=${payment.id}, receipt=${payment.receipt_number}`, 'pdf-generator');
        
        doc.end();
      } catch (error) {
        errorLogger.logError(`Failed to generate PDF receipt for payment ID=${payment.id}`, 'pdf-generator', error as Error);
        reject(error);
      }
    });
  }
}