import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockFetchSuccess, mockFetchError } from '../../../tests/utils';
import UserManagement from '../index';

// Mock the wouter navigation
const mockNavigate = vi.fn();
vi.mock('wouter', () => ({
  useLocation: () => ['/', mockNavigate],
  useRoute: () => [false, {}],
  Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
  Route: ({ children }: any) => children,
  Switch: ({ children }: any) => children,
}));

describe('UserManagement', () => {
  const mockUsers = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      full_name: 'Admin User',
      role: 'admin',
      is_active: true,
      created_at: '2023-01-01T00:00:00Z',
    },
    {
      id: 2,
      username: 'manager',
      email: '<EMAIL>',
      full_name: 'Manager User',
      role: 'manager',
      is_active: true,
      created_at: '2023-01-02T00:00:00Z',
    },
  ];

  const mockRoles = [
    {
      id: 1,
      name: 'Admin',
      description: 'Administrator role',
      is_system: true,
      permissions: [1, 2, 3],
    },
    {
      id: 2,
      name: 'Manager',
      description: 'Manager role',
      is_system: false,
      permissions: [1, 2],
    },
  ];

  const mockGroups = [
    {
      id: 1,
      name: 'Administrators',
      description: 'Admin group',
      member_count: 5,
    },
    {
      id: 2,
      name: 'Managers',
      description: 'Manager group',
      member_count: 10,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock API responses
    global.fetch = vi.fn()
      .mockImplementationOnce(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockUsers),
      }))
      .mockImplementationOnce(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockRoles),
      }))
      .mockImplementationOnce(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockGroups),
      }));
  });

  describe('Initial Rendering', () => {
    it('should render the user management page with tabs', async () => {
      render(<UserManagement />);

      expect(screen.getByText('User Management')).toBeInTheDocument();
      expect(screen.getByText('Manage users, roles, and permissions for your organization')).toBeInTheDocument();

      // Check tabs
      expect(screen.getByRole('tab', { name: /users/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /roles/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /groups/i })).toBeInTheDocument();
    });

    it('should show loading state initially', () => {
      render(<UserManagement />);
      
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    it('should load and display users data', async () => {
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
        expect(screen.getByText('manager')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });
  });

  describe('Tab Navigation', () => {
    it('should switch between tabs correctly', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Switch to Roles tab
      const rolesTab = screen.getByRole('tab', { name: /roles/i });
      await user.click(rolesTab);

      expect(rolesTab).toHaveAttribute('aria-selected', 'true');
      expect(screen.getByText('Admin')).toBeInTheDocument();
      expect(screen.getByText('Manager')).toBeInTheDocument();

      // Switch to Groups tab
      const groupsTab = screen.getByRole('tab', { name: /groups/i });
      await user.click(groupsTab);

      expect(groupsTab).toHaveAttribute('aria-selected', 'true');
      expect(screen.getByText('Administrators')).toBeInTheDocument();
      expect(screen.getByText('Managers')).toBeInTheDocument();
    });
  });

  describe('Users Tab', () => {
    it('should display user information in table format', async () => {
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('Admin User')).toBeInTheDocument();
        expect(screen.getByText('Manager User')).toBeInTheDocument();
      });

      // Check table headers
      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Username')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('Role')).toBeInTheDocument();
    });

    it('should have action buttons for each user', async () => {
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Check for action buttons
      const editButtons = screen.getAllByText('Edit');
      const roleButtons = screen.getAllByText('Roles');
      const permissionButtons = screen.getAllByText('Permissions');

      expect(editButtons).toHaveLength(2);
      expect(roleButtons).toHaveLength(2);
      expect(permissionButtons).toHaveLength(2);
    });

    it('should navigate to user permissions when permissions button is clicked', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      const permissionButtons = screen.getAllByText('Permissions');
      await user.click(permissionButtons[0]);

      expect(mockNavigate).toHaveBeenCalledWith('/user-management/permissions/user-permissions?userId=1');
    });

    it('should handle user data loading errors', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Failed to load users'));

      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText(/error loading users/i)).toBeInTheDocument();
      });
    });
  });

  describe('Roles Tab', () => {
    it('should display role information', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Switch to roles tab
      const rolesTab = screen.getByRole('tab', { name: /roles/i });
      await user.click(rolesTab);

      await waitFor(() => {
        expect(screen.getByText('Admin')).toBeInTheDocument();
        expect(screen.getByText('Administrator role')).toBeInTheDocument();
        expect(screen.getByText('Manager')).toBeInTheDocument();
        expect(screen.getByText('Manager role')).toBeInTheDocument();
      });
    });

    it('should show system role indicators', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      const rolesTab = screen.getByRole('tab', { name: /roles/i });
      await user.click(rolesTab);

      await waitFor(() => {
        // Should show system role badge for Admin role
        expect(screen.getByText(/system/i)).toBeInTheDocument();
      });
    });

    it('should have role management actions', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      const rolesTab = screen.getByRole('tab', { name: /roles/i });
      await user.click(rolesTab);

      await waitFor(() => {
        // Look for role action buttons
        const viewButtons = screen.getAllByText(/view/i);
        const editButtons = screen.getAllByText(/edit/i);
        
        expect(viewButtons.length).toBeGreaterThan(0);
        expect(editButtons.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Groups Tab', () => {
    it('should display group information', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Switch to groups tab
      const groupsTab = screen.getByRole('tab', { name: /groups/i });
      await user.click(groupsTab);

      await waitFor(() => {
        expect(screen.getByText('Administrators')).toBeInTheDocument();
        expect(screen.getByText('Admin group')).toBeInTheDocument();
        expect(screen.getByText('Managers')).toBeInTheDocument();
        expect(screen.getByText('Manager group')).toBeInTheDocument();
      });
    });

    it('should show member counts for groups', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      const groupsTab = screen.getByRole('tab', { name: /groups/i });
      await user.click(groupsTab);

      await waitFor(() => {
        expect(screen.getByText('5')).toBeInTheDocument(); // Administrators member count
        expect(screen.getByText('10')).toBeInTheDocument(); // Managers member count
      });
    });
  });

  describe('Search and Filtering', () => {
    it('should filter users based on search input', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
        expect(screen.getByText('manager')).toBeInTheDocument();
      });

      // Find search input (implementation specific)
      const searchInput = screen.queryByPlaceholderText(/search/i);
      if (searchInput) {
        await user.type(searchInput, 'admin');

        // Should show only admin user
        expect(screen.getByText('admin')).toBeInTheDocument();
        expect(screen.queryByText('manager')).not.toBeInTheDocument();
      }
    });
  });

  describe('Permission Integration', () => {
    it('should show permission viewing buttons', async () => {
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Check for permission-related buttons
      const permissionButtons = screen.getAllByText('Permissions');
      expect(permissionButtons).toHaveLength(2);

      // Check for role management buttons
      const roleButtons = screen.getAllByText('Roles');
      expect(roleButtons).toHaveLength(2);
    });

    it('should navigate to permission matrix', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Look for permission matrix navigation (implementation specific)
      const matrixLink = screen.queryByText(/permission matrix/i);
      if (matrixLink) {
        await user.click(matrixLink);
        expect(mockNavigate).toHaveBeenCalledWith('/user-management/permissions');
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      global.fetch = vi.fn()
        .mockRejectedValueOnce(new Error('Users API failed'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockRoles),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockGroups),
        });

      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });

    it('should handle empty data sets', async () => {
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([]),
        });

      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText(/no users found/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', async () => {
      render(<UserManagement />);

      expect(screen.getByRole('heading', { name: /user management/i })).toBeInTheDocument();
    });

    it('should have accessible table structure', async () => {
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
      });

      // Check for table headers
      const columnHeaders = screen.getAllByRole('columnheader');
      expect(columnHeaders.length).toBeGreaterThan(0);
    });

    it('should have accessible tab navigation', async () => {
      render(<UserManagement />);

      expect(screen.getByRole('tablist')).toBeInTheDocument();
      
      const tabs = screen.getAllByRole('tab');
      expect(tabs).toHaveLength(3);
      
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected');
      });
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Tab through interactive elements
      await user.tab();
      
      // Should focus on first interactive element
      expect(document.activeElement).toBeInstanceOf(HTMLElement);
    });
  });

  describe('Responsive Design', () => {
    it('should handle mobile viewport', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<UserManagement />);

      await waitFor(() => {
        expect(screen.getByText('admin')).toBeInTheDocument();
      });

      // Component should still render properly on mobile
      expect(screen.getByText('User Management')).toBeInTheDocument();
    });
  });
});
