import { Request, Response } from 'express';

/**
 * Make an API request
 * @param method HTTP method
 * @param url URL to request
 * @param data Optional data to send
 * @param headers Optional headers
 * @returns Promise with fetch response
 */
export async function apiRequest(
  method: string,
  url: string,
  data?: any,
  headers: HeadersInit = {}
): Promise<Response> {
  const options: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    credentials: 'include',
  };

  if (data && method !== 'GET') {
    options.body = JSON.stringify(data);
  }

  return fetch(url, options);
}
