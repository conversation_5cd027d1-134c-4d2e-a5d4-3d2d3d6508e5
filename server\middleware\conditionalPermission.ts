import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from './auth';
import { 
  conditionalPermissionService, 
  PermissionEvaluationContext 
} from '../services/conditionalPermissionService';
import { enhancedPermissionService } from '../services/enhancedPermissionService';

export interface ConditionalPermissionOptions {
  permissionCode: string;
  amountField?: string;
  extractContext?: (req: AuthRequest) => Partial<PermissionEvaluationContext>;
  onApprovalRequired?: (req: AuthRequest, res: Response, approverRoles: string[]) => void;
}

/**
 * Middleware to check permissions with conditional evaluation
 * @param options Configuration options for conditional permission checking
 * @returns Middleware function
 */
export function requireConditionalPermission(options: ConditionalPermissionOptions) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Build evaluation context
      const context: PermissionEvaluationContext = {
        userId: req.user.id,
        permissionCode: options.permissionCode,
        timestamp: new Date(),
        ipAddress: getClientIP(req),
        userAgent: req.get('User-Agent'),
        ...extractDefaultContext(req),
        ...(options.extractContext ? options.extractContext(req) : {})
      };

      // Extract amount if specified
      if (options.amountField) {
        const amount = extractAmount(req, options.amountField);
        if (amount !== undefined) {
          context.amount = amount;
        }
      }

      // Check permission with conditions
      const result = await enhancedPermissionService.checkPermissionWithConditions(context);

      if (!result.allowed) {
        if (result.requiresApproval) {
          // Handle approval requirement
          if (options.onApprovalRequired) {
            return options.onApprovalRequired(req, res, result.approverRoles || []);
          } else {
            return res.status(202).json({
              message: 'Approval required for this operation',
              requiresApproval: true,
              approverRoles: result.approverRoles,
              reason: result.reason
            });
          }
        } else {
          return res.status(403).json({
            message: 'Access denied',
            reason: result.reason,
            required_permission: options.permissionCode
          });
        }
      }

      // Permission granted, continue
      next();
    } catch (error) {
      console.error('Error in conditional permission middleware:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  };
}

/**
 * Middleware for loan creation with conditional permissions
 */
export function requireLoanCreationPermission(amountField: string = 'amount') {
  return requireConditionalPermission({
    permissionCode: 'loan_create_basic',
    amountField,
    extractContext: (req) => ({
      deviceInfo: extractDeviceInfo(req),
      sessionInfo: extractSessionInfo(req),
      location: extractLocationInfo(req)
    })
  });
}

/**
 * Middleware for loan approval with conditional permissions
 */
export function requireLoanApprovalPermission(amountField: string = 'amount') {
  return requireConditionalPermission({
    permissionCode: 'loan_approve_tier2',
    amountField,
    extractContext: (req) => ({
      deviceInfo: extractDeviceInfo(req),
      sessionInfo: extractSessionInfo(req),
      location: extractLocationInfo(req)
    }),
    onApprovalRequired: (req, res, approverRoles) => {
      // Custom approval handling for loans
      res.status(202).json({
        message: 'Loan approval requires additional authorization',
        requiresApproval: true,
        approverRoles,
        nextSteps: 'Please contact your manager or submit for approval workflow'
      });
    }
  });
}

/**
 * Middleware for customer data export with conditional permissions
 */
export function requireCustomerExportPermission() {
  return requireConditionalPermission({
    permissionCode: 'customer_export_financial',
    extractContext: (req) => ({
      deviceInfo: extractDeviceInfo(req),
      sessionInfo: extractSessionInfo(req),
      location: extractLocationInfo(req)
    })
  });
}

/**
 * Middleware for payment operations with conditional permissions
 */
export function requirePaymentOperationPermission(amountField: string = 'amount') {
  return requireConditionalPermission({
    permissionCode: 'payment_process_automated',
    amountField,
    extractContext: (req) => ({
      deviceInfo: extractDeviceInfo(req),
      sessionInfo: extractSessionInfo(req),
      location: extractLocationInfo(req)
    })
  });
}

/**
 * Extract client IP address from request
 */
function getClientIP(req: Request): string {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    (req.headers['x-real-ip'] as string) ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    '127.0.0.1'
  );
}

/**
 * Extract amount from request body or params
 */
function extractAmount(req: Request, field: string): number | undefined {
  const value = req.body?.[field] || req.params?.[field] || req.query?.[field];
  if (value === undefined || value === null) return undefined;
  
  const amount = typeof value === 'string' ? parseFloat(value) : Number(value);
  return isNaN(amount) ? undefined : amount;
}

/**
 * Extract default context from request
 */
function extractDefaultContext(req: AuthRequest): Partial<PermissionEvaluationContext> {
  return {
    deviceInfo: extractDeviceInfo(req),
    sessionInfo: extractSessionInfo(req),
    location: extractLocationInfo(req)
  };
}

/**
 * Extract device information from request
 */
function extractDeviceInfo(req: Request) {
  const userAgent = req.get('User-Agent') || '';
  
  let deviceType: 'desktop' | 'laptop' | 'mobile' | 'tablet' = 'desktop';
  
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    if (/iPad|Tablet/.test(userAgent)) {
      deviceType = 'tablet';
    } else {
      deviceType = 'mobile';
    }
  } else if (/Laptop/.test(userAgent)) {
    deviceType = 'laptop';
  }

  return {
    type: deviceType,
    id: req.get('X-Device-ID'), // Custom header for device identification
    registered: req.get('X-Device-Registered') === 'true'
  };
}

/**
 * Extract session information from request
 */
function extractSessionInfo(req: AuthRequest) {
  const sessionStart = req.session?.sessionStart || req.user?.created_at;
  const sessionAge = sessionStart ? (Date.now() - new Date(sessionStart).getTime()) / 1000 : 0;
  
  return {
    sessionAge,
    lastActivity: new Date(),
    mfaVerified: req.session?.mfaVerified || false,
    freshAuth: req.session?.freshAuth || false
  };
}

/**
 * Extract location information from request
 */
function extractLocationInfo(req: Request) {
  return {
    country: req.get('X-Country-Code') || req.get('CF-IPCountry'), // Cloudflare header
    region: req.get('X-Region-Code'),
    city: req.get('X-City-Name')
  };
}

/**
 * Utility function to check if permission conditions exist
 * @param permissionCode Permission code to check
 * @returns Promise<boolean> True if conditions exist
 */
export async function hasPermissionConditions(permissionCode: string): Promise<boolean> {
  try {
    const conditions = await conditionalPermissionService.getPermissionConditions(permissionCode);
    return conditions.length > 0;
  } catch (error) {
    console.error('Error checking permission conditions:', error);
    return false;
  }
}

/**
 * Utility function to get permission conditions for display
 * @param permissionCode Permission code
 * @returns Promise<any[]> Array of conditions
 */
export async function getPermissionConditionsForDisplay(permissionCode: string): Promise<any[]> {
  try {
    return await conditionalPermissionService.getPermissionConditions(permissionCode);
  } catch (error) {
    console.error('Error getting permission conditions:', error);
    return [];
  }
}
