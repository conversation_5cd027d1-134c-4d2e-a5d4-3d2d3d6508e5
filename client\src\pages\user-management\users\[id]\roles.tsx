import React, { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/components/auth/auth-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Loader2, Save, ArrowLeft, UserCheck, Shield, AlertTriangle, Lock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Define types
interface User {
  id: number;
  email: string;
  full_name: string;
  role: string;
  company_id: number;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  company_id: number | null;
}

interface UserRole {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  source: string;
  assignment_id: number | null;
}

export default function UserRoleManagement() {
  const [match, params] = useRoute('/user-management/users/:id/roles');
  const userId = parseInt(params?.id || '0');
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const { user: currentUser } = useAuth();
  const queryClient = useQueryClient();

  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);

  // Business logic state
  const isSelfEditing = currentUser?.id === userId;
  const isCurrentUserOwner = currentUser?.role === 'owner' || currentUser?.role === 'saas_admin';

  // Helper functions for business logic
  const isTargetUserOwner = (targetUser: User | undefined, targetUserRoles: UserRole[] | undefined): boolean => {
    if (!targetUser || !targetUserRoles) return false;

    // Check enum role
    if (targetUser.role === 'owner') return true;

    // Check custom roles
    return targetUserRoles.some(role => role.name === 'Owner');
  };

  const getOwnerRoleId = (roles: Role[] | undefined): number | null => {
    if (!roles) return null;
    const ownerRole = roles.find(role => role.name === 'Owner' && role.is_system);
    return ownerRole?.id || null;
  };

  const isOwnerRoleSelected = (roleIds: number[], roles: Role[] | undefined): boolean => {
    const ownerRoleId = getOwnerRoleId(roles);
    return ownerRoleId ? roleIds.includes(ownerRoleId) : false;
  };

  const shouldDisableRole = (roleId: number, roles: Role[] | undefined): boolean => {
    if (isSelfEditing) return true;

    const ownerRoleId = getOwnerRoleId(roles);
    const isOwnerSelected = isOwnerRoleSelected(selectedRoleIds, roles);

    // If Owner is selected, disable all other roles
    if (isOwnerSelected && roleId !== ownerRoleId) return true;

    // If target user is Owner and current user is not Owner/admin, disable Owner role
    if (roleId === ownerRoleId && isTargetUserOwner(user, userRoles) && !isCurrentUserOwner) return true;

    return false;
  };

  // Fetch user details
  const { data: user, isLoading: isLoadingUser } = useQuery({
    queryKey: [`/api/users/${userId}`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/users/${userId}`);
      return res.json();
    },
    enabled: userId > 0
  });

  // Fetch user's current role assignments
  const { data: userRoles, isLoading: isLoadingUserRoles } = useQuery({
    queryKey: [`/api/users/${userId}/roles`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/users/${userId}/roles`);
      return res.json();
    },
    enabled: userId > 0
  });

  // Fetch all available roles
  const { data: allRoles, isLoading: isLoadingRoles } = useQuery({
    queryKey: ['/api/roles'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/roles');
      return res.json();
    }
  });

  // Update user roles mutation
  const updateUserRolesMutation = useMutation({
    mutationFn: async (roleIds: number[]) => {
      const res = await apiRequest('PUT', `/api/users/${userId}/roles`, { roleIds });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/users/${userId}/roles`] });
      queryClient.invalidateQueries({ queryKey: ['/api/companies/13/users'] }); // Refresh users list
      toast({
        title: 'Roles updated',
        description: 'User role assignments have been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating roles',
        description: error.message || 'There was an error updating the user roles.',
        variant: 'destructive',
      });
    }
  });

  // Update selected roles when user roles are loaded
  useEffect(() => {
    if (userRoles) {
      setSelectedRoleIds(userRoles.map((ur: UserRole) => ur.id));
    }
  }, [userRoles]);

  // Handle role selection change
  const handleRoleChange = (roleId: number, checked: boolean) => {
    const ownerRoleId = getOwnerRoleId(allRoles);

    setSelectedRoleIds(prev => {
      if (checked) {
        // If selecting Owner role, clear all other roles (Owner exclusivity)
        if (roleId === ownerRoleId) {
          return [roleId];
        }
        // If selecting any other role while Owner is selected, remove Owner first
        else if (ownerRoleId && prev.includes(ownerRoleId)) {
          return [roleId];
        }
        // Normal addition
        else {
          return [...prev, roleId];
        }
      } else {
        return prev.filter(id => id !== roleId);
      }
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateUserRolesMutation.mutate(selectedRoleIds);
  };

  // Group roles by type
  const systemRoles = allRoles?.filter((role: Role) => role.is_system) || [];
  const customRoles = allRoles?.filter((role: Role) => !role.is_system) || [];

  if (isLoadingUser || isLoadingUserRoles || isLoadingRoles) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!user && userId > 0) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <h2 className="text-2xl font-bold mb-2">User not found</h2>
          <p className="text-muted-foreground mb-4">The user you're looking for doesn't exist or you don't have permission to view them.</p>
          <Button onClick={() => navigate('/user-management')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to User Management
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => navigate('/user-management')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Manage User Roles</h1>
            <p className="text-muted-foreground">
              {user?.full_name} ({user?.email})
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{user?.role}</Badge>
        </div>
      </div>

      {/* Warning Messages */}
      {isSelfEditing && (
        <Alert className="mb-6">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            <strong>Read-Only Mode:</strong> You cannot modify your own role assignments. Contact an administrator if changes are needed.
          </AlertDescription>
        </Alert>
      )}

      {isTargetUserOwner(user, userRoles) && !isCurrentUserOwner && (
        <Alert className="mb-6">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            <strong>Owner Protection:</strong> Owner role assignments cannot be modified by other users. Only the Owner themselves or system administrators can make changes.
          </AlertDescription>
        </Alert>
      )}

      {isOwnerRoleSelected(selectedRoleIds, allRoles) && (
        <Alert className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Owner Role Exclusivity:</strong> The Owner role cannot be combined with other roles. Selecting Owner will automatically deselect all other roles.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Role Assignments */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Current Assignments
                {isSelfEditing && <Lock className="h-4 w-4 text-muted-foreground" />}
              </CardTitle>
              <CardDescription>
                Roles currently assigned to this user
                {isSelfEditing && " (Read-Only)"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userRoles && userRoles.length > 0 ? (
                <div className="space-y-2">
                  {userRoles.map((userRole: UserRole) => (
                    <div key={userRole.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="font-medium">{userRole.name}</p>
                        <p className="text-sm text-muted-foreground">{userRole.description}</p>
                        {userRole.source && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Source: {userRole.source}
                          </p>
                        )}
                      </div>
                      <Badge variant={userRole.is_system ? 'default' : 'outline'}>
                        {userRole.is_system ? 'System' : 'Custom'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No roles assigned</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Role Assignment Form */}
        <div className="lg:col-span-2">
          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Assign Roles
                </CardTitle>
                <CardDescription>
                  Select roles to assign to this user. Changes will be applied immediately.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Alert>
                  <AlertDescription>
                    <strong>Note:</strong> System roles are available to all companies, while custom roles are specific to your organization.
                  </AlertDescription>
                </Alert>

                {/* System Roles */}
                {systemRoles.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      <h3 className="text-lg font-medium">System Roles</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {systemRoles.map((role: Role) => {
                        const isDisabled = shouldDisableRole(role.id, allRoles);
                        const isOwnerRole = role.name === 'Owner';

                        return (
                          <div key={role.id} className={`flex items-start space-x-3 p-3 border rounded-lg ${isDisabled ? 'opacity-50 bg-muted/30' : ''}`}>
                            <Checkbox
                              id={`role-${role.id}`}
                              checked={selectedRoleIds.includes(role.id)}
                              disabled={isDisabled}
                              onCheckedChange={(checked) => handleRoleChange(role.id, !!checked)}
                            />
                            <div className="flex-1">
                              <Label
                                htmlFor={`role-${role.id}`}
                                className={`text-sm font-medium leading-none ${isDisabled ? 'cursor-not-allowed opacity-70' : ''}`}
                              >
                                {role.name}
                                {isDisabled && isOwnerRole && (
                                  <Lock className="inline h-3 w-3 ml-1" />
                                )}
                              </Label>
                              <p className="text-xs text-muted-foreground mt-1">
                                {role.description}
                              </p>
                              <div className="flex gap-1 mt-2">
                                <Badge variant="default" className="text-xs">
                                  System
                                </Badge>
                                {isDisabled && isOwnerRole && (
                                  <Badge variant="outline" className="text-xs">
                                    Protected
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {systemRoles.length > 0 && customRoles.length > 0 && <Separator />}

                {/* Custom Roles */}
                {customRoles.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <UserCheck className="h-4 w-4" />
                      <h3 className="text-lg font-medium">Custom Roles</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {customRoles.map((role: Role) => {
                        const isDisabled = shouldDisableRole(role.id, allRoles);

                        return (
                          <div key={role.id} className={`flex items-start space-x-3 p-3 border rounded-lg ${isDisabled ? 'opacity-50 bg-muted/30' : ''}`}>
                            <Checkbox
                              id={`role-${role.id}`}
                              checked={selectedRoleIds.includes(role.id)}
                              disabled={isDisabled}
                              onCheckedChange={(checked) => handleRoleChange(role.id, !!checked)}
                            />
                            <div className="flex-1">
                              <Label
                                htmlFor={`role-${role.id}`}
                                className={`text-sm font-medium leading-none ${isDisabled ? 'cursor-not-allowed opacity-70' : ''}`}
                              >
                                {role.name}
                              </Label>
                              <p className="text-xs text-muted-foreground mt-1">
                                {role.description}
                              </p>
                              <Badge variant="outline" className="mt-2 text-xs">
                                Custom
                              </Badge>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {systemRoles.length === 0 && customRoles.length === 0 && (
                  <p className="text-muted-foreground text-center py-8">
                    No roles available for assignment.
                  </p>
                )}
              </CardContent>
              <div className="flex justify-end gap-2 p-6 pt-0">
                <Button type="button" variant="outline" onClick={() => navigate('/user-management')}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateUserRolesMutation.isPending || isSelfEditing || (isTargetUserOwner(user, userRoles) && !isCurrentUserOwner)}
                >
                  {updateUserRolesMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : isSelfEditing ? (
                    <>
                      <Lock className="mr-2 h-4 w-4" />
                      Read-Only Mode
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
}
