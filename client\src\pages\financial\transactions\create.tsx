import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useContextData } from '@/lib/useContextData';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { format } from 'date-fns';

// UI Components
import { Button } from '@/components/ui/button';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Spinner } from '@/components/ui/spinner';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';

// Icons
import { ArrowLeft, Save, Calendar as CalendarIcon } from 'lucide-react';

// Schema for transaction creation
const transactionSchema = z.object({
  account_id: z.coerce.number({
    required_error: "Account is required",
  }),
  transaction_date: z.date({
    required_error: "Transaction date is required",
  }),
  transaction_type: z.enum(['debit', 'credit'], {
    required_error: "Transaction type is required",
  }),
  amount: z.coerce.number()
    .positive("Amount must be positive")
    .min(0.01, "Amount must be at least 0.01"),
  description: z.string().min(1, "Description is required"),
  reference_type: z.enum(['loan_disbursement', 'collection', 'expense', 'investment', 'withdrawal', 'transfer', 'adjustment']),
  reference_id: z.coerce.number(),
});

type TransactionFormValues = z.infer<typeof transactionSchema>;

// Types
interface Account {
  id: number;
  company_id: number;
  account_code: string;
  account_name: string;
  account_type: 'asset' | 'liability' | 'equity' | 'income' | 'expense';
  parent_account_id: number | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Reference type options
const referenceTypeOptions = [
  { value: 'loan_disbursement', label: 'Loan Disbursement' },
  { value: 'collection', label: 'Collection' },
  { value: 'expense', label: 'Expense' },
  { value: 'investment', label: 'Investment' },
  { value: 'withdrawal', label: 'Withdrawal' },
  { value: 'transfer', label: 'Transfer' },
  { value: 'adjustment', label: 'Adjustment' },
];

const CreateTransactionPage = () => {
  const { companyId } = useContextData();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();

  // Fetch accounts for the dropdown
  const { 
    data: accounts = [], 
    isLoading: isLoadingAccounts 
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'accounts'],
    queryFn: async () => {
      if (!companyId) return [];
      const response = await apiRequest('GET', `/api/companies/${companyId}/accounts`);
      return await response.json();
    },
    enabled: !!companyId
  });

  // Form setup
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      account_id: undefined,
      transaction_date: new Date(),
      transaction_type: 'debit',
      amount: undefined,
      description: '',
      reference_type: 'adjustment', // Default to adjustment as a valid reference type
      reference_id: 0, // Default to 0 as a valid number
    },
  });

  // Create transaction mutation
  const createMutation = useMutation({
    mutationFn: async (data: TransactionFormValues) => {
      // Format data for API
      const formattedData = {
        ...data,
        // Format date in yyyy-MM-dd format (just the date part)
        transaction_date: format(data.transaction_date, "yyyy-MM-dd"),
        // Convert amount to string for numeric database field
        amount: data.amount.toString(),
        // Ensure reference_id is a number
        reference_id: data.reference_id || 0,
        company_id: companyId,
      };
      
      const response = await apiRequest('POST', `/api/companies/${companyId}/transactions`, formattedData);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Transaction created",
        description: "The transaction has been created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'transactions'] });
      navigate('/financial/transactions');
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create transaction. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Form submission handler
  const onSubmit = (data: TransactionFormValues) => {
    createMutation.mutate(data);
  };

  // Group accounts by type for better organization in the dropdown
  const accountsByType = accounts.reduce((groups: Record<string, Account[]>, account: Account) => {
    if (account.is_active) { // Only show active accounts
      const type = account.account_type;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(account);
    }
    return groups;
  }, {});

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex items-center mb-2">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate('/financial/transactions')}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
          </div>
          <CardTitle className="text-2xl font-bold">Create New Transaction</CardTitle>
          <CardDescription>
            Record a new financial transaction
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Account */}
                <FormField
                  control={form.control}
                  name="account_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        value={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an account" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.keys(accountsByType).map((accountType) => (
                            <div key={accountType}>
                              <div className="px-2 py-1.5 text-sm font-semibold bg-gray-100">
                                {accountType.charAt(0).toUpperCase() + accountType.slice(1)}
                              </div>
                              {accountsByType[accountType].map((account: Account) => (
                                <SelectItem key={account.id} value={account.id.toString()}>
                                  {account.account_code} - {account.account_name}
                                </SelectItem>
                              ))}
                            </div>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the account involved in this transaction
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Transaction Date */}
                <FormField
                  control={form.control}
                  name="transaction_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Transaction Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className="w-full pl-3 text-left font-normal"
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Select a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>
                        The date when the transaction occurred
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Transaction Type */}
                <FormField
                  control={form.control}
                  name="transaction_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Transaction Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select transaction type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="debit">Debit</SelectItem>
                          <SelectItem value="credit">Credit</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose whether this is a debit or credit transaction
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Amount */}
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0.01"
                          placeholder="0.00"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        The monetary value of the transaction
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Reference Type (Optional) */}
                <FormField
                  control={form.control}
                  name="reference_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a reference type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {referenceTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Categorize this transaction for reporting
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Reference ID (Optional) */}
                <FormField
                  control={form.control}
                  name="reference_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference ID (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="e.g., Loan ID or Collection ID"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value) : undefined;
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        ID of the referenced entity (if applicable)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter details about this transaction"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a clear description of the transaction
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <Button 
            variant="outline" 
            onClick={() => navigate('/financial/transactions')}
          >
            Cancel
          </Button>
          <Button 
            onClick={form.handleSubmit(onSubmit)}
            disabled={createMutation.isPending || isLoadingAccounts}
          >
            {createMutation.isPending ? (
              <>
                <Spinner className="mr-2 h-4 w-4" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Create Transaction
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CreateTransactionPage;