import { Express, Response } from 'express';
import { db } from '../db';
import { companyPrefixSettings, companyPrefixSettingsSchema } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import errorLogger from '../utils/errorLogger';

export function registerCompanyPrefixSettingsRoutes(app: Express): void {
  console.log('Inside registerCompanyPrefixSettingsRoutes function');
  // GET /api/companies/:companyId/prefix-settings
  app.get('/api/companies/:companyId/prefix-settings', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      console.log(`GET /api/companies/${companyId}/prefix-settings called`);

      // Get company prefix settings
      const settings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      // If settings don't exist, return a 200 response with empty data
      if (!settings) {
        console.log(`No prefix settings found for company ${companyId}`);
        return res.status(200).json({
          data: null,
          message: 'No prefix settings found for this company',
          code: 'NO_PREFIX_SETTINGS'
        });
      }

      console.log(`Found prefix settings for company ${companyId}:`, settings);
      return res.json({
        data: settings,
        message: 'Prefix settings retrieved successfully',
        code: 'PREFIX_SETTINGS_FOUND'
      });
    } catch (error) {
      errorLogger.logError('Error fetching company prefix settings', 'prefix-settings-fetch', error as Error);
      return res.status(500).json({
        data: null,
        message: 'Failed to fetch company prefix settings',
        code: 'PREFIX_SETTINGS_FETCH_ERROR'
      });
    }
  });

  // POST /api/companies/:companyId/prefix-settings
  app.post('/api/companies/:companyId/prefix-settings', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      console.log(`POST /api/companies/${companyId}/prefix-settings called with body:`, req.body);

      // Check if settings already exist for this company
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      if (existingSettings) {
        console.log(`Prefix settings already exist for company ${companyId}`);
        return res.status(409).json({
          data: null,
          message: 'Prefix settings already exist for this company. Use PUT to update.',
          code: 'PREFIX_SETTINGS_ALREADY_EXIST'
        });
      }

      // Validate request body
      const validatedData = companyPrefixSettingsSchema.parse({
        ...req.body,
        company_id: companyId,
      });

      // Create new settings
      const [result] = await db
        .insert(companyPrefixSettings)
        .values(validatedData)
        .returning();

      console.log(`Created prefix settings for company ${companyId}:`, result);
      return res.status(201).json({
        data: result,
        message: 'Prefix settings created successfully',
        code: 'PREFIX_SETTINGS_CREATED'
      });
    } catch (error) {
      errorLogger.logError('Error creating company prefix settings', 'prefix-settings-create', error as Error);

      // Check if it's a validation error
      if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
        return res.status(400).json({
          data: null,
          message: 'Invalid input data',
          code: 'INVALID_INPUT',
          errors: (error as any).errors
        });
      }

      return res.status(500).json({
        data: null,
        message: 'Failed to create company prefix settings',
        code: 'PREFIX_SETTINGS_CREATE_ERROR'
      });
    }
  });

  // PUT /api/companies/:companyId/prefix-settings
  app.put('/api/companies/:companyId/prefix-settings', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      console.log(`PUT /api/companies/${companyId}/prefix-settings called with body:`, req.body);

      // Validate request body
      const validatedData = companyPrefixSettingsSchema.parse({
        ...req.body,
        company_id: companyId,
      });

      // Check if settings already exist
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      let result;
      let isNew = false;

      if (existingSettings) {
        console.log(`Updating existing prefix settings for company ${companyId}`);
        // Update existing settings
        [result] = await db
          .update(companyPrefixSettings)
          .set({
            ...validatedData,
            updated_at: new Date(),
          })
          .where(eq(companyPrefixSettings.company_id, companyId))
          .returning();
      } else {
        console.log(`Creating new prefix settings for company ${companyId}`);
        // Create new settings if they don't exist
        [result] = await db
          .insert(companyPrefixSettings)
          .values(validatedData)
          .returning();
        isNew = true;
      }

      console.log(`${isNew ? 'Created' : 'Updated'} prefix settings for company ${companyId}:`, result);
      return res.status(isNew ? 201 : 200).json({
        data: result,
        message: `Prefix settings ${isNew ? 'created' : 'updated'} successfully`,
        code: isNew ? 'PREFIX_SETTINGS_CREATED' : 'PREFIX_SETTINGS_UPDATED'
      });
    } catch (error) {
      errorLogger.logError('Error updating company prefix settings', 'prefix-settings-update', error as Error);

      // Check if it's a validation error
      if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
        return res.status(400).json({
          data: null,
          message: 'Invalid input data',
          code: 'INVALID_INPUT',
          errors: (error as any).errors
        });
      }

      return res.status(500).json({
        data: null,
        message: 'Failed to update company prefix settings',
        code: 'PREFIX_SETTINGS_UPDATE_ERROR'
      });
    }
  });

  // DELETE /api/companies/:companyId/prefix-settings
  app.delete('/api/companies/:companyId/prefix-settings', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      console.log(`DELETE /api/companies/${companyId}/prefix-settings called`);

      // Check if settings exist
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      if (!existingSettings) {
        console.log(`No prefix settings found for company ${companyId}`);
        return res.status(200).json({
          data: null,
          message: 'No prefix settings found for this company',
          code: 'NO_PREFIX_SETTINGS'
        });
      }

      // Delete the settings
      await db
        .delete(companyPrefixSettings)
        .where(eq(companyPrefixSettings.company_id, companyId));

      console.log(`Deleted prefix settings for company ${companyId}`);
      return res.status(200).json({
        data: null,
        message: 'Prefix settings deleted successfully',
        code: 'PREFIX_SETTINGS_DELETED'
      });
    } catch (error) {
      errorLogger.logError('Error deleting company prefix settings', 'prefix-settings-delete', error as Error);
      return res.status(500).json({
        data: null,
        message: 'Failed to delete company prefix settings',
        code: 'PREFIX_SETTINGS_DELETE_ERROR'
      });
    }
  });
}
