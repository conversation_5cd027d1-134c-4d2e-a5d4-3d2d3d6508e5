import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation, Link } from "wouter";
import { useAuth } from "@/lib/auth";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency, formatDate } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { loanTypeEnum } from "@shared/schema";
import { z } from "zod";
import { Calendar, FileText, Loader2, Plus, Search, Edit, Trash2, MoreVertical, ArrowLeft, ArrowRight, Check } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LoanFormRenderer, LoanFormData } from "@/components/form-builder/LoanFormRenderer";

// Interfaces and types
interface Loan {
  id: number;
  customer_id: number;
  company_id: number;
  amount: string | number;
  interest_rate: string | number;
  interest_type: 'flat' | 'reducing' | 'compound';
  loan_type: 'personal' | 'business' | 'education' | 'housing' | 'vehicle' | 'agriculture' | 'microfinance' | 'other';
  term_months: number;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  customer?: {
    id: number;
    full_name: string;
  };
  notes?: string | null;
}

interface Customer {
  id: number;
  full_name: string;
  company_id: number;
}

interface FormTemplate {
  id: number;
  name: string;
  description?: string;
  company_id: number;
  branch_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface LoanConfiguration {
  id: number;
  company_id: number;
  template_id: number;
  loan_type: string;
  branch_id: number | null;
  is_active: boolean;
  order: number;
  created_at: string;
  updated_at: string;
  template?: FormTemplate;
}

// Form schemas
const loanFormSchema = z.object({
  company_id: z.number(),
  customer_id: z.number().int().positive({ message: "Please select a customer" }),
  amount: z.string().min(1, "Amount is required"),
  interest_rate: z.string().min(1, "Interest rate is required"),
  interest_type: z.enum(['flat', 'reducing', 'compound'], {
    required_error: "Please select an interest type",
  }),
  loan_type: z.enum(['personal', 'business', 'education', 'housing', 'vehicle', 'agriculture', 'microfinance', 'other'], {
    required_error: "Please select a loan type",
  }).default('personal'),
  term_months: z.string().min(1, "Loan term is required"),
  start_date: z.string().min(1, "Start date is required"),
  end_date: z.string().min(1, "End date is required"),
  // Optional fields
  notes: z.string().optional().nullable(),
});

// Using z.input<typeof loanFormSchema> for input values (what's in the form)
// and z.output<typeof loanFormSchema> for properly transformed output (for API requests)

export default function Loans() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const user = getCurrentUser();
  const companyId = user?.company_id || 1; // Default to 1 for SaaS admin if no company_id
  const isMobile = useIsMobile();
  const [, setLocation] = useLocation();
  
  // State for UI control
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);
  
  // Multi-step form state
  const [currentStep, setCurrentStep] = useState(1); // 1: Loan Details, 2: Forms, 3: Review
  const [editCurrentStep, setEditCurrentStep] = useState(1); // 1: Loan Details, 2: Forms, 3: Review
  const [formData, setFormData] = useState<Record<number | string, LoanFormData>>({});
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  
  // Data fetching
  const { data: loansData, isLoading } = useQuery<Loan[]>({
    queryKey: [`/api/companies/${companyId}/loans`],
    enabled: !!companyId,
  });
  
  const { data: customers = [] } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${companyId}/customers`],
    enabled: !!companyId,
  });
  
  // Fetch active form templates
  const { data: formTemplates = [] } = useQuery<FormTemplate[]>({
    queryKey: [`/api/companies/${companyId}/form-templates`],
    enabled: !!companyId,
  });
  
  // Debug: Log loan data to see if notes field is present
  useEffect(() => {
    if (loansData && loansData.length > 0) {
      console.log("First loan record:", loansData[0]);
    }
  }, [loansData]);

  // Filter loans based on search query
  const filteredLoans = loansData?.filter(loan => {
    const matchesSearch = 
      loan.id.toString().includes(searchQuery) ||
      (loan.customer?.full_name?.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesSearch;
  }) || [];

  // Loan form
  const loanForm = useForm<z.input<typeof loanFormSchema>>({
    resolver: zodResolver(loanFormSchema),
    defaultValues: {
      company_id: companyId,
      customer_id: 0,
      amount: "",
      interest_rate: "",
      interest_type: "flat",
      loan_type: "personal",
      term_months: "",
      start_date: new Date().toISOString().split('T')[0],
      end_date: new Date(new Date().setMonth(new Date().getMonth() + 12)).toISOString().split('T')[0],
      notes: null,
    },
  });
  
  // Edit loan form
  const editForm = useForm<z.input<typeof loanFormSchema>>({
    resolver: zodResolver(loanFormSchema),
    defaultValues: {
      company_id: companyId,
      customer_id: 0,
      amount: "",
      interest_rate: "",
      interest_type: "flat",
      loan_type: "personal",
      term_months: "",
      start_date: "",
      end_date: "",
      notes: null,
    },
  });

  // Track selected customer for validation
  const selectedCustomerId = loanForm.watch("customer_id");
  const selectedInterestType = loanForm.watch("interest_type");
  const selectedStartDate = loanForm.watch("start_date");
  const selectedTermMonths = loanForm.watch("term_months");
  const selectedLoanType = loanForm.watch("loan_type");
  
  // Fetch loan configurations based on selected loan type
  const { data: loanConfigurations = [], isLoading: isLoadingConfigurations } = useQuery<(LoanConfiguration & { template: FormTemplate })[]>({
    queryKey: [`/api/companies/${companyId}/loan-configurations/loan-type/${selectedLoanType}`],
    enabled: !!companyId && !!selectedLoanType,
  });
  
  // Extract just the templates from the loan configurations for use in the form
  const formTemplatesForLoanType = loanConfigurations?.map(config => config.template) || [];
  
  // Debug: Log template and configuration data
  useEffect(() => {
    if (selectedLoanType) {
      console.log("Fetching configurations for loan type:", selectedLoanType);
      console.log("Configuration data:", loanConfigurations);
      console.log("Form templates for loan type:", formTemplatesForLoanType);
    }
  }, [loanConfigurations, formTemplatesForLoanType, companyId, selectedLoanType]);
  
  // Calculate end date when term months or start date changes
  const updateEndDate = () => {
    if (selectedStartDate && selectedTermMonths) {
      const startDate = new Date(selectedStartDate);
      const endDate = new Date(startDate);
      endDate.setMonth(startDate.getMonth() + parseInt(selectedTermMonths.toString(), 10));
      loanForm.setValue("end_date", endDate.toISOString().split('T')[0]);
    }
  };

  // Update end date when term months or start date changes
  if (selectedStartDate && selectedTermMonths) {
    updateEndDate();
  }
  
  // Mutations
  const createLoanMutation = useMutation({
    mutationFn: async (data: z.input<typeof loanFormSchema>) => {
      // Manual conversion to match exactly what API expects
      const transformedData = {
        ...data,
        amount: String(data.amount), // Convert to string (Drizzle will handle as numeric)
        interest_rate: String(data.interest_rate), // Convert to string (Drizzle will handle as numeric)
        term_months: Number(data.term_months), // Convert to number
        // Now the API will handle string date conversion via preprocess in the schema
        start_date: data.start_date,
        end_date: data.end_date,
      };
      console.log("Sending loan data:", transformedData);
      const res = await apiRequest('POST', '/api/loans', transformedData);
      return res.json();
    },
    onSuccess: (newLoan) => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
      setIsAddDialogOpen(false);
      
      // Store current loan type for form mapping
      const loanType = selectedLoanType;
      
      // Reset form
      loanForm.reset({
        company_id: companyId,
        customer_id: 0,
        amount: "",
        interest_rate: "",
        interest_type: "flat",
        loan_type: "personal",
        term_months: "",
        start_date: new Date().toISOString().split('T')[0],
        end_date: new Date(new Date().setMonth(new Date().getMonth() + 12)).toISOString().split('T')[0],
        notes: null,
      });
      
      // Show success toast
      toast({
        title: "Loan created",
        description: "The loan has been created successfully.",
      });
      
      // Navigate to loan detail page to view and fill out associated forms
      if (newLoan && newLoan.id) {        
        // Navigate to loan detail page with the new loan ID
        navigateToLoanDetail(newLoan.id);
        
        // Show toast prompting user if needed
        toast({
          title: "Loan Detail",
          description: "You can now view and manage forms associated with this loan.",
          duration: 5000,
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create loan",
        variant: "destructive",
      });
    },
  });

  const updateLoanMutation = useMutation({
    mutationFn: async (data: z.input<typeof loanFormSchema>) => {
      if (!selectedLoan) {
        throw new Error("No loan selected for update");
      }
      
      // Manual conversion to match exactly what API expects
      const transformedData = {
        ...data,
        amount: String(data.amount), // Convert to string (Drizzle will handle as numeric)
        interest_rate: String(data.interest_rate), // Convert to string (Drizzle will handle as numeric)
        term_months: Number(data.term_months), // Convert to number
        // Now the API will handle string date conversion via preprocess in the schema
        start_date: data.start_date,
        end_date: data.end_date,
      };
      console.log("Updating loan data:", transformedData);
      const res = await apiRequest('PATCH', `/api/loans/${selectedLoan.id}`, transformedData);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
      setIsEditDialogOpen(false);
      editForm.reset();
      toast({
        title: "Loan updated",
        description: "The loan has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update loan",
        variant: "destructive",
      });
    },
  });

  const deleteLoanMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest('DELETE', `/api/loans/${id}?companyId=${companyId}`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
      toast({
        title: "Loan deleted",
        description: "The loan has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete loan",
        variant: "destructive",
      });
    },
  });

  // Multi-step form handlers
  const submitFormWithData = async (data: z.input<typeof loanFormSchema>) => {
    setIsFormSubmitting(true);
    try {
      // First, create the loan
      const response = await createLoanMutation.mutateAsync(data);
      const newLoanId = response.id;
      
      // Then submit all associated form submissions if we have any
      const formSubmissionPromises = Object.entries(formData).map(async ([templateId, data]) => {
        // Skip the form data validation function
        const { _validate, ...formValues } = data;
        
        // Submit the form data
        return await fetch(`/api/companies/${companyId}/loans/${newLoanId}/form-submissions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            company_id: companyId,
            loan_id: newLoanId,
            template_id: parseInt(templateId),
            form_data: JSON.stringify(formValues)
          })
        });
      });
      
      // Wait for all form submissions to complete
      await Promise.all(formSubmissionPromises);
      
      // Reset the current step and form data
      setCurrentStep(1);
      setFormData({});
      
      // Navigate to the loan detail page
      navigateToLoanDetail(newLoanId);
    } catch (error) {
      console.error("Error creating loan with forms:", error);
      toast({
        title: "Error",
        description: "Failed to create loan with forms. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsFormSubmitting(false);
    }
  };
  
  // Handle step navigation
  const goToNextStep = async () => {
    if (currentStep === 1) {
      // Validate the loan form data
      const valid = await loanForm.trigger();
      if (!valid) return;
      
      setCurrentStep(2);
    } else if (currentStep === 2) {
      // Check if forms need to be validated
      const allFormsValid = formTemplatesForLoanType.length === 0 || 
        formTemplatesForLoanType.every(template => {
          const templateData = formData[template.id];
          return templateData && templateData._validate ? templateData._validate() : true;
        });
      
      if (!allFormsValid) {
        toast({
          title: "Form Validation Error",
          description: "Please fill out all required fields in the form(s).",
          variant: "destructive",
        });
        return;
      }
      
      setCurrentStep(3);
    }
  };
  
  const goToPrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  // Handle form data changes
  const handleFormDataChange = (templateId: number, data: LoanFormData) => {
    setFormData(prev => ({
      ...prev,
      [templateId]: data
    }));
  };
  
  // Form handlers
  const onSubmitAdd = (data: z.input<typeof loanFormSchema>) => {
    if (currentStep === 3) {
      // Final step - submit everything
      submitFormWithData(data);
    } else {
      // Just validate and go to next step
      goToNextStep();
    }
  };

  const onSubmitEdit = async (data: z.input<typeof loanFormSchema>) => {
    if (selectedLoan) {
      try {
        setIsFormSubmitting(true);
        
        // First, update the loan (we don't need to add the id as it's handled in the mutation)
        await updateLoanMutation.mutateAsync(data);
        
        // Then submit all associated form submissions if we have any
        if (Object.keys(formData).length > 0) {
          const formSubmissionPromises = Object.entries(formData).map(async ([templateId, data]) => {
            // Skip the form data validation function
            const { _validate, ...formValues } = data as any;
            
            // Submit the form data
            try {
              // Check if there's already a submission for this template
              const existingSubmissions = await fetch(
                `/api/companies/${companyId}/loans/${selectedLoan.id}/form-submissions`
              ).then(res => res.json());
              
              const existingSubmission = existingSubmissions.find(
                (sub: any) => sub.template_id === parseInt(templateId)
              );
              
              if (existingSubmission) {
                // Update existing submission
                return await fetch(`/api/companies/${companyId}/form-submissions/${existingSubmission.id}`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    company_id: companyId,
                    loan_id: selectedLoan.id,
                    template_id: parseInt(templateId),
                    form_data: JSON.stringify(formValues)
                  })
                });
              } else {
                // Create new submission
                return await fetch(`/api/companies/${companyId}/loans/${selectedLoan.id}/form-submissions`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    company_id: companyId,
                    loan_id: selectedLoan.id,
                    template_id: parseInt(templateId),
                    form_data: JSON.stringify(formValues)
                  })
                });
              }
            } catch (error) {
              console.error("Error with form submission:", error);
              throw error;
            }
          });
          
          // Wait for all form submissions to complete
          await Promise.all(formSubmissionPromises);
        }
        
        // Reset form state
        setEditCurrentStep(1);
        setFormData({});
        
        // Close the dialog
        setIsEditDialogOpen(false);
        
        toast({
          title: "Loan Updated",
          description: "Loan and associated forms have been updated successfully.",
          duration: 3000,
        });
      } catch (error) {
        console.error("Error updating loan with forms:", error);
        toast({
          title: "Error",
          description: "Failed to update loan with forms. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsFormSubmitting(false);
      }
    }
  };

  // Helper functions
  const handleOpenEditDialog = (loan: Loan) => {
    setSelectedLoan(loan);
    
    // Reset the form data for dynamic forms
    setFormData({});
    // Reset edit step to 1
    setEditCurrentStep(1);
    
    editForm.reset({
      company_id: loan.company_id,
      customer_id: loan.customer_id,
      amount: loan.amount.toString(),
      interest_rate: loan.interest_rate.toString(),
      interest_type: loan.interest_type,
      loan_type: loan.loan_type || "personal",
      term_months: loan.term_months.toString(),
      start_date: new Date(loan.start_date).toISOString().split('T')[0],
      end_date: new Date(loan.end_date).toISOString().split('T')[0],
      notes: loan.notes || null,
    });
    
    // Load form templates for this loan type
    queryClient.invalidateQueries({
      queryKey: [`/api/companies/${companyId}/loan-configurations/loan-type/${loan.loan_type}`]
    });
    
    // Fetch existing form submissions for this loan
    fetchFormSubmissionsForLoan(loan.id);
    
    setIsEditDialogOpen(true);
  };
  
  // Fetch form submissions for a loan to populate edit form
  const fetchFormSubmissionsForLoan = async (loanId: number) => {
    try {
      const response = await fetch(`/api/companies/${companyId}/loans/${loanId}/form-submissions`);
      if (response.ok) {
        const submissions = await response.json();
        
        // Organize form data by template ID
        const formDataByTemplate: Record<string, any> = {};
        submissions.forEach((submission: any) => {
          if (submission.template_id && submission.form_data) {
            try {
              // Parse the form data if it's a string
              const parsedData = typeof submission.form_data === 'string' 
                ? JSON.parse(submission.form_data) 
                : submission.form_data;
                
              formDataByTemplate[submission.template_id] = parsedData;
            } catch (e) {
              console.error("Error parsing form data:", e);
            }
          }
        });
        
        // Update form data state with retrieved submissions
        setFormData(formDataByTemplate);
      }
    } catch (error) {
      console.error("Error fetching form submissions:", error);
    }
  };

  const handleDeleteLoan = (id: number) => {
    if (window.confirm("Are you sure you want to delete this loan? This cannot be undone.")) {
      deleteLoanMutation.mutate(id);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by the filter above
  };

  // Get label for interest type
  const getInterestTypeLabel = (type: string) => {
    switch (type) {
      case 'flat': return 'Flat Rate';
      case 'reducing': return 'Reducing Balance';
      case 'compound': return 'Compound Interest';
      default: return type;
    }
  };
  
  // Get display name for loan type
  const getLoanTypeDisplay = (type: string) => {
    switch (type) {
      case 'personal': return 'Personal Loan';
      case 'business': return 'Business Loan';
      case 'education': return 'Education Loan';
      case 'housing': return 'Housing Loan';
      case 'vehicle': return 'Vehicle Loan';
      case 'agriculture': return 'Agriculture Loan';
      case 'microfinance': return 'Microfinance Loan';
      case 'other': return 'Other Loan';
      default: return type;
    }
  };
  
  const navigateToLoanDetail = (loanId: number) => {
    setLocation(`/loans/${loanId}`);
  };

  // UI components and rendering
  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Loans</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage customer loans and financing
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Link href="/loan-form-builder">
            <Button variant="outline" className="flex items-center gap-1">
              <FileText size={16} />
              <span>Loan Templates</span>
            </Button>
          </Link>
          
          <Button 
            className="flex items-center gap-1"
            onClick={() => navigate("/loans/template-selection")}
          >
            <Plus size={16} />
            <span>Add Loan</span>
          </Button>
          
          {/* Old dialog code removed */}
          {/* <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-1">
                <Plus size={16} />
                <span>Add Loan</span>
              </Button>
            </DialogTrigger> */}
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Loan</DialogTitle>
                <DialogDescription>
                  Create a new loan for a customer.
                </DialogDescription>
              </DialogHeader>
              
              {/* Step indicator */}
              <div className="flex items-center justify-center mb-4">
                <div className="flex items-center w-full max-w-xl">
                  <div className="relative flex items-center text-teal-600 w-full">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full border transition-colors ${currentStep >= 1 ? 'bg-teal-100 border-teal-500' : 'border-gray-300'}`}>
                      <span className={`text-sm font-medium ${currentStep >= 1 ? 'text-teal-700' : 'text-gray-500'}`}>1</span>
                    </div>
                    <div className={`h-0.5 flex-1 mx-2 transition-colors ${currentStep >= 2 ? 'bg-teal-500' : 'bg-gray-300'}`}></div>
                  </div>
                  
                  <div className="relative flex items-center text-teal-600 w-full">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full border transition-colors ${currentStep >= 2 ? 'bg-teal-100 border-teal-500' : 'border-gray-300'}`}>
                      <span className={`text-sm font-medium ${currentStep >= 2 ? 'text-teal-700' : 'text-gray-500'}`}>2</span>
                    </div>
                    <div className={`h-0.5 flex-1 mx-2 transition-colors ${currentStep >= 3 ? 'bg-teal-500' : 'bg-gray-300'}`}></div>
                  </div>
                  
                  <div className="relative flex items-center text-teal-600 w-full">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full border transition-colors ${currentStep >= 3 ? 'bg-teal-100 border-teal-500' : 'border-gray-300'}`}>
                      <span className={`text-sm font-medium ${currentStep >= 3 ? 'text-teal-700' : 'text-gray-500'}`}>3</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center mb-4">
                <div className="flex w-full max-w-xl justify-between px-3">
                  <span className={`text-xs ${currentStep >= 1 ? 'text-teal-700 font-medium' : 'text-gray-500'}`}>Loan Details</span>
                  <span className={`text-xs ${currentStep >= 2 ? 'text-teal-700 font-medium' : 'text-gray-500'}`}>Required Forms</span>
                  <span className={`text-xs ${currentStep >= 3 ? 'text-teal-700 font-medium' : 'text-gray-500'}`}>Review</span>
                </div>
              </div>
              
              <Form {...loanForm}>
                <form onSubmit={loanForm.handleSubmit(onSubmitAdd)} className="space-y-4">
                  {/* STEP 1: Basic Loan Information */}
                  {currentStep === 1 && (
                    <>
                      {/* Customer Selection */}
                      <FormField
                        control={loanForm.control}
                        name="customer_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Customer*</FormLabel>
                            <Select 
                              value={field.value ? field.value.toString() : ""}
                              onValueChange={(value) => field.onChange(parseInt(value, 10))}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select customer" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {customers.map((customer) => (
                                  <SelectItem key={customer.id} value={customer.id.toString()}>
                                    {customer.full_name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Loan Amount */}
                      <FormField
                        control={loanForm.control}
                        name="amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Loan Amount (₹)*</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="E.g., 50000" 
                                {...field} 
                                type="number"
                                step="0.01"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Interest Rate */}
                      <FormField
                        control={loanForm.control}
                        name="interest_rate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Interest Rate (%)*</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="E.g., 12.5" 
                                {...field} 
                                type="number"
                                step="0.01"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Interest Type */}
                      <FormField
                        control={loanForm.control}
                        name="interest_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Interest Type*</FormLabel>
                            <Select 
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select interest type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="flat">Flat Rate</SelectItem>
                                <SelectItem value="reducing">Reducing Balance</SelectItem>
                                <SelectItem value="compound">Compound Interest</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              {selectedInterestType === 'flat' && 'Interest calculated on full loan amount throughout term'}
                              {selectedInterestType === 'reducing' && 'Interest calculated on remaining balance only'}
                              {selectedInterestType === 'compound' && 'Interest compounds over time (interest on interest)'}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Loan Type */}
                      <FormField
                        control={loanForm.control}
                        name="loan_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Loan Type*</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select loan type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="personal">Personal Loan</SelectItem>
                                <SelectItem value="business">Business Loan</SelectItem>
                                <SelectItem value="education">Education Loan</SelectItem>
                                <SelectItem value="housing">Housing Loan</SelectItem>
                                <SelectItem value="vehicle">Vehicle Loan</SelectItem>
                                <SelectItem value="agriculture">Agriculture Loan</SelectItem>
                                <SelectItem value="microfinance">Microfinance Loan</SelectItem>
                                <SelectItem value="other">Other Loan Type</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Associated forms will be available based on loan type
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Term Months */}
                      <FormField
                        control={loanForm.control}
                        name="term_months"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Loan Term (Months)*</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="E.g., 12" 
                                {...field} 
                                type="number"
                                min="1"
                                onChange={(e) => {
                                  field.onChange(e);
                                  // Update end date when term changes
                                  if (e.target.value) {
                                    updateEndDate();
                                  }
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Start Date */}
                      <FormField
                        control={loanForm.control}
                        name="start_date"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Start Date*</FormLabel>
                            <FormControl>
                              <Input 
                                type="date"
                                {...field} 
                                onChange={(e) => {
                                  field.onChange(e);
                                  // Update end date when start date changes
                                  if (e.target.value) {
                                    updateEndDate();
                                  }
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* End Date */}
                      <FormField
                        control={loanForm.control}
                        name="end_date"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>End Date*</FormLabel>
                            <FormControl>
                              <Input 
                                type="date"
                                {...field} 
                                readOnly // End date is calculated from term and start date
                                className="bg-gray-50"
                              />
                            </FormControl>
                            <FormDescription>
                              Auto-calculated based on term and start date
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Notes */}
                      <FormField
                        control={loanForm.control}
                        name="notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Notes</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Additional details about this loan"
                                {...field}
                                value={field.value || ""}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                  
                  {/* Step 2: Form templates */}
                  {currentStep === 2 && (
                    <div className="space-y-6">
                      {formTemplatesForLoanType.length === 0 ? (
                        <div className="p-4 border rounded bg-yellow-50 text-yellow-800">
                          <p>No form templates configured for this loan type. You can proceed to the next step.</p>
                        </div>
                      ) : (
                        formTemplatesForLoanType.map(template => (
                          <div key={template.id} className="mb-6">
                            <LoanFormRenderer
                              templateId={template.id}
                              companyId={companyId}
                              onFormDataChange={(data) => handleFormDataChange(template.id, data)}
                            />
                          </div>
                        ))
                      )}
                    </div>
                  )}
                  
                  {/* Step 3: Review */}
                  {currentStep === 3 && (
                    <div className="space-y-6">
                      <Card>
                        <CardHeader>
                          <CardTitle>Loan Summary</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <h4 className="text-sm font-medium text-gray-500">Customer</h4>
                              <p className="text-base font-medium">
                                {customers.find(c => c.id === selectedCustomerId)?.full_name || 'Not selected'}
                              </p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-500">Loan Type</h4>
                              <p className="text-base font-medium">{getLoanTypeDisplay(selectedLoanType || 'personal')}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-500">Amount</h4>
                              <p className="text-base font-medium">₹{loanForm.getValues("amount")}</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-500">Interest</h4>
                              <p className="text-base font-medium">{loanForm.getValues("interest_rate")}% ({getInterestTypeLabel(selectedInterestType)})</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-500">Term</h4>
                              <p className="text-base font-medium">{loanForm.getValues("term_months")} months</p>
                            </div>
                            <div>
                              <h4 className="text-sm font-medium text-gray-500">Date Range</h4>
                              <p className="text-base font-medium">
                                {new Date(loanForm.getValues("start_date")).toLocaleDateString()} - {new Date(loanForm.getValues("end_date")).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          
                          {formTemplatesForLoanType.length > 0 && (
                            <div className="mt-4">
                              <h4 className="text-sm font-medium text-gray-500 mb-2">Forms Attached</h4>
                              <ul className="list-disc pl-5 space-y-1">
                                {formTemplatesForLoanType.map(template => (
                                  <li key={template.id} className="text-sm">
                                    {template.name}
                                    {formData[template.id] ? 
                                      <span className="text-green-600 ml-2"><Check className="inline h-4 w-4" /> Completed</span> : 
                                      <span className="text-red-600 ml-2">Not completed</span>
                                    }
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  )}

                  {/* Navigation Buttons */}
                  <DialogFooter className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Button 
                        type="button" 
                        variant="ghost"
                        onClick={() => {
                          setIsAddDialogOpen(false);
                          setCurrentStep(1); // Reset to first step when dialog closes
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {currentStep > 1 && (
                        <Button 
                          type="button" 
                          variant="outline"
                          onClick={goToPrevStep}
                          disabled={isFormSubmitting}
                        >
                          <ArrowLeft className="h-4 w-4 mr-2" />
                          Previous
                        </Button>
                      )}
                      
                      {currentStep < 3 ? (
                        <Button 
                          type="button" 
                          onClick={goToNextStep}
                          disabled={isFormSubmitting}
                        >
                          Next
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      ) : (
                        <Button 
                          type="submit" 
                          disabled={isFormSubmitting || !selectedCustomerId}
                        >
                          {isFormSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Creating...
                            </>
                          ) : (
                            <>Complete Loan</>
                          )}
                        </Button>
                      )}
                    </div>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Search */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search by customer name or loan ID..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button type="submit" className="whitespace-nowrap">Search</Button>
          </form>
        </CardContent>
      </Card>

      {/* Loans List */}
      <Card>
        <CardHeader>
          <CardTitle>Loans</CardTitle>
          <CardDescription>
            View and manage customer loans
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : filteredLoans.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No loans found. Create a new loan to get started.
            </div>
          ) : (
            <>
              {/* Desktop view - Table */}
              <div className="hidden md:block overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Interest</TableHead>
                      <TableHead>Term</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Notes</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLoans.map((loan) => (
                      <TableRow 
                        key={loan.id} 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => navigateToLoanDetail(loan.id)}
                      >
                        <TableCell>#{loan.id}</TableCell>
                        <TableCell>
                          {loan.customer?.full_name || `Customer ${loan.customer_id}`}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(
                            typeof loan.amount === 'string' 
                              ? parseFloat(loan.amount) 
                              : loan.amount, 
                            'INR', 
                            'en-IN'
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{loan.interest_rate}%</span>
                            <span className="text-xs text-muted-foreground">
                              {getInterestTypeLabel(loan.interest_type)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{loan.term_months} months</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            {formatDate(loan.start_date)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            {formatDate(loan.end_date)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {loan.notes ? (
                            <span className="text-sm">{loan.notes}</span>
                          ) : (
                            <span className="text-sm text-muted-foreground italic">No notes</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent row click
                              handleOpenEditDialog(loan);
                            }}
                          >
                            Edit
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent row click
                              handleDeleteLoan(loan.id);
                            }}
                            className="text-destructive hover:text-destructive"
                          >
                            Delete
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile view - Cards */}
              <div className="grid grid-cols-1 gap-4 md:hidden">
                {filteredLoans.map((loan) => (
                  <Card 
                    key={loan.id} 
                    className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => navigateToLoanDetail(loan.id)}
                  >
                    <CardHeader className="p-4 pb-0">
                      <div className="flex justify-between items-start">
                        <div>
                          <Badge className="mb-2">Loan #{loan.id}</Badge>
                          <CardTitle className="text-base mb-1">
                            {loan.customer?.full_name || `Customer ${loan.customer_id}`}
                          </CardTitle>
                          <CardDescription>
                            {formatCurrency(
                              typeof loan.amount === 'string' 
                                ? parseFloat(loan.amount) 
                                : loan.amount, 
                              'INR', 
                              'en-IN'
                            )}
                          </CardDescription>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={(e) => e.stopPropagation()} // Prevent card click
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation(); // Prevent card click
                              handleOpenEditDialog(loan);
                            }}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation(); // Prevent card click
                                handleDeleteLoan(loan.id);
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-4">
                        <div>
                          <span className="text-xs font-medium text-muted-foreground block">Interest</span>
                          <span className="text-sm">
                            {loan.interest_rate}% ({getInterestTypeLabel(loan.interest_type)})
                          </span>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-muted-foreground block">Term</span>
                          <span className="text-sm">{loan.term_months} months</span>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-muted-foreground block">Start Date</span>
                          <span className="text-sm flex items-center">
                            <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                            {formatDate(loan.start_date)}
                          </span>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-muted-foreground block">End Date</span>
                          <span className="text-sm flex items-center">
                            <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                            {formatDate(loan.end_date)}
                          </span>
                        </div>
                      </div>
                      {loan.notes && (
                        <div className="mt-4 border-t border-gray-100 pt-3">
                          <span className="text-xs font-medium text-muted-foreground block mb-1">Notes</span>
                          <span className="text-sm">{loan.notes}</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Edit Loan Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        if (!open) {
          setEditCurrentStep(1); // Reset to first step when dialog closes
        }
      }}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Loan</DialogTitle>
            <DialogDescription>
              Update the loan details and associated forms.
            </DialogDescription>
          </DialogHeader>

          {/* Multi-step edit form */}
          <div className="mt-4">
            {/* Step Indicator */}
            <div className="flex mb-6">
              <div className="flex-1">
                <div 
                  className={`w-full h-1 ${editCurrentStep >= 1 ? 'bg-primary' : 'bg-gray-200'}`}
                ></div>
                <div className="mt-2 text-center text-xs font-medium">
                  Basic Info
                </div>
              </div>
              <div className="flex-1">
                <div 
                  className={`w-full h-1 ${editCurrentStep >= 2 ? 'bg-primary' : 'bg-gray-200'}`}
                ></div>
                <div className="mt-2 text-center text-xs font-medium">
                  Forms
                </div>
              </div>
              <div className="flex-1">
                <div 
                  className={`w-full h-1 ${editCurrentStep >= 3 ? 'bg-primary' : 'bg-gray-200'}`}
                ></div>
                <div className="mt-2 text-center text-xs font-medium">
                  Review
                </div>
              </div>
            </div>

            {/* Step 1: Basic Info */}
            {editCurrentStep === 1 && (
              <Form {...editForm}>
                <form className="space-y-4">
                  {/* Customer Selection */}
                  <FormField
                    control={editForm.control}
                    name="customer_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer*</FormLabel>
                        <Select 
                          value={field.value ? field.value.toString() : ""}
                          onValueChange={(value) => field.onChange(parseInt(value, 10))}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select customer" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {customers.map((customer) => (
                              <SelectItem key={customer.id} value={customer.id.toString()}>
                                {customer.full_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Loan Amount */}
                  <FormField
                    control={editForm.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Loan Amount (₹)*</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="E.g., 50000" 
                            {...field} 
                            type="number"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Interest Rate */}
                  <FormField
                    control={editForm.control}
                    name="interest_rate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Interest Rate (%)*</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="E.g., 12" 
                            {...field} 
                            type="number"
                            step="0.1"
                            min="0"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Interest Type */}
                  <FormField
                    control={editForm.control}
                    name="interest_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Interest Type*</FormLabel>
                        <Select 
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                          }}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="flat">Flat</SelectItem>
                            <SelectItem value="reducing">Reducing</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Loan Type */}
                  <FormField
                    control={editForm.control}
                    name="loan_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Loan Type*</FormLabel>
                        <Select 
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                            // Update form templates when loan type changes in edit mode
                            // Set the loan type for the current active loan
                            // Fetch configurations for this loan type 
                            // Type assertion to ensure value is treated as a valid loan type
                            const loanType = value;
                            queryClient.invalidateQueries({
                              queryKey: [`/api/companies/${companyId}/loan-configurations/loan-type/${loanType}`]
                            });
                            // Reset form data when loan type changes
                            setFormData({});
                          }}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="personal">Personal</SelectItem>
                            <SelectItem value="business">Business</SelectItem>
                            <SelectItem value="education">Education</SelectItem>
                            <SelectItem value="housing">Housing</SelectItem>
                            <SelectItem value="vehicle">Vehicle</SelectItem>
                            <SelectItem value="agriculture">Agriculture</SelectItem>
                            <SelectItem value="microfinance">Microfinance</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Term Months */}
                  <FormField
                    control={editForm.control}
                    name="term_months"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Term (Months)*</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="E.g., 12" 
                            {...field} 
                            type="number"
                            min="1"
                            onChange={(e) => {
                              field.onChange(e);
                              // Auto-update end date when term changes
                              const startDate = new Date(editForm.getValues().start_date);
                              if (!isNaN(startDate.getTime())) {
                                const endDate = new Date(startDate);
                                endDate.setMonth(startDate.getMonth() + parseInt(e.target.value, 10));
                                editForm.setValue("end_date", endDate.toISOString().split('T')[0]);
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Start Date */}
                  <FormField
                    control={editForm.control}
                    name="start_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date*</FormLabel>
                        <FormControl>
                          <Input 
                            type="date"
                            {...field} 
                            onChange={(e) => {
                              field.onChange(e);
                              // Auto-update end date when start date changes
                              const startDate = new Date(e.target.value);
                              const termMonths = parseInt(editForm.getValues().term_months, 10);
                              if (!isNaN(startDate.getTime()) && !isNaN(termMonths)) {
                                const endDate = new Date(startDate);
                                endDate.setMonth(startDate.getMonth() + termMonths);
                                editForm.setValue("end_date", endDate.toISOString().split('T')[0]);
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* End Date */}
                  <FormField
                    control={editForm.control}
                    name="end_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Date*</FormLabel>
                        <FormControl>
                          <Input 
                            type="date"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Notes */}
                  <FormField
                    control={editForm.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Add any additional notes about this loan"
                            {...field} 
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            )}

            {/* Step 2: Forms */}
            {editCurrentStep === 2 && formTemplatesForLoanType.length > 0 && (
              <div className="space-y-6">
                <div className="mb-4">
                  <h3 className="text-lg font-medium">Complete Required Forms</h3>
                  <p className="text-sm text-gray-500">
                    Fill out all required forms for this loan type.
                  </p>
                </div>
                
                {formTemplatesForLoanType.map(template => (
                  <div key={template.id} className="mb-6">
                    <LoanFormRenderer
                      templateId={template.id}
                      companyId={companyId}
                      onFormDataChange={(data) => handleFormDataChange(template.id, data)}
                      initialValues={formData[template.id] || {}}
                    />
                  </div>
                ))}
              </div>
            )}

            {/* Step 2: No Forms Available Message */}
            {editCurrentStep === 2 && formTemplatesForLoanType.length === 0 && (
              <div className="p-6 border rounded-md bg-gray-50 text-center">
                <p className="text-gray-500 mb-2">No form templates configured for this loan type.</p>
                <p className="text-sm text-gray-400">
                  You can configure forms for different loan types in the Forms section.
                </p>
              </div>
            )}

            {/* Step 3: Review */}
            {editCurrentStep === 3 && (
              <div className="space-y-6">
                <div className="mb-4">
                  <h3 className="text-lg font-medium">Review Loan Details</h3>
                  <p className="text-sm text-gray-500">
                    Please review all details before updating this loan.
                  </p>
                </div>
                
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Customer</h4>
                        <p className="text-base font-medium">
                          {customers.find(c => c.id === editForm.getValues().customer_id)?.full_name || 'Unknown'}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Loan Type</h4>
                        <p className="text-base font-medium capitalize">{editForm.getValues().loan_type}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Amount</h4>
                        <p className="text-base font-medium">₹{editForm.getValues().amount}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Interest</h4>
                        <p className="text-base font-medium">
                          {editForm.getValues().interest_rate}% ({editForm.getValues().interest_type})
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Term</h4>
                        <p className="text-base font-medium">{editForm.getValues().term_months} months</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Date Range</h4>
                        <p className="text-base font-medium">
                          {new Date(editForm.getValues().start_date).toLocaleDateString()} - {new Date(editForm.getValues().end_date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    
                    {formTemplatesForLoanType.length > 0 && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-gray-500 mb-2">Forms Attached</h4>
                        <ul className="list-disc pl-5 space-y-1">
                          {formTemplatesForLoanType.map(template => (
                            <li key={template.id} className="text-sm">
                              {template.name}
                              {formData[template.id] ? 
                                <span className="text-green-600 ml-2"><Check className="inline h-4 w-4" /> Completed</span> : 
                                <span className="text-red-600 ml-2">Not completed</span>
                              }
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center mt-6">
              <div className="flex items-center space-x-2">
                <Button 
                  type="button" 
                  variant="ghost"
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setEditCurrentStep(1);
                  }}
                >
                  Cancel
                </Button>
              </div>
              
              <div className="flex items-center space-x-2">
                {editCurrentStep > 1 && (
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => setEditCurrentStep(prev => prev - 1)}
                  >
                    Back
                  </Button>
                )}
                
                {editCurrentStep < 3 && (
                  <Button 
                    type="button"
                    onClick={() => {
                      // Validate the current step
                      if (editCurrentStep === 1) {
                        // Validate basic loan info
                        const result = loanFormSchema.safeParse(editForm.getValues());
                        if (!result.success) {
                          // Show form errors
                          result.error.issues.forEach(issue => {
                            editForm.setError(issue.path[0] as any, {
                              type: 'manual',
                              message: issue.message
                            });
                          });
                          return;
                        }
                      } else if (editCurrentStep === 2) {
                        // Validate form data is complete (if required)
                        const allFormsComplete = formTemplatesForLoanType.every(template => {
                          // Check if this form has required fields that aren't filled
                          const templateFormData = formData[template.id];
                          if (!templateFormData || !templateFormData._validate) return true; // Skip if no validation function
                          return templateFormData._validate();
                        });
                        
                        if (!allFormsComplete) {
                          toast({
                            title: "Incomplete Forms",
                            description: "Please complete all required fields in the forms.",
                            variant: "destructive"
                          });
                          return;
                        }
                      }
                      
                      // Move to next step
                      setEditCurrentStep(prev => prev + 1);
                    }}
                  >
                    Next
                  </Button>
                )}
                
                {editCurrentStep === 3 && (
                  <Button 
                    type="button"
                    onClick={() => {
                      // If we have selected loan, submit the form
                      if (selectedLoan) {
                        onSubmitEdit(editForm.getValues());
                      }
                    }}
                    disabled={updateLoanMutation.isPending}
                  >
                    {updateLoanMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Update Loan
                  </Button>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}