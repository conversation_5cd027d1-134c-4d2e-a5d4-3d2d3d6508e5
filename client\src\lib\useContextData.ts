import { useAuth } from './auth';
import { useCompany } from './companies';
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from './queryClient';
import { useQueryClient } from '@tanstack/react-query';
import logger from './logger';
import { DEFAULT_COMPANY_ID, getSafeCompanyId, isValidCompanyId } from '@/config/companyConfig';

export interface Branch {
  id: number;
  name: string;
  company_id: number;
  address?: string;
  phone?: string;
  email?: string;
  is_active: boolean;
}

/**
 * Custom hook that centralizes access to company, branch, and user data
 * throughout the application. This hook aggregates all necessary context data
 * and provides a consistent interface for components.
 */
export const useContextData = () => {
  const auth = useAuth();
  const company = useCompany();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastCompanyId, setLastCompanyId] = useState<number | undefined>(undefined);

  // Get the current user from auth context
  const user = auth.user;

  // Get the current company from company context, with fallback to user.company_id
  const currentCompany = company.currentCompany || null;

  // Derive companyId using a consistent approach, with proper logging of the source
  // IMPORTANT: Always prioritize the JWT token's company ID (user?.company_id) over company context
  // This ensures we use the actual logged-in company, not a cached/selected company
  // However, if company context is loading and we have a user company_id, use that immediately
  let derivedCompanyId = user?.company_id || currentCompany?.company_id;

  // If company context is still loading but we have user company_id, use it
  if (company.isLoading && user?.company_id && !currentCompany) {
    derivedCompanyId = user.company_id;
  }

  // Company ID derivation logic (logging removed for cleaner console)

  // Ensure we're using a valid company ID
  const validatedCompanyId = getSafeCompanyId(derivedCompanyId);

  const companyId = validatedCompanyId;
  const companyName = currentCompany?.company?.name || user?.company_name || "Your Company"; // Default name

  // Invalidate cache when company changes
  useEffect(() => {
    if (companyId && lastCompanyId && companyId !== lastCompanyId) {
      // Company changed, invalidating cache (logging removed for cleaner console)

      // Invalidate all company-related queries
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      queryClient.invalidateQueries({ queryKey: ['companies'] });

      // Clear specific company data to force refetch
      queryClient.removeQueries({ queryKey: ['/api/companies', lastCompanyId] });
    }

    if (companyId) {
      setLastCompanyId(companyId);
    }
  }, [companyId, lastCompanyId, queryClient]);

  // Log context data for debugging
  useEffect(() => {
    // Get the component stack trace to identify which component is using this hook
    const stackTrace = new Error().stack || '';
    const callerInfo = stackTrace.split('\n').slice(2, 4).join(' -> ');

    // Generate a unique instance ID for this component
    const instanceId = Math.random().toString(36).substring(2, 9);

    // Context data initialization (verbose logging removed for cleaner console)

    // Log a warning if no company ID is available (development only)
    if (!companyId && import.meta.env.DEV) {
      console.warn(`[ContextData] No company ID available. User needs to select a company.`);
    }

    // Company ID tracking (logging removed for cleaner console)

    return () => {
      // Context cleanup (logging removed for cleaner console)
    };
  }, [user, companyId, companyName, currentCompany]);

  // Fetch branches when companyId changes
  useEffect(() => {
    let isMounted = true;

    const fetchBranches = async () => {
      if (!companyId) {
        setBranches([]);
        setSelectedBranch(null);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const url = `/api/companies/${companyId}/branches`;
        // Fetching branches (logging removed for cleaner console)

        const branchesData = await apiRequest('GET', url);

        if (isMounted) {
          const branchArray = await branchesData.json();
        setBranches(branchArray);

          // If no branch is selected yet, select the first one
          if (!selectedBranch && branchArray.length > 0) {
            setSelectedBranch(branchArray[0]);
            // Auto-selected first branch (logging removed for cleaner console)
          }
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Failed to fetch branches'));
          // Error logging preserved for troubleshooting
          if (import.meta.env.DEV) {
            console.error('Error fetching branches:', err);
          }
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchBranches();

    return () => {
      isMounted = false;
    };
  }, [companyId]);

  // Switch branch with validation
  const switchBranch = (branchId: number) => {
    const branch = branches.find(b => b.id === branchId);
    if (branch) {
      setSelectedBranch(branch);
    } else {
      toast({
        title: 'Error',
        description: `Branch with ID ${branchId} not found.`,
        variant: 'destructive',
      });
    }
  };

  return {
    // User data
    user,
    userId: user?.id,
    userRole: user?.role,

    // Company data
    companyId,
    companyName,
    currentCompany,
    userCompanies: company.userCompanies,
    switchCompany: company.switchCompany,

    // Branch data
    branches,
    selectedBranch,
    branchId: selectedBranch?.id,
    branchName: selectedBranch?.name,
    switchBranch,

    // Status flags
    isLoading: isLoading || company.isLoading || auth.loading,
    error: error || company.error || auth.error,
    isAuthenticated: !!user,
    hasCompanyContext: !!companyId,
    hasBranchContext: !!selectedBranch
  };
};