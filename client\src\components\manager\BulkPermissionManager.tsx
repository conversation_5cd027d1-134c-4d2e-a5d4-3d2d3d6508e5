import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Users,
  Shield,
  Plus,
  Minus,
  Loader2,
  CheckCircle,
  AlertTriangle,
  Search
} from 'lucide-react';
import { apiRequest } from '@/lib/api';

interface TeamMember {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  department?: string;
  branch?: string;
  permissions: Permission[];
  roles: CustomRole[];
  last_login?: Date;
  active: boolean;
}

interface Permission {
  id: number;
  code: string;
  name: string;
  category: string;
}

interface CustomRole {
  id: number;
  name: string;
  description?: string;
}

interface BulkPermissionManagerProps {
  teamMembers: TeamMember[];
  onSuccess: () => void;
}

interface BulkOperation {
  user_ids: number[];
  role_ids?: number[];
  action: 'assign' | 'remove';
  justification?: string;
}

export const BulkPermissionManager: React.FC<BulkPermissionManagerProps> = ({
  teamMembers,
  onSuccess
}) => {
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [availableRoles, setAvailableRoles] = useState<CustomRole[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);
  const [action, setAction] = useState<'assign' | 'remove'>('assign');
  const [justification, setJustification] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingRoles, setLoadingRoles] = useState(true);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchAvailableRoles();
  }, []);

  const fetchAvailableRoles = async () => {
    try {
      const response = await apiRequest('GET', '/api/manager-tools/available-roles');

      if (!response.ok) {
        throw new Error('Failed to fetch available roles');
      }

      const roles = await response.json();
      setAvailableRoles(roles);
    } catch (error) {
      console.error('Error fetching available roles:', error);
      setMessage({ type: 'error', text: 'Failed to load available roles' });
    } finally {
      setLoadingRoles(false);
    }
  };

  const filteredMembers = teamMembers.filter(member =>
    member.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUserSelection = (userId: number, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(filteredMembers.map(member => member.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleRoleSelection = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedRoles(prev => [...prev, roleId]);
    } else {
      setSelectedRoles(prev => prev.filter(id => id !== roleId));
    }
  };

  const handleBulkOperation = async () => {
    if (selectedUsers.length === 0) {
      setMessage({ type: 'error', text: 'Please select at least one user' });
      return;
    }

    if (selectedRoles.length === 0) {
      setMessage({ type: 'error', text: 'Please select at least one role' });
      return;
    }

    if (!justification.trim()) {
      setMessage({ type: 'error', text: 'Please provide a justification for this operation' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      const operation: BulkOperation = {
        user_ids: selectedUsers,
        role_ids: selectedRoles,
        action,
        justification: justification.trim()
      };

      const response = await apiRequest('POST', '/api/manager-tools/bulk-permissions', operation);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to perform bulk operation');
      }

      const result = await response.json();
      setMessage({
        type: 'success',
        text: `Operation completed: ${result.success} successful, ${result.failed} failed`
      });

      // Reset form
      setSelectedUsers([]);
      setSelectedRoles([]);
      setJustification('');

      // Refresh parent data
      onSuccess();
    } catch (error) {
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : 'An error occurred'
      });
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Bulk Permission Management
          </CardTitle>
          <CardDescription>
            Assign or remove roles for multiple team members at once
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {message && (
            <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
              {message.type === 'error' ? (
                <AlertTriangle className="h-4 w-4" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              <AlertDescription>{message.text}</AlertDescription>
            </Alert>
          )}

          {/* Action Selection */}
          <div className="space-y-2">
            <Label>Operation Type</Label>
            <Select value={action} onValueChange={(value: 'assign' | 'remove') => setAction(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="assign">
                  <div className="flex items-center">
                    <Plus className="mr-2 h-4 w-4" />
                    Assign Roles
                  </div>
                </SelectItem>
                <SelectItem value="remove">
                  <div className="flex items-center">
                    <Minus className="mr-2 h-4 w-4" />
                    Remove Roles
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* User Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Select Team Members</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={selectedUsers.length === filteredMembers.length && filteredMembers.length > 0}
                  onCheckedChange={handleSelectAll}
                />
                <Label htmlFor="select-all" className="text-sm">
                  Select All ({filteredMembers.length})
                </Label>
              </div>
            </div>

            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search team members..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>

            <div className="max-h-60 overflow-y-auto border rounded-md p-4 space-y-2">
              {filteredMembers.map((member) => (
                <div key={member.id} className="flex items-center space-x-3 p-2 hover:bg-muted rounded">
                  <Checkbox
                    id={`user-${member.id}`}
                    checked={selectedUsers.includes(member.id)}
                    onCheckedChange={(checked) => handleUserSelection(member.id, checked as boolean)}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{member.full_name}</div>
                    <div className="text-sm text-muted-foreground">{member.email}</div>
                    <div className="text-xs text-muted-foreground">
                      {member.department && `${member.department} • `}
                      {member.permissions.length} permissions
                    </div>
                  </div>
                  <Badge variant={member.active ? 'default' : 'secondary'}>
                    {member.active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              ))}
            </div>

            {selectedUsers.length > 0 && (
              <div className="text-sm text-muted-foreground">
                {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
              </div>
            )}
          </div>

          <Separator />

          {/* Role Selection */}
          <div className="space-y-4">
            <Label>Select Roles</Label>
            {loadingRoles ? (
              <div className="flex justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <div className="max-h-40 overflow-y-auto border rounded-md p-4 space-y-2">
                {availableRoles.map((role) => (
                  <div key={role.id} className="flex items-center space-x-3 p-2 hover:bg-muted rounded">
                    <Checkbox
                      id={`role-${role.id}`}
                      checked={selectedRoles.includes(role.id)}
                      onCheckedChange={(checked) => handleRoleSelection(role.id, checked as boolean)}
                    />
                    <div className="flex-1">
                      <div className="font-medium">{role.name}</div>
                      {role.description && (
                        <div className="text-sm text-muted-foreground">{role.description}</div>
                      )}
                    </div>
                    <Shield className="h-4 w-4 text-muted-foreground" />
                  </div>
                ))}
              </div>
            )}

            {selectedRoles.length > 0 && (
              <div className="text-sm text-muted-foreground">
                {selectedRoles.length} role{selectedRoles.length !== 1 ? 's' : ''} selected
              </div>
            )}
          </div>

          <Separator />

          {/* Justification */}
          <div className="space-y-2">
            <Label htmlFor="justification">Justification *</Label>
            <Textarea
              id="justification"
              placeholder="Provide a reason for this bulk operation..."
              value={justification}
              onChange={(e) => setJustification(e.target.value)}
              rows={3}
            />
          </div>

          {/* Submit Button */}
          <Button
            onClick={handleBulkOperation}
            disabled={loading || selectedUsers.length === 0 || selectedRoles.length === 0}
            className="w-full"
          >
            {loading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : action === 'assign' ? (
              <Plus className="mr-2 h-4 w-4" />
            ) : (
              <Minus className="mr-2 h-4 w-4" />
            )}
            {action === 'assign' ? 'Assign Roles' : 'Remove Roles'}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
