import React, { useState, useCallback, useMemo } from 'react';
import { DragDropContext, Droppable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Search, Plus, AlertTriangle, RefreshCw, Expand, Minimize2 } from 'lucide-react';
import { RoleNode } from './RoleNode';
import { InheritanceTypeSelector, type InheritanceType } from './InheritanceTypeSelector';
import { useRoleHierarchy, type RoleHierarchyNode } from '@/hooks/useRoleHierarchy';
import { useToast } from '@/components/ui/use-toast';

interface RoleHierarchyBuilderProps {
  companyId?: number;
  onEditRole?: (roleId: number) => void;
  onCreateRole?: () => void;
  className?: string;
}

export function RoleHierarchyBuilder({
  companyId,
  onEditRole,
  onCreateRole,
  className
}: RoleHierarchyBuilderProps) {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set());
  const [selectedInheritanceType, setSelectedInheritanceType] = useState<InheritanceType>('inherit');
  const [conflicts, setConflicts] = useState<Map<number, string>>(new Map());

  const {
    hierarchyTree,
    roles,
    isLoading,
    error,
    createHierarchy,
    updateHierarchy,
    deleteHierarchy,
    isCreating,
    isUpdating,
    isDeleting,
    checkCircularDependency,
  } = useRoleHierarchy(companyId);

  // Filter roles based on search query
  const filteredTree = useMemo(() => {
    if (!hierarchyTree || !searchQuery.trim()) {
      return hierarchyTree;
    }

    const filterNodes = (nodes: RoleHierarchyNode[]): RoleHierarchyNode[] => {
      return nodes.filter(node => {
        const matchesSearch = node.role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                            node.role.description?.toLowerCase().includes(searchQuery.toLowerCase());
        const hasMatchingChildren = node.children && filterNodes(node.children).length > 0;

        if (matchesSearch || hasMatchingChildren) {
          return {
            ...node,
            children: hasMatchingChildren ? filterNodes(node.children) : node.children,
          };
        }
        return false;
      }).filter(Boolean) as RoleHierarchyNode[];
    };

    return filterNodes(hierarchyTree);
  }, [hierarchyTree, searchQuery]);

  // Toggle node expansion
  const handleToggleExpand = useCallback((roleId: number) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(roleId)) {
        newSet.delete(roleId);
      } else {
        newSet.add(roleId);
      }
      return newSet;
    });
  }, []);

  // Expand all nodes
  const handleExpandAll = useCallback(() => {
    if (!hierarchyTree) return;

    const getAllRoleIds = (nodes: RoleHierarchyNode[]): number[] => {
      return nodes.reduce((ids, node) => {
        ids.push(node.role.id);
        if (node.children) {
          ids.push(...getAllRoleIds(node.children));
        }
        return ids;
      }, [] as number[]);
    };

    setExpandedNodes(new Set(getAllRoleIds(hierarchyTree)));
  }, [hierarchyTree]);

  // Collapse all nodes
  const handleCollapseAll = useCallback(() => {
    setExpandedNodes(new Set());
  }, []);

  // Handle drag and drop
  const handleDragEnd = useCallback(async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    const roleId = parseInt(draggableId.replace('role-', ''));
    const newParentId = parseInt(destination.droppableId.replace('role-children-', ''));

    // Check for circular dependency
    const wouldCreateCircular = await checkCircularDependency(newParentId, roleId);
    if (wouldCreateCircular) {
      toast({
        title: 'Invalid Operation',
        description: 'This operation would create a circular dependency in the role hierarchy.',
        variant: 'destructive',
      });
      return;
    }

    // Create new hierarchy relationship
    createHierarchy({
      parent_role_id: newParentId,
      child_role_id: roleId,
      inheritance_type: selectedInheritanceType,
    });
  }, [createHierarchy, selectedInheritanceType, checkCircularDependency, toast]);

  // Handle inheritance type update
  const handleUpdateInheritance = useCallback((parentId: number, childId: number, inheritanceType: InheritanceType) => {
    // Find the hierarchy relationship ID
    // This would need to be implemented based on your data structure
    // For now, we'll assume we can find it or create a new endpoint
    updateHierarchy({
      id: 0, // This should be the actual hierarchy relationship ID
      data: { inheritance_type: inheritanceType }
    });
  }, [updateHierarchy]);

  // Handle role deletion
  const handleDeleteRole = useCallback((roleId: number) => {
    // This would typically open a confirmation dialog
    // For now, we'll just show a toast
    toast({
      title: 'Delete Role',
      description: 'Role deletion functionality would be implemented here.',
    });
  }, [toast]);

  // Handle adding child role
  const handleAddChild = useCallback((parentId: number) => {
    // This would typically open a role creation dialog
    // For now, we'll just show a toast
    toast({
      title: 'Add Child Role',
      description: 'Child role creation functionality would be implemented here.',
    });
  }, [toast]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load role hierarchy: {error.message}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Role Hierarchy Builder</CardTitle>
            <CardDescription>
              Drag and drop roles to create hierarchical relationships. Configure inheritance types to control permission flow.
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleExpandAll}>
              <Expand className="h-4 w-4 mr-1" />
              Expand All
            </Button>
            <Button variant="outline" size="sm" onClick={handleCollapseAll}>
              <Minimize2 className="h-4 w-4 mr-1" />
              Collapse All
            </Button>
            {onCreateRole && (
              <Button onClick={onCreateRole}>
                <Plus className="h-4 w-4 mr-1" />
                Create Role
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Search and Controls */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search roles..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Default inheritance:</span>
            <InheritanceTypeSelector
              value={selectedInheritanceType}
              onChange={setSelectedInheritanceType}
              size="sm"
              showTooltip={false}
            />
          </div>
        </div>

        {/* Role Hierarchy Tree */}
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="role-hierarchy-root" type="ROLE">
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className={`
                  min-h-[200px] space-y-2
                  ${snapshot.isDraggingOver ? 'bg-blue-50 border-2 border-dashed border-blue-300 rounded-md p-2' : ''}
                `}
              >
                {filteredTree && filteredTree.length > 0 ? (
                  filteredTree.map((node, index) => (
                    <RoleNode
                      key={node.role.id}
                      node={node}
                      index={index}
                      level={0}
                      isExpanded={expandedNodes.has(node.role.id)}
                      onToggleExpand={handleToggleExpand}
                      onEditRole={onEditRole || (() => {})}
                      onDeleteRole={handleDeleteRole}
                      onUpdateInheritance={handleUpdateInheritance}
                      onAddChild={handleAddChild}
                      isDragDisabled={isCreating || isUpdating || isDeleting}
                      hasConflicts={conflicts.has(node.role.id)}
                      conflictMessage={conflicts.get(node.role.id)}
                    />
                  ))
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    {searchQuery ? 'No roles found matching your search.' : 'No roles found. Create your first role to get started.'}
                  </div>
                )}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>

        {/* Status Information */}
        {(isCreating || isUpdating || isDeleting) && (
          <Alert>
            <RefreshCw className="h-4 w-4 animate-spin" />
            <AlertDescription>
              {isCreating && 'Creating hierarchy relationship...'}
              {isUpdating && 'Updating hierarchy relationship...'}
              {isDeleting && 'Deleting hierarchy relationship...'}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
