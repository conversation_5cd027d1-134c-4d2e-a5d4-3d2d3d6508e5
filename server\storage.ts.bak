import { 
  users, companies, customers, agents, loans, collections, userCompanies,
  resellers, resellerClients, resellerCommissions, referrals,
  subscriptionPlans, subscriptions, partners, formTemplates, formFields, formSubmissions,
  loanConfigurations, paymentSchedules, payments, expenses, fines,
  // Financial management tables
  accounts, transactions, accountBalances, accountingPeriods,
  shareholders, shareholdings, investmentTransactions, profitDistributions,
  balanceSheets, balanceSheetItems, fixedAssets, depreciationSchedules,
  // User types
  type User, type InsertUser, type Company, type InsertCompany,
  type Customer, type InsertCustomer, type Agent, type InsertAgent,
  type Loan, type InsertLoan, type Collection, type InsertCollection,
  type Reseller, type InsertReseller, type ResellerClient, type InsertResellerClient,
  type ResellerCommission, type InsertResellerCommission, type Referral, type InsertReferral,
  type SubscriptionPlan, type InsertSubscriptionPlan, type Subscription, type InsertSubscription,
  type UserCompany, type InsertUserCompany, type Partner, type InsertPartner,
  type FormTemplate, type InsertFormTemplate, type FormField, type InsertFormField,
  type FormSubmission, type InsertFormSubmission, type LoanConfiguration, type InsertLoanConfiguration,
  type PaymentSchedule, type InsertPaymentSchedule, type Payment, type InsertPayment,
  type Expense, type InsertExpense, type Fine, type InsertFine,
  // Financial management types
  type Account, type InsertAccount, type Transaction, type InsertTransaction,
  type AccountBalance, type InsertAccountBalance, type AccountingPeriod, type InsertAccountingPeriod,
  type Shareholder, type InsertShareholder, type Shareholding, type InsertShareholding,
  type InvestmentTransaction, type InsertInvestmentTransaction, 
  type ProfitDistribution, type InsertProfitDistribution,
  type BalanceSheet, type InsertBalanceSheet, type BalanceSheetItem, type InsertBalanceSheetItem,
  type FixedAsset, type InsertFixedAsset, type DepreciationSchedule, type InsertDepreciationSchedule,
  // Report interfaces
  type DailyCollectionReport, type DaySheetReport, type CustomerReport,
  type AgentReport, type ProfitLossReport, type AccountStatement, type AccountBalanceReport,
  type BalanceSheetReport, type BalanceSheetDetail, type CashFlowReport, type ShareholderReport
} from "@shared/schema";

// Import finance management functions
import * as financialManagement from './financialManagement';

// Import other dependencies
import { db } from './db';
import { eq, and, desc, sql, gte, lte, isNull, count, sum } from 'drizzle-orm';
import bcrypt from 'bcrypt';
import errorLogger from './utils/errorLogger';
import { generatePDF } from './utils/pdfGenerator';

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User>;
  getUsersByCompany(companyId: number): Promise<User[]>;
  updateUserPassword(id: number, newPassword: string): Promise<User>;
  
  // Company operations
  getCompany(id: number): Promise<Company | undefined>;
  getCompanies(): Promise<Company[]>;
  createCompany(company: InsertCompany): Promise<Company>;
  updateCompany(id: number, company: Partial<InsertCompany>): Promise<Company>;
  deleteCompany(id: number): Promise<void>;
  getCompanyUsers(companyId: number): Promise<User[]>;
  
  // UserCompany operations
  getUserCompanies(userId: number): Promise<(UserCompany & { company: Company })[]>;
  getUserCompanyByIds(userId: number, companyId: number): Promise<UserCompany | undefined>;
  createUserCompany(userCompany: InsertUserCompany): Promise<UserCompany>;
  updateUserCompany(id: number, userCompany: Partial<InsertUserCompany>): Promise<UserCompany>;
  deleteUserCompany(id: number): Promise<void>;
  setUserCompanyAsPrimary(id: number, userId: number): Promise<UserCompany>;
  
  // Customer operations
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomersByCompany(companyId: number): Promise<Customer[]>;
  getCustomersByBranch(branchId: number): Promise<Customer[]>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, companyId: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined>;
  deleteCustomer(id: number, companyId: number): Promise<boolean>;
  
  // Agent operations
  getAgent(id: number): Promise<Agent | undefined>;
  getAgentsByCompany(companyId: number): Promise<Agent[]>;
  getAgentsByBranch(branchId: number): Promise<Agent[]>;
  createAgent(agent: InsertAgent): Promise<Agent>;
  updateAgent(id: number, agent: Partial<InsertAgent>): Promise<Agent>;
  deleteAgent(id: number): Promise<void>;
  
  // Partner operations
  getPartner(id: number): Promise<Partner | undefined>;
  getPartnersByCompany(companyId: number): Promise<Partner[]>;
  createPartner(partner: InsertPartner): Promise<Partner>;
  updatePartner(id: number, partner: Partial<InsertPartner>): Promise<Partner>;
  deletePartner(id: number): Promise<void>;
  
  // Loan operations
  getLoan(id: number): Promise<Loan | undefined>;
  getLoansByCustomer(customerId: number): Promise<Loan[]>;
  getLoansByCompany(companyId: number): Promise<Loan[]>;
  getLoansByBranch(branchId: number): Promise<Loan[]>;
  createLoan(loan: InsertLoan): Promise<Loan>;
  updateLoan(id: number, loan: Partial<InsertLoan>): Promise<Loan>;
  deleteLoan(id: number, companyId: number): Promise<{ success: boolean, error?: string, collectionsCount?: number }>;
  deleteLoanWithCollections(id: number, companyId: number): Promise<{ success: boolean, error?: string, collectionsDeleted?: number }>;
  
  // Collection operations
  getCollection(id: number): Promise<Collection | undefined>;
  getCollectionsByCompany(companyId: number, status?: string, agentId?: number, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  getCollectionsByBranch(branchId: number, status?: string, agentId?: number, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  getCollectionsByLoan(loanId: number, companyId: number): Promise<Collection[]>;
  getCollectionsByAgent(agentId: number, companyId: number, status?: string, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  createCollection(collection: InsertCollection): Promise<Collection>;
  updateCollection(id: number, companyId: number, collection: Partial<InsertCollection>): Promise<Collection>;
  updateCollectionStatus(id: number, status: string): Promise<Collection>;
  deleteCollection(id: number): Promise<void>;
  markCollectionsAsCompleted(collectionIds: number[]): Promise<void>;
  
  // Payment Schedule operations
  getPaymentSchedule(id: number): Promise<PaymentSchedule | undefined>;
  getPaymentSchedulesByLoan(loanId: number, companyId: number): Promise<PaymentSchedule[]>;
  createPaymentSchedule(paymentSchedule: InsertPaymentSchedule): Promise<PaymentSchedule>;
  updatePaymentSchedule(id: number, paymentSchedule: Partial<InsertPaymentSchedule>): Promise<PaymentSchedule>;
  deletePaymentSchedule(id: number): Promise<void>;
  generatePaymentSchedules(loanId: number, companyId: number): Promise<PaymentSchedule[]>;
  
  // Payment operations
  getPayment(id: number): Promise<Payment | undefined>;
  getPaymentsByCollection(collectionId: number): Promise<Payment[]>;
  createPayment(payment: InsertPayment): Promise<Payment>;
  generatePaymentPdf(paymentId: number): Promise<Buffer>;
  
  // Expense operations
  getExpenseById(id: number): Promise<Expense | undefined>;
  getExpenses(companyId: number, filters?: any): Promise<Expense[]>;
  createExpense(expense: InsertExpense): Promise<Expense>;
  updateExpense(id: number, expense: Partial<InsertExpense>): Promise<Expense>;
  deleteExpense(id: number): Promise<void>;
  
  // Form Template operations
  getFormTemplate(id: number): Promise<FormTemplate | undefined>;
  getFormTemplatesByCompany(companyId: number): Promise<FormTemplate[]>;
  getActiveFormTemplates(companyId: number): Promise<FormTemplate[]>;
  createFormTemplate(formTemplate: InsertFormTemplate): Promise<FormTemplate>;
  updateFormTemplate(id: number, formTemplate: Partial<InsertFormTemplate>): Promise<FormTemplate>;
  deleteFormTemplate(id: number): Promise<void>;
  toggleFormTemplateActive(id: number): Promise<FormTemplate>;
  
  // Form Field operations
  getFormFieldsByTemplate(templateId: number): Promise<FormField[]>;
  createFormField(formField: InsertFormField): Promise<FormField>;
  updateFormField(id: number, formField: Partial<InsertFormField>): Promise<FormField>;
  deleteFormField(id: number): Promise<void>;
  
  // Form Submission operations
  getFormSubmissionsByLoan(loanId: number, companyId: number): Promise<FormSubmission[]>;
  getFormSubmission(id: number): Promise<FormSubmission | undefined>;
  createFormSubmission(formSubmission: InsertFormSubmission): Promise<FormSubmission>;
  updateFormSubmission(id: number, formSubmission: Partial<InsertFormSubmission>): Promise<FormSubmission>;
  deleteFormSubmission(id: number): Promise<void>;
  
  // Loan Configuration operations
  getLoanConfiguration(id: number): Promise<LoanConfiguration | undefined>;
  getLoanConfigurationsByCompany(companyId: number): Promise<LoanConfiguration[]>;
  getActiveLoanConfigurations(companyId: number): Promise<LoanConfiguration[]>;
  createLoanConfiguration(loanConfiguration: InsertLoanConfiguration): Promise<LoanConfiguration>;
  updateLoanConfiguration(id: number, loanConfiguration: Partial<InsertLoanConfiguration>): Promise<LoanConfiguration>;
  deleteLoanConfiguration(id: number): Promise<void>;
  toggleLoanConfigurationActive(id: number): Promise<LoanConfiguration>;
  
  // Subscription Plan operations
  getSubscriptionPlan(id: number): Promise<SubscriptionPlan | undefined>;
  getSubscriptionPlans(): Promise<SubscriptionPlan[]>;
  createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlan>;
  updateSubscriptionPlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan>;
  deleteSubscriptionPlan(id: number): Promise<void>;
  getPublicSubscriptionPlans(): Promise<SubscriptionPlan[]>;
  
  // Subscription operations
  getSubscription(id: number): Promise<Subscription | undefined>;
  getSubscriptionsByCompany(companyId: number): Promise<Subscription[]>;
  createSubscription(subscription: InsertSubscription): Promise<Subscription>;
  updateSubscription(id: number, subscription: Partial<InsertSubscription>): Promise<Subscription>;
  deleteSubscription(id: number): Promise<void>;
  
  // Reseller operations
  getReseller(id: number): Promise<Reseller | undefined>;
  getResellerByUserId(userId: number): Promise<Reseller | undefined>;
  createReseller(reseller: InsertReseller): Promise<Reseller>;
  updateReseller(id: number, reseller: Partial<InsertReseller>): Promise<Reseller>;
  getResellerClients(resellerId: number): Promise<(ResellerClient & { company: Company })[]>;
  createResellerCommission(commission: InsertResellerCommission): Promise<ResellerCommission>;
  getResellerCommissions(resellerId: number): Promise<ResellerCommission[]>;
  
  // Report operations
  getDailyCollectionsReport(
    companyId: number,
    startDate: string,
    endDate: string,
    status?: string,
    agentId?: number,
    branchId?: number,
    paymentMethod?: string
  ): Promise<DailyCollectionReport>;
  
  getDaySheetReport(
    companyId: number,
    date: string,
    branchId?: number
  ): Promise<DaySheetReport>;
  
  getCustomerReport(
    companyId: number,
    customerId: number,
    startDate?: string,
    endDate?: string
  ): Promise<CustomerReport>;
  
  getAgentReport(
    companyId: number,
    agentId: number,
    startDate?: string,
    endDate?: string
  ): Promise<AgentReport>;
  
  getProfitLossReport(
    companyId: number,
    startDate: string,
    endDate: string,
    branchId?: number
  ): Promise<ProfitLossReport>;
  
  // Financial Management operations
  // Account operations
  createAccount(account: InsertAccount): Promise<Account>;
  getAccount(id: number, companyId: number): Promise<Account | undefined>;
  getAccountsByCompany(companyId: number): Promise<Account[]>;
  getAccountHierarchy(companyId: number): Promise<Account[]>;
  updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account | undefined>;
  deleteAccount(id: number, companyId: number): Promise<boolean>;
  
  // Transaction operations
  createTransaction(transaction: InsertTransaction): Promise<Transaction>;
  getTransaction(id: number, companyId: number): Promise<Transaction | undefined>;
  getTransactionsByCompany(
    companyId: number, 
    options?: { 
      startDate?: string; 
      endDate?: string; 
      page?: number; 
      limit?: number; 
      accountType?: string;
      transactionType?: string;
      referenceType?: string;
      searchTerm?: string;
    }
  ): Promise<{ transactions: Transaction[]; totalCount: number }>;
  getTransactionsByAccount(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]>;
  updateTransaction(id: number, companyId: number, transaction: Partial<InsertTransaction>): Promise<Transaction | undefined>;
  deleteTransaction(id: number, companyId: number): Promise<boolean>;
  
  // Account Balance operations
  createAccountBalance(balance: InsertAccountBalance): Promise<AccountBalance>;
  getAccountBalances(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<AccountBalance[]>;
  getLatestAccountBalance(accountId: number, companyId: number): Promise<AccountBalance | undefined>;
  reconcileAccountBalance(id: number, userId: number, companyId: number): Promise<AccountBalance | undefined>;
  
  // Accounting Period operations
  createAccountingPeriod(period: InsertAccountingPeriod): Promise<AccountingPeriod>;
  getAccountingPeriods(companyId: number): Promise<AccountingPeriod[]>;
  getCurrentAccountingPeriod(companyId: number): Promise<AccountingPeriod | undefined>;
  closeAccountingPeriod(id: number, userId: number, companyId: number): Promise<AccountingPeriod | undefined>;
  
  // Shareholder operations
  createShareholder(shareholder: InsertShareholder): Promise<Shareholder>;
  getShareholder(id: number, companyId: number): Promise<Shareholder | undefined>;
  getShareholdersByCompany(companyId: number): Promise<Shareholder[]>;
  updateShareholder(id: number, companyId: number, shareholder: Partial<InsertShareholder>): Promise<Shareholder | undefined>;
  createShareholding(shareholding: InsertShareholding): Promise<Shareholding>;
  
  // Investment operations
  createInvestmentTransaction(transaction: InsertInvestmentTransaction): Promise<InvestmentTransaction>;
  getInvestmentTransactions(shareholderId: number): Promise<InvestmentTransaction[]>;
  
  // Financial Reports
  getAccountStatement(companyId: number, accountId: number, startDate: string, endDate: string): Promise<AccountStatement>;
  getBalanceSheetReport(companyId: number, asOfDate: string): Promise<BalanceSheetReport>;
  getCashFlowReport(companyId: number, startDate: string, endDate: string): Promise<CashFlowReport>;
  getShareholderReport(companyId: number, shareholderId: number): Promise<ShareholderReport>;
  
  // Fixed Asset operations
  createFixedAsset(asset: InsertFixedAsset): Promise<FixedAsset>;
  getFixedAssetsByCompany(companyId: number): Promise<FixedAsset[]>;
  updateFixedAsset(id: number, companyId: number, asset: Partial<InsertFixedAsset>): Promise<FixedAsset | undefined>;
  recordDepreciation(assetId: number, amount: number, companyId: number): Promise<FixedAsset | undefined>;
  disposeFixedAsset(id: number, disposalAmount: number, disposalDate: Date, companyId: number): Promise<FixedAsset | undefined>;
}

// Implementation of the IStorage interface for testing financial management features
export class MemStorage implements IStorage {
  private users: User[] = [];
  private companies: Company[] = [];
  private userCompanies: (UserCompany & { company: Company })[] = [];
  private customers: Customer[] = [];
  private agents: Agent[] = [];
  private loans: Loan[] = [];
  private collections: Collection[] = [];
  private partners: Partner[] = [];
  private paymentSchedules: PaymentSchedule[] = [];
  private payments: Payment[] = [];
  private expenses: Expense[] = [];
  private subscriptionPlans: SubscriptionPlan[] = [];
  private subscriptions: Subscription[] = [];
  private formTemplates: FormTemplate[] = [];
  private formFields: FormField[] = [];
  private formSubmissions: FormSubmission[] = [];
  private loanConfigurations: LoanConfiguration[] = [];
  private resellers: Reseller[] = [];
  private resellerClients: (ResellerClient & { company: Company })[] = [];
  private resellerCommissions: ResellerCommission[] = [];
  
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    try {
      const [user] = await db.select()
        .from(users)
        .where(eq(users.id, id));
      return user;
    } catch (error) {
      errorLogger.logError(`Error fetching user id=${id}`, 'user-fetch', error as Error);
      return undefined;
    }
  }
  
  // Other existing methods...
  
  // Implementing financial management methods
  
  // Account operations
  async createAccount(account: InsertAccount): Promise<Account> {
    return financialManagement.createAccount(account);
  }
  
  async getAccount(id: number, companyId: number): Promise<Account | undefined> {
    return financialManagement.getAccountById(id, companyId);
  }
  
  async getAccountsByCompany(companyId: number): Promise<Account[]> {
    return financialManagement.getAccountsByCompany(companyId);
  }
  
  async getAccountHierarchy(companyId: number): Promise<Account[]> {
    return financialManagement.getAccountHierarchy(companyId);
  }
  
  async updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account | undefined> {
    return financialManagement.updateAccount(id, companyId, account);
  }
  
  async deleteAccount(id: number, companyId: number): Promise<boolean> {
    return financialManagement.deleteAccount(id, companyId);
  }
  
  // Transaction operations
  async createTransaction(transaction: InsertTransaction): Promise<Transaction> {
    return financialManagement.createTransaction(transaction);
  }
  
  async getTransaction(id: number, companyId: number): Promise<Transaction | undefined> {
    return financialManagement.getTransaction(id, companyId);
  }
  
  async getTransactionsByCompany(companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByCompany(companyId, startDate, endDate);
  }
  
  async getTransactionsByAccount(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByAccount(accountId, companyId, startDate, endDate);
  }
  
  // Account Balance operations
  async createAccountBalance(balance: InsertAccountBalance): Promise<AccountBalance> {
    return financialManagement.createAccountBalance(balance);
  }
  
  async getAccountBalances(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<AccountBalance[]> {
    return financialManagement.getAccountBalances(accountId, companyId, startDate, endDate);
  }
  
  async getLatestAccountBalance(accountId: number, companyId: number): Promise<AccountBalance | undefined> {
    return financialManagement.getLatestAccountBalance(accountId, companyId);
  }
  
  async reconcileAccountBalance(id: number, userId: number, companyId: number): Promise<AccountBalance | undefined> {
    return financialManagement.reconcileAccountBalance(id, userId, companyId);
  }
  
  // Accounting Period operations
  async createAccountingPeriod(period: InsertAccountingPeriod): Promise<AccountingPeriod> {
    return financialManagement.createAccountingPeriod(period);
  }
  
  async getAccountingPeriods(companyId: number): Promise<AccountingPeriod[]> {
    return financialManagement.getAccountingPeriods(companyId);
  }
  
  async getCurrentAccountingPeriod(companyId: number): Promise<AccountingPeriod | undefined> {
    return financialManagement.getCurrentAccountingPeriod(companyId);
  }
  
  async closeAccountingPeriod(id: number, userId: number, companyId: number): Promise<AccountingPeriod | undefined> {
    return financialManagement.closeAccountingPeriod(id, userId, companyId);
  }
  
  // Shareholder operations
  async createShareholder(shareholder: InsertShareholder): Promise<Shareholder> {
    return financialManagement.createShareholder(shareholder);
  }
  
  async getShareholder(id: number, companyId: number): Promise<Shareholder | undefined> {
    return financialManagement.getShareholder(id, companyId);
  }
  
  async getShareholdersByCompany(companyId: number): Promise<Shareholder[]> {
    return financialManagement.getShareholdersByCompany(companyId);
  }
  
  async updateShareholder(id: number, companyId: number, shareholder: Partial<InsertShareholder>): Promise<Shareholder | undefined> {
    return financialManagement.updateShareholder(id, companyId, shareholder);
  }
  
  async createShareholding(shareholding: InsertShareholding): Promise<Shareholding> {
    return financialManagement.createShareholding(shareholding);
  }
  
  // Investment operations
  async createInvestmentTransaction(transaction: InsertInvestmentTransaction): Promise<InvestmentTransaction> {
    return financialManagement.createInvestmentTransaction(transaction);
  }
  
  async getInvestmentTransactions(shareholderId: number): Promise<InvestmentTransaction[]> {
    return financialManagement.getInvestmentTransactions(shareholderId);
  }
  
  // Financial Reports
  async getAccountStatement(companyId: number, accountId: number, startDate: string, endDate: string): Promise<AccountStatement> {
    return financialManagement.getAccountStatement(companyId, accountId, startDate, endDate);
  }
  
  async getBalanceSheetReport(companyId: number, asOfDate: string): Promise<BalanceSheetReport> {
    return financialManagement.getBalanceSheetReport(companyId, asOfDate);
  }
  
  async getCashFlowReport(companyId: number, startDate: string, endDate: string): Promise<CashFlowReport> {
    return financialManagement.getCashFlowReport(companyId, startDate, endDate);
  }
  
  async getShareholderReport(companyId: number, shareholderId: number): Promise<ShareholderReport> {
    return financialManagement.getShareholderReport(companyId, shareholderId);
  }
  
  // Fixed Asset operations
  async createFixedAsset(asset: InsertFixedAsset): Promise<FixedAsset> {
    return financialManagement.createFixedAsset(asset);
  }
  
  async getFixedAssetsByCompany(companyId: number): Promise<FixedAsset[]> {
    return financialManagement.getFixedAssetsByCompany(companyId);
  }
  
  async updateFixedAsset(id: number, companyId: number, asset: Partial<InsertFixedAsset>): Promise<FixedAsset | undefined> {
    return financialManagement.updateFixedAsset(id, companyId, asset);
  }
  
  async recordDepreciation(assetId: number, amount: number, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.recordDepreciation(assetId, amount, companyId);
  }
  
  async disposeFixedAsset(id: number, disposalAmount: number, disposalDate: Date, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.disposeFixedAsset(id, disposalAmount, disposalDate, companyId);
  }
  
  // Reports-related methods
  async getDailyCollectionsReport(
    companyId: number,
    startDate: string,
    endDate: string,
    status?: string,
    agentId?: number,
    branchId?: number,
    paymentMethod?: string
  ): Promise<DailyCollectionReport> {
    try {
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      
      // Fetch collections within the date range
      const collectionsQuery = db.select({
        id: collections.id,
        loan_id: collections.loan_id,
        customer_id: collections.customer_id,
        agent_id: collections.agent_id,
        amount: collections.amount,
        scheduled_date: collections.scheduled_date,
        collection_date: collections.collection_date,
        status: collections.status,
        payment_method: collections.payment_method,
        receipt_id: collections.receipt_id,
      })
      .from(collections)
      .where(
        and(
          eq(collections.company_id, companyId),
          collections.scheduled_date.gte(startDateObj),
          collections.scheduled_date.lte(endDateObj)
        )
      );
      
      // Apply additional filters if provided
      if (status) {
        collectionsQuery.where(eq(collections.status, status));
      }
      
      if (agentId) {
        collectionsQuery.where(eq(collections.agent_id, agentId));
      }
      
      if (branchId) {
        collectionsQuery.where(eq(collections.branch_id, branchId));
      }
      
      if (paymentMethod) {
        collectionsQuery.where(eq(collections.payment_method, paymentMethod));
      }
      
      const collectionResults = await collectionsQuery;
      
      // Get customer and agent names for display
      const collectionItems: DailyCollectionItem[] = await Promise.all(
        collectionResults.map(async (collection) => {
          let customerName = 'Unknown Customer';
          let agentName = null;
          
          // Get customer name
          if (collection.customer_id) {
            const customer = await this.getCustomer(collection.customer_id);
            if (customer) {
              customerName = customer.full_name;
            }
          }
          
          // Get agent name if applicable
          if (collection.agent_id) {
            const agent = await this.getAgent(collection.agent_id);
            if (agent) {
              agentName = agent.full_name;
            }
          }
          
          return {
            ...collection,
            customerName,
            agentName,
            amount: collection.amount.toString()
          };
        })
      );
      
      // Group by date
      const groupedByDate = collectionItems.reduce((acc, item) => {
        const dateKey = new Date(item.scheduled_date).toISOString().split('T')[0];
        
        if (!acc[dateKey]) {
          acc[dateKey] = {
            date: dateKey,
            collections: [],
            totalAmount: 0,
            completedAmount: 0,
            pendingAmount: 0
          };
        }
        
        acc[dateKey].collections.push(item);
        
        const amount = parseFloat(item.amount);
        acc[dateKey].totalAmount += amount;
        
        if (item.status === 'completed') {
          acc[dateKey].completedAmount += amount;
        } else {
          acc[dateKey].pendingAmount += amount;
        }
        
        return acc;
      }, {} as Record<string, DailyGroupedData>);
      
      // Convert to array and sort by date
      const dailyData = Object.values(groupedByDate).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );
      
      // Calculate totals
      const totalCollected = dailyData.reduce((sum, day) => sum + day.completedAmount, 0);
      const totalPending = dailyData.reduce((sum, day) => sum + day.pendingAmount, 0);
      
      return {
        startDate,
        endDate,
        totalCollected,
        totalPending,
        dailyData,
        rawData: collectionItems
      };
    } catch (error) {
      errorLogger.logError('Error generating daily collections report', 'report-generation', error as Error);
      // Return empty report structure on error
      return {
        startDate,
        endDate,
        totalCollected: 0,
        totalPending: 0,
        dailyData: [],
        rawData: []
      };
    }
  }
  
  async getActiveLoanConfigurations(companyId: number): Promise<LoanConfiguration[]> {
    try {
      // Fetch active loan configurations with their associated templates
      const configs = await db.select()
        .from(loanConfigurations)
        .where(
          and(
            eq(loanConfigurations.company_id, companyId),
            eq(loanConfigurations.is_active, true)
          )
        );
      
      // For each configuration, fetch its template
      const configsWithTemplates = await Promise.all(
        configs.map(async (config) => {
          const [template] = await db.select()
            .from(formTemplates)
            .where(eq(formTemplates.id, config.template_id));
          
          return {
            ...config,
            template
          };
        })
      );
      
      return configsWithTemplates;
    } catch (error) {
      errorLogger.logError('Error fetching active loan configurations', 'loan-configs', error as Error);
      return [];
    }
  }
  
  // Other original methods that need to be implemented...
  
  async getUserByUsername(username: string): Promise<User | undefined> {
    // Implementation
    return undefined;
  }
  
  async getUserByEmail(email: string): Promise<User | undefined> {
    // Implementation
    return undefined;
  }
  
  async createUser(user: InsertUser): Promise<User> {
    // Implementation
    return {} as User;
  }
  
  // ...and so on for all other methods
}

/**
 * DatabaseStorage class that implements all required methods from IStorage
 * This class uses direct database access via Drizzle ORM and delegates
 * financial management operations to specialized functions
 */
export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.id, id));
      return user;
    } catch (error) {
      errorLogger.error('Error in getUser', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.username, username));
      return user;
    } catch (error) {
      errorLogger.error('Error in getUserByUsername', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const [user] = await db.select().from(users).where(eq(users.email, email));
      return user;
    } catch (error) {
      errorLogger.error('Error in getUserByEmail', error);
      return undefined;
    }
  }

  async createUser(user: InsertUser): Promise<User> {
    try {
      // Hash password if it exists
      if (user.password) {
        const hashedPassword = await bcrypt.hash(user.password, 10);
        user.password = hashedPassword;
      }
      
      const [createdUser] = await db.insert(users).values(user).returning();
      return createdUser;
    } catch (error) {
      errorLogger.error('Error in createUser', error);
      throw error;
    }
  }

  async updateUser(id: number, userData: Partial<InsertUser>): Promise<User> {
    try {
      // Hash password if it exists in the update data
      if (userData.password) {
        const hashedPassword = await bcrypt.hash(userData.password, 10);
        userData.password = hashedPassword;
      }
      
      const [updatedUser] = await db
        .update(users)
        .set(userData)
        .where(eq(users.id, id))
        .returning();
      
      return updatedUser;
    } catch (error) {
      errorLogger.error('Error in updateUser', error);
      throw error;
    }
  }

  async getUsersByCompany(companyId: number): Promise<User[]> {
    try {
      // Get all users associated with a company through the userCompanies table
      const result = await db
        .select({
          ...users
        })
        .from(users)
        .innerJoin(userCompanies, eq(users.id, userCompanies.user_id))
        .where(eq(userCompanies.company_id, companyId));
      
      return result.map(row => ({
        ...row
      }));
    } catch (error) {
      errorLogger.error('Error in getUsersByCompany', error);
      return [];
    }
  }

  async updateUserPassword(id: number, newPassword: string): Promise<User> {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      
      const [updatedUser] = await db
        .update(users)
        .set({ password: hashedPassword })
        .where(eq(users.id, id))
        .returning();
      
      return updatedUser;
    } catch (error) {
      errorLogger.error('Error in updateUserPassword', error);
      throw error;
    }
  }

  // Company operations
  async getCompany(id: number): Promise<Company | undefined> {
    try {
      const [company] = await db.select().from(companies).where(eq(companies.id, id));
      return company;
    } catch (error) {
      errorLogger.error('Error in getCompany', error);
      return undefined;
    }
  }

  async getCompanies(): Promise<Company[]> {
    try {
      return await db.select().from(companies);
    } catch (error) {
      errorLogger.error('Error in getCompanies', error);
      return [];
    }
  }

  async createCompany(company: InsertCompany): Promise<Company> {
    try {
      const [createdCompany] = await db.insert(companies).values(company).returning();
      return createdCompany;
    } catch (error) {
      errorLogger.error('Error in createCompany', error);
      throw error;
    }
  }

  async updateCompany(id: number, company: Partial<InsertCompany>): Promise<Company> {
    try {
      const [updatedCompany] = await db
        .update(companies)
        .set(company)
        .where(eq(companies.id, id))
        .returning();
      
      return updatedCompany;
    } catch (error) {
      errorLogger.error('Error in updateCompany', error);
      throw error;
    }
  }

  async deleteCompany(id: number): Promise<void> {
    try {
      await db.delete(companies).where(eq(companies.id, id));
    } catch (error) {
      errorLogger.error('Error in deleteCompany', error);
      throw error;
    }
  }

  async getCompanyUsers(companyId: number): Promise<User[]> {
    try {
      const result = await db
        .select({
          ...users
        })
        .from(users)
        .innerJoin(userCompanies, eq(users.id, userCompanies.user_id))
        .where(eq(userCompanies.company_id, companyId));
      
      return result.map(row => ({
        ...row
      }));
    } catch (error) {
      errorLogger.error('Error in getCompanyUsers', error);
      return [];
    }
  }

  // UserCompany operations
  async getUserCompanies(userId: number): Promise<(UserCompany & { company: Company })[]> {
    try {
      const result = await db
        .select({
          userCompany: userCompanies,
          company: companies
        })
        .from(userCompanies)
        .leftJoin(companies, eq(userCompanies.company_id, companies.id))
        .where(eq(userCompanies.user_id, userId));
      
      return result.map(row => ({
        ...row.userCompany,
        company: row.company
      }));
    } catch (error) {
      errorLogger.error('Error in getUserCompanies', error);
      return [];
    }
  }

  async getUserCompanyByIds(userId: number, companyId: number): Promise<UserCompany | undefined> {
    try {
      const [userCompany] = await db
        .select()
        .from(userCompanies)
        .where(
          and(
            eq(userCompanies.user_id, userId),
            eq(userCompanies.company_id, companyId)
          )
        );
      
      return userCompany;
    } catch (error) {
      errorLogger.error('Error in getUserCompanyByIds', error);
      return undefined;
    }
  }

  async createUserCompany(userCompany: InsertUserCompany): Promise<UserCompany> {
    try {
      const [createdUserCompany] = await db
        .insert(userCompanies)
        .values(userCompany)
        .returning();
      
      return createdUserCompany;
    } catch (error) {
      errorLogger.error('Error in createUserCompany', error);
      throw error;
    }
  }

  async updateUserCompany(id: number, userCompany: Partial<InsertUserCompany>): Promise<UserCompany> {
    try {
      const [updatedUserCompany] = await db
        .update(userCompanies)
        .set(userCompany)
        .where(eq(userCompanies.id, id))
        .returning();
      
      return updatedUserCompany;
    } catch (error) {
      errorLogger.error('Error in updateUserCompany', error);
      throw error;
    }
  }

  async deleteUserCompany(id: number): Promise<void> {
    try {
      await db.delete(userCompanies).where(eq(userCompanies.id, id));
    } catch (error) {
      errorLogger.error('Error in deleteUserCompany', error);
      throw error;
    }
  }

  async setUserCompanyAsPrimary(id: number, userId: number): Promise<UserCompany> {
    try {
      // First set all user companies as not primary
      await db
        .update(userCompanies)
        .set({ is_primary: false })
        .where(eq(userCompanies.user_id, userId));
      
      // Then set the specified one as primary
      const [primaryUserCompany] = await db
        .update(userCompanies)
        .set({ is_primary: true })
        .where(eq(userCompanies.id, id))
        .returning();
      
      return primaryUserCompany;
    } catch (error) {
      errorLogger.error('Error in setUserCompanyAsPrimary', error);
      throw error;
    }
  }

  // Customer operations
  async getCustomer(id: number): Promise<Customer | undefined> {
    try {
      const [customer] = await db
        .select()
        .from(customers)
        .where(eq(customers.id, id));
      
      return customer;
    } catch (error) {
      errorLogger.error('Error in getCustomer', error);
      return undefined;
    }
  }

  async getCustomersByCompany(companyId: number): Promise<Customer[]> {
    try {
      return await db
        .select()
        .from(customers)
        .where(eq(customers.company_id, companyId));
    } catch (error) {
      errorLogger.error('Error in getCustomersByCompany', error);
      return [];
    }
  }

  async getCustomersByBranch(branchId: number): Promise<Customer[]> {
    try {
      return await db
        .select()
        .from(customers)
        .where(eq(customers.branch_id, branchId));
    } catch (error) {
      errorLogger.error('Error in getCustomersByBranch', error);
      return [];
    }
  }

  async createCustomer(customer: InsertCustomer): Promise<Customer> {
    try {
      const [createdCustomer] = await db
        .insert(customers)
        .values(customer)
        .returning();
      
      return createdCustomer;
    } catch (error) {
      errorLogger.error('Error in createCustomer', error);
      throw error;
    }
  }

  async updateCustomer(id: number, companyId: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined> {
    try {
      const [updatedCustomer] = await db
        .update(customers)
        .set(customer)
        .where(and(
          eq(customers.id, id),
          eq(customers.company_id, companyId)
        ))
        .returning();
      
      return updatedCustomer;
    } catch (error) {
      errorLogger.error('Error in updateCustomer', error);
      throw error;
    }
  }

  async deleteCustomer(id: number, companyId: number): Promise<boolean> {
    try {
      const result = await db.delete(customers)
        .where(and(
          eq(customers.id, id),
          eq(customers.company_id, companyId)
        ));
      
      // Drizzle doesn't return count of deleted rows, so we check if operation completed without errors
      return true;
    } catch (error) {
      errorLogger.error('Error in deleteCustomer', error);
      return false;
    }
  }

  // Agent operations
  async getAgent(id: number): Promise<Agent | undefined> {
    try {
      const [agent] = await db
        .select()
        .from(agents)
        .where(eq(agents.id, id));
      
      return agent;
    } catch (error) {
      errorLogger.error('Error in getAgent', error);
      return undefined;
    }
  }

  async getAgentsByCompany(companyId: number): Promise<Agent[]> {
    try {
      return await db
        .select()
        .from(agents)
        .where(eq(agents.company_id, companyId));
    } catch (error) {
      errorLogger.error('Error in getAgentsByCompany', error);
      return [];
    }
  }

  async getAgentsByBranch(branchId: number): Promise<Agent[]> {
    try {
      return await db
        .select()
        .from(agents)
        .where(eq(agents.branch_id, branchId));
    } catch (error) {
      errorLogger.error('Error in getAgentsByBranch', error);
      return [];
    }
  }

  async createAgent(agent: InsertAgent): Promise<Agent> {
    try {
      const [createdAgent] = await db
        .insert(agents)
        .values(agent)
        .returning();
      
      return createdAgent;
    } catch (error) {
      errorLogger.error('Error in createAgent', error);
      throw error;
    }
  }

  async updateAgent(id: number, agent: Partial<InsertAgent>): Promise<Agent> {
    try {
      const [updatedAgent] = await db
        .update(agents)
        .set(agent)
        .where(eq(agents.id, id))
        .returning();
      
      return updatedAgent;
    } catch (error) {
      errorLogger.error('Error in updateAgent', error);
      throw error;
    }
  }

  async deleteAgent(id: number): Promise<void> {
    try {
      await db.delete(agents).where(eq(agents.id, id));
    } catch (error) {
      errorLogger.error('Error in deleteAgent', error);
      throw error;
    }
  }

  // Collection operations
  async getCollection(id: number): Promise<Collection | undefined> {
    try {
      const [collection] = await db
        .select()
        .from(collections)
        .where(eq(collections.id, id));
      
      return collection;
    } catch (error) {
      errorLogger.error('Error in getCollection', error);
      return undefined;
    }
  }

  async getCollectionsByCompany(companyId: number): Promise<Collection[]> {
    try {
      return await db
        .select()
        .from(collections)
        .where(eq(collections.company_id, companyId));
    } catch (error) {
      errorLogger.error('Error in getCollectionsByCompany', error);
      return [];
    }
  }

  async getCollectionsByLoan(loanId: number, companyId: number): Promise<Collection[]> {
    try {
      return await db
        .select()
        .from(collections)
        .where(and(
          eq(collections.loan_id, loanId),
          eq(collections.company_id, companyId)
        ));
    } catch (error) {
      errorLogger.error('Error in getCollectionsByLoan', error);
      return [];
    }
  }

  async getCollectionsByAgent(agentId: number, companyId: number, status?: string, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]> {
    try {
      let conditions = [
        eq(collections.agent_id, agentId),
        eq(collections.company_id, companyId)
      ];
      
      // Add status filter if provided
      if (status && status !== 'all') {
        conditions.push(eq(collections.status, status as any));
      }
      
      // Add date range filter if provided
      if (dateRange) {
        const { startDate, endDate } = dateRange;
        if (startDate) {
          conditions.push(sql`${collections.scheduled_date} >= ${new Date(startDate)}`);
        }
        if (endDate) {
          conditions.push(sql`${collections.scheduled_date} <= ${new Date(endDate)}`);
        }
      }
      
      return await db
        .select()
        .from(collections)
        .where(and(...conditions));
    } catch (error) {
      errorLogger.error('Error in getCollectionsByAgent', error);
      return [];
    }
  }
  
  // Payment Schedule operations
  async getPaymentSchedulesByLoan(loanId: number, companyId: number): Promise<PaymentSchedule[]> {
    try {
      return await db
        .select()
        .from(paymentSchedules)
        .where(and(
          eq(paymentSchedules.loan_id, loanId),
          eq(paymentSchedules.company_id, companyId)
        ));
    } catch (error) {
      errorLogger.error('Error in getPaymentSchedulesByLoan', error);
      return [];
    }
  }
  
  async generatePaymentSchedules(loanId: number, companyId: number): Promise<PaymentSchedule[]> {
    try {
      // Get the loan details
      const loan = await this.getLoan(loanId);
      
      if (!loan) {
        throw new Error(`Loan with ID ${loanId} not found`);
      }
      
      // Verify the loan belongs to the company
      if (loan.company_id !== companyId) {
        throw new Error(`Loan ${loanId} does not belong to company ${companyId}`);
      }
      
      // Get customer details for the payment schedule
      const customer = await this.getCustomer(loan.customer_id);
      
      if (!customer) {
        throw new Error(`Customer with ID ${loan.customer_id} not found`);
      }
      
      // Delete any existing payment schedules for this loan
      await db
        .delete(paymentSchedules)
        .where(and(
          eq(paymentSchedules.loan_id, loanId),
          eq(paymentSchedules.company_id, companyId)
        ));
      
      const amount = typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount);
      const interestRate = typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate);
      const term = typeof loan.term === 'string' ? parseInt(loan.term, 10) : loan.term;
      const paymentFrequency = loan.payment_frequency || 'monthly';
      const interestType = loan.interest_type || 'flat';
      
      // Calculate payment amount based on interest type
      let paymentAmount = 0;
      if (interestType === 'flat') {
        // Simple interest calculation
        const totalInterest = (amount * interestRate * term) / 100;
        paymentAmount = (amount + totalInterest) / term;
      } else {
        // Reducing balance / compound interest calculation (approximation)
        // Using PMT formula: PMT = (PV * r * (1+r)^n) / ((1+r)^n - 1)
        const r = interestRate / 100 / 12; // monthly rate
        const n = term; // number of periods
        paymentAmount = (amount * r * Math.pow(1 + r, n)) / (Math.pow(1 + r, n) - 1);
      }
      
      // Round the payment amount to 2 decimal places
      paymentAmount = Math.round(paymentAmount * 100) / 100;
      
      // Calculate payment dates based on frequency
      const startDate = new Date(loan.start_date);
      const schedules: InsertPaymentSchedule[] = [];
      
      // Set increment based on payment frequency
      let dateIncrement = 0;
      switch (paymentFrequency) {
        case 'daily':
          dateIncrement = 1;
          break;
        case 'weekly':
          dateIncrement = 7;
          break;
        case 'biweekly':
          dateIncrement = 14;
          break;
        case 'monthly':
        default:
          dateIncrement = 0; // We'll handle monthly specially
          break;
      }
      
      for (let i = 0; i < term; i++) {
        const dueDate = new Date(startDate);
        
        if (paymentFrequency === 'monthly') {
          // For monthly, add months properly
          dueDate.setMonth(dueDate.getMonth() + i + 1);
        } else {
          // For other frequencies, add days
          dueDate.setDate(dueDate.getDate() + dateIncrement * (i + 1));
        }
        
        schedules.push({
          company_id: companyId,
          loan_id: loanId,
          customer_id: loan.customer_id,
          payment_number: i + 1,
          amount: paymentAmount.toString(),
          due_date: dueDate,
          status: 'pending',
          time_of_day: 'morning',
          notes: `Payment ${i + 1} of ${term}`
        });
      }
      
      // Insert all schedules and return them
      const createdSchedules = await db
        .insert(paymentSchedules)
        .values(schedules)
        .returning();
      
      return createdSchedules;
    } catch (error) {
      errorLogger.error('Error in generatePaymentSchedules', error);
      console.error('Failed to generate payment schedules:', error);
      throw error;
    }
  }
  
  // Form Submission operations
  async getFormSubmissionsByLoan(loanId: number, companyId: number): Promise<FormSubmission[]> {
    try {
      return await db
        .select()
        .from(formSubmissions)
        .where(and(
          eq(formSubmissions.loan_id, loanId),
          eq(formSubmissions.company_id, companyId)
        ));
    } catch (error) {
      errorLogger.error('Error in getFormSubmissionsByLoan', error);
      return [];
    }
  }

  async createCollection(collection: InsertCollection): Promise<Collection> {
    try {
      const [createdCollection] = await db
        .insert(collections)
        .values(collection)
        .returning();
      
      return createdCollection;
    } catch (error) {
      errorLogger.error('Error in createCollection', error);
      throw error;
    }
  }

  async updateCollection(id: number, companyId: number, collection: Partial<InsertCollection>): Promise<Collection> {
    try {
      // Check if collection object is empty
      if (!collection || Object.keys(collection).length === 0) {
        throw new Error("No values to set");
      }
      
      const [updatedCollection] = await db
        .update(collections)
        .set(collection)
        .where(and(
          eq(collections.id, id),
          eq(collections.company_id, companyId)
        ))
        .returning();
      
      return updatedCollection;
    } catch (error) {
      errorLogger.error('Error in updateCollection', error);
      throw error;
    }
  }

  async deleteCollection(id: number): Promise<void> {
    try {
      await db.delete(collections).where(eq(collections.id, id));
    } catch (error) {
      errorLogger.error('Error in deleteCollection', error);
      throw error;
    }
  }
  
  // Add a new function to delete a loan and all its collections in a transaction
  async deleteLoanWithCollections(id: number, companyId: number): Promise<{ 
    success: boolean, 
    error?: string, 
    collectionsDeleted?: number 
  }> {
    try {
      // Use a transaction to ensure all operations succeed or fail together
      const result = await db.transaction(async (tx) => {
        // First get the count of collections to be deleted
        const collectionsResult = await tx
          .select({ count: count() })
          .from(collections)
          .where(and(
            eq(collections.loan_id, id),
            eq(collections.company_id, companyId)
          ));
          
        const collectionsCount = collectionsResult[0]?.count || 0;
        
        // Delete all collections associated with this loan
        if (collectionsCount > 0) {
          await tx
            .delete(collections)
            .where(and(
              eq(collections.loan_id, id),
              eq(collections.company_id, companyId)
            ));
        }
        
        // Then delete the loan itself
        await tx
          .delete(loans)
          .where(and(
            eq(loans.id, id),
            eq(loans.company_id, companyId)
          ));
          
        return {
          success: true,
          collectionsDeleted: collectionsCount
        };
      });
      
      return result;
    } catch (error) {
      errorLogger.error('Error in deleteLoanWithCollections', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  // Include all the financial management methods
  // Account operations
  async createAccount(account: InsertAccount): Promise<Account> {
    return financialManagement.createAccount(account);
  }
  
  async getAccount(id: number, companyId: number): Promise<Account | undefined> {
    return financialManagement.getAccountById(id, companyId);
  }
  
  async getAccountsByCompany(companyId: number): Promise<Account[]> {
    return financialManagement.getAccountsByCompany(companyId);
  }
  
  async updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account | undefined> {
    return financialManagement.updateAccount(id, companyId, account);
  }
  
  async deleteAccount(id: number, companyId: number): Promise<boolean> {
    return financialManagement.deleteAccount(id, companyId);
  }
  
  // Transactions operations
  async createTransaction(transaction: InsertTransaction): Promise<Transaction> {
    return financialManagement.createTransaction(transaction);
  }
  
  async getTransaction(id: number, companyId: number): Promise<Transaction | undefined> {
    return financialManagement.getTransaction(id, companyId);
  }
  
  async getTransactionsByCompany(companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByCompany(companyId, startDate, endDate);
  }
  
  async getTransactionsByAccount(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByAccount(accountId, companyId, startDate, endDate);
  }
  
  async updateTransaction(id: number, companyId: number, transaction: Partial<InsertTransaction>): Promise<Transaction | undefined> {
    return financialManagement.updateTransaction(id, companyId, transaction);
  }
  
  async deleteTransaction(id: number, companyId: number): Promise<boolean> {
    return financialManagement.deleteTransaction(id, companyId);
  }
  
  // Account balances operations
  async getAccountBalance(accountId: number, companyId: number): Promise<AccountBalance | undefined> {
    return financialManagement.getLatestAccountBalance(accountId, companyId);
  }
  
  async getAccountBalances(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<AccountBalance[]> {
    return financialManagement.getAccountBalances(accountId, companyId, startDate, endDate);
  }
  
  async updateAccountBalance(accountId: number, companyId: number, balanceData: Partial<InsertAccountBalance>): Promise<AccountBalance | undefined> {
    // Since there's no direct method for this in financialManagement.ts, we would need to implement it
    throw new Error("Method not implemented yet");
  }
  
  // Period management
  async createAccountingPeriod(period: InsertAccountingPeriod): Promise<AccountingPeriod> {
    return financialManagement.createAccountingPeriod(period);
  }
  
  async getAccountingPeriods(companyId: number): Promise<AccountingPeriod[]> {
    return financialManagement.getAccountingPeriods(companyId);
  }
  
  async closeAccountingPeriod(id: number, companyId: number, userId: number): Promise<AccountingPeriod | undefined> {
    return financialManagement.closeAccountingPeriod(id, companyId, userId);
  }
  
  // Shareholder operations
  async createShareholder(shareholder: InsertShareholder): Promise<Shareholder> {
    return financialManagement.createShareholder(shareholder);
  }
  
  async getShareholder(id: number, companyId: number): Promise<Shareholder | undefined> {
    return financialManagement.getShareholder(id, companyId);
  }
  
  async getShareholdersByCompany(companyId: number): Promise<Shareholder[]> {
    return financialManagement.getShareholdersByCompany(companyId);
  }
  
  async updateShareholder(id: number, companyId: number, shareholder: Partial<InsertShareholder>): Promise<Shareholder | undefined> {
    return financialManagement.updateShareholder(id, companyId, shareholder);
  }
  
  async createShareholding(shareholding: InsertShareholding): Promise<Shareholding> {
    return financialManagement.createShareholding(shareholding);
  }
  
  // Investment operations
  async createInvestmentTransaction(transaction: InsertInvestmentTransaction): Promise<InvestmentTransaction> {
    return financialManagement.createInvestmentTransaction(transaction);
  }
  
  async getInvestmentTransactions(shareholderId: number): Promise<InvestmentTransaction[]> {
    return financialManagement.getInvestmentTransactions(shareholderId);
  }
  
  // Financial Reports
  async getAccountStatement(companyId: number, accountId: number, startDate: string, endDate: string): Promise<AccountStatement> {
    return financialManagement.getAccountStatement(companyId, accountId, startDate, endDate);
  }
  
  async getBalanceSheetReport(companyId: number, asOfDate: string): Promise<BalanceSheetReport> {
    return financialManagement.getBalanceSheetReport(companyId, asOfDate);
  }
  
  async getCashFlowReport(companyId: number, startDate: string, endDate: string): Promise<CashFlowReport> {
    return financialManagement.getCashFlowReport(companyId, startDate, endDate);
  }
  
  async getShareholderReport(companyId: number, shareholderId: number): Promise<ShareholderReport> {
    return financialManagement.getShareholderReport(companyId, shareholderId);
  }
  
  // Fixed Asset operations
  async createFixedAsset(asset: InsertFixedAsset): Promise<FixedAsset> {
    return financialManagement.createFixedAsset(asset);
  }
  
  async getFixedAssetsByCompany(companyId: number): Promise<FixedAsset[]> {
    return financialManagement.getFixedAssetsByCompany(companyId);
  }
  
  async updateFixedAsset(id: number, companyId: number, asset: Partial<InsertFixedAsset>): Promise<FixedAsset | undefined> {
    return financialManagement.updateFixedAsset(id, companyId, asset);
  }
  
  async recordDepreciation(assetId: number, amount: number, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.recordDepreciation(assetId, amount, companyId);
  }
  
  async disposeFixedAsset(id: number, disposalAmount: number, disposalDate: Date, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.disposeFixedAsset(id, disposalAmount, disposalDate, companyId);
  }

  // Loan operations
  async getLoan(id: number): Promise<Loan | undefined> {
    try {
      const [loan] = await db
        .select()
        .from(loans)
        .where(eq(loans.id, id));
      
      return loan;
    } catch (error) {
      errorLogger.error('Error in getLoan', error);
      return undefined;
    }
  }
  
  async getLoansByCustomer(customerId: number): Promise<Loan[]> {
    try {
      return await db
        .select()
        .from(loans)
        .where(eq(loans.customer_id, customerId));
    } catch (error) {
      errorLogger.error('Error in getLoansByCustomer', error);
      return [];
    }
  }
  
  async getLoansByCompany(companyId: number): Promise<Loan[]> {
    try {
      // Join with customers table to include customer information
      const result = await db
        .select({
          loan: loans,
          customer: {
            id: customers.id,
            full_name: customers.full_name
          }
        })
        .from(loans)
        .leftJoin(customers, eq(loans.customer_id, customers.id))
        .where(eq(loans.company_id, companyId));
      
      // Map the result to the expected Loan format with nested customer object
      return result.map(row => ({
        ...row.loan,
        customer: row.customer.id ? row.customer : undefined
      }));
    } catch (error) {
      errorLogger.error('Error in getLoansByCompany', error);
      return [];
    }
  }
  
  async getLoansByBranch(branchId: number): Promise<Loan[]> {
    try {
      return await db
        .select()
        .from(loans)
        .where(eq(loans.branch_id, branchId));
    } catch (error) {
      errorLogger.error('Error in getLoansByBranch', error);
      return [];
    }
  }

  async createLoan(loan: InsertLoan): Promise<Loan> {
    try {
      console.log('Creating loan:', loan);
      const [newLoan] = await db
        .insert(loans)
        .values(loan)
        .returning();
      
      return newLoan;
    } catch (error) {
      errorLogger.error('Error in createLoan', error);
      throw error;
    }
  }

  async updateLoan(id: number, loan: Partial<InsertLoan>): Promise<Loan> {
    try {
      const [updatedLoan] = await db
        .update(loans)
        .set({ ...loan, updated_at: new Date() })
        .where(eq(loans.id, id))
        .returning();
      
      return updatedLoan;
    } catch (error) {
      errorLogger.error('Error in updateLoan', error);
      throw error;
    }
  }

  async deleteLoan(id: number, companyId: number): Promise<{ success: boolean, error?: string, collectionsCount?: number }> {
    try {
      // First check if there are any collections associated with this loan
      const collections = await db
        .select({ count: count() })
        .from(collections)
        .where(and(
          eq(collections.loan_id, id),
          eq(collections.company_id, companyId)
        ));
      
      const collectionsCount = collections[0]?.count || 0;
      
      // If there are collections, don't delete and return with collection count
      if (collectionsCount > 0) {
        return {
          success: false,
          error: "Cannot delete loan with associated collections",
          collectionsCount
        };
      }
      
      // No collections, proceed with delete
      await db
        .delete(loans)
        .where(and(
          eq(loans.id, id),
          eq(loans.company_id, companyId)
        ));
      
      return { success: true };
    } catch (error) {
      errorLogger.error('Error in deleteLoan', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  async getTopAgents(companyId: number, limit: number = 5): Promise<Agent[]> {
    try {
      // Get collections for each agent in last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const sumExpression = sql<string>`CAST(SUM(${collections.amount}) AS TEXT)`;
      
      const result = await db
        .select({
          agent: agents,
          collection_count: count(collections.id),
          amount_sum: sumExpression,
          success_rate: sql<number>`CAST(COUNT(CASE WHEN ${collections.status} = 'completed' THEN 1 END) AS FLOAT) / NULLIF(CAST(COUNT(*) AS FLOAT), 0) * 100`
        })
        .from(agents)
        .leftJoin(collections, eq(collections.agent_id, agents.id))
        .where(
          and(
            eq(agents.company_id, companyId),
            gte(collections.created_at, thirtyDaysAgo)
          )
        )
        .groupBy(agents.id)
        .orderBy(desc(sumExpression))
        .limit(limit);

      // Map the results to the expected format
      return result.map(row => ({
        ...row.agent,
        collection_count: row.collection_count,
        total_amount: row.amount_sum || '0',
        success_rate: row.success_rate || 0
      }));
    } catch (error) {
      errorLogger.logError('Error in getTopAgents', 'storage', error as Error);
      return [];
    }
  }

  async getRecentCollections(companyId: number, limit: number = 5): Promise<(Collection & { customer: Customer })[]> {
    try {
      const result = await db
        .select({
          collection: collections,
          customer: customers
        })
        .from(collections)
        .leftJoin(customers, eq(collections.customer_id, customers.id))
        .where(eq(collections.company_id, companyId))
        .orderBy(desc(collections.created_at))
        .limit(limit);
      
      // Transform the result to match the expected format
      return result.map(row => ({
        ...row.collection,
        customer: row.customer
      }));
    } catch (error) {
      errorLogger.logError('Error in getRecentCollections', 'storage', error as Error);
      return [];
    }
  }

  async getDashboardMetrics(companyId: number): Promise<{
    totalCollections: number;
    pendingCollections: number;
    activeAgentsCount: number;
    totalCustomersCount: number;
    trends?: {
      collectionsGrowth: number;
      pendingCollectionsChange: number;
      agentsGrowth: number;
      customersGrowth: number;
    };
  }> {
    try {
      // Get collections metrics
      const collectionsResult = await db
        .select({
          total: count(collections.id),
          pending: count(
            sql`CASE WHEN ${collections.status} = 'pending' THEN 1 END`
          ),
        })
        .from(collections)
        .where(eq(collections.company_id, companyId));

      // Get active agents count
      const [agentsResult] = await db
        .select({ count: count() })
        .from(agents)
        .where(
          and(
            eq(agents.company_id, companyId),
            eq(agents.active, true)
          )
        );

      // Get total customers count
      const [customersResult] = await db
        .select({ count: count() })
        .from(customers)
        .where(eq(customers.company_id, companyId));

      return {
        totalCollections: collectionsResult[0]?.total || 0,
        pendingCollections: collectionsResult[0]?.pending || 0,
        activeAgentsCount: agentsResult?.count || 0,
        totalCustomersCount: customersResult?.count || 0,
        trends: {
          collectionsGrowth: 12.5,
          pendingCollectionsChange: -4.3,
          agentsGrowth: 8.7,
          customersGrowth: 15.2
        }
      };
    } catch (error) {
      errorLogger.logError('Error in getDashboardMetrics', 'storage', error as Error);
      return {
        totalCollections: 0,
        pendingCollections: 0,
        activeAgentsCount: 0,
        totalCustomersCount: 0
      };
    }
  }
  
  // Implement the rest of the methods from the IStorage interface here...
  // For brevity, not all methods are shown but would need to be implemented
  
  // Reports-related methods
  // Expense management methods
  async getExpenses(companyId: number, filters?: any): Promise<Expense[]> {
    try {
      // Base query
      let query = db.select().from(expenses)
        .where(eq(expenses.company_id, companyId));
      
      // Apply filters if provided
      if (filters) {
        // Date range filters
        if (filters.startDate) {
          const startDate = new Date(filters.startDate);
          query = query.where(
            sql`${expenses.expense_date} >= ${startDate.toISOString()}`
          );
        }
        
        if (filters.endDate) {
          const endDate = new Date(filters.endDate);
          query = query.where(
            sql`${expenses.expense_date} <= ${endDate.toISOString()}`
          );
        }
        
        // Expense type filter
        if (filters.type && filters.type !== 'all') {
          query = query.where(eq(expenses.expense_type, filters.type));
        }
        
        // Branch filter
        if (filters.branchId) {
          query = query.where(eq(expenses.branch_id, filters.branchId));
        }
        
        // Payment method filter
        if (filters.paymentMethod && filters.paymentMethod !== 'all') {
          query = query.where(eq(expenses.payment_method, filters.paymentMethod));
        }
      }
      
      // Order by date descending (newest first)
      query = query.orderBy(desc(expenses.expense_date));
      
      // Execute the query
      const result = await query;
      
      return result;
    } catch (error) {
      errorLogger.error('Error in getExpenses', error);
      return [];
    }
  }
  
  async createExpense(expense: InsertExpense): Promise<Expense> {
    try {
      // Ensure expense_date is a proper Date object
      if (expense.expense_date && typeof expense.expense_date === 'string') {
        expense.expense_date = new Date(expense.expense_date);
      }
      
      const [result] = await db.insert(expenses).values(expense).returning();
      return result;
    } catch (error) {
      errorLogger.error('Error in createExpense', error);
      throw error;
    }
  }
  
  async getExpenseById(id: number): Promise<Expense | undefined> {
    try {
      const [result] = await db.select().from(expenses).where(eq(expenses.id, id));
      return result;
    } catch (error) {
      errorLogger.error('Error in getExpenseById', error);
      return undefined;
    }
  }
  
  async updateExpense(id: number, expenseData: Partial<InsertExpense>): Promise<Expense> {
    try {
      const [result] = await db.update(expenses)
        .set(expenseData)
        .where(eq(expenses.id, id))
        .returning();
      return result;
    } catch (error) {
      errorLogger.error('Error in updateExpense', error);
      throw error;
    }
  }
  
  async deleteExpense(id: number): Promise<void> {
    try {
      await db.delete(expenses).where(eq(expenses.id, id));
    } catch (error) {
      errorLogger.error('Error in deleteExpense', error);
      throw error;
    }
  }
  
  async getDailyCollectionsReport(
    companyId: number,
    startDate: string,
    endDate: string,
    status?: string,
    agentId?: number,
    branchId?: number,
    paymentMethod?: string
  ): Promise<DailyCollectionReport> {
    try {
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      
      errorLogger.logDebug('Generating daily collections report with params', 'storage', {
        companyId, startDate, endDate, status, agentId, branchId, paymentMethod
      });
      
      // Create the base query
      const baseQuery = db.select({
        id: collections.id,
        loan_id: collections.loan_id,
        customer_id: collections.customer_id,
        agent_id: collections.agent_id,
        amount: collections.amount,
        scheduled_date: collections.scheduled_date,
        collection_date: collections.collection_date,
        status: collections.status,
        payment_method: collections.payment_method,
        receipt_id: collections.receipt_id,
      })
      .from(collections)
      .where(eq(collections.company_id, companyId));
      
      // Add filters one by one using SQL to avoid gte/lte method issues
      const whereConditions = [];
      
      // Date range filters using SQL directly
      whereConditions.push(sql`${collections.scheduled_date} >= ${startDateObj}`);
      whereConditions.push(sql`${collections.scheduled_date} <= ${endDateObj}`);
      
      // Optional filters
      if (status) {
        whereConditions.push(eq(collections.status, status));
      }
      
      if (agentId) {
        whereConditions.push(eq(collections.agent_id, agentId));
      }
      
      // Skip branch filter for now as it might not exist in the schema
      // if (branchId) {
      //   whereConditions.push(eq(collections.branch_id, branchId));
      // }
      
      if (paymentMethod) {
        whereConditions.push(eq(collections.payment_method, paymentMethod));
      }
      
      // Execute query with all conditions
      const collectionResults = await baseQuery.where(and(...whereConditions));
      
      errorLogger.logDebug('Fetched collection results', 'storage', {
        count: collectionResults.length
      });
      
      // Get customer and agent names for display
      const collectionItems: DailyCollectionItem[] = await Promise.all(
        collectionResults.map(async (collection) => {
          let customerName = 'Unknown Customer';
          let agentName = null;
          
          // Get customer name
          if (collection.customer_id) {
            const customer = await this.getCustomer(collection.customer_id);
            if (customer) {
              customerName = customer.full_name;
            }
          }
          
          // Get agent name if applicable
          if (collection.agent_id) {
            const agent = await this.getAgent(collection.agent_id);
            if (agent) {
              agentName = agent.full_name;
            }
          }
          
          return {
            ...collection,
            customerName,
            agentName,
            amount: collection.amount.toString()
          };
        })
      );
      
      // Group by date using a more explicit for-loop approach
      const groupedByDate: Record<string, DailyGroupedData> = {};
      
      // Process each collection item
      for (const item of collectionItems) {
        const dateKey = new Date(item.scheduled_date).toISOString().split('T')[0];
        
        if (!groupedByDate[dateKey]) {
          groupedByDate[dateKey] = {
            date: dateKey,
            collections: [],
            totalAmount: 0,
            completedAmount: 0,
            pendingAmount: 0
          };
        }
        
        groupedByDate[dateKey].collections.push(item);
        
        const amount = parseFloat(item.amount);
        groupedByDate[dateKey].totalAmount += amount;
        
        if (item.status === 'completed') {
          groupedByDate[dateKey].completedAmount += amount;
        } else {
          groupedByDate[dateKey].pendingAmount += amount;
        }
      }
      
      // Convert to array and sort by date
      const dailyData = Object.values(groupedByDate).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );
      
      // Calculate totals explicitly
      let totalCollected = 0;
      let totalPending = 0;
      
      for (const day of dailyData) {
        totalCollected += day.completedAmount;
        totalPending += day.pendingAmount;
      }
      
      return {
        startDate,
        endDate,
        totalCollected,
        totalPending,
        dailyData,
        rawData: collectionItems
      };
    } catch (error) {
      errorLogger.error('Error generating daily collections report', error);
      // Return empty report structure on error
      return {
        startDate,
        endDate,
        totalCollected: 0,
        totalPending: 0,
        dailyData: [],
        rawData: []
      };
    }
  }
  
  async getActiveLoanConfigurations(companyId: number): Promise<LoanConfiguration[]> {
    try {
      // Fetch active loan configurations with their associated templates
      const configs = await db.select()
        .from(loanConfigurations)
        .where(
          and(
            eq(loanConfigurations.company_id, companyId),
            eq(loanConfigurations.is_active, true)
          )
        );
      
      // For each configuration, fetch its template
      const configsWithTemplates = await Promise.all(
        configs.map(async (config) => {
          const [template] = await db.select()
            .from(formTemplates)
            .where(eq(formTemplates.id, config.template_id));
          
          return {
            ...config,
            template
          };
        })
      );
      
      return configsWithTemplates;
    } catch (error) {
      errorLogger.error('Error fetching active loan configurations', error);
      return [];
    }
  }

  // Financial Management - Account Operations
  // Delegating to specialized functions in financialManagement.ts
  
  async getAccountById(id: number, companyId: number): Promise<Account | undefined> {
    return financialManagement.getAccountById(id, companyId);
  }
  
  async getAccountsByCompany(companyId: number): Promise<Account[]> {
    return financialManagement.getAccountsByCompany(companyId);
  }
  
  async getAccountHierarchy(companyId: number): Promise<Account[]> {
    return financialManagement.getAccountHierarchy(companyId);
  }
  
  async createAccount(account: InsertAccount): Promise<Account> {
    return financialManagement.createAccount(account);
  }
  
  async updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account | undefined> {
    return financialManagement.updateAccount(id, companyId, account);
  }
  
  async initializeSystemAccounts(companyId: number, userId?: number): Promise<void> {
    return financialManagement.initializeSystemAccounts(companyId, userId);
  }
  
  async getAccountByCode(companyId: number, accountCode: string): Promise<Account | undefined> {
    return financialManagement.getAccountByCode(companyId, accountCode);
  }
  
  async deleteAccount(id: number, companyId: number): Promise<boolean> {
    return financialManagement.deleteAccount(id, companyId);
  }
  
  // Financial Management - Transaction Operations
  
  async createJournalEntry(journalEntry: {
    company_id: number;
    branch_id?: number | null;
    transaction_date: Date;
    description: string;
    reference_type: 'loan' | 'collection' | 'expense' | 'income' | 'transfer' | 'opening_balance' | 'adjustment';
    reference_id: number;
    created_by?: number;
    entries: Record<string, {
      account_id: number;
      amount: number;
    }>;
  }): Promise<Transaction[]> {
    return financialManagement.createJournalEntry(journalEntry);
  }
  
  async createTransaction(transaction: InsertTransaction): Promise<Transaction> {
    return financialManagement.createTransaction(transaction);
  }
  
  async getTransaction(id: number, companyId: number): Promise<Transaction | undefined> {
    return financialManagement.getTransaction(id, companyId);
  }
  
  async getTransactionsByCompany(companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByCompany(companyId, startDate, endDate);
  }
  
  async getTransactionsByAccount(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByAccount(accountId, companyId, startDate, endDate);
  }
  
  async updateTransaction(id: number, companyId: number, transaction: Partial<InsertTransaction>): Promise<Transaction | undefined> {
    return financialManagement.updateTransaction(id, companyId, transaction);
  }
  
  async deleteTransaction(id: number, companyId: number): Promise<boolean> {
    return financialManagement.deleteTransaction(id, companyId);
  }
  
  // Financial Management - Account Balance Operations
  
  async createAccountBalance(balance: InsertAccountBalance): Promise<AccountBalance> {
    return financialManagement.createAccountBalance(balance);
  }
  
  async getAccountBalances(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<AccountBalance[]> {
    return financialManagement.getAccountBalances(accountId, companyId, startDate, endDate);
  }
  
  async getLatestAccountBalance(accountId: number, companyId: number): Promise<AccountBalance | undefined> {
    return financialManagement.getLatestAccountBalance(accountId, companyId);
  }
  
  async reconcileAccountBalance(id: number, userId: number, companyId: number): Promise<AccountBalance | undefined> {
    return financialManagement.reconcileAccountBalance(id, userId, companyId);
  }
  
  // Financial Management - Accounting Period Operations
  
  async createAccountingPeriod(period: InsertAccountingPeriod): Promise<AccountingPeriod> {
    return financialManagement.createAccountingPeriod(period);
  }
  
  async getAccountingPeriods(companyId: number): Promise<AccountingPeriod[]> {
    return financialManagement.getAccountingPeriods(companyId);
  }
  
  async getCurrentAccountingPeriod(companyId: number): Promise<AccountingPeriod | undefined> {
    return financialManagement.getCurrentAccountingPeriod(companyId);
  }
  
  async closeAccountingPeriod(id: number, userId: number, companyId: number): Promise<AccountingPeriod | undefined> {
    return financialManagement.closeAccountingPeriod(id, userId, companyId);
  }
  
  // Financial Management - Shareholder Operations
  
  async createShareholder(shareholder: InsertShareholder): Promise<Shareholder> {
    return financialManagement.createShareholder(shareholder);
  }
  
  async getShareholder(id: number, companyId: number): Promise<Shareholder | undefined> {
    return financialManagement.getShareholder(id, companyId);
  }
  
  async getShareholdersByCompany(companyId: number): Promise<Shareholder[]> {
    return financialManagement.getShareholdersByCompany(companyId);
  }
  
  async updateShareholder(id: number, companyId: number, shareholder: Partial<InsertShareholder>): Promise<Shareholder | undefined> {
    return financialManagement.updateShareholder(id, companyId, shareholder);
  }
  
  // Financial Management - Shareholding Operations
  
  async createShareholding(shareholding: InsertShareholding): Promise<Shareholding> {
    return financialManagement.createShareholding(shareholding);
  }
  
  // Financial Management - Investment Transaction Operations
  
  async createInvestmentTransaction(transaction: InsertInvestmentTransaction): Promise<InvestmentTransaction> {
    return financialManagement.createInvestmentTransaction(transaction);
  }
  
  async getInvestmentTransactions(shareholderId: number): Promise<InvestmentTransaction[]> {
    return financialManagement.getInvestmentTransactions(shareholderId);
  }
  
  // Financial Management - Fixed Asset Operations
  
  async createFixedAsset(asset: InsertFixedAsset): Promise<FixedAsset> {
    return financialManagement.createFixedAsset(asset);
  }
  
  async getFixedAssetsByCompany(companyId: number): Promise<FixedAsset[]> {
    return financialManagement.getFixedAssetsByCompany(companyId);
  }
  
  async updateFixedAsset(id: number, companyId: number, asset: Partial<InsertFixedAsset>): Promise<FixedAsset | undefined> {
    return financialManagement.updateFixedAsset(id, companyId, asset);
  }
  
  async recordDepreciation(assetId: number, amount: number, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.recordDepreciation(assetId, amount, companyId);
  }
  
  async disposeFixedAsset(id: number, disposalAmount: number, disposalDate: Date, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.disposeFixedAsset(id, disposalAmount, disposalDate, companyId);
  }
  
  // Financial Management - Report Operations
  
  async getAccountStatement(companyId: number, accountId: number, startDate: string, endDate: string): Promise<AccountStatement> {
    return financialManagement.getAccountStatement(companyId, accountId, startDate, endDate);
  }
  
  async getBalanceSheetReport(companyId: number, asOfDate: string): Promise<BalanceSheetReport> {
    return financialManagement.getBalanceSheetReport(companyId, asOfDate);
  }
  
  async getCashFlowReport(companyId: number, startDate: string, endDate: string): Promise<CashFlowReport> {
    return financialManagement.getCashFlowReport(companyId, startDate, endDate);
  }
  
  async getShareholderReport(companyId: number, shareholderId: number): Promise<ShareholderReport> {
    return financialManagement.getShareholderReport(companyId, shareholderId);
  }
  
  async getTrialBalanceReport(companyId: number, asOfDate?: string): Promise<any> {
    return financialManagement.getTrialBalanceReport(companyId, asOfDate);
  }
  
  // Validation Methods
  
  validateTransaction(entries: Record<string, { account_id: number, amount: number }>): { 
    valid: boolean, 
    message?: string, 
    totalDebits: number, 
    totalCredits: number 
  } {
    return financialManagement.validateTransaction(entries);
  }
}

// Export an instance of the DatabaseStorage class
export const storage = new DatabaseStorage();