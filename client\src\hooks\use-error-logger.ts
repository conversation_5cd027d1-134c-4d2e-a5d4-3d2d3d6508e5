import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import errorLogger from '@/lib/errorLogger';

export type ErrorHandler = (error: any, showToast?: boolean) => void;
export type WarningHandler = (message: string, details?: any, showToast?: boolean) => void;
export type InfoHandler = (message: string, details?: any) => void;

/**
 * Custom hook for logging errors and showing toast notifications
 * @param source The component or page where the hook is used
 */
export function useErrorLogger(source: string) {
  const { toast } = useToast();

  const logError: ErrorHandler = useCallback((error: any, showToast = true) => {
    let errorMessage = 'An unexpected error occurred';
    
    // Check if the first parameter is a string (a custom message) or an error object
    if (typeof error === 'string') {
      errorMessage = error;
      // Show toast notification if enabled
      if (showToast) {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }
      // Log with the message as the message and no error details
      errorLogger.error(errorMessage, source);
    } else {
      // It's an error object
      errorMessage = error?.message || 'An unexpected error occurred';
      
      // Log the error
      errorLogger.error(errorMessage, source, error);
      
      // Show toast notification if enabled
      if (showToast) {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    }
  }, [source, toast]);

  const logWarning: WarningHandler = useCallback((message: string, details?: any, showToast = false) => {
    // Log the warning
    errorLogger.warn(message, source, details);
    
    // Show toast notification if enabled
    if (showToast) {
      toast({
        title: 'Warning',
        description: message,
        variant: 'default',
      });
    }
  }, [source, toast]);

  const logInfo: InfoHandler = useCallback((message: string, details?: any) => {
    // Log the info message
    errorLogger.info(message, source, details);
  }, [source]);

  return { logError, logWarning, logInfo };
}

export default useErrorLogger;