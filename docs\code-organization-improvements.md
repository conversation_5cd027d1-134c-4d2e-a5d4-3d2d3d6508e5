# Code Organization & Standards Improvements

## Current Issues with Root Directory JavaScript Files

### 📁 **File Organization Problems**

The root directory currently contains numerous JavaScript files that should be organized into proper directories:

**Migration Scripts (should be in `server/scripts/migrations/`):**
- `update-loan-reference-codes.js`
- `run-neon-migrations.js`
- `migrate-expenses-neon.js`
- `run-company-prefix-settings-migration.js`
- `update-customer-reference-codes.js`
- `update-transaction-reference-codes.js`
- `run-migration-simple.js`
- `run-customer-reference-migration.js`
- `run-enhanced-permissions-migration.js`
- `run-loan-reference-migration.js`
- `run-partner-reference-migration.js`
- `run-transaction-reference-migration.js`

**Debug/Utility Scripts (should be in `tools/` or `scripts/`):**
- `debug_token_refresh.js`
- `debug-company-context.js`
- `check-partners-table.js`
- `test-collection-id.js`
- `test-partners-query.js`

### 🔧 **Module System Inconsistencies**

**Issue:** Mixed use of CommonJS and ES modules
- Project uses `"type": "module"` in package.json (ES modules)
- Some scripts still use CommonJS (`require/module.exports`)
- This violates the project's module standard

**Examples:**
```javascript
// ❌ CommonJS (check-partners-table.js)
const { Pool } = require('@neondatabase/serverless');

// ✅ ES Modules (update-loan-reference-codes.js)
import { Pool, neonConfig } from '@neondatabase/serverless';
```

### 🔄 **Code Duplication**

**Repeated patterns across scripts:**
1. Database connection setup
2. Environment variable loading
3. Error handling patterns
4. Neon WebSocket configuration

## Recommended Improvements

### 1. **Create Proper Directory Structure**

```
scripts/
├── migrations/
│   ├── loan-reference-codes.js
│   ├── customer-reference-codes.js
│   ├── transaction-reference-codes.js
│   └── ...
├── utils/
│   ├── database-connection.js
│   ├── env-loader.js
│   └── migration-runner.js
└── debug/
    ├── token-refresh.js
    ├── company-context.js
    └── ...

tools/
├── check-partners-table.js
├── test-collection-id.js
└── ...
```

### 2. **Standardize Module System**

Convert all scripts to ES modules:

```javascript
// ✅ Standard ES module pattern
import { Pool, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import ws from 'ws';
```

### 3. **Create Shared Utilities**

**Database Connection Utility (`scripts/utils/database-connection.js`):**
```javascript
import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';
import { loadEnvironment } from './env-loader.js';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

export function createDatabasePool() {
  loadEnvironment();

  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  return new Pool({
    connectionString: process.env.DATABASE_URL,
    max: 5
  });
}
```

**Environment Loader (`scripts/utils/env-loader.js`):**
```javascript
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

export function loadEnvironment() {
  const envPath = path.resolve('.env');
  console.log('Checking for .env file at:', envPath);
  console.log('File exists:', fs.existsSync(envPath));

  if (fs.existsSync(envPath)) {
    const envConfig = dotenv.parse(fs.readFileSync(envPath));
    for (const k in envConfig) {
      process.env[k] = envConfig[k];
    }
    console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
  }
}
```

### 4. **Script Template**

**Standard Migration Script Template:**
```javascript
#!/usr/bin/env node
/**
 * Migration Script: [Description]
 * Purpose: [What this script does]
 * Usage: node scripts/migrations/[script-name].js
 */

import { createDatabasePool } from '../utils/database-connection.js';

async function runMigration() {
  const pool = createDatabasePool();

  try {
    console.log('Starting migration: [Migration Name]');

    // Migration logic here

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the migration
runMigration();
```

## Implementation Plan

### Phase 1: Directory Structure
- [ ] Create `scripts/` directory with subdirectories
- [ ] Create `tools/` directory
- [ ] Move files to appropriate directories

### Phase 2: Module Standardization
- [ ] Convert CommonJS scripts to ES modules
- [ ] Create shared utility modules
- [ ] Update import statements

### Phase 3: Code Cleanup
- [ ] Remove duplicate code using shared utilities
- [ ] Standardize error handling patterns
- [ ] Add proper documentation headers

### Phase 4: Documentation
- [ ] Create README files for each directory
- [ ] Document script purposes and usage
- [ ] Add troubleshooting guides

## Benefits

1. **Better Organization:** Clear separation of concerns
2. **Consistency:** Uniform module system and patterns
3. **Maintainability:** Reduced code duplication
4. **Developer Experience:** Easier to find and understand scripts
5. **Standards Compliance:** Follows project's ES module standard

## Current Status: ✅ Completed

**Completed:** Code organization improvements implementation
**Date:** January 2025

### What Was Accomplished:

1. **✅ Created Proper Directory Structure:**
   - `scripts/` - Main scripts directory with subdirectories
   - `scripts/migrations/` - Database migration scripts
   - `scripts/debug/` - Debug and troubleshooting scripts
   - `scripts/utils/` - Shared utility modules
   - `tools/` - Development tools and test scripts

2. **✅ Created Shared Utilities:**
   - `scripts/utils/env-loader.js` - Environment variable management
   - `scripts/utils/database-connection.js` - Database connection utilities
   - `scripts/utils/migration-runner.js` - Standardized migration execution

3. **✅ Converted All Scripts to ES Modules:**
   - All scripts now use `import/export` syntax
   - Consistent with project's `"type": "module"` setting
   - Eliminated CommonJS/ES module conflicts

4. **✅ Reorganized Files:**
   - **Migration Scripts:** Moved to `scripts/migrations/`
   - **Debug Scripts:** Moved to `scripts/debug/`
   - **Tools:** Moved to `tools/`
   - **Removed:** All JavaScript files from root directory

5. **✅ Standardized Script Patterns:**
   - Consistent error handling using migration runner
   - Standardized logging and statistics
   - Dry-run support for safe testing
   - Proper documentation headers

6. **✅ Created Documentation:**
   - `scripts/README.md` - Scripts directory documentation
   - `tools/README.md` - Tools directory documentation
   - Usage guidelines and best practices

### Files Reorganized:

**Migration Scripts (moved to `scripts/migrations/`):**
- `update-loan-reference-codes.js` → `scripts/migrations/update-loan-reference-codes.js`
- `update-customer-reference-codes.js` → `scripts/migrations/update-customer-reference-codes.js`
- `update-transaction-reference-codes.js` → `scripts/migrations/update-transaction-reference-codes.js`
- `migrate-expenses-neon.js` → `scripts/migrations/migrate-expenses-neon.js`
- `run-neon-migrations.js` → `scripts/migrations/run-neon-migrations.js`
- `run-company-prefix-settings-migration.js` → `scripts/migrations/run-company-prefix-settings-migration.js`
- `run-customer-reference-migration.js` → `scripts/migrations/run-customer-reference-migration.js`

**Debug Scripts (moved to `scripts/debug/`):**
- `debug-company-context.js` → `scripts/debug/company-context.js`
- `debug_token_refresh.js` → `scripts/debug/token-refresh.js`
- `check-partners-table.js` → `scripts/debug/check-partners-table.js`

**Tools (moved to `tools/`):**
- `test-collection-id.js` → `tools/test-collection-id.js`
- `test-partners-query.js` → `tools/test-partners-query.js`
- `update-loan-reference-codes-client.js` → `tools/update-loan-reference-codes-client.js`
- `update-loan-reference-codes-programmatic.js` → `tools/update-loan-reference-codes-programmatic.js`

The root directory is now clean and follows proper code organization standards.
