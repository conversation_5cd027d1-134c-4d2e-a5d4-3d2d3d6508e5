import React from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { useContextData } from '@/lib/useContextData';
import { format } from 'date-fns';

interface ReportDownloadButtonProps {
  reportType: string;
  format: 'pdf' | 'csv';
  startDate?: Date;
  endDate?: Date;
  asOfDate?: Date;
  disabled?: boolean;
}

export function ReportDownloadButton({
  reportType,
  format: fileFormat,
  startDate,
  endDate,
  asOfDate,
  disabled = false,
}: ReportDownloadButtonProps) {
  const { companyId } = useContextData();

  const handleDownload = () => {
    if (!companyId) return;

    let url = `/api/companies/${companyId}/reports/${reportType}/download?format=${fileFormat}`;

    if (startDate && endDate) {
      url += `&startDate=${format(startDate, 'yyyy-MM-dd')}&endDate=${format(endDate, 'yyyy-MM-dd')}`;
    } else if (asOfDate) {
      url += `&asOfDate=${format(asOfDate, 'yyyy-MM-dd')}`;
    }

    // Open in a new tab
    window.open(url, '_blank');
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleDownload}
      disabled={disabled}
    >
      <Download className="mr-2 h-4 w-4" />
      {fileFormat.toUpperCase()}
    </Button>
  );
}
