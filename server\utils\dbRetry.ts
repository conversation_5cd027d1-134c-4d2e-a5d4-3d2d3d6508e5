/**
 * Database Retry Utility
 * 
 * This utility provides a function to retry database operations with exponential backoff
 * when rate limit errors are encountered.
 */

import errorLogger from './errorLogger';

/**
 * Executes a database operation with retry logic for rate limit errors
 * 
 * @param operation The database operation to execute
 * @param operationName Name of the operation for logging purposes
 * @param maxRetries Maximum number of retry attempts
 * @returns The result of the operation or undefined if all retries fail
 */
export async function withDbRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3
): Promise<T | undefined> {
  let retryCount = 0;
  let lastError: any = null;
  
  while (retryCount < maxRetries) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      // Check if it's a rate limit error
      const isRateLimitError = error.message && 
        (error.message.includes('rate limit') || 
         error.message.includes('exceeded the rate limit'));
      
      if (isRateLimitError && retryCount < maxRetries - 1) {
        // Exponential backoff: wait longer between each retry
        const delayMs = Math.pow(2, retryCount) * 500; // 500ms, 1000ms, 2000ms
        console.log(`Rate limit hit in ${operationName}, retrying in ${delayMs}ms (attempt ${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
        retryCount++;
      } else {
        // Not a rate limit error or we've exhausted retries
        errorLogger.error(`Error in ${operationName}`, error);
        return undefined;
      }
    }
  }
  
  errorLogger.error(`Failed ${operationName} after ${maxRetries} attempts`, lastError);
  return undefined;
}

/**
 * Executes a database operation with retry logic for rate limit errors
 * This version throws the last error if all retries fail
 * 
 * @param operation The database operation to execute
 * @param operationName Name of the operation for logging purposes
 * @param maxRetries Maximum number of retry attempts
 * @returns The result of the operation
 * @throws The last error encountered if all retries fail
 */
export async function withDbRetryOrThrow<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3
): Promise<T> {
  let retryCount = 0;
  let lastError: any = null;
  
  while (retryCount < maxRetries) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      // Check if it's a rate limit error
      const isRateLimitError = error.message && 
        (error.message.includes('rate limit') || 
         error.message.includes('exceeded the rate limit'));
      
      if (isRateLimitError && retryCount < maxRetries - 1) {
        // Exponential backoff: wait longer between each retry
        const delayMs = Math.pow(2, retryCount) * 500; // 500ms, 1000ms, 2000ms
        console.log(`Rate limit hit in ${operationName}, retrying in ${delayMs}ms (attempt ${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
        retryCount++;
      } else if (!isRateLimitError) {
        // If it's not a rate limit error, throw immediately
        errorLogger.error(`Error in ${operationName}`, error);
        throw error;
      } else {
        // We've exhausted retries for rate limit errors
        break;
      }
    }
  }
  
  errorLogger.error(`Failed ${operationName} after ${maxRetries} attempts`, lastError);
  throw lastError;
}
