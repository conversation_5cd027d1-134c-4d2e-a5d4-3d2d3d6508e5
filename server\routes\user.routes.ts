import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireRole, AuthRequest } from '../middleware/auth';
import { insertUserSchema, users } from '../../shared/schema';
import errorLogger from '../utils/errorLogger';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import { db } from '../db';
import { eq } from 'drizzle-orm';

export function registerUserRoutes(app: Express): void {
  // Get current user
  app.get('/api/users/me', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const user = await storage.getUser(req.user.id);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Get user's companies
      const userCompanies = await storage.getUserCompanies(user.id);

      // Return user data without sensitive information
      return res.json({
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        company_id: req.user.company_id,
        companies: userCompanies.map(uc => ({
          id: uc.company_id,
          name: uc.company?.name || `Company ${uc.company_id}`,
          role: uc.role,
          is_primary: uc.is_primary
        }))
      });
    } catch (error) {
      console.error('Error fetching current user:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get all users (admin only)
  app.get('/api/users', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const users = await storage.getUsers();

      // Return users without sensitive information
      const safeUsers = users.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        mobile_number: user.mobile_number,
        company_id: user.company_id
      }));

      return res.json(safeUsers);
    } catch (error) {
      errorLogger.logError('Error fetching users', 'user-routes', error as Error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get users by company
  app.get('/api/companies/:companyId/users', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = parseInt(req.params.companyId);

      // Check if user has access to this company
      if (req.user.role !== 'saas_admin' && req.user.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc => uc.company_id === companyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      const users = await storage.getUsersByCompany(companyId);

      // Return users without sensitive information
      const safeUsers = users.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        mobile_number: user.mobile_number,
        company_id: user.company_id
      }));

      return res.json(safeUsers);
    } catch (error) {
      console.error(`Error fetching users for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get user by ID
  app.get('/api/users/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);

      // Users can only view their own profile or users in their company, unless they're saas_admin
      if (req.user.role !== 'saas_admin' && req.user.id !== userId) {
        const user = await storage.getUser(userId);

        if (!user || user.company_id !== req.user.company_id) {
          return res.status(403).json({ message: 'Access denied' });
        }
      }

      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Return user without sensitive information
      return res.json({
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        mobile_number: user.mobile_number,
        company_id: user.company_id
      });
    } catch (error) {
      errorLogger.logError(`Error fetching user ${req.params.id}`, 'user-routes', error as Error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create user (admin only)
  app.post('/api/users', authMiddleware, requireRole(['saas_admin', 'owner']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = insertUserSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Company owners can only create users for their own company
      if (req.user.role === 'owner') {
        if (result.data.company_id !== req.user.company_id) {
          return res.status(403).json({ message: 'You can only create users for your own company' });
        }

        // Company owners can't create saas_admin users
        if (result.data.role === 'saas_admin') {
          return res.status(403).json({ message: 'You cannot create saas_admin users' });
        }
      }

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(result.data.username);
      if (existingUser) {
        return res.status(400).json({ message: 'Username already exists' });
      }

      // Check if email already exists in the same company
      if (result.data.company_id) {
        const existingEmailInCompany = await storage.getUserByEmailAndCompany(result.data.email, result.data.company_id);
        if (existingEmailInCompany) {
          return res.status(400).json({
            message: 'Email already exists in this company',
            error: 'This email is already registered with another user in this company. Please use a different email address.',
            field: 'email'
          });
        }
      }

      const user = await storage.createUser(result.data);

      // Create user-company association if company_id is provided
      if (user.company_id) {
        await storage.createUserCompany({
          user_id: user.id,
          company_id: user.company_id,
          is_primary: true,
          role: user.role
        });
      }

      // Return user without sensitive information
      return res.status(201).json({
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        company_id: user.company_id
      });
    } catch (error) {
      console.error('Error creating user:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update user
  app.put('/api/users/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);

      // Users can only update their own profile or users in their company if they're owner
      if (req.user.role !== 'saas_admin' && req.user.id !== userId) {
        if (req.user.role !== 'owner') {
          return res.status(403).json({ message: 'Access denied' });
        }

        const user = await storage.getUser(userId);

        if (!user || user.company_id !== req.user.company_id) {
          return res.status(403).json({ message: 'Access denied' });
        }
      }

      // Don't allow changing role or company_id unless saas_admin
      if (req.user.role !== 'saas_admin') {
        delete req.body.role;
        delete req.body.company_id;
      }

      // Don't allow owner to change user to saas_admin
      if (req.user.role === 'owner' && req.body.role === 'saas_admin') {
        return res.status(403).json({ message: 'You cannot change a user to saas_admin' });
      }

      const user = await storage.updateUser(userId, req.body);

      // Return user without sensitive information
      return res.json({
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        mobile_number: user.mobile_number,
        company_id: user.company_id
      });
    } catch (error) {
      console.error(`Error updating user ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get user companies
  app.get('/api/users/:id/companies', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);

      // Users can only view their own companies or if they're saas_admin
      if (req.user.role !== 'saas_admin' && req.user.id !== userId) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const userCompanies = await storage.getUserCompanies(userId);

      return res.json(userCompanies.map(uc => ({
        id: uc.company_id,
        name: uc.company?.name || `Company ${uc.company_id}`,
        role: uc.role,
        is_primary: uc.is_primary
      })));
    } catch (error) {
      console.error(`Error fetching companies for user ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Change password
  app.post('/api/users/:id/change-password', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);
      const { currentPassword, newPassword } = req.body;

      // Users can only change their own password
      if (req.user.id !== userId && req.user.role !== 'saas_admin') {
        return res.status(403).json({ message: 'Access denied' });
      }

      // Validate input
      if (!newPassword || newPassword.length < 8) {
        return res.status(400).json({ message: 'New password must be at least 8 characters long' });
      }

      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // If not saas_admin, verify current password
      if (req.user.role !== 'saas_admin') {
        const isPasswordValid = await bcrypt.compare(currentPassword, user.password);

        if (!isPasswordValid) {
          return res.status(401).json({ message: 'Current password is incorrect' });
        }
      }

      await storage.updateUserPassword(userId, newPassword);

      return res.json({ message: 'Password updated successfully' });
    } catch (error) {
      console.error(`Error changing password for user ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Admin password reset
  app.post('/api/users/:id/reset-password', authMiddleware, requireRole(['saas_admin', 'owner']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);

      // Verify user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // If user is owner, verify they can only reset passwords for users in their company
      if (req.user.role === 'owner' && user.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'Access denied: You can only reset passwords for users in your company' });
      }

      // Don't allow owner to reset saas_admin password
      if (req.user.role === 'owner' && user.role === 'saas_admin') {
        return res.status(403).json({ message: 'You cannot reset a saas_admin password' });
      }

      // Generate a secure temporary password
      const tempPassword = crypto.randomBytes(8).toString('hex').toUpperCase();

      // Hash the temporary password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(tempPassword, salt);

      // Update user's password and set force password change flag
      await db
        .update(users)
        .set({
          password: hashedPassword,
          password_updated_at: new Date(),
          // Note: You might want to add a force_password_change field to the schema
        })
        .where(eq(users.id, userId));

      // Log the password reset action for audit trail
      errorLogger.logError(
        `Password reset by admin for user ${user.email}`,
        'admin-password-reset',
        new Error(`Admin ${req.user.email} reset password for user ${user.email}`)
      );

      return res.json({
        message: 'Password has been reset successfully',
        temporaryPassword: tempPassword,
        userEmail: user.email
      });
    } catch (error) {
      console.error(`Error resetting password for user ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
