import { Express, Response } from 'express';
import { storage } from '../../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../../middleware/auth';
import { insertTransactionSchema } from '@shared/schema';
import { ZodError } from 'zod';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

export function registerTransactionRoutes(app: Express): void {
  // Get all transactions for a company
  app.get('/api/companies/:companyId/transactions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Parse query parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const startDate = req.query.start_date as string;
      const endDate = req.query.end_date as string;
      const accountType = req.query.account_type as string;
      const transactionType = req.query.transaction_type as string;
      const referenceType = req.query.reference_type as string;
      const searchTerm = req.query.search as string;

      const options = {
        page,
        limit,
        startDate,
        endDate,
        accountType,
        transactionType,
        referenceType,
        searchTerm
      };

      const result = await storage.getTransactionsByCompany(companyId, options);

      return res.json({
        transactions: result.transactions,
        pagination: {
          page,
          limit,
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / limit)
        }
      });
    } catch (error) {
      console.error(`Error fetching transactions for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get transactions by account
  app.get('/api/companies/:companyId/accounts/:accountId/transactions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accountId = parseInt(req.params.accountId);
      const startDate = req.query.start_date as string;
      const endDate = req.query.end_date as string;

      const transactions = await storage.getTransactionsByAccount(accountId, companyId, startDate, endDate);
      return res.json(transactions);
    } catch (error) {
      console.error('Error fetching account transactions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Transaction summary route
  app.get('/api/companies/:companyId/transactions/summary', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Parse query parameters
      const startDate = req.query.start_date as string;
      const endDate = req.query.end_date as string;

      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      const summary = await storage.getTransactionSummary(companyId, { startDate, endDate });
      return res.json(summary);
    } catch (error) {
      console.error(`Error fetching transaction summary for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
