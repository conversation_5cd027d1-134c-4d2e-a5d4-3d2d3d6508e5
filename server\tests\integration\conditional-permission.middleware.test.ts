import { describe, it, expect, beforeEach, vi } from 'vitest';
import express from 'express';
import request from 'supertest';
import { testUsers, mockDatabase } from './setup';

// Mock the database
const mockDb = mockDatabase();
vi.mock('../../db', () => ({
  db: mockDb
}));

// Mock the enhanced permission service
const mockEnhancedPermissionService = {
  checkPermissionWithConditions: vi.fn()
};

vi.mock('../../services/enhancedPermissionService', () => ({
  EnhancedPermissionService: vi.fn().mockImplementation(() => mockEnhancedPermissionService)
}));

// Mock the conditional permission service
const mockConditionalPermissionService = {
  evaluatePermissionConditions: vi.fn()
};

vi.mock('../../services/conditionalPermissionService', () => ({
  conditionalPermissionService: mockConditionalPermissionService
}));

describe('Conditional Permission Middleware Integration Tests', () => {
  let app: express.Express;

  beforeEach(async () => {
    // Create a fresh Express app for each test
    app = express();
    app.use(express.json());

    // Mock authentication middleware
    app.use((req: any, res, next) => {
      req.user = testUsers.loanOfficer; // Default to loan officer
      next();
    });

    // Import conditional permission middleware
    const { requireConditionalPermission } = await import('../../middleware/conditionalPermission');

    // Create test routes with conditional permission middleware
    app.post('/test/loan-create', 
      requireConditionalPermission({
        permissionCode: 'loan_create_basic',
        amountField: 'amount',
        extractContext: (req) => ({
          amount: req.body.amount,
          deviceInfo: { type: 'desktop', trusted: true },
          sessionInfo: { 
            age: 1800, 
            lastActivity: new Date(Date.now() - 300000), // 5 minutes ago
            mfaVerified: true 
          },
          location: { country: 'US', ipAddress: '*************' }
        })
      }),
      (req, res) => {
        res.json({ message: 'Loan creation successful', amount: req.body.amount });
      }
    );

    app.post('/test/loan-approve',
      requireConditionalPermission({
        permissionCode: 'loan_approve_tier2',
        amountField: 'amount',
        onApprovalRequired: (req, res, approverRoles) => {
          res.status(202).json({
            message: 'Approval workflow initiated',
            requiresApproval: true,
            approverRoles,
            workflowId: 'WF-' + Date.now()
          });
        }
      }),
      (req, res) => {
        res.json({ message: 'Loan approved successfully', amount: req.body.amount });
      }
    );
  });

  describe('Successful Permission Checks', () => {
    it('should allow access when all conditions pass', async () => {
      // Mock permission service to allow access
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: true,
        reason: 'All conditions satisfied'
      });

      const loanData = {
        amount: 5000,
        customer_id: 1,
        term: 12
      };

      const response = await request(app)
        .post('/test/loan-create')
        .send(loanData)
        .expect(200);

      expect(response.body).toMatchObject({
        message: 'Loan creation successful',
        amount: 5000
      });

      // Verify the permission service was called with correct context
      expect(mockEnhancedPermissionService.checkPermissionWithConditions).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUsers.loanOfficer.id,
          permissionCode: 'loan_create_basic',
          amount: 5000,
          deviceInfo: expect.objectContaining({ type: 'desktop', trusted: true }),
          sessionInfo: expect.objectContaining({ mfaVerified: true }),
          location: expect.objectContaining({ country: 'US' })
        })
      );
    });

    it('should extract context from request correctly', async () => {
      // Mock permission service to allow access
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: true
      });

      const loanData = {
        amount: 15000,
        customer_id: 2
      };

      await request(app)
        .post('/test/loan-create')
        .send(loanData)
        .expect(200);

      // Verify context extraction
      const callArgs = mockEnhancedPermissionService.checkPermissionWithConditions.mock.calls[0][0];
      expect(callArgs).toMatchObject({
        userId: testUsers.loanOfficer.id,
        permissionCode: 'loan_create_basic',
        amount: 15000,
        timestamp: expect.any(Date),
        ipAddress: expect.any(String),
        userAgent: expect.any(String)
      });
    });
  });

  describe('Permission Denials', () => {
    it('should return 403 when conditions fail without approval option', async () => {
      // Mock permission service to deny access
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: false,
        reason: 'Access outside allowed hours',
        requiresApproval: false
      });

      const loanData = {
        amount: 5000,
        customer_id: 1
      };

      const response = await request(app)
        .post('/test/loan-create')
        .send(loanData)
        .expect(403);

      expect(response.body).toMatchObject({
        message: 'Access denied',
        reason: 'Access outside allowed hours',
        required_permission: 'loan_create_basic'
      });
    });

    it('should return 401 for unauthenticated requests', async () => {
      // Override authentication middleware to simulate no user
      app.use((req: any, res, next) => {
        req.user = null;
        next();
      });

      const loanData = {
        amount: 5000,
        customer_id: 1
      };

      const response = await request(app)
        .post('/test/loan-create')
        .send(loanData)
        .expect(401);

      expect(response.body).toHaveProperty('message', 'Authentication required');
    });
  });

  describe('Approval Workflows', () => {
    it('should trigger approval workflow when conditions require approval', async () => {
      // Mock permission service to require approval
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: false,
        requiresApproval: true,
        approverRoles: ['manager', 'director'],
        reason: 'Amount exceeds user limit'
      });

      const loanData = {
        amount: 50000,
        customer_id: 1
      };

      const response = await request(app)
        .post('/test/loan-approve')
        .send(loanData)
        .expect(202);

      expect(response.body).toMatchObject({
        message: 'Approval workflow initiated',
        requiresApproval: true,
        approverRoles: ['manager', 'director'],
        workflowId: expect.stringMatching(/^WF-\d+$/)
      });
    });

    it('should use default approval response when no custom handler provided', async () => {
      // Mock permission service to require approval
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: false,
        requiresApproval: true,
        approverRoles: ['manager'],
        reason: 'Amount exceeds threshold'
      });

      const loanData = {
        amount: 25000,
        customer_id: 1
      };

      const response = await request(app)
        .post('/test/loan-create') // This route doesn't have custom approval handler
        .send(loanData)
        .expect(202);

      expect(response.body).toMatchObject({
        message: 'Approval required for this operation',
        requiresApproval: true,
        approverRoles: ['manager'],
        reason: 'Amount exceeds threshold'
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle permission service errors gracefully', async () => {
      // Mock permission service to throw error
      mockEnhancedPermissionService.checkPermissionWithConditions.mockRejectedValueOnce(
        new Error('Permission service unavailable')
      );

      const loanData = {
        amount: 5000,
        customer_id: 1
      };

      const response = await request(app)
        .post('/test/loan-create')
        .send(loanData)
        .expect(500);

      expect(response.body).toHaveProperty('message', 'Permission evaluation failed');
    });

    it('should handle malformed request data', async () => {
      // Mock permission service to allow access
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: true
      });

      // Send malformed JSON
      const response = await request(app)
        .post('/test/loan-create')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);

      // Express should handle the JSON parsing error
      expect(response.body).toBeDefined();
    });
  });

  describe('Context Extraction', () => {
    it('should extract IP address from various headers', async () => {
      // Mock permission service to allow access
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: true
      });

      await request(app)
        .post('/test/loan-create')
        .set('X-Forwarded-For', '***********, ***********')
        .set('X-Real-IP', '***********')
        .send({ amount: 5000 })
        .expect(200);

      const callArgs = mockEnhancedPermissionService.checkPermissionWithConditions.mock.calls[0][0];
      expect(callArgs.ipAddress).toBeDefined();
    });

    it('should extract user agent information', async () => {
      // Mock permission service to allow access
      mockEnhancedPermissionService.checkPermissionWithConditions.mockResolvedValueOnce({
        allowed: true
      });

      await request(app)
        .post('/test/loan-create')
        .set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        .send({ amount: 5000 })
        .expect(200);

      const callArgs = mockEnhancedPermissionService.checkPermissionWithConditions.mock.calls[0][0];
      expect(callArgs.userAgent).toContain('Mozilla/5.0');
    });
  });
});
