import React, { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Loader2, Save, ArrowLeft, Trash2 } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';

// Define types
interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  company_id: number;
  permissions: Permission[];
}

interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
}

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  source: string;
  assignment_id: number;
}

interface Group {
  id: number;
  name: string;
  description: string;
}

export default function RoleDetail() {
  const [match, params] = useRoute('/user-management/roles/:id');
  const roleId = parseInt(params?.id || '0');
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('details');

  // Role form state
  const [roleForm, setRoleForm] = useState({
    name: '',
    description: '',
    permissions: [] as number[]
  });

  // Fetch role details
  const { data: role, isLoading: isLoadingRole } = useQuery({
    queryKey: [`/api/roles/${roleId}`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/roles/${roleId}`);
      return res.json();
    },
    enabled: roleId > 0
  });

  // Fetch all permissions
  const { data: allPermissions, isLoading: isLoadingPermissions } = useQuery({
    queryKey: ['/api/permissions'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/permissions');
      return res.json();
    }
  });

  // Fetch users with this role
  const { data: users, isLoading: isLoadingUsers } = useQuery({
    queryKey: [`/api/roles/${roleId}/users`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/roles/${roleId}/users`);
      return res.json();
    },
    enabled: roleId > 0
  });

  // Fetch groups with this role
  const { data: groups, isLoading: isLoadingGroups } = useQuery({
    queryKey: [`/api/roles/${roleId}/groups`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/roles/${roleId}/groups`);
      return res.json();
    },
    enabled: roleId > 0
  });

  // Update role mutation
  const updateRoleMutation = useMutation({
    mutationFn: async (data: typeof roleForm) => {
      const res = await apiRequest('PUT', `/api/roles/${roleId}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/roles/${roleId}`] });
      toast({
        title: 'Role updated',
        description: 'The role has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating role',
        description: error.message || 'There was an error updating the role.',
        variant: 'destructive',
      });
    }
  });

  // Delete role mutation
  const deleteRoleMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest('DELETE', `/api/roles/${roleId}`);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: 'Role deleted',
        description: 'The role has been deleted successfully.',
      });
      navigate('/user-management');
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting role',
        description: error.message || 'There was an error deleting the role.',
        variant: 'destructive',
      });
    }
  });

  // Remove user from role mutation
  const removeUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const res = await apiRequest('DELETE', `/api/users/${userId}/roles/${roleId}`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/roles/${roleId}/users`] });
      toast({
        title: 'User removed',
        description: 'The user has been removed from this role.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error removing user',
        description: error.message || 'There was an error removing the user from this role.',
        variant: 'destructive',
      });
    }
  });

  // Remove group from role mutation
  const removeGroupMutation = useMutation({
    mutationFn: async (groupId: number) => {
      const res = await apiRequest('DELETE', `/api/groups/${groupId}/roles/${roleId}`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/roles/${roleId}/groups`] });
      toast({
        title: 'Group removed',
        description: 'The group has been removed from this role.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error removing group',
        description: error.message || 'There was an error removing the group from this role.',
        variant: 'destructive',
      });
    }
  });

  // Update form when role data is loaded
  useEffect(() => {
    if (role) {
      setRoleForm({
        name: role.name,
        description: role.description || '',
        permissions: role.permissions.map((p: Permission) => p.id)
      });
    }
  }, [role]);

  // Handle form changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRoleForm(prev => ({ ...prev, [name]: value }));
  };

  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    setRoleForm(prev => {
      if (checked) {
        return { ...prev, permissions: [...prev.permissions, permissionId] };
      } else {
        return { ...prev, permissions: prev.permissions.filter(id => id !== permissionId) };
      }
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateRoleMutation.mutate(roleForm);
  };

  // Define table columns
  const userColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'full_name',
      header: 'Name',
    },
    {
      accessorKey: 'username',
      header: 'Username',
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'source',
      header: 'Assignment Type',
      cell: ({ row }) => (
        <Badge variant={row.original.source === 'direct' ? 'default' : 'outline'}>
          {row.original.source === 'direct' ? 'Direct' : 'Via Group'}
        </Badge>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        row.original.source === 'direct' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeUserMutation.mutate(row.original.id)}
            disabled={removeUserMutation.isPending}
          >
            {removeUserMutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Remove'}
          </Button>
        )
      ),
    },
  ];

  const groupColumns: ColumnDef<Group>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => removeGroupMutation.mutate(row.original.id)}
          disabled={removeGroupMutation.isPending}
        >
          {removeGroupMutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Remove'}
        </Button>
      ),
    },
  ];

  // Group permissions by category
  const groupedPermissions = allPermissions?.reduce((acc: Record<string, Permission[]>, permission: Permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {});

  if (isLoadingRole) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!role && roleId > 0) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <h2 className="text-2xl font-bold mb-2">Role not found</h2>
          <p className="text-muted-foreground mb-4">The role you're looking for doesn't exist or you don't have permission to view it.</p>
          <Button onClick={() => navigate('/user-management')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to User Management
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => navigate('/user-management')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">{role?.name}</h1>
          {role?.is_system && <Badge>System Role</Badge>}
        </div>
        <div className="flex items-center gap-2">
          {!role?.is_system && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Role
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the role and remove all associated permissions from users and groups.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => deleteRoleMutation.mutate()}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {deleteRoleMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Deleting...
                      </>
                    ) : (
                      "Delete"
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="groups">Groups</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle>Role Details</CardTitle>
                <CardDescription>View and edit role details and permissions.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Role Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={roleForm.name}
                      onChange={handleInputChange}
                      disabled={role?.is_system}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      name="description"
                      value={roleForm.description}
                      onChange={handleInputChange}
                      disabled={role?.is_system}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Permissions</Label>
                  {isLoadingPermissions ? (
                    <div className="flex justify-center items-center h-40">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <div className="border rounded-md p-4 h-[400px] overflow-y-auto">
                      {groupedPermissions && Object.entries(groupedPermissions).map(([category, permissions]) => (
                        <div key={category} className="mb-6">
                          <h3 className="text-sm font-medium mb-2">{category}</h3>
                          <div className="grid grid-cols-2 gap-2">
                            {permissions.map((permission: Permission) => (
                              <div key={permission.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`permission-${permission.id}`}
                                  checked={roleForm.permissions.includes(permission.id)}
                                  onCheckedChange={(checked) => handlePermissionChange(permission.id, !!checked)}
                                  disabled={role?.is_system}
                                />
                                <label
                                  htmlFor={`permission-${permission.id}`}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  {permission.name}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
              {!role?.is_system && (
                <CardFooter>
                  <Button type="submit" disabled={updateRoleMutation.isPending}>
                    {updateRoleMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </CardFooter>
              )}
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>Users with this Role</CardTitle>
              <CardDescription>Manage users assigned to this role.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingUsers ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <DataTable columns={userColumns} data={users || []} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="groups">
          <Card>
            <CardHeader>
              <CardTitle>Groups with this Role</CardTitle>
              <CardDescription>Manage groups assigned to this role.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingGroups ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <DataTable columns={groupColumns} data={groups || []} />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
