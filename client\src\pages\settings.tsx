import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/lib/auth";
import { Loader2, UserIcon, Shield, Building, BellIcon, LockIcon, RefreshCcw, Building2, Plus, Pencil, Trash, Settings as SettingsIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { CompanyManagement } from "@/components/company/CompanyManagement";
import { SystemSettings } from "@/components/settings/SystemSettings";
import NotificationSettings from "@/components/settings/NotificationSettings";
import { SecuritySettings } from "@/components/settings/SecuritySettings";
import BranchManager from "@/components/branch/BranchManager";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogFooter,
  DialogHeader,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";

export default function Settings() {
  const { getCurrentUser, isAuthenticated } = useAuth();
  const user = getCurrentUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  // Fetch the latest user data
  const {
    data: userData,
    isLoading: isUserDataLoading,
    refetch: refetchUserData
  } = useQuery({
    queryKey: [`/api/users/${user?.id}`],
    enabled: !!user?.id && isAuthenticated()
  });

  // Fetch company data
  const {
    data: companyData,
    isLoading: isCompanyLoading,
    refetch: refetchCompanyData
  } = useQuery({
    queryKey: [`/api/companies/${user?.company_id}`],
    enabled: !!user?.company_id && (user?.role === 'owner' || user?.role === 'saas_admin')
  });

  // Profile form state
  const [profileForm, setProfileForm] = useState({
    fullName: user?.full_name || "",
    email: user?.email || "",
    username: user?.username || "",
  });

  // Update form when user data is loaded
  useEffect(() => {
    if (userData) {
      setProfileForm({
        fullName: userData.full_name || "",
        email: userData.email || "",
        username: userData.username || "",
      });
    }
  }, [userData]);

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Company form state
  const [companyForm, setCompanyForm] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    website: "",
  });

  // Update company form when company data is loaded
  useEffect(() => {
    if (companyData) {
      setCompanyForm({
        name: companyData.name || "",
        email: companyData.email || "",
        phone: companyData.phone || "",
        address: companyData.address || "",
        website: companyData.website || "",
      });
    }
  }, [companyData]);

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileForm((prev) => ({ ...prev, [name]: value }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleCompanyChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCompanyForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleCompanySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Use apiRequest from queryClient for consistent authentication handling
      const response = await apiRequest(
        'PATCH',
        `/api/companies/${user?.company_id}`,
        companyForm
      );

      const updatedCompany = await response.json();

      // Update any cached user data if it contains company information
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${user?.company_id}`] });

      toast({
        title: "Company information updated",
        description: "Your company information has been updated successfully.",
      });

    } catch (error) {
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "An error occurred while updating company information",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await apiRequest(
        'PATCH',
        `/api/users/${user?.id}`,
        {
          full_name: profileForm.fullName,
          email: profileForm.email,
          username: profileForm.username
        }
      );

      queryClient.invalidateQueries({ queryKey: [`/api/users/${user?.id}`] });

      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      });

    } catch (error) {
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "An error occurred while updating your profile",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation do not match.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await apiRequest(
        'POST',
        `/api/users/${user?.id}/change-password`,
        {
          current_password: passwordForm.currentPassword,
          new_password: passwordForm.newPassword
        }
      );

      toast({
        title: "Password changed",
        description: "Your password has been changed successfully. You will be logged out for security reasons.",
      });

      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      // Log out the user after 2 seconds to allow them to see the success message
      setTimeout(() => {
        // Clear auth token
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');

        // Redirect to login page
        window.location.href = '/login';
      }, 2000);

    } catch (error) {
      toast({
        title: "Password change failed",
        description: error instanceof Error ? error.message : "An error occurred while changing your password",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Account Settings</h1>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <UserIcon className="h-4 w-4" />
            <span>Profile</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <LockIcon className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <BellIcon className="h-4 w-4" />
            <span>Notifications</span>
          </TabsTrigger>
          {(user?.role === 'owner' || user?.role === 'saas_admin') && (
            <TabsTrigger value="company" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              <span>Company</span>
            </TabsTrigger>
          )}
          {(user?.role === 'owner' || user?.role === 'saas_admin') && (
            <TabsTrigger value="branches" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <span>Branches</span>
            </TabsTrigger>
          )}
          {(user?.role === 'owner' || user?.role === 'saas_admin') && (
            <TabsTrigger value="system" className="flex items-center gap-2">
              <SettingsIcon className="h-4 w-4" />
              <span>System</span>
            </TabsTrigger>
          )}
          {user?.role === 'saas_admin' && (
            <TabsTrigger value="roles" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>User Roles</span>
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information and contact details
              </CardDescription>
            </CardHeader>
            <form onSubmit={saveProfile}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={profileForm.fullName}
                    onChange={handleProfileChange}
                    placeholder="Your full name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={profileForm.email}
                    onChange={handleProfileChange}
                    placeholder="Your email address"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    name="username"
                    value={profileForm.username}
                    onChange={handleProfileChange}
                    placeholder="Your username"
                  />
                </div>

                {user?.role && (
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Input
                      id="role"
                      value={user.role.replace('_', ' ')}
                      readOnly
                      disabled
                      className="capitalize bg-gray-100"
                    />
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <SecuritySettings userId={user?.id} />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettings />
        </TabsContent>

        {(user?.role === 'owner' || user?.role === 'saas_admin') && (
          <TabsContent value="company">
            <CompanyManagement />
          </TabsContent>
        )}

        {(user?.role === 'owner' || user?.role === 'saas_admin') && (
          <TabsContent value="branches">
            <BranchManager />
          </TabsContent>
        )}

        {(user?.role === 'owner' || user?.role === 'saas_admin') && (
          <TabsContent value="system">
            <SystemSettings />
          </TabsContent>
        )}

        {user?.role === 'saas_admin' && (
          <TabsContent value="roles">
            <Card>
              <CardHeader>
                <CardTitle>User Roles Management</CardTitle>
                <CardDescription>
                  Manage permissions and access levels for different user roles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-500">
                  This feature is coming soon. You will be able to manage user roles and permissions here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}

