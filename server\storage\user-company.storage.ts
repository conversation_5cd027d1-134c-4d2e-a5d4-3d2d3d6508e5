import { db } from '../db';
import { eq, and } from 'drizzle-orm';
import { userCompanies, companies } from '@shared/schema';
import { UserCompany, InsertUserCompany, Company } from '@shared/schema';
import errorLogger from '../utils/errorLogger';
import { IUserCompanyStorage } from './interfaces';

export class UserCompanyStorage implements IUserCompanyStorage {
  async getUserCompanies(userId: number): Promise<(UserCompany & { company: Company })[]> {
    try {
      const userCompanyRecords = await db.select({
        ...userCompanies,
        company: companies
      })
        .from(userCompanies)
        .leftJoin(companies, eq(userCompanies.company_id, companies.id))
        .where(eq(userCompanies.user_id, userId));

      return userCompanyRecords;
    } catch (error) {
      errorLogger.logError(`Error fetching user companies for user id=${userId}`, 'user-companies-fetch', error as Error);
      return [];
    }
  }

  async getUserCompanyByIds(userId: number, companyId: number): Promise<UserCompany | undefined> {
    try {
      const [userCompany] = await db.select()
        .from(userCompanies)
        .where(
          and(
            eq(userCompanies.user_id, userId),
            eq(userCompanies.company_id, companyId)
          )
        );
      return userCompany;
    } catch (error) {
      errorLogger.logError(`Error fetching user company for user id=${userId} and company id=${companyId}`, 'user-company-fetch', error as Error);
      return undefined;
    }
  }

  async createUserCompany(userCompanyData: InsertUserCompany): Promise<UserCompany> {
    try {
      // If this is set as primary, unset any existing primary for this user
      if (userCompanyData.is_primary) {
        await db.update(userCompanies)
          .set({ is_primary: false })
          .where(eq(userCompanies.user_id, userCompanyData.user_id));
      }

      const [userCompany] = await db.insert(userCompanies)
        .values(userCompanyData)
        .returning();

      return userCompany;
    } catch (error) {
      errorLogger.logError(`Error creating user company association`, 'user-company-create', error as Error);
      throw error;
    }
  }

  async updateUserCompany(id: number, userCompanyData: Partial<InsertUserCompany>): Promise<UserCompany> {
    try {
      // If this is set as primary, unset any existing primary for this user
      if (userCompanyData.is_primary) {
        const [userCompany] = await db.select()
          .from(userCompanies)
          .where(eq(userCompanies.id, id));

        if (userCompany) {
          await db.update(userCompanies)
            .set({ is_primary: false })
            .where(
              and(
                eq(userCompanies.user_id, userCompany.user_id),
                eq(userCompanies.is_primary, true)
              )
            );
        }
      }

      const [updatedUserCompany] = await db.update(userCompanies)
        .set(userCompanyData)
        .where(eq(userCompanies.id, id))
        .returning();

      return updatedUserCompany;
    } catch (error) {
      errorLogger.logError(`Error updating user company id=${id}`, 'user-company-update', error as Error);
      throw error;
    }
  }

  async deleteUserCompany(id: number): Promise<void> {
    try {
      await db.delete(userCompanies)
        .where(eq(userCompanies.id, id));
    } catch (error) {
      errorLogger.logError(`Error deleting user company id=${id}`, 'user-company-delete', error as Error);
      throw error;
    }
  }

  async setUserCompanyAsPrimary(id: number, userId: number): Promise<UserCompany> {
    try {
      // Unset any existing primary for this user
      await db.update(userCompanies)
        .set({ is_primary: false })
        .where(eq(userCompanies.user_id, userId));

      // Set the specified user company as primary
      const [updatedUserCompany] = await db.update(userCompanies)
        .set({ is_primary: true })
        .where(eq(userCompanies.id, id))
        .returning();

      return updatedUserCompany;
    } catch (error) {
      errorLogger.logError(`Error setting user company id=${id} as primary`, 'user-company-set-primary', error as Error);
      throw error;
    }
  }

  async updateUserCompanyPrimary(id: number, isPrimary: boolean): Promise<UserCompany | undefined> {
    try {
      // Get the user company to update
      const [userCompany] = await db.select()
        .from(userCompanies)
        .where(eq(userCompanies.id, id));

      if (!userCompany) {
        return undefined;
      }

      // If setting as primary, unset any existing primary for this user
      if (isPrimary) {
        await db.update(userCompanies)
          .set({ is_primary: false })
          .where(
            and(
              eq(userCompanies.user_id, userCompany.user_id),
              eq(userCompanies.is_primary, true)
            )
          );
      }

      // Update the specified user company
      const [updatedUserCompany] = await db.update(userCompanies)
        .set({ is_primary: isPrimary })
        .where(eq(userCompanies.id, id))
        .returning();

      return updatedUserCompany;
    } catch (error) {
      errorLogger.logError(`Error updating user company id=${id} primary status`, 'user-company-update-primary', error as Error);
      return undefined;
    }
  }
}
