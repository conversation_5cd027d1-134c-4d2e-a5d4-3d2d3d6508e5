# Role Assignment Restrictions and Business Logic

## Overview

This document describes the comprehensive role assignment restrictions and business logic implemented in the user role management system. These features ensure proper security, data integrity, and user experience when managing role assignments across the application.

## Feature Overview

### 1. Owner Role Exclusivity
**Rule**: The Owner role cannot be combined with any other roles (system or custom).

**Rationale**: Owner roles typically have supreme administrative privileges and should not be diluted or confused by having additional role assignments. This ensures clear accountability and prevents privilege escalation conflicts.

**Behavior**:
- When Owner role is selected, all other roles are automatically deselected
- When Owner role is selected, all other role checkboxes become disabled
- Attempting to assign Owner + other roles via API returns validation error
- Clear warning message explains the exclusivity requirement

### 2. Self-Role Modification Prevention
**Rule**: Users cannot modify their own role assignments.

**Rationale**: Prevents users from escalating their own privileges or accidentally removing their access. Maintains proper separation of duties and requires administrative oversight for role changes.

**Behavior**:
- When a user accesses their own role management page, all controls are disabled
- Save button shows "Read-Only Mode" with lock icon
- Clear warning message explains the restriction
- API blocks self-modification attempts with 403 Forbidden response

### 3. Owner Role Protection
**Rule**: Owner role assignments cannot be modified by non-privileged users.

**Rationale**: Protects the highest privilege level from unauthorized changes. Only system administrators or the Owner themselves should be able to modify Owner role assignments.

**Behavior**:
- Non-privileged users cannot modify Owner role assignments
- Owner checkbox is disabled and shows as "Protected" for unauthorized users
- Clear warning message explains the protection
- API enforces permission checks before allowing Owner role modifications

## Technical Implementation Details

### Backend API Changes

#### File: `server/routes/user-role.routes.ts`

**New Validation Logic in PUT `/api/users/:id/roles` endpoint**:

```typescript
// Self-Role Modification Prevention
if (req.user.id === userId) {
  return res.status(403).json({ 
    message: 'You cannot modify your own role assignments',
    code: 'SELF_ROLE_MODIFICATION_DENIED'
  });
}

// Owner Role Protection
const isTargetUserOwner = user.role === 'owner';
const hasOwnerCustomRole = targetUserRoles.some(ur => ur.role?.name === 'Owner');

if ((isTargetUserOwner || hasOwnerCustomRole) && 
    req.user.role !== 'saas_admin' && req.user.role !== 'owner') {
  return res.status(403).json({ 
    message: 'Owner role assignments cannot be modified by other users',
    code: 'OWNER_ROLE_PROTECTION'
  });
}

// Owner Role Exclusivity
const ownerRole = roleDetails.find(role => role.name === 'Owner');
if (ownerRole && roleIds.length > 1) {
  return res.status(400).json({ 
    message: 'Owner role cannot be combined with other roles. Owner role must be assigned exclusively.',
    code: 'OWNER_ROLE_EXCLUSIVITY'
  });
}
```

**Enhanced Role Retrieval with Legacy Support**:

```typescript
// Legacy enum role mapping
const legacyRoles = [];
if (user.role) {
  let roleName = '';
  switch (user.role) {
    case 'owner': roleName = 'Owner'; break;
    case 'employee': roleName = 'Employee'; break;
    case 'collection_agent': roleName = 'Collection Agent'; break;
    case 'system_admin': roleName = 'System Administrator'; break;
  }
  
  if (roleName) {
    const [systemRole] = await db
      .select()
      .from(customRoles)
      .where(and(
        eq(customRoles.name, roleName),
        eq(customRoles.is_system, true)
      ));
    
    if (systemRole && !directRoles.some(r => r.role.id === systemRole.id)) {
      legacyRoles.push({
        assignment: { id: null, user_id: userId, role_id: systemRole.id, created_at: null },
        role: systemRole
      });
    }
  }
}
```

### Frontend Component Changes

#### File: `client/src/pages/user-management/users/[id]/roles.tsx`

**New Business Logic State**:

```typescript
const isSelfEditing = currentUser?.id === userId;
const isCurrentUserOwner = currentUser?.role === 'owner' || currentUser?.role === 'saas_admin';
```

**Helper Functions**:

```typescript
const isTargetUserOwner = (targetUser: User | undefined, targetUserRoles: UserRole[] | undefined): boolean => {
  if (!targetUser || !targetUserRoles) return false;
  if (targetUser.role === 'owner') return true;
  return targetUserRoles.some(role => role.name === 'Owner');
};

const shouldDisableRole = (roleId: number, roles: Role[] | undefined): boolean => {
  if (isSelfEditing) return true;
  
  const ownerRoleId = getOwnerRoleId(roles);
  const isOwnerSelected = isOwnerRoleSelected(selectedRoleIds, roles);
  
  if (isOwnerSelected && roleId !== ownerRoleId) return true;
  if (roleId === ownerRoleId && isTargetUserOwner(user, userRoles) && !isCurrentUserOwner) return true;
  
  return false;
};
```

**Enhanced Role Change Handler**:

```typescript
const handleRoleChange = (roleId: number, checked: boolean) => {
  const ownerRoleId = getOwnerRoleId(allRoles);
  
  setSelectedRoleIds(prev => {
    if (checked) {
      // Owner role exclusivity
      if (roleId === ownerRoleId) {
        return [roleId]; // Only Owner role
      }
      // Remove Owner if selecting other roles
      else if (ownerRoleId && prev.includes(ownerRoleId)) {
        return [roleId];
      }
      else {
        return [...prev, roleId];
      }
    } else {
      return prev.filter(id => id !== roleId);
    }
  });
};
```

### Error Codes and Response Formats

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `SELF_ROLE_MODIFICATION_DENIED` | 403 | User attempting to modify their own roles |
| `OWNER_ROLE_PROTECTION` | 403 | Unauthorized attempt to modify Owner role |
| `OWNER_ROLE_EXCLUSIVITY` | 400 | Attempt to combine Owner with other roles |

**Example Error Response**:
```json
{
  "message": "Owner role cannot be combined with other roles. Owner role must be assigned exclusively.",
  "code": "OWNER_ROLE_EXCLUSIVITY"
}
```

## User Interface Changes

### Warning Messages and Alert Banners

**Self-Editing Alert**:
```jsx
{isSelfEditing && (
  <Alert className="mb-6">
    <Lock className="h-4 w-4" />
    <AlertDescription>
      <strong>Read-Only Mode:</strong> You cannot modify your own role assignments. 
      Contact an administrator if changes are needed.
    </AlertDescription>
  </Alert>
)}
```

**Owner Protection Alert**:
```jsx
{isTargetUserOwner(user, userRoles) && !isCurrentUserOwner && (
  <Alert className="mb-6">
    <Shield className="h-4 w-4" />
    <AlertDescription>
      <strong>Owner Protection:</strong> Owner role assignments cannot be modified by other users. 
      Only the Owner themselves or system administrators can make changes.
    </AlertDescription>
  </Alert>
)}
```

**Owner Exclusivity Alert**:
```jsx
{isOwnerRoleSelected(selectedRoleIds, allRoles) && (
  <Alert className="mb-6">
    <AlertTriangle className="h-4 w-4" />
    <AlertDescription>
      <strong>Owner Role Exclusivity:</strong> The Owner role cannot be combined with other roles. 
      Selecting Owner will automatically deselect all other roles.
    </AlertDescription>
  </Alert>
)}
```

### Visual Indicators

**Disabled Role Styling**:
```jsx
<div className={`flex items-start space-x-3 p-3 border rounded-lg ${isDisabled ? 'opacity-50 bg-muted/30' : ''}`}>
  <Checkbox
    disabled={isDisabled}
    // ... other props
  />
  <Label className={`text-sm font-medium leading-none ${isDisabled ? 'cursor-not-allowed opacity-70' : ''}`}>
    {role.name}
    {isDisabled && isOwnerRole && <Lock className="inline h-3 w-3 ml-1" />}
  </Label>
</div>
```

**Protected Role Badge**:
```jsx
{isDisabled && isOwnerRole && (
  <Badge variant="outline" className="text-xs">
    Protected
  </Badge>
)}
```

**Read-Only Save Button**:
```jsx
<Button 
  disabled={updateUserRolesMutation.isPending || isSelfEditing || (isTargetUserOwner(user, userRoles) && !isCurrentUserOwner)}
>
  {isSelfEditing ? (
    <>
      <Lock className="mr-2 h-4 w-4" />
      Read-Only Mode
    </>
  ) : (
    <>
      <Save className="mr-2 h-4 w-4" />
      Save Changes
    </>
  )}
</Button>
```

### Role Selection Behavior

**Automatic Deselection Logic**:
1. When Owner role is selected → All other roles are automatically deselected
2. When any other role is selected while Owner is active → Owner is automatically deselected
3. Disabled roles cannot be toggled
4. Visual feedback shows which roles are disabled and why

**Current Assignments Display**:
- Shows role source (legacy/direct/group)
- Displays role type badges (System/Custom)
- Includes role descriptions
- Shows assignment metadata when available

## Legacy System Integration

### Enum-Based Role Support

The system maintains backward compatibility with the existing enum-based role system while adding support for the new custom role system.

**Legacy Role Detection**:
```typescript
// Check enum role and map to custom role
if (user.role) {
  const roleMapping = {
    'owner': 'Owner',
    'employee': 'Employee',
    'collection_agent': 'Collection Agent',
    'system_admin': 'System Administrator'
  };

  const roleName = roleMapping[user.role];
  if (roleName) {
    // Find corresponding system role and include in response
  }
}
```

**Hybrid Role Assignment Support**:
- Users can have both enum roles (legacy) and custom role assignments
- System prevents conflicts between enum and custom role assignments
- Legacy roles are clearly marked with `source: 'legacy'` in API responses
- UI displays source information for transparency

**Data Migration Considerations**:
- Existing enum roles are automatically detected and displayed
- No immediate migration required - system works with current data
- Future migration can move enum roles to custom role assignments
- Business rules apply equally to both legacy and custom roles

### Role Source Tracking

**Source Types**:
- `legacy`: From enum-based `users.role` column
- `direct`: From `user_roles` table assignments
- `group`: From group membership role inheritance

**API Response Format**:
```json
{
  "id": 20,
  "name": "Employee",
  "description": "Standard employee access",
  "is_system": true,
  "source": "legacy",
  "assignment_id": null
}
```

## Testing and Validation

### API Endpoint Testing Results

**Successful Test Cases**:
- ✅ `GET /api/users/25` - User details retrieval
- ✅ `GET /api/users/25/roles` - Role assignments with legacy support
- ✅ `GET /api/roles` - Available roles retrieval
- ✅ `PUT /api/users/17/roles` - Valid role assignment updates

**Business Rule Validation**:
- ✅ Owner role exclusivity enforced at API level
- ✅ Self-modification attempts blocked with 403 status
- ✅ Owner protection prevents unauthorized changes
- ✅ Legacy enum roles properly integrated and displayed

### User Interface Testing Scenarios

**Self-Editing Restrictions**:
1. User 17 accessing `/user-management/users/17/roles`
2. All checkboxes disabled ✅
3. Save button shows "Read-Only Mode" ✅
4. Warning alert displayed ✅

**Owner Role Exclusivity**:
1. Select Owner role → Other roles automatically deselected ✅
2. Select other role while Owner active → Owner deselected ✅
3. Owner selected → Other checkboxes disabled ✅
4. Warning alert shown when Owner selected ✅

**Owner Role Protection**:
1. Non-owner viewing Owner user's roles → Owner checkbox disabled ✅
2. Protection alert displayed ✅
3. "Protected" badge shown on Owner role ✅

### Edge Cases and Error Handling

**Handled Edge Cases**:
- Users with both legacy enum roles and custom role assignments
- Missing or invalid role IDs in API requests
- Network errors during role assignment updates
- Concurrent role modifications by multiple users
- Users with no role assignments (empty state)

**Error Handling Verification**:
- Specific error codes returned for each business rule violation
- User-friendly error messages displayed in UI
- Graceful degradation when API calls fail
- Proper loading states during async operations

## Future Considerations

### Potential Enhancements

1. **Role Assignment History**: Track who made role changes and when
2. **Temporary Role Assignments**: Time-limited role grants
3. **Role Assignment Approval Workflow**: Require approval for sensitive role changes
4. **Bulk Role Operations**: Assign roles to multiple users simultaneously
5. **Role Templates**: Predefined role combinations for common user types

### Maintenance Notes

1. **Database Schema**: Current implementation works with existing schema
2. **Performance**: Role queries optimized for current user base size
3. **Security**: Regular review of permission logic recommended
4. **Monitoring**: Consider adding audit logs for role assignment changes

### Migration Path

If migrating from enum-based to fully custom role system:

1. **Phase 1**: Current hybrid approach (implemented)
2. **Phase 2**: Migrate enum roles to custom role assignments
3. **Phase 3**: Remove enum role columns and update legacy code
4. **Phase 4**: Enhanced role management features

---

**Document Version**: 1.0
**Last Updated**: December 2024
**Authors**: Development Team
**Review Date**: Quarterly
