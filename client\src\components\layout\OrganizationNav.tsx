import { useState } from "react";
import { Link, useLocation } from "wouter";
import { Building2, UsersRound } from "lucide-react";

/**
 * A standalone component for Organization navigation links
 * This is specifically created to ensure the branches and groups links
 * are always visible in the sidebar
 */
export default function OrganizationNav() {
  const [location] = useLocation();

  return (
    <div className="py-2">
      <div className="px-3 mb-2 text-xs font-medium text-blue-200 uppercase">Organization</div>
      
      {/* Branches */}
      <Link href="/branches">
        <div 
          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
            location.startsWith('/branches') 
              ? 'bg-blue-800 text-white' 
              : 'text-blue-100 hover:bg-blue-800 hover:text-white'
          }`}
        >
          <Building2 className="mr-3 h-5 w-5 text-blue-300" />
          <span>Branches</span>
        </div>
      </Link>
      
      {/* Groups */}
      <Link href="/groups">
        <div 
          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
            location.startsWith('/groups') 
              ? 'bg-blue-800 text-white' 
              : 'text-blue-100 hover:bg-blue-800 hover:text-white'
          }`}
        >
          <UsersRound className="mr-3 h-5 w-5 text-blue-300" />
          <span>Groups</span>
        </div>
      </Link>
    </div>
  );
}