-- Initialize database schema for TrackFina application
-- This file will be executed automatically when deploying with Docker

-- Add extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Sessions table (for session management)
CREATE TABLE IF NOT EXISTS "session" (
  "sid" varchar NOT NULL COLLATE "default",
  "sess" json NOT NULL,
  "expire" timestamp(6) NOT NULL,
  CONSTRAINT "session_pkey" PRIMARY KEY ("sid")
);

-- Create necessary enums
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('saas_admin', 'reseller', 'owner', 'employee', 'agent', 'customer', 'partner');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'transaction_type') THEN
        CREATE TYPE transaction_type AS ENUM ('income', 'expense', 'transfer', 'investment', 'loan_disbursement', 'loan_repayment', 'collection');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'account_type') THEN
        CREATE TYPE account_type AS ENUM ('asset', 'liability', 'equity', 'revenue', 'expense');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'collection_status') THEN
        CREATE TYPE collection_status AS ENUM ('pending', 'completed', 'overdue', 'partial', 'cancelled', 'rescheduled');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_method') THEN
        CREATE TYPE payment_method AS ENUM ('cash', 'upi', 'bank_transfer', 'cheque', 'card_payment', 'online_transfer', 'mobile_wallet');
    END IF;
END $$;

-- Users and authentication
CREATE TABLE IF NOT EXISTS "users" (
  "id" SERIAL PRIMARY KEY,
  "email" varchar(255) NOT NULL UNIQUE,
  "password_hash" varchar(255) NOT NULL,
  "mobile_number" varchar(20),
  "full_name" varchar(255),
  "role" user_role NOT NULL DEFAULT 'employee',
  "active" boolean NOT NULL DEFAULT true,
  "profile_image" varchar(255),
  "email_verified" boolean NOT NULL DEFAULT false,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "users_mobile_number_format_check" CHECK (mobile_number IS NULL OR mobile_number ~ '^\\+91[0-9]{10}$')
);

-- Companies
CREATE TABLE IF NOT EXISTS "companies" (
  "id" SERIAL PRIMARY KEY,
  "name" varchar(255) NOT NULL,
  "address" text,
  "phone" varchar(20),
  "email" varchar(255),
  "website" varchar(255),
  "logo" varchar(255),
  "active" boolean NOT NULL DEFAULT true,
  "is_primary" boolean NOT NULL DEFAULT false,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- User-Company relationship
CREATE TABLE IF NOT EXISTS "user_companies" (
  "id" SERIAL PRIMARY KEY,
  "user_id" integer NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("user_id", "company_id")
);

-- Branches
CREATE TABLE IF NOT EXISTS "branches" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "name" varchar(255) NOT NULL,
  "address" text,
  "phone" varchar(20),
  "email" varchar(255),
  "manager_id" integer REFERENCES "users" ("id") ON DELETE SET NULL,
  "active" boolean NOT NULL DEFAULT true,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Financial Accounts
CREATE TABLE IF NOT EXISTS "accounts" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "account_code" varchar(20) NOT NULL,
  "name" varchar(255) NOT NULL,
  "description" text,
  "account_type" account_type NOT NULL,
  "parent_id" integer REFERENCES "accounts" ("id") ON DELETE SET NULL,
  "opening_balance" numeric(15, 2) NOT NULL DEFAULT 0,
  "current_balance" numeric(15, 2) NOT NULL DEFAULT 0,
  "is_system" boolean NOT NULL DEFAULT false,
  "active" boolean NOT NULL DEFAULT true,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("company_id", "account_code")
);

-- Transactions
CREATE TABLE IF NOT EXISTS "transactions" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "branch_id" integer REFERENCES "branches" ("id") ON DELETE SET NULL,
  "transaction_date" timestamp NOT NULL,
  "amount" numeric(15, 2) NOT NULL,
  "description" text,
  "transaction_type" transaction_type NOT NULL,
  "from_account_id" integer REFERENCES "accounts" ("id") ON DELETE SET NULL,
  "to_account_id" integer REFERENCES "accounts" ("id") ON DELETE SET NULL,
  "reference_type" varchar(50),
  "reference_id" integer,
  "created_by" integer REFERENCES "users" ("id") ON DELETE SET NULL,
  "attachment" varchar(255),
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Customers
CREATE TABLE IF NOT EXISTS "customers" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "full_name" varchar(255) NOT NULL,
  "email" varchar(255),
  "phone" varchar(20) NOT NULL,
  "address" text,
  "profile_image" varchar(255),
  "credit_score" integer,
  "kyc_verified" boolean NOT NULL DEFAULT false,
  "notes" text,
  "active" boolean NOT NULL DEFAULT true,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Loans
CREATE TABLE IF NOT EXISTS "loans" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "customer_id" integer NOT NULL REFERENCES "customers" ("id") ON DELETE CASCADE,
  "loan_amount" numeric(15, 2) NOT NULL,
  "interest_rate" numeric(5, 2) NOT NULL,
  "loan_date" timestamp NOT NULL,
  "loan_term_months" integer NOT NULL,
  "processing_fee" numeric(15, 2) NOT NULL DEFAULT 0,
  "disbursed_amount" numeric(15, 2) NOT NULL,
  "outstanding_amount" numeric(15, 2) NOT NULL,
  "loan_purpose" text,
  "loan_status" varchar(20) NOT NULL DEFAULT 'active',
  "payment_frequency" varchar(20) NOT NULL DEFAULT 'monthly',
  "repayment_amount" numeric(15, 2) NOT NULL,
  "next_due_date" timestamp,
  "guarantor_name" varchar(255),
  "guarantor_phone" varchar(20),
  "guarantor_address" text,
  "collateral_details" text,
  "created_by" integer REFERENCES "users" ("id") ON DELETE SET NULL,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Collections
CREATE TABLE IF NOT EXISTS "collections" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "loan_id" integer NOT NULL REFERENCES "loans" ("id") ON DELETE CASCADE,
  "customer_id" integer NOT NULL REFERENCES "customers" ("id") ON DELETE CASCADE,
  "scheduled_date" timestamp NOT NULL,
  "scheduled_amount" numeric(15, 2) NOT NULL,
  "collected_amount" numeric(15, 2) DEFAULT 0,
  "payment_date" timestamp,
  "collection_number" varchar(50),
  "status" collection_status NOT NULL DEFAULT 'pending',
  "payment_method" payment_method,
  "notes" text,
  "receipt_id" varchar(50),
  "transaction_id" integer REFERENCES "transactions" ("id") ON DELETE SET NULL,
  "collected_by" integer REFERENCES "users" ("id") ON DELETE SET NULL,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Expenses
CREATE TABLE IF NOT EXISTS "expenses" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "branch_id" integer REFERENCES "branches" ("id") ON DELETE SET NULL,
  "expense_date" timestamp NOT NULL,
  "amount" numeric(15, 2) NOT NULL,
  "category" varchar(100) NOT NULL,
  "description" text,
  "payment_method" payment_method NOT NULL,
  "receipt_number" varchar(50),
  "vendor" varchar(255),
  "is_recurring" boolean NOT NULL DEFAULT false,
  "recurrence_frequency" varchar(20),
  "created_by" integer REFERENCES "users" ("id") ON DELETE SET NULL,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create default admin user if none exists (password is 'admin123')
INSERT INTO "users" ("email", "password_hash", "full_name", "role", "email_verified")
SELECT '<EMAIL>', '$2b$10$mLWfI1CPIK8Th1OihG1XReQQaIGdj5bxnW6H0JzH6Dv18/7kEu2Dy', 'System Administrator', 'saas_admin', true
WHERE NOT EXISTS (SELECT 1 FROM "users" WHERE email = '<EMAIL>');