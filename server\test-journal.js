// A simple test script to create a journal entry directly
const axios = require('axios');
const fs = require('fs');
const cookie = require('cookie');

// Try to read cookie from file
let cookieValue = '';
try {
  cookieValue = fs.readFileSync('./cookie.txt', 'utf8').trim();
  console.log('Found cookie:', cookieValue);
} catch (err) {
  console.error('No cookie file found. You need to login first and save cookie.');
  process.exit(1);
}

// Parse the cookie
const parsedCookie = cookie.parse(cookieValue);
const sessionId = parsedCookie['connect.sid'];

if (!sessionId) {
  console.error('No session ID found in cookie. Make sure you have a valid session.');
  process.exit(1);
}

// Set up API client with cookie
const api = axios.create({
  baseURL: 'http://localhost:5000',
  headers: {
    'Cookie': cookieValue
  }
});

async function testJournalEntry() {
  try {
    console.log('Testing journal entry creation...');

    // First get the current user to determine their company
    const userResponse = await api.get('/api/auth/me');
    const userData = userResponse.data;

    if (!userData || !userData.company_id) {
      console.error('No company associated with current user. Please set a company first.');
      return;
    }

    const companyId = userData.company_id;
    console.log(`Using company ID ${companyId} from current user context`);

    // Call the test endpoint
    const response = await api.post(`/api/companies/${companyId}/test-journal-entry`);

    console.log('Journal entry created:');
    console.log(JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error('Error creating test journal entry:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error(error.message);
    }
  }
}

// Run the test
testJournalEntry();