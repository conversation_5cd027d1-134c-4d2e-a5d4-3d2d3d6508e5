import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  User,
  Shield,
  Search,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Loader2,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/api';

interface UserPermission {
  user_id: number;
  user_info: {
    id: number;
    full_name: string;
    email: string;
    role: string;
  };
  direct_roles: Array<{
    role_id: number;
    role_name: string;
    role_description: string;
  }>;
  effective_permissions: {
    total_count: number;
    permissions_by_category: Record<string, Array<{
      id: number;
      code: string;
      name: string;
      description: string;
      category: string;
    }>>;
    permission_codes: string[];
  };
}

interface PermissionCheckResult {
  user_id: number;
  permission_type: string;
  operation_type?: string;
  context: any;
  has_permission: boolean;
  checked_at: string;
}

interface UserPermissionViewerProps {
  userId?: number;
  onUserSelect?: (userId: number) => void;
}

export function UserPermissionViewer({ userId, onUserSelect }: UserPermissionViewerProps) {
  const [userPermissions, setUserPermissions] = useState<UserPermission | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchUserId, setSearchUserId] = useState(userId?.toString() || '');
  const [permissionCheck, setPermissionCheck] = useState({
    permission_type: '',
    operation_type: '',
    amount: '',
    checking: false,
    result: null as PermissionCheckResult | null
  });
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (userId) {
      setSearchUserId(userId.toString());
      loadUserPermissions(userId);
    }
  }, [userId]);

  const loadUserPermissions = async (targetUserId: number) => {
    setLoading(true);
    try {
      const response = await apiRequest('GET', `/api/permissions/user/${targetUserId}/effective`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found');
        }
        throw new Error('Failed to load user permissions');
      }

      const data = await response.json();
      setUserPermissions(data);

      // Expand all categories by default
      setExpandedCategories(new Set(Object.keys(data.effective_permissions.permissions_by_category)));

      if (onUserSelect) {
        onUserSelect(targetUserId);
      }
    } catch (error) {
      console.error('Error loading user permissions:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load user permissions",
        variant: "destructive",
      });
      setUserPermissions(null);
    } finally {
      setLoading(false);
    }
  };

  const handleUserSearch = () => {
    const targetUserId = parseInt(searchUserId);
    if (isNaN(targetUserId) || targetUserId <= 0) {
      toast({
        title: "Invalid User ID",
        description: "Please enter a valid user ID",
        variant: "destructive",
      });
      return;
    }
    loadUserPermissions(targetUserId);
  };

  const checkPermission = async () => {
    if (!userPermissions || !permissionCheck.permission_type) {
      toast({
        title: "Missing Information",
        description: "Please select a user and permission type",
        variant: "destructive",
      });
      return;
    }

    setPermissionCheck(prev => ({ ...prev, checking: true }));

    try {
      const context: any = {};
      if (permissionCheck.amount) {
        context.amount = parseFloat(permissionCheck.amount);
      }

      const response = await apiRequest('POST', '/api/permissions/check', {
        user_id: userPermissions.user_id,
        permission_type: permissionCheck.permission_type,
        operation_type: permissionCheck.operation_type || undefined,
        context
      });

      if (!response.ok) throw new Error('Failed to check permission');

      const result = await response.json();
      setPermissionCheck(prev => ({ ...prev, result }));
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to check permission",
        variant: "destructive",
      });
    } finally {
      setPermissionCheck(prev => ({ ...prev, checking: false }));
    }
  };

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const getCategoryDisplayName = (category: string) => {
    return category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">User Permission Viewer</h2>
        <p className="text-muted-foreground">
          View effective permissions and test access for specific users
        </p>
      </div>

      {/* User Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search User
          </CardTitle>
          <CardDescription>
            Enter a user ID to view their effective permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="Enter User ID..."
              value={searchUserId}
              onChange={(e) => setSearchUserId(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleUserSearch()}
              className="flex-1"
            />
            <Button onClick={handleUserSearch} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Search className="h-4 w-4 mr-2" />}
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* User Information */}
      {userPermissions && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* User Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Full Name</label>
                <p className="font-medium">{userPermissions.user_info.full_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Email</label>
                <p className="font-medium">{userPermissions.user_info.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">System Role</label>
                <Badge variant="outline">{userPermissions.user_info.role}</Badge>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Direct Roles</label>
                <div className="space-y-2 mt-2">
                  {userPermissions.direct_roles.length > 0 ? (
                    userPermissions.direct_roles.map((role) => (
                      <div key={role.role_id} className="p-2 bg-muted/50 rounded">
                        <div className="font-medium">{role.role_name}</div>
                        <div className="text-sm text-muted-foreground">{role.role_description}</div>
                      </div>
                    ))
                  ) : (
                    <div className="p-2 text-sm text-muted-foreground italic">
                      No direct roles assigned
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Permission Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Permission Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">
                  {userPermissions.effective_permissions.total_count}
                </div>
                <p className="text-sm text-muted-foreground">Total Permissions</p>
              </div>
              <Separator />
              <div className="space-y-2">
                {Object.keys(userPermissions.effective_permissions.permissions_by_category).length > 0 ? (
                  Object.entries(userPermissions.effective_permissions.permissions_by_category).map(([category, permissions]) => (
                    <div key={category} className="flex justify-between items-center">
                      <span className="text-sm font-medium">{getCategoryDisplayName(category)}</span>
                      <Badge variant="secondary">{permissions.length}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">No permissions assigned</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Contact your administrator to assign roles and permissions
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Permission Checker */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Permission Checker
              </CardTitle>
              <CardDescription>
                Test specific permissions for this user
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Permission Type</label>
                <select
                  className="w-full mt-1 p-2 border rounded"
                  value={permissionCheck.permission_type}
                  onChange={(e) => setPermissionCheck(prev => ({ ...prev, permission_type: e.target.value }))}
                >
                  <option value="">Select permission type</option>
                  <option value="loan_create">Loan Creation</option>
                  <option value="loan_approve">Loan Approval</option>
                  <option value="loan_disburse">Loan Disbursement</option>
                  <option value="customer_data">Customer Data Access</option>
                  <option value="customer_export">Customer Export</option>
                  <option value="customer_communication">Customer Communication</option>
                  <option value="payment_operation">Payment Operation</option>
                  <option value="report">Report Access</option>
                </select>
              </div>

              {permissionCheck.permission_type && (
                <div>
                  <label className="text-sm font-medium">Operation Type</label>
                  <Input
                    placeholder="e.g., basic, financial, email, manual"
                    value={permissionCheck.operation_type}
                    onChange={(e) => setPermissionCheck(prev => ({ ...prev, operation_type: e.target.value }))}
                  />
                </div>
              )}

              {(permissionCheck.permission_type.includes('loan') || permissionCheck.permission_type.includes('payment')) && (
                <div>
                  <label className="text-sm font-medium">Amount (optional)</label>
                  <Input
                    type="number"
                    placeholder="Enter amount"
                    value={permissionCheck.amount}
                    onChange={(e) => setPermissionCheck(prev => ({ ...prev, amount: e.target.value }))}
                  />
                </div>
              )}

              <Button
                onClick={checkPermission}
                disabled={permissionCheck.checking || !permissionCheck.permission_type}
                className="w-full"
              >
                {permissionCheck.checking ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Check Permission
              </Button>

              {permissionCheck.result && (
                <Alert className={permissionCheck.result.has_permission ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                  {permissionCheck.result.has_permission ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription>
                    <strong>
                      {permissionCheck.result.has_permission ? 'Permission Granted' : 'Permission Denied'}
                    </strong>
                    <br />
                    {permissionCheck.result.permission_type}
                    {permissionCheck.result.operation_type && ` (${permissionCheck.result.operation_type})`}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Permissions */}
      {userPermissions && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Permissions</CardTitle>
            <CardDescription>
              All effective permissions organized by category
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[500px]">
              <div className="space-y-4">
                {Object.keys(userPermissions.effective_permissions.permissions_by_category).length > 0 ? (
                  Object.entries(userPermissions.effective_permissions.permissions_by_category).map(([category, permissions]) => (
                    <div key={category} className="space-y-2">
                      <div
                        className="flex items-center justify-between p-3 bg-muted/50 rounded-lg cursor-pointer hover:bg-muted/70 transition-colors"
                        onClick={() => toggleCategory(category)}
                      >
                        <div className="flex items-center gap-2">
                          {expandedCategories.has(category) ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                          <h3 className="font-semibold">{getCategoryDisplayName(category)}</h3>
                        </div>
                        <Badge variant="secondary">{permissions.length} permissions</Badge>
                      </div>

                      {expandedCategories.has(category) && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 ml-6">
                          {permissions.map((permission) => (
                            <div key={permission.id} className="p-3 border rounded-lg">
                              <div className="font-medium text-sm">{permission.name}</div>
                              <div className="text-xs text-muted-foreground mt-1">{permission.description}</div>
                              <code className="text-xs bg-muted px-2 py-1 rounded mt-2 inline-block">
                                {permission.code}
                              </code>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Permissions Found</h3>
                    <p className="text-muted-foreground mb-4">
                      This user currently has no permissions assigned.
                    </p>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>• Contact your system administrator to assign roles</p>
                      <p>• Check if the user needs to be added to groups</p>
                      <p>• Verify if temporary permissions are needed</p>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
