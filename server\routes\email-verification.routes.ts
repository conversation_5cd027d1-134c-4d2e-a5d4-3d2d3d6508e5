import { Express, Request, Response } from 'express';
import { authMiddleware, AuthRequest } from '../middleware/auth';
import { emailVerificationService } from '../services/emailVerificationService';
import { z } from 'zod';

// Validation schemas
const verifyEmailSchema = z.object({
  token: z.string()
    .min(1, 'Token is required')
    .max(128, 'Token too long')
});

const resendVerificationSchema = z.object({
  email: z.string().email('Invalid email format').optional()
});

export function registerEmailVerificationRoutes(app: Express): void {
  
  // Verify email with token (public route)
  app.post('/api/auth/verify-email', async (req: Request, res: Response) => {
    try {
      const result = verifyEmailSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid token format', 
          errors: result.error.errors 
        });
      }

      const verificationResult = await emailVerificationService.verifyEmail(result.data.token);
      
      if (verificationResult.success) {
        return res.json({
          success: true,
          message: verificationResult.message,
          data: {
            userId: verificationResult.userId
          }
        });
      } else {
        return res.status(400).json({
          success: false,
          message: verificationResult.message
        });
      }
    } catch (error) {
      console.error('Email verification error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error during email verification' 
      });
    }
  });

  // Get verification status (authenticated route)
  app.get('/api/auth/verification-status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      const status = await emailVerificationService.getVerificationStatus(req.user.id);
      
      return res.json({
        success: true,
        data: status,
        message: status.verified 
          ? 'Email is verified' 
          : 'Email verification pending'
      });
    } catch (error) {
      console.error('Error getting verification status:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error retrieving verification status' 
      });
    }
  });

  // Resend verification email (authenticated route)
  app.post('/api/auth/resend-verification', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      const result = resendVerificationSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid input', 
          errors: result.error.errors 
        });
      }

      // Check if email is already verified
      const isVerified = await emailVerificationService.isEmailVerified(req.user.id);
      if (isVerified) {
        return res.status(400).json({
          success: false,
          message: 'Email is already verified'
        });
      }

      const sendResult = await emailVerificationService.resendVerificationEmail(req.user.id);
      
      if (sendResult.success) {
        return res.json({
          success: true,
          message: sendResult.message
        });
      } else {
        return res.status(400).json({
          success: false,
          message: sendResult.message
        });
      }
    } catch (error) {
      console.error('Error resending verification email:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error resending verification email' 
      });
    }
  });

  // Send verification email to new user (authenticated route - admin only)
  app.post('/api/auth/send-verification', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      // TODO: Add admin permission check
      // For now, allow any authenticated user to send verification emails

      const schema = z.object({
        userId: z.number().positive('User ID must be a positive number'),
        email: z.string().email('Invalid email format')
      });

      const result = schema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid input', 
          errors: result.error.errors 
        });
      }

      const sendResult = await emailVerificationService.sendVerificationEmail(
        result.data.userId, 
        result.data.email
      );
      
      if (sendResult.success) {
        return res.json({
          success: true,
          message: sendResult.message
        });
      } else {
        return res.status(400).json({
          success: false,
          message: sendResult.message
        });
      }
    } catch (error) {
      console.error('Error sending verification email:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error sending verification email' 
      });
    }
  });

  // Check if email verification is required (public route)
  app.get('/api/auth/verification-required/:email', async (req: Request, res: Response) => {
    try {
      const email = req.params.email;
      
      if (!email || !z.string().email().safeParse(email).success) {
        return res.status(400).json({
          success: false,
          message: 'Invalid email format'
        });
      }

      // For now, email verification is always required for new registrations
      // In the future, this could be configurable per company
      return res.json({
        success: true,
        data: {
          required: true,
          message: 'Email verification is required for all new accounts'
        }
      });
    } catch (error) {
      console.error('Error checking verification requirement:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error checking verification requirement' 
      });
    }
  });

  // Admin route to cleanup expired tokens
  app.post('/api/auth/cleanup-expired-tokens', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      // TODO: Add admin permission check
      // For now, allow any authenticated user to cleanup tokens

      const cleanedCount = await emailVerificationService.cleanupExpiredTokens();
      
      return res.json({
        success: true,
        data: {
          cleanedTokens: cleanedCount
        },
        message: `Cleaned up ${cleanedCount} expired verification tokens`
      });
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error cleaning up expired tokens' 
      });
    }
  });
}
