import { useSettings } from "@/contexts/SettingsContext";
import { formatCurrency, formatDate } from "@/lib/utils";

export function useSystemSettings() {
  const { settings, isLoading, error } = useSettings();

  // Format date using system settings
  const formatDateWithSettings = (dateString: string): string => {
    return formatDate(dateString, settings.date_format);
  };

  // Format currency using system settings
  const formatCurrencyWithSettings = (
    amount: number,
    currency: string = 'INR',
    locale: string = 'en-IN'
  ): string => {
    return formatCurrency(amount, currency, locale, settings.currency_symbol);
  };

  return {
    dateFormat: settings.date_format,
    currencySymbol: settings.currency_symbol,
    formatDate: formatDateWithSettings,
    formatCurrency: formatCurrencyWithSettings,
    isLoading,
    error,
  };
}
