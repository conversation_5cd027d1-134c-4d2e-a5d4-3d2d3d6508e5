#!/usr/bin/env node
/**
 * Migration Script: Customer Reference Code Migration
 * Purpose: Add customer_reference_code column to customers table
 * Usage: node scripts/migrations/run-customer-reference-migration.js
 */

import { runMigration, logMigrationStats } from '../utils/migration-runner.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

await runMigration('Customer Reference Code Migration', async (pool, { dryRun }) => {
  const migrationPath = path.join(__dirname, '..', '..', 'migrations', '007_add_customer_reference_code.sql');
  
  if (!fs.existsSync(migrationPath)) {
    throw new Error(`Migration file not found: ${migrationPath}`);
  }
  
  if (dryRun) {
    console.log('Would add customer_reference_code column to customers table');
    
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'customers' AND column_name = 'customer_reference_code'
    `);
    
    logMigrationStats({
      'Column exists': columnCheck.rows.length > 0 ? 'Yes' : 'No',
      'Action': columnCheck.rows.length > 0 ? 'No changes needed' : 'Would add column'
    });
    return;
  }
  
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  await pool.query(migrationSQL);
  
  const columnCheck = await pool.query(`
    SELECT column_name
    FROM information_schema.columns
    WHERE table_name = 'customers' AND column_name = 'customer_reference_code'
  `);
  
  if (columnCheck.rows.length > 0) {
    console.log('✅ customer_reference_code column added to customers table');
    logMigrationStats({
      'Column added': 'Yes',
      'Migration status': 'Success'
    });
  } else {
    throw new Error('customer_reference_code column was not created');
  }
});
