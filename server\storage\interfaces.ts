import {
  User, InsertUser, Company, InsertCompany, Customer, InsertCustomer,
  Agent, InsertAgent, Loan, InsertLoan, Collection, InsertCollection,
  Payment, InsertPayment, UserCompany, InsertUserCompany, Partner, InsertPartner,
  FormTemplate, InsertFormTemplate, FormField, InsertFormField,
  FormSubmission, InsertFormSubmission, LoanConfiguration, InsertLoanConfiguration,
  Expense, InsertExpense, Fine, InsertFine, CompanyPrefixSettings,
  // Financial management types
  Account, InsertAccount, Transaction, InsertTransaction,
  AccountBalance, InsertAccountBalance, AccountingPeriod, InsertAccountingPeriod,
  Shareholder, InsertShareholder, Shareholding, InsertShareholding,
  InvestmentTransaction, InsertInvestmentTransaction,
  ProfitDistribution, InsertProfitDistribution,
  BalanceSheet, InsertBalanceSheet, BalanceSheetItem, InsertBalanceSheetItem,
  FixedAsset, InsertFixedAsset,
  // Report interfaces
  DailyCollectionReport, DaySheetReport, CustomerReport,
  AgentReport, ProfitLossReport, AccountStatement, AccountBalanceReport,
  BalanceSheetReport, BalanceSheetDetail, CashFlowReport, ShareholderReport,
  // Subscription types
  SubscriptionPlan, InsertSubscriptionPlan, Subscription, InsertSubscription,
  // Reseller types
  Reseller, InsertReseller, ResellerClient, InsertResellerClient,
  ResellerCommission, InsertResellerCommission, Referral, InsertReferral
} from "../../shared/schema";

// User-related storage operations
export interface IUserStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User>;
  getUsersByCompany(companyId: number): Promise<User[]>;
  updateUserPassword(id: number, newPassword: string): Promise<User>;
  getUsers(): Promise<User[]>;
}

// Company-related storage operations
export interface ICompanyStorage {
  getCompany(id: number): Promise<Company | undefined>;
  getCompanies(): Promise<Company[]>;
  createCompany(company: InsertCompany): Promise<Company>;
  updateCompany(id: number, company: Partial<InsertCompany>): Promise<Company>;
  deleteCompany(id: number): Promise<void>;
  getCompanyUsers(companyId: number): Promise<User[]>;
}

// UserCompany-related storage operations
export interface IUserCompanyStorage {
  getUserCompanies(userId: number): Promise<(UserCompany & { company: Company })[]>;
  getUserCompanyByIds(userId: number, companyId: number): Promise<UserCompany | undefined>;
  createUserCompany(userCompany: InsertUserCompany): Promise<UserCompany>;
  updateUserCompany(id: number, userCompany: Partial<InsertUserCompany>): Promise<UserCompany>;
  deleteUserCompany(id: number): Promise<void>;
  setUserCompanyAsPrimary(id: number, userId: number): Promise<UserCompany>;
  updateUserCompanyPrimary(id: number, isPrimary: boolean): Promise<UserCompany | undefined>;
}

// Customer-related storage operations
export interface ICustomerStorage {
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomersByCompany(companyId: number): Promise<Customer[]>;
  getCustomersByBranch(branchId: number): Promise<Customer[]>;
  getCustomerByPhone(phone: string, companyId: number): Promise<Customer | undefined>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, companyId: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined>;
  deleteCustomer(id: number, companyId: number): Promise<{ success: boolean, error?: string, loansCount?: number }>;
  getHighestCustomerSerial(companyId: number, prefix: string): Promise<number>;
}

// Loan-related storage operations
export interface ILoanStorage {
  getLoan(id: number): Promise<Loan | undefined>;
  getLoansByCustomer(customerId: number): Promise<Loan[]>;
  getLoansByCompany(companyId: number): Promise<Loan[]>;
  getLoansByBranch(branchId: number): Promise<Loan[]>;
  createLoan(loan: InsertLoan): Promise<Loan>;
  updateLoan(id: number, companyId: number, loan: Partial<InsertLoan>): Promise<Loan>;
  deleteLoan(id: number, companyId: number): Promise<{ success: boolean, error?: string, collectionsCount?: number, nonPendingCollections?: number, transactionsCount?: number }>;
  deleteLoanWithCollections(id: number, companyId: number): Promise<{ success: boolean, error?: string, collectionsDeleted?: number, transactionsDeleted?: number }>;
}

// Payment Schedule operations have been removed

// Collection-related storage operations
export interface ICollectionStorage {
  getCollection(id: number): Promise<Collection | undefined>;
  getCollectionsByCompany(companyId: number, status?: string, agentId?: number, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  getCollectionsByBranch(branchId: number, status?: string, agentId?: number, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  getCollectionsByLoan(loanId: number, companyId: number): Promise<Collection[]>;
  getCollectionsByAgent(agentId: number, companyId: number, status?: string, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  createCollection(collection: InsertCollection): Promise<Collection>;
  updateCollection(id: number, companyId: number, collection: Partial<InsertCollection>): Promise<Collection>;
  updateCollectionStatus(id: number, status: string): Promise<Collection>;
  deleteCollection(id: number, companyId?: number): Promise<boolean>;
  markCollectionsAsCompleted(collectionIds: number[]): Promise<void>;
  getPendingCollections(companyId: number): Promise<Collection[]>;
  getOverdueCollections(companyId: number): Promise<Collection[]>;

  // New method for paginated collections with search
  getPaginatedCollections(
    companyId: number,
    options: {
      page?: number;
      limit?: number;
      status?: string;
      searchTerm?: string;
      agentId?: number;
      dateRange?: { startDate: string, endDate: string };
    }
  ): Promise<{ collections: Collection[], totalCount: number }>;

  // Collection completion restriction methods
  canCompleteCollection(collectionId: number): Promise<{ canComplete: boolean; reason?: string; nextCollectionEmi?: number }>;
  getCollectionsWithCompletionStatus(loanId: number, companyId: number): Promise<(Collection & { canComplete: boolean; completionReason?: string })[]>;
  getHighestCollectionSerial(companyId: number, prefix: string): Promise<number>;
}

// Payment-related storage operations
export interface IPaymentStorage {
  getPayment(id: number): Promise<Payment | undefined>;
  getPaymentsByCompany(companyId: number, dateRange?: { startDate: string, endDate: string }): Promise<Payment[]>;
  getPaymentsByLoan(loanId: number, companyId: number): Promise<Payment[]>;
  getPaymentsByCustomer(customerId: number, companyId: number): Promise<Payment[]>;
  getPaymentsByCollection(collectionId: number, companyId: number): Promise<Payment[]>;
  createPayment(payment: InsertPayment): Promise<Payment>;
  updatePayment(id: number, companyId: number, payment: Partial<InsertPayment>): Promise<Payment>;
  deletePayment(id: number, companyId: number): Promise<boolean>;
  getPaymentReceipt(id: number, companyId: number): Promise<any>;
}

// Partner-related storage operations
export interface IPartnerStorage {
  getPartner(id: number): Promise<Partner | undefined>;
  getPartnersByCompany(companyId: number): Promise<Partner[]>;
  createPartner(partner: InsertPartner): Promise<Partner>;
  updatePartner(id: number, companyId: number, partner: Partial<InsertPartner>): Promise<Partner>;
  deletePartner(id: number, companyId: number): Promise<{ success: boolean, error?: string }>;
}

// Agent-related storage operations
export interface IAgentStorage {
  getAgent(id: number): Promise<(Agent & { full_name?: string, email?: string, phone?: string }) | undefined>;
  getAgentsByCompany(companyId: number): Promise<(Agent & { full_name?: string, email?: string, phone?: string })[]>;
  getAgentByUserAndCompany(userId: number, companyId: number): Promise<Agent | undefined>;
  createAgent(agent: InsertAgent): Promise<Agent>;
  updateAgent(id: number, companyId: number, agent: Partial<InsertAgent>): Promise<Agent | undefined>;
  deleteAgent(id: number, companyId: number): Promise<boolean>;
  getHighestAgentSerial(companyId: number, prefix: string): Promise<number>;
}

// Company Prefix Settings-related storage operations
export interface ICompanyPrefixSettingsStorage {
  getCompanyPrefixSettings(companyId: number): Promise<CompanyPrefixSettings | undefined>;
  createCompanyPrefixSettings(settings: CompanyPrefixSettings): Promise<CompanyPrefixSettings>;
  updateCompanyPrefixSettings(companyId: number, settings: Partial<CompanyPrefixSettings>): Promise<CompanyPrefixSettings>;
  deleteCompanyPrefixSettings(companyId: number): Promise<void>;
}

// Import financial interfaces
import { IAccountStorage, ITransactionStorage, IReportStorage } from './financial/interfaces';

// Main storage interface that combines all domain interfaces
export interface IStorage extends
  IUserStorage,
  ICompanyStorage,
  IUserCompanyStorage,
  ICustomerStorage,
  ILoanStorage,
  ICollectionStorage,
  IPaymentStorage,
  IPartnerStorage,
  IAgentStorage,
  ICompanyPrefixSettingsStorage,
  IAccountStorage,
  ITransactionStorage,
  IReportStorage
  // Other domain interfaces will be added here
{
  // Any methods that don't fit into a specific domain
}
