import React, { useState } from 'react';
import { Draggable, Droppable } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  MoreVertical, 
  Edit, 
  Trash2, 
  Users, 
  Shield, 
  ChevronDown, 
  ChevronRight,
  GripVertical,
  Plus,
  AlertTriangle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { InheritanceTypeDisplay, InheritanceTypeSelector, type InheritanceType } from './InheritanceTypeSelector';
import type { RoleHierarchyNode } from '@/hooks/useRoleHierarchy';

interface RoleNodeProps {
  node: RoleHierarchyNode;
  index: number;
  level: number;
  isExpanded: boolean;
  onToggleExpand: (roleId: number) => void;
  onEditRole: (roleId: number) => void;
  onDeleteRole: (roleId: number) => void;
  onUpdateInheritance: (parentId: number, childId: number, inheritanceType: InheritanceType) => void;
  onAddChild: (parentId: number) => void;
  isDragDisabled?: boolean;
  hasConflicts?: boolean;
  conflictMessage?: string;
}

export function RoleNode({
  node,
  index,
  level,
  isExpanded,
  onToggleExpand,
  onEditRole,
  onDeleteRole,
  onUpdateInheritance,
  onAddChild,
  isDragDisabled = false,
  hasConflicts = false,
  conflictMessage,
}: RoleNodeProps) {
  const [isEditingInheritance, setIsEditingInheritance] = useState(false);
  const hasChildren = node.children && node.children.length > 0;
  const isSystemRole = node.role.is_system;

  const handleInheritanceChange = (newType: InheritanceType) => {
    if (node.parents.length > 0) {
      const parentId = node.parents[0].role.id; // Assuming single parent for now
      onUpdateInheritance(parentId, node.role.id, newType);
    }
    setIsEditingInheritance(false);
  };

  const getIndentationStyle = (level: number) => ({
    marginLeft: `${level * 24}px`,
  });

  return (
    <div style={getIndentationStyle(level)}>
      <Draggable 
        draggableId={`role-${node.role.id}`} 
        index={index}
        isDragDisabled={isDragDisabled || isSystemRole}
      >
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            className={`mb-2 ${snapshot.isDragging ? 'opacity-50' : ''}`}
          >
            <Card className={`
              transition-all duration-200 hover:shadow-md
              ${snapshot.isDragging ? 'shadow-lg rotate-2' : ''}
              ${hasConflicts ? 'border-red-300 bg-red-50' : ''}
            `}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {/* Drag Handle */}
                    <div 
                      {...provided.dragHandleProps}
                      className={`
                        p-1 rounded cursor-grab active:cursor-grabbing
                        ${isDragDisabled || isSystemRole ? 'opacity-30 cursor-not-allowed' : 'hover:bg-gray-100'}
                      `}
                    >
                      <GripVertical className="h-4 w-4 text-gray-400" />
                    </div>

                    {/* Expand/Collapse Button */}
                    {hasChildren && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onToggleExpand(node.role.id)}
                        className="p-1 h-6 w-6"
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    )}

                    {/* Role Name and System Badge */}
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-sm">{node.role.name}</h3>
                      {isSystemRole && <Badge variant="outline">System</Badge>}
                      {hasConflicts && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <AlertTriangle className="h-4 w-4 text-red-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{conflictMessage}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>

                  {/* Actions Menu */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEditRole(node.role.id)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Role
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onAddChild(node.role.id)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Child Role
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Users className="mr-2 h-4 w-4" />
                        View Users
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Shield className="mr-2 h-4 w-4" />
                        View Permissions
                      </DropdownMenuItem>
                      {!isSystemRole && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => onDeleteRole(node.role.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Role
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-2">
                  {/* Role Description */}
                  {node.role.description && (
                    <p className="text-xs text-muted-foreground">
                      {node.role.description}
                    </p>
                  )}

                  {/* Inheritance Type */}
                  {node.inheritanceType && (
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">Inheritance:</span>
                      {isEditingInheritance ? (
                        <div className="flex-1 max-w-[150px]">
                          <InheritanceTypeSelector
                            value={node.inheritanceType}
                            onChange={handleInheritanceChange}
                            size="sm"
                            showTooltip={false}
                          />
                        </div>
                      ) : (
                        <button
                          onClick={() => setIsEditingInheritance(true)}
                          className="hover:opacity-80 transition-opacity"
                        >
                          <InheritanceTypeDisplay
                            type={node.inheritanceType}
                            size="sm"
                            showTooltip={true}
                          />
                        </button>
                      )}
                    </div>
                  )}

                  {/* Role Stats */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>Depth: {node.depth}</span>
                    {hasChildren && (
                      <span>Children: {node.children.length}</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Drop Zone for Child Roles */}
            <Droppable droppableId={`role-children-${node.role.id}`} type="ROLE">
              {(provided, snapshot) => (
                <div
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className={`
                    min-h-[8px] transition-all duration-200
                    ${snapshot.isDraggingOver ? 'bg-blue-100 border-2 border-dashed border-blue-300 rounded-md' : ''}
                  `}
                >
                  {/* Render Children */}
                  {isExpanded && hasChildren && (
                    <div className="mt-2">
                      {node.children.map((childNode, childIndex) => (
                        <RoleNode
                          key={childNode.role.id}
                          node={childNode}
                          index={childIndex}
                          level={level + 1}
                          isExpanded={isExpanded}
                          onToggleExpand={onToggleExpand}
                          onEditRole={onEditRole}
                          onDeleteRole={onDeleteRole}
                          onUpdateInheritance={onUpdateInheritance}
                          onAddChild={onAddChild}
                          isDragDisabled={isDragDisabled}
                        />
                      ))}
                    </div>
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>
        )}
      </Draggable>
    </div>
  );
}
