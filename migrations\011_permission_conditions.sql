-- Permission Conditions Migration
-- This migration adds conditional permission support for time, location, amount, and approval-based conditions

-- Create permission_conditions table
CREATE TABLE IF NOT EXISTS "permission_conditions" (
  "id" SERIAL PRIMARY KEY,
  "permission_id" INTEGER NOT NULL REFERENCES "permissions"("id") ON DELETE CASCADE,
  "condition_type" VARCHAR(50) NOT NULL, -- 'time', 'location', 'amount', 'approval', 'device', 'session'
  "condition_config" JSONB NOT NULL,
  "is_active" BOOLEAN DEFAULT true NOT NULL,
  "priority" INTEGER DEFAULT 0 NOT NULL, -- Higher priority conditions are evaluated first
  "description" TEXT, -- Human-readable description of the condition
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_permission_conditions_permission_id" ON "permission_conditions"("permission_id");
CREATE INDEX IF NOT EXISTS "idx_permission_conditions_type" ON "permission_conditions"("condition_type");
CREATE INDEX IF NOT EXISTS "idx_permission_conditions_active" ON "permission_conditions"("is_active");
CREATE INDEX IF NOT EXISTS "idx_permission_conditions_priority" ON "permission_conditions"("priority" DESC);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS "idx_permission_conditions_permission_type_active" 
ON "permission_conditions"("permission_id", "condition_type", "is_active");

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_permission_conditions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_permission_conditions_updated_at
  BEFORE UPDATE ON "permission_conditions"
  FOR EACH ROW
  EXECUTE FUNCTION update_permission_conditions_updated_at();

-- Insert example permission conditions for demonstration

-- Time-based condition: Loan creation only during business hours
INSERT INTO "permission_conditions" ("permission_id", "condition_type", "condition_config", "description", "priority")
SELECT 
  p.id,
  'time',
  '{"start_time": "09:00", "end_time": "17:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"], "timezone": "UTC"}'::jsonb,
  'Loan creation only allowed during business hours (9 AM - 5 PM, Monday-Friday)',
  10
FROM "permissions" p
WHERE p.code = 'loan_create_basic'
AND NOT EXISTS (
  SELECT 1 FROM "permission_conditions" pc 
  WHERE pc.permission_id = p.id AND pc.condition_type = 'time'
);

-- Amount-based condition: Basic loan creation limited to $10,000
INSERT INTO "permission_conditions" ("permission_id", "condition_type", "condition_config", "description", "priority")
SELECT 
  p.id,
  'amount',
  '{"min_amount": 0, "max_amount": 10000, "currency": "USD"}'::jsonb,
  'Basic loan creation limited to maximum $10,000',
  20
FROM "permissions" p
WHERE p.code = 'loan_create_basic'
AND NOT EXISTS (
  SELECT 1 FROM "permission_conditions" pc 
  WHERE pc.permission_id = p.id AND pc.condition_type = 'amount'
);

-- Amount-based condition: Advanced loan creation limited to $50,000
INSERT INTO "permission_conditions" ("permission_id", "condition_type", "condition_config", "description", "priority")
SELECT 
  p.id,
  'amount',
  '{"min_amount": 0, "max_amount": 50000, "currency": "USD"}'::jsonb,
  'Advanced loan creation limited to maximum $50,000',
  20
FROM "permissions" p
WHERE p.code = 'loan_create_advanced'
AND NOT EXISTS (
  SELECT 1 FROM "permission_conditions" pc 
  WHERE pc.permission_id = p.id AND pc.condition_type = 'amount'
);

-- Approval-based condition: High-value loan approvals require manager approval
INSERT INTO "permission_conditions" ("permission_id", "condition_type", "condition_config", "description", "priority")
SELECT 
  p.id,
  'approval',
  '{"requires_approval": true, "approver_roles": ["Branch Manager", "Company Administrator"], "approval_threshold": 25000, "auto_approve_below": 5000}'::jsonb,
  'Loan approvals above $25,000 require manager approval',
  30
FROM "permissions" p
WHERE p.code IN ('loan_approve_tier2', 'loan_approve_tier3')
AND NOT EXISTS (
  SELECT 1 FROM "permission_conditions" pc 
  WHERE pc.permission_id = p.id AND pc.condition_type = 'approval'
);

-- Location-based condition: Customer data export restricted to office networks
INSERT INTO "permission_conditions" ("permission_id", "condition_type", "condition_config", "description", "priority")
SELECT 
  p.id,
  'location',
  '{"allowed_ip_ranges": ["***********/24", "10.0.0.0/8"], "allowed_countries": ["US", "CA"], "blocked_countries": ["CN", "RU"], "require_vpn": true}'::jsonb,
  'Customer data export only allowed from office networks or VPN',
  15
FROM "permissions" p
WHERE p.code IN ('customer_export_basic', 'customer_export_financial')
AND NOT EXISTS (
  SELECT 1 FROM "permission_conditions" pc 
  WHERE pc.permission_id = p.id AND pc.condition_type = 'location'
);

-- Session-based condition: Financial operations require recent authentication
INSERT INTO "permission_conditions" ("permission_id", "condition_type", "condition_config", "description", "priority")
SELECT 
  p.id,
  'session',
  '{"max_session_age": 3600, "require_mfa": true, "max_idle_time": 1800, "require_fresh_auth": true}'::jsonb,
  'Financial operations require recent authentication and MFA',
  25
FROM "permissions" p
WHERE p.code IN ('payment_process_automated', 'payment_refund', 'payment_adjust')
AND NOT EXISTS (
  SELECT 1 FROM "permission_conditions" pc 
  WHERE pc.permission_id = p.id AND pc.condition_type = 'session'
);

-- Device-based condition: Mobile access restrictions for sensitive operations
INSERT INTO "permission_conditions" ("permission_id", "condition_type", "condition_config", "description", "priority")
SELECT 
  p.id,
  'device',
  '{"allowed_device_types": ["desktop", "laptop"], "blocked_device_types": ["mobile", "tablet"], "require_registered_device": true, "max_devices_per_user": 3}'::jsonb,
  'Sensitive operations restricted to desktop/laptop devices only',
  5
FROM "permissions" p
WHERE p.code IN ('loan_disburse_advanced', 'customer_export_financial', 'system_audit_logs')
AND NOT EXISTS (
  SELECT 1 FROM "permission_conditions" pc 
  WHERE pc.permission_id = p.id AND pc.condition_type = 'device'
);

-- Add comments for documentation
COMMENT ON TABLE "permission_conditions" IS 'Stores conditional rules for permissions that must be evaluated at runtime';
COMMENT ON COLUMN "permission_conditions"."condition_type" IS 'Type of condition: time, location, amount, approval, device, session';
COMMENT ON COLUMN "permission_conditions"."condition_config" IS 'JSON configuration for the condition with type-specific parameters';
COMMENT ON COLUMN "permission_conditions"."priority" IS 'Evaluation priority - higher numbers are evaluated first';
COMMENT ON COLUMN "permission_conditions"."is_active" IS 'Whether this condition is currently active and should be evaluated';

-- Create a view for easier querying of active conditions
CREATE OR REPLACE VIEW "active_permission_conditions" AS
SELECT 
  pc.*,
  p.code as permission_code,
  p.name as permission_name,
  p.category as permission_category
FROM "permission_conditions" pc
JOIN "permissions" p ON pc.permission_id = p.id
WHERE pc.is_active = true
ORDER BY pc.priority DESC, pc.created_at ASC;

COMMENT ON VIEW "active_permission_conditions" IS 'View of active permission conditions with permission details for easier querying';
