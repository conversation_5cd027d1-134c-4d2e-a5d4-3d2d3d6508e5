import { Express, Response } from 'express';
import { AuthRequest, authMiddleware } from '../middleware/auth';
import { db } from '../db';
import { branches, insertBranchSchema, userCompanies } from '../../shared/schema';
import { eq, and } from 'drizzle-orm';
import { ZodError } from 'zod';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to validate user's company role
async function getUserCompanyRole(userId: number, companyId: number) {
  const [userCompany] = await db.select()
    .from(userCompanies)
    .where(and(
      eq(userCompanies.user_id, userId),
      eq(userCompanies.company_id, companyId)
    ));

  return userCompany;
}

export function registerBranchRoutes(app: Express): void {
  console.log('🌿 [BRANCH_ROUTES] Registering branch routes...');

  // Test route to verify branch routes are working
  app.get('/api/branches/test', (req, res) => {
    console.log('🌿 [BRANCH_TEST] Test route called');
    res.json({ message: 'Branch routes are working!', timestamp: new Date().toISOString() });
  });

  // Create branch
  app.post('/api/branches', authMiddleware, async (req: AuthRequest, res: Response) => {
    console.log('🌿 [BRANCH_CREATE] POST /api/branches called');
    console.log('🌿 [BRANCH_CREATE] Request body:', req.body);
    console.log('🌿 [BRANCH_CREATE] User:', req.user?.id);

    try {
      const validatedData = insertBranchSchema.parse(req.body);
      console.log('🌿 [BRANCH_CREATE] Validated data:', validatedData);

      const branch = await db
        .insert(branches)
        .values({
          ...validatedData,
          company_id: Number(validatedData.company_id),
        })
        .returning();

      console.log('🌿 [BRANCH_CREATE] Branch created successfully:', branch[0]);
      res.status(201).json(branch[0]);
    } catch (error) {
      console.error('🌿 [BRANCH_CREATE] Error creating branch:', error);
      if (error instanceof ZodError) {
        res.status(400).json({ errors: formatZodError(error) });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  });

  // Get branches for a company
  app.get('/api/companies/:companyId/branches', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = Number(req.params.companyId);
      const userCompany = await getUserCompanyRole(req.user.id, companyId);

      if (!userCompany) {
        return res.status(403).json({ message: 'You do not have access to this company' });
      }

      const branchList = await db
        .select()
        .from(branches)
        .where(eq(branches.company_id, companyId));

      res.status(200).json(branchList);
    } catch (error) {
      console.error('Error fetching branches:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get single branch
  app.get('/api/branches/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.id);
      const [branch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, branch.company_id);

      if (!userCompany) {
        return res.status(403).json({ message: 'You do not have access to this branch' });
      }

      res.status(200).json(branch);
    } catch (error) {
      console.error('Error fetching branch:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Update branch (PATCH)
  app.patch('/api/branches/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.id);
      const [existingBranch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!existingBranch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, existingBranch.company_id);

      if (!userCompany || !['owner', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to update this branch' });
      }

      const updateData = req.body;
      const [updatedBranch] = await db
        .update(branches)
        .set(updateData)
        .where(eq(branches.id, branchId))
        .returning();

      res.status(200).json(updatedBranch);
    } catch (error) {
      console.error('Error updating branch:', error);
      if (error instanceof ZodError) {
        res.status(400).json({ errors: formatZodError(error) });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  });

  // Update branch (PUT) - same as PATCH for compatibility
  app.put('/api/branches/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.id);
      const [existingBranch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!existingBranch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, existingBranch.company_id);

      if (!userCompany || !['owner', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to update this branch' });
      }

      const updateData = req.body;
      const [updatedBranch] = await db
        .update(branches)
        .set(updateData)
        .where(eq(branches.id, branchId))
        .returning();

      res.status(200).json(updatedBranch);
    } catch (error) {
      console.error('Error updating branch:', error);
      if (error instanceof ZodError) {
        res.status(400).json({ errors: formatZodError(error) });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  });

  // Delete branch
  app.delete('/api/branches/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.id);
      const [existingBranch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!existingBranch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, existingBranch.company_id);

      if (!userCompany || !['owner', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to delete this branch' });
      }

      await db
        .delete(branches)
        .where(eq(branches.id, branchId));

      res.status(200).json({ message: 'Branch deleted successfully' });
    } catch (error) {
      console.error('Error deleting branch:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });
}
