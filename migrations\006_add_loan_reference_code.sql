-- Migration to add loan_reference_code column to loans table
-- This column will store company-specific loan identifiers

-- Add loan_reference_code column to loans table
ALTER TABLE "loans" 
  ADD COLUMN IF NOT EXISTS "loan_reference_code" TEXT;

-- Create an index for faster lookups by loan_reference_code
CREATE INDEX IF NOT EXISTS idx_loans_reference_code ON loans(loan_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN loans.loan_reference_code IS 'Company-specific loan identifier string (e.g., GS-001) that is unique within each company';
