import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { UserPermissionViewer } from '@/components/permissions';
import { useLocation } from 'wouter';

export default function UserPermissionsPage() {
  const [location, navigate] = useLocation();
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>();

  // Parse URL parameters reactively when location changes
  useEffect(() => {
    // Try both wouter location and window.location for better compatibility
    let searchParams;
    if (location.includes('?')) {
      searchParams = new URLSearchParams(location.split('?')[1]);
    } else {
      searchParams = new URLSearchParams(window.location.search);
    }

    const userIdParam = searchParams.get('userId');
    if (userIdParam) {
      const parsedUserId = parseInt(userIdParam);
      if (!isNaN(parsedUserId) && parsedUserId > 0) {
        setSelectedUserId(parsedUserId);
      }
    } else {
      setSelectedUserId(undefined);
    }
  }, [location]);

  return (
    <div className="container mx-auto py-6">
      {/* Header with Back Button */}
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate('/user-management')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">User Permissions</h1>
          <p className="text-muted-foreground">
            View and analyze individual user permissions
          </p>
        </div>
      </div>

      <UserPermissionViewer
        userId={selectedUserId}
        onUserSelect={setSelectedUserId}
      />
    </div>
  );
}
