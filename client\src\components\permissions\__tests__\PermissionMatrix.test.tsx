import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, createMockRole, createMockPermissionCategory, mockFetchSuccess } from '../../../../tests/utils';
import { PermissionMatrix } from '../PermissionMatrix';

describe('PermissionMatrix', () => {
  const mockOnPermissionChange = vi.fn();
  const mockOnBulkAssign = vi.fn();

  const defaultProps = {
    roles: [
      createMockRole({ id: 1, name: 'Admin', permissions: [1, 2] }),
      createMockRole({ id: 2, name: 'User', permissions: [1] }),
    ],
    permissionCategories: [
      createMockPermissionCategory({
        category: 'loans',
        metadata: { name: 'Loan Management', description: 'Loan operations', icon: 'CreditCard' },
        permissions: [
          { id: 1, code: 'loan_create', name: 'Create Loans', description: 'Create new loans', category: 'loans' },
          { id: 2, code: 'loan_approve', name: 'Approve Loans', description: 'Approve loan applications', category: 'loans' },
        ],
      }),
    ],
    onPermissionChange: mockOnPermissionChange,
    onBulkAssign: mockOnBulkAssign,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetchSuccess({});
  });

  describe('Rendering', () => {
    it('should render the permission matrix with roles and permissions', () => {
      render(<PermissionMatrix {...defaultProps} />);

      // Check if roles are displayed
      expect(screen.getByText('Admin')).toBeInTheDocument();
      expect(screen.getByText('User')).toBeInTheDocument();

      // Check if permission categories are displayed
      expect(screen.getByText('Loan Management')).toBeInTheDocument();

      // Check if permissions are displayed
      expect(screen.getByText('Create Loans')).toBeInTheDocument();
      expect(screen.getByText('Approve Loans')).toBeInTheDocument();
    });

    it('should show loading state when loading prop is true', () => {
      render(<PermissionMatrix {...defaultProps} loading={true} />);

      // The component should be disabled during loading
      const checkboxes = screen.getAllByRole('checkbox');
      checkboxes.forEach(checkbox => {
        expect(checkbox).toBeDisabled();
      });
    });

    it('should display correct permission states for roles', () => {
      render(<PermissionMatrix {...defaultProps} />);

      const checkboxes = screen.getAllByRole('checkbox');

      // Admin should have both permissions (indices 0 and 1)
      expect(checkboxes[0]).toBeChecked(); // loan_create for Admin
      expect(checkboxes[1]).toBeChecked(); // loan_approve for Admin

      // User should only have first permission (indices 2 and 3)
      expect(checkboxes[2]).toBeChecked(); // loan_create for User
      expect(checkboxes[3]).not.toBeChecked(); // loan_approve for User
    });
  });

  describe('Search Functionality', () => {
    it('should filter permissions based on search term', async () => {
      const user = userEvent.setup();
      render(<PermissionMatrix {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText(/search permissions/i);
      await user.type(searchInput, 'create');

      // Should show only permissions matching "create"
      expect(screen.getByText('Create Loans')).toBeInTheDocument();
      expect(screen.queryByText('Approve Loans')).not.toBeInTheDocument();
    });

    it('should show no results message when search yields no matches', async () => {
      const user = userEvent.setup();
      render(<PermissionMatrix {...defaultProps} />);

      const searchInput = screen.getByPlaceholderText(/search permissions/i);
      await user.type(searchInput, 'nonexistent');

      expect(screen.getByText(/no permissions found/i)).toBeInTheDocument();
    });
  });

  describe('Category Management', () => {
    it('should allow expanding and collapsing categories', async () => {
      const user = userEvent.setup();
      render(<PermissionMatrix {...defaultProps} />);

      const categoryHeader = screen.getByText('Loan Management');

      // Initially expanded, permissions should be visible
      expect(screen.getByText('Create Loans')).toBeInTheDocument();

      // Click to collapse
      await user.click(categoryHeader);

      // Permissions should be hidden (this depends on implementation)
      // Note: The actual behavior depends on how the component handles collapsed state
    });

    it('should filter by category when category is selected', async () => {
      const multiCategoryProps = {
        ...defaultProps,
        permissionCategories: [
          ...defaultProps.permissionCategories,
          createMockPermissionCategory({
            category: 'customers',
            metadata: { name: 'Customer Management', description: 'Customer operations', icon: 'Users' },
            permissions: [
              { id: 3, code: 'customer_view', name: 'View Customers', description: 'View customer data', category: 'customers' },
            ],
          }),
        ],
      };

      render(<PermissionMatrix {...multiCategoryProps} />);

      // All permissions should be visible initially
      expect(screen.getByText('Create Loans')).toBeInTheDocument();
      expect(screen.getByText('View Customers')).toBeInTheDocument();

      // Filter by loans category (implementation specific)
      // This would require clicking on a category filter if implemented
    });
  });

  describe('Permission Changes', () => {
    it('should call onPermissionChange when checkbox is clicked', async () => {
      const user = userEvent.setup();
      render(<PermissionMatrix {...defaultProps} />);

      const checkboxes = screen.getAllByRole('checkbox');

      // Click on unchecked permission (User's loan_approve permission)
      await user.click(checkboxes[3]);

      await waitFor(() => {
        expect(mockOnPermissionChange).toHaveBeenCalledWith(2, 2, true);
      });
    });

    it('should handle permission change errors gracefully', async () => {
      const user = userEvent.setup();
      const failingOnPermissionChange = vi.fn().mockRejectedValue(new Error('Permission change failed'));

      render(<PermissionMatrix {...defaultProps} onPermissionChange={failingOnPermissionChange} />);

      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[3]);

      await waitFor(() => {
        expect(failingOnPermissionChange).toHaveBeenCalled();
      });

      // Should show error message (depends on implementation)
      // expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });

  describe('Bulk Operations', () => {
    it('should handle bulk assignment when onBulkAssign is provided', async () => {
      const user = userEvent.setup();
      render(<PermissionMatrix {...defaultProps} />);

      // Look for bulk operation controls (implementation specific)
      // This test assumes there are bulk operation buttons/controls
      const bulkButtons = screen.queryAllByText(/bulk/i);

      if (bulkButtons.length > 0) {
        await user.click(bulkButtons[0]);

        await waitFor(() => {
          expect(mockOnBulkAssign).toHaveBeenCalled();
        });
      }
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for checkboxes', () => {
      render(<PermissionMatrix {...defaultProps} />);

      const checkboxes = screen.getAllByRole('checkbox');
      checkboxes.forEach(checkbox => {
        expect(checkbox).toHaveAttribute('aria-label');
      });
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<PermissionMatrix {...defaultProps} />);

      const firstCheckbox = screen.getAllByRole('checkbox')[0];
      firstCheckbox.focus();

      // Test Tab navigation
      await user.keyboard('{Tab}');

      // Should move focus to next interactive element
      expect(document.activeElement).not.toBe(firstCheckbox);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty roles array', () => {
      render(<PermissionMatrix {...defaultProps} roles={[]} />);

      expect(screen.getByText(/no roles/i)).toBeInTheDocument();
    });

    it('should handle empty permission categories', () => {
      render(<PermissionMatrix {...defaultProps} permissionCategories={[]} />);

      expect(screen.getByText(/no permissions/i)).toBeInTheDocument();
    });

    it('should handle roles without permissions', () => {
      const rolesWithoutPermissions = [
        createMockRole({ id: 1, name: 'Empty Role', permissions: [] }),
      ];

      render(<PermissionMatrix {...defaultProps} roles={rolesWithoutPermissions} />);

      const checkboxes = screen.getAllByRole('checkbox');
      checkboxes.forEach(checkbox => {
        expect(checkbox).not.toBeChecked();
      });
    });
  });
});
