#!/usr/bin/env node
/**
 * Test Script: Collection ID Generation
 * Purpose: Test script to demonstrate the collection ID generation behavior
 * Usage: node tools/test-collection-id.js
 */

import { withDatabasePool } from '../scripts/utils/database-connection.js';
import { sql, eq, and, like } from 'drizzle-orm';

// Mock implementation of getCompanyName function
async function getCompanyName(pool, companyId) {
  try {
    // Query the companies table to get the company name
    const result = await pool.query('SELECT name FROM companies WHERE id = $1', [companyId]);
    const company = result.rows[0];

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);

    let prefix = '';
    if (words.length === 0) {
      prefix = `C${companyId}`;
    } else if (words.length === 1) {
      // If only one word, use first and last letter of that word
      const word = words[0];
      prefix = word.length > 1
        ? (word.charAt(0) + word.charAt(word.length - 1)).toUpperCase()
        : word.toUpperCase() + companyId;
    } else {
      // If multiple words, use first letter of first word and first letter of second word
      prefix = (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
    }
    
    return `${prefix}-`;
  } catch (error) {
    console.error(`Error fetching company name for ID ${companyId}:`, error);
    // Return a fallback value in case of error
    return `C${companyId}-`;
  }
}

// Mock implementation of getHighestCollectionSerial function
async function getHighestCollectionSerial(pool, companyId, prefix) {
  try {
    // Find the max serial for this company and prefix
    // company_collection_string is like 'GS-001', 'GS-002', ...
    const result = await pool.query(`
      SELECT MAX(company_collection_string) as max_string
      FROM collections
      WHERE company_id = $1 AND company_collection_string LIKE $2
    `, [companyId, `${prefix}%`]);
    
    const maxString = result.rows[0]?.max_string;
    if (!maxString) return 0;
    
    // Extract the serial part (e.g., 'GS-012' => 12)
    const match = maxString.match(/^(.*-)(\d{3})$/);
    if (match) {
      return parseInt(match[2], 10);
    }
    return 0;
  } catch (error) {
    console.error(`Error getting highest collection serial for company ${companyId} and prefix ${prefix}:`, error);
    return 0;
  }
}

// Test function to demonstrate the behavior
async function testCollectionIdGeneration() {
  await withDatabasePool(async (pool) => {
    console.log('=== COLLECTION ID GENERATION TEST ===\n');
    
    // Test with two companies that would have the same prefix
    const company1Id = 1; // Assuming this is "GOVINDARAJI S"
    const company2Id = 2; // Assuming this is "GOODS SOLUTIONS"
    
    // Get prefixes for both companies
    const prefix1 = await getCompanyName(pool, company1Id);
    const prefix2 = await getCompanyName(pool, company2Id);
    
    console.log(`Company 1 (ID: ${company1Id}) prefix: ${prefix1}`);
    console.log(`Company 2 (ID: ${company2Id}) prefix: ${prefix2}`);
    
    // Get highest serials for both companies
    const highestSerial1 = await getHighestCollectionSerial(pool, company1Id, prefix1);
    const highestSerial2 = await getHighestCollectionSerial(pool, company2Id, prefix2);
    
    console.log(`Company 1 highest serial: ${highestSerial1}`);
    console.log(`Company 2 highest serial: ${highestSerial2}`);
    
    // Generate next collection IDs for both companies
    const nextSerial1 = (highestSerial1 + 1).toString().padStart(3, '0');
    const nextSerial2 = (highestSerial2 + 1).toString().padStart(3, '0');
    
    const nextCollectionId1 = `${prefix1}${nextSerial1}`;
    const nextCollectionId2 = `${prefix2}${nextSerial2}`;
    
    console.log(`Next collection ID for Company 1: ${nextCollectionId1}`);
    console.log(`Next collection ID for Company 2: ${nextCollectionId2}`);
    
    console.log('\n=== TEST ANALYSIS ===');
    if (prefix1 === prefix2) {
      console.log('⚠️  WARNING: Both companies have the same prefix!');
      console.log('   This could lead to collection ID conflicts.');
      console.log('   Consider using company ID in prefix generation.');
    } else {
      console.log('✅ Companies have different prefixes - no conflicts expected.');
    }
    
    console.log('\n=== RECOMMENDATIONS ===');
    console.log('1. Ensure company names are unique enough to generate distinct prefixes');
    console.log('2. Consider fallback logic for companies with similar names');
    console.log('3. Test with actual company data to verify uniqueness');
    console.log('4. Monitor for prefix collisions in production');
  });
}

// Run the test
testCollectionIdGeneration().catch(console.error);
