import { useCallback, useEffect, useState } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { User, Phone, Mail, MapPin, ArrowLeft, Edit, Trash, FileText } from "lucide-react";
import { Helmet } from "react-helmet";
import { useAuth } from "@/lib/auth";
import { useContextData } from "@/lib/useContextData";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useIsMobile } from "@/hooks/use-is-mobile";
import { getInitials } from "@/lib/utils";

interface Customer {
  id: number;
  company_id: number;
  full_name: string;
  email: string;
  phone: string;
  address: string;
  profile_image?: string;
  credit_score?: number;
  kyc_verified?: boolean;
  active: boolean;
  notes?: string;
  // These will be fetched separately
  loans_count?: number;
  active_loans_count?: number;
  overdue_loans_count?: number;
}

export default function CustomerDetail() {
  const { id } = useParams();
  const customerId = parseInt(id, 10);
  const [, navigate] = useLocation();
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const { companyId } = useContextData();
  const isMobile = useIsMobile();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const { data: customer, isLoading, isError, error, refetch } = useQuery<Customer>({
    queryKey: [`/api/customers/${customerId}?companyId=${companyId}`],
    queryFn: async () => {
      if (!customerId) throw new Error("Customer ID is required");

      console.log(`Fetching customer ${customerId} with company context ${companyId}`);
      const response = await apiRequest('GET', `/api/customers/${customerId}?companyId=${companyId}`);
      return await response.json();
    },
    enabled: !!customerId && !!companyId,
    retry: 1, // Only retry once to avoid excessive requests on permission errors
    staleTime: 0, // Consider data always stale to force refetch
    cacheTime: 1000, // Short cache time to ensure fresh data
    refetchOnMount: true, // Refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gets focus
    onError: (err: any) => {
      console.error('Error fetching customer:', err);
      // Show a toast with the error message
      toast({
        title: "Error",
        description: err?.message || "Failed to load customer details",
        variant: "destructive",
      });
    }
  });

  // Force refetch when navigating back to this page
  useEffect(() => {
    // This ensures we get fresh data after editing
    refetch();
  }, [refetch, customerId, companyId]);

  const { data: loans = [] } = useQuery<any[]>({
    queryKey: [`/api/customers/${customerId}/loans?companyId=${companyId}`],
    queryFn: async () => {
      if (!customerId) return [];

      console.log(`Fetching loans for customer ${customerId} with company context ${companyId}`);
      const response = await apiRequest('GET', `/api/customers/${customerId}/loans?companyId=${companyId}`);
      return await response.json();
    },
    enabled: !!customerId && !!companyId,
  });

  const getCreditScoreColor = (score?: number) => {
    if (!score) return "bg-gray-200";
    if (score >= 750) return "bg-green-500";
    if (score >= 650) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Helper function to capitalize loan status
  const capitalizeStatus = (status?: string) => {
    if (!status) return '';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };
  const deleteCustomerMutation = useMutation({
    mutationFn: async () => {
      console.log(`Deleting customer ${customerId} with company context ${companyId}`);
      const response = await fetch(`/api/customers/${customerId}?companyId=${companyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        credentials: "include",
      });

      if (!response.ok) {
        // Parse the JSON error response
        const errorData = await response.json();
        // Extract just the error message
        throw new Error(errorData.message || "Failed to delete customer");
      }

      return await response.json();
    },
    onSuccess: (data) => {
      console.log('Delete customer success:', data);
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/customers`] });
      toast({
        title: "Success",
        description: "Customer deleted successfully",
      });
      navigate("/customers");
    },
    onError: (error: any) => {
      console.error("Error deleting customer:", error);
      toast({
        title: "Error",
        description: error?.message || "Failed to delete customer. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleDeleteCustomer = () => {
    deleteCustomerMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-pulse text-center">
          <p className="text-muted-foreground">Loading customer details...</p>
        </div>
      </div>
    );
  }

  if (isError || !customer) {
    // Get a more specific error message if available
    let errorMessage = "The customer you're looking for doesn't exist or you don't have permission to view it.";

    if (error instanceof Error) {
      if (error.message.includes("403") || error.message.includes("Access denied")) {
        errorMessage = "You don't have permission to view this customer. It may belong to a different company.";
      } else if (error.message.includes("404") || error.message.includes("not found")) {
        errorMessage = "This customer could not be found. It may have been deleted.";
      }
    }

    return (
      <div className="flex flex-col items-center justify-center h-96 gap-4">
        <h2 className="text-2xl font-semibold">Customer not found</h2>
        <p className="text-muted-foreground">
          {errorMessage}
        </p>
        <Button onClick={() => navigate("/customers")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to customers
        </Button>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{customer.full_name} | Customer Profile | TrackFina</title>
      </Helmet>

      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/customers")}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <h1 className="text-2xl font-semibold">Customer Profile</h1>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => navigate(`/customers/${customer.id}/edit`)}
              className="gap-1"
            >
              <Edit className="h-4 w-4" />
              <span>Edit</span>
            </Button>

            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="gap-1">
                  <Trash className="h-4 w-4" />
                  <span>Delete</span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently delete {customer.full_name}'s profile and all associated data.
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteCustomer}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    disabled={deleteCustomerMutation.isPending}
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Profile Card */}
        <Card className="md:col-span-1">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="h-24 w-24">
                <AvatarFallback className="text-xl">
                  {getInitials(customer.full_name)}
                </AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-2xl font-semibold mb-1">{customer.full_name}</CardTitle>
            <div className="flex justify-center gap-2 mt-2">
              <Badge variant={customer.active ? "default" : "secondary"}>
                {customer.active ? "Active" : "Inactive"}
              </Badge>
              {customer.kyc_verified && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  KYC Verified
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{customer.email || "Not provided"}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Phone className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Phone</p>
                  <p className="font-medium">{customer.phone || "Not provided"}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm text-muted-foreground">Address</p>
                  <p className="font-medium">{customer.address || "Not provided"}</p>
                </div>
              </div>
              {customer.credit_score && (
                <div className="flex items-start gap-3">
                  <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Credit Score</p>
                    <div className="flex items-center gap-2">
                      <span
                        className={`h-3 w-3 rounded-full ${getCreditScoreColor(customer.credit_score)}`}
                      />
                      <p className="font-medium">{customer.credit_score}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {customer.notes && (
              <div className="mt-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Notes</h3>
                <p className="text-sm border rounded-md p-3 bg-muted/50">{customer.notes}</p>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => navigate(`/customers/${customer.id}/loans`)}
            >
              <FileText className="h-4 w-4 mr-2" />
              View Loans
            </Button>
          </CardFooter>
        </Card>

        {/* Tab content card */}
        <div className="md:col-span-2">
          <Tabs defaultValue="overview" className="w-full">
            <Card>
              <CardHeader>
                <TabsList className="w-full grid grid-cols-3">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="loans">Loans ({loans.length || 0})</TabsTrigger>
                  <TabsTrigger value="activity">Activity</TabsTrigger>
                </TabsList>
              </CardHeader>
              <CardContent>
                <TabsContent value="overview" className="mt-0 space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Loan Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-center">
                            <p className="text-muted-foreground">Total Loans</p>
                            <p className="text-2xl font-bold">{loans.length || 0}</p>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-center">
                            <p className="text-muted-foreground">Active Loans</p>
                            <p className="text-2xl font-bold">{loans.filter(l => l.status === 'active').length || 0}</p>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-center">
                            <p className="text-muted-foreground">Overdue</p>
                            <p className="text-2xl font-bold">{loans.filter(l => l.status === 'overdue').length || 0}</p>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <Button onClick={() => navigate(`/loans/create?customer_id=${customer.id}&source=customer&return_to=${encodeURIComponent(`/customers/${customer.id}`)}`)}>
                        Create New Loan
                      </Button>
                      <Button variant="outline" onClick={() => navigate(`/collections?customer_id=${customer.id}`)}>
                        View Collections
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="loans">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold">Loan History</h3>
                      <Button
                        size="sm"
                        onClick={() => navigate(`/loans/create?customer_id=${customer.id}&source=customer&return_to=${encodeURIComponent(`/customers/${customer.id}`)}`)}
                      >
                        New Loan
                      </Button>
                    </div>

                    {loans.length === 0 ? (
                      <div className="text-center py-10 border rounded-md bg-muted/50">
                        <p className="text-muted-foreground">No loans found for this customer</p>
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={() => navigate(`/loans/create?customer_id=${customer.id}&source=customer&return_to=${encodeURIComponent(`/customers/${customer.id}`)}`)}
                        >
                          Create First Loan
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {loans.map((loan: any) => (
                          <Card key={loan.id} className="overflow-hidden">
                            <CardContent className="p-0">
                              <div className="flex flex-col sm:flex-row sm:items-center justify-between p-4">
                                <div>
                                  <p className="font-semibold">{loan.loan_reference_code || `Loan #${loan.id}`}</p>
                                  <p className="text-sm text-muted-foreground">
                                    {new Date(loan.start_date).toLocaleDateString()} -
                                    {new Date(loan.end_date).toLocaleDateString()}
                                  </p>
                                </div>
                                <div className="mt-2 sm:mt-0">
                                  <Badge className={
                                    loan.status === 'active'
                                      ? 'bg-green-100 text-green-800 hover:bg-green-100'
                                      : loan.status === 'overdue'
                                        ? 'bg-red-100 text-red-800 hover:bg-red-100'
                                        : loan.status === 'completed'
                                          ? 'bg-blue-100 text-blue-800 hover:bg-blue-100'
                                          : ''
                                  }>
                                    {capitalizeStatus(loan.status)}
                                  </Badge>
                                </div>
                                <div className="mt-2 sm:mt-0">
                                  <p className="text-lg font-bold">${loan.amount.toLocaleString()}</p>
                                  <p className="text-sm text-muted-foreground">{loan.interest_rate}% {loan.interest_type}</p>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => navigate(`/loans/${loan.id}`)}
                                >
                                  View
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="activity">
                  <div className="text-center py-10">
                    <p className="text-muted-foreground">Activity history will be available soon</p>
                  </div>
                </TabsContent>
              </CardContent>
            </Card>
          </Tabs>
        </div>
      </div>
    </>
  );
}