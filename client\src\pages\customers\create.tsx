import React from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/lib/auth";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { DirectCustomerForm } from "@/components/customer/DirectCustomerForm";

export default function CreateCustomerPage() {
  const { user } = useAuth();
  const [, navigate] = useLocation();

  // Get the company ID from the user context
  // If no company context is available, show an error message
  if (!user?.company_id) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="p-4 bg-red-50 text-red-700 rounded-md">
          <h2 className="font-semibold text-lg mb-2">Company Context Required</h2>
          <p>You need to select a company before creating customers.</p>
          <Button
            variant="outline"
            onClick={() => navigate("/dashboard")}
            className="mt-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Return to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const companyId = user.company_id;

  // Handle successful customer creation
  const handleCustomerCreated = (customer: any) => {
    console.log('Customer created callback received:', customer);
    // Navigate to customers list regardless of the response structure
    // This ensures we always redirect even if the response format changes
    navigate("/customers");
  };

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => navigate("/customers")}
            className="text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Customers
          </Button>
          <h1 className="text-2xl font-bold">
            Create New Customer
          </h1>
        </div>
      </div>

      <DirectCustomerForm
        companyId={companyId}
        onSuccess={handleCustomerCreated}
        onCancel={() => navigate("/customers")}
      />
    </div>
  );
}