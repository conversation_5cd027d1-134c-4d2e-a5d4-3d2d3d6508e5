import { db } from '../db';
import { eq } from 'drizzle-orm';
import { companies, users } from '@shared/schema';
import { Company, InsertCompany, User } from '@shared/schema';
import errorLogger from '../utils/errorLogger';
import { ICompanyStorage } from './interfaces';

export class CompanyStorage implements ICompanyStorage {
  async getCompany(id: number): Promise<Company | undefined> {
    try {
      const [company] = await db.select()
        .from(companies)
        .where(eq(companies.id, id));
      return company;
    } catch (error) {
      errorLogger.logError(`Error fetching company id=${id}`, 'company-fetch', error as Error);
      return undefined;
    }
  }

  async getCompanies(): Promise<Company[]> {
    try {
      const allCompanies = await db.select().from(companies);
      return allCompanies;
    } catch (error) {
      errorLogger.logError('Error fetching all companies', 'companies-fetch', error as Error);
      return [];
    }
  }

  async createCompany(companyData: InsertCompany): Promise<Company> {
    try {
      const [company] = await db.insert(companies)
        .values(companyData)
        .returning();

      return company;
    } catch (error) {
      errorLogger.logError(`Error creating company`, 'company-create', error as Error);
      throw error;
    }
  }

  async updateCompany(id: number, companyData: Partial<InsertCompany>): Promise<Company> {
    try {
      const [updatedCompany] = await db.update(companies)
        .set(companyData)
        .where(eq(companies.id, id))
        .returning();

      return updatedCompany;
    } catch (error) {
      errorLogger.logError(`Error updating company id=${id}`, 'company-update', error as Error);
      throw error;
    }
  }

  async deleteCompany(id: number): Promise<{ success: boolean, error?: string, customersCount?: number }> {
    try {
      // Check if the company has any customers
      const companyCustomers = await db.select()
        .from(customers)
        .where(eq(customers.company_id, id));

      if (companyCustomers.length > 0) {
        return {
          success: false,
          error: 'Cannot delete company with associated customers. Please delete all customers first.',
          customersCount: companyCustomers.length
        };
      }

      // Delete the company
      await db.delete(companies)
        .where(eq(companies.id, id));

      return { success: true };
    } catch (error) {
      errorLogger.logError(`Error deleting company id=${id}`, 'company-delete', error as Error);
      return {
        success: false,
        error: `Error deleting company: ${(error as Error).message}`
      };
    }
  }

  async getCompanyUsers(companyId: number): Promise<User[]> {
    try {
      const companyUsers = await db.select()
        .from(users)
        .where(eq(users.company_id, companyId));
      return companyUsers;
    } catch (error) {
      errorLogger.logError(`Error fetching users for company id=${companyId}`, 'company-users-fetch', error as Error);
      return [];
    }
  }
}
