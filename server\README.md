# Financial Tracker Server

This is the server component of the Financial Tracker application. It provides a RESTful API for managing loans, collections, payments, and financial data.

## Project Structure

The server is organized into a modular structure:

```
server/
├── routes/              # API route handlers
│   ├── index.ts         # Main router that combines all route modules
│   ├── auth.routes.ts   # Authentication routes
│   ├── user.routes.ts   # User management routes
│   ├── company.routes.ts # Company management routes
│   └── ...              # Other route modules
├── storage/             # Data storage and retrieval
│   ├── index.ts         # Main storage interface and implementation
│   ├── interfaces.ts    # Define all storage interfaces
│   ├── user.storage.ts  # User-related storage operations
│   ├── company.storage.ts # Company-related storage operations
│   └── ...              # Other storage modules
├── middleware/          # Express middleware
│   ├── auth.ts          # Authentication middleware
│   └── ...              # Other middleware
├── utils/               # Utility functions
│   ├── jwt.ts           # JWT token utilities
│   ├── errorLogger.ts   # Error logging utilities
│   └── ...              # Other utilities
├── db.ts                # Database connection and configuration
├── financialManagement.ts # Financial management functions
├── index.ts             # Server entry point
└── routes.ts            # Legacy routes file (being refactored)
```

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- PostgreSQL database

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables in a `.env` file:
   ```
   DATABASE_URL=postgres://username:password@localhost:5432/financial_tracker
   JWT_SECRET=your_jwt_secret
   ```
4. Start the development server:
   ```
   npm run dev
   ```

## API Documentation

### Authentication

- `POST /api/auth/login` - Log in a user
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/refresh-token` - Refresh authentication token

### Users

- `GET /api/users/me` - Get current user
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create a new user (admin only)
- `PUT /api/users/:id` - Update a user
- `POST /api/users/:id/change-password` - Change user password

### Companies

- `GET /api/companies` - Get all companies (admin only)
- `GET /api/companies/:id` - Get company by ID
- `POST /api/companies` - Create a new company (admin only)
- `PUT /api/companies/:id` - Update a company
- `DELETE /api/companies/:id` - Delete a company (admin only)
- `GET /api/companies/:id/users` - Get company users

## Development

### Adding a New Module

1. Define the interface in `storage/interfaces.ts`
2. Create a new storage implementation file (e.g., `storage/new-module.storage.ts`)
3. Create a new routes file (e.g., `routes/new-module.routes.ts`)
4. Update `storage/index.ts` to use the new storage implementation
5. Update `routes/index.ts` to register the new routes

### Testing

Run tests with:
```
npm test
```

## Modular Architecture

The application has been refactored from a monolithic structure into a modular architecture. Each module is self-contained with its own storage and routes implementations. See `REFACTORING.md` for details on the refactoring approach and benefits.

The following modules have been implemented:

1. **Authentication Module** - User authentication and authorization
2. **User Module** - User management
3. **Company Module** - Company management
4. **User-Company Module** - User-company relationships
5. **Customer Module** - Customer management
6. **Loan Module** - Loan management
7. **Payment Schedule Module** - Payment schedule generation and management
8. **Collection Module** - Collection management
9. **Payment Module** - Payment processing
10. **Financial Management Module** - Accounts, transactions, and reporting

## Contributing

1. Create a feature branch
2. Make your changes
3. Write tests for your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
