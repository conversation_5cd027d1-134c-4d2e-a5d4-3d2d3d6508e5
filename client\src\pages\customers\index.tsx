import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2, Plus, ListFilter, Table as TableIcon, GridIcon } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Advanced search component
import { AdvancedCustomerSearch, type SearchFilters } from "@/components/customer/AdvancedCustomerSearch";
import { CustomerProfileCard } from "@/components/customer/CustomerProfileCard";

import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { getInitials } from "@/lib/utils";

const COUNTRY_CODE = '+91'; // Global constant for country code

// Customer schema for form validation
const customerSchema = z.object({
  company_id: z.number(),
  full_name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  phone: z.string().refine(
    (value) => {
      // Accept only format with country code followed by exactly 10 digits
      // Escape the + in the country code for regex
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      return pattern.test(value);
    },
    { message: `Phone number must be exactly 10 digits with ${COUNTRY_CODE} country code` }
  ),
  email: z.string()
    .refine(
      (value) => {
        // If empty, it's valid (since email is optional)
        if (!value) return true;

        // Email regex pattern for validation
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        return emailPattern.test(value);
      },
      { message: "Please enter a valid email address (e.g., <EMAIL>)" }
    )
    .optional()
    .or(z.literal("")),
  address: z.string().optional().or(z.literal("")),
  credit_score: z.coerce.number().min(0).max(1000).optional(),
  active: z.boolean().default(true),
  kyc_verified: z.boolean().default(false),
  notes: z.string().optional().or(z.literal(""))
});

type CustomerFormValues = z.infer<typeof customerSchema>;

interface Customer {
  id: number;
  company_id: number;
  full_name: string;
  email: string;
  phone: string;
  address: string;
  profile_image: string;
  credit_score: number;
  kyc_verified?: boolean;
  active: boolean;
  notes?: string;
  customer_reference_code?: string;
  // We'll simulate these properties for now
  loans_count?: number;
  active_loans_count?: number;
  overdue_loans_count?: number;
}

export default function Customers() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, setLocation] = useLocation();
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    query: "",
    searchBy: "all",
    location: "",
    creditScoreRange: null,
    status: "all",
    loanStatus: "all"
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAddSheetOpen, setIsAddSheetOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "table">("grid");

  // Create form
  const createForm = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      company_id: user?.company_id,
      full_name: "",
      phone: "",
      email: "",
      address: "",
      credit_score: undefined,
      active: true,
      kyc_verified: false,
      notes: ""
    }
  });

  // Get the current company ID from user context
  const companyId = user?.company_id;

  // Track when company ID changes to invalidate queries
  const [currentCompanyId, setCurrentCompanyId] = useState<number | undefined>(companyId);

  // Update current company ID when user context changes
  useEffect(() => {
    if (companyId !== currentCompanyId) {
      setCurrentCompanyId(companyId);
      console.log(`Company changed from ${currentCompanyId} to ${companyId}, refreshing customer data`);
    }
  }, [companyId, currentCompanyId]);

  const {
    data: customersData,
    isLoading,
    error: queryError,
    refetch
  } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${companyId}/customers`],
    enabled: !!companyId
  });

  // Refetch when company changes
  useEffect(() => {
    if (companyId) {
      console.log(`Querying customers for company ID: ${companyId}`);
      refetch();
    }
  }, [companyId, refetch]);

  // Handle errors
  useEffect(() => {
    if (queryError) {
      console.error("Error fetching customers:", queryError);

      // Check for access denied errors
      const errorResponse = (queryError as any).response;
      if (errorResponse?.status === 403) {
        console.warn("Access denied to company:", companyId);
      }
    }
  }, [queryError, companyId]);

  // Enhanced filtering based on advanced search filters
  const filteredCustomers = customersData ? customersData.filter(customer => {
    // Basic text search
    const textMatch = searchFilters.query === "" || (
      (searchFilters.searchBy === "all" || searchFilters.searchBy === "name") &&
      customer.full_name.toLowerCase().includes(searchFilters.query.toLowerCase())
    ) || (
        (searchFilters.searchBy === "all" || searchFilters.searchBy === "email") &&
        (customer.email?.toLowerCase() || "").includes(searchFilters.query.toLowerCase())
      ) || (
        (searchFilters.searchBy === "all" || searchFilters.searchBy === "phone") &&
        (customer.phone || "").includes(searchFilters.query)
      ) || (
        (searchFilters.searchBy === "id") &&
        customer.id.toString().includes(searchFilters.query)
      );

    // Location filter
    const locationMatch = searchFilters.location === "" ||
      (customer.address?.toLowerCase() || "").includes(searchFilters.location.toLowerCase());

    // Status filter
    const statusMatch = searchFilters.status === "all" ||
      (searchFilters.status === "active" && customer.active !== false) ||
      (searchFilters.status === "inactive" && customer.active === false) ||
      (searchFilters.status === "verified" && customer.kyc_verified === true) ||
      (searchFilters.status === "unverified" && customer.kyc_verified !== true);

    // Credit score filter
    const creditScoreMatch = !searchFilters.creditScoreRange || (
      customer.credit_score &&
      customer.credit_score >= (searchFilters.creditScoreRange[0] || 0) &&
      customer.credit_score <= (searchFilters.creditScoreRange[1] || 1000)
    );

    // For now, we'll return all customers that match the text search and additional filters
    // Loan status filter would be implemented when we have loan data
    return textMatch && locationMatch && statusMatch && creditScoreMatch;
  }) : [];

  // Function to enhance customer data with real loan information from API
  const {
    data: allLoans = [],
    refetch: refetchLoans
  } = useQuery<any[]>({
    queryKey: [`/api/companies/${companyId}/loans`],
    enabled: !!companyId,
  });

  // Refetch loans when company changes
  useEffect(() => {
    if (companyId) {
      console.log(`Querying loans for company ID: ${companyId}`);
      refetchLoans();
    }
  }, [companyId, refetchLoans]);

  const enhanceCustomerData = (customers: Customer[]): Customer[] => {
    return customers.map(customer => {
      // Get all loans for this customer
      const customerLoans = allLoans.filter(loan => loan.customer_id === customer.id);

      // Calculate loan counts
      const loansCount = customerLoans.length;
      const activeLoansCount = customerLoans.filter(loan => loan.status === 'active').length;
      const overdueLoansCount = customerLoans.filter(loan => loan.status === 'overdue').length;

      return {
        ...customer,
        loans_count: loansCount,
        active_loans_count: activeLoansCount,
        overdue_loans_count: overdueLoansCount,
      };
    });
  };

  // Sort customers in descending order by ID and then enhance the data
  const enhancedCustomers = enhanceCustomerData(
    [...filteredCustomers].sort((a, b) => b.id - a.id)
  );

  const handleAdvancedSearch = (filters: SearchFilters) => {
    setSearchFilters(filters);
  };

  // Create customer mutation
  const createCustomerMutation = useMutation({
    mutationFn: async (data: CustomerFormValues) => {
      const response = await apiRequest("POST", "/api/customers", data);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/customers`] });
      setIsAddSheetOpen(false);
      createForm.reset();
      toast({
        title: "Success",
        description: "Customer created successfully",
      });
    },
    onError: (error) => {
      console.error("Error creating customer:", error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Form submission handler
  const handleCreateCustomer = (data: CustomerFormValues) => {
    createCustomerMutation.mutate(data);
  };

  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Customers</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your customer information and loans
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as "grid" | "table")} className="hidden md:block">
            <TabsList className="grid w-[120px] grid-cols-2">
              <TabsTrigger value="grid" className="px-2">
                <GridIcon className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="table" className="px-2">
                <TableIcon className="h-4 w-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button
            className="flex items-center gap-1"
            onClick={() => {
              createForm.reset({
                company_id: user?.company_id,
                full_name: "",
                phone: "",
                email: "",
                address: "",
                credit_score: undefined,
                active: true,
                kyc_verified: false,
                notes: ""
              });
              setIsAddSheetOpen(true);
            }}
          >
            <Plus size={16} />
            <span>Add Customer</span>
          </Button>
        </div>
      </div>

      {/* Advanced Search */}
      <AdvancedCustomerSearch onSearch={handleAdvancedSearch} isLoading={isLoading} />

      {/* Customers List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between py-3 px-4">
          <div>
            <CardTitle className="text-base">Customers List</CardTitle>
            <CardDescription className="text-xs">
              {enhancedCustomers.length} {enhancedCustomers.length === 1 ? 'customer' : 'customers'} found
            </CardDescription>
          </div>
          <div className="md:hidden">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => setViewMode(viewMode === "grid" ? "table" : "grid")}
            >
              {viewMode === "grid" ? <TableIcon className="h-4 w-4" /> : <GridIcon className="h-4 w-4" />}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : enhancedCustomers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No customers found matching your search criteria.
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {enhancedCustomers.map((customer) => (
                <CustomerProfileCard
                  key={customer.id}
                  id={customer.id}
                  full_name={customer.full_name}
                  email={customer.email}
                  phone={customer.phone}
                  address={customer.address}
                  profile_image={customer.profile_image}
                  credit_score={customer.credit_score}
                  kyc_verified={customer.kyc_verified}
                  active={customer.active}
                  customer_reference_code={customer.customer_reference_code}
                  loansCount={customer.loans_count}
                  activeLoansCount={customer.active_loans_count}
                  overdueLoansCount={customer.overdue_loans_count}
                />
              ))}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Loans</TableHead>
                    <TableHead>Credit Score</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {enhancedCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <Avatar className="h-10 w-10 mr-4">
                            <AvatarFallback className="bg-accent text-white">
                              {getInitials(customer.full_name)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{customer.full_name}</div>
                            <div className="text-sm text-muted-foreground">
                              {customer.customer_reference_code ?
                                `ID: ${customer.customer_reference_code}` :
                                `ID: CUST-${customer.id}`
                              }
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{customer.email || "No email"}</div>
                          <div>{customer.phone || "No phone"}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Badge variant={customer.active ? "default" : "secondary"} className={customer.active ? "bg-green-500 hover:bg-green-400" : ""}>
                            {customer.active ? "Active" : "Inactive"}
                          </Badge>
                          {customer.kyc_verified !== undefined && (
                            <div>
                              <Badge variant="outline" className={customer.kyc_verified ? "bg-green-50" : "bg-amber-50"}>
                                {customer.kyc_verified ? "KYC Verified" : "Unverified"}
                              </Badge>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>Total: {customer.loans_count || 0}</div>
                          {customer.active_loans_count !== undefined && customer.active_loans_count > 0 && (
                            <div className="text-green-600">{customer.active_loans_count} active</div>
                          )}
                          {customer.overdue_loans_count !== undefined && customer.overdue_loans_count > 0 && (
                            <div className="text-red-600">{customer.overdue_loans_count} overdue</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {customer.credit_score ? (
                          <Badge variant={
                            customer.credit_score > 700 ? "default" :
                              customer.credit_score > 500 ? "secondary" :
                                "destructive"
                          } className={
                            customer.credit_score > 700 ? "bg-green-500 hover:bg-green-400" :
                              customer.credit_score > 500 ? "bg-yellow-500 hover:bg-yellow-400" :
                                ""
                          }>
                            {customer.credit_score}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">Not rated</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="default"
                          size="sm"
                          className="gap-1"
                          onClick={() => setLocation(`/customers/${customer.id}/loans`)}
                        >
                          <Plus size={14} />
                          <span>Add Loan</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Customer Sheet */}
      <Sheet open={isAddSheetOpen} onOpenChange={setIsAddSheetOpen}>
        <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
          <SheetHeader className="mb-5">
            <SheetTitle>Add New Customer</SheetTitle>
            <SheetDescription>
              Add a new customer to your organization. Fill in the details below.
            </SheetDescription>
          </SheetHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(handleCreateCustomer)} className="space-y-4">
              <FormField
                control={createForm.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="E.g., John Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number*</FormLabel>
                    <FormControl>
                      <div className="flex w-full" style={{ display: "flex" }}>
                        <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground" style={{ flexShrink: 0, width: "50px" }}>
                          {COUNTRY_CODE}
                        </span>
                        <div style={{ flex: 1, width: "100%" }}>
                          <Input
                            className="rounded-l-none w-full"
                            placeholder="E.g., 9876543210"
                            {...field}
                            value={field.value?.replace(new RegExp(`^${COUNTRY_CODE.replace('+', '\\+')}`),'')}
                            onChange={(e) => {
                              // Remove non-digit characters
                              const digitsOnly = e.target.value.replace(/\D/g, '');

                              // Trim to 10 digits max
                              const trimmed = digitsOnly.substring(0, 10);

                              // Update form value with country code prefix
                              field.onChange(`${COUNTRY_CODE}${trimmed}`);

                              // Show validation message if not exactly 10 digits
                              // We're checking digitsOnly which is just the number part without country code
                              if (digitsOnly.length > 0) {
                                if (digitsOnly.length < 10) {
                                  createForm.setError("phone", {
                                    type: "manual",
                                    message: `Phone number must be exactly 10 digits (currently ${digitsOnly.length})`
                                  });
                                } else if (digitsOnly.length === 10) {
                                  // Clear error when exactly 10 digits
                                  createForm.clearErrors("phone");
                                }
                              } else {
                                // Clear error if empty
                                createForm.clearErrors("phone");
                              }
                            }}
                          />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />


              <FormField
                control={createForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="E.g., <EMAIL>"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => {
                          // Get the input value
                          const value = e.target.value;

                          // Basic email validation
                          const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;

                          // Update the form value
                          field.onChange(value);

                          // If value is not empty and doesn't match pattern, show error
                          if (value && !emailPattern.test(value)) {
                            createForm.setError("email", {
                              type: "manual",
                              message: "Please enter a valid email address"
                            });
                          } else {
                            // Clear error if valid or empty
                            createForm.clearErrors("email");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Customer's address"
                        className="resize-none min-h-[80px]"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="credit_score"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Credit Score</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter score (0-1000)"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                        value={field.value === undefined ? "" : field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active Customer</FormLabel>
                        <FormDescription>
                          Mark as an active customer
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={createForm.control}
                  name="kyc_verified"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>KYC Verified</FormLabel>
                        <FormDescription>
                          Customer has completed KYC
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={createForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional notes about this customer"
                        className="resize-none min-h-[80px]"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsAddSheetOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={createCustomerMutation.isPending}>
                  {createCustomerMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Add Customer
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
