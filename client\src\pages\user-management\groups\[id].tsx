import React, { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Loader2, Save, ArrowLeft, Trash2, UserPlus, Shield } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';

// Define types
interface Group {
  id: number;
  name: string;
  description: string;
  company_id: number;
  branch_id: number | null;
  status: string;
  members: User[];
  roles: Role[];
}

interface User {
  id: number;
  email: string;
  full_name: string;
  role: string;
  membership_id: number;
  joined_at: string;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  assignment_id: number;
  assigned_at: string;
}

interface Branch {
  id: number;
  name: string;
  company_id: number;
}

export default function GroupDetail() {
  const [match, params] = useRoute('/user-management/groups/:id');
  const groupId = parseInt(params?.id || '0');
  const [location, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('details');
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(null);

  // Group form state
  const [groupForm, setGroupForm] = useState({
    name: '',
    description: '',
    branch_id: null as number | null,
    status: 'active'
  });

  // Fetch group details
  const { data: group, isLoading: isLoadingGroup } = useQuery({
    queryKey: [`/api/group-management/groups/${groupId}`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/group-management/groups/${groupId}`);
      return res.json();
    },
    enabled: groupId > 0
  });

  // Fetch available users (not in the group)
  const { data: availableUsers, isLoading: isLoadingUsers, refetch: refetchAvailableUsers } = useQuery({
    queryKey: [`/api/group-management/groups/${groupId}/available-users`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/group-management/groups/${groupId}/available-users`);
      return res.json();
    },
    enabled: groupId > 0
  });

  // Fetch available roles
  const { data: availableRoles, isLoading: isLoadingRoles, refetch: refetchAvailableRoles } = useQuery({
    queryKey: [`/api/group-management/groups/${groupId}/available-roles`],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/group-management/groups/${groupId}/available-roles`);
      return res.json();
    },
    enabled: groupId > 0
  });

  // Fetch branches
  const { data: branches, isLoading: isLoadingBranches } = useQuery({
    queryKey: ['/api/branches'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/branches');
      return res.json();
    }
  });

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: async (data: typeof groupForm) => {
      const res = await apiRequest('PATCH', `/api/groups/${groupId}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/group-management/groups/${groupId}`] });
      toast({
        title: 'Group updated',
        description: 'The group has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating group',
        description: error.message || 'There was an error updating the group.',
        variant: 'destructive',
      });
    }
  });

  // Delete group mutation
  const deleteGroupMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest('DELETE', `/api/groups/${groupId}`);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: 'Group deleted',
        description: 'The group has been deleted successfully.',
      });
      navigate('/user-management');
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting group',
        description: error.message || 'There was an error deleting the group.',
        variant: 'destructive',
      });
    }
  });

  // Add user to group mutation
  const addUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const res = await apiRequest('POST', `/api/group-management/groups/${groupId}/users`, { user_id: userId });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/group-management/groups/${groupId}`] });
      refetchAvailableUsers();
      toast({
        title: 'User added',
        description: 'The user has been added to the group.',
      });
      setIsAddUserDialogOpen(false);
      setSelectedUserId(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error adding user',
        description: error.message || 'There was an error adding the user to the group.',
        variant: 'destructive',
      });
    }
  });

  // Remove user from group mutation
  const removeUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const res = await apiRequest('DELETE', `/api/group-management/groups/${groupId}/users/${userId}`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/group-management/groups/${groupId}`] });
      refetchAvailableUsers();
      toast({
        title: 'User removed',
        description: 'The user has been removed from the group.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error removing user',
        description: error.message || 'There was an error removing the user from the group.',
        variant: 'destructive',
      });
    }
  });

  // Add role to group mutation
  const addRoleMutation = useMutation({
    mutationFn: async (roleId: number) => {
      const res = await apiRequest('POST', `/api/group-management/groups/${groupId}/roles`, { role_id: roleId });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/group-management/groups/${groupId}`] });
      refetchAvailableRoles();
      toast({
        title: 'Role assigned',
        description: 'The role has been assigned to the group.',
      });
      setIsAddRoleDialogOpen(false);
      setSelectedRoleId(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error assigning role',
        description: error.message || 'There was an error assigning the role to the group.',
        variant: 'destructive',
      });
    }
  });

  // Remove role from group mutation
  const removeRoleMutation = useMutation({
    mutationFn: async (roleId: number) => {
      const res = await apiRequest('DELETE', `/api/group-management/groups/${groupId}/roles/${roleId}`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/group-management/groups/${groupId}`] });
      refetchAvailableRoles();
      toast({
        title: 'Role removed',
        description: 'The role has been removed from the group.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error removing role',
        description: error.message || 'There was an error removing the role from the group.',
        variant: 'destructive',
      });
    }
  });

  // Update form when group data is loaded
  useEffect(() => {
    if (group) {
      setGroupForm({
        name: group.name,
        description: group.description || '',
        branch_id: group.branch_id,
        status: group.status || 'active'
      });
    }
  }, [group]);

  // Handle form changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGroupForm(prev => ({ ...prev, [name]: value }));
  };

  const handleBranchChange = (value: string) => {
    setGroupForm(prev => ({ ...prev, branch_id: value ? parseInt(value) : null }));
  };

  const handleStatusChange = (value: string) => {
    setGroupForm(prev => ({ ...prev, status: value }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateGroupMutation.mutate(groupForm);
  };

  // Define table columns
  const userColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'full_name',
      header: 'Name',
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'role',
      header: 'System Role',
      cell: ({ row }) => (
        <Badge variant="outline">{row.original.role}</Badge>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => removeUserMutation.mutate(row.original.id)}
          disabled={removeUserMutation.isPending}
        >
          {removeUserMutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Remove'}
        </Button>
      ),
    },
  ];

  const roleColumns: ColumnDef<Role>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      accessorKey: 'is_system',
      header: 'Type',
      cell: ({ row }) => (
        row.original.is_system ? <Badge>System</Badge> : <Badge variant="outline">Custom</Badge>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => removeRoleMutation.mutate(row.original.id)}
          disabled={removeRoleMutation.isPending}
        >
          {removeRoleMutation.isPending ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Remove'}
        </Button>
      ),
    },
  ];

  if (isLoadingGroup) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!group && groupId > 0) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <h2 className="text-2xl font-bold mb-2">Group not found</h2>
          <p className="text-muted-foreground mb-4">The group you're looking for doesn't exist or you don't have permission to view it.</p>
          <Button onClick={() => navigate('/user-management')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to User Management
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => navigate('/user-management')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">{group?.name}</h1>
          <Badge variant={group?.status === 'active' ? 'default' : 'secondary'}>
            {group?.status === 'active' ? 'Active' : 'Inactive'}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Group
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the group and remove all user memberships and role assignments.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => deleteGroupMutation.mutate()}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {deleteGroupMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    "Delete"
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="roles">Roles</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle>Group Details</CardTitle>
                <CardDescription>View and edit group details.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Group Name</Label>
                    <Input
                      id="name"
                      name="name"
                      value={groupForm.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      name="description"
                      value={groupForm.description}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="branch_id">Branch (Optional)</Label>
                    <Select value={groupForm.branch_id?.toString() || ''} onValueChange={handleBranchChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a branch" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No Branch</SelectItem>
                        {branches?.map((branch: Branch) => (
                          <SelectItem key={branch.id} value={branch.id.toString()}>
                            {branch.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select value={groupForm.status} onValueChange={handleStatusChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={updateGroupMutation.isPending}>
                  {updateGroupMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="members">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Group Members</CardTitle>
                <CardDescription>Manage users in this group.</CardDescription>
              </div>
              <Button onClick={() => setIsAddUserDialogOpen(true)}>
                <UserPlus className="mr-2 h-4 w-4" />
                Add Member
              </Button>
            </CardHeader>
            <CardContent>
              {!group?.members || group.members.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 text-center">
                  <p className="text-muted-foreground mb-4">This group has no members yet.</p>
                  <Button onClick={() => setIsAddUserDialogOpen(true)}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Add Member
                  </Button>
                </div>
              ) : (
                <DataTable columns={userColumns} data={group.members} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Group Roles</CardTitle>
                <CardDescription>Manage roles assigned to this group.</CardDescription>
              </div>
              <Button onClick={() => setIsAddRoleDialogOpen(true)}>
                <Shield className="mr-2 h-4 w-4" />
                Assign Role
              </Button>
            </CardHeader>
            <CardContent>
              {!group?.roles || group.roles.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 text-center">
                  <p className="text-muted-foreground mb-4">This group has no roles assigned yet.</p>
                  <Button onClick={() => setIsAddRoleDialogOpen(true)}>
                    <Shield className="mr-2 h-4 w-4" />
                    Assign Role
                  </Button>
                </div>
              ) : (
                <DataTable columns={roleColumns} data={group.roles} />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add User Dialog */}
      <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Member to Group</DialogTitle>
            <DialogDescription>
              Select a user to add to this group.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="user">Select User</Label>
                <Select value={selectedUserId?.toString() || ''} onValueChange={(value) => setSelectedUserId(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a user" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingUsers ? (
                      <div className="flex justify-center items-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </div>
                    ) : availableUsers?.length === 0 ? (
                      <div className="p-2 text-center text-sm text-muted-foreground">
                        No available users to add
                      </div>
                    ) : (
                      availableUsers?.map((user: User) => (
                        <SelectItem key={user.id} value={user.id.toString()}>
                          {user.full_name} ({user.username})
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsAddUserDialogOpen(false);
                setSelectedUserId(null);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => selectedUserId && addUserMutation.mutate(selectedUserId)}
              disabled={!selectedUserId || addUserMutation.isPending}
            >
              {addUserMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                "Add to Group"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Role Dialog */}
      <Dialog open={isAddRoleDialogOpen} onOpenChange={setIsAddRoleDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Assign Role to Group</DialogTitle>
            <DialogDescription>
              Select a role to assign to this group.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="role">Select Role</Label>
                <Select value={selectedRoleId?.toString() || ''} onValueChange={(value) => setSelectedRoleId(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingRoles ? (
                      <div className="flex justify-center items-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </div>
                    ) : availableRoles?.length === 0 ? (
                      <div className="p-2 text-center text-sm text-muted-foreground">
                        No available roles to assign
                      </div>
                    ) : (
                      availableRoles?.map((role: Role) => (
                        <SelectItem key={role.id} value={role.id.toString()}>
                          {role.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsAddRoleDialogOpen(false);
                setSelectedRoleId(null);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => selectedRoleId && addRoleMutation.mutate(selectedRoleId)}
              disabled={!selectedRoleId || addRoleMutation.isPending}
            >
              {addRoleMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Assigning...
                </>
              ) : (
                "Assign Role"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
