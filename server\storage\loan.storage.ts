import { db } from '../db';
import { eq, and, like, sql } from 'drizzle-orm';
import { loans, customers, collections, transactions } from '../../shared/schema';
import { Loan, InsertLoan } from '../../shared/schema';
import errorLogger from '../utils/errorLogger';
import { ILoanStorage } from './interfaces';

export class LoanStorage implements ILoanStorage {
  async getLoan(id: number): Promise<Loan | undefined> {
    try {
      // Join with customers table to include customer information
      const result = await db
        .select({
          loan: loans,
          customer: {
            id: customers.id,
            full_name: customers.full_name
          }
        })
        .from(loans)
        .leftJoin(customers, eq(loans.customer_id, customers.id))
        .where(eq(loans.id, id));

      if (result.length === 0) {
        return undefined;
      }

      // Return just the loan - customer data can be fetched separately if needed
      return result[0].loan;
    } catch (error) {
      errorLogger.logError(`Error fetching loan id=${id}`, 'loan-fetch', error as Error);
      return undefined;
    }
  }

  async getLoansByCustomer(customerId: number): Promise<Loan[]> {
    try {
      // Join with customers table to include customer information
      const result = await db
        .select({
          loan: loans,
          customer: {
            id: customers.id,
            full_name: customers.full_name
          }
        })
        .from(loans)
        .leftJoin(customers, eq(loans.customer_id, customers.id))
        .where(eq(loans.customer_id, customerId));

      // Map the result to the expected Loan format with nested customer object
      return result.map(row => ({
        ...row.loan,
        customer: row.customer.id ? row.customer : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching loans for customer id=${customerId}`, 'loan-fetch', error as Error);
      return [];
    }
  }

  async getLoansByCompany(companyId: number): Promise<Loan[]> {
    try {
      // Join with customers table to include customer information
      const result = await db
        .select({
          loan: loans,
          customer: {
            id: customers.id,
            full_name: customers.full_name
          }
        })
        .from(loans)
        .leftJoin(customers, eq(loans.customer_id, customers.id))
        .where(eq(loans.company_id, companyId));

      // Map the result to the expected Loan format with nested customer object
      return result.map(row => ({
        ...row.loan,
        customer: row.customer.id ? row.customer : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching loans for company id=${companyId}`, 'loan-fetch', error as Error);
      return [];
    }
  }

  async getLoansByBranch(branchId: number): Promise<Loan[]> {
    try {
      // Since loans table doesn't have branch_id, we need to filter through customers
      // who belong to a specific branch (if customers have branch_id)
      // For now, return empty array as loans don't have direct branch association
      errorLogger.logError(`getLoansByBranch not implemented - loans table doesn't have branch_id field`, 'loan-fetch', new Error('Not implemented'));
      return [];
    } catch (error) {
      errorLogger.logError(`Error fetching loans for branch id=${branchId}`, 'loan-fetch', error as Error);
      return [];
    }
  }

  async createLoan(loanData: InsertLoan): Promise<Loan> {
    try {
      const [loan] = await db.insert(loans)
        .values(loanData)
        .returning();

      return loan;
    } catch (error) {
      errorLogger.logError(`Error creating loan`, 'loan-create', error as Error);
      throw error;
    }
  }

  async updateLoan(id: number, companyId: number, loanData: Partial<InsertLoan>): Promise<Loan> {
    try {
      // First check if the loan exists and belongs to the company
      const [existingLoan] = await db.select()
        .from(loans)
        .where(
          and(
            eq(loans.id, id),
            eq(loans.company_id, companyId)
          )
        );

      if (!existingLoan) {
        throw new Error(`Loan with id=${id} not found for company id=${companyId}`);
      }

      const [updatedLoan] = await db.update(loans)
        .set(loanData)
        .where(eq(loans.id, id))
        .returning();

      return updatedLoan;
    } catch (error) {
      errorLogger.logError(`Error updating loan id=${id}`, 'loan-update', error as Error);
      throw error;
    }
  }

  async deleteLoan(id: number, companyId: number): Promise<{
    success: boolean,
    error?: string,
    collectionsCount?: number,
    nonPendingCollections?: number,
    transactionsCount?: number
  }> {
    try {
      // First check if the loan exists and belongs to the company
      const [existingLoan] = await db.select()
        .from(loans)
        .where(
          and(
            eq(loans.id, id),
            eq(loans.company_id, companyId)
          )
        );

      if (!existingLoan) {
        return {
          success: false,
          error: `Loan with id=${id} not found for company id=${companyId}`
        };
      }

      // Check if there are any collections associated with this loan
      const loanCollections = await db.select()
        .from(collections)
        .where(eq(collections.loan_id, id));

      if (loanCollections.length > 0) {
        // Check if all collections have "pending" status
        const nonPendingCollections = loanCollections.filter(collection => collection.status !== 'pending');

        if (nonPendingCollections.length > 0) {
          return {
            success: false,
            error: 'Cannot delete loan with non-pending collections. All collections must have "pending" status.',
            collectionsCount: loanCollections.length,
            nonPendingCollections: nonPendingCollections.length
          };
        }
      }

      // Check if there are any transactions associated with this loan
      const loanTransactions = await db.select()
        .from(transactions)
        .where(
          and(
            eq(transactions.reference_type, 'loan'),
            eq(transactions.reference_id, id)
          )
        );

      // If we have any dependencies, return error with counts
      if (loanCollections.length > 0 || loanTransactions.length > 0) {
        return {
          success: false,
          error: 'Cannot delete loan with associated records. The loan has collections or financial transactions.',
          collectionsCount: loanCollections.length > 0 ? loanCollections.length : undefined,
          transactionsCount: loanTransactions.length > 0 ? loanTransactions.length : undefined
        };
      }

      // Delete the loan
      await db.delete(loans)
        .where(eq(loans.id, id));

      return { success: true };
    } catch (error) {
      errorLogger.logError(`Error deleting loan id=${id}`, 'loan-delete', error as Error);
      return {
        success: false,
        error: `Error deleting loan: ${(error as Error).message}`
      };
    }
  }

  async deleteLoanWithCollections(id: number, companyId: number): Promise<{
    success: boolean,
    error?: string,
    collectionsDeleted?: number,
    transactionsDeleted?: number
  }> {
    try {
      // First check if the loan exists and belongs to the company
      const [existingLoan] = await db.select()
        .from(loans)
        .where(
          and(
            eq(loans.id, id),
            eq(loans.company_id, companyId)
          )
        );

      if (!existingLoan) {
        return {
          success: false,
          error: `Loan with id=${id} not found for company id=${companyId}`
        };
      }

      // Check if there are any non-pending collections
      const loanCollections = await db.select()
        .from(collections)
        .where(eq(collections.loan_id, id));

      const nonPendingCollections = loanCollections.filter(collection => collection.status !== 'pending');

      if (nonPendingCollections.length > 0) {
        return {
          success: false,
          error: 'Cannot delete loan with non-pending collections. All collections must have "pending" status.',
          collectionsDeleted: 0
        };
      }

      // Use a transaction to ensure all operations succeed or fail together
      return await db.transaction(async (tx) => {
        // Delete associated transactions first
        const deleteTransactionsResult = await tx.delete(transactions)
          .where(
            and(
              eq(transactions.reference_type, 'loan'),
              eq(transactions.reference_id, id)
            )
          )
          .returning();

        const transactionsDeleted = deleteTransactionsResult.length;

        // Delete associated collections
        const deleteCollectionsResult = await tx.delete(collections)
          .where(eq(collections.loan_id, id))
          .returning();

        const collectionsDeleted = deleteCollectionsResult.length;

        // Finally, delete the loan
        await tx.delete(loans)
          .where(eq(loans.id, id));

        return {
          success: true,
          collectionsDeleted,
          transactionsDeleted
        };
      });
    } catch (error) {
      errorLogger.logError(`Error deleting loan id=${id} with collections`, 'loan-delete-with-collections', error as Error);
      return {
        success: false,
        error: `Error deleting loan with collections: ${(error as Error).message}`
      };
    }
  }

  /**
   * Get the highest serial number for a given company and prefix (e.g., 'GS-')
   * Returns the highest serial as a number, or 0 if none found.
   * Only considers loans from the specific company.
   */
  async getHighestLoanSerial(companyId: number, prefix: string): Promise<number> {
    try {
      // Find the max serial for this company and prefix
      // loan_reference_code is like 'GS-001', 'GS-002', ...
      const result = await db.select({ maxString: sql`MAX(${loans.loan_reference_code})` })
        .from(loans)
        .where(
          and(
            eq(loans.company_id, companyId),
            like(loans.loan_reference_code, `${prefix}%`)
          )
        );
      const maxString = result[0]?.maxString as string | undefined;
      if (!maxString) return 0;

      // Extract the serial part (e.g., 'GS-012' => 12)
      const match = maxString.match(/^(.*-)(\d{3})$/);
      if (match) {
        return parseInt(match[2], 10);
      }
      return 0;
    } catch (error) {
      errorLogger.logError(`Error getting highest loan serial for company ${companyId} and prefix ${prefix}`, 'loan-serial', error as Error);
      return 0;
    }
  }
}
