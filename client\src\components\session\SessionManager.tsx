import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Monitor, 
  Laptop, 
  Smartphone, 
  Tablet, 
  MapPin, 
  Clock, 
  Shield, 
  LogOut,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { formatDistanceToNow } from 'date-fns';

interface SessionInfo {
  id: number;
  session_id: string;
  device_type: 'desktop' | 'laptop' | 'mobile' | 'tablet' | 'unknown';
  device_name?: string;
  ip_address?: string;
  location_country?: string;
  location_city?: string;
  created_at: string;
  last_activity: string;
  expires_at?: string;
  is_trusted_device: boolean;
  mfa_verified: boolean;
  isCurrent: boolean;
}

interface SessionStatistics {
  totalSessions: number;
  activeSessions: number;
  expiredSessions: number;
  terminatedSessions: number;
  averageSessionDuration: number;
  deviceBreakdown: Record<string, number>;
  locationBreakdown: Record<string, number>;
}

const deviceIcons = {
  desktop: Monitor,
  laptop: Laptop,
  mobile: Smartphone,
  tablet: Tablet,
  unknown: Monitor
};

export function SessionManager() {
  const queryClient = useQueryClient();
  const [selectedSession, setSelectedSession] = useState<string | null>(null);

  // Fetch active sessions
  const { data: sessionsData, isLoading: sessionsLoading } = useQuery({
    queryKey: ['sessions', 'active'],
    queryFn: () => apiRequest('/api/sessions/active'),
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Fetch session statistics
  const { data: statisticsData } = useQuery({
    queryKey: ['sessions', 'statistics'],
    queryFn: () => apiRequest('/api/sessions/statistics?days=30'),
    refetchInterval: 60000 // Refresh every minute
  });

  // Terminate session mutation
  const terminateSessionMutation = useMutation({
    mutationFn: (sessionId: string) => 
      apiRequest(`/api/sessions/${sessionId}`, { method: 'DELETE' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      setSelectedSession(null);
    }
  });

  // Terminate other sessions mutation
  const terminateOthersMutation = useMutation({
    mutationFn: () => 
      apiRequest('/api/sessions/terminate-others', { method: 'DELETE' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
    }
  });

  const sessions: SessionInfo[] = sessionsData?.sessions || [];
  const statistics: SessionStatistics = statisticsData?.statistics || {
    totalSessions: 0,
    activeSessions: 0,
    expiredSessions: 0,
    terminatedSessions: 0,
    averageSessionDuration: 0,
    deviceBreakdown: {},
    locationBreakdown: {}
  };

  const handleTerminateSession = (sessionId: string) => {
    if (confirm('Are you sure you want to terminate this session?')) {
      terminateSessionMutation.mutate(sessionId);
    }
  };

  const handleTerminateOthers = () => {
    if (confirm('Are you sure you want to terminate all other sessions? This will log you out from all other devices.')) {
      terminateOthersMutation.mutate();
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    const IconComponent = deviceIcons[deviceType as keyof typeof deviceIcons] || Monitor;
    return <IconComponent className="h-4 w-4" />;
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (sessionsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading session information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Session Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Active Sessions</p>
                <p className="text-2xl font-bold">{statistics.activeSessions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Avg. Duration</p>
                <p className="text-2xl font-bold">
                  {formatDuration(statistics.averageSessionDuration)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Monitor className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Total (30d)</p>
                <p className="text-2xl font-bold">{statistics.totalSessions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm font-medium">Terminated</p>
                <p className="text-2xl font-bold">{statistics.terminatedSessions}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Sessions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Active Sessions</CardTitle>
              <CardDescription>
                Manage your active login sessions across different devices
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              onClick={handleTerminateOthers}
              disabled={terminateOthersMutation.isPending || sessions.length <= 1}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout Other Devices
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {sessions.length === 0 ? (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                No active sessions found. This might indicate a session management issue.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {sessions.map((session) => (
                <div
                  key={session.session_id}
                  className={`border rounded-lg p-4 ${
                    session.isCurrent ? 'border-primary bg-primary/5' : 'border-border'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="mt-1">
                        {getDeviceIcon(session.device_type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium">
                            {session.device_name || `${session.device_type} device`}
                          </h4>
                          {session.isCurrent && (
                            <Badge variant="default">Current Session</Badge>
                          )}
                          {session.is_trusted_device && (
                            <Badge variant="secondary">
                              <Shield className="h-3 w-3 mr-1" />
                              Trusted
                            </Badge>
                          )}
                          {session.mfa_verified && (
                            <Badge variant="outline">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              MFA
                            </Badge>
                          )}
                        </div>
                        
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div className="flex items-center space-x-4">
                            <span>IP: {session.ip_address || 'Unknown'}</span>
                            {session.location_country && (
                              <span className="flex items-center">
                                <MapPin className="h-3 w-3 mr-1" />
                                {session.location_city}, {session.location_country}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-4">
                            <span>
                              Created: {formatDistanceToNow(new Date(session.created_at), { addSuffix: true })}
                            </span>
                            <span>
                              Last active: {formatDistanceToNow(new Date(session.last_activity), { addSuffix: true })}
                            </span>
                          </div>
                          {session.expires_at && (
                            <div>
                              Expires: {formatDistanceToNow(new Date(session.expires_at), { addSuffix: true })}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {!session.isCurrent && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTerminateSession(session.session_id)}
                        disabled={terminateSessionMutation.isPending}
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Terminate
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Device and Location Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Device Types (30 days)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(statistics.deviceBreakdown).map(([device, count]) => (
                <div key={device} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getDeviceIcon(device)}
                    <span className="capitalize">{device}</span>
                  </div>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Locations (30 days)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(statistics.locationBreakdown).slice(0, 5).map(([location, count]) => (
                <div key={location} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4" />
                    <span>{location}</span>
                  </div>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
