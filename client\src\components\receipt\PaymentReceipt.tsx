import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { format } from 'date-fns';
import { Payment, Customer, Collection } from '../../../../shared/schema';

interface PaymentReceiptProps {
  payment: Payment;
  customer?: Customer;
  collection?: Collection;
  onClose?: () => void;
  onPrint?: () => void;
}

export function PaymentReceipt({ 
  payment, 
  customer, 
  collection, 
  onClose, 
  onPrint 
}: PaymentReceiptProps) {
  if (!payment) return null;
  
  const printReceipt = () => {
    if (onPrint) {
      onPrint();
      return;
    }
    
    // Default print implementation
    const receiptWindow = window.open('', '_blank');
    if (!receiptWindow) return;
    
    const receiptContent = document.getElementById('payment-receipt')?.innerHTML;
    if (!receiptContent) return;
    
    receiptWindow.document.write(`
      <html>
        <head>
          <title>Payment Receipt ${payment.receipt_number}</title>
          <style>
            body {
              font-family: 'Arial', sans-serif;
              padding: 20px;
              max-width: 800px;
              margin: 0 auto;
            }
            .receipt-wrapper {
              border: 1px solid #ddd;
              padding: 20px;
              border-radius: 8px;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 20px;
            }
            .company-logo {
              max-width: 150px;
              margin-bottom: 10px;
            }
            .receipt-body {
              margin-bottom: 20px;
            }
            .receipt-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              border-bottom: 1px dashed #eee;
              padding-bottom: 5px;
            }
            .receipt-footer {
              text-align: center;
              margin-top: 30px;
              font-size: 12px;
              color: #666;
            }
            .receipt-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .receipt-subtitle {
              font-size: 16px;
              color: #666;
              margin-bottom: 20px;
            }
            .amount {
              font-weight: bold;
              font-size: 18px;
            }
            .separator {
              height: 1px;
              background-color: #ddd;
              margin: 15px 0;
            }
            @media print {
              body {
                padding: 0;
              }
              .receipt-wrapper {
                border: none;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-wrapper">
            ${receiptContent}
          </div>
          <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button onClick="window.print()">Print Receipt</button>
          </div>
        </body>
      </html>
    `);
    
    receiptWindow.document.close();
    receiptWindow.focus();
  };
  
  return (
    <Card className="w-full max-w-md mx-auto bg-white" id="payment-receipt">
      <CardHeader className="text-center border-b pb-6">
        <CardTitle className="text-2xl font-bold mb-1">Payment Receipt</CardTitle>
        <CardDescription className="text-sm text-gray-500">
          Receipt No: {payment.receipt_number}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pt-6">
        <div className="text-center mb-4">
          <div className="font-bold text-xl">Amount Paid</div>
          <div className="text-3xl font-bold text-green-600 mt-1">
            ₹{parseFloat(payment.amount.toString()).toLocaleString()}
          </div>
          {payment.fine_amount && parseFloat(payment.fine_amount.toString()) > 0 && (
            <div className="text-sm text-red-500 mt-1">
              Including Fine: ₹{parseFloat(payment.fine_amount.toString()).toLocaleString()}
            </div>
          )}
        </div>
        
        <Separator className="my-4" />
        
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-500">Payment Date:</span>
            <span className="font-medium">
              {format(new Date(payment.payment_date), 'dd MMM yyyy, hh:mm a')}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-gray-500">Payment Method:</span>
            <span className="font-medium capitalize">
              {payment.payment_method}
            </span>
          </div>
          
          {customer && (
            <>
              <Separator className="my-4" />
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Customer:</span>
                  <span className="font-medium">{customer.full_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Phone:</span>
                  <span className="font-medium">{customer.phone}</span>
                </div>
              </div>
            </>
          )}
          
          {collection && (
            <>
              <Separator className="my-4" />
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Loan ID:</span>
                  <span className="font-medium">{collection.loan_id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Schedule Date:</span>
                  <span className="font-medium">
                    {format(new Date(collection.scheduled_date), 'dd MMM yyyy')}
                  </span>
                </div>
              </div>
            </>
          )}
          
          {payment.notes && (
            <>
              <Separator className="my-4" />
              <div>
                <span className="text-gray-500 block mb-1">Notes:</span>
                <span className="text-sm">{payment.notes}</span>
              </div>
            </>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between pt-4 border-t">
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        )}
        <div className="flex space-x-2 ml-auto">
          <Button variant="outline" onClick={printReceipt}>
            Print
          </Button>
          <Button 
            onClick={() => window.open(`/api/payments/${payment.id}/pdf?companyId=${payment.company_id}`, '_blank')}
          >
            Download PDF
          </Button>
        </div>
      </CardFooter>
      
      <div className="text-xs text-center text-gray-400 pb-4">
        This is a computer-generated receipt and doesn't require a signature.
      </div>
    </Card>
  );
}