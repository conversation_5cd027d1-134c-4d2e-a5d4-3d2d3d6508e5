import { IFinancialStorage } from './interfaces';
import { AccountStorage } from './account.storage';
import { TransactionStorage } from './transaction.storage';
import { ReportStorage } from './report.storage';

// Create a class that implements the IFinancialStorage interface by combining all financial storage implementations
class FinancialStorage implements IFinancialStorage {
  private accountStorage: AccountStorage;
  private transactionStorage: TransactionStorage;
  private reportStorage: ReportStorage;

  constructor() {
    this.accountStorage = new AccountStorage();
    this.transactionStorage = new TransactionStorage();
    this.reportStorage = new ReportStorage();

    // Bind methods after storage instances are created
    this.bindMethods();
  }

  private bindMethods() {
    // Account methods
    this.getAccount = this.accountStorage.getAccount.bind(this.accountStorage);
    this.getAccountsByCompany = this.accountStorage.getAccountsByCompany.bind(this.accountStorage);
    this.createAccount = this.accountStorage.createAccount.bind(this.accountStorage);
    this.updateAccount = this.accountStorage.updateAccount.bind(this.accountStorage);
    this.deleteAccount = this.accountStorage.deleteAccount.bind(this.accountStorage);
    this.getAccountBalance = this.accountStorage.getAccountBalance.bind(this.accountStorage);
    this.updateAccountBalance = this.accountStorage.updateAccountBalance.bind(this.accountStorage);

    // Transaction methods
    this.getTransaction = this.transactionStorage.getTransaction.bind(this.transactionStorage);
    this.getTransactionsByCompany = this.transactionStorage.getTransactionsByCompany.bind(this.transactionStorage);
    this.getTransactionsByAccount = this.transactionStorage.getTransactionsByAccount.bind(this.transactionStorage);
    this.createTransaction = this.transactionStorage.createTransaction.bind(this.transactionStorage);
    this.updateTransaction = this.transactionStorage.updateTransaction.bind(this.transactionStorage);
    this.deleteTransaction = this.transactionStorage.deleteTransaction.bind(this.transactionStorage);
    this.getTransactionSummary = this.transactionStorage.getTransactionSummary.bind(this.transactionStorage);

    // Report methods
    this.getProfitLossReport = this.reportStorage.getProfitLossReport.bind(this.reportStorage);
    this.getCashFlowReport = this.reportStorage.getCashFlowReport.bind(this.reportStorage);
    this.getCollectionReport = this.reportStorage.getCollectionReport.bind(this.reportStorage);
  }

  // Delegate methods to the appropriate storage implementation (declared as properties)
  getAccount!: AccountStorage['getAccount'];
  getAccountsByCompany!: AccountStorage['getAccountsByCompany'];
  createAccount!: AccountStorage['createAccount'];
  updateAccount!: AccountStorage['updateAccount'];
  deleteAccount!: AccountStorage['deleteAccount'];
  getAccountBalance!: AccountStorage['getAccountBalance'];
  updateAccountBalance!: AccountStorage['updateAccountBalance'];

  getTransaction!: TransactionStorage['getTransaction'];
  getTransactionsByCompany!: TransactionStorage['getTransactionsByCompany'];
  getTransactionsByAccount!: TransactionStorage['getTransactionsByAccount'];
  createTransaction!: TransactionStorage['createTransaction'];
  updateTransaction!: TransactionStorage['updateTransaction'];
  deleteTransaction!: TransactionStorage['deleteTransaction'];
  getTransactionSummary!: TransactionStorage['getTransactionSummary'];

  getProfitLossReport!: ReportStorage['getProfitLossReport'];
  getCashFlowReport!: ReportStorage['getCashFlowReport'];
  getCollectionReport!: ReportStorage['getCollectionReport'];
}

// Create and export a singleton instance
export const financialStorage = new FinancialStorage();
