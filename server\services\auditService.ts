import { db } from '../db';
import {
  permissionAuditLogs, dataAccessAuditLogs, permissionChangeLogs,
  type PermissionAuditLog, type InsertPermissionAuditLog,
  type DataAccessAuditLog, type InsertDataAccessAuditLog,
  type PermissionChangeLog, type InsertPermissionChangeLog,
  type PermissionUsageContext, type DataAccessContext, type PermissionChangeContext
} from '@shared/schema';
import { eq, and, desc, count, gte, lte, inArray, sql } from 'drizzle-orm';
import crypto from 'crypto';

export class AuditService {
  /**
   * Generate unique audit ID
   */
  private generateAuditId(): string {
    return `audit_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  /**
   * Calculate risk score based on operation context
   */
  private calculateRiskScore(context: any): number {
    let riskScore = 0;

    // Base risk factors
    if (context.isSensitiveOperation) riskScore += 30;
    if (context.operationType === 'delete') riskScore += 20;
    if (context.operationType === 'approve') riskScore += 15;
    if (context.operationType === 'export') riskScore += 25;

    // Permission-specific risk
    if (context.permissionCode?.includes('admin')) riskScore += 20;
    if (context.permissionCode?.includes('delete')) riskScore += 15;
    if (context.permissionCode?.includes('approve')) riskScore += 10;

    // Data sensitivity risk
    if (context.sensitiveFieldsAccessed?.length > 0) {
      riskScore += Math.min(context.sensitiveFieldsAccessed.length * 5, 25);
    }

    // Time-based risk (outside business hours)
    const hour = new Date().getHours();
    if (hour < 6 || hour > 22) riskScore += 10;

    return Math.min(riskScore, 100);
  }

  /**
   * Log permission usage
   */
  async logPermissionUsage(context: PermissionUsageContext): Promise<PermissionAuditLog> {
    const auditId = this.generateAuditId();
    const riskScore = context.riskScore || this.calculateRiskScore(context);

    const auditData: InsertPermissionAuditLog = {
      audit_id: auditId,
      user_id: context.userId,
      company_id: context.companyId,
      session_id: context.sessionId,
      permission_code: context.permissionCode,
      permission_name: context.permissionName,
      action: 'used',
      result: context.result,
      resource_type: context.resourceType,
      resource_id: context.resourceId,
      operation_type: context.operationType,
      operation_details: context.operationDetails,
      endpoint: context.endpoint,
      method: context.method,
      ip_address: context.ipAddress,
      user_agent: context.userAgent,
      referer: context.metadata?.referer,
      metadata: context.metadata || {},
      risk_score: riskScore,
      is_sensitive_operation: context.isSensitiveOperation || false,
      compliance_flags: context.complianceFlags || [],
      response_time_ms: context.responseTimeMs,
    };

    const [auditLog] = await db.insert(permissionAuditLogs).values(auditData).returning();
    return auditLog;
  }

  /**
   * Log data access
   */
  async logDataAccess(context: DataAccessContext): Promise<DataAccessAuditLog> {
    const auditId = this.generateAuditId();

    const auditData: InsertDataAccessAuditLog = {
      audit_id: auditId,
      user_id: context.userId,
      company_id: context.companyId,
      session_id: context.sessionId,
      entity_type: context.entityType as any,
      entity_id: context.entityId,
      action: this.mapActionToAuditAction(context.method),
      table_name: context.tableName,
      field_names: context.fieldNames || [],
      old_values: context.oldValues,
      new_values: context.newValues,
      query_filters: context.queryFilters,
      record_count: context.recordCount || 1,
      data_scope: context.dataScope,
      field_access_level: context.fieldAccessLevel,
      sensitive_fields_accessed: context.sensitiveFieldsAccessed || [],
      endpoint: context.endpoint,
      method: context.method,
      ip_address: context.ipAddress,
      user_agent: context.userAgent,
      metadata: context.metadata || {},
      compliance_flags: [],
      retention_period_days: context.retentionPeriodDays,
    };

    const [auditLog] = await db.insert(dataAccessAuditLogs).values(auditData).returning();
    return auditLog;
  }

  /**
   * Log permission changes
   */
  async logPermissionChange(context: PermissionChangeContext): Promise<PermissionChangeLog> {
    const auditId = this.generateAuditId();

    const auditData: InsertPermissionChangeLog = {
      audit_id: auditId,
      changed_by: context.changedBy,
      target_user_id: context.targetUserId,
      target_role_id: context.targetRoleId,
      company_id: context.companyId,
      change_type: context.changeType,
      permission_code: context.permissionCode,
      permission_name: context.permissionName,
      role_name: context.roleName,
      action: this.mapChangeTypeToAction(context.changeType),
      reason: context.reason,
      approval_workflow_id: context.approvalWorkflowId,
      is_temporary: context.isTemporary || false,
      expires_at: context.expiresAt,
      previous_state: context.previousState,
      new_state: context.newState,
      change_summary: context.changeSummary,
      ip_address: context.ipAddress,
      user_agent: context.userAgent,
      session_id: context.sessionId,
      metadata: context.metadata || {},
      compliance_flags: [],
      risk_assessment: context.riskAssessment || 'low',
    };

    const [auditLog] = await db.insert(permissionChangeLogs).values(auditData).returning();
    return auditLog;
  }

  /**
   * Map HTTP method to audit action
   */
  private mapActionToAuditAction(method?: string): any {
    switch (method?.toUpperCase()) {
      case 'GET': return 'viewed';
      case 'POST': return 'created';
      case 'PUT':
      case 'PATCH': return 'updated';
      case 'DELETE': return 'deleted';
      default: return 'viewed';
    }
  }

  /**
   * Map change type to audit action
   */
  private mapChangeTypeToAction(changeType: string): any {
    switch (changeType) {
      case 'permission_grant':
      case 'role_assign': return 'granted';
      case 'permission_revoke':
      case 'role_remove': return 'revoked';
      case 'role_modify': return 'modified';
      default: return 'modified';
    }
  }

  /**
   * Get permission audit logs with filters
   */
  async getPermissionAuditLogs(filters: {
    companyId?: number;
    userId?: number;
    permissionCode?: string;
    result?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{ logs: PermissionAuditLog[]; totalCount: number }> {
    const conditions = [];

    if (filters.companyId) {
      conditions.push(eq(permissionAuditLogs.company_id, filters.companyId));
    }
    if (filters.userId) {
      conditions.push(eq(permissionAuditLogs.user_id, filters.userId));
    }
    if (filters.permissionCode) {
      conditions.push(eq(permissionAuditLogs.permission_code, filters.permissionCode));
    }
    if (filters.result) {
      conditions.push(eq(permissionAuditLogs.result, filters.result));
    }
    if (filters.startDate) {
      conditions.push(gte(permissionAuditLogs.timestamp, filters.startDate));
    }
    if (filters.endDate) {
      conditions.push(lte(permissionAuditLogs.timestamp, filters.endDate));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(permissionAuditLogs)
      .where(whereClause);

    // Get logs with pagination
    const logs = await db
      .select()
      .from(permissionAuditLogs)
      .where(whereClause)
      .orderBy(desc(permissionAuditLogs.timestamp))
      .limit(filters.limit || 50)
      .offset(filters.offset || 0);

    return { logs, totalCount };
  }

  /**
   * Get data access audit logs with filters
   */
  async getDataAccessAuditLogs(filters: {
    companyId?: number;
    userId?: number;
    entityType?: string;
    entityId?: string;
    tableName?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{ logs: DataAccessAuditLog[]; totalCount: number }> {
    const conditions = [];

    if (filters.companyId) {
      conditions.push(eq(dataAccessAuditLogs.company_id, filters.companyId));
    }
    if (filters.userId) {
      conditions.push(eq(dataAccessAuditLogs.user_id, filters.userId));
    }
    if (filters.entityType) {
      conditions.push(eq(dataAccessAuditLogs.entity_type, filters.entityType as any));
    }
    if (filters.entityId) {
      conditions.push(eq(dataAccessAuditLogs.entity_id, filters.entityId));
    }
    if (filters.tableName) {
      conditions.push(eq(dataAccessAuditLogs.table_name, filters.tableName));
    }
    if (filters.startDate) {
      conditions.push(gte(dataAccessAuditLogs.timestamp, filters.startDate));
    }
    if (filters.endDate) {
      conditions.push(lte(dataAccessAuditLogs.timestamp, filters.endDate));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(dataAccessAuditLogs)
      .where(whereClause);

    // Get logs with pagination
    const logs = await db
      .select()
      .from(dataAccessAuditLogs)
      .where(whereClause)
      .orderBy(desc(dataAccessAuditLogs.timestamp))
      .limit(filters.limit || 50)
      .offset(filters.offset || 0);

    return { logs, totalCount };
  }

  /**
   * Get permission change logs with filters
   */
  async getPermissionChangeLogs(filters: {
    companyId?: number;
    changedBy?: number;
    targetUserId?: number;
    targetRoleId?: number;
    changeType?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{ logs: PermissionChangeLog[]; totalCount: number }> {
    const conditions = [];

    if (filters.companyId) {
      conditions.push(eq(permissionChangeLogs.company_id, filters.companyId));
    }
    if (filters.changedBy) {
      conditions.push(eq(permissionChangeLogs.changed_by, filters.changedBy));
    }
    if (filters.targetUserId) {
      conditions.push(eq(permissionChangeLogs.target_user_id, filters.targetUserId));
    }
    if (filters.targetRoleId) {
      conditions.push(eq(permissionChangeLogs.target_role_id, filters.targetRoleId));
    }
    if (filters.changeType) {
      conditions.push(eq(permissionChangeLogs.change_type, filters.changeType));
    }
    if (filters.startDate) {
      conditions.push(gte(permissionChangeLogs.timestamp, filters.startDate));
    }
    if (filters.endDate) {
      conditions.push(lte(permissionChangeLogs.timestamp, filters.endDate));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(permissionChangeLogs)
      .where(whereClause);

    // Get logs with pagination
    const logs = await db
      .select()
      .from(permissionChangeLogs)
      .where(whereClause)
      .orderBy(desc(permissionChangeLogs.timestamp))
      .limit(filters.limit || 50)
      .offset(filters.offset || 0);

    return { logs, totalCount };
  }

  /**
   * Get audit statistics for a company
   */
  async getAuditStatistics(companyId: number, startDate?: Date, endDate?: Date): Promise<{
    permissionUsage: { total: number; denied: number; success: number };
    dataAccess: { total: number; creates: number; updates: number; deletes: number; views: number };
    permissionChanges: { total: number; grants: number; revokes: number; modifications: number };
    riskDistribution: { low: number; medium: number; high: number; critical: number };
    topUsers: Array<{ userId: number; activityCount: number }>;
    topPermissions: Array<{ permissionCode: string; usageCount: number }>;
  }> {
    const conditions = [eq(permissionAuditLogs.company_id, companyId)];
    const dataConditions = [eq(dataAccessAuditLogs.company_id, companyId)];
    const changeConditions = [eq(permissionChangeLogs.company_id, companyId)];

    if (startDate) {
      conditions.push(gte(permissionAuditLogs.timestamp, startDate));
      dataConditions.push(gte(dataAccessAuditLogs.timestamp, startDate));
      changeConditions.push(gte(permissionChangeLogs.timestamp, startDate));
    }
    if (endDate) {
      conditions.push(lte(permissionAuditLogs.timestamp, endDate));
      dataConditions.push(lte(dataAccessAuditLogs.timestamp, endDate));
      changeConditions.push(lte(permissionChangeLogs.timestamp, endDate));
    }

    // Permission usage statistics
    const permissionStats = await db
      .select({
        total: count(),
        result: permissionAuditLogs.result,
      })
      .from(permissionAuditLogs)
      .where(and(...conditions))
      .groupBy(permissionAuditLogs.result);

    const permissionUsage = {
      total: permissionStats.reduce((sum, stat) => sum + stat.total, 0),
      denied: permissionStats.find(s => s.result === 'denied')?.total || 0,
      success: permissionStats.find(s => s.result === 'success')?.total || 0,
    };

    // Data access statistics
    const dataStats = await db
      .select({
        total: count(),
        action: dataAccessAuditLogs.action,
      })
      .from(dataAccessAuditLogs)
      .where(and(...dataConditions))
      .groupBy(dataAccessAuditLogs.action);

    const dataAccess = {
      total: dataStats.reduce((sum, stat) => sum + stat.total, 0),
      creates: dataStats.find(s => s.action === 'created')?.total || 0,
      updates: dataStats.find(s => s.action === 'updated')?.total || 0,
      deletes: dataStats.find(s => s.action === 'deleted')?.total || 0,
      views: dataStats.find(s => s.action === 'viewed')?.total || 0,
    };

    // Permission change statistics
    const changeStats = await db
      .select({
        total: count(),
        action: permissionChangeLogs.action,
      })
      .from(permissionChangeLogs)
      .where(and(...changeConditions))
      .groupBy(permissionChangeLogs.action);

    const permissionChanges = {
      total: changeStats.reduce((sum, stat) => sum + stat.total, 0),
      grants: changeStats.find(s => s.action === 'granted')?.total || 0,
      revokes: changeStats.find(s => s.action === 'revoked')?.total || 0,
      modifications: changeStats.find(s => s.action === 'modified')?.total || 0,
    };

    // Risk distribution (from permission audit logs)
    const riskStats = await db
      .select({
        count: count(),
        riskLevel: sql<string>`
          CASE
            WHEN ${permissionAuditLogs.risk_score} < 25 THEN 'low'
            WHEN ${permissionAuditLogs.risk_score} < 50 THEN 'medium'
            WHEN ${permissionAuditLogs.risk_score} < 75 THEN 'high'
            ELSE 'critical'
          END
        `.as('risk_level'),
      })
      .from(permissionAuditLogs)
      .where(and(...conditions))
      .groupBy(sql`risk_level`);

    const riskDistribution = {
      low: riskStats.find(s => s.riskLevel === 'low')?.count || 0,
      medium: riskStats.find(s => s.riskLevel === 'medium')?.count || 0,
      high: riskStats.find(s => s.riskLevel === 'high')?.count || 0,
      critical: riskStats.find(s => s.riskLevel === 'critical')?.count || 0,
    };

    // Top users by activity
    const topUsers = await db
      .select({
        userId: permissionAuditLogs.user_id,
        activityCount: count(),
      })
      .from(permissionAuditLogs)
      .where(and(...conditions))
      .groupBy(permissionAuditLogs.user_id)
      .orderBy(desc(count()))
      .limit(10);

    // Top permissions by usage
    const topPermissions = await db
      .select({
        permissionCode: permissionAuditLogs.permission_code,
        usageCount: count(),
      })
      .from(permissionAuditLogs)
      .where(and(...conditions))
      .groupBy(permissionAuditLogs.permission_code)
      .orderBy(desc(count()))
      .limit(10);

    return {
      permissionUsage,
      dataAccess,
      permissionChanges,
      riskDistribution,
      topUsers: topUsers.filter(u => u.userId !== null).map(u => ({ userId: u.userId!, activityCount: u.activityCount })),
      topPermissions,
    };
  }

  /**
   * Get sensitive operations audit trail
   */
  async getSensitiveOperationsAudit(companyId: number, startDate?: Date, endDate?: Date): Promise<{
    sensitivePermissionUsage: PermissionAuditLog[];
    sensitiveDataAccess: DataAccessAuditLog[];
    highRiskChanges: PermissionChangeLog[];
  }> {
    const conditions = [
      eq(permissionAuditLogs.company_id, companyId),
      eq(permissionAuditLogs.is_sensitive_operation, true),
    ];

    const dataConditions = [eq(dataAccessAuditLogs.company_id, companyId)];
    const changeConditions = [
      eq(permissionChangeLogs.company_id, companyId),
      inArray(permissionChangeLogs.risk_assessment, ['high', 'critical']),
    ];

    if (startDate) {
      conditions.push(gte(permissionAuditLogs.timestamp, startDate));
      dataConditions.push(gte(dataAccessAuditLogs.timestamp, startDate));
      changeConditions.push(gte(permissionChangeLogs.timestamp, startDate));
    }
    if (endDate) {
      conditions.push(lte(permissionAuditLogs.timestamp, endDate));
      dataConditions.push(lte(dataAccessAuditLogs.timestamp, endDate));
      changeConditions.push(lte(permissionChangeLogs.timestamp, endDate));
    }

    // Sensitive permission usage
    const sensitivePermissionUsage = await db
      .select()
      .from(permissionAuditLogs)
      .where(and(...conditions))
      .orderBy(desc(permissionAuditLogs.timestamp))
      .limit(100);

    // Sensitive data access (where sensitive fields were accessed)
    const sensitiveDataAccess = await db
      .select()
      .from(dataAccessAuditLogs)
      .where(and(
        ...dataConditions,
        sql`jsonb_array_length(${dataAccessAuditLogs.sensitive_fields_accessed}) > 0`
      ))
      .orderBy(desc(dataAccessAuditLogs.timestamp))
      .limit(100);

    // High-risk permission changes
    const highRiskChanges = await db
      .select()
      .from(permissionChangeLogs)
      .where(and(...changeConditions))
      .orderBy(desc(permissionChangeLogs.timestamp))
      .limit(100);

    return {
      sensitivePermissionUsage,
      sensitiveDataAccess,
      highRiskChanges,
    };
  }

  /**
   * Get compliance audit report
   */
  async getComplianceAuditReport(companyId: number, startDate: Date, endDate: Date): Promise<{
    totalAuditEvents: number;
    complianceViolations: number;
    dataRetentionCompliance: { compliant: number; nonCompliant: number };
    accessPatterns: Array<{ hour: number; accessCount: number }>;
    failedAccessAttempts: PermissionAuditLog[];
    dataExports: DataAccessAuditLog[];
    adminActions: PermissionChangeLog[];
  }> {
    const permissionConditions = [
      eq(permissionAuditLogs.company_id, companyId),
      gte(permissionAuditLogs.timestamp, startDate),
      lte(permissionAuditLogs.timestamp, endDate),
    ];

    const dataConditions = [
      eq(dataAccessAuditLogs.company_id, companyId),
      gte(dataAccessAuditLogs.timestamp, startDate),
      lte(dataAccessAuditLogs.timestamp, endDate),
    ];

    const changeConditions = [
      eq(permissionChangeLogs.company_id, companyId),
      gte(permissionChangeLogs.timestamp, startDate),
      lte(permissionChangeLogs.timestamp, endDate),
    ];

    // Total audit events
    const [permissionCount] = await db
      .select({ count: count() })
      .from(permissionAuditLogs)
      .where(and(...permissionConditions));

    const [dataCount] = await db
      .select({ count: count() })
      .from(dataAccessAuditLogs)
      .where(and(...dataConditions));

    const [changeCount] = await db
      .select({ count: count() })
      .from(permissionChangeLogs)
      .where(and(...changeConditions));

    const totalAuditEvents = permissionCount.count + dataCount.count + changeCount.count;

    // Compliance violations (events with compliance flags)
    const [violationCount] = await db
      .select({ count: count() })
      .from(permissionAuditLogs)
      .where(and(
        ...permissionConditions,
        sql`jsonb_array_length(${permissionAuditLogs.compliance_flags}) > 0`
      ));

    // Access patterns by hour
    const accessPatterns = await db
      .select({
        hour: sql<number>`EXTRACT(HOUR FROM ${permissionAuditLogs.timestamp})`.as('hour'),
        accessCount: count(),
      })
      .from(permissionAuditLogs)
      .where(and(...permissionConditions))
      .groupBy(sql`hour`)
      .orderBy(sql`hour`);

    // Failed access attempts
    const failedAccessAttempts = await db
      .select()
      .from(permissionAuditLogs)
      .where(and(
        ...permissionConditions,
        eq(permissionAuditLogs.result, 'denied')
      ))
      .orderBy(desc(permissionAuditLogs.timestamp))
      .limit(50);

    // Data exports
    const dataExports = await db
      .select()
      .from(dataAccessAuditLogs)
      .where(and(
        ...dataConditions,
        eq(dataAccessAuditLogs.action, 'exported')
      ))
      .orderBy(desc(dataAccessAuditLogs.timestamp))
      .limit(50);

    // Admin actions (permission changes by admins)
    const adminActions = await db
      .select()
      .from(permissionChangeLogs)
      .where(and(...changeConditions))
      .orderBy(desc(permissionChangeLogs.timestamp))
      .limit(50);

    return {
      totalAuditEvents,
      complianceViolations: violationCount.count,
      dataRetentionCompliance: { compliant: 0, nonCompliant: 0 }, // TODO: Implement based on retention policies
      accessPatterns,
      failedAccessAttempts,
      dataExports,
      adminActions,
    };
  }
}

// Create singleton instance
export const auditService = new AuditService();
