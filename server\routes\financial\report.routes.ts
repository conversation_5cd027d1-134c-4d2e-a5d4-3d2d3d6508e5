import { Express, Response } from 'express';
import { storage } from '../../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../../middleware/auth';

export function registerReportRoutes(app: Express): void {
  // Get profit/loss report
  app.get('/api/companies/:companyId/reports/profit-loss', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      // Parse query parameters (support both startDate/endDate and start_date/end_date)
      const startDate = (req.query.startDate as string) || (req.query.start_date as string) || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Default to 90 days ago
      const endDate = (req.query.endDate as string) || (req.query.end_date as string) || new Date().toISOString().split('T')[0]; // Default to today
      
      const report = await storage.getProfitLossReport(companyId, startDate, endDate);
      return res.json(report);
    } catch (error) {
      console.error(`Error generating profit/loss report for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get cash flow report
  app.get('/api/companies/:companyId/reports/cash-flow', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      // Parse query parameters (support both startDate/endDate and start_date/end_date)
      const startDate = (req.query.startDate as string) || (req.query.start_date as string) || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Default to 30 days ago
      const endDate = (req.query.endDate as string) || (req.query.end_date as string) || new Date().toISOString().split('T')[0]; // Default to today
      
      const report = await storage.getCashFlowReport(companyId, startDate, endDate);
      return res.json(report);
    } catch (error) {
      console.error(`Error generating cash flow report for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get collection report
  app.get('/api/companies/:companyId/reports/collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      // Parse query parameters (support both startDate/endDate and start_date/end_date)
      const startDate = (req.query.startDate as string) || (req.query.start_date as string) || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Default to 30 days ago
      const endDate = (req.query.endDate as string) || (req.query.end_date as string) || new Date().toISOString().split('T')[0]; // Default to today
      
      const report = await storage.getCollectionReport(companyId, startDate, endDate);
      return res.json(report);
    } catch (error) {
      console.error(`Error generating collection report for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get daily collections report
  app.get('/api/companies/:companyId/reports/daily-collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract filter parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;
      const status = req.query.status as string;
      const agentId = req.query.agentId ? parseInt(req.query.agentId as string) : undefined;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : undefined;
      const paymentMethod = req.query.paymentMethod as string;

      // Validate required parameters
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Validate date format
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        return res.status(400).json({ message: 'Invalid date format. Please use YYYY-MM-DD format.' });
      }

      // Generate report
      const report = await storage.getDailyCollectionsReport(
        companyId,
        startDate,
        endDate,
        status,
        agentId,
        branchId,
        paymentMethod
      );
      return res.json(report);
    } catch (error) {
      console.error(`Error generating daily collections report for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
