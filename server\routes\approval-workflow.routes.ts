import { Express, Response } from 'express';
import { authMiddleware, requirePermission, AuthRequest } from '../middleware/auth';
import { ApprovalWorkflowService } from '../services/approvalWorkflowService';
import {
  insertApprovalWorkflowSchema,
  insertApprovalWorkflowStepSchema,
  insertApprovalActionSchema,
  insertApprovalEscalationRuleSchema,
} from '../../shared/schema';
import { z } from 'zod';

// Validation schemas
const createWorkflowSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  workflowType: z.enum(['permission_elevation', 'loan_approval', 'customer_data_access', 'emergency_access', 'role_assignment', 'custom']),
  triggerConditions: z.record(z.any()).optional(),
  autoEscalationHours: z.number().int().min(1).max(8760).default(24),
  maxEscalationLevels: z.number().int().min(1).max(10).default(3),
  steps: z.array(z.object({
    stepName: z.string().min(1).max(255),
    stepType: z.enum(['sequential', 'parallel', 'any_one', 'majority', 'unanimous']).default('sequential'),
    requiredApprovers: z.number().int().min(1).default(1),
    approverRoles: z.array(z.number().int()).optional(),
    approverUsers: z.array(z.number().int()).optional(),
    escalationRoles: z.array(z.number().int()).optional(),
    stepTimeoutHours: z.number().int().min(1).max(8760).default(24),
    isOptional: z.boolean().default(false),
    conditions: z.record(z.any()).optional(),
  })).min(1),
});

const startWorkflowSchema = z.object({
  requestType: z.string().min(1),
  requestId: z.string().min(1),
  requestData: z.record(z.any()),
  priority: z.enum(['low', 'medium', 'high', 'emergency']).default('medium'),
  metadata: z.record(z.any()).optional(),
});

const approvalDecisionSchema = z.object({
  action: z.enum(['approve', 'deny', 'delegate', 'escalate', 'request_info']),
  comments: z.string().optional(),
  delegatedTo: z.number().int().optional(),
  actionData: z.record(z.any()).optional(),
});

export function setupApprovalWorkflowRoutes(app: Express) {
  console.log('🔧 [SETUP] Registering approval workflow routes...');
  const workflowService = new ApprovalWorkflowService();


  // ==================== WORKFLOW MANAGEMENT ====================

  // Create workflow
  app.post('/api/approval-workflows', authMiddleware, requirePermission('workflow_create'), async (req: AuthRequest, res: Response) => {
    try {
      const result = createWorkflowSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const workflow = await workflowService.createWorkflow(
        req.user!.company_id!,
        req.user!.id,
        result.data
      );

      return res.status(201).json(workflow);
    } catch (error: any) {
      console.error('Error creating workflow:', error);
      return res.status(400).json({
        message: error.message || 'Failed to create workflow'
      });
    }
  });

  // Get workflows
  app.get('/api/approval-workflows', authMiddleware, requirePermission('workflow_view'), async (req: AuthRequest, res: Response) => {
    try {
      const workflowType = req.query.type as string;

      const workflows = await workflowService.getWorkflows(
        req.user!.company_id!,
        workflowType as any
      );

      return res.json(workflows);
    } catch (error: any) {
      console.error('Error getting workflows:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get workflows'
      });
    }
  });

  // Get all workflow instances (MUST come before /:id route)
  app.get('/api/approval-workflows/instances', authMiddleware, requirePermission('workflow_view'), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : req.user!.company_id!;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const instances = await workflowService.getWorkflowInstances(companyId);
      return res.json(instances);
    } catch (error: any) {
      console.error('Error getting workflow instances:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get workflow instances'
      });
    }
  });

  // Get pending approvals for current user (MUST come before /:id route)
  app.get('/api/approval-workflows/pending', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const pendingApprovals = await workflowService.getPendingApprovalsForUser(
        req.user!.id,
        req.user!.company_id!
      );

      return res.json(pendingApprovals);
    } catch (error: any) {
      console.error('Error getting pending approvals:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get pending approvals'
      });
    }
  });

  // Get workflow with steps (MUST come after specific routes)
  app.get('/api/approval-workflows/:id', authMiddleware, requirePermission('workflow_view'), async (req: AuthRequest, res: Response) => {
    try {
      const workflowId = parseInt(req.params.id);

      if (isNaN(workflowId)) {
        return res.status(400).json({ message: 'Invalid workflow ID' });
      }

      const result = await workflowService.getWorkflowWithSteps(workflowId);
      return res.json(result);
    } catch (error: any) {
      console.error('Error getting workflow:', error);
      return res.status(404).json({
        message: error.message || 'Workflow not found'
      });
    }
  });

  // Update workflow
  app.put('/api/approval-workflows/:id', authMiddleware, requirePermission('workflow_edit'), async (req: AuthRequest, res: Response) => {
    try {
      const workflowId = parseInt(req.params.id);

      if (isNaN(workflowId)) {
        return res.status(400).json({ message: 'Invalid workflow ID' });
      }

      const result = insertApprovalWorkflowSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const workflow = await workflowService.updateWorkflow(workflowId, result.data);
      return res.json(workflow);
    } catch (error: any) {
      console.error('Error updating workflow:', error);
      return res.status(400).json({
        message: error.message || 'Failed to update workflow'
      });
    }
  });

  // Delete workflow
  app.delete('/api/approval-workflows/:id', authMiddleware, requirePermission('workflow_delete'), async (req: AuthRequest, res: Response) => {
    try {
      const workflowId = parseInt(req.params.id);

      if (isNaN(workflowId)) {
        return res.status(400).json({ message: 'Invalid workflow ID' });
      }

      await workflowService.deleteWorkflow(workflowId);
      return res.json({ message: 'Workflow deleted successfully' });
    } catch (error: any) {
      console.error('Error deleting workflow:', error);
      return res.status(400).json({
        message: error.message || 'Failed to delete workflow'
      });
    }
  });

  // ==================== WORKFLOW EXECUTION ====================

  // Start workflow
  app.post('/api/approval-workflows/start', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const result = startWorkflowSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const context = {
        ...result.data,
        requesterId: req.user!.id,
        companyId: req.user!.company_id!,
      };

      const workflowResult = await workflowService.startWorkflow(context);
      return res.status(201).json(workflowResult);
    } catch (error: any) {
      console.error('Error starting workflow:', error);
      return res.status(400).json({
        message: error.message || 'Failed to start workflow'
      });
    }
  });



  // Get workflow status
  app.get('/api/approval-workflows/instances/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {


      const workflowInstanceId = parseInt(req.params.id);

      if (isNaN(workflowInstanceId)) {
        return res.status(400).json({ message: 'Invalid workflow instance ID' });
      }

      const status = await workflowService.getWorkflowStatus(workflowInstanceId);
      return res.json(status);
    } catch (error: any) {
      console.error('Error getting workflow status:', error);
      return res.status(404).json({
        message: error.message || 'Workflow instance not found'
      });
    }
  });

  // Process approval decision
  app.post('/api/approval-workflows/instances/:workflowId/steps/:stepId/approve', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const workflowInstanceId = parseInt(req.params.workflowId);
      const stepInstanceId = parseInt(req.params.stepId);

      if (isNaN(workflowInstanceId) || isNaN(stepInstanceId)) {
        return res.status(400).json({ message: 'Invalid workflow or step instance ID' });
      }

      const result = approvalDecisionSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const workflowResult = await workflowService.processApprovalDecision(
        workflowInstanceId,
        stepInstanceId,
        req.user!.id,
        result.data
      );

      return res.json(workflowResult);
    } catch (error: any) {
      console.error('Error processing approval decision:', error);
      return res.status(400).json({
        message: error.message || 'Failed to process approval decision'
      });
    }
  });

  // Cancel workflow
  app.post('/api/approval-workflows/instances/:id/cancel', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const workflowInstanceId = parseInt(req.params.id);

      if (isNaN(workflowInstanceId)) {
        return res.status(400).json({ message: 'Invalid workflow instance ID' });
      }

      const { reason } = req.body;

      await workflowService.cancelWorkflow(workflowInstanceId, req.user!.id, reason);
      return res.json({ message: 'Workflow cancelled successfully' });
    } catch (error: any) {
      console.error('Error cancelling workflow:', error);
      return res.status(400).json({
        message: error.message || 'Failed to cancel workflow'
      });
    }
  });



  // Process escalations (admin endpoint)
  app.post('/api/approval-workflows/escalations/process', authMiddleware, requirePermission('workflow_admin'), async (req: AuthRequest, res: Response) => {
    try {
      const escalatedCount = await workflowService.processEscalations(req.user!.company_id!);

      return res.json({
        message: `Processed ${escalatedCount} escalations`,
        escalatedCount
      });
    } catch (error: any) {
      console.error('Error processing escalations:', error);
      return res.status(500).json({
        message: error.message || 'Failed to process escalations'
      });
    }
  });
}
