import { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { 
  Plus, 
  Search, 
  CreditCard, 
  Calendar, 
  DollarSign, 
  Package, 
  CheckCircle2, 
  XCircle, 
  Edit, 
  Trash2,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { usdToFormattedInr } from "@/lib/currency";

// Mock subscription plans data
const mockPlans = [
  {
    id: 1,
    name: "Basic",
    description: "Ideal for small microfinance institutions",
    price: 99.99,
    billing_period: "monthly",
    features: [
      "Up to 5 users",
      "Basic collection tracking",
      "Customer management",
      "Standard reports"
    ],
    is_active: true,
    created_at: "2023-01-10T10:00:00Z"
  },
  {
    id: 2,
    name: "Professional",
    description: "Perfect for growing businesses",
    price: 199.99,
    billing_period: "monthly",
    features: [
      "Up to 20 users",
      "Advanced collection tracking",
      "Customer segmentation",
      "Advanced analytics",
      "Agent performance metrics"
    ],
    is_active: true,
    created_at: "2023-01-12T14:30:00Z"
  },
  {
    id: 3,
    name: "Enterprise",
    description: "For large financial institutions",
    price: 499.99,
    billing_period: "monthly",
    features: [
      "Unlimited users",
      "White-label solution",
      "Priority support",
      "Custom integrations",
      "Advanced reporting and analytics",
      "Full API access"
    ],
    is_active: true,
    created_at: "2023-01-15T09:15:00Z"
  },
  {
    id: 4,
    name: "Annual Basic",
    description: "Basic plan with annual billing",
    price: 999.99,
    billing_period: "annual",
    features: [
      "Up to 5 users",
      "Basic collection tracking",
      "Customer management",
      "Standard reports"
    ],
    is_active: true,
    created_at: "2023-02-01T11:20:00Z"
  }
];

// Mock subscriptions data
const mockSubscriptions = [
  {
    id: 1,
    company_id: 1,
    company_name: "Skyline Finance Co.",
    plan_id: 2,
    plan_name: "Professional",
    status: "active",
    start_date: "2023-03-01T00:00:00Z",
    end_date: "2024-03-01T00:00:00Z",
    billing_cycle: "monthly",
    last_payment_date: "2023-10-01T00:00:00Z",
    next_payment_date: "2023-11-01T00:00:00Z",
    payment_method: "credit_card",
    auto_renew: true
  },
  {
    id: 2,
    company_id: 2,
    company_name: "Golden Capital Ltd.",
    plan_id: 3,
    plan_name: "Enterprise",
    status: "active",
    start_date: "2023-02-15T00:00:00Z",
    end_date: "2024-02-15T00:00:00Z",
    billing_cycle: "annual",
    last_payment_date: "2023-02-15T00:00:00Z",
    next_payment_date: "2024-02-15T00:00:00Z",
    payment_method: "bank_transfer",
    auto_renew: true
  },
  {
    id: 3,
    company_id: 3,
    company_name: "Pinnacle Microfinance",
    plan_id: 1,
    plan_name: "Basic",
    status: "pending",
    start_date: "2023-10-25T00:00:00Z",
    end_date: "2023-11-25T00:00:00Z",
    billing_cycle: "monthly",
    last_payment_date: null,
    next_payment_date: "2023-10-25T00:00:00Z",
    payment_method: "credit_card",
    auto_renew: true
  }
];

// Mock company data for dropdown
const mockCompanies = [
  {
    id: 1,
    name: "Skyline Finance Co."
  },
  {
    id: 2,
    name: "Golden Capital Ltd."
  },
  {
    id: 3,
    name: "Pinnacle Microfinance"
  }
];

type Plan = typeof mockPlans[0];
type Subscription = typeof mockSubscriptions[0];

export default function Subscriptions() {
  const { toast } = useToast();
  const [plans, setPlans] = useState<Plan[]>(mockPlans);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>(mockSubscriptions);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<"subscriptions" | "plans">("subscriptions");
  
  // Plan state
  const [isCreatePlanOpen, setIsCreatePlanOpen] = useState(false);
  const [isEditPlanOpen, setIsEditPlanOpen] = useState(false);
  const [isDeletePlanOpen, setIsDeletePlanOpen] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<Plan | null>(null);
  const [planForm, setPlanForm] = useState({
    name: "",
    description: "",
    price: "",
    billing_period: "monthly",
    is_active: true,
    features: [""]
  });
  
  // Subscription state
  const [isCreateSubOpen, setIsCreateSubOpen] = useState(false);
  const [isEditSubOpen, setIsEditSubOpen] = useState(false);
  const [isDeleteSubOpen, setIsDeleteSubOpen] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null);
  const [subscriptionForm, setSubscriptionForm] = useState({
    company_id: "",
    company_name: "",
    plan_id: "",
    status: "pending",
    billing_cycle: "monthly",
    payment_method: "credit_card",
    auto_renew: true
  });
  
  // Filtered plans and subscriptions based on search
  const filteredPlans = plans.filter(plan => 
    plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    plan.description.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const filteredSubscriptions = subscriptions.filter(sub => 
    sub.company_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    sub.plan_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    sub.status.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };
  
  // Format currency - convert USD to INR for Indian customers
  const formatCurrency = (amount: number) => {
    return usdToFormattedInr(amount);
  };
  
  // Handle plan form change
  const handlePlanFormChange = (field: string, value: any) => {
    setPlanForm(prev => ({ ...prev, [field]: value }));
  };
  
  // Add feature field to plan form
  const addFeatureField = () => {
    setPlanForm(prev => ({
      ...prev,
      features: [...prev.features, ""]
    }));
  };
  
  // Update feature in plan form
  const updateFeature = (index: number, value: string) => {
    const updatedFeatures = [...planForm.features];
    updatedFeatures[index] = value;
    setPlanForm(prev => ({
      ...prev,
      features: updatedFeatures
    }));
  };
  
  // Remove feature field from plan form
  const removeFeature = (index: number) => {
    if (planForm.features.length <= 1) return;
    const updatedFeatures = [...planForm.features];
    updatedFeatures.splice(index, 1);
    setPlanForm(prev => ({
      ...prev,
      features: updatedFeatures
    }));
  };
  
  // Open create plan dialog
  const handleOpenCreatePlan = () => {
    setPlanForm({
      name: "",
      description: "",
      price: "",
      billing_period: "monthly",
      is_active: true,
      features: [""]
    });
    setIsCreatePlanOpen(true);
  };
  
  // Open edit plan dialog
  const handleOpenEditPlan = (plan: Plan) => {
    setCurrentPlan(plan);
    setPlanForm({
      name: plan.name,
      description: plan.description,
      price: plan.price.toString(),
      billing_period: plan.billing_period,
      is_active: plan.is_active,
      features: [...plan.features]
    });
    setIsEditPlanOpen(true);
  };
  
  // Open delete plan dialog
  const handleOpenDeletePlan = (plan: Plan) => {
    setCurrentPlan(plan);
    setIsDeletePlanOpen(true);
  };
  
  // Create new plan
  const handleCreatePlan = () => {
    if (!planForm.name || !planForm.price) {
      toast({
        title: "Required fields missing",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }
    
    // Filter out empty features
    const features = planForm.features.filter(f => f.trim() !== "");
    
    const newPlan: Plan = {
      id: plans.length + 1,
      name: planForm.name,
      description: planForm.description,
      price: parseFloat(planForm.price),
      billing_period: planForm.billing_period,
      features: features,
      is_active: planForm.is_active,
      created_at: new Date().toISOString()
    };
    
    setPlans([...plans, newPlan]);
    setIsCreatePlanOpen(false);
    
    toast({
      title: "Plan created",
      description: `${newPlan.name} plan has been created successfully.`
    });
  };
  
  // Update existing plan
  const handleUpdatePlan = () => {
    if (!currentPlan) return;
    
    if (!planForm.name || !planForm.price) {
      toast({
        title: "Required fields missing",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }
    
    // Filter out empty features
    const features = planForm.features.filter(f => f.trim() !== "");
    
    const updatedPlans = plans.map(plan => 
      plan.id === currentPlan.id ? {
        ...plan,
        name: planForm.name,
        description: planForm.description,
        price: parseFloat(planForm.price),
        billing_period: planForm.billing_period,
        features: features,
        is_active: planForm.is_active
      } : plan
    );
    
    setPlans(updatedPlans);
    setIsEditPlanOpen(false);
    
    toast({
      title: "Plan updated",
      description: `${planForm.name} plan has been updated successfully.`
    });
  };
  
  // Delete plan
  const handleDeletePlan = () => {
    if (!currentPlan) return;
    
    // Check if plan is in use
    const planInUse = subscriptions.some(sub => sub.plan_id === currentPlan.id);
    
    if (planInUse) {
      toast({
        title: "Cannot delete plan",
        description: "This plan is currently in use by one or more subscriptions.",
        variant: "destructive"
      });
      setIsDeletePlanOpen(false);
      return;
    }
    
    const updatedPlans = plans.filter(plan => plan.id !== currentPlan.id);
    setPlans(updatedPlans);
    setIsDeletePlanOpen(false);
    
    toast({
      title: "Plan deleted",
      description: `${currentPlan.name} plan has been deleted successfully.`
    });
  };
  
  // Handle subscription form change
  const handleSubFormChange = (field: string, value: any) => {
    setSubscriptionForm(prev => ({ ...prev, [field]: value }));
  };
  
  // Open create subscription dialog
  const handleOpenCreateSub = () => {
    setSubscriptionForm({
      company_id: "",
      company_name: "",
      plan_id: "",
      status: "pending",
      billing_cycle: "monthly",
      payment_method: "credit_card",
      auto_renew: true
    });
    setIsCreateSubOpen(true);
  };
  
  // Open edit subscription dialog
  const handleOpenEditSub = (subscription: Subscription) => {
    setCurrentSubscription(subscription);
    setSubscriptionForm({
      company_id: subscription.company_id.toString(),
      company_name: subscription.company_name,
      plan_id: subscription.plan_id.toString(),
      status: subscription.status,
      billing_cycle: subscription.billing_cycle,
      payment_method: subscription.payment_method,
      auto_renew: subscription.auto_renew
    });
    setIsEditSubOpen(true);
  };
  
  // Open delete subscription dialog
  const handleOpenDeleteSub = (subscription: Subscription) => {
    setCurrentSubscription(subscription);
    setIsDeleteSubOpen(true);
  };
  
  // Create new subscription
  const handleCreateSubscription = () => {
    if (!subscriptionForm.company_id || !subscriptionForm.plan_id) {
      toast({
        title: "Required fields missing",
        description: "Please select a company and a plan.",
        variant: "destructive"
      });
      return;
    }
    
    const selectedPlan = plans.find(p => p.id.toString() === subscriptionForm.plan_id);
    
    if (!selectedPlan) {
      toast({
        title: "Invalid plan",
        description: "Please select a valid subscription plan.",
        variant: "destructive"
      });
      return;
    }
    
    const today = new Date();
    const endDate = new Date(today);
    
    if (subscriptionForm.billing_cycle === "monthly") {
      endDate.setMonth(endDate.getMonth() + 1);
    } else {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }
    
    const newSubscription: Subscription = {
      id: subscriptions.length + 1,
      company_id: parseInt(subscriptionForm.company_id),
      company_name: subscriptionForm.company_name,
      plan_id: parseInt(subscriptionForm.plan_id),
      plan_name: selectedPlan.name,
      status: subscriptionForm.status,
      start_date: today.toISOString(),
      end_date: endDate.toISOString(),
      billing_cycle: subscriptionForm.billing_cycle,
      last_payment_date: subscriptionForm.status === "active" ? today.toISOString() : null,
      next_payment_date: endDate.toISOString(),
      payment_method: subscriptionForm.payment_method,
      auto_renew: subscriptionForm.auto_renew
    };
    
    setSubscriptions([...subscriptions, newSubscription]);
    setIsCreateSubOpen(false);
    
    toast({
      title: "Subscription created",
      description: `Subscription for ${subscriptionForm.company_name} has been created successfully.`
    });
  };
  
  // Update existing subscription
  const handleUpdateSubscription = () => {
    if (!currentSubscription) return;
    
    const selectedPlan = plans.find(p => p.id.toString() === subscriptionForm.plan_id);
    
    if (!selectedPlan) {
      toast({
        title: "Invalid plan",
        description: "Please select a valid subscription plan.",
        variant: "destructive"
      });
      return;
    }
    
    const updatedSubscriptions = subscriptions.map(sub => 
      sub.id === currentSubscription.id ? {
        ...sub,
        plan_id: parseInt(subscriptionForm.plan_id),
        plan_name: selectedPlan.name,
        status: subscriptionForm.status,
        billing_cycle: subscriptionForm.billing_cycle,
        payment_method: subscriptionForm.payment_method,
        auto_renew: subscriptionForm.auto_renew
      } : sub
    );
    
    setSubscriptions(updatedSubscriptions);
    setIsEditSubOpen(false);
    
    toast({
      title: "Subscription updated",
      description: `Subscription for ${currentSubscription.company_name} has been updated successfully.`
    });
  };
  
  // Delete subscription
  const handleDeleteSubscription = () => {
    if (!currentSubscription) return;
    
    const updatedSubscriptions = subscriptions.filter(sub => sub.id !== currentSubscription.id);
    setSubscriptions(updatedSubscriptions);
    setIsDeleteSubOpen(false);
    
    toast({
      title: "Subscription deleted",
      description: `Subscription for ${currentSubscription.company_name} has been deleted successfully.`
    });
  };
  
  // Get status badge class
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Subscription Management</h1>
        
        <div className="flex gap-2">
          {activeTab === "plans" && (
            <Button onClick={handleOpenCreatePlan} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Plan
            </Button>
          )}
          
          {activeTab === "subscriptions" && (
            <Button onClick={handleOpenCreateSub} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Subscription
            </Button>
          )}
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="text-xl">
                {activeTab === "subscriptions" ? "Active Subscriptions" : "Subscription Plans"}
              </CardTitle>
              <CardDescription>
                {activeTab === "subscriptions" 
                  ? "Manage company subscriptions to your services"
                  : "Configure and manage subscription plans for your platform"
                }
              </CardDescription>
            </div>
            
            <div className="flex gap-2">
              <Button 
                variant={activeTab === "subscriptions" ? "default" : "outline"} 
                onClick={() => setActiveTab("subscriptions")}
                size="sm"
              >
                Subscriptions
              </Button>
              <Button 
                variant={activeTab === "plans" ? "default" : "outline"} 
                onClick={() => setActiveTab("plans")}
                size="sm"
              >
                Plans
              </Button>
            </div>
          </div>
          
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder={activeTab === "subscriptions" 
                ? "Search subscriptions by company or plan..." 
                : "Search plans by name or description..."
              }
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Subscriptions Tab */}
          {activeTab === "subscriptions" && (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Company</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Billing Cycle</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>Next Payment</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                
                <TableBody>
                  {filteredSubscriptions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center h-24 text-gray-500">
                        No subscriptions found matching your search.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredSubscriptions.map((subscription) => (
                      <TableRow key={subscription.id}>
                        <TableCell className="font-medium">{subscription.company_name}</TableCell>
                        <TableCell>{subscription.plan_name}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(subscription.status)}`}>
                            {subscription.status}
                          </span>
                        </TableCell>
                        <TableCell className="capitalize">{subscription.billing_cycle}</TableCell>
                        <TableCell>{formatDate(subscription.start_date)}</TableCell>
                        <TableCell>{formatDate(subscription.next_payment_date)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="h-8 w-8 text-amber-600"
                              onClick={() => handleOpenEditSub(subscription)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="h-8 w-8 text-red-600"
                              onClick={() => handleOpenDeleteSub(subscription)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
          
          {/* Plans Tab */}
          {activeTab === "plans" && (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Billing Period</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                
                <TableBody>
                  {filteredPlans.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center h-24 text-gray-500">
                        No plans found matching your search.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPlans.map((plan) => (
                      <TableRow key={plan.id}>
                        <TableCell className="font-medium">{plan.name}</TableCell>
                        <TableCell className="max-w-xs truncate">{plan.description}</TableCell>
                        <TableCell>{formatCurrency(plan.price)}/{plan.billing_period === "monthly" ? "mo" : "yr"}</TableCell>
                        <TableCell className="capitalize">{plan.billing_period}</TableCell>
                        <TableCell>
                          {plan.is_active ? (
                            <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
                              Inactive
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="h-8 w-8 text-amber-600"
                              onClick={() => handleOpenEditPlan(plan)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="h-8 w-8 text-red-600"
                              onClick={() => handleOpenDeletePlan(plan)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex justify-between border-t p-4">
          <div className="text-sm text-gray-500">
            {activeTab === "subscriptions" 
              ? `Showing ${filteredSubscriptions.length} of ${subscriptions.length} subscriptions`
              : `Showing ${filteredPlans.length} of ${plans.length} plans`
            }
          </div>
        </CardFooter>
      </Card>
      
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              {activeTab === "subscriptions" ? "Active Subscriptions" : "Active Plans"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              {activeTab === "subscriptions" ? (
                <>
                  <CreditCard className="h-6 w-6 mr-2 text-blue-600" />
                  <span className="text-2xl font-bold">
                    {subscriptions.filter(s => s.status === "active").length}
                  </span>
                </>
              ) : (
                <>
                  <Package className="h-6 w-6 mr-2 text-blue-600" />
                  <span className="text-2xl font-bold">
                    {plans.filter(p => p.is_active).length}
                  </span>
                </>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              {activeTab === "subscriptions" ? "Monthly Revenue" : "Popular Plan"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeTab === "subscriptions" ? (
              <div className="flex items-center">
                <DollarSign className="h-6 w-6 mr-2 text-green-600" />
                <span className="text-2xl font-bold">
                  {formatCurrency(
                    subscriptions
                      .filter(s => s.status === "active" && s.billing_cycle === "monthly")
                      .reduce((sum, sub) => {
                        const plan = plans.find(p => p.id === sub.plan_id);
                        return sum + (plan ? plan.price : 0);
                      }, 0)
                  )}
                </span>
              </div>
            ) : (
              <div className="flex items-center">
                <CheckCircle2 className="h-6 w-6 mr-2 text-green-600" />
                <span className="text-2xl font-bold">
                  {
                    (() => {
                      const planCounts = plans.map(plan => ({
                        name: plan.name,
                        count: subscriptions.filter(sub => sub.plan_id === plan.id).length
                      }));
                      planCounts.sort((a, b) => b.count - a.count);
                      return planCounts.length > 0 ? planCounts[0].name : "N/A";
                    })()
                  }
                </span>
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              {activeTab === "subscriptions" ? "Renewal Rate" : "Avg. Price"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeTab === "subscriptions" ? (
              <div className="flex items-center">
                <Calendar className="h-6 w-6 mr-2 text-purple-600" />
                <span className="text-2xl font-bold">
                  {
                    subscriptions.length > 0
                      ? `${Math.round(
                          (subscriptions.filter(s => s.auto_renew).length / subscriptions.length) * 100
                        )}%`
                      : "0%"
                  }
                </span>
              </div>
            ) : (
              <div className="flex items-center">
                <DollarSign className="h-6 w-6 mr-2 text-purple-600" />
                <span className="text-2xl font-bold">
                  {formatCurrency(
                    plans.length > 0
                      ? plans.reduce((sum, plan) => sum + plan.price, 0) / plans.length
                      : 0
                  )}
                </span>
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              {activeTab === "subscriptions" ? "Expiring Soon" : "Inactive Plans"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeTab === "subscriptions" ? (
              <div className="flex items-center">
                <AlertCircle className="h-6 w-6 mr-2 text-amber-600" />
                <span className="text-2xl font-bold">
                  {
                    (() => {
                      const now = new Date();
                      const thirtyDaysFromNow = new Date(now);
                      thirtyDaysFromNow.setDate(now.getDate() + 30);
                      
                      return subscriptions.filter(sub => {
                        const endDate = new Date(sub.end_date);
                        return sub.status === "active" && 
                               endDate <= thirtyDaysFromNow &&
                               endDate >= now;
                      }).length;
                    })()
                  }
                </span>
              </div>
            ) : (
              <div className="flex items-center">
                <XCircle className="h-6 w-6 mr-2 text-gray-600" />
                <span className="text-2xl font-bold">
                  {plans.filter(p => !p.is_active).length}
                </span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Create Plan Dialog */}
      <Dialog open={isCreatePlanOpen} onOpenChange={setIsCreatePlanOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Plan</DialogTitle>
            <DialogDescription>
              Add a new subscription plan to your platform.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Plan Name <span className="text-red-500">*</span></Label>
              <Input
                id="name"
                value={planForm.name}
                onChange={(e) => handlePlanFormChange("name", e.target.value)}
                placeholder="e.g. Basic, Professional, Enterprise"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={planForm.description}
                onChange={(e) => handlePlanFormChange("description", e.target.value)}
                placeholder="Brief description of the plan"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="price">Price <span className="text-red-500">*</span></Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={planForm.price}
                onChange={(e) => handlePlanFormChange("price", e.target.value)}
                placeholder="99.99"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="billing_period">Billing Period</Label>
              <Select
                value={planForm.billing_period}
                onValueChange={(value) => handlePlanFormChange("billing_period", value)}
              >
                <SelectTrigger id="billing_period">
                  <SelectValue placeholder="Select billing period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="annual">Annual</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Features</Label>
              {planForm.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    value={feature}
                    onChange={(e) => updateFeature(index, e.target.value)}
                    placeholder={`Feature ${index + 1}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeFeature(index)}
                    className="text-red-500"
                  >
                    <XCircle className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addFeatureField}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Feature
              </Button>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={planForm.is_active}
                onCheckedChange={(checked) => handlePlanFormChange("is_active", checked)}
              />
              <Label htmlFor="is_active">Active</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsCreatePlanOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleCreatePlan}>
              Create Plan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Plan Dialog */}
      <Dialog open={isEditPlanOpen} onOpenChange={setIsEditPlanOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Plan</DialogTitle>
            <DialogDescription>
              Update the existing subscription plan.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Plan Name <span className="text-red-500">*</span></Label>
              <Input
                id="edit-name"
                value={planForm.name}
                onChange={(e) => handlePlanFormChange("name", e.target.value)}
                placeholder="e.g. Basic, Professional, Enterprise"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Input
                id="edit-description"
                value={planForm.description}
                onChange={(e) => handlePlanFormChange("description", e.target.value)}
                placeholder="Brief description of the plan"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-price">Price <span className="text-red-500">*</span></Label>
              <Input
                id="edit-price"
                type="number"
                step="0.01"
                min="0"
                value={planForm.price}
                onChange={(e) => handlePlanFormChange("price", e.target.value)}
                placeholder="99.99"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-billing_period">Billing Period</Label>
              <Select
                value={planForm.billing_period}
                onValueChange={(value) => handlePlanFormChange("billing_period", value)}
              >
                <SelectTrigger id="edit-billing_period">
                  <SelectValue placeholder="Select billing period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="annual">Annual</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Features</Label>
              {planForm.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    value={feature}
                    onChange={(e) => updateFeature(index, e.target.value)}
                    placeholder={`Feature ${index + 1}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => removeFeature(index)}
                    className="text-red-500"
                  >
                    <XCircle className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addFeatureField}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Feature
              </Button>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-is_active"
                checked={planForm.is_active}
                onCheckedChange={(checked) => handlePlanFormChange("is_active", checked)}
              />
              <Label htmlFor="edit-is_active">Active</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsEditPlanOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleUpdatePlan}>
              Update Plan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Plan Confirmation Dialog */}
      <Dialog open={isDeletePlanOpen} onOpenChange={setIsDeletePlanOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the "{currentPlan?.name}" plan? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDeletePlanOpen(false)}>
              Cancel
            </Button>
            <Button type="button" variant="destructive" onClick={handleDeletePlan}>
              Delete Plan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Create Subscription Dialog */}
      <Dialog open={isCreateSubOpen} onOpenChange={setIsCreateSubOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Subscription</DialogTitle>
            <DialogDescription>
              Add a new subscription for a company.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="company_id">Company <span className="text-red-500">*</span></Label>
              <Select
                value={subscriptionForm.company_id}
                onValueChange={(value) => {
                  // Find company name
                  const companyName = mockCompanies.find(c => c.id.toString() === value)?.name || "";
                  handleSubFormChange("company_id", value);
                  handleSubFormChange("company_name", companyName);
                }}
              >
                <SelectTrigger id="company_id">
                  <SelectValue placeholder="Select a company" />
                </SelectTrigger>
                <SelectContent>
                  {mockCompanies.map(company => (
                    <SelectItem key={company.id} value={company.id.toString()}>
                      {company.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="plan_id">Subscription Plan <span className="text-red-500">*</span></Label>
              <Select
                value={subscriptionForm.plan_id}
                onValueChange={(value) => handleSubFormChange("plan_id", value)}
              >
                <SelectTrigger id="plan_id">
                  <SelectValue placeholder="Select a plan" />
                </SelectTrigger>
                <SelectContent>
                  {plans.filter(p => p.is_active).map(plan => (
                    <SelectItem key={plan.id} value={plan.id.toString()}>
                      {plan.name} ({formatCurrency(plan.price)}/{plan.billing_period === "monthly" ? "mo" : "yr"})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status">Subscription Status</Label>
              <Select
                value={subscriptionForm.status}
                onValueChange={(value) => handleSubFormChange("status", value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="billing_cycle">Billing Cycle</Label>
              <Select
                value={subscriptionForm.billing_cycle}
                onValueChange={(value) => handleSubFormChange("billing_cycle", value)}
              >
                <SelectTrigger id="billing_cycle">
                  <SelectValue placeholder="Select billing cycle" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="annual">Annual</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="payment_method">Payment Method</Label>
              <Select
                value={subscriptionForm.payment_method}
                onValueChange={(value) => handleSubFormChange("payment_method", value)}
              >
                <SelectTrigger id="payment_method">
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="credit_card">Credit Card</SelectItem>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="paypal">PayPal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="auto_renew"
                checked={subscriptionForm.auto_renew}
                onCheckedChange={(checked) => handleSubFormChange("auto_renew", checked)}
              />
              <Label htmlFor="auto_renew">Auto-renew</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsCreateSubOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleCreateSubscription}>
              Create Subscription
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Subscription Dialog */}
      <Dialog open={isEditSubOpen} onOpenChange={setIsEditSubOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Subscription</DialogTitle>
            <DialogDescription>
              Update the subscription for {currentSubscription?.company_name}.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-company">Company</Label>
              <Input
                id="edit-company"
                value={subscriptionForm.company_name}
                readOnly
                disabled
                className="bg-gray-100"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-plan_id">Subscription Plan <span className="text-red-500">*</span></Label>
              <Select
                value={subscriptionForm.plan_id}
                onValueChange={(value) => handleSubFormChange("plan_id", value)}
              >
                <SelectTrigger id="edit-plan_id">
                  <SelectValue placeholder="Select a plan" />
                </SelectTrigger>
                <SelectContent>
                  {plans.filter(p => p.is_active).map(plan => (
                    <SelectItem key={plan.id} value={plan.id.toString()}>
                      {plan.name} ({formatCurrency(plan.price)}/{plan.billing_period === "monthly" ? "mo" : "yr"})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-status">Subscription Status</Label>
              <Select
                value={subscriptionForm.status}
                onValueChange={(value) => handleSubFormChange("status", value)}
              >
                <SelectTrigger id="edit-status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-billing_cycle">Billing Cycle</Label>
              <Select
                value={subscriptionForm.billing_cycle}
                onValueChange={(value) => handleSubFormChange("billing_cycle", value)}
              >
                <SelectTrigger id="edit-billing_cycle">
                  <SelectValue placeholder="Select billing cycle" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="annual">Annual</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-payment_method">Payment Method</Label>
              <Select
                value={subscriptionForm.payment_method}
                onValueChange={(value) => handleSubFormChange("payment_method", value)}
              >
                <SelectTrigger id="edit-payment_method">
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="credit_card">Credit Card</SelectItem>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="paypal">PayPal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-auto_renew"
                checked={subscriptionForm.auto_renew}
                onCheckedChange={(checked) => handleSubFormChange("auto_renew", checked)}
              />
              <Label htmlFor="edit-auto_renew">Auto-renew</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsEditSubOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleUpdateSubscription}>
              Update Subscription
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Subscription Confirmation Dialog */}
      <Dialog open={isDeleteSubOpen} onOpenChange={setIsDeleteSubOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the subscription for {currentSubscription?.company_name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDeleteSubOpen(false)}>
              Cancel
            </Button>
            <Button type="button" variant="destructive" onClick={handleDeleteSubscription}>
              Delete Subscription
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}