import { db } from '../db';
import { eq, and, gte, lte, sql, desc, ne, lt, like, or, count } from 'drizzle-orm';
import { collections, loans, customers, agents, payments } from '../../shared/schema';
import { Collection, InsertCollection } from '../../shared/schema';
import errorLogger from '../utils/errorLogger';
import { ICollectionStorage } from './interfaces';
import { SQL } from 'drizzle-orm';

export class CollectionStorage implements ICollectionStorage {
  async getCollection(id: number): Promise<Collection | undefined> {
    try {
      const [collection] = await db.select()
        .from(collections)
        .where(eq(collections.id, id));
      return collection;
    } catch (error) {
      errorLogger.logError(`Error fetching collection id=${id}`, 'collection-fetch', error as Error);
      return undefined;
    }
  }

  async getCollectionsByCompany(
    companyId: number,
    status?: string,
    agentId?: number,
    dateRange?: { startDate: string, endDate: string }
  ): Promise<Collection[]> {
    try {
      let query = db.select({
        collection: collections,
        loan_reference_code: loans.loan_reference_code
      })
        .from(collections)
        .leftJoin(loans, eq(collections.loan_id, loans.id))
        .where(eq(collections.company_id, companyId));

      // Apply status filter if provided
      if (status) {
        query = query.where(eq(collections.status, status));
      }

      // Apply agent filter if provided
      if (agentId) {
        query = query.where(eq(collections.agent_id, agentId));
      }

      // Apply date range filter if provided
      if (dateRange) {
        query = query.where(
          and(
            gte(collections.scheduled_date, dateRange.startDate),
            lte(collections.scheduled_date, dateRange.endDate)
          )
        );
      }

      // Order by scheduled date descending
      query = query.orderBy(desc(collections.scheduled_date));

      const result = await query;

      // Map the result to include loan reference code in the collection object
      return result.map(row => ({
        ...row.collection,
        loan: row.loan_reference_code ? { loan_reference_code: row.loan_reference_code } : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching collections for company id=${companyId}`, 'collection-fetch', error as Error);
      return [];
    }
  }

  async getCollectionsByBranch(
    branchId: number,
    status?: string,
    agentId?: number,
    dateRange?: { startDate: string, endDate: string }
  ): Promise<Collection[]> {
    try {
      // First get all loans for this branch
      const branchLoans = await db.select({ id: loans.id })
        .from(loans)
        .where(eq(loans.branch_id, branchId));

      const loanIds = branchLoans.map(loan => loan.id);

      if (loanIds.length === 0) {
        return [];
      }

      // Then get collections for these loans with loan reference codes
      let query = db.select({
        collection: collections,
        loan_reference_code: loans.loan_reference_code
      })
        .from(collections)
        .leftJoin(loans, eq(collections.loan_id, loans.id))
        .where(sql`${collections.loan_id} IN (${loanIds.join(',')})`);

      // Apply status filter if provided
      if (status) {
        query = query.where(eq(collections.status, status));
      }

      // Apply agent filter if provided
      if (agentId) {
        query = query.where(eq(collections.agent_id, agentId));
      }

      // Apply date range filter if provided
      if (dateRange) {
        query = query.where(
          and(
            gte(collections.scheduled_date, dateRange.startDate),
            lte(collections.scheduled_date, dateRange.endDate)
          )
        );
      }

      // Order by scheduled date descending
      query = query.orderBy(desc(collections.scheduled_date));

      const result = await query;

      // Map the result to include loan reference code in the collection object
      return result.map(row => ({
        ...row.collection,
        loan: row.loan_reference_code ? { loan_reference_code: row.loan_reference_code } : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching collections for branch id=${branchId}`, 'collection-fetch', error as Error);
      return [];
    }
  }

  async getCollectionsByLoan(loanId: number, companyId: number): Promise<Collection[]> {
    try {
      // First check if the loan exists and belongs to the company
      const [loan] = await db.select()
        .from(loans)
        .where(
          and(
            eq(loans.id, loanId),
            eq(loans.company_id, companyId)
          )
        );

      if (!loan) {
        return [];
      }

      const result = await db.select({
        collection: collections,
        loan_reference_code: loans.loan_reference_code
      })
        .from(collections)
        .leftJoin(loans, eq(collections.loan_id, loans.id))
        .where(eq(collections.loan_id, loanId))
        .orderBy(desc(collections.scheduled_date));

      // Map the result to include loan reference code in the collection object
      return result.map(row => ({
        ...row.collection,
        loan: row.loan_reference_code ? { loan_reference_code: row.loan_reference_code } : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching collections for loan id=${loanId}`, 'collection-fetch', error as Error);
      return [];
    }
  }

  async getCollectionsByAgent(
    agentId: number,
    companyId: number,
    status?: string,
    dateRange?: { startDate: string, endDate: string }
  ): Promise<Collection[]> {
    try {
      // First check if the agent exists and belongs to the company
      const [agent] = await db.select()
        .from(agents)
        .where(
          and(
            eq(agents.id, agentId),
            eq(agents.company_id, companyId)
          )
        );

      if (!agent) {
        return [];
      }

      let query = db.select({
        collection: collections,
        loan_reference_code: loans.loan_reference_code
      })
        .from(collections)
        .leftJoin(loans, eq(collections.loan_id, loans.id))
        .where(
          and(
            eq(collections.agent_id, agentId),
            eq(collections.company_id, companyId)
          )
        );

      // Apply status filter if provided
      if (status) {
        query = query.where(eq(collections.status, status));
      }

      // Apply date range filter if provided
      if (dateRange) {
        query = query.where(
          and(
            gte(collections.scheduled_date, dateRange.startDate),
            lte(collections.scheduled_date, dateRange.endDate)
          )
        );
      }

      // Order by scheduled date descending
      query = query.orderBy(desc(collections.scheduled_date));

      const result = await query;

      // Map the result to include loan reference code in the collection object
      return result.map(row => ({
        ...row.collection,
        loan: row.loan_reference_code ? { loan_reference_code: row.loan_reference_code } : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching collections for agent id=${agentId}`, 'collection-fetch', error as Error);
      return [];
    }
  }

  async createCollection(collectionData: InsertCollection): Promise<Collection> {
    try {
      // Validate that the loan and customer exist and belong to the company
      const [loan] = await db.select()
        .from(loans)
        .where(
          and(
            eq(loans.id, collectionData.loan_id),
            eq(loans.company_id, collectionData.company_id)
          )
        );

      if (!loan) {
        throw new Error(`Loan with id=${collectionData.loan_id} not found for company id=${collectionData.company_id}`);
      }

      const [customer] = await db.select()
        .from(customers)
        .where(
          and(
            eq(customers.id, collectionData.customer_id),
            eq(customers.company_id, collectionData.company_id)
          )
        );

      if (!customer) {
        throw new Error(`Customer with id=${collectionData.customer_id} not found for company id=${collectionData.company_id}`);
      }

      // If agent_id is provided, validate that the agent exists and belongs to the company
      if (collectionData.agent_id) {
        const [agent] = await db.select()
          .from(agents)
          .where(
            and(
              eq(agents.id, collectionData.agent_id),
              eq(agents.company_id, collectionData.company_id)
            )
          );

        if (!agent) {
          throw new Error(`Agent with id=${collectionData.agent_id} not found for company id=${collectionData.company_id}`);
        }
      }

      const [collection] = await db.insert(collections)
        .values(collectionData)
        .returning();

      return collection;
    } catch (error) {
      errorLogger.logError(`Error creating collection`, 'collection-create', error as Error);
      throw error;
    }
  }

  async updateCollection(id: number, companyId: number, collectionData: Partial<InsertCollection>): Promise<Collection> {
    try {
      // First check if the collection exists and belongs to the company
      const [existingCollection] = await db.select()
        .from(collections)
        .where(
          and(
            eq(collections.id, id),
            eq(collections.company_id, companyId)
          )
        );

      if (!existingCollection) {
        throw new Error(`Collection with id=${id} not found for company id=${companyId}`);
      }

      // If agent_id is being updated, validate that the agent exists and belongs to the company
      if (collectionData.agent_id) {
        const [agent] = await db.select()
          .from(agents)
          .where(
            and(
              eq(agents.id, collectionData.agent_id),
              eq(agents.company_id, companyId)
            )
          );

        if (!agent) {
          throw new Error(`Agent with id=${collectionData.agent_id} not found for company id=${companyId}`);
        }
      }

      const [updatedCollection] = await db.update(collections)
        .set(collectionData)
        .where(eq(collections.id, id))
        .returning();

      return updatedCollection;
    } catch (error) {
      errorLogger.logError(`Error updating collection id=${id}`, 'collection-update', error as Error);
      throw error;
    }
  }

  async updateCollectionStatus(id: number, status: string): Promise<Collection> {
    try {
      const [updatedCollection] = await db.update(collections)
        .set({ status })
        .where(eq(collections.id, id))
        .returning();

      return updatedCollection;
    } catch (error) {
      errorLogger.logError(`Error updating status for collection id=${id}`, 'collection-status-update', error as Error);
      throw error;
    }
  }
  async deleteCollection(id: number, companyId?: number): Promise<boolean> {
    try {
      // Check if there are any payments associated with this collection
      const collectionPayments = await db.select()
        .from(payments)
        .where(eq(payments.collection_id, id));

      if (collectionPayments.length > 0) {
        throw new Error(`Cannot delete collection with id=${id} because it has associated payments`);
      }

      // If companyId is provided, also check the collection belongs to the company
      let query = db.delete(collections).where(eq(collections.id, id));

      if (companyId !== undefined) {
        // First verify the collection exists and belongs to the company
        const [existingCollection] = await db.select()
          .from(collections)
          .where(
            and(
              eq(collections.id, id),
              eq(collections.company_id, companyId)
            )
          );

        if (!existingCollection) {
          return false; // Collection not found or doesn't belong to this company
        }

        // Add company ID to the delete condition
        query = db.delete(collections)
          .where(
            and(
              eq(collections.id, id),
              eq(collections.company_id, companyId)
            )
          );
      }

      const result = await query;
      return true;
    } catch (error) {
      errorLogger.logError(`Error deleting collection id=${id}`, 'collection-delete', error as Error);
      throw error;
    }
  }

  async markCollectionsAsCompleted(collectionIds: number[]): Promise<void> {
    try {
      if (collectionIds.length === 0) {
        return;
      }

      await db.update(collections)
        .set({ status: 'completed' })
        .where(sql`${collections.id} IN (${collectionIds.join(',')})`);
    } catch (error) {
      errorLogger.logError(`Error marking collections as completed`, 'collection-mark-completed', error as Error);
      throw error;
    }
  }

  /**
   * Check if a collection can be completed based on sequential order restrictions
   * Collections must be completed in EMI number order (1, 2, 3, 4, 5...)
   */
  async canCompleteCollection(collectionId: number): Promise<{ canComplete: boolean; reason?: string; nextCollectionEmi?: number }> {
    try {
      // Get the collection details
      const [collection] = await db.select()
        .from(collections)
        .where(eq(collections.id, collectionId));

      if (!collection) {
        return { canComplete: false, reason: 'Collection not found' };
      }

      if (collection.status === 'completed') {
        return { canComplete: false, reason: 'Collection is already completed' };
      }

      // Get all collections for the same loan, ordered by EMI number
      const loanCollections = await db.select()
        .from(collections)
        .where(eq(collections.loan_id, collection.loan_id))
        .orderBy(collections.emi_number);

      // If EMI number is not set, allow completion (backward compatibility)
      if (!collection.emi_number) {
        return { canComplete: true };
      }

      // Check if there are any incomplete collections with lower EMI numbers
      const incompleteEarlierCollections = loanCollections.filter(c =>
        c.emi_number &&
        c.emi_number < collection.emi_number &&
        c.status !== 'completed'
      );

      if (incompleteEarlierCollections.length > 0) {
        // Find the earliest incomplete collection
        const earliestIncomplete = incompleteEarlierCollections.reduce((earliest, current) =>
          (current.emi_number! < earliest.emi_number!) ? current : earliest
        );

        return {
          canComplete: false,
          reason: `Please complete Collection ${earliestIncomplete.emi_number} before completing this one.`,
          nextCollectionEmi: earliestIncomplete.emi_number!
        };
      }

      return { canComplete: true };
    } catch (error) {
      errorLogger.logError(`Error checking if collection ${collectionId} can be completed`, 'collection-can-complete', error as Error);
      return { canComplete: false, reason: 'Error checking collection completion eligibility' };
    }
  }

  /**
   * Get collections for a loan with completion eligibility information
   */
  async getCollectionsWithCompletionStatus(loanId: number, companyId: number): Promise<(Collection & { canComplete: boolean; completionReason?: string })[]> {
    try {
      // Get all collections for the loan
      const loanCollections = await this.getCollectionsByLoan(loanId, companyId);

      // Add completion status to each collection
      const collectionsWithStatus = await Promise.all(
        loanCollections.map(async (collection) => {
          if (collection.status === 'completed') {
            return { ...collection, canComplete: false, completionReason: 'Already completed' };
          }

          const { canComplete, reason } = await this.canCompleteCollection(collection.id);
          return {
            ...collection,
            canComplete,
            completionReason: reason
          };
        })
      );

      return collectionsWithStatus;
    } catch (error) {
      errorLogger.logError(`Error getting collections with completion status for loan ${loanId}`, 'collection-completion-status', error as Error);
      return [];
    }
  }
  async getPendingCollections(companyId: number): Promise<Collection[]> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of today

      const result = await db.select({
        collection: collections,
        loan_reference_code: loans.loan_reference_code
      })
        .from(collections)
        .leftJoin(loans, eq(collections.loan_id, loans.id))
        .where(
          and(
            eq(collections.company_id, companyId),
            ne(collections.status, 'completed'),
            gte(collections.scheduled_date, today) // Today or in the future
          )
        )
        .orderBy(desc(collections.scheduled_date));

      // Map the result to include loan reference code in the collection object
      return result.map(row => ({
        ...row.collection,
        loan: row.loan_reference_code ? { loan_reference_code: row.loan_reference_code } : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching pending collections for company id=${companyId}`, 'collection-pending-fetch', error as Error);
      return [];
    }
  }

  async getOverdueCollections(companyId: number): Promise<Collection[]> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of today

      const result = await db.select({
        collection: collections,
        loan_reference_code: loans.loan_reference_code
      })
        .from(collections)
        .leftJoin(loans, eq(collections.loan_id, loans.id))
        .where(
          and(
            eq(collections.company_id, companyId),
            ne(collections.status, 'completed'),
            lt(collections.scheduled_date, today) // Before today (past)
          )
        )
        .orderBy(desc(collections.scheduled_date));

      // Map the result to include loan reference code in the collection object
      return result.map(row => ({
        ...row.collection,
        loan: row.loan_reference_code ? { loan_reference_code: row.loan_reference_code } : undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching overdue collections for company id=${companyId}`, 'collection-overdue-fetch', error as Error);
      return [];
    }
  }

  async getPaginatedCollections(
    companyId: number,
    options: {
      page?: number;
      limit?: number;
      status?: string;
      searchTerm?: string;
      agentId?: number;
      dateRange?: { startDate: string, endDate: string };
    }
  ): Promise<{ collections: Collection[], totalCount: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        searchTerm,
        agentId,
        dateRange
      } = options;

      // Calculate offset from page and limit
      const offset = (page - 1) * limit;

      // Start building the base query conditions
      const whereConditions: SQL<unknown>[] = [eq(collections.company_id, companyId)];

      // Add status filter if provided
      if (status && status !== 'all') {
        if (status === 'pending') {
          const today = new Date();
          today.setHours(0, 0, 0, 0); // Start of today
          whereConditions.push(eq(collections.status, 'pending'));
          whereConditions.push(gte(collections.scheduled_date, today));
        } else if (status === 'overdue') {
          const today = new Date();
          today.setHours(0, 0, 0, 0); // Start of today
          whereConditions.push(ne(collections.status, 'completed'));
          whereConditions.push(lt(collections.scheduled_date, today));
        } else {
          whereConditions.push(eq(collections.status, status as any));
        }
      }

      // Add agent filter if provided
      if (agentId) {
        whereConditions.push(eq(collections.agent_id, agentId));
      }

      // Add date range filter if provided
      if (dateRange) {
        const startDate = new Date(dateRange.startDate);
        const endDate = new Date(dateRange.endDate);
        whereConditions.push(gte(collections.scheduled_date, startDate));
        whereConditions.push(lte(collections.scheduled_date, endDate));
      }

      // If search term is provided, we need to join with customers table to search by name/mobile
      if (searchTerm) {
        // First get customer IDs that match the search term
        const matchingCustomers = await db.select({ id: customers.id })
          .from(customers)
          .where(
            and(
              eq(customers.company_id, companyId),
              or(
                like(customers.full_name, `%${searchTerm}%`),
                like(sql`COALESCE(${customers.phone}, '')`, `%${searchTerm}%`),
                like(sql`COALESCE(${customers.email}, '')`, `%${searchTerm}%`)
              )
            )
          );

        const customerIds = matchingCustomers.map(c => c.id);

        // Add search conditions
        const searchConditions: SQL<unknown>[] = [
          // Search by collection ID
          like(sql`CAST(${collections.id} AS TEXT)`, `%${searchTerm}%`),
          // Search by loan ID
          like(sql`CAST(${collections.loan_id} AS TEXT)`, `%${searchTerm}%`),
          // Search by receipt ID if it's not null
          like(sql`COALESCE(${collections.receipt_id}, '')`, `%${searchTerm}%`)
        ];

        // Add customer ID condition if we found matching customers
        if (customerIds.length > 0) {
          const customerIdsList = customerIds.join(',');
          if (customerIdsList) {
            searchConditions.push(
              sql`${collections.customer_id} IN (${customerIdsList})`
            );
          }
        }

        // Add search conditions to the where clause
        if (searchConditions.length > 0) {
          whereConditions.push(or(...searchConditions));
        }
      }

      // Build the final query with loan join
      const query = db.select({
        collection: collections,
        loan_reference_code: loans.loan_reference_code
      })
        .from(collections)
        .leftJoin(loans, eq(collections.loan_id, loans.id))
        .where(and(...whereConditions))
        .orderBy(desc(collections.scheduled_date))
        .limit(limit)
        .offset(offset);

      // Build the count query
      const countQuery = db.select({ count: count() })
        .from(collections)
        .where(and(...whereConditions));

      // Execute both queries
      const [result, countResult] = await Promise.all([
        query,
        countQuery
      ]);

      // Map the result to include loan reference code in the collection object
      const collectionsWithLoanRef = result.map(row => ({
        ...row.collection,
        loan: row.loan_reference_code ? { loan_reference_code: row.loan_reference_code } : undefined
      }));

      return {
        collections: collectionsWithLoanRef,
        totalCount: Number(countResult[0].count)
      };
    } catch (error) {
      errorLogger.logError(`Error fetching paginated collections for company id=${companyId}`, 'collection-paginated-fetch', error as Error);
      return { collections: [], totalCount: 0 };
    }
  }

  /**
   * Get the highest serial number for a given company and prefix (e.g., 'GS-')
   * Returns the highest serial as a number, or 0 if none found.
   * Only considers collections from the specific company.
   */
  async getHighestCollectionSerial(companyId: number, prefix: string): Promise<number> {
    try {
      // Find the max serial for this company and prefix
      // company_collection_string is like 'GS-001', 'GS-002', ...
      const result = await db.select({ maxString: sql`MAX(${collections.company_collection_string})` })
        .from(collections)
        .where(
          and(
            eq(collections.company_id, companyId),
            like(collections.company_collection_string, `${prefix}%`)
          )
        );
      const maxString = result[0]?.maxString as string | undefined;
      if (!maxString) return 0;

      // Extract the serial part (e.g., 'GS-012' => 12)
      const match = maxString.match(/^(.*-)(\d{3})$/);
      if (match) {
        return parseInt(match[2], 10);
      }
      return 0;
    } catch (error) {
      errorLogger.logError(`Error getting highest collection serial for company ${companyId} and prefix ${prefix}`, 'collection-serial', error as Error);
      return 0;
    }
  }
}
