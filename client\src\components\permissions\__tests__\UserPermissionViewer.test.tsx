import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockFetchSuccess, mockFetchError, createMockUserPermission } from '../../../../tests/utils';
import { UserPermissionViewer } from '../UserPermissionViewer';

describe('UserPermissionViewer', () => {
  const mockOnUserSelect = vi.fn();

  const mockUserPermission = createMockUserPermission({
    user_id: 123,
    user_info: {
      id: 123,
      username: 'testuser',
      email: '<EMAIL>',
      role: 'manager',
    },
    direct_roles: [
      {
        role_id: 1,
        role_name: 'Manager',
        role_description: 'Manager role with elevated permissions',
      },
    ],
    effective_permissions: {
      total_count: 5,
      permissions_by_category: {
        loans: [
          { id: 1, code: 'loan_create', name: 'Create Loans', description: 'Create new loans', category: 'loans' },
          { id: 2, code: 'loan_approve', name: 'Approve Loans', description: 'Approve loan applications', category: 'loans' },
        ],
        customers: [
          { id: 3, code: 'customer_view', name: 'View Customers', description: 'View customer data', category: 'customers' },
        ],
      },
      permission_codes: ['loan_create', 'loan_approve', 'customer_view'],
    },
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial Rendering', () => {
    it('should render user search input', () => {
      render(<UserPermissionViewer />);

      expect(screen.getByPlaceholderText(/enter user id/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /load permissions/i })).toBeInTheDocument();
    });

    it('should render with pre-filled userId when provided', () => {
      render(<UserPermissionViewer userId={123} />);

      const input = screen.getByDisplayValue('123');
      expect(input).toBeInTheDocument();
    });
  });

  describe('User Permission Loading', () => {
    it('should load user permissions when userId is provided', async () => {
      mockFetchSuccess(mockUserPermission);

      render(<UserPermissionViewer userId={123} onUserSelect={mockOnUserSelect} />);

      await waitFor(() => {
        expect(screen.getByText('testuser')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('Manager')).toBeInTheDocument();
      });

      expect(mockOnUserSelect).toHaveBeenCalledWith(123);
    });

    it('should load permissions when search button is clicked', async () => {
      const user = userEvent.setup();
      mockFetchSuccess(mockUserPermission);

      render(<UserPermissionViewer onUserSelect={mockOnUserSelect} />);

      const input = screen.getByPlaceholderText(/enter user id/i);
      const button = screen.getByRole('button', { name: /load permissions/i });

      await user.type(input, '123');
      await user.click(button);

      await waitFor(() => {
        expect(screen.getByText('testuser')).toBeInTheDocument();
      });

      expect(mockOnUserSelect).toHaveBeenCalledWith(123);
    });

    it('should show loading state while fetching data', async () => {
      const user = userEvent.setup();

      // Mock delayed response
      global.fetch = vi.fn().mockImplementation(() =>
        new Promise(resolve =>
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve(mockUserPermission),
          }), 100)
        )
      );

      render(<UserPermissionViewer />);

      const input = screen.getByPlaceholderText(/enter user id/i);
      const button = screen.getByRole('button', { name: /load permissions/i });

      await user.type(input, '123');
      await user.click(button);

      // Should show loading state
      expect(button).toBeDisabled();
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    it('should handle user not found error', async () => {
      const user = userEvent.setup();
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 404,
        json: () => Promise.resolve({ error: 'User not found' }),
      });

      render(<UserPermissionViewer />);

      const input = screen.getByPlaceholderText(/enter user id/i);
      const button = screen.getByRole('button', { name: /load permissions/i });

      await user.type(input, '999');
      await user.click(button);

      await waitFor(() => {
        expect(screen.getByText(/user not found/i)).toBeInTheDocument();
      });
    });

    it('should handle general API errors', async () => {
      const user = userEvent.setup();
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      render(<UserPermissionViewer />);

      const input = screen.getByPlaceholderText(/enter user id/i);
      const button = screen.getByRole('button', { name: /load permissions/i });

      await user.type(input, '123');
      await user.click(button);

      await waitFor(() => {
        expect(screen.getByText(/error loading/i)).toBeInTheDocument();
      });
    });
  });

  describe('Permission Display', () => {
    beforeEach(() => {
      mockFetchSuccess(mockUserPermission);
    });

    it('should display user information correctly', async () => {
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText('testuser')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('manager')).toBeInTheDocument();
      });
    });

    it('should display direct roles', async () => {
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText('Manager')).toBeInTheDocument();
        expect(screen.getByText('Manager role with elevated permissions')).toBeInTheDocument();
      });
    });

    it('should display permissions by category', async () => {
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        // Check category headers
        expect(screen.getByText(/loans/i)).toBeInTheDocument();
        expect(screen.getByText(/customers/i)).toBeInTheDocument();

        // Check individual permissions
        expect(screen.getByText('Create Loans')).toBeInTheDocument();
        expect(screen.getByText('Approve Loans')).toBeInTheDocument();
        expect(screen.getByText('View Customers')).toBeInTheDocument();
      });
    });

    it('should display permission count', async () => {
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText('5')).toBeInTheDocument(); // total_count
      });
    });
  });

  describe('Category Expansion', () => {
    beforeEach(() => {
      mockFetchSuccess(mockUserPermission);
    });

    it('should allow expanding and collapsing permission categories', async () => {
      const user = userEvent.setup();
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText('Create Loans')).toBeInTheDocument();
      });

      // Find category header and click to collapse
      const categoryHeaders = screen.getAllByRole('button');
      const loansCategory = categoryHeaders.find(header =>
        header.textContent?.includes('loans')
      );

      if (loansCategory) {
        await user.click(loansCategory);

        // Permissions should be hidden (implementation dependent)
        // This test assumes the component has collapsible categories
      }
    });
  });

  describe('Permission Checking', () => {
    beforeEach(() => {
      mockFetchSuccess(mockUserPermission);
    });

    it('should allow checking specific permissions', async () => {
      const user = userEvent.setup();

      // Mock permission check response
      const mockCheckResult = {
        user_id: 123,
        permission_type: 'loan',
        operation_type: 'create',
        context: { amount: 10000 },
        has_permission: true,
        checked_at: new Date().toISOString(),
      };

      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockUserPermission),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockCheckResult),
        });

      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText('testuser')).toBeInTheDocument();
      });

      // Look for permission check form
      const permissionTypeSelect = screen.getByDisplayValue('');
      const checkButton = screen.getByRole('button', { name: /check permission/i });

      await user.selectOptions(permissionTypeSelect, 'loan');
      await user.click(checkButton);

      await waitFor(() => {
        expect(screen.getByText(/has permission: true/i)).toBeInTheDocument();
      });
    });

    it('should handle permission check with amount context', async () => {
      const user = userEvent.setup();
      mockFetchSuccess(mockUserPermission);

      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText('testuser')).toBeInTheDocument();
      });

      // Fill in amount field if available
      const amountInput = screen.queryByPlaceholderText(/amount/i);
      if (amountInput) {
        await user.type(amountInput, '50000');
      }
    });
  });

  describe('Input Validation', () => {
    it('should validate user ID input', async () => {
      const user = userEvent.setup();
      render(<UserPermissionViewer />);

      const input = screen.getByPlaceholderText(/enter user id/i);
      const button = screen.getByRole('button', { name: /load permissions/i });

      // Try with empty input
      await user.click(button);
      expect(button).toBeDisabled();

      // Try with invalid input
      await user.type(input, 'invalid');
      expect(button).toBeDisabled();

      // Try with valid input
      await user.clear(input);
      await user.type(input, '123');
      expect(button).toBeEnabled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      render(<UserPermissionViewer />);

      expect(screen.getByLabelText(/user id/i)).toBeInTheDocument();
    });

    it('should have proper heading structure', async () => {
      mockFetchSuccess(mockUserPermission);
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
      });
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<UserPermissionViewer />);

      const input = screen.getByPlaceholderText(/enter user id/i);

      await user.tab();
      expect(input).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /load permissions/i })).toHaveFocus();
    });
  });

  describe('Edge Cases', () => {
    it('should handle user with no permissions', async () => {
      const userWithNoPermissions = {
        ...mockUserPermission,
        effective_permissions: {
          total_count: 0,
          permissions_by_category: {},
          permission_codes: [],
        },
      };

      mockFetchSuccess(userWithNoPermissions);
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText(/no permissions/i)).toBeInTheDocument();
      });
    });

    it('should handle user with no direct roles', async () => {
      const userWithNoRoles = {
        ...mockUserPermission,
        direct_roles: [],
      };

      mockFetchSuccess(userWithNoRoles);
      render(<UserPermissionViewer userId={123} />);

      await waitFor(() => {
        expect(screen.getByText(/no direct roles/i)).toBeInTheDocument();
      });
    });
  });
});
