import { sql } from 'drizzle-orm';
import { pgTable, serial, integer, text, timestamp } from 'drizzle-orm/pg-core';

export async function up(db: any) {
  // Create company_settings table
  await db.execute(sql`
    CREATE TABLE IF NOT EXISTS company_settings (
      id SERIAL PRIMARY KEY,
      company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
      date_format TEXT NOT NULL DEFAULT 'dd-MM-yyyy',
      currency_symbol TEXT NOT NULL DEFAULT '₹',
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
      UNIQUE(company_id)
    );
  `);

  // Create index for faster lookups
  await db.execute(sql`
    CREATE INDEX IF NOT EXISTS idx_company_settings_company_id ON company_settings(company_id);
  `);
}

export async function down(db: any) {
  // Drop the company_settings table
  await db.execute(sql`
    DROP TABLE IF EXISTS company_settings;
  `);
}
