#!/usr/bin/env npx tsx

/**
 * Migration Script: Populate Customer Reference Codes
 * 
 * This script populates the customer_reference_code field for existing customers
 * that don't have reference codes assigned.
 * 
 * Usage:
 *   npx tsx scripts/populate-customer-reference-codes.ts
 *   npx tsx scripts/populate-customer-reference-codes.ts --dry-run
 *   npx tsx scripts/populate-customer-reference-codes.ts --company-id=13
 */

import { db } from '../server/db';
import { customers, companyPrefixSettings } from '../shared/schema';
import { eq, and, isNull, or, desc } from 'drizzle-orm';

interface MigrationOptions {
  dryRun: boolean;
  companyId?: number;
  verbose: boolean;
}

async function parseArgs(): Promise<MigrationOptions> {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    companyId: args.find(arg => arg.startsWith('--company-id='))?.split('=')[1] ? 
      parseInt(args.find(arg => arg.startsWith('--company-id='))!.split('=')[1]) : undefined,
    verbose: args.includes('--verbose') || args.includes('-v')
  };
}

async function getCompanyPrefix(companyId: number): Promise<{ prefix: string; startNumber: number }> {
  try {
    // Try to get prefix from company_prefix_settings
    const [prefixSettings] = await db.select()
      .from(companyPrefixSettings)
      .where(eq(companyPrefixSettings.company_id, companyId));

    if (prefixSettings) {
      return {
        prefix: prefixSettings.customer_prefix || 'DC',
        startNumber: prefixSettings.customer_start_number || 1
      };
    }

    // Fallback to default
    return { prefix: 'DC', startNumber: 1 };
  } catch (error) {
    console.warn(`Warning: Could not get prefix settings for company ${companyId}, using default`);
    return { prefix: 'DC', startNumber: 1 };
  }
}

async function getHighestSerial(companyId: number, prefix: string): Promise<number> {
  try {
    const result = await db.select()
      .from(customers)
      .where(
        and(
          eq(customers.company_id, companyId),
          // Only look at customers that already have reference codes with this prefix
          eq(customers.customer_reference_code, `${prefix}%`)
        )
      )
      .orderBy(desc(customers.customer_reference_code));

    let highestSerial = 0;
    for (const customer of result) {
      if (customer.customer_reference_code) {
        const match = customer.customer_reference_code.match(new RegExp(`^${prefix}-(\\d+)$`));
        if (match) {
          const serial = parseInt(match[1], 10);
          if (serial > highestSerial) {
            highestSerial = serial;
          }
        }
      }
    }

    return highestSerial;
  } catch (error) {
    console.warn(`Warning: Could not get highest serial for company ${companyId}, starting from 0`);
    return 0;
  }
}

async function populateCustomerReferenceCodes(options: MigrationOptions): Promise<void> {
  console.log('🚀 Starting Customer Reference Code Population...');
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }

  try {
    // Get customers that need reference codes
    let whereCondition = or(
      isNull(customers.customer_reference_code),
      eq(customers.customer_reference_code, '')
    );

    if (options.companyId) {
      whereCondition = and(
        eq(customers.company_id, options.companyId),
        whereCondition
      );
    }

    const customersToUpdate = await db.select()
      .from(customers)
      .where(whereCondition)
      .orderBy(customers.company_id, customers.id);

    console.log(`📊 Found ${customersToUpdate.length} customers that need reference codes`);

    if (customersToUpdate.length === 0) {
      console.log('✅ All customers already have reference codes!');
      return;
    }

    // Group by company
    const companiesMap = new Map<number, typeof customersToUpdate>();
    for (const customer of customersToUpdate) {
      if (!companiesMap.has(customer.company_id)) {
        companiesMap.set(customer.company_id, []);
      }
      companiesMap.get(customer.company_id)!.push(customer);
    }

    console.log(`🏢 Processing ${companiesMap.size} companies`);

    let totalUpdated = 0;

    for (const [companyId, companyCustomers] of companiesMap) {
      console.log(`\n🏢 Processing Company ID: ${companyId} (${companyCustomers.length} customers)`);
      
      // Get company prefix settings
      const { prefix, startNumber } = await getCompanyPrefix(companyId);
      console.log(`   📝 Using prefix: "${prefix}" with start number: ${startNumber}`);

      // Get highest existing serial for this company and prefix
      const highestSerial = await getHighestSerial(companyId, prefix);
      let nextSerial = Math.max(highestSerial + 1, startNumber);
      
      console.log(`   🔢 Starting from serial: ${nextSerial} (highest existing: ${highestSerial})`);

      // Update each customer
      for (const customer of companyCustomers) {
        const referenceCode = `${prefix}-${nextSerial.toString().padStart(3, '0')}`;
        
        if (options.verbose) {
          console.log(`   👤 Customer ${customer.id} (${customer.full_name}) → ${referenceCode}`);
        }

        if (!options.dryRun) {
          await db.update(customers)
            .set({ 
              customer_reference_code: referenceCode,
              updated_at: new Date()
            })
            .where(eq(customers.id, customer.id));
        }

        nextSerial++;
        totalUpdated++;
      }

      console.log(`   ✅ ${options.dryRun ? 'Would update' : 'Updated'} ${companyCustomers.length} customers for company ${companyId}`);
    }

    console.log(`\n🎉 Migration completed! ${options.dryRun ? 'Would update' : 'Updated'} ${totalUpdated} customers total`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    const options = await parseArgs();
    
    console.log('📋 Migration Options:', {
      dryRun: options.dryRun,
      companyId: options.companyId || 'all',
      verbose: options.verbose
    });

    await populateCustomerReferenceCodes(options);
    
    console.log('\n✅ Migration script completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Migration script failed:', error);
    process.exit(1);
  }
}

// Run the migration
main();
