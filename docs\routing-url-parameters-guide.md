# Routing and URL Parameters Guide

## Quick Reference: Direct Import vs DynamicImport

### 🚨 Critical Decision Matrix

| Page Characteristic | Import Method | Reason |
|---------------------|---------------|---------|
| **Parses URL parameters on load** | ✅ **Direct Import** | Avoids timing conflicts |
| **Complex initialization logic** | ✅ **Direct Import** | Ensures reliable component mounting |
| **Frequently accessed page** | ✅ **Direct Import** | No lazy loading benefit |
| **Simple, self-contained page** | ✅ **DynamicImport** | Bundle size optimization |
| **Rarely accessed page** | ✅ **DynamicImport** | Lazy loading beneficial |
| **No URL parameter dependencies** | ✅ **DynamicImport** | Safe for dynamic loading |

## Common URL Parameter Loading Issue

### Problem Symptoms
- ✅ Manual search/input works correctly
- ❌ Direct URL navigation with parameters fails to auto-load
- ❌ useEffect doesn't receive URL parameters on initial render

### Root Cause
**DynamicImport creates timing conflicts:**
```
Route → DynamicImport → Lazy Load → Component → useEffect → URL parsing
```

### Solution Pattern

#### ❌ Problematic (DynamicImport)
```typescript
// App.tsx - CAUSES TIMING ISSUES
<Route path="/user-management/permissions/user-permissions">
  <AppLayout allowedRoles={['saas_admin', 'owner']}>
    <DynamicImport path="/user-management/permissions/user-permissions" />
  </AppLayout>
</Route>
```

#### ✅ Solution (Direct Import)
```typescript
// App.tsx - RELIABLE TIMING
import UserPermissionsPage from "@/pages/user-management/permissions/user-permissions";

<Route path="/user-management/permissions/user-permissions">
  <AppLayout allowedRoles={['saas_admin', 'owner']}>
    <UserPermissionsPage />
  </AppLayout>
</Route>
```

## Robust URL Parameter Parsing

### Enhanced useEffect Pattern
```typescript
// Component with URL parameters
import { useLocation } from 'wouter';
import { useState, useEffect } from 'react';

export default function ParameterDependentPage() {
  const [location] = useLocation();
  const [paramValue, setParamValue] = useState<number | undefined>();

  useEffect(() => {
    // Handle both wouter location and window.location for compatibility
    let searchParams;
    if (location.includes('?')) {
      searchParams = new URLSearchParams(location.split('?')[1]);
    } else {
      searchParams = new URLSearchParams(window.location.search);
    }
    
    const param = searchParams.get('paramName');
    if (param) {
      const parsed = parseInt(param);
      if (!isNaN(parsed) && parsed > 0) {
        setParamValue(parsed);
        // Trigger data loading here
        loadData(parsed);
      }
    } else {
      setParamValue(undefined);
    }
  }, [location]);

  return (
    // Component JSX
  );
}
```

## Testing Checklist

### URL Parameter Page Testing
- [ ] **Direct URL Navigation**: Test `/page?param=value` directly in browser
- [ ] **Parameter Changes**: Navigate between different parameter values
- [ ] **Missing Parameters**: Test `/page` without parameters
- [ ] **Invalid Parameters**: Test with malformed values (`?param=abc`)
- [ ] **Multiple Parameters**: Test with multiple URL parameters
- [ ] **Browser Back/Forward**: Ensure parameters work with browser navigation

### Debugging Steps
1. **Add Console Logs**: Track component lifecycle and parameter parsing
2. **Test API Directly**: Verify backend endpoints work independently
3. **Check Route Configuration**: Ensure correct import method is used
4. **Verify Component Mounting**: Confirm component mounts before parameter parsing

## Implementation Examples

### Example 1: User Permissions Page
```typescript
// ✅ CORRECT - Direct import for parameter-dependent page
import UserPermissionsPage from "@/pages/user-management/permissions/user-permissions";

<Route path="/user-management/permissions/user-permissions">
  <AppLayout allowedRoles={['saas_admin', 'owner']}>
    <UserPermissionsPage />
  </AppLayout>
</Route>
```

### Example 2: Financial Reports (No Parameters)
```typescript
// ✅ CORRECT - DynamicImport for simple page
<Route path="/financial/reports">
  <AppLayout allowedRoles={['saas_admin', 'owner', 'employee']}>
    <DynamicImport path="/financial/reports/index" />
  </AppLayout>
</Route>
```

## Migration Guide

### Converting from DynamicImport to Direct Import

1. **Add Import Statement**:
```typescript
// Add to App.tsx imports
import ComponentName from "@/pages/path/to/component";
```

2. **Update Route**:
```typescript
// Change from:
<DynamicImport path="/path/to/component" />

// To:
<ComponentName />
```

3. **Test URL Parameters**:
- Navigate directly to URL with parameters
- Verify automatic loading works
- Test edge cases (missing/invalid parameters)

## Prevention Guidelines

### Route Planning
- **Identify parameter dependencies** during route design
- **Document import method choice** in route comments
- **Plan for parameter validation** and error handling

### Code Review Checklist
- [ ] Parameter-dependent pages use direct import
- [ ] Simple pages use DynamicImport for optimization
- [ ] URL parameter parsing is robust and tested
- [ ] Direct URL navigation is verified

## Common Pitfalls

### ❌ Don't Do This
```typescript
// Using DynamicImport for parameter-dependent page
<Route path="/users/:id/details">
  <DynamicImport path="/users/[id]/details" />
</Route>
```

### ✅ Do This Instead
```typescript
// Direct import for parameter-dependent page
import UserDetails from "@/pages/users/[id]/details";

<Route path="/users/:id/details">
  <UserDetails />
</Route>
```

## Related Documentation
- [Troubleshooting Guide](./troubleshooting-guide.md#8-url-parameter-loading-issues-with-dynamicimport) - Complete troubleshooting case
- [Development Best Practices](./development-best-practices.md#route-import-strategy) - Routing guidelines
- [DynamicImport Component](../client/src/components/DynamicImport.tsx) - Implementation details

---

**Last Updated**: Based on User Permissions page URL parameter loading issue resolution
**Status**: ✅ Resolved and documented for future reference
