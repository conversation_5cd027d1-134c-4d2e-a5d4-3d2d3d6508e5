import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useContextData } from '@/lib/useContextData';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface UseReportDataOptions {
  reportType: string;
  startDate?: Date;
  endDate?: Date;
  asOfDate?: Date;
  enabled?: boolean;
  branchId?: number;
}

export function useReportData<T>({
  reportType,
  startDate,
  endDate,
  asOfDate,
  enabled = true,
  branchId,
}: UseReportDataOptions) {
  const { companyId, isAuthenticated } = useContextData();
  const { toast } = useToast();

  return useQuery<T>({
    queryKey: [
      `/api/companies`,
      companyId,
      `reports/${reportType}`,
      startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
      asOfDate ? format(asOfDate, 'yyyy-MM-dd') : undefined,
      branchId,
    ],
    queryFn: async () => {
      if (!companyId) throw new Error('Company ID is required');

      let url = `/api/companies/${companyId}/reports/${reportType}?`;

      if (startDate && endDate) {
        url += `startDate=${format(startDate, 'yyyy-MM-dd')}&endDate=${format(endDate, 'yyyy-MM-dd')}`;
      } else if (asOfDate) {
        url += `asOfDate=${format(asOfDate, 'yyyy-MM-dd')}`;
      }

      if (branchId) {
        url += `&branchId=${branchId}`;
      }

      try {
        const response = await apiRequest('GET', url);

        if (!response.ok) {
          throw new Error(`Failed to fetch ${reportType} report`);
        }

        const data = await response.json();

        // Special handling for accounts report
        if (reportType === 'accounts' && Array.isArray(data)) {
          // Ensure all account objects have the required properties
          return data.map(account => ({
            accountId: account?.accountId || 0,
            accountName: account?.accountName || 'Unknown',
            accountCode: account?.accountCode || 'N/A',
            accountType: account?.accountType || 'Other',
            openingBalance: account?.openingBalance || 0,
            totalDebits: account?.totalDebits || 0,
            totalCredits: account?.totalCredits || 0,
            closingBalance: account?.closingBalance || 0,
          }));
        }

        return data;
      } catch (error) {
        console.error('Error fetching report data:', error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : `Failed to fetch ${reportType} report`,
          variant: "destructive",
        });
        throw error;
      }
    },
    enabled: !!companyId && isAuthenticated && enabled,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}
