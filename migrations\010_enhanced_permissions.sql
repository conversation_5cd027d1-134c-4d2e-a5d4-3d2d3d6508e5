-- Enhanced Permissions Migration
-- This migration adds granular permissions for loan management, customer data protection, and financial operations

-- Add new granular loan management permissions
INSERT INTO permissions (code, name, description, category) VALUES
-- Loan Creation Permissions
('loan_create_basic', 'Create Basic Loans', 'Create loans up to $10,000', 'loan_management'),
('loan_create_advanced', 'Create Advanced Loans', 'Create loans with advanced features up to $50,000', 'loan_management'),
('loan_create_unlimited', 'Create Unlimited Loans', 'No loan amount restrictions', 'loan_management'),

-- Loan Approval Permissions
('loan_approve_tier1', 'Approve Tier 1 Loans', 'Approve loans up to $10,000', 'loan_management'),
('loan_approve_tier2', 'Approve Tier 2 Loans', 'Approve loans up to $50,000', 'loan_management'),
('loan_approve_tier3', 'Approve Tier 3 Loans', 'Approve loans up to $100,000', 'loan_management'),
('loan_approve_unlimited', 'Approve Unlimited Loans', 'No approval limits', 'loan_management'),

-- Loan Modification Permissions
('loan_modify_terms', 'Modify Loan Terms', 'Modify loan terms and conditions', 'loan_management'),
('loan_modify_interest', 'Modify Interest Rates', 'Change interest rates on existing loans', 'loan_management'),
('loan_modify_schedule', 'Modify Payment Schedule', 'Modify payment schedules and due dates', 'loan_management'),
('loan_modify_status', 'Modify Loan Status', 'Change loan status (active, closed, defaulted)', 'loan_management'),

-- Loan Disbursement Permissions
('loan_disburse_basic', 'Disburse Basic Loans', 'Disburse loans up to $10,000', 'loan_management'),
('loan_disburse_advanced', 'Disburse Advanced Loans', 'Disburse loans up to $100,000', 'loan_management'),
('loan_disburse_unlimited', 'Disburse Unlimited Loans', 'No disbursement limits', 'loan_management'),

-- Customer Data Protection Permissions
('customer_view_basic', 'View Basic Customer Info', 'View basic customer information (name, contact)', 'customer_management'),
('customer_view_financial', 'View Financial Documents', 'Access customer financial documents and history', 'customer_management'),
('customer_view_sensitive', 'View Sensitive Data', 'Access SSN, credit scores, and sensitive information', 'customer_management'),
('customer_view_all', 'View All Customer Data', 'Full access to all customer information', 'customer_management'),

-- Customer Data Export Permissions
('customer_export_basic', 'Export Basic Data', 'Export basic customer information', 'customer_management'),
('customer_export_financial', 'Export Financial Data', 'Export customer financial information', 'customer_management'),
('customer_export_sensitive', 'Export Sensitive Data', 'Export sensitive customer data', 'customer_management'),
('customer_export_bulk', 'Bulk Export Customer Data', 'Export large datasets of customer information', 'customer_management'),

-- Customer Communication Permissions
('customer_contact_email', 'Send Customer Emails', 'Send emails to customers', 'customer_management'),
('customer_contact_sms', 'Send SMS Notifications', 'Send SMS notifications to customers', 'customer_management'),
('customer_contact_call', 'Make Customer Calls', 'Make phone calls to customers', 'customer_management'),
('customer_contact_automated', 'Setup Automated Communications', 'Configure automated communication workflows', 'customer_management'),

-- Financial Operations Permissions
('payment_process_manual', 'Process Manual Payments', 'Process manual payment transactions', 'financial_management'),
('payment_process_automated', 'Setup Automated Payments', 'Configure automated payment processing', 'financial_management'),
('payment_refund', 'Issue Refunds', 'Issue refunds to customers', 'financial_management'),
('payment_void', 'Void Transactions', 'Void payment transactions', 'financial_management'),
('payment_adjust', 'Adjust Payments', 'Make adjustments to payment records', 'financial_management'),

-- Financial Reporting Permissions
('report_view_basic', 'View Basic Reports', 'View basic financial and operational reports', 'report_management'),
('report_view_detailed', 'View Detailed Reports', 'Access detailed financial reports and analytics', 'report_management'),
('report_view_executive', 'View Executive Reports', 'Access executive-level reports and dashboards', 'report_management'),
('report_export', 'Export Reports', 'Export reports in various formats', 'report_management'),
('report_create_custom', 'Create Custom Reports', 'Create and configure custom reports', 'report_management'),
('report_schedule', 'Schedule Reports', 'Schedule automated report generation', 'report_management'),

-- Collection Management Permissions
('collection_view_basic', 'View Collection Activities', 'View basic collection information', 'financial_management'),
('collection_manage', 'Manage Collections', 'Manage collection activities and workflows', 'financial_management'),
('collection_escalate', 'Escalate Collections', 'Escalate collection cases to higher levels', 'financial_management'),
('collection_settle', 'Settle Collection Cases', 'Negotiate and settle collection cases', 'financial_management'),

-- System Administration Permissions
('system_backup', 'System Backup', 'Perform system backups and data exports', 'system_settings'),
('system_restore', 'System Restore', 'Restore system from backups', 'system_settings'),
('system_maintenance', 'System Maintenance', 'Perform system maintenance tasks', 'system_settings'),
('system_audit_logs', 'View Audit Logs', 'Access system audit logs and security events', 'system_settings'),

-- Company Settings Permissions
('company_settings_basic', 'Manage Basic Settings', 'Manage basic company settings', 'company_management'),
('company_settings_advanced', 'Manage Advanced Settings', 'Manage advanced company configurations', 'company_management'),
('company_settings_security', 'Manage Security Settings', 'Configure security policies and settings', 'company_management'),
('company_settings_integration', 'Manage Integrations', 'Configure third-party integrations', 'company_management'),

-- Branch and Department Permissions
('branch_view', 'View Branch Information', 'View branch details and statistics', 'company_management'),
('branch_manage', 'Manage Branches', 'Create, edit, and manage branch information', 'company_management'),
('department_view', 'View Department Information', 'View department details and statistics', 'company_management'),
('department_manage', 'Manage Departments', 'Create, edit, and manage department information', 'company_management')

ON CONFLICT (code) DO NOTHING;

-- Update existing system roles with new permissions
-- Owner gets most permissions except unlimited loan operations
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Owner' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'loan_create_advanced', 'loan_approve_tier3', 'loan_modify_terms', 'loan_modify_interest',
  'loan_modify_schedule', 'loan_modify_status', 'loan_disburse_advanced',
  'customer_view_all', 'customer_export_financial', 'customer_contact_email',
  'customer_contact_sms', 'customer_contact_call', 'customer_contact_automated',
  'payment_process_manual', 'payment_process_automated', 'payment_refund', 'payment_adjust',
  'report_view_detailed', 'report_export', 'report_create_custom', 'report_schedule',
  'collection_manage', 'collection_escalate', 'collection_settle',
  'company_settings_advanced', 'branch_manage', 'department_manage'
)
AND (SELECT id FROM custom_roles WHERE name = 'Owner' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Employee gets basic permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Employee' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'loan_create_basic', 'customer_view_basic', 'customer_contact_email',
  'payment_process_manual', 'report_view_basic', 'collection_view_basic',
  'branch_view', 'department_view'
)
AND (SELECT id FROM custom_roles WHERE name = 'Employee' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Agent gets field-specific permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Agent' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'loan_create_basic', 'customer_view_financial', 'customer_contact_email',
  'customer_contact_sms', 'customer_contact_call', 'payment_process_manual',
  'report_view_basic', 'collection_view_basic', 'collection_manage'
)
AND (SELECT id FROM custom_roles WHERE name = 'Agent' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Create some predefined role templates for common use cases
INSERT INTO custom_roles (name, description, is_system, company_id)
SELECT 'Loan Officer', 'Standard loan officer with loan creation and customer management permissions', true, NULL
WHERE NOT EXISTS (SELECT 1 FROM custom_roles WHERE name = 'Loan Officer' AND company_id IS NULL);

INSERT INTO custom_roles (name, description, is_system, company_id)
SELECT 'Collection Agent', 'Specialized role for collection activities', true, NULL
WHERE NOT EXISTS (SELECT 1 FROM custom_roles WHERE name = 'Collection Agent' AND company_id IS NULL);

INSERT INTO custom_roles (name, description, is_system, company_id)
SELECT 'Financial Analyst', 'Role focused on financial reporting and analysis', true, NULL
WHERE NOT EXISTS (SELECT 1 FROM custom_roles WHERE name = 'Financial Analyst' AND company_id IS NULL);

INSERT INTO custom_roles (name, description, is_system, company_id)
SELECT 'Branch Manager', 'Manager role with branch-level permissions', true, NULL
WHERE NOT EXISTS (SELECT 1 FROM custom_roles WHERE name = 'Branch Manager' AND company_id IS NULL);

INSERT INTO custom_roles (name, description, is_system, company_id)
SELECT 'Accountant', 'Role focused on financial operations and reporting', true, NULL
WHERE NOT EXISTS (SELECT 1 FROM custom_roles WHERE name = 'Accountant' AND company_id IS NULL);

-- Assign permissions to Loan Officer template
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Loan Officer' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'loan_create_advanced', 'loan_approve_tier2', 'loan_modify_terms', 'loan_disburse_basic',
  'customer_view_financial', 'customer_contact_email', 'customer_contact_sms', 'customer_contact_call',
  'payment_process_manual', 'report_view_basic', 'collection_view_basic'
)
AND (SELECT id FROM custom_roles WHERE name = 'Loan Officer' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign permissions to Collection Agent template
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Collection Agent' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'customer_view_basic', 'customer_contact_email', 'customer_contact_sms', 'customer_contact_call',
  'payment_process_manual', 'collection_manage', 'collection_escalate', 'report_view_basic'
)
AND (SELECT id FROM custom_roles WHERE name = 'Collection Agent' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign permissions to Financial Analyst template
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Financial Analyst' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'customer_view_financial', 'payment_process_manual', 'report_view_detailed',
  'report_export', 'report_create_custom', 'report_schedule'
)
AND (SELECT id FROM custom_roles WHERE name = 'Financial Analyst' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign permissions to Branch Manager template
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Branch Manager' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'loan_create_advanced', 'loan_approve_tier3', 'loan_modify_terms', 'loan_modify_interest',
  'customer_view_all', 'customer_contact_automated', 'payment_process_automated',
  'report_view_detailed', 'report_export', 'collection_manage', 'collection_escalate',
  'branch_manage', 'department_view'
)
AND (SELECT id FROM custom_roles WHERE name = 'Branch Manager' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign permissions to Accountant template
INSERT INTO role_permissions (role_id, permission_id)
SELECT
  (SELECT id FROM custom_roles WHERE name = 'Accountant' AND is_system = true LIMIT 1),
  p.id
FROM permissions p
WHERE p.code IN (
  'customer_view_financial', 'payment_process_manual', 'payment_process_automated',
  'payment_refund', 'payment_adjust', 'report_view_detailed', 'report_export',
  'report_create_custom', 'system_audit_logs'
)
AND (SELECT id FROM custom_roles WHERE name = 'Accountant' AND is_system = true LIMIT 1) IS NOT NULL
ON CONFLICT (role_id, permission_id) DO NOTHING;
