#!/usr/bin/env node
/**
 * Debug Script: Token Refresh Process
 * Purpose: Debug script to test token refresh process and company switching
 * Usage: node scripts/debug/token-refresh.js
 */

import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'trackfina-secret-key';

// Mock scenario: User 1 has JWT token for company 3, but frontend is somehow requesting data for company 1
console.log('=== DEBUG TOKEN REFRESH SCENARIO ===\n');

// Original token (User 1 with company 3)
const originalTokenPayload = {
  userId: 1,
  role: 'owner',
  companyId: 3
};

const originalToken = jwt.sign(originalTokenPayload, JWT_SECRET, { expiresIn: '30d' });
console.log('1. Original JWT Token (User 1, Company 3):');
console.log('   Payload:', originalTokenPayload);
console.log('   Token:', originalToken.substring(0, 50) + '...\n');

// Decode original token to verify
const decodedOriginal = jwt.verify(originalToken, JWT_SECRET);
console.log('2. Decoded Original Token:');
console.log('   ', decodedOriginal);
console.log('   Company ID from JWT:', decodedOriginal.companyId, '\n');

// Simulate token refresh request (should switch from company 3 to company 1)
const refreshRequestCompanyId = 1;
console.log('3. Token Refresh Request:');
console.log('   Requested company_id:', refreshRequestCompanyId);
console.log('   Current JWT company_id:', decodedOriginal.companyId);

// Check if user has access to company 1 (this would be the validation step)
// In the backend, this would check userCompanies table
console.log('   Access check: Does User 1 have access to Company 1? (This would be DB lookup)\n');

// Generate new token with company 1
const newTokenPayload = {
  userId: decodedOriginal.userId,
  role: decodedOriginal.role,
  companyId: refreshRequestCompanyId
};

const newToken = jwt.sign(newTokenPayload, JWT_SECRET, { expiresIn: '30d' });
console.log('4. New JWT Token (User 1, Company 1):');
console.log('   Payload:', newTokenPayload);
console.log('   Token:', newToken.substring(0, 50) + '...\n');

// Verify new token
const decodedNew = jwt.verify(newToken, JWT_SECRET);
console.log('5. Decoded New Token:');
console.log('   ', decodedNew);
console.log('   Company ID from new JWT:', decodedNew.companyId, '\n');

console.log('=== ANALYSIS ===');
console.log('- Original JWT had company_id:', decodedOriginal.companyId);
console.log('- New JWT has company_id:', decodedNew.companyId);
console.log('- Backend middleware will use:', decodedNew.companyId, 'for access control');
console.log('- Frontend should use user.company_id from localStorage, but JWT controls actual access');
console.log('\nISSUE: If localStorage still has company_id: 3 but JWT has company_id: 1,');
console.log('frontend will try to fetch /api/companies/3/loans but JWT only allows company 1 access!');

console.log('\n=== DEBUGGING STEPS ===');
console.log('1. Check JWT token payload in browser localStorage');
console.log('2. Check user_data in localStorage');
console.log('3. Verify API calls use the correct company ID from JWT');
console.log('4. Ensure frontend updates localStorage when switching companies');

console.log('\n=== SOLUTION ===');
console.log('When switching companies:');
console.log('1. Generate new JWT with new company ID');
console.log('2. Update user_data in localStorage with new company_id');
console.log('3. Clear React Query cache to refetch data for new company');
console.log('4. Redirect to appropriate page for new company context');
