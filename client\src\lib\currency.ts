/**
 * Currency utility functions for the application
 * Converting USD to INR with a fixed exchange rate
 */

// Fixed exchange rate for USD to INR
// This is a static rate - in a production app, you might want to fetch the current rate from an API
const USD_TO_INR_RATE = 84;

/**
 * Convert USD amount to INR
 * @param amount Amount in USD
 * @returns Amount in INR
 */
export function convertUsdToInr(amount: number): number {
  return amount * USD_TO_INR_RATE;
}

/**
 * Format currency as INR with the ₹ symbol and thousands separators
 * @param amount Amount in INR
 * @returns Formatted INR amount as string
 */
export function formatInr(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Convert USD to INR and format as INR string
 * @param amountInUsd Amount in USD
 * @returns Formatted INR amount as string
 */
export function usdToFormattedInr(amountInUsd: number): string {
  const amountInInr = convertUsdToInr(amountInUsd);
  return formatInr(amountInInr);
}