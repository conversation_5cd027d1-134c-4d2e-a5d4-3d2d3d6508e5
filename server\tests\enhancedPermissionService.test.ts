import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedPermissionService } from '../services/enhancedPermissionService';

// Mock the database
vi.mock('../db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
  }
}));

// Mock the schema
vi.mock('@shared/schema', () => ({
  users: { id: 'id', role: 'role' },
  userRoles: { user_id: 'user_id', role_id: 'role_id' },
  rolePermissions: { role_id: 'role_id', permission_id: 'permission_id' },
  permissions: { id: 'id', code: 'code' },
  groupUsers: { user_id: 'user_id', group_id: 'group_id' },
  groupRoles: { group_id: 'group_id', role_id: 'role_id' },
  customRoles: { id: 'id' }
}));

// Mock the permission middleware
vi.mock('../middleware/permission', () => ({
  hasPermission: vi.fn()
}));

// Mock the conditional permission service
vi.mock('../services/conditionalPermissionService', () => ({
  conditionalPermissionService: {
    evaluatePermissionConditions: vi.fn()
  }
}));

describe('EnhancedPermissionService', () => {
  let service: EnhancedPermissionService;
  let mockDb: any;

  beforeEach(() => {
    service = new EnhancedPermissionService();
    mockDb = vi.mocked(require('../db').db);

    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('checkLoanCreationPermission', () => {
    it('should allow unlimited loan creation with unlimited permission', async () => {
      // Mock getUserPermissions to return unlimited permission
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_create_unlimited']);

      const result = await service.checkLoanCreationPermission(1, 1000000);
      expect(result).toBe(true);
    });

    it('should allow basic loan creation up to $10,000', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_create_basic']);

      const result1 = await service.checkLoanCreationPermission(1, 5000);
      expect(result1).toBe(true);

      const result2 = await service.checkLoanCreationPermission(1, 10000);
      expect(result2).toBe(true);

      const result3 = await service.checkLoanCreationPermission(1, 15000);
      expect(result3).toBe(false);
    });

    it('should allow advanced loan creation up to $50,000', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_create_advanced']);

      const result1 = await service.checkLoanCreationPermission(1, 25000);
      expect(result1).toBe(true);

      const result2 = await service.checkLoanCreationPermission(1, 50000);
      expect(result2).toBe(true);

      const result3 = await service.checkLoanCreationPermission(1, 75000);
      expect(result3).toBe(false);
    });

    it('should deny loan creation without proper permissions', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['some_other_permission']);

      const result = await service.checkLoanCreationPermission(1, 5000);
      expect(result).toBe(false);
    });
  });

  describe('checkLoanApprovalPermission', () => {
    it('should allow unlimited loan approval with unlimited permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_approve_unlimited']);

      const result = await service.checkLoanApprovalPermission(1, 1000000);
      expect(result).toBe(true);
    });

    it('should allow tier-based loan approval', async () => {
      // Test tier 1 (up to $10,000)
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_approve_tier1']);
      let result = await service.checkLoanApprovalPermission(1, 8000);
      expect(result).toBe(true);

      result = await service.checkLoanApprovalPermission(1, 15000);
      expect(result).toBe(false);

      // Test tier 2 (up to $50,000)
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_approve_tier2']);
      result = await service.checkLoanApprovalPermission(1, 30000);
      expect(result).toBe(true);

      result = await service.checkLoanApprovalPermission(1, 75000);
      expect(result).toBe(false);

      // Test tier 3 (up to $100,000)
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_approve_tier3']);
      result = await service.checkLoanApprovalPermission(1, 80000);
      expect(result).toBe(true);

      result = await service.checkLoanApprovalPermission(1, 150000);
      expect(result).toBe(false);
    });
  });

  describe('checkCustomerDataAccess', () => {
    it('should allow basic data access with basic permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_view_basic']);

      const result = await service.checkCustomerDataAccess(1, 'basic');
      expect(result).toBe(true);
    });

    it('should allow financial data access with financial permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_view_financial']);

      const basicResult = await service.checkCustomerDataAccess(1, 'basic');
      expect(basicResult).toBe(true);

      const financialResult = await service.checkCustomerDataAccess(1, 'financial');
      expect(financialResult).toBe(true);

      const sensitiveResult = await service.checkCustomerDataAccess(1, 'sensitive');
      expect(sensitiveResult).toBe(false);
    });

    it('should allow all data access with sensitive permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_view_sensitive']);

      const basicResult = await service.checkCustomerDataAccess(1, 'basic');
      expect(basicResult).toBe(true);

      const financialResult = await service.checkCustomerDataAccess(1, 'financial');
      expect(financialResult).toBe(true);

      const sensitiveResult = await service.checkCustomerDataAccess(1, 'sensitive');
      expect(sensitiveResult).toBe(true);
    });

    it('should deny access without proper permissions', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['some_other_permission']);

      const result = await service.checkCustomerDataAccess(1, 'basic');
      expect(result).toBe(false);
    });
  });

  describe('checkCustomerCommunicationPermission', () => {
    it('should allow email communication with email permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_contact_email']);

      const result = await service.checkCustomerCommunicationPermission(1, 'email');
      expect(result).toBe(true);
    });

    it('should allow SMS communication with SMS permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_contact_sms']);

      const result = await service.checkCustomerCommunicationPermission(1, 'sms');
      expect(result).toBe(true);
    });

    it('should deny communication without proper permissions', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['some_other_permission']);

      const emailResult = await service.checkCustomerCommunicationPermission(1, 'email');
      expect(emailResult).toBe(false);

      const smsResult = await service.checkCustomerCommunicationPermission(1, 'sms');
      expect(smsResult).toBe(false);
    });
  });

  describe('checkPaymentOperationPermission', () => {
    it('should allow manual payment processing with manual permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['payment_process_manual']);

      const result = await service.checkPaymentOperationPermission(1, 'manual');
      expect(result).toBe(true);
    });

    it('should allow refunds with refund permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['payment_refund']);

      const result = await service.checkPaymentOperationPermission(1, 'refund');
      expect(result).toBe(true);
    });

    it('should deny payment operations without proper permissions', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['some_other_permission']);

      const result = await service.checkPaymentOperationPermission(1, 'manual');
      expect(result).toBe(false);
    });
  });

  describe('checkReportPermission', () => {
    it('should allow basic reports with basic permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['report_view_basic']);

      const result = await service.checkReportPermission(1, 'basic');
      expect(result).toBe(true);
    });

    it('should allow detailed reports with detailed permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['report_view_detailed']);

      const basicResult = await service.checkReportPermission(1, 'basic');
      expect(basicResult).toBe(true);

      const detailedResult = await service.checkReportPermission(1, 'detailed');
      expect(detailedResult).toBe(true);
    });

    it('should allow report export with export permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['report_export']);

      const result = await service.checkReportPermission(1, 'export');
      expect(result).toBe(true);
    });

    it('should deny reports without proper permissions', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['some_other_permission']);

      const result = await service.checkReportPermission(1, 'basic');
      expect(result).toBe(false);
    });
  });

  describe('checkPermissionWithContext', () => {
    it('should check loan creation permission with context', async () => {
      vi.spyOn(service, 'checkLoanCreationPermission').mockResolvedValue(true);

      const context = { userId: 1, amount: 25000 };
      const result = await service.checkPermissionWithContext(context, 'loan_create');

      expect(result).toBe(true);
      expect(service.checkLoanCreationPermission).toHaveBeenCalledWith(1, 25000);
    });

    it('should check customer data access with context', async () => {
      vi.spyOn(service, 'checkCustomerDataAccess').mockResolvedValue(true);

      const context = { userId: 1 };
      const result = await service.checkPermissionWithContext(context, 'customer_data', 'financial');

      expect(result).toBe(true);
      expect(service.checkCustomerDataAccess).toHaveBeenCalledWith(1, 'financial');
    });

    it('should return false for unknown permission types', async () => {
      const context = { userId: 1 };
      const result = await service.checkPermissionWithContext(context, 'unknown_type' as any);

      expect(result).toBe(false);
    });
  });

  describe('error handling', () => {
    it('should handle errors gracefully in loan creation check', async () => {
      vi.spyOn(service, 'getUserPermissions').mockRejectedValue(new Error('Database error'));

      const result = await service.checkLoanCreationPermission(1, 5000);
      expect(result).toBe(false);
    });

    it('should handle errors gracefully in customer data access check', async () => {
      vi.spyOn(service, 'getUserPermissions').mockRejectedValue(new Error('Database error'));

      const result = await service.checkCustomerDataAccess(1, 'basic');
      expect(result).toBe(false);
    });
  });

  describe('getUserPermissions', () => {
    beforeEach(() => {
      const mockDb = vi.mocked(require('../db').db);
      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockReturnThis();
      mockDb.innerJoin.mockReturnThis();
    });

    it('should return empty array for non-existent user', async () => {
      const mockDb = vi.mocked(require('../db').db);
      mockDb.select.mockResolvedValueOnce([]); // No user found

      const result = await service.getUserPermissions(999);
      expect(result).toEqual([]);
    });

    it('should return all permissions for saas_admin', async () => {
      const mockDb = vi.mocked(require('../db').db);

      // Mock user with saas_admin role
      mockDb.select.mockResolvedValueOnce([{ id: 1, role: 'saas_admin' }]);
      // Mock all permissions
      mockDb.select.mockResolvedValueOnce([
        { code: 'loan_create_unlimited' },
        { code: 'customer_view_sensitive' },
        { code: 'payment_process_manual' }
      ]);

      const result = await service.getUserPermissions(1);
      expect(result).toEqual(['loan_create_unlimited', 'customer_view_sensitive', 'payment_process_manual']);
    });

    it('should return role-based permissions for regular users', async () => {
      const mockDb = vi.mocked(require('../db').db);

      // Mock user with regular role
      mockDb.select.mockResolvedValueOnce([{ id: 1, role: 'loan_officer' }]);
      // Mock role permissions
      mockDb.select.mockResolvedValueOnce([
        { code: 'loan_create_basic' },
        { code: 'customer_view_basic' }
      ]);

      const result = await service.getUserPermissions(1);
      expect(result).toEqual(['loan_create_basic', 'customer_view_basic']);
    });

    it('should handle database errors gracefully', async () => {
      const mockDb = vi.mocked(require('../db').db);
      mockDb.select.mockRejectedValueOnce(new Error('Database connection failed'));

      const result = await service.getUserPermissions(1);
      expect(result).toEqual([]);
    });
  });

  describe('checkPermissionWithConditions', () => {
    beforeEach(() => {
      const { hasPermission } = require('../middleware/permission');
      const { conditionalPermissionService } = require('../services/conditionalPermissionService');

      vi.mocked(hasPermission).mockResolvedValue(true);
      vi.mocked(conditionalPermissionService.evaluatePermissionConditions).mockResolvedValue({ passed: true });
    });

    it('should return false when user lacks base permission', async () => {
      const { hasPermission } = require('../middleware/permission');
      vi.mocked(hasPermission).mockResolvedValue(false);

      const context = {
        userId: 1,
        permissionCode: 'loan_create_basic',
        amount: 5000
      };

      const result = await service.checkPermissionWithConditions(context);
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('User does not have permission');
    });

    it('should return true when user has permission and conditions pass', async () => {
      const context = {
        userId: 1,
        permissionCode: 'loan_create_basic',
        amount: 5000
      };

      const result = await service.checkPermissionWithConditions(context);
      expect(result.allowed).toBe(true);
    });

    it('should return approval required when conditions require approval', async () => {
      const { conditionalPermissionService } = require('../services/conditionalPermissionService');
      vi.mocked(conditionalPermissionService.evaluatePermissionConditions).mockResolvedValue({
        passed: false,
        requiresApproval: true,
        approverRoles: ['manager', 'director'],
        reason: 'Amount exceeds threshold'
      });

      const context = {
        userId: 1,
        permissionCode: 'loan_create_basic',
        amount: 50000
      };

      const result = await service.checkPermissionWithConditions(context);
      expect(result.allowed).toBe(false);
      expect(result.requiresApproval).toBe(true);
      expect(result.approverRoles).toEqual(['manager', 'director']);
    });

    it('should return false when conditions fail without approval', async () => {
      const { conditionalPermissionService } = require('../services/conditionalPermissionService');
      vi.mocked(conditionalPermissionService.evaluatePermissionConditions).mockResolvedValue({
        passed: false,
        reason: 'Access outside allowed hours'
      });

      const context = {
        userId: 1,
        permissionCode: 'loan_create_basic',
        amount: 5000
      };

      const result = await service.checkPermissionWithConditions(context);
      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('Access outside allowed hours');
    });

    it('should handle errors gracefully', async () => {
      const { hasPermission } = require('../middleware/permission');
      vi.mocked(hasPermission).mockRejectedValue(new Error('Permission check failed'));

      const context = {
        userId: 1,
        permissionCode: 'loan_create_basic',
        amount: 5000
      };

      const result = await service.checkPermissionWithConditions(context);
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Error checking permission');
    });
  });

  describe('checkLoanDisbursementPermission', () => {
    it('should allow unlimited loan disbursement with unlimited permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_disburse_unlimited']);

      const result = await service.checkLoanDisbursementPermission(1, 1000000);
      expect(result).toBe(true);
    });

    it('should allow tiered loan disbursement based on amount', async () => {
      // Test tier 1 (up to $5,000)
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_disburse_tier1']);
      let result = await service.checkLoanDisbursementPermission(1, 3000);
      expect(result).toBe(true);

      result = await service.checkLoanDisbursementPermission(1, 8000);
      expect(result).toBe(false);

      // Test tier 2 (up to $25,000)
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['loan_disburse_tier2']);
      result = await service.checkLoanDisbursementPermission(1, 20000);
      expect(result).toBe(true);

      result = await service.checkLoanDisbursementPermission(1, 30000);
      expect(result).toBe(false);
    });

    it('should deny disbursement without proper permissions', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['some_other_permission']);

      const result = await service.checkLoanDisbursementPermission(1, 5000);
      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      vi.spyOn(service, 'getUserPermissions').mockRejectedValue(new Error('Database error'));

      const result = await service.checkLoanDisbursementPermission(1, 5000);
      expect(result).toBe(false);
    });
  });

  describe('checkCustomerExportPermission', () => {
    it('should allow customer export with proper permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_export']);

      const result = await service.checkCustomerExportPermission(1);
      expect(result).toBe(true);
    });

    it('should deny customer export without permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_view_basic']);

      const result = await service.checkCustomerExportPermission(1);
      expect(result).toBe(false);
    });
  });

  describe('checkCustomerCommunicationPermission', () => {
    it('should allow customer communication with proper permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_communication']);

      const result = await service.checkCustomerCommunicationPermission(1);
      expect(result).toBe(true);
    });

    it('should deny customer communication without permission', async () => {
      vi.spyOn(service, 'getUserPermissions').mockResolvedValue(['customer_view_basic']);

      const result = await service.checkCustomerCommunicationPermission(1);
      expect(result).toBe(false);
    });
  });
});
