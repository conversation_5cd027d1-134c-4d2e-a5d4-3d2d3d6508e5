// Script to fix user permissions for group management and other features
// Run this script to assign necessary permissions to users

import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function fixUserPermissions() {
  const client = await pool.connect();

  try {
    console.log('🔧 Starting permission fix...');

    // 1. First, let's see what users exist
    const usersResult = await client.query(`
      SELECT id, email, full_name, role, company_id
      FROM users
      ORDER BY id
    `);

    console.log('\n📋 Current users:');
    usersResult.rows.forEach(user => {
      console.log(`  - ID: ${user.id}, Email: ${user.email}, Role: ${user.role}, Company: ${user.company_id}`);
    });

    // 2. Check what permissions exist
    const permissionsResult = await client.query(`
      SELECT id, code, name, category
      FROM permissions
      WHERE category IN ('group_management', 'user_management', 'role_management')
      ORDER BY category, code
    `);

    console.log('\n🔑 Available permissions:');
    permissionsResult.rows.forEach(perm => {
      console.log(`  - ${perm.code}: ${perm.name} (${perm.category})`);
    });

    // 3. Check what roles exist
    const rolesResult = await client.query(`
      SELECT id, name, description, company_id
      FROM custom_roles
      ORDER BY id
    `);

    console.log('\n👥 Available roles:');
    rolesResult.rows.forEach(role => {
      console.log(`  - ID: ${role.id}, Name: ${role.name}, Company: ${role.company_id}`);
    });

    // 4. Create a default admin role if it doesn't exist
    const adminRoleResult = await client.query(`
      SELECT id FROM custom_roles
      WHERE name = 'Admin' AND company_id = 1
    `);

    let adminRoleId;
    if (adminRoleResult.rows.length === 0) {
      console.log('\n🆕 Creating Admin role...');
      const createRoleResult = await client.query(`
        INSERT INTO custom_roles (name, description, company_id, created_at, updated_at)
        VALUES ('Admin', 'Full administrative access', 1, NOW(), NOW())
        RETURNING id
      `);
      adminRoleId = createRoleResult.rows[0].id;
      console.log(`✅ Created Admin role with ID: ${adminRoleId}`);
    } else {
      adminRoleId = adminRoleResult.rows[0].id;
      console.log(`✅ Found existing Admin role with ID: ${adminRoleId}`);
    }

    // 5. Get essential permissions that admin should have
    const essentialPermissions = [
      'user_view', 'user_create', 'user_edit', 'user_delete',
      'role_view', 'role_create', 'role_edit', 'role_delete',
      'group_view', 'group_create', 'group_edit', 'group_delete',
      'permission_view', 'permission_assign',
      'customer_view', 'customer_create', 'customer_edit',
      'loan_view', 'loan_create', 'loan_edit'
    ];

    console.log('\n🔗 Assigning permissions to Admin role...');

    for (const permCode of essentialPermissions) {
      // Check if permission exists
      const permResult = await client.query(`
        SELECT id FROM permissions WHERE code = $1
      `, [permCode]);

      if (permResult.rows.length > 0) {
        const permId = permResult.rows[0].id;

        // Check if role already has this permission
        const existingResult = await client.query(`
          SELECT id FROM role_permissions
          WHERE role_id = $1 AND permission_id = $2
        `, [adminRoleId, permId]);

        if (existingResult.rows.length === 0) {
          await client.query(`
            INSERT INTO role_permissions (role_id, permission_id, created_at)
            VALUES ($1, $2, NOW())
          `, [adminRoleId, permId]);
          console.log(`  ✅ Added ${permCode} permission`);
        } else {
          console.log(`  ⏭️  ${permCode} already assigned`);
        }
      } else {
        console.log(`  ⚠️  Permission ${permCode} not found in database`);
      }
    }

    // 6. Assign admin role to the first user (assuming it's the main user)
    const firstUser = usersResult.rows[0];
    if (firstUser) {
      console.log(`\n👤 Assigning Admin role to user: ${firstUser.email}`);

      // Check if user already has this role
      const existingUserRoleResult = await client.query(`
        SELECT id FROM user_roles
        WHERE user_id = $1 AND role_id = $2
      `, [firstUser.id, adminRoleId]);

      if (existingUserRoleResult.rows.length === 0) {
        await client.query(`
          INSERT INTO user_roles (user_id, role_id, created_at)
          VALUES ($1, $2, NOW())
        `, [firstUser.id, adminRoleId]);
        console.log(`✅ Assigned Admin role to ${firstUser.email}`);
      } else {
        console.log(`⏭️  ${firstUser.email} already has Admin role`);
      }
    }

    // 7. Show final user permissions
    console.log('\n📊 Final user permissions:');
    const userPermissionsResult = await client.query(`
      SELECT DISTINCT u.email, p.code, p.name
      FROM users u
      JOIN user_roles ur ON u.id = ur.user_id
      JOIN role_permissions rp ON ur.role_id = rp.role_id
      JOIN permissions p ON rp.permission_id = p.id
      WHERE u.id = $1
      ORDER BY p.code
    `, [firstUser.id]);

    userPermissionsResult.rows.forEach(row => {
      console.log(`  - ${row.code}: ${row.name}`);
    });

    console.log('\n🎉 Permission fix completed successfully!');
    console.log('\n💡 You should now be able to access group management and other features.');
    console.log('   Please refresh your browser and try again.');

  } catch (error) {
    console.error('❌ Error fixing permissions:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
fixUserPermissions()
  .then(() => {
    console.log('\n✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
