#!/usr/bin/env npx tsx

/**
 * Migration Script: Populate Loan Reference Codes
 * 
 * This script populates the loan_reference_code field for existing loans
 * that don't have reference codes assigned.
 * 
 * Usage:
 *   npx tsx scripts/populate-loan-reference-codes.ts
 *   npx tsx scripts/populate-loan-reference-codes.ts --dry-run
 *   npx tsx scripts/populate-loan-reference-codes.ts --company-id=13
 */

import { db } from '../server/db';
import { loans, companyPrefixSettings } from '../shared/schema';
import { eq, and, isNull, or, desc } from 'drizzle-orm';

interface MigrationOptions {
  dryRun: boolean;
  companyId?: number;
  verbose: boolean;
}

async function parseArgs(): Promise<MigrationOptions> {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    companyId: args.find(arg => arg.startsWith('--company-id='))?.split('=')[1] ? 
      parseInt(args.find(arg => arg.startsWith('--company-id='))!.split('=')[1]) : undefined,
    verbose: args.includes('--verbose') || args.includes('-v')
  };
}

async function getCompanyPrefix(companyId: number): Promise<{ prefix: string; startNumber: number }> {
  try {
    // Try to get prefix from company_prefix_settings
    const [prefixSettings] = await db.select()
      .from(companyPrefixSettings)
      .where(eq(companyPrefixSettings.company_id, companyId));

    if (prefixSettings) {
      return {
        prefix: prefixSettings.loan_prefix || 'LN',
        startNumber: prefixSettings.loan_start_number || 1
      };
    }

    // Fallback to default
    return { prefix: 'LN', startNumber: 1 };
  } catch (error) {
    console.warn(`Warning: Could not get prefix settings for company ${companyId}, using default`);
    return { prefix: 'LN', startNumber: 1 };
  }
}

async function getHighestSerial(companyId: number, prefix: string): Promise<number> {
  try {
    const result = await db.select()
      .from(loans)
      .where(
        and(
          eq(loans.company_id, companyId),
          // Only look at loans that already have reference codes with this prefix
          eq(loans.loan_reference_code, `${prefix}%`)
        )
      )
      .orderBy(desc(loans.loan_reference_code));

    let highestSerial = 0;
    for (const loan of result) {
      if (loan.loan_reference_code) {
        const match = loan.loan_reference_code.match(new RegExp(`^${prefix}-(\\d+)$`));
        if (match) {
          const serial = parseInt(match[1], 10);
          if (serial > highestSerial) {
            highestSerial = serial;
          }
        }
      }
    }

    return highestSerial;
  } catch (error) {
    console.warn(`Warning: Could not get highest serial for company ${companyId}, starting from 0`);
    return 0;
  }
}

async function populateLoanReferenceCodes(options: MigrationOptions): Promise<void> {
  console.log('🚀 Starting Loan Reference Code Population...');
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }

  try {
    // Get loans that need reference codes
    let whereCondition = or(
      isNull(loans.loan_reference_code),
      eq(loans.loan_reference_code, '')
    );

    if (options.companyId) {
      whereCondition = and(
        eq(loans.company_id, options.companyId),
        whereCondition
      );
    }

    const loansToUpdate = await db.select()
      .from(loans)
      .where(whereCondition)
      .orderBy(loans.company_id, loans.id);

    console.log(`📊 Found ${loansToUpdate.length} loans that need reference codes`);

    if (loansToUpdate.length === 0) {
      console.log('✅ All loans already have reference codes!');
      return;
    }

    // Group by company
    const companiesMap = new Map<number, typeof loansToUpdate>();
    for (const loan of loansToUpdate) {
      if (!companiesMap.has(loan.company_id)) {
        companiesMap.set(loan.company_id, []);
      }
      companiesMap.get(loan.company_id)!.push(loan);
    }

    console.log(`🏢 Processing ${companiesMap.size} companies`);

    let totalUpdated = 0;

    for (const [companyId, companyLoans] of companiesMap) {
      console.log(`\n🏢 Processing Company ID: ${companyId} (${companyLoans.length} loans)`);
      
      // Get company prefix settings
      const { prefix, startNumber } = await getCompanyPrefix(companyId);
      console.log(`   📝 Using prefix: "${prefix}" with start number: ${startNumber}`);

      // Get highest existing serial for this company and prefix
      const highestSerial = await getHighestSerial(companyId, prefix);
      let nextSerial = Math.max(highestSerial + 1, startNumber);
      
      console.log(`   🔢 Starting from serial: ${nextSerial} (highest existing: ${highestSerial})`);

      // Update each loan
      for (const loan of companyLoans) {
        const referenceCode = `${prefix}-${nextSerial.toString().padStart(3, '0')}`;
        
        if (options.verbose) {
          console.log(`   💰 Loan ${loan.id} → ${referenceCode}`);
        }

        if (!options.dryRun) {
          await db.update(loans)
            .set({ 
              loan_reference_code: referenceCode,
              updated_at: new Date()
            })
            .where(eq(loans.id, loan.id));
        }

        nextSerial++;
        totalUpdated++;
      }

      console.log(`   ✅ ${options.dryRun ? 'Would update' : 'Updated'} ${companyLoans.length} loans for company ${companyId}`);
    }

    console.log(`\n🎉 Migration completed! ${options.dryRun ? 'Would update' : 'Updated'} ${totalUpdated} loans total`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    const options = await parseArgs();
    
    console.log('📋 Migration Options:', {
      dryRun: options.dryRun,
      companyId: options.companyId || 'all',
      verbose: options.verbose
    });

    await populateLoanReferenceCodes(options);
    
    console.log('\n✅ Migration script completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Migration script failed:', error);
    process.exit(1);
  }
}

// Run the migration
main();
