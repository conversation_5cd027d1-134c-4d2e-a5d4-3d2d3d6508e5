# Troubleshooting Documentation

This directory contains comprehensive troubleshooting guides for the FinancialTracker application.

## 📋 Available Guides

### 🔍 [Systematic Codebase Analysis](./systematic-codebase-analysis.md)
**Comprehensive Application Health Check**

Complete systematic analysis of all modules, routes, and components. Identifies route conflicts, caching issues, compilation problems, and missing features across the entire codebase.

**Coverage**:
- ✅ All 38 route files analyzed
- ✅ Storage layer duplication detection
- ✅ Frontend state management review
- ✅ Build warning analysis
- ✅ CRUD operation coverage verification
- ✅ Authentication flow validation

**When to Use**:
- Periodic health checks of the entire application
- Before major releases or deployments
- When multiple modules show issues simultaneously
- For comprehensive code quality assessment

### 🔧 [Route Conflicts and Caching Issues](./route-conflicts-and-caching-issues.md)
**Critical Issue Resolution Guide**

Comprehensive methodology for diagnosing and resolving:
- Route conflicts causing data not to appear
- Frontend caching issues after logout/login cycles
- CRUD operations failing silently
- Data persistence problems across modules

**When to Use**: 
- Data shows "No items found" despite successful creation
- API endpoints returning empty arrays unexpectedly
- Data disappears after logout/login
- 404 errors on seemingly correct endpoints

**Modules Covered**:
- Branch Management ✅ (Resolved)
- Customer Management
- Loan Management
- User Management
- Financial Management
- Any CRUD-based module

## 🚀 Quick Start

### Immediate Diagnosis Commands

```bash
# Check for route conflicts
grep -r "api/your-endpoint" server/routes/

# Test API endpoints directly
curl -X GET "http://localhost:8080/api/test-endpoint"

# Check compilation status
npm run build

# Monitor route registration
npm start | grep "routes registered"
```

### Browser Console Quick Checks

```javascript
// Check store states
console.log('Store state:', useYourStore.getState());

// Check React Query cache
console.log('Query cache:', queryClient.getQueryCache().getAll());

// Check authentication
console.log('Auth token:', localStorage.getItem('auth_token'));
```

## 📚 Related Documentation

- [Main Documentation Index](../main.md)
- [Development Best Practices](../development-best-practices.md)
- [API 404 Fixes Summary](../api-404-fixes-summary.md)
- [Route Audit Summary](../route-audit-and-prefix-settings-summary.md)

## 🆘 Emergency Troubleshooting

### For Specific Issues
If you're experiencing critical data visibility issues:

1. **Start Here**: [Route Conflicts and Caching Issues Guide](./route-conflicts-and-caching-issues.md)
2. **Follow the systematic debugging methodology**
3. **Use the provided test scripts and commands**
4. **Check the prevention guidelines to avoid future issues**

### For Comprehensive Assessment
If multiple modules are showing problems or you need a complete health check:

1. **Run**: `node scripts/test-systematic-analysis.js`
2. **Review**: [Systematic Codebase Analysis Report](./systematic-codebase-analysis.md)
3. **Follow**: The prioritized action plan in the analysis
4. **Verify**: All fixes using the provided test scripts

## 📞 Support

For additional support or to report new troubleshooting scenarios:
- Create detailed issue reports with reproduction steps
- Include server logs and browser console output
- Reference this troubleshooting documentation in your reports

---

**Last Updated**: January 2025  
**Maintained By**: Development Team  
**Coverage**: All FinancialTracker modules
