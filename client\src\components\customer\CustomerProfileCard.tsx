import { useState } from "react";
import { useLocation } from "wouter";
import {
  Card,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { getInitials } from "@/lib/utils";
import { useAuth } from "@/lib/auth";
import {
  CreditCard,
  FileText,
  Mail,
  MapPin,
  Phone,
  User,
  ChevronDown,
  ChevronUp,
  FileWarning,
} from "lucide-react";

type CustomerProps = {
  id: number;
  full_name: string;
  email?: string;
  phone?: string;
  address?: string;
  profile_image?: string;
  credit_score?: number;
  kyc_verified?: boolean;
  active?: boolean;
  customer_reference_code?: string;
  loansCount?: number;
  activeLoansCount?: number;
  overdueLoansCount?: number;
};

export function CustomerProfileCard({
  id,
  full_name,
  email,
  phone,
  address,
  profile_image,
  credit_score,
  kyc_verified = false,
  active = true,
  customer_reference_code,
  loansCount = 0,
  activeLoansCount = 0,
  overdueLoansCount = 0
}: CustomerProps) {
  const [expanded, setExpanded] = useState(false);

  const getCreditScoreColor = () => {
    if (!credit_score) return "secondary";
    if (credit_score > 700) return "default"; // Using default with custom class
    if (credit_score > 500) return "secondary";
    return "destructive";
  };

  const getCreditScoreClass = () => {
    if (!credit_score) return "";
    if (credit_score > 700) return "bg-green-500 hover:bg-green-400";
    if (credit_score > 500) return "bg-yellow-500 hover:bg-yellow-400";
    return "";
  };

  const [, setLocation] = useLocation();

  const handleViewLoans = () => {
    setLocation(`/customers/${id}/loans`);
  };

  // Get the current user's company ID for navigation
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const companyId = user?.company_id;

  const handleViewProfile = () => {
    // Navigate to the customer profile with the current company context
    console.log(`Navigating to customer ${id} profile with company context ${companyId}`);
    setLocation(`/customers/${id}`);
  };

  return (
    <Card className={`h-full flex flex-col ${!active ? 'opacity-70' : ''}`}>
      <CardContent className="p-3 flex-grow">
        <div className="flex flex-col gap-2">
          {/* Header section with name */}
          <div className="flex items-center gap-2">
            <Avatar className="h-10 w-10 flex-shrink-0">
              <AvatarFallback className="bg-primary text-primary-foreground text-sm">
                {getInitials(full_name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-medium text-base w-full max-w-[150px]">{full_name}</h3>
              <div className="text-xs text-muted-foreground">
                {customer_reference_code ?
                  `ID: ${customer_reference_code}` :
                  `ID: CUST-${id}`
                }
              </div>
            </div>
          </div>

          {/* Status badges and credit score */}
          <div className="flex items-center flex-wrap gap-2">
            {active ? (
              <Badge variant="default" className="bg-green-500 hover:bg-green-400 text-xs">Active</Badge>
            ) : (
              <Badge variant="secondary" className="text-xs">Inactive</Badge>
            )}
            {kyc_verified && (
              <Badge variant="outline" className="bg-green-50 text-xs">KYC</Badge>
            )}
            {credit_score && (
              <Badge variant={getCreditScoreColor()} className={`${getCreditScoreClass()} text-xs`}>{credit_score}</Badge>
            )}
          </div>

          {/* Contact info */}
          <div className="space-y-1 text-xs">
            {email && (
              <div className="flex items-center gap-1">
                <Mail className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                <span className="truncate">{email}</span>
              </div>
            )}
            {phone && (
              <div className="flex items-center gap-1">
                <Phone className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                <span>{phone}</span>
              </div>
            )}
            {address && expanded && (
              <div className="flex items-start gap-1">
                <MapPin className="h-3 w-3 text-muted-foreground mt-0.5 flex-shrink-0" />
                <span className="line-clamp-2">{address}</span>
              </div>
            )}
          </div>

          {/* Loan information */}
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3 text-muted-foreground flex-shrink-0" />
              <span>Loans: {loansCount}</span>
            </div>
            {(activeLoansCount > 0 || overdueLoansCount > 0) && (
              <div className="flex items-center gap-2">
                {activeLoansCount > 0 && (
                  <span className="text-green-600">{activeLoansCount} Active</span>
                )}
                {overdueLoansCount > 0 && (
                  <span className="text-red-600">{overdueLoansCount} Overdue</span>
                )}
              </div>
            )}
          </div>

          {/* Expand/Collapse button */}
          <div className="flex justify-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpanded(!expanded)}
              className="text-xs h-6 px-2 -mx-2 -my-1"
            >
              {expanded ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between gap-2 py-2 px-3 bg-muted/30">
        <Button
          variant="outline"
          size="sm"
          className="text-xs h-8 flex-1"
          onClick={handleViewProfile}
        >
          <User className="h-3 w-3 mr-1" />
          Profile
        </Button>
        <Button
          variant="default"
          size="sm"
          className="text-xs h-8 flex-1"
          onClick={handleViewLoans}
        >
          <FileText className="h-3 w-3 mr-1" />
          Loans
        </Button>
      </CardFooter>
    </Card>
  );
}