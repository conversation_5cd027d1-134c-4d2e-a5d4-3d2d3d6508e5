import React, { useState, useEffect } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/lib/auth";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CustomerCombobox } from "@/components/ui/customer-combobox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Spinner } from "@/components/ui/spinner";
import { LoadingOverlay } from "@/components/ui/loading-overlay";
import { addDays, addWeeks, format } from "date-fns";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";

// Define schema for the quick loan form
const quickLoanFormSchema = z.object({
  customer_id: z.number({
    required_error: "Customer is required",
  }),
  loan_amount: z.number({
    required_error: "Loan amount is required",
  }).min(1, {
    message: "Amount must be greater than 0",
  }),
  interest_rate: z.number({
    required_error: "Interest rate is required",
  }).min(0, {
    message: "Interest rate must be non-negative",
  }),
  loan_type: z.enum(["personal", "business", "education", "housing", "vehicle", "agriculture", "microfinance", "other"], {
    required_error: "Loan type is required",
  }),
  frequency: z.enum(["daily", "weekly", "monthly"], {
    required_error: "Payment frequency is required",
  }),
  term_units: z.number({
    required_error: "Number of payments is required",
  }).min(1, {
    message: "Number of payments must be at least 1",
  }),
  deduct_interest: z.boolean().default(true),
  start_date: z.string({
    required_error: "Start date is required",
  }),
  notes: z.string().optional().nullable(),
});

type QuickLoanFormValues = z.infer<typeof quickLoanFormSchema>;

type Customer = {
  id: number;
  full_name: string;
  company_id: number;
  customer_reference_code?: string;
  phone?: string;
  email?: string;
  // Other customer fields are optional for this component
};

interface QuickLoanFormProps {
  companyId: number;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  presetFrequency?: 'daily' | 'weekly' | 'monthly';
}

export function QuickLoanForm({
  companyId,
  onSuccess,
  onCancel,
  presetFrequency = 'weekly',
}: QuickLoanFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();

  // Add loading state for better user feedback
  const [loadingState, setLoadingState] = useState({
    isLoading: false,
    message: '',
    progress: 0,
  });

  // Log the company ID received from props and the user's current company ID
  console.log('QuickLoanForm - Company ID check:', {
    propsCompanyId: companyId,
    userCompanyId: user?.company_id,
    match: companyId === user?.company_id ? 'Yes' : 'No'
  });

  // Always use the most up-to-date company ID (from user if available)
  const effectiveCompanyId = user?.company_id || companyId;

  // Fetch customers for dropdown
  const { data: customers = [] as Customer[], isLoading: isLoadingCustomers } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${effectiveCompanyId}/customers`],
    queryFn: async () => {
      if (!effectiveCompanyId) return [];

      console.log(`Fetching customers for company ${effectiveCompanyId}`);

      // Force a fresh request to the server by adding a cache-busting timestamp
      const timestamp = new Date().getTime();
      const response = await apiRequest('GET', `/api/companies/${effectiveCompanyId}/customers?_t=${timestamp}`);
      const data = await response.json();

      // Log the customers to verify they belong to the correct company
      console.log(`Received ${data.length} customers for company ${effectiveCompanyId}`);

      // Server should have already filtered customers, but let's double-check
      // to ensure we only show customers from the current company
      const filteredCustomers = data.filter((customer: Customer) => customer.company_id === effectiveCompanyId);

      if (filteredCustomers.length !== data.length) {
        console.warn(`Client-side filtering: Removed ${data.length - filteredCustomers.length} customers that don't belong to company ${effectiveCompanyId}`);
      }

      return filteredCustomers;
    },
    enabled: !!effectiveCompanyId,
    // Override the global staleTime setting to ensure we always get fresh data
    staleTime: 0,
    // Don't cache this data for long
    cacheTime: 1000,
    // Always refetch when the component mounts
    refetchOnMount: true,
  });

  // Calculate today's date for default start date
  const today = new Date();
  const defaultStartDate = today.toISOString().split('T')[0];

  // Set up form with default values
  const form = useForm<QuickLoanFormValues>({
    resolver: zodResolver(quickLoanFormSchema),
    defaultValues: {
      customer_id: undefined,
      loan_amount: 10000,
      interest_rate: 10,
      loan_type: "personal",
      frequency: presetFrequency,  // Use the preset frequency
      term_units: presetFrequency === "daily" ? 30 : presetFrequency === "weekly" ? 10 : 3,  // Default based on frequency
      deduct_interest: true,
      start_date: defaultStartDate,
      notes: "",
    },
  });

  // Set the frequency based on the preset when the component loads
  useEffect(() => {
    if (presetFrequency) {
      form.setValue("frequency", presetFrequency);

      // Set appropriate default term units based on frequency
      if (presetFrequency === "daily") {
        form.setValue("term_units", 30);
      } else if (presetFrequency === "weekly") {
        form.setValue("term_units", 10);
      } else if (presetFrequency === "monthly") {
        form.setValue("term_units", 3);
      }
    }
  }, [presetFrequency, form]);

  // Invalidate and refetch customer data when company ID changes
  useEffect(() => {
    if (effectiveCompanyId) {
      console.log(`Company ID changed to ${effectiveCompanyId}, invalidating customer cache`);
      // Invalidate the customer query cache for all companies
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      // Force refetch the customers for the current company
      queryClient.refetchQueries({ queryKey: [`/api/companies/${effectiveCompanyId}/customers`] });
    }
  }, [effectiveCompanyId, queryClient]);

  // Watch form values for calculations
  const loanAmount = form.watch("loan_amount");
  const interestRate = form.watch("interest_rate");
  const deductInterest = form.watch("deduct_interest");
  const frequency = form.watch("frequency");
  const termUnits = form.watch("term_units");
  const startDate = form.watch("start_date");

  // Calculate derived values
  const [summaryValues, setSummaryValues] = useState({
    totalInterestAmount: 0,
    actualDisbursalAmount: 0,
    totalRepayableAmount: 0,
    installmentAmount: 0,
    endDate: "",
    termUnits: 0, // Store the actual term units without conversion
  });

  // Update calculated values when form values change
  useEffect(() => {
    // Calculate interest amount
    const interestAmount = loanAmount * (interestRate / 100);

    // Calculate actual amount to disburse
    const actualDisbursalAmount = deductInterest
      ? loanAmount - interestAmount
      : loanAmount;

    // Calculate total repayable amount
    const totalRepayableAmount = deductInterest
      ? loanAmount
      : loanAmount + interestAmount;

    // Calculate installment amount
    const installmentAmount = totalRepayableAmount / termUnits;

    // Calculate end date based on frequency and term
    let endDateStr = "";

    // Only calculate end date if we have a valid start date and term units
    if (startDate && !isNaN(termUnits)) {
      let endDate = new Date(startDate);

      // Check if the date is valid before proceeding
      if (!isNaN(endDate.getTime())) {
        if (frequency === "daily") {
          endDate = addDays(endDate, termUnits - 1);
        } else if (frequency === "weekly") {
          endDate = addWeeks(endDate, termUnits - 1);
        } else if (frequency === "monthly") {
          // Add months manually since date-fns doesn't have an addMonths function
          endDate.setMonth(endDate.getMonth() + termUnits - 1);
        }

        // Only convert to ISO string if the date is valid
        if (!isNaN(endDate.getTime())) {
          endDateStr = endDate.toISOString().split('T')[0];
        }
      }
    }

    // Keep the original term units, don't convert to months
    // This ensures we store exactly what the user selected (10 weeks = 10 weeks, not 3 months)
    setSummaryValues({
      totalInterestAmount: interestAmount,
      actualDisbursalAmount,
      totalRepayableAmount,
      installmentAmount,
      endDate: endDateStr,
      termUnits: termUnits, // Store the actual term units directly
    });
  }, [loanAmount, interestRate, deductInterest, frequency, termUnits, startDate]);

  // Create loan mutation
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: QuickLoanFormValues) => {
      // Set loading state at the beginning
      setLoadingState({
        isLoading: true,
        message: 'Creating loan...',
        progress: 10,
      });

      // Transform quick loan data to the format expected by the API
      const loanData = {
        customer_id: data.customer_id,
        company_id: effectiveCompanyId,
        amount: data.loan_amount.toString(),
        interest_rate: data.interest_rate.toString(),
        interest_type: "flat", // Default to flat for these simple loans
        loan_type: data.loan_type,
        term: data.term_units, // Keep the original term units - don't convert to months
        terms_frequency: data.frequency, // Set terms_frequency to match payment_frequency
        start_date: data.start_date,
        end_date: summaryValues.endDate,
        payment_frequency: data.frequency,
        disbursed_amount: summaryValues.actualDisbursalAmount.toString(),
        total_repayable: summaryValues.totalRepayableAmount.toString(),
        installment_amount: summaryValues.installmentAmount.toString(),
        notes: data.notes || "",
        status: "active", // Default to active status
      };

      // Update loading state before API call
      setLoadingState(prev => ({
        ...prev,
        message: 'Saving loan details...',
        progress: 30,
      }));

      const response = await apiRequest('POST', '/api/loans', loanData);

      // Update loading state for payment schedules generation
      setLoadingState(prev => ({
        ...prev,
        message: 'Generating payment schedules...',
        progress: 70,
      }));

      return response;
    },
    onSuccess: (data) => {
      // Update loading before final steps
      setLoadingState(prev => ({
        ...prev,
        message: 'Finalizing...',
        progress: 90,
      }));

      queryClient.invalidateQueries({ queryKey: [`/api/companies/${effectiveCompanyId}/loans`] });

      // Clear loading state
      setTimeout(() => {
        setLoadingState({
          isLoading: false,
          message: '',
          progress: 0,
        });

        toast({
          title: "Loan created",
          description: "New loan has been created successfully",
        });

        if (onSuccess) onSuccess(data);
      }, 500); // Small delay to ensure the user sees the "Finalizing" message
    },
    onError: (error: any) => {
      // Clear loading state on error
      setLoadingState({
        isLoading: false,
        message: '',
        progress: 0,
      });

      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = (values: QuickLoanFormValues) => {
    mutate(values);
  };

  // Predefined loan options
  const handleQuickPreset = (preset: string) => {
    switch(preset) {
      case "daily-100":
        form.setValue("frequency", "daily");
        form.setValue("term_units", 100);
        break;
      case "daily-30":
        form.setValue("frequency", "daily");
        form.setValue("term_units", 30);
        break;
      case "weekly-10":
        form.setValue("frequency", "weekly");
        form.setValue("term_units", 10);
        break;
      case "weekly-20":
        form.setValue("frequency", "weekly");
        form.setValue("term_units", 20);
        break;
      case "monthly-3":
        form.setValue("frequency", "monthly");
        form.setValue("term_units", 3);
        break;
      case "monthly-6":
        form.setValue("frequency", "monthly");
        form.setValue("term_units", 6);
        break;
      case "monthly-12":
        form.setValue("frequency", "monthly");
        form.setValue("term_units", 12);
        break;
      default:
        break;
    }
  };

  if (isLoadingCustomers) {
    return (
      <div className="flex justify-center py-6">
        <Spinner size="lg" />
        <span className="ml-2">Loading customers...</span>
      </div>
    );
  }

  return (
    <>
      {/* Add the loading overlay */}
      <LoadingOverlay
        isLoading={loadingState.isLoading}
        message={loadingState.message}
        progress={loadingState.progress}
        showProgress={true}
      />

      <Card className="w-full">
        <CardHeader>
          <CardTitle>Create Quick Loan</CardTitle>
          <CardDescription>
            Simplified loan creation for daily and weekly finance
          </CardDescription>
        </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Selection */}
              <FormField
                control={form.control}
                name="customer_id"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex justify-between items-center">
                      <FormLabel className="font-medium">Customer</FormLabel>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                        onClick={() => {
                          // Force refresh customer data
                          queryClient.invalidateQueries({ queryKey: [`/api/companies/${effectiveCompanyId}/customers`] });
                          queryClient.refetchQueries({ queryKey: [`/api/companies/${effectiveCompanyId}/customers`] });
                        }}
                      >
                        Refresh
                      </Button>
                    </div>
                    <FormControl>
                      <CustomerCombobox
                        customers={Array.isArray(customers) ? customers : []}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select customer..."
                        searchPlaceholder="Search customers by name, Customer ID, phone, or email..."
                        emptyText="No customers found for this company."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Loan Type */}
              <FormField
                control={form.control}
                name="loan_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Loan Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select loan type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="personal">Personal</SelectItem>
                        <SelectItem value="business">Business</SelectItem>
                        <SelectItem value="microfinance">Microfinance</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Loan Amount */}
              <FormField
                control={form.control}
                name="loan_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Loan Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter loan amount"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Interest Rate */}
              <FormField
                control={form.control}
                name="interest_rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Interest Rate (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter interest rate"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Interest Deduction Option */}
              <FormField
                control={form.control}
                name="deduct_interest"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className="font-medium">Interest Collection</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => field.onChange(value === "upfront")}
                        defaultValue={field.value ? "upfront" : "included"}
                        className="flex flex-col space-y-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="upfront" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Deduct interest upfront
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="included" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Include interest in installments
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Predefined loan options - show based on frequency */}
              <div className="space-y-3">
                <Label className="font-medium">Loan Terms Presets</Label>
                {frequency === 'daily' && (
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      type="button"
                      variant={form.getValues("term_units") === 30 ? "default" : "outline"}
                      onClick={() => handleQuickPreset("daily-30")}
                      className="h-10"
                    >
                      30 Days
                    </Button>
                    <Button
                      type="button"
                      variant={form.getValues("term_units") === 100 ? "default" : "outline"}
                      onClick={() => handleQuickPreset("daily-100")}
                      className="h-10"
                    >
                      100 Days
                    </Button>
                  </div>
                )}

                {frequency === 'weekly' && (
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      type="button"
                      variant={form.getValues("term_units") === 10 ? "default" : "outline"}
                      onClick={() => handleQuickPreset("weekly-10")}
                      className="h-10"
                    >
                      10 Weeks
                    </Button>
                    <Button
                      type="button"
                      variant={form.getValues("term_units") === 20 ? "default" : "outline"}
                      onClick={() => handleQuickPreset("weekly-20")}
                      className="h-10"
                    >
                      20 Weeks
                    </Button>
                  </div>
                )}

                {frequency === 'monthly' && (
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      type="button"
                      variant={form.getValues("term_units") === 3 ? "default" : "outline"}
                      onClick={() => handleQuickPreset("monthly-3")}
                      className="h-10"
                    >
                      3 Months
                    </Button>
                    <Button
                      type="button"
                      variant={form.getValues("term_units") === 6 ? "default" : "outline"}
                      onClick={() => handleQuickPreset("monthly-6")}
                      className="h-10"
                    >
                      6 Months
                    </Button>
                    <Button
                      type="button"
                      variant={form.getValues("term_units") === 12 ? "default" : "outline"}
                      onClick={() => handleQuickPreset("monthly-12")}
                      className="h-10"
                    >
                      12 Months
                    </Button>
                  </div>
                )}
              </div>

              {/* Frequency Display - Read-only when preset */}
              <FormField
                control={form.control}
                name="frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Payment Frequency</FormLabel>
                    {presetFrequency ? (
                      // If preset, just show a disabled select with the preset value
                      <FormControl>
                        <Input
                          value={field.value === "daily" ? "Daily" :
                                field.value === "weekly" ? "Weekly" :
                                field.value === "monthly" ? "Monthly" : field.value}
                          disabled
                          className="capitalize"
                        />
                      </FormControl>
                    ) : (
                      // Otherwise show the select dropdown
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment frequency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Number of Payments */}
              <FormField
                control={form.control}
                name="term_units"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Number of {frequency === "daily" ? "Days" : frequency === "weekly" ? "Weeks" : "Months"}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={`Enter number of ${frequency === "daily" ? "days" : frequency === "weekly" ? "weeks" : "months"}`}
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Start Date */}
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Start Date</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* End Date (Calculated) */}
              <div className="space-y-2">
                <Label className="font-medium">End Date (Calculated)</Label>
                <Input
                  type="date"
                  value={summaryValues.endDate}
                  disabled
                />
              </div>
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about this loan"
                      className="resize-none min-h-[100px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Loan Summary */}
            <Card className="bg-slate-50 dark:bg-slate-900">
              <CardHeader className="py-3">
                <CardTitle className="text-lg">Loan Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Loan Amount</p>
                    <p className="font-semibold">₹{loanAmount.toLocaleString()}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Interest Amount</p>
                    <p className="font-semibold">₹{summaryValues.totalInterestAmount.toLocaleString()}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Disbursed Amount</p>
                    <p className="font-semibold text-green-600">₹{summaryValues.actualDisbursalAmount.toLocaleString()}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Total Repayable</p>
                    <p className="font-semibold">₹{summaryValues.totalRepayableAmount.toLocaleString()}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Installment Amount</p>
                    <p className="font-semibold">₹{summaryValues.installmentAmount.toLocaleString(undefined, { maximumFractionDigits: 2 })}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Installment Frequency</p>
                    <p className="font-semibold capitalize">{frequency}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Number of Installments</p>
                    <p className="font-semibold">{termUnits}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-slate-500">Interest Collection</p>
                    <p className="font-semibold">{deductInterest ? "Upfront" : "Included in Installments"}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end space-x-2">
              {onCancel && (
                <Button variant="outline" type="button" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isPending}>
                {isPending && <Spinner className="mr-2 h-4 w-4" />}
                Create Loan
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
    </>
  );
}