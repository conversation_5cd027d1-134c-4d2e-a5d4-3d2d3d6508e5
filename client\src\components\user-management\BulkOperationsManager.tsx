import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Upload, Download, Users, FileText, AlertCircle, CheckCircle, 
  XCircle, Clock, FileDown, FileUp, Settings 
} from 'lucide-react';
import { apiRequest } from '@/lib/api';
import { validateCsvFile, readCsvFile, generateFilename, userExportColumns, exportToCsv } from '@/lib/exportUtils';
import { useCompany } from '@/lib/companies';

interface BulkOperationResult {
  success: number;
  failed: number;
  errors: string[];
  details: {
    successfulRecords: any[];
    failedRecords: Array<{ record: any; error: string }>;
  };
}

interface UserTemplate {
  id: number;
  name: string;
  description: string;
  default_role: string;
  usage_count: number;
  is_active: boolean;
  created_at: string;
}

interface BulkOperationLog {
  id: number;
  operation_type: string;
  total_records: number;
  successful_records: number;
  failed_records: number;
  status: string;
  started_at: string;
  completed_at: string;
  file_name?: string;
}

export default function BulkOperationsManager() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { currentCompany } = useCompany();
  
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [operationResult, setOperationResult] = useState<BulkOperationResult | null>(null);

  // Fetch user templates
  const { data: userTemplates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['/api/bulk-operations/user-templates'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/bulk-operations/user-templates');
      return response.json() as Promise<UserTemplate[]>;
    },
  });

  // Fetch bulk operation logs
  const { data: operationLogs, isLoading: isLoadingLogs } = useQuery({
    queryKey: ['/api/bulk-operations/logs'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/bulk-operations/logs');
      return response.json() as Promise<BulkOperationLog[]>;
    },
  });

  // Import users mutation
  const importUsersMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('csvFile', file);
      
      const response = await apiRequest('POST', '/api/bulk-operations/import-users', formData);
      return response.json() as Promise<BulkOperationResult>;
    },
    onSuccess: (result) => {
      setOperationResult(result);
      queryClient.invalidateQueries({ queryKey: ['/api/bulk-operations/logs'] });
      queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.company_id || currentCompany?.id, 'users'] });
      
      toast({
        title: 'Import completed',
        description: `Successfully imported ${result.success} users. ${result.failed} failed.`,
        variant: result.failed > 0 ? 'destructive' : 'default',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Import failed',
        description: error.message || 'Failed to import users',
        variant: 'destructive',
      });
    },
    onSettled: () => {
      setIsUploading(false);
      setUploadProgress(0);
    }
  });

  // Export users mutation
  const exportUsersMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('GET', '/api/bulk-operations/export-users');
      return response.text();
    },
    onSuccess: (csvContent) => {
      const filename = generateFilename('users_export');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.click();
      URL.revokeObjectURL(url);
      
      toast({
        title: 'Export completed',
        description: 'Users exported successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Export failed',
        description: error.message || 'Failed to export users',
        variant: 'destructive',
      });
    }
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validateCsvFile(file);
    if (!validation.valid) {
      toast({
        title: 'Invalid file',
        description: validation.error,
        variant: 'destructive',
      });
      return;
    }

    setSelectedFile(file);
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadProgress(0);
    
    // Simulate progress
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return prev;
        }
        return prev + 10;
      });
    }, 200);

    importUsersMutation.mutate(selectedFile);
  };

  const handleExport = () => {
    exportUsersMutation.mutate();
  };

  const downloadTemplate = async () => {
    try {
      const response = await apiRequest('GET', '/api/bulk-operations/csv-template');
      const csvContent = await response.text();
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'user_import_template.csv';
      link.click();
      URL.revokeObjectURL(url);
      
      toast({
        title: 'Template downloaded',
        description: 'CSV template downloaded successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Download failed',
        description: error.message || 'Failed to download template',
        variant: 'destructive',
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Bulk Operations</h2>
          <p className="text-muted-foreground">
            Import, export, and manage users in bulk
          </p>
        </div>
      </div>

      <Tabs defaultValue="import" className="space-y-4">
        <TabsList>
          <TabsTrigger value="import">Import Users</TabsTrigger>
          <TabsTrigger value="export">Export Users</TabsTrigger>
          <TabsTrigger value="templates">User Templates</TabsTrigger>
          <TabsTrigger value="logs">Operation Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileUp className="h-5 w-5" />
                Import Users from CSV
              </CardTitle>
              <CardDescription>
                Upload a CSV file to create multiple users at once
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={downloadTemplate}
                  className="flex items-center gap-2"
                >
                  <FileDown className="h-4 w-4" />
                  Download Template
                </Button>
                <span className="text-sm text-muted-foreground">
                  Download the CSV template to see the required format
                </span>
              </div>

              <div className="space-y-2">
                <Label htmlFor="csv-file">Select CSV File</Label>
                <Input
                  id="csv-file"
                  type="file"
                  accept=".csv"
                  onChange={handleFileSelect}
                  disabled={isUploading}
                />
              </div>

              {selectedFile && (
                <Alert>
                  <FileText className="h-4 w-4" />
                  <AlertDescription>
                    Selected file: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
                  </AlertDescription>
                </Alert>
              )}

              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Uploading and processing...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} />
                </div>
              )}

              <Button
                onClick={handleImport}
                disabled={!selectedFile || isUploading}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                {isUploading ? 'Importing...' : 'Import Users'}
              </Button>

              {operationResult && (
                <Alert className={operationResult.failed > 0 ? 'border-red-200' : 'border-green-200'}>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p>
                        Import completed: {operationResult.success} successful, {operationResult.failed} failed
                      </p>
                      {operationResult.errors.length > 0 && (
                        <details className="mt-2">
                          <summary className="cursor-pointer font-medium">View Errors</summary>
                          <ul className="mt-2 space-y-1 text-sm">
                            {operationResult.errors.slice(0, 10).map((error, index) => (
                              <li key={index} className="text-red-600">• {error}</li>
                            ))}
                            {operationResult.errors.length > 10 && (
                              <li className="text-muted-foreground">
                                ... and {operationResult.errors.length - 10} more errors
                              </li>
                            )}
                          </ul>
                        </details>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileDown className="h-5 w-5" />
                Export Users to CSV
              </CardTitle>
              <CardDescription>
                Download all users in your organization as a CSV file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={handleExport}
                disabled={exportUsersMutation.isPending}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {exportUsersMutation.isPending ? 'Exporting...' : 'Export Users'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                User Templates
              </CardTitle>
              <CardDescription>
                Manage templates for creating users with predefined settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingTemplates ? (
                <div className="text-center py-4">Loading templates...</div>
              ) : userTemplates && userTemplates.length > 0 ? (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {userTemplates.map((template) => (
                    <Card key={template.id} className="p-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{template.name}</h4>
                          <Badge variant={template.is_active ? 'default' : 'secondary'}>
                            {template.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {template.description}
                        </p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>Role: {template.default_role}</span>
                          <span>Used: {template.usage_count} times</span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No user templates found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Operation Logs
              </CardTitle>
              <CardDescription>
                View history of bulk operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingLogs ? (
                <div className="text-center py-4">Loading logs...</div>
              ) : operationLogs && operationLogs.length > 0 ? (
                <div className="space-y-4">
                  {operationLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(log.status)}
                          <span className="font-medium capitalize">
                            {log.operation_type.replace('_', ' ')}
                          </span>
                          <Badge className={getStatusColor(log.status)}>
                            {log.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {log.file_name && <span>File: {log.file_name} • </span>}
                          Total: {log.total_records} • 
                          Success: {log.successful_records} • 
                          Failed: {log.failed_records}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Started: {new Date(log.started_at).toLocaleString()}
                          {log.completed_at && (
                            <span> • Completed: {new Date(log.completed_at).toLocaleString()}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No operation logs found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
