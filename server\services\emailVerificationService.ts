import crypto from 'crypto';
import { db } from '../db';
import { 
  users, 
  emailVerificationTokens,
  type InsertEmailVerificationToken 
} from '@shared/schema';
import { eq, and, gt } from 'drizzle-orm';

export class EmailVerificationService {
  /**
   * Generate verification token for user
   */
  async generateVerificationToken(userId: number): Promise<string> {
    try {
      const token = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Delete any existing tokens for this user
      await db.delete(emailVerificationTokens)
        .where(eq(emailVerificationTokens.user_id, userId));

      // Insert new token
      await db.insert(emailVerificationTokens).values({
        user_id: userId,
        token,
        expires_at: expiresAt
      });

      return token;
    } catch (error) {
      console.error('Error generating verification token:', error);
      throw new Error('Failed to generate verification token');
    }
  }

  /**
   * Verify email using token
   */
  async verifyEmail(token: string): Promise<{ success: boolean; message: string; userId?: number }> {
    try {
      // Find valid token
      const [verificationRecord] = await db
        .select()
        .from(emailVerificationTokens)
        .where(
          and(
            eq(emailVerificationTokens.token, token),
            gt(emailVerificationTokens.expires_at, new Date())
          )
        );

      if (!verificationRecord) {
        return { 
          success: false, 
          message: 'Invalid or expired verification token. Please request a new verification email.' 
        };
      }

      // Update user as verified
      await db.update(users)
        .set({ 
          email_verified: true,
          updated_at: new Date()
        })
        .where(eq(users.id, verificationRecord.user_id));

      // Delete used token
      await db.delete(emailVerificationTokens)
        .where(eq(emailVerificationTokens.id, verificationRecord.id));

      return { 
        success: true, 
        message: 'Email verified successfully. You can now access all features.',
        userId: verificationRecord.user_id
      };
    } catch (error) {
      console.error('Error verifying email:', error);
      return { 
        success: false, 
        message: 'Server error during email verification. Please try again.' 
      };
    }
  }

  /**
   * Send verification email (placeholder - integrate with email service)
   */
  async sendVerificationEmail(userId: number, email: string): Promise<{ success: boolean; message: string }> {
    try {
      const token = await this.generateVerificationToken(userId);
      const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email?token=${token}`;

      // In production, integrate with email service (SendGrid, AWS SES, etc.)
      console.log(`
========================================
EMAIL VERIFICATION
========================================
To: ${email}
Subject: Verify your TrackFina account

Please click the link below to verify your email address:
${verificationUrl}

This link will expire in 24 hours.

If you didn't create an account with TrackFina, please ignore this email.

Best regards,
TrackFina Team
========================================
      `);
      
      // TODO: Implement actual email sending
      // await emailService.send({
      //   to: email,
      //   subject: 'Verify your TrackFina account',
      //   template: 'email-verification',
      //   data: { 
      //     verificationUrl,
      //     email 
      //   }
      // });

      return {
        success: true,
        message: 'Verification email sent successfully. Please check your inbox.'
      };
    } catch (error) {
      console.error('Error sending verification email:', error);
      return {
        success: false,
        message: 'Failed to send verification email. Please try again.'
      };
    }
  }

  /**
   * Resend verification email
   */
  async resendVerificationEmail(userId: number): Promise<{ success: boolean; message: string }> {
    try {
      // Get user email
      const [user] = await db
        .select({ email: users.email, email_verified: users.email_verified })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        return {
          success: false,
          message: 'User not found.'
        };
      }

      if (user.email_verified) {
        return {
          success: false,
          message: 'Email is already verified.'
        };
      }

      return await this.sendVerificationEmail(userId, user.email);
    } catch (error) {
      console.error('Error resending verification email:', error);
      return {
        success: false,
        message: 'Failed to resend verification email. Please try again.'
      };
    }
  }

  /**
   * Check if user's email is verified
   */
  async isEmailVerified(userId: number): Promise<boolean> {
    try {
      const [user] = await db
        .select({ email_verified: users.email_verified })
        .from(users)
        .where(eq(users.id, userId));

      return user?.email_verified || false;
    } catch (error) {
      console.error('Error checking email verification status:', error);
      return false;
    }
  }

  /**
   * Get verification status for user
   */
  async getVerificationStatus(userId: number): Promise<{
    verified: boolean;
    email: string;
    hasPendingToken: boolean;
  }> {
    try {
      const [user] = await db
        .select({ 
          email: users.email, 
          email_verified: users.email_verified 
        })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        throw new Error('User not found');
      }

      // Check for pending verification token
      const [pendingToken] = await db
        .select()
        .from(emailVerificationTokens)
        .where(
          and(
            eq(emailVerificationTokens.user_id, userId),
            gt(emailVerificationTokens.expires_at, new Date())
          )
        );

      return {
        verified: user.email_verified || false,
        email: user.email,
        hasPendingToken: !!pendingToken
      };
    } catch (error) {
      console.error('Error getting verification status:', error);
      throw new Error('Failed to get verification status');
    }
  }

  /**
   * Clean up expired tokens (should be run periodically)
   */
  async cleanupExpiredTokens(): Promise<number> {
    try {
      const result = await db
        .delete(emailVerificationTokens)
        .where(
          and(
            eq(emailVerificationTokens.expires_at, new Date())
          )
        );

      console.log(`Cleaned up ${result.rowCount || 0} expired verification tokens`);
      return result.rowCount || 0;
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
      return 0;
    }
  }
}

export const emailVerificationService = new EmailVerificationService();
