import { describe, it, expect, beforeEach, vi, beforeAll, afterAll } from 'vitest';
import { EnhancedSessionService } from '../enhancedSessionService';
import { db } from '../../db';
import { userSessions, sessionPolicies, trustedDevices, users } from '@shared/schema';
import { eq } from 'drizzle-orm';

// Mock the database
vi.mock('../../db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn()
  }
}));

describe('EnhancedSessionService', () => {
  let service: EnhancedSessionService;
  let mockDb: any;

  beforeEach(() => {
    service = new EnhancedSessionService();
    mockDb = db as any;
    vi.clearAllMocks();
  });

  describe('createSession', () => {
    it('should create a new session with default policy', async () => {
      // Mock user lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([{ id: 1, role: 'employee' }])
      });

      // Mock session policy lookup (no policies found)
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([])
      });

      // Mock active sessions lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([])
      });

      // Mock trusted device lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      });

      // Mock session creation
      const mockSession = {
        id: 1,
        session_id: 'test-session-id',
        user_id: 1,
        device_type: 'desktop',
        status: 'active',
        created_at: new Date(),
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 86400000),
        is_trusted_device: false,
        requires_mfa: false,
        mfa_verified: false,
        fresh_auth: true
      };

      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([mockSession])
      });

      // Mock activity logging
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockResolvedValue(undefined)
      });

      const result = await service.createSession({
        userId: 1,
        deviceType: 'desktop',
        userAgent: 'Mozilla/5.0...',
        ipAddress: '***********'
      });

      expect(result.session).toBeDefined();
      expect(result.session.user_id).toBe(1);
      expect(result.session.device_type).toBe('desktop');
      expect(mockDb.insert).toHaveBeenCalledTimes(2); // Session + activity log
    });

    it('should enforce concurrent session limits', async () => {
      // Mock user lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([{ id: 1, role: 'employee' }])
      });

      // Mock session policy lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([{
          max_concurrent_sessions: 2,
          max_sessions_per_device_type: { desktop: 1 },
          idle_timeout_seconds: 3600,
          absolute_timeout_seconds: 86400
        }])
      });

      // Mock active sessions lookup (2 existing sessions)
      const existingSessions = [
        { session_id: 'session-1', device_type: 'desktop' },
        { session_id: 'session-2', device_type: 'mobile' }
      ];
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue(existingSessions)
      });

      // Mock session termination
      mockDb.update.mockReturnValueOnce({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ session_id: 'session-1', user_id: 1 }])
      });

      // Mock trusted device lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      });

      // Mock session creation
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{
          id: 1,
          session_id: 'new-session',
          user_id: 1,
          device_type: 'desktop'
        }])
      });

      // Mock activity logging (2 calls: termination + creation)
      mockDb.insert.mockReturnValue({
        values: vi.fn().mockResolvedValue(undefined)
      });

      const result = await service.createSession({
        userId: 1,
        deviceType: 'desktop',
        userAgent: 'Mozilla/5.0...',
        ipAddress: '***********'
      });

      expect(result.session).toBeDefined();
      expect(mockDb.update).toHaveBeenCalled(); // Session termination
    });
  });

  describe('validateSession', () => {
    it('should validate an active session', async () => {
      const mockSession = {
        id: 1,
        session_id: 'test-session',
        user_id: 1,
        status: 'active',
        last_activity: new Date(Date.now() - 1000), // 1 second ago
        expires_at: new Date(Date.now() + 3600000), // 1 hour from now
        idle_timeout: 3600,
        requires_mfa: false,
        mfa_verified: false
      };

      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([mockSession])
      });

      // Mock session activity update
      mockDb.update.mockReturnValueOnce({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(undefined)
      });

      const result = await service.validateSession('test-session', '***********');

      expect(result.isValid).toBe(true);
      expect(result.session).toEqual(mockSession);
    });

    it('should reject expired session', async () => {
      const mockSession = {
        id: 1,
        session_id: 'test-session',
        user_id: 1,
        status: 'active',
        last_activity: new Date(),
        expires_at: new Date(Date.now() - 1000), // Expired 1 second ago
        idle_timeout: 3600,
        requires_mfa: false,
        mfa_verified: false
      };

      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([mockSession])
      });

      // Mock session termination
      mockDb.update.mockReturnValueOnce({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([mockSession])
      });

      // Mock activity logging
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockResolvedValue(undefined)
      });

      const result = await service.validateSession('test-session');

      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('Session expired');
      expect(result.requiresAction).toBe('logout');
    });

    it('should reject session requiring MFA', async () => {
      const mockSession = {
        id: 1,
        session_id: 'test-session',
        user_id: 1,
        status: 'active',
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 3600000),
        idle_timeout: 3600,
        requires_mfa: true,
        mfa_verified: false
      };

      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([mockSession])
      });

      const result = await service.validateSession('test-session');

      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('MFA verification required');
      expect(result.requiresAction).toBe('mfa');
    });
  });

  describe('getUserActiveSessions', () => {
    it('should return active sessions for user', async () => {
      const mockSessions = [
        { id: 1, session_id: 'session-1', user_id: 1, status: 'active' },
        { id: 2, session_id: 'session-2', user_id: 1, status: 'active' }
      ];

      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue(mockSessions)
      });

      const result = await service.getUserActiveSessions(1);

      expect(result).toEqual(mockSessions);
      expect(result).toHaveLength(2);
    });
  });

  describe('terminateSession', () => {
    it('should terminate a session and log activity', async () => {
      const mockSession = {
        session_id: 'test-session',
        user_id: 1,
        company_id: 1
      };

      mockDb.update.mockReturnValueOnce({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([mockSession])
      });

      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockResolvedValue(undefined)
      });

      await service.terminateSession('test-session', 'User logout', 1);

      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.insert).toHaveBeenCalled(); // Activity logging
    });
  });

  describe('cleanupExpiredSessions', () => {
    it('should clean up expired sessions', async () => {
      const expiredSessions = [
        { session_id: 'expired-1', user_id: 1, company_id: 1 },
        { session_id: 'expired-2', user_id: 2, company_id: 1 }
      ];

      mockDb.update.mockReturnValueOnce({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue(expiredSessions)
      });

      // Mock activity logging for each expired session
      mockDb.insert.mockReturnValue({
        values: vi.fn().mockResolvedValue(undefined)
      });

      const result = await service.cleanupExpiredSessions();

      expect(result).toBe(2);
      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.insert).toHaveBeenCalledTimes(2); // One log per expired session
    });
  });

  describe('getSessionStatistics', () => {
    it('should return session statistics', async () => {
      const mockSessions = [
        {
          status: 'active',
          device_type: 'desktop',
          location_country: 'US',
          created_at: new Date(),
          terminated_at: null
        },
        {
          status: 'expired',
          device_type: 'mobile',
          location_country: 'CA',
          created_at: new Date(Date.now() - 3600000),
          terminated_at: new Date()
        }
      ];

      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(mockSessions)
      });

      const result = await service.getSessionStatistics({ userId: 1, days: 30 });

      expect(result.totalSessions).toBe(2);
      expect(result.activeSessions).toBe(1);
      expect(result.expiredSessions).toBe(1);
      expect(result.deviceBreakdown).toEqual({ desktop: 1, mobile: 1 });
      expect(result.locationBreakdown).toEqual({ US: 1, CA: 1 });
    });
  });
});
