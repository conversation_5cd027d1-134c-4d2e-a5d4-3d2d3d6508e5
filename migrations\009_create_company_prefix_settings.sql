-- Migration to create company_prefix_settings table
-- This table will store company-specific prefix settings for reference codes

-- Create company_prefix_settings table
CREATE TABLE IF NOT EXISTS "company_prefix_settings" (
  "id" SERIAL PRIMARY KEY,
  "company_id" INTEGER NOT NULL REFERENCES "companies" ("id") ON DELETE CASCADE,
  "loan_prefix" VARCHAR(5) NOT NULL,
  "loan_start_number" INTEGER NOT NULL DEFAULT 1,
  "collection_prefix" VARCHAR(5) NOT NULL,
  "collection_start_number" INTEGER NOT NULL DEFAULT 1,
  "customer_prefix" VARCHAR(5) NOT NULL,
  "customer_start_number" INTEGER NOT NULL DEFAULT 1,
  "partner_prefix" VARCHAR(5) NOT NULL,
  "partner_start_number" INTEGER NOT NULL DEFAULT 1,
  "agent_prefix" VARCHAR(5) NOT NULL,
  "agent_start_number" INTEGER NOT NULL DEFAULT 1,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE("company_id")
);

-- Create an index for faster lookups by company_id
CREATE INDEX IF NOT EXISTS idx_company_prefix_settings_company_id ON company_prefix_settings(company_id);

-- Comment on the table to document its purpose
COMMENT ON TABLE company_prefix_settings IS 'Stores company-specific prefix settings for reference codes used in loans, collections, customers, partners, and agents';

-- Comments on columns to document their purpose
COMMENT ON COLUMN company_prefix_settings.loan_prefix IS 'Prefix used for loan reference codes (e.g., "GS" for GOVINDARAJI S)';
COMMENT ON COLUMN company_prefix_settings.loan_start_number IS 'Starting number for loan reference codes (e.g., 1 for GS-001)';
COMMENT ON COLUMN company_prefix_settings.collection_prefix IS 'Prefix used for collection reference codes';
COMMENT ON COLUMN company_prefix_settings.collection_start_number IS 'Starting number for collection reference codes';
COMMENT ON COLUMN company_prefix_settings.customer_prefix IS 'Prefix used for customer reference codes';
COMMENT ON COLUMN company_prefix_settings.customer_start_number IS 'Starting number for customer reference codes';
COMMENT ON COLUMN company_prefix_settings.partner_prefix IS 'Prefix used for partner reference codes';
COMMENT ON COLUMN company_prefix_settings.partner_start_number IS 'Starting number for partner reference codes';
COMMENT ON COLUMN company_prefix_settings.agent_prefix IS 'Prefix used for agent reference codes';
COMMENT ON COLUMN company_prefix_settings.agent_start_number IS 'Starting number for agent reference codes';
