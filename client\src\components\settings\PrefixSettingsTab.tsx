import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';
import { PrefixSetupForm } from '@/components/company/PrefixSetupForm';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';

export function PrefixSettingsTab() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [prefixSettings, setPrefixSettings] = useState<any>(null);

  // Define the type for the API response
  interface PrefixSettingsResponse {
    data: any;
    message: string;
    code: string;
  }

  // Fetch current prefix settings
  const {
    data,
    isLoading,
    error,
  } = useQuery<PrefixSettingsResponse>({
    queryKey: [`/api/companies/${user?.company_id}/prefix-settings`],
    enabled: !!user?.company_id,
  });

  // Update prefix settings when data changes
  useEffect(() => {
    if (data && data.data) {
      setPrefixSettings(data.data);
    }
  }, [data]);

  const handleComplete = () => {
    // Invalidate the query to refetch the latest data
    queryClient.invalidateQueries({ queryKey: [`/api/companies/${user?.company_id}/prefix-settings`] });

    toast({
      title: 'Settings updated',
      description: 'Your prefix settings have been saved',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load prefix settings. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Reference Code Settings</h2>
        <p className="text-muted-foreground">
          Configure custom prefixes and starting numbers for your entities
        </p>
      </div>

      <Alert className="border-red-500 bg-red-50">
        <InfoIcon className="h-4 w-4 text-red-500" />
        <AlertTitle className="text-red-500 font-bold">Important</AlertTitle>
        <AlertDescription className="text-red-500 font-medium">
          Once saved, you should not be able to change this information.
        </AlertDescription>
      </Alert>

      {user?.company_id && (
        <PrefixSetupForm
          companyId={user.company_id}
          onComplete={handleComplete}
          initialValues={prefixSettings}
        />
      )}
    </div>
  );
}
