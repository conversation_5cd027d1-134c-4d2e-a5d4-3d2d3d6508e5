// server/utils/errorLogger.ts
class ErrorLogger {
  private isDevMode: boolean;
  
  constructor() {
    this.isDevMode = process.env.NODE_ENV !== 'production';
  }
  
  /**
   * Log error messages with context
   * @param message Error message to log
   * @param context Context where the error occurred
   * @param error Original error object if available
   */
  logError(message: string, context: string, error?: Error): void {
    const timestamp = new Date().toISOString();
    const errorDetails = error 
      ? `\n  Stack: ${error.stack || 'No stack trace'}\n  Message: ${error.message}`
      : '';
    
    console.error(`[${timestamp}] [ERROR] [${context}] ${message}${errorDetails}`);
  }
  
  /**
   * Shorthand method for logging errors - for compatibility with client-side code
   * @param message Error message to log
   * @param context Context where the error occurred
   * @param data Additional data or error object
   */
  error(message: string, context: string, data?: any): void {
    if (data instanceof Error) {
      this.logError(message, context, data);
    } else {
      const timestamp = new Date().toISOString();
      console.error(`[${timestamp}] [ERROR] [${context}] ${message}`, data || '');
    }
  }
  
  /**
   * Shorthand method for logging warnings - for compatibility with client-side code
   * @param message Warning message to log
   * @param context Context where the warning occurred
   * @param data Additional data for context
   */
  warn(message: string, context: string, data?: any): void {
    this.logWarning(message, context, data);
  }

  /**
   * Log informational messages
   * @param message Info message to log
   * @param context Context where the info is coming from
   * @param data Optional additional data for context
   */
  logInfo(message: string, context: string, data?: Record<string, any>): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [INFO] [${context}] ${message}`;
    
    if (data) {
      console.log(logMessage, data);
    } else {
      console.log(logMessage);
    }
  }

  /**
   * Log warning messages
   * @param message Warning message to log
   * @param context Context where the warning occurred
   * @param data Optional additional data for context
   */
  logWarning(message: string, context: string, data?: Record<string, any>): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [WARNING] [${context}] ${message}`;
    
    if (data) {
      console.warn(logMessage, data);
    } else {
      console.warn(logMessage);
    }
  }
  
  /**
   * Log debug messages (only in development)
   * @param message Debug message to log
   * @param context Context where the debug message is coming from
   * @param data Optional additional data for context
   */
  logDebug(message: string, context: string, data?: Record<string, any>): void {
    if (!this.isDevMode) return;
    
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [DEBUG] [${context}] ${message}`;
    
    if (data) {
      console.log(logMessage, data);
    } else {
      console.log(logMessage);
    }
  }

  /**
   * Log database-related errors with enhanced details
   * @param message Error message to log
   * @param query The SQL query that caused the error (if available)
   * @param error Original error object if available
   */
  logDatabaseError(message: string, query?: string, error?: Error): void {
    const timestamp = new Date().toISOString();
    const queryDetails = query ? `\n  Query: ${query}` : '';
    const errorDetails = error 
      ? `\n  Stack: ${error.stack || 'No stack trace'}\n  Message: ${error.message}`
      : '';
    
    console.error(`[${timestamp}] [ERROR] [DATABASE] ${message}${queryDetails}${errorDetails}`);
  }

  /**
   * Log API request errors with relevant details
   * @param message Error message to log
   * @param endpoint API endpoint that caused the error
   * @param method HTTP method (GET, POST, etc.)
   * @param error Original error object if available
   */
  logApiError(message: string, endpoint: string, method: string, error?: Error): void {
    const timestamp = new Date().toISOString();
    const errorDetails = error 
      ? `\n  Stack: ${error.stack || 'No stack trace'}\n  Message: ${error.message}`
      : '';
    
    console.error(`[${timestamp}] [ERROR] [API] ${message}\n  Endpoint: ${endpoint}\n  Method: ${method}${errorDetails}`);
  }
  
  /**
   * Format an error for API response
   * @param message User-friendly error message
   * @param error Original error object (optional)
   * @param status HTTP status code (default 500)
   * @returns Formatted error object
   */
  formatErrorResponse(message: string, error?: Error, status: number = 500): any {
    if (error) {
      this.logError(message, 'api-response', error);
    }
    
    return {
      error: {
        message,
        status,
        timestamp: new Date().toISOString()
      }
    };
  }
}

// Export singleton instance
const errorLogger = new ErrorLogger();
export default errorLogger;