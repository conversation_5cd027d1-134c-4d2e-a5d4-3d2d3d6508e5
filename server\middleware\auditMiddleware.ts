import { Request, Response, NextFunction } from 'express';
import { auditService } from '../services/auditService';
import { type AuthRequest } from './auth';
import { type PermissionUsageContext, type DataAccessContext } from '@shared/schema';

// Extend AuthRequest to include audit context
export interface AuditRequest extends AuthRequest {
  auditContext?: {
    resourceType?: string;
    resourceId?: string;
    operationType?: string;
    operationDetails?: string;
    isSensitiveOperation?: boolean;
    complianceFlags?: string[];
    entityType?: string;
    tableName?: string;
    fieldNames?: string[];
    oldValues?: any;
    newValues?: any;
    sensitiveFieldsAccessed?: string[];
  };
}

/**
 * Middleware to log permission usage
 */
export function auditPermissionUsage(
  permissionCode: string,
  options: {
    resourceType?: string;
    operationType?: string;
    isSensitiveOperation?: boolean;
    complianceFlags?: string[];
  } = {}
) {
  return async (req: AuditRequest, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // Store original end function
    const originalEnd = res.end;
    
    // Override end function to log audit after response
    res.end = function(chunk?: any, encoding?: any) {
      const responseTime = Date.now() - startTime;
      
      // Log permission usage after response is sent
      if (req.user) {
        const result = res.statusCode >= 200 && res.statusCode < 300 ? 'success' : 
                      res.statusCode === 403 ? 'denied' : 'error';
        
        const context: PermissionUsageContext = {
          userId: req.user.id,
          companyId: req.user.company_id,
          sessionId: req.session?.sessionId,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.path,
          method: req.method,
          permissionCode,
          permissionName: options.resourceType ? `${options.resourceType} ${permissionCode}` : permissionCode,
          resourceType: options.resourceType || req.auditContext?.resourceType,
          resourceId: req.params.id || req.auditContext?.resourceId,
          operationType: options.operationType || req.auditContext?.operationType || req.method.toLowerCase(),
          operationDetails: req.auditContext?.operationDetails,
          result,
          isSensitiveOperation: options.isSensitiveOperation || req.auditContext?.isSensitiveOperation || false,
          complianceFlags: options.complianceFlags || req.auditContext?.complianceFlags || [],
          responseTimeMs: responseTime,
          metadata: {
            statusCode: res.statusCode,
            query: req.query,
            params: req.params,
            referer: req.get('Referer'),
          },
        };

        auditService.logPermissionUsage(context).catch(error => {
          console.error('Failed to log permission usage:', error);
        });
      }
      
      // Call original end function
      originalEnd.call(this, chunk, encoding);
    };

    next();
  };
}

/**
 * Middleware to log data access
 */
export function auditDataAccess(
  entityType: string,
  tableName: string,
  options: {
    sensitiveFields?: string[];
    retentionPeriodDays?: number;
  } = {}
) {
  return async (req: AuditRequest, res: Response, next: NextFunction) => {
    // Store original end function
    const originalEnd = res.end;
    
    // Override end function to log audit after response
    res.end = function(chunk?: any, encoding?: any) {
      // Log data access after response is sent
      if (req.user && res.statusCode >= 200 && res.statusCode < 300) {
        const context: DataAccessContext = {
          userId: req.user.id,
          companyId: req.user.company_id,
          sessionId: req.session?.sessionId,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.path,
          method: req.method,
          entityType,
          entityId: req.params.id || req.auditContext?.resourceId || 'unknown',
          tableName,
          fieldNames: req.auditContext?.fieldNames,
          oldValues: req.auditContext?.oldValues,
          newValues: req.auditContext?.newValues,
          recordCount: 1,
          dataScope: 'company', // TODO: Determine actual data scope
          fieldAccessLevel: 'full', // TODO: Determine actual access level
          sensitiveFieldsAccessed: req.auditContext?.sensitiveFieldsAccessed || options.sensitiveFields || [],
          retentionPeriodDays: options.retentionPeriodDays,
          metadata: {
            statusCode: res.statusCode,
            query: req.query,
            params: req.params,
          },
        };

        auditService.logDataAccess(context).catch(error => {
          console.error('Failed to log data access:', error);
        });
      }
      
      // Call original end function
      originalEnd.call(this, chunk, encoding);
    };

    next();
  };
}

/**
 * Middleware to set audit context for the request
 */
export function setAuditContext(context: {
  resourceType?: string;
  resourceId?: string;
  operationType?: string;
  operationDetails?: string;
  isSensitiveOperation?: boolean;
  complianceFlags?: string[];
  entityType?: string;
  tableName?: string;
  fieldNames?: string[];
  sensitiveFieldsAccessed?: string[];
}) {
  return (req: AuditRequest, res: Response, next: NextFunction) => {
    req.auditContext = {
      ...req.auditContext,
      ...context,
    };
    next();
  };
}

/**
 * Middleware to capture request/response data for audit
 */
export function captureAuditData() {
  return (req: AuditRequest, res: Response, next: NextFunction) => {
    // Capture request body for create/update operations
    if ((req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') && req.body) {
      if (!req.auditContext) req.auditContext = {};
      req.auditContext.newValues = req.body;
    }

    // Store original json function to capture response data
    const originalJson = res.json;
    res.json = function(body: any) {
      // For GET operations, capture the returned data
      if (req.method === 'GET' && body) {
        if (!req.auditContext) req.auditContext = {};
        // Don't log full response for list operations to avoid large logs
        if (Array.isArray(body)) {
          req.auditContext.newValues = { recordCount: body.length };
        } else if (body.id) {
          req.auditContext.newValues = { id: body.id };
        }
      }
      
      return originalJson.call(this, body);
    };

    next();
  };
}

/**
 * Comprehensive audit middleware that combines permission and data access logging
 */
export function comprehensiveAudit(
  permissionCode: string,
  entityType: string,
  tableName: string,
  options: {
    resourceType?: string;
    operationType?: string;
    isSensitiveOperation?: boolean;
    complianceFlags?: string[];
    sensitiveFields?: string[];
    retentionPeriodDays?: number;
  } = {}
) {
  return [
    captureAuditData(),
    setAuditContext({
      resourceType: options.resourceType,
      operationType: options.operationType,
      isSensitiveOperation: options.isSensitiveOperation,
      complianceFlags: options.complianceFlags,
      entityType,
      tableName,
      sensitiveFieldsAccessed: options.sensitiveFields,
    }),
    auditPermissionUsage(permissionCode, {
      resourceType: options.resourceType,
      operationType: options.operationType,
      isSensitiveOperation: options.isSensitiveOperation,
      complianceFlags: options.complianceFlags,
    }),
    auditDataAccess(entityType, tableName, {
      sensitiveFields: options.sensitiveFields,
      retentionPeriodDays: options.retentionPeriodDays,
    }),
  ];
}

/**
 * Audit middleware for sensitive operations
 */
export function auditSensitiveOperation(
  permissionCode: string,
  operationType: string,
  complianceFlags: string[] = []
) {
  return auditPermissionUsage(permissionCode, {
    operationType,
    isSensitiveOperation: true,
    complianceFlags: ['sensitive_operation', ...complianceFlags],
  });
}

/**
 * Audit middleware for admin operations
 */
export function auditAdminOperation(
  permissionCode: string,
  operationType: string
) {
  return auditPermissionUsage(permissionCode, {
    operationType,
    isSensitiveOperation: true,
    complianceFlags: ['admin_operation', 'privileged_access'],
  });
}
