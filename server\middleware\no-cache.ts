import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to disable all caching for financial data accuracy
 * This ensures that all API responses are never cached by browsers or proxies
 */
export function noCacheMiddleware(req: Request, res: Response, next: NextFunction) {
  // Set comprehensive no-cache headers
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate, private, max-age=0',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Last-Modified': new Date().toUTCString(),
    'ETag': `"${Date.now()}-${Math.random().toString(36).substring(7)}"`,
    'Vary': '*',
  });

  // Add security headers for financial applications
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
  });

  next();
}
