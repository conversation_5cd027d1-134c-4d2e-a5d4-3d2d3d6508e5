import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return React.createElement(
    QueryClientProvider,
    { client: queryClient },
    children
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Mock data factories
export const createMockRole = (overrides = {}) => ({
  id: 1,
  name: 'Test Role',
  description: 'Test role description',
  is_system: false,
  permissions: [1, 2, 3],
  ...overrides,
});

export const createMockPermission = (overrides = {}) => ({
  id: 1,
  code: 'test_permission',
  name: 'Test Permission',
  description: 'Test permission description',
  category: 'test',
  ...overrides,
});

export const createMockPermissionCategory = (overrides = {}) => ({
  category: 'test',
  metadata: {
    name: 'Test Category',
    description: 'Test category description',
    icon: 'Shield',
  },
  permissions: [createMockPermission()],
  ...overrides,
});

export const createMockUserPermission = (overrides = {}) => ({
  user_id: 1,
  user_info: {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    role: 'user',
  },
  direct_roles: [
    {
      role_id: 1,
      role_name: 'Test Role',
      role_description: 'Test role description',
    },
  ],
  effective_permissions: {
    total_count: 3,
    permissions_by_category: {
      test: [createMockPermission()],
    },
    permission_codes: ['test_permission'],
  },
  ...overrides,
});

export const createMockPermissionCondition = (overrides = {}) => ({
  id: 1,
  permission_id: 1,
  condition_type: 'time' as const,
  condition_config: {
    start_time: '09:00',
    end_time: '17:00',
    days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    timezone: 'UTC',
  },
  is_active: true,
  priority: 1,
  description: 'Test condition',
  ...overrides,
});

// Mock fetch responses
export const mockFetchSuccess = (data: any) => {
  global.fetch = vi.fn().mockResolvedValue({
    ok: true,
    status: 200,
    json: async () => data,
  } as Response);
};

export const mockFetchError = (status = 500, message = 'Server Error') => {
  global.fetch = vi.fn().mockResolvedValue({
    ok: false,
    status,
    json: async () => ({ error: message }),
  } as Response);
};

export const mockFetchNetworkError = () => {
  global.fetch = vi.fn().mockRejectedValue(new Error('Network Error'));
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { customRender as render };
