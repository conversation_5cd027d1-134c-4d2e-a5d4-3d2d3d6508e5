-- Migration: Role Hierarchy System
-- Description: Create role hierarchy and role templates tables for advanced role management
-- Date: 2024-01-XX

-- Create inheritance type enum
CREATE TYPE inheritance_type AS ENUM ('inherit', 'override', 'deny');

-- Create role hierarchy table for role inheritance
CREATE TABLE role_hierarchy (
  id SERIAL PRIMARY KEY,
  parent_role_id INTEGER NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
  child_role_id INTEGER NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
  inheritance_type inheritance_type DEFAULT 'inherit' NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT unique_parent_child UNIQUE (parent_role_id, child_role_id),
  CONSTRAINT check_not_self_referencing CHECK (parent_role_id != child_role_id)
);

-- Create role templates table for quick role setup
CREATE TABLE role_templates (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  template_config JSONB NOT NULL,
  industry TEXT,
  is_system BOOLEAN DEFAULT FALSE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT unique_template_name UNIQUE (name)
);

-- Create indexes for better performance
CREATE INDEX idx_role_hierarchy_parent ON role_hierarchy(parent_role_id);
CREATE INDEX idx_role_hierarchy_child ON role_hierarchy(child_role_id);
CREATE INDEX idx_role_hierarchy_inheritance_type ON role_hierarchy(inheritance_type);
CREATE INDEX idx_role_templates_industry ON role_templates(industry);
CREATE INDEX idx_role_templates_is_system ON role_templates(is_system);

-- Insert predefined role templates for common use cases
INSERT INTO role_templates (name, description, template_config, industry, is_system) VALUES
(
  'Loan Officer',
  'Standard loan officer with loan creation and customer management permissions',
  '{
    "permissions": [
      "loan_create", "loan_view", "loan_edit", "loan_approve_small",
      "customer_create", "customer_view", "customer_edit",
      "collection_view", "collection_create"
    ],
    "conditions": {
      "loan_approve_small": {
        "type": "amount",
        "config": {"max_amount": 50000, "currency": "USD"}
      }
    },
    "description": "Can manage loans up to $50,000 and handle customer relationships"
  }',
  'financial_services',
  true
),
(
  'Senior Loan Officer',
  'Senior loan officer with higher approval limits and team oversight',
  '{
    "permissions": [
      "loan_create", "loan_view", "loan_edit", "loan_approve_medium", "loan_delete",
      "customer_create", "customer_view", "customer_edit", "customer_delete",
      "collection_view", "collection_create", "collection_edit",
      "user_view", "report_view_basic"
    ],
    "conditions": {
      "loan_approve_medium": {
        "type": "amount",
        "config": {"max_amount": 200000, "currency": "USD"}
      }
    },
    "description": "Can manage loans up to $200,000 and oversee junior staff"
  }',
  'financial_services',
  true
),
(
  'Collection Agent',
  'Specialized role for collection activities and customer follow-up',
  '{
    "permissions": [
      "collection_view", "collection_create", "collection_edit",
      "customer_view", "customer_contact",
      "payment_record", "payment_view",
      "report_view_collections"
    ],
    "conditions": {
      "collection_create": {
        "type": "time",
        "config": {
          "start_time": "08:00",
          "end_time": "18:00",
          "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
          "timezone": "UTC"
        }
      }
    },
    "description": "Focused on payment collection and customer communication during business hours"
  }',
  'financial_services',
  true
),
(
  'Branch Manager',
  'Complete branch management with oversight of all local operations',
  '{
    "permissions": [
      "loan_create", "loan_view", "loan_edit", "loan_approve_large", "loan_delete",
      "customer_create", "customer_view", "customer_edit", "customer_delete",
      "collection_view", "collection_create", "collection_edit", "collection_delete",
      "user_view", "user_create", "user_edit",
      "role_view", "role_assign",
      "report_view_all", "report_export",
      "expense_create", "expense_view", "expense_edit",
      "branch_settings_edit"
    ],
    "conditions": {
      "loan_approve_large": {
        "type": "amount",
        "config": {"max_amount": 500000, "currency": "USD"}
      }
    },
    "description": "Full branch operations management with high approval limits"
  }',
  'financial_services',
  true
),
(
  'Auditor',
  'Read-only access for audit and compliance purposes',
  '{
    "permissions": [
      "loan_view", "customer_view", "collection_view", "payment_view",
      "user_view", "role_view", "permission_view",
      "report_view_all", "report_export",
      "expense_view", "transaction_view",
      "audit_log_view"
    ],
    "conditions": {},
    "description": "Comprehensive read-only access for audit and compliance activities"
  }',
  'financial_services',
  true
),
(
  'Customer Service Representative',
  'Customer-facing role with limited system access',
  '{
    "permissions": [
      "customer_view", "customer_edit", "customer_contact",
      "loan_view", "collection_view",
      "payment_view", "payment_record",
      "report_view_basic"
    ],
    "conditions": {
      "customer_edit": {
        "type": "session",
        "config": {
          "max_session_age": 480,
          "require_mfa": false
        }
      }
    },
    "description": "Customer service and support with basic account management"
  }',
  'financial_services',
  true
);

-- Insert some example role hierarchies for demonstration
-- Note: These will only work if the referenced roles exist

-- Create a hierarchy: Branch Manager -> Senior Loan Officer -> Loan Officer
-- This will be handled by the application layer to ensure roles exist first

-- Add comments for documentation
COMMENT ON TABLE role_hierarchy IS 'Defines parent-child relationships between roles for inheritance';
COMMENT ON COLUMN role_hierarchy.inheritance_type IS 'How child role inherits from parent: inherit (add permissions), override (replace permissions), deny (remove permissions)';
COMMENT ON TABLE role_templates IS 'Predefined role configurations for quick setup across different industries';
COMMENT ON COLUMN role_templates.template_config IS 'JSON configuration containing permissions, conditions, and metadata for the role template';
COMMENT ON COLUMN role_templates.industry IS 'Industry or sector this template is designed for (e.g., financial_services, healthcare, retail)';

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_role_hierarchy_updated_at 
    BEFORE UPDATE ON role_hierarchy 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_role_templates_updated_at 
    BEFORE UPDATE ON role_templates 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
