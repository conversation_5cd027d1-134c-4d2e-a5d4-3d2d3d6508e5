import { db } from './db';
import { eq, and, between, sql } from 'drizzle-orm';
import { format, parse, subMonths, eachMonthOfInterval } from 'date-fns';
import errorLogger from './utils/errorLogger';
import { transactions, expenses, accounts } from '@shared/schema';
import { SYSTEM_ACCOUNT_CODES } from './config/systemAccounts';

/**
 * Generates a profit and loss report for a company within a specified date range
 * 
 * @param companyId The ID of the company
 * @param startDate Start date in YYYY-MM-DD format
 * @param endDate End date in YYYY-MM-DD format
 * @param branchId Optional branch ID to filter by
 * @returns Promise<ProfitLossReport>
 */
export async function getProfitLossReport(
  companyId: number,
  startDate: string,
  endDate: string,
  branchId?: number
) {
  try {
    // Generating profit/loss report (logging removed for cleaner console)
    
    // Parse dates
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    // Get all transactions for the period
    let transactionsQuery = db.select()
      .from(transactions)
      .where(and(
        eq(transactions.company_id, companyId),
        between(transactions.transaction_date, startDateObj, endDateObj)
      ));
    
    // Add branch filter if provided
    if (branchId) {
      transactionsQuery = transactionsQuery.where(eq(transactions.branch_id, branchId));
    }
    
    const periodTransactions = await transactionsQuery;
    
    // Get all accounts for the company
    const companyAccounts = await db.select()
      .from(accounts)
      .where(eq(accounts.company_id, companyId));
    
    // Filter accounts by type
    const incomeAccounts = companyAccounts.filter(a => a.account_type === 'income');
    const expenseAccounts = companyAccounts.filter(a => a.account_type === 'expense');
    
    // Calculate income totals
    let interestIncome = 0;
    let fineIncome = 0;
    let processingFeeIncome = 0;
    let otherIncome = 0;
    
    // Process income transactions
    for (const transaction of periodTransactions) {
      // Find the account for this transaction
      const account = companyAccounts.find(a => a.id === transaction.account_id);
      
      if (!account) continue;
      
      // Only process credit transactions for income accounts
      if (account.account_type === 'income' && transaction.transaction_type === 'credit') {
        const amount = Number(transaction.amount);
        
        // Categorize by account code
        if (account.account_code === SYSTEM_ACCOUNT_CODES.INTEREST_INCOME) {
          interestIncome += amount;
        } else if (account.account_code === SYSTEM_ACCOUNT_CODES.PENALTY_INCOME) {
          fineIncome += amount;
        } else if (account.account_code === SYSTEM_ACCOUNT_CODES.PROCESSING_FEE_INCOME) {
          processingFeeIncome += amount;
        } else {
          otherIncome += amount;
        }
      }
    }
    
    // Calculate total income
    const totalIncome = interestIncome + fineIncome + processingFeeIncome + otherIncome;
    
    // Calculate expense breakdown
    const expenseCategories = new Map();
    
    // Process expense transactions
    for (const transaction of periodTransactions) {
      // Find the account for this transaction
      const account = companyAccounts.find(a => a.id === transaction.account_id);
      
      if (!account) continue;
      
      // Only process debit transactions for expense accounts
      if (account.account_type === 'expense' && transaction.transaction_type === 'debit') {
        const amount = Number(transaction.amount);
        const category = account.account_name;
        
        if (expenseCategories.has(category)) {
          expenseCategories.set(category, expenseCategories.get(category) + amount);
        } else {
          expenseCategories.set(category, amount);
        }
      }
    }
    
    // Convert expense categories to array format
    const expenseBreakdown = Array.from(expenseCategories.entries()).map(([category, amount]) => ({
      category,
      amount: Number(amount)
    }));
    
    // Calculate total expenses
    const totalExpenses = expenseBreakdown.reduce((sum, item) => sum + item.amount, 0);
    
    // Calculate net profit
    const netProfit = totalIncome - totalExpenses;
    
    // Generate monthly data for the chart
    const monthlyData = generateMonthlyData(
      startDateObj,
      endDateObj,
      periodTransactions,
      companyAccounts
    );
    
    return {
      startDate,
      endDate,
      totalIncome,
      totalExpenses,
      netProfit,
      incomeBreakdown: {
        interestIncome,
        fineIncome,
        processingFeeIncome,
        otherIncome
      },
      expenseBreakdown,
      monthlyData
    };
  } catch (error) {
    errorLogger.logError(`Failed to generate profit/loss report for company ${companyId}`, 'profit-loss-report', error as Error);
    
    // Return empty report structure on error
    return {
      startDate,
      endDate,
      totalIncome: 0,
      totalExpenses: 0,
      netProfit: 0,
      incomeBreakdown: {
        interestIncome: 0,
        fineIncome: 0,
        processingFeeIncome: 0,
        otherIncome: 0
      },
      expenseBreakdown: [],
      monthlyData: []
    };
  }
}

/**
 * Generates monthly data for the profit/loss chart
 */
function generateMonthlyData(
  startDate: Date,
  endDate: Date,
  transactions: any[],
  accounts: any[]
) {
  // Get all months in the date range
  const months = eachMonthOfInterval({ start: startDate, end: endDate });
  
  return months.map(month => {
    const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
    const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0);
    
    // Filter transactions for this month
    const monthTransactions = transactions.filter(t => {
      const txDate = new Date(t.transaction_date);
      return txDate >= monthStart && txDate <= monthEnd;
    });
    
    // Calculate income and expenses for this month
    let monthIncome = 0;
    let monthExpenses = 0;
    
    for (const transaction of monthTransactions) {
      const account = accounts.find(a => a.id === transaction.account_id);
      if (!account) continue;
      
      const amount = Number(transaction.amount);
      
      if (account.account_type === 'income' && transaction.transaction_type === 'credit') {
        monthIncome += amount;
      } else if (account.account_type === 'expense' && transaction.transaction_type === 'debit') {
        monthExpenses += amount;
      }
    }
    
    return {
      month: format(month, 'MMM yyyy'),
      income: monthIncome,
      expenses: monthExpenses,
      profit: monthIncome - monthExpenses
    };
  });
}
