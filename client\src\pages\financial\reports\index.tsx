import React, { useState } from 'react';
import { subMonths } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { BarChart3, LineChart, TrendingDown, TrendingUp, AlertCircle } from 'lucide-react';
import { useContextData } from '@/lib/useContextData';

// Import report components
import { ProfitLossReport } from './components/ProfitLossReport';
import { BalanceSheetReport } from './components/BalanceSheetReport';
import { CashFlowReport } from './components/CashFlowReport';
import { AccountsReport } from './components/AccountsReport';

// Import types
import { ReportTabType } from './types';

export default function FinancialReports() {
  // Get authentication and company context
  const { isAuthenticated, companyId } = useContextData();

  // Tab state
  const [activeTab, setActiveTab] = useState<ReportTabType>('profit-loss');

  // Date range state for reports that use date ranges
  const [dateRange, setDateRange] = useState<{
    from: Date;
    to: Date;
  }>({
    from: subMonths(new Date(), 3),
    to: new Date(),
  });

  // Balance sheet date state
  const [balanceSheetDate, setBalanceSheetDate] = useState<Date>(new Date());

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Financial Reports</CardTitle>
          <CardDescription>
            View and analyze detailed financial reports for your company
          </CardDescription>
          <div className="flex justify-end mt-2">
            <Button variant="link" asChild>
              <a href="/test/trial-balance" target="_blank" rel="noopener noreferrer">
                <BarChart3 className="mr-2 h-4 w-4" />
                Trial Balance Report
              </a>
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs
            defaultValue="profit-loss"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as ReportTabType)}
            className="w-full"
          >
            <TabsList className="mb-6 w-full grid grid-cols-2 md:grid-cols-4">
              <TabsTrigger value="profit-loss">
                <TrendingUp className="h-4 w-4 mr-2" />
                Profit & Loss
              </TabsTrigger>
              <TabsTrigger value="balance-sheet">
                <BarChart3 className="h-4 w-4 mr-2" />
                Balance Sheet
              </TabsTrigger>
              <TabsTrigger value="cash-flow">
                <TrendingDown className="h-4 w-4 mr-2" />
                Cash Flow
              </TabsTrigger>
              <TabsTrigger value="accounts">
                <LineChart className="h-4 w-4 mr-2" />
                Accounts
              </TabsTrigger>
            </TabsList>

            {/* Profit & Loss Report */}
            <TabsContent value="profit-loss">
              <ProfitLossReport
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
              />
            </TabsContent>

            {/* Balance Sheet Report */}
            <TabsContent value="balance-sheet">
              <BalanceSheetReport
                date={balanceSheetDate}
                onDateChange={setBalanceSheetDate}
              />
            </TabsContent>

            {/* Cash Flow Report */}
            <TabsContent value="cash-flow">
              <CashFlowReport
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
              />
            </TabsContent>

            {/* Accounts Report */}
            <TabsContent value="accounts">
              <AccountsReport
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
