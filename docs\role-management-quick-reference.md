# Role Management System - Quick Reference Guide

## Business Rules Summary

| Rule | Description | Error Code |
|------|-------------|------------|
| **Owner Exclusivity** | Owner role cannot be combined with other roles | `OWNER_ROLE_EXCLUSIVITY` |
| **Self-Modification Prevention** | Users cannot modify their own role assignments | `SELF_ROLE_MODIFICATION_DENIED` |
| **Owner Protection** | Owner roles protected from unauthorized changes | `OWNER_ROLE_PROTECTION` |

## API Endpoints

### Get User Roles
```http
GET /api/users/{id}/roles
```
**Response**: Array of role objects with legacy support
```json
[
  {
    "id": 20,
    "name": "Employee",
    "description": "Standard employee access",
    "is_system": true,
    "source": "legacy",
    "assignment_id": null
  }
]
```

### Update User Roles
```http
PUT /api/users/{id}/roles
Content-Type: application/json

{
  "roleIds": [20, 12]
}
```

**Success Response**: `200 OK`
**Error Responses**: 
- `400` - Owner exclusivity violation
- `403` - Self-modification or Owner protection violation

## Frontend Component Usage

### Key Props and State
```typescript
const isSelfEditing = currentUser?.id === userId;
const isCurrentUserOwner = currentUser?.role === 'owner' || currentUser?.role === 'saas_admin';
```

### Helper Functions
```typescript
// Check if target user is an Owner
const isTargetUserOwner = (user, userRoles) => {
  return user?.role === 'owner' || userRoles?.some(role => role.name === 'Owner');
};

// Determine if role should be disabled
const shouldDisableRole = (roleId, roles) => {
  if (isSelfEditing) return true;
  if (isOwnerSelected && roleId !== ownerRoleId) return true;
  if (isOwnerRole && isTargetOwner && !isCurrentUserOwner) return true;
  return false;
};
```

### Role Change Handler Pattern
```typescript
const handleRoleChange = (roleId, checked) => {
  const ownerRoleId = getOwnerRoleId(allRoles);
  
  setSelectedRoleIds(prev => {
    if (checked) {
      // Owner exclusivity logic
      if (roleId === ownerRoleId) return [roleId];
      if (prev.includes(ownerRoleId)) return [roleId];
      return [...prev, roleId];
    } else {
      return prev.filter(id => id !== roleId);
    }
  });
};
```

## UI Components and Styling

### Warning Alerts
```jsx
// Self-editing restriction
<Alert className="mb-6">
  <Lock className="h-4 w-4" />
  <AlertDescription>
    <strong>Read-Only Mode:</strong> You cannot modify your own role assignments.
  </AlertDescription>
</Alert>

// Owner protection
<Alert className="mb-6">
  <Shield className="h-4 w-4" />
  <AlertDescription>
    <strong>Owner Protection:</strong> Owner role assignments cannot be modified.
  </AlertDescription>
</Alert>

// Owner exclusivity
<Alert className="mb-6">
  <AlertTriangle className="h-4 w-4" />
  <AlertDescription>
    <strong>Owner Role Exclusivity:</strong> Owner role cannot be combined with other roles.
  </AlertDescription>
</Alert>
```

### Disabled Role Styling
```jsx
<div className={`role-container ${isDisabled ? 'opacity-50 bg-muted/30' : ''}`}>
  <Checkbox disabled={isDisabled} />
  <Label className={isDisabled ? 'cursor-not-allowed opacity-70' : ''}>
    {role.name}
    {isDisabled && isOwnerRole && <Lock className="inline h-3 w-3 ml-1" />}
  </Label>
  {isDisabled && isOwnerRole && (
    <Badge variant="outline">Protected</Badge>
  )}
</div>
```

## Database Schema Integration

### Legacy Role Support
```sql
-- Users table (existing)
users.role ENUM('owner', 'employee', 'collection_agent', 'system_admin')

-- Custom roles table
custom_roles (id, name, description, is_system, company_id)

-- User role assignments
user_roles (id, user_id, role_id, created_at)
```

### Role Source Types
- `legacy`: From `users.role` enum column
- `direct`: From `user_roles` table
- `group`: From group membership inheritance

## Testing Checklist

### Backend API Tests
- [ ] Owner + other roles → `400 OWNER_ROLE_EXCLUSIVITY`
- [ ] Self-modification → `403 SELF_ROLE_MODIFICATION_DENIED`
- [ ] Non-owner modifying Owner → `403 OWNER_ROLE_PROTECTION`
- [ ] Valid role assignment → `200 OK`
- [ ] Legacy role detection working
- [ ] Role source tracking accurate

### Frontend UI Tests
- [ ] Self-editing shows read-only mode
- [ ] Owner selection deselects other roles
- [ ] Disabled roles show proper styling
- [ ] Warning alerts display correctly
- [ ] Save button states work properly
- [ ] Legacy roles display with source info

### User Experience Tests
- [ ] Clear error messages for violations
- [ ] Intuitive role selection behavior
- [ ] Proper visual feedback for restrictions
- [ ] Accessible interface for disabled states
- [ ] Responsive design on different screen sizes

## Common Issues and Solutions

### Issue: Legacy roles not showing
**Solution**: Check that enum role mapping is working in API endpoint

### Issue: Owner role not being protected
**Solution**: Verify `isCurrentUserOwner` logic includes both 'owner' and 'saas_admin'

### Issue: Self-editing not detected
**Solution**: Ensure `currentUser.id` comparison uses correct data types (number vs string)

### Issue: Role deselection not working
**Solution**: Check `handleRoleChange` logic for Owner exclusivity

### Issue: Visual indicators not showing
**Solution**: Verify `shouldDisableRole` function returns correct boolean values

## File Locations

### Backend
- **Main API**: `server/routes/user-role.routes.ts`
- **Database Schema**: `server/db/schema.ts`
- **Auth Middleware**: `server/middleware/auth.ts`

### Frontend
- **Main Component**: `client/src/pages/user-management/users/[id]/roles.tsx`
- **Auth Context**: `client/src/components/auth/auth-context.tsx`
- **UI Components**: `client/src/components/ui/`

### Documentation
- **Full Documentation**: `docs/role-assignment-restrictions.md`
- **Quick Reference**: `docs/role-management-quick-reference.md`

## Environment Variables

No additional environment variables required. Uses existing database and authentication configuration.

## Dependencies

### Backend
- `drizzle-orm` - Database ORM
- `express` - Web framework
- Existing auth middleware

### Frontend
- `@tanstack/react-query` - Data fetching
- `wouter` - Routing
- `lucide-react` - Icons
- Existing UI component library

---

**Quick Reference Version**: 1.0  
**Last Updated**: December 2024  
**For Full Documentation**: See `docs/role-assignment-restrictions.md`
