import { accountTypeEnum } from '@shared/schema';

/**
 * Default system accounts configuration
 * These accounts are automatically created for each new company
 * and are protected from deletion or renaming
 * 
 * Chart of Accounts Structure:
 * 1000-1999: Assets
 * 2000-2999: Liabilities
 * 3000-3999: Equity
 * 4000-4999: Income/Revenue
 * 5000-5999: Expenses
 */
export const DEFAULT_SYSTEM_ACCOUNTS = [
  // Asset accounts - 1000 series
  {
    account_code: "1000",
    account_name: "Cash",
    account_type: "asset" as const,
    description: "Physical currency held on premises",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1010",
    account_name: "Bank",
    account_type: "asset" as const,
    description: "Funds held in checking and savings accounts",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1020",
    account_name: "Petty Cash",
    account_type: "asset" as const,
    description: "Small cash fund for minor expenses",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1100",
    account_name: "Accounts Receivable",
    account_type: "asset" as const,
    description: "Amounts owed to the company",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1200",
    account_name: "Loans Receivable",
    account_type: "asset" as const,
    description: "Principal amounts of loans outstanding",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1300",
    account_name: "Interest Receivable",
    account_type: "asset" as const,
    description: "Accrued interest on loans not yet received",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1400",
    account_name: "Prepaid Expenses",
    account_type: "asset" as const,
    description: "Expenses paid in advance",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1500",
    account_name: "Office Equipment",
    account_type: "asset" as const,
    description: "Physical equipment used in operations",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1510",
    account_name: "Accumulated Depreciation - Equipment",
    account_type: "asset" as const,
    description: "Accumulated depreciation of equipment assets",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1600",
    account_name: "Vehicles",
    account_type: "asset" as const,
    description: "Company-owned vehicles",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "1610",
    account_name: "Accumulated Depreciation - Vehicles",
    account_type: "asset" as const,
    description: "Accumulated depreciation of vehicle assets",
    is_system: true,
    is_active: true,
  },
  
  // Liability accounts - 2000 series
  {
    account_code: "2000",
    account_name: "Accounts Payable",
    account_type: "liability" as const,
    description: "Amounts owed to vendors and suppliers",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "2100",
    account_name: "Loans Payable",
    account_type: "liability" as const,
    description: "Borrowed funds owed to lenders",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "2200",
    account_name: "Interest Payable",
    account_type: "liability" as const,
    description: "Interest owed but not yet paid",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "2300",
    account_name: "Salaries Payable",
    account_type: "liability" as const,
    description: "Salaries and wages owed to employees",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "2400",
    account_name: "Customer Deposits",
    account_type: "liability" as const,
    description: "Funds received from customers in advance",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "2500",
    account_name: "Taxes Payable",
    account_type: "liability" as const,
    description: "Tax amounts owed to government authorities",
    is_system: true,
    is_active: true,
  },
  
  // Equity accounts - 3000 series
  {
    account_code: "3000",
    account_name: "Owner's Equity",
    account_type: "equity" as const,
    description: "Owner's investment in the business",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "3100",
    account_name: "Retained Earnings",
    account_type: "equity" as const,
    description: "Accumulated profits reinvested in the business",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "3200",
    account_name: "Capital Contributions",
    account_type: "equity" as const,
    description: "Additional investments by owners",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "3300",
    account_name: "Dividends Paid",
    account_type: "equity" as const,
    description: "Distributions to shareholders/owners",
    is_system: true,
    is_active: true,
  },
  
  // Income accounts - 4000 series
  {
    account_code: "4000",
    account_name: "Interest Income",
    account_type: "income" as const,
    description: "Income from loan interest payments",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "4100",
    account_name: "Commission Income",
    account_type: "income" as const,
    description: "Income from commissions earned",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "4200",
    account_name: "Penalty Income",
    account_type: "income" as const,
    description: "Income from late payment penalties",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "4300",
    account_name: "Fee Income",
    account_type: "income" as const,
    description: "Income from service fees",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "4400",
    account_name: "Processing Fee Income",
    account_type: "income" as const,
    description: "Income from loan processing fees",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "4900",
    account_name: "Other Income",
    account_type: "income" as const,
    description: "Miscellaneous income sources",
    is_system: true,
    is_active: true,
  },
  
  // Expense accounts - 5000 series
  {
    account_code: "5000",
    account_name: "Rent Expense",
    account_type: "expense" as const,
    description: "Expenses for rented facilities",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5100",
    account_name: "Salary Expense",
    account_type: "expense" as const,
    description: "Employee salaries and wages",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5200",
    account_name: "Utilities Expense",
    account_type: "expense" as const,
    description: "Electricity, water, internet, etc.",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5300",
    account_name: "Office Supplies Expense",
    account_type: "expense" as const,
    description: "Consumable office materials",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5400",
    account_name: "Transport Expense",
    account_type: "expense" as const,
    description: "Transportation and travel costs",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5500",
    account_name: "Marketing Expense",
    account_type: "expense" as const,
    description: "Advertising and promotion costs",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5600",
    account_name: "Insurance Expense",
    account_type: "expense" as const,
    description: "Business insurance premiums",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5700",
    account_name: "Depreciation Expense",
    account_type: "expense" as const,
    description: "Allocation of asset costs over useful life",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5800",
    account_name: "Bad Debt Expense",
    account_type: "expense" as const,
    description: "Losses from uncollectible loans",
    is_system: true,
    is_active: true,
  },
  {
    account_code: "5900",
    account_name: "Miscellaneous Expense",
    account_type: "expense" as const,
    description: "Expenses not categorized elsewhere",
    is_system: true,
    is_active: true,
  },
];

/**
 * System account codes to use for specific operations
 * These are used to automatically select the appropriate accounts for transactions
 */
export const SYSTEM_ACCOUNT_CODES = {
  // Asset accounts
  CASH: "1000",
  BANK: "1010",
  PETTY_CASH: "1020",
  ACCOUNTS_RECEIVABLE: "1100",
  LOAN_RECEIVABLE: "1200",
  INTEREST_RECEIVABLE: "1300",
  PREPAID_EXPENSES: "1400",
  OFFICE_EQUIPMENT: "1500",
  EQUIPMENT_DEPRECIATION: "1510",
  VEHICLES: "1600",
  VEHICLE_DEPRECIATION: "1610",
  
  // Liability accounts
  ACCOUNTS_PAYABLE: "2000",
  LOANS_PAYABLE: "2100",
  INTEREST_PAYABLE: "2200",
  SALARIES_PAYABLE: "2300",
  CUSTOMER_DEPOSITS: "2400",
  TAXES_PAYABLE: "2500",
  
  // Equity accounts
  OWNERS_EQUITY: "3000",
  RETAINED_EARNINGS: "3100",
  CAPITAL_CONTRIBUTIONS: "3200",
  DIVIDENDS_PAID: "3300",
  
  // Income accounts
  INTEREST_INCOME: "4000",
  COMMISSION_INCOME: "4100",
  PENALTY_INCOME: "4200",
  FEE_INCOME: "4300",
  PROCESSING_FEE_INCOME: "4400",
  OTHER_INCOME: "4900",
  
  // Expense accounts
  RENT_EXPENSE: "5000",
  SALARY_EXPENSE: "5100",
  UTILITIES_EXPENSE: "5200",
  OFFICE_SUPPLIES_EXPENSE: "5300",
  TRANSPORT_EXPENSE: "5400",
  MARKETING_EXPENSE: "5500",
  INSURANCE_EXPENSE: "5600",
  DEPRECIATION_EXPENSE: "5700",
  BAD_DEBT_EXPENSE: "5800",
  GENERAL_EXPENSES: "5900",
};