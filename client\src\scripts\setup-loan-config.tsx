import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Check, Loader2 } from "lucide-react";
import { useCompany } from "@/lib/companies";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from '@/lib/queryClient';

// This component is used to set up the loan configurations for a company
// It now directly links form templates without loan type categorization
export default function SetupLoanConfig() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { currentCompany } = useCompany();
  const companyId = currentCompany?.company_id;
  const [setupComplete, setSetupComplete] = useState(false);

  // Fetch form templates
  const { data: formTemplates = [], isLoading: isLoadingTemplates } = useQuery<any[]>({
    queryKey: [`/api/companies/${companyId}/form-templates`],
    enabled: !!companyId,
  });

  // Fetch existing loan configurations
  const { data: existingConfigs = [], isLoading: isLoadingConfigs } = useQuery<any[]>({
    queryKey: [`/api/companies/${companyId}/loan-configurations`],
    enabled: !!companyId,
  });

  const createConfigMutation = useMutation({
    mutationFn: async (templateId: number) => {
      const response = await apiRequest('POST', `/api/companies/${companyId}/loan-configurations`, {
        company_id: companyId,
        template_id: templateId,
        is_active: true,
        order: 0
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create loan configuration: ${response.statusText}`);
      }
      
      return await response.json();
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ 
        queryKey: [`/api/companies/${companyId}/loan-configurations`] 
      });
      
      toast({
        title: "Configuration created",
        description: "Loan configuration has been created successfully.",
      });
      
      setSetupComplete(true);
    },
    onError: (error) => {
      toast({
        title: "Error creating configuration",
        description: "There was a problem creating the loan configuration.",
        variant: "destructive"
      });
      console.error("Error creating loan configuration:", error);
    }
  });

  const handleSetupConfig = async () => {
    // Find the first active template
    const activeTemplate = formTemplates?.find(t => t.is_active);
    
    if (activeTemplate) {
      createConfigMutation.mutate(activeTemplate.id);
    } else {
      toast({
        title: "No active templates",
        description: "Please create and activate a form template first.",
        variant: "destructive"
      });
    }
  };

  // Check if we need to set up configurations
  const needsSetup = !isLoadingConfigs && 
                     (!existingConfigs || existingConfigs.length === 0) && 
                     formTemplates && 
                     formTemplates.length > 0;

  return (
    <Card className="w-full max-w-md mx-auto mt-8">
      <CardHeader>
        <CardTitle>Loan Configuration Setup</CardTitle>
        <CardDescription>
          Set up your loan templates for use in loan creation
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoadingTemplates || isLoadingConfigs ? (
          <div className="flex justify-center p-4">
            <Loader2 className="w-6 h-6 animate-spin text-primary" />
          </div>
        ) : needsSetup ? (
          <Alert className="mb-4 bg-yellow-50 border-yellow-200">
            <AlertCircle className="h-4 w-4 text-yellow-600" />
            <AlertTitle className="text-yellow-600">Configuration needed</AlertTitle>
            <AlertDescription>
              You currently have no loan configurations. 
              This will link your form template for direct loan creation.
            </AlertDescription>
          </Alert>
        ) : setupComplete ? (
          <Alert className="bg-green-50 border-green-200">
            <Check className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-600">Setup Complete</AlertTitle>
            <AlertDescription>
              Loan configuration has been created successfully.
            </AlertDescription>
          </Alert>
        ) : (
          <Alert className="bg-blue-50 border-blue-200">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertTitle className="text-blue-600">Configuration exists</AlertTitle>
            <AlertDescription>
              You already have loan configurations set up.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      {needsSetup && (
        <CardFooter className="flex justify-end">
          <Button 
            onClick={handleSetupConfig}
            disabled={createConfigMutation.isPending}
          >
            {createConfigMutation.isPending && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Set up loan configuration
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}