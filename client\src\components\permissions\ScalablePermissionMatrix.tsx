import React, { useState, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Shield,
  Users,
  CreditCard,
  DollarSign,
  BarChart,
  UserCheck,
  Building,
  Settings,
  Lock,
  ArrowDown,
  ArrowRight,
  Clock,
  AlertTriangle,
  Info,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight,
  Grid,
  List
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  permissions?: number[];
  parent_roles?: number[];
  child_roles?: number[];
  hierarchy_depth?: number;
  effective_permissions?: number[];
  inherited_permissions?: number[];
  temporary_permissions?: TemporaryPermission[];
}

interface TemporaryPermission {
  id: number;
  permission_id: number;
  granted_by: number;
  granted_at: string;
  expires_at: string;
  priority: 'low' | 'medium' | 'high' | 'emergency';
  reason?: string;
  is_active: boolean;
}

interface RoleHierarchy {
  role_id: number;
  parent_role_id: number;
  inheritance_type: 'inherit' | 'override' | 'deny';
}

interface PermissionCategory {
  category: string;
  metadata: {
    name: string;
    description: string;
    icon: string;
  };
  permissions: Permission[];
}

interface ScalablePermissionMatrixProps {
  roles: Role[];
  permissionCategories: PermissionCategory[];
  roleHierarchies?: RoleHierarchy[];
  onPermissionChange: (roleId: number, permissionId: number, granted: boolean) => Promise<void>;
  onBulkAssign?: (assignments: Array<{role_id: number, permission_ids: number[], action: 'grant' | 'revoke'}>) => Promise<void>;
  loading?: boolean;
  userExperienceLevel?: 'basic' | 'advanced' | 'expert';
}

const categoryIcons: Record<string, React.ComponentType<any>> = {
  CreditCard,
  Users,
  DollarSign,
  BarChart,
  UserCheck,
  Shield,
  Building,
  Settings,
  Lock
};

export function ScalablePermissionMatrix({
  roles,
  permissionCategories,
  roleHierarchies = [],
  onPermissionChange,
  onBulkAssign,
  loading = false,
  userExperienceLevel = 'basic'
}: ScalablePermissionMatrixProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'role-focused'>('grid');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [isUpdating, setIsUpdating] = useState(false);
  const [showOnlyGranted, setShowOnlyGranted] = useState(false);

  // Initialize expanded categories
  useEffect(() => {
    if (permissionCategories.length > 0) {
      setExpandedCategories(new Set(permissionCategories.map(cat => cat.category)));
    }
  }, [permissionCategories]);

  // Auto-select first role for role-focused view
  useEffect(() => {
    if (viewMode === 'role-focused' && roles.length > 0 && !selectedRole) {
      setSelectedRole(roles[0].id);
    }
  }, [viewMode, roles, selectedRole]);

  const handlePermissionToggle = async (roleId: number, permissionId: number, currentlyGranted: boolean) => {
    setIsUpdating(true);
    try {
      await onPermissionChange(roleId, permissionId, !currentlyGranted);
      toast({
        title: "Permission Updated",
        description: `Permission ${currentlyGranted ? 'revoked from' : 'granted to'} role successfully.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update permission. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const isPermissionGranted = (role: Role, permissionId: number): boolean => {
    const directPermissions = role.permissions || [];
    const inheritedPermissions = role.inherited_permissions || [];
    const temporaryPermissions = role.temporary_permissions?.filter(tp => 
      tp.is_active && new Date(tp.expires_at) > new Date()
    ).map(tp => tp.permission_id) || [];
    
    return [...directPermissions, ...inheritedPermissions, ...temporaryPermissions].includes(permissionId);
  };

  const getPermissionSource = (role: Role, permissionId: number): 'direct' | 'inherited' | 'temporary' | 'none' => {
    if (role.permissions?.includes(permissionId)) return 'direct';
    if (role.inherited_permissions?.includes(permissionId)) return 'inherited';
    if (role.temporary_permissions?.some(tp =>
      tp.permission_id === permissionId &&
      tp.is_active &&
      new Date(tp.expires_at) > new Date()
    )) return 'temporary';
    return 'none';
  };

  const getCategoryIcon = (iconName: string) => {
    const IconComponent = categoryIcons[iconName] || Lock;
    return <IconComponent className="h-4 w-4" />;
  };

  // Filter permissions based on search and category
  const filteredCategories = permissionCategories.filter(category => {
    if (selectedCategory && category.category !== selectedCategory) return false;
    if (searchTerm) {
      return category.permissions.some(permission =>
        permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.code.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    return true;
  }).map(category => ({
    ...category,
    permissions: category.permissions.filter(permission => {
      if (searchTerm) {
        const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          permission.code.toLowerCase().includes(searchTerm.toLowerCase());
        if (!matchesSearch) return false;
      }
      
      if (showOnlyGranted && selectedRole) {
        const role = roles.find(r => r.id === selectedRole);
        return role ? isPermissionGranted(role, permission.id) : false;
      }
      
      return true;
    })
  }));

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              {userExperienceLevel === 'basic' ? 'User Permissions' : 'Permission Matrix'}
            </h2>
            <p className="text-muted-foreground">
              {userExperienceLevel === 'basic' 
                ? 'Control what each user can access in your system'
                : 'Manage role permissions with advanced controls'
              }
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              {roles.length} Roles • {permissionCategories.reduce((acc, cat) => acc + cat.permissions.length, 0)} Permissions
            </Badge>
          </div>
        </div>

        {/* View Mode Controls */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">View:</label>
                  <div className="flex gap-1">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="flex items-center gap-1"
                    >
                      <Grid className="h-3 w-3" />
                      {roles.length <= 4 ? 'Grid' : 'Compact'}
                    </Button>
                    <Button
                      variant={viewMode === 'role-focused' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('role-focused')}
                      className="flex items-center gap-1"
                    >
                      <Users className="h-3 w-3" />
                      By Role
                    </Button>
                    {userExperienceLevel !== 'basic' && (
                      <Button
                        variant={viewMode === 'list' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setViewMode('list')}
                        className="flex items-center gap-1"
                      >
                        <List className="h-3 w-3" />
                        List
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search permissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {viewMode === 'role-focused' && (
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Role:</label>
              <select 
                value={selectedRole || ''} 
                onChange={(e) => setSelectedRole(Number(e.target.value))}
                className="text-sm border rounded px-2 py-1 min-w-[150px]"
              >
                {roles.map(role => (
                  <option key={role.id} value={role.id}>{role.name}</option>
                ))}
              </select>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={showOnlyGranted}
                  onCheckedChange={setShowOnlyGranted}
                />
                <label className="text-sm">Only granted</label>
              </div>
            </div>
          )}

          <div className="flex gap-2">
            <Button
              variant={selectedCategory ? "outline" : "default"}
              onClick={() => setSelectedCategory(null)}
              size="sm"
            >
              All Categories
            </Button>
            {permissionCategories.slice(0, 3).map((category) => (
              <Button
                key={category.category}
                variant={selectedCategory === category.category ? "default" : "outline"}
                onClick={() => setSelectedCategory(
                  selectedCategory === category.category ? null : category.category
                )}
                size="sm"
                className="hidden sm:flex"
              >
                {getCategoryIcon(category.metadata.icon)}
                <span className="ml-1">{category.metadata.name}</span>
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Permission Matrix Content */}
      {viewMode === 'grid' && roles.length <= 4 ? (
        // Traditional grid view for small number of roles
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Permission Assignment Grid
            </CardTitle>
            <CardDescription>
              Click checkboxes to grant or revoke permissions for each role
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px] w-full">
              <div className="space-y-6">
                {filteredCategories.map((category) => (
                  <div key={category.category} className="space-y-3">
                    {/* Category Header */}
                    <div
                      className="flex items-center justify-between p-3 bg-muted/50 rounded-lg cursor-pointer hover:bg-muted/70 transition-colors"
                      onClick={() => toggleCategory(category.category)}
                    >
                      <div className="flex items-center gap-3">
                        {getCategoryIcon(category.metadata.icon)}
                        <div>
                          <h3 className="font-semibold">{category.metadata.name}</h3>
                          <p className="text-sm text-muted-foreground">{category.metadata.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          {category.permissions.length} permissions
                        </Badge>
                        {expandedCategories.has(category.category) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </div>

                    {/* Permissions Table */}
                    {expandedCategories.has(category.category) && (
                      <div className="border rounded-lg overflow-hidden">
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead className="bg-muted/30">
                              <tr>
                                <th className="text-left p-3 font-medium min-w-[300px]">Permission</th>
                                {roles.map(role => (
                                  <th key={role.id} className="text-center p-3 font-medium min-w-[140px]">
                                    <div className="flex flex-col items-center gap-1">
                                      <span className="text-sm font-medium">{role.name}</span>
                                      {role.is_system && (
                                        <Badge variant="outline" className="text-xs">System</Badge>
                                      )}
                                    </div>
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {category.permissions.map((permission, index) => (
                                <tr
                                  key={permission.id}
                                  className={`border-t hover:bg-muted/20 ${index % 2 === 0 ? 'bg-background' : 'bg-muted/10'}`}
                                >
                                  <td className="p-3">
                                    <div className="space-y-1">
                                      <div className="font-medium">{permission.name}</div>
                                      <div className="text-sm text-muted-foreground">{permission.description}</div>
                                      {userExperienceLevel !== 'basic' && (
                                        <code className="text-xs bg-muted px-2 py-1 rounded">{permission.code}</code>
                                      )}
                                    </div>
                                  </td>
                                  {roles.map(role => {
                                    const isGranted = isPermissionGranted(role, permission.id);
                                    const source = getPermissionSource(role, permission.id);

                                    return (
                                      <td key={role.id} className="p-3 text-center">
                                        <div className="flex flex-col items-center gap-1">
                                          <div className="relative">
                                            <Checkbox
                                              checked={isGranted}
                                              onCheckedChange={(checked) =>
                                                handlePermissionToggle(role.id, permission.id, isGranted)
                                              }
                                              disabled={loading || isUpdating || source === 'inherited'}
                                              className={`mx-auto ${
                                                source === 'direct' ? 'border-blue-500' :
                                                source === 'inherited' ? 'border-green-500' :
                                                source === 'temporary' ? 'border-orange-500' :
                                                ''
                                              }`}
                                            />
                                            {userExperienceLevel !== 'basic' && source !== 'none' && source !== 'direct' && (
                                              <div className="absolute -top-1 -right-1">
                                                {source === 'inherited' && (
                                                  <TooltipProvider>
                                                    <Tooltip>
                                                      <TooltipTrigger>
                                                        <ArrowDown className="h-3 w-3 text-green-600" />
                                                      </TooltipTrigger>
                                                      <TooltipContent>
                                                        <p>Inherited from parent role</p>
                                                      </TooltipContent>
                                                    </Tooltip>
                                                  </TooltipProvider>
                                                )}
                                                {source === 'temporary' && (
                                                  <TooltipProvider>
                                                    <Tooltip>
                                                      <TooltipTrigger>
                                                        <Clock className="h-3 w-3 text-orange-600" />
                                                      </TooltipTrigger>
                                                      <TooltipContent>
                                                        <p>Temporary permission</p>
                                                      </TooltipContent>
                                                    </Tooltip>
                                                  </TooltipProvider>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      </td>
                                    );
                                  })}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      ) : viewMode === 'role-focused' ? (
        // Role-focused view for scalability
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Role Selection Sidebar */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Roles
              </CardTitle>
              <CardDescription>
                Select a role to manage its permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px]">
                <div className="space-y-2">
                  {roles.map(role => {
                    const isSelected = selectedRole === role.id;
                    const grantedCount = role.permissions?.length || 0;
                    const totalPermissions = permissionCategories.reduce((acc, cat) => acc + cat.permissions.length, 0);

                    return (
                      <div
                        key={role.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                        onClick={() => setSelectedRole(role.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{role.name}</div>
                            <div className="text-sm text-muted-foreground">{role.description}</div>
                            <div className="flex items-center gap-2 mt-1">
                              {role.is_system && (
                                <Badge variant="outline" className="text-xs">System</Badge>
                              )}
                              <span className="text-xs text-muted-foreground">
                                {grantedCount}/{totalPermissions} permissions
                              </span>
                            </div>
                          </div>
                          {isSelected && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Permission Management */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Permissions for {roles.find(r => r.id === selectedRole)?.name}
              </CardTitle>
              <CardDescription>
                Manage permissions for the selected role
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedRole && (
                <ScrollArea className="h-[500px]">
                  <div className="space-y-4">
                    {filteredCategories.map((category) => (
                      <div key={category.category} className="space-y-3">
                        {/* Category Header */}
                        <div
                          className="flex items-center justify-between p-3 bg-muted/50 rounded-lg cursor-pointer hover:bg-muted/70 transition-colors"
                          onClick={() => toggleCategory(category.category)}
                        >
                          <div className="flex items-center gap-3">
                            {getCategoryIcon(category.metadata.icon)}
                            <div>
                              <h3 className="font-semibold">{category.metadata.name}</h3>
                              <p className="text-sm text-muted-foreground">{category.metadata.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">
                              {category.permissions.filter(p => {
                                const role = roles.find(r => r.id === selectedRole);
                                return role ? isPermissionGranted(role, p.id) : false;
                              }).length}/{category.permissions.length}
                            </Badge>
                            {expandedCategories.has(category.category) ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </div>
                        </div>

                        {/* Permissions List */}
                        {expandedCategories.has(category.category) && (
                          <div className="space-y-2 pl-4">
                            {category.permissions.map((permission) => {
                              const role = roles.find(r => r.id === selectedRole);
                              if (!role) return null;

                              const isGranted = isPermissionGranted(role, permission.id);
                              const source = getPermissionSource(role, permission.id);

                              return (
                                <div
                                  key={permission.id}
                                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/20 transition-colors"
                                >
                                  <div className="flex-1">
                                    <div className="flex items-center gap-3">
                                      <Checkbox
                                        checked={isGranted}
                                        onCheckedChange={(checked) =>
                                          handlePermissionToggle(role.id, permission.id, isGranted)
                                        }
                                        disabled={loading || isUpdating || source === 'inherited'}
                                        className={`${
                                          source === 'direct' ? 'border-blue-500' :
                                          source === 'inherited' ? 'border-green-500' :
                                          source === 'temporary' ? 'border-orange-500' :
                                          ''
                                        }`}
                                      />
                                      <div>
                                        <div className="font-medium">{permission.name}</div>
                                        <div className="text-sm text-muted-foreground">{permission.description}</div>
                                        {userExperienceLevel !== 'basic' && (
                                          <code className="text-xs bg-muted px-2 py-1 rounded mt-1 inline-block">
                                            {permission.code}
                                          </code>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {userExperienceLevel !== 'basic' && source !== 'none' && source !== 'direct' && (
                                      <div className="flex items-center gap-1">
                                        {source === 'inherited' && (
                                          <TooltipProvider>
                                            <Tooltip>
                                              <TooltipTrigger>
                                                <Badge variant="secondary" className="text-xs">
                                                  <ArrowDown className="h-3 w-3 mr-1" />
                                                  Inherited
                                                </Badge>
                                              </TooltipTrigger>
                                              <TooltipContent>
                                                <p>Inherited from parent role</p>
                                              </TooltipContent>
                                            </Tooltip>
                                          </TooltipProvider>
                                        )}
                                        {source === 'temporary' && (
                                          <TooltipProvider>
                                            <Tooltip>
                                              <TooltipTrigger>
                                                <Badge variant="outline" className="text-xs border-orange-500 text-orange-700">
                                                  <Clock className="h-3 w-3 mr-1" />
                                                  Temporary
                                                </Badge>
                                              </TooltipTrigger>
                                              <TooltipContent>
                                                <p>Temporary permission</p>
                                              </TooltipContent>
                                            </Tooltip>
                                          </TooltipProvider>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        // List view for expert users
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <List className="h-5 w-5" />
              Permission List View
            </CardTitle>
            <CardDescription>
              Comprehensive view of all permissions across all roles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px] w-full">
              <div className="space-y-6">
                {filteredCategories.map((category) => (
                  <div key={category.category} className="space-y-3">
                    {/* Category Header */}
                    <div
                      className="flex items-center justify-between p-3 bg-muted/50 rounded-lg cursor-pointer hover:bg-muted/70 transition-colors"
                      onClick={() => toggleCategory(category.category)}
                    >
                      <div className="flex items-center gap-3">
                        {getCategoryIcon(category.metadata.icon)}
                        <div>
                          <h3 className="font-semibold">{category.metadata.name}</h3>
                          <p className="text-sm text-muted-foreground">{category.metadata.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          {category.permissions.length} permissions
                        </Badge>
                        {expandedCategories.has(category.category) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </div>

                    {/* Permissions List */}
                    {expandedCategories.has(category.category) && (
                      <div className="space-y-3">
                        {category.permissions.map((permission) => (
                          <Card key={permission.id} className="border-l-4 border-l-blue-500">
                            <CardContent className="p-4">
                              <div className="space-y-3">
                                {/* Permission Header */}
                                <div>
                                  <h4 className="font-semibold">{permission.name}</h4>
                                  <p className="text-sm text-muted-foreground">{permission.description}</p>
                                  <code className="text-xs bg-muted px-2 py-1 rounded mt-1 inline-block">
                                    {permission.code}
                                  </code>
                                </div>

                                {/* Role Assignments */}
                                <div>
                                  <h5 className="text-sm font-medium mb-2">Role Assignments:</h5>
                                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
                                    {roles.map(role => {
                                      const isGranted = isPermissionGranted(role, permission.id);
                                      const source = getPermissionSource(role, permission.id);

                                      return (
                                        <div
                                          key={role.id}
                                          className={`p-2 border rounded-lg flex items-center justify-between ${
                                            isGranted ? 'border-green-500 bg-green-50 dark:bg-green-950' : 'border-gray-200'
                                          }`}
                                        >
                                          <div className="flex items-center gap-2 flex-1 min-w-0">
                                            <Checkbox
                                              checked={isGranted}
                                              onCheckedChange={(checked) =>
                                                handlePermissionToggle(role.id, permission.id, isGranted)
                                              }
                                              disabled={loading || isUpdating || source === 'inherited'}
                                              className={`${
                                                source === 'direct' ? 'border-blue-500' :
                                                source === 'inherited' ? 'border-green-500' :
                                                source === 'temporary' ? 'border-orange-500' :
                                                ''
                                              }`}
                                            />
                                            <div className="min-w-0 flex-1">
                                              <div className="text-sm font-medium truncate">{role.name}</div>
                                              {role.is_system && (
                                                <Badge variant="outline" className="text-xs mt-1">System</Badge>
                                              )}
                                            </div>
                                          </div>
                                          <div className="flex items-center gap-1 ml-2">
                                            {source !== 'none' && source !== 'direct' && (
                                              <div>
                                                {source === 'inherited' && (
                                                  <TooltipProvider>
                                                    <Tooltip>
                                                      <TooltipTrigger>
                                                        <ArrowDown className="h-3 w-3 text-green-600" />
                                                      </TooltipTrigger>
                                                      <TooltipContent>
                                                        <p>Inherited from parent role</p>
                                                      </TooltipContent>
                                                    </Tooltip>
                                                  </TooltipProvider>
                                                )}
                                                {source === 'temporary' && (
                                                  <TooltipProvider>
                                                    <Tooltip>
                                                      <TooltipTrigger>
                                                        <Clock className="h-3 w-3 text-orange-600" />
                                                      </TooltipTrigger>
                                                      <TooltipContent>
                                                        <p>Temporary permission</p>
                                                      </TooltipContent>
                                                    </Tooltip>
                                                  </TooltipProvider>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      );
                                    })}
                                  </div>
                                </div>

                                {/* Permission Statistics */}
                                <div className="flex items-center gap-4 text-sm text-muted-foreground pt-2 border-t">
                                  <span>
                                    Granted to: {roles.filter(role => isPermissionGranted(role, permission.id)).length}/{roles.length} roles
                                  </span>
                                  <span>
                                    Direct: {roles.filter(role => getPermissionSource(role, permission.id) === 'direct').length}
                                  </span>
                                  <span>
                                    Inherited: {roles.filter(role => getPermissionSource(role, permission.id) === 'inherited').length}
                                  </span>
                                  <span>
                                    Temporary: {roles.filter(role => getPermissionSource(role, permission.id) === 'temporary').length}
                                  </span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
