import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Clock,
  MapPin,
  DollarSign,
  UserCheck,
  Smartphone,
  Shield,
  Plus,
  Trash2,
  Save,
  AlertTriangle,
  Info
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import {
  TimeConditionConfig,
  LocationConditionConfig,
  AmountConditionConfig,
  ApprovalConditionConfig,
  DeviceConditionConfig,
  SessionConditionConfig
} from './ConditionConfigs';

interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
}

interface PermissionCondition {
  id?: number;
  permission_id: number;
  condition_type: 'time' | 'location' | 'amount' | 'approval' | 'device' | 'session';
  condition_config: any;
  is_active: boolean;
  priority: number;
  description: string;
}

interface ConditionalPermissionEditorProps {
  permission: Permission;
  onSave: (conditions: PermissionCondition[]) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const conditionTypes = [
  { value: 'time', label: 'Time-based', icon: Clock, description: 'Restrict access by time and days' },
  { value: 'location', label: 'Location-based', icon: MapPin, description: 'Restrict access by IP or geography' },
  { value: 'amount', label: 'Amount-based', icon: DollarSign, description: 'Restrict based on transaction amounts' },
  { value: 'approval', label: 'Approval-based', icon: UserCheck, description: 'Require approval for certain operations' },
  { value: 'device', label: 'Device-based', icon: Smartphone, description: 'Restrict access by device type' },
  { value: 'session', label: 'Session-based', icon: Shield, description: 'Require fresh authentication or MFA' }
];

const defaultConfigs = {
  time: {
    start_time: '09:00',
    end_time: '17:00',
    days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    timezone: 'UTC'
  },
  location: {
    allowed_ip_ranges: ['***********/24'],
    allowed_countries: ['US'],
    blocked_countries: [],
    require_vpn: false
  },
  amount: {
    min_amount: 0,
    max_amount: 10000,
    currency: 'USD'
  },
  approval: {
    requires_approval: true,
    approval_threshold: 25000,
    auto_approve_below: 5000,
    approver_roles: ['manager', 'director']
  },
  device: {
    allowed_device_types: ['desktop', 'laptop'],
    blocked_device_types: ['mobile', 'tablet'],
    require_registered_device: true,
    max_devices_per_user: 3
  },
  session: {
    max_session_age: 3600,
    require_mfa: true,
    require_fresh_auth: false,
    max_idle_time: 1800
  }
};

export function ConditionalPermissionEditor({
  permission,
  onSave,
  onCancel,
  loading = false
}: ConditionalPermissionEditorProps) {
  const [conditions, setConditions] = useState<PermissionCondition[]>([]);
  const [activeTab, setActiveTab] = useState('conditions');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadExistingConditions();
  }, [permission.id]);

  const loadExistingConditions = async () => {
    try {
      const response = await fetch(`/api/permissions/${permission.code}/conditions`);
      if (response.ok) {
        const data = await response.json();
        setConditions(data);
      }
    } catch (error) {
      console.error('Error loading conditions:', error);
    }
  };

  const addCondition = (type: string) => {
    const newCondition: PermissionCondition = {
      permission_id: permission.id,
      condition_type: type as any,
      condition_config: defaultConfigs[type as keyof typeof defaultConfigs],
      is_active: true,
      priority: conditions.length + 1,
      description: `${type.charAt(0).toUpperCase() + type.slice(1)} condition for ${permission.name}`
    };
    setConditions([...conditions, newCondition]);
  };

  const updateCondition = (index: number, updates: Partial<PermissionCondition>) => {
    const updated = [...conditions];
    updated[index] = { ...updated[index], ...updates };
    setConditions(updated);
  };

  const removeCondition = (index: number) => {
    setConditions(conditions.filter((_, i) => i !== index));
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave(conditions);
      toast({
        title: "Success",
        description: "Permission conditions saved successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save permission conditions",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const getConditionIcon = (type: string) => {
    const conditionType = conditionTypes.find(ct => ct.value === type);
    if (!conditionType) return Info;
    const Icon = conditionType.icon;
    return <Icon className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Conditional Permission Editor</h2>
          <p className="text-muted-foreground">
            Configure conditions for <code className="bg-muted px-2 py-1 rounded text-sm">{permission.code}</code>
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving || loading}>
            {saving ? 'Saving...' : 'Save Conditions'}
            <Save className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Permission Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {permission.name}
          </CardTitle>
          <CardDescription>
            {permission.description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Badge variant="outline">{permission.category}</Badge>
            <Badge variant="secondary">{conditions.length} Conditions</Badge>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="conditions">Conditions</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="conditions" className="space-y-4">
          {/* Add Condition */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Add New Condition</CardTitle>
              <CardDescription>
                Choose a condition type to restrict when this permission can be used
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {conditionTypes.map((type) => {
                  const Icon = type.icon;
                  const hasCondition = conditions.some(c => c.condition_type === type.value);

                  return (
                    <Button
                      key={type.value}
                      variant={hasCondition ? "secondary" : "outline"}
                      className="h-auto p-4 flex flex-col items-start gap-2"
                      onClick={() => addCondition(type.value)}
                      disabled={hasCondition}
                    >
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <span className="font-medium">{type.label}</span>
                      </div>
                      <span className="text-xs text-muted-foreground text-left">
                        {type.description}
                      </span>
                      {hasCondition && (
                        <Badge variant="secondary" className="text-xs">
                          Already added
                        </Badge>
                      )}
                    </Button>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Existing Conditions */}
          {conditions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Current Conditions</CardTitle>
                <CardDescription>
                  Configure the conditions that must be met for this permission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  <div className="space-y-4">
                    {conditions.map((condition, index) => (
                      <ConditionEditor
                        key={index}
                        condition={condition}
                        index={index}
                        onUpdate={(updates) => updateCondition(index, updates)}
                        onRemove={() => removeCondition(index)}
                        icon={getConditionIcon(condition.condition_type)}
                      />
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}

          {conditions.length === 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                No conditions configured. This permission will be granted based on role assignments only.
                Add conditions above to implement additional restrictions.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Condition Summary</CardTitle>
              <CardDescription>
                Preview of all configured conditions for this permission
              </CardDescription>
            </CardHeader>
            <CardContent>
              {conditions.length > 0 ? (
                <div className="space-y-4">
                  {conditions.map((condition, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getConditionIcon(condition.condition_type)}
                          <span className="font-medium capitalize">
                            {condition.condition_type} Condition
                          </span>
                          <Badge variant={condition.is_active ? "default" : "secondary"}>
                            {condition.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                        <Badge variant="outline">Priority: {condition.priority}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {condition.description}
                      </p>
                      <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                        {JSON.stringify(condition.condition_config, null, 2)}
                      </pre>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No conditions configured</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface ConditionEditorProps {
  condition: PermissionCondition;
  index: number;
  onUpdate: (updates: Partial<PermissionCondition>) => void;
  onRemove: () => void;
  icon: React.ReactNode;
}

function ConditionEditor({ condition, index, onUpdate, onRemove, icon }: ConditionEditorProps) {
  const updateConfig = (updates: any) => {
    onUpdate({
      condition_config: {
        ...condition.condition_config,
        ...updates
      }
    });
  };

  return (
    <div className="border rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {icon}
          <span className="font-medium capitalize">{condition.condition_type} Condition</span>
        </div>
        <div className="flex items-center gap-2">
          <Switch
            checked={condition.is_active}
            onCheckedChange={(checked) => onUpdate({ is_active: checked })}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor={`priority-${index}`}>Priority</Label>
          <Input
            id={`priority-${index}`}
            type="number"
            value={condition.priority}
            onChange={(e) => onUpdate({ priority: parseInt(e.target.value) || 0 })}
            min="0"
            max="100"
          />
        </div>
        <div className="md:col-span-1">
          <Label htmlFor={`description-${index}`}>Description</Label>
          <Input
            id={`description-${index}`}
            value={condition.description}
            onChange={(e) => onUpdate({ description: e.target.value })}
            placeholder="Describe this condition..."
          />
        </div>
      </div>

      {/* Condition-specific configuration */}
      <div className="space-y-4">
        {condition.condition_type === 'time' && (
          <TimeConditionConfig
            config={condition.condition_config}
            onUpdate={updateConfig}
          />
        )}
        {condition.condition_type === 'location' && (
          <LocationConditionConfig
            config={condition.condition_config}
            onUpdate={updateConfig}
          />
        )}
        {condition.condition_type === 'amount' && (
          <AmountConditionConfig
            config={condition.condition_config}
            onUpdate={updateConfig}
          />
        )}
        {condition.condition_type === 'approval' && (
          <ApprovalConditionConfig
            config={condition.condition_config}
            onUpdate={updateConfig}
          />
        )}
        {condition.condition_type === 'device' && (
          <DeviceConditionConfig
            config={condition.condition_config}
            onUpdate={updateConfig}
          />
        )}
        {condition.condition_type === 'session' && (
          <SessionConditionConfig
            config={condition.condition_config}
            onUpdate={updateConfig}
          />
        )}
      </div>

      {/* Raw JSON editor for advanced users */}
      <details className="mt-4">
        <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
          Advanced: Edit Raw JSON
        </summary>
        <div className="mt-2">
          <Textarea
            value={JSON.stringify(condition.condition_config, null, 2)}
            onChange={(e) => {
              try {
                const config = JSON.parse(e.target.value);
                onUpdate({ condition_config: config });
              } catch (error) {
                // Invalid JSON, don't update
              }
            }}
            className="font-mono text-sm"
            rows={6}
          />
        </div>
      </details>
    </div>
  );
}
