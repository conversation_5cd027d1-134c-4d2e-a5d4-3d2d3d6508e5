import { Account, InsertAccount, Transaction, InsertTransaction } from '../../../shared/schema';

// Account-related storage operations
export interface IAccountStorage {
  getAccount(id: number): Promise<Account | undefined>;
  getAccountsByCompany(companyId: number): Promise<Account[]>;
  createAccount(account: InsertAccount): Promise<Account>;
  updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account>;
  deleteAccount(id: number, companyId: number): Promise<boolean>;
  getAccountBalance(id: number, companyId: number): Promise<string>;
  updateAccountBalance(id: number, companyId: number, amount: string, isCredit: boolean): Promise<Account>;
}

// Transaction-related storage operations
export interface ITransactionStorage {
  getTransaction(id: number): Promise<Transaction | undefined>;
  getTransactionsByCompany(
    companyId: number,
    dateRange?: { startDate: string, endDate: string },
    accountId?: number,
    transactionType?: string
  ): Promise<Transaction[]>;
  getTransactionsByAccount(accountId: number, companyId: number): Promise<Transaction[]>;
  createTransaction(transaction: InsertTransaction): Promise<Transaction>;
  updateTransaction(id: number, companyId: number, transaction: Partial<InsertTransaction>): Promise<Transaction>;
  deleteTransaction(id: number, companyId: number): Promise<boolean>;
  getTransactionSummary(
    companyId: number,
    dateRange: { startDate: string, endDate: string }
  ): Promise<{ credits: string, debits: string, net: string }>;
}

// Report-related storage operations
export interface IReportStorage {
  getProfitLossReport(
    companyId: number,
    dateRange: { startDate: string, endDate: string }
  ): Promise<any>;
  getCashFlowReport(
    companyId: number,
    dateRange: { startDate: string, endDate: string }
  ): Promise<any>;
  getCollectionReport(
    companyId: number,
    dateRange: { startDate: string, endDate: string }
  ): Promise<any>;
}

// Combined financial storage interface
export interface IFinancialStorage extends
  IAccountStorage,
  ITransactionStorage,
  IReportStorage
{
  // Any methods that don't fit into a specific domain
}
