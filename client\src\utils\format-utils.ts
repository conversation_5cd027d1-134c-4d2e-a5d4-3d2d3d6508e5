/**
 * Formatting utilities for currency and number presentation
 */

/**
 * Format a number as currency
 * 
 * @param amount The amount to format
 * @param currency Currency code (default: 'INR')
 * @param locale Locale string (default: 'en-IN')
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number | string, 
  currency: string = 'INR', 
  locale: string = 'en-IN'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    maximumFractionDigits: 0,
    minimumFractionDigits: 0
  }).format(Number(amount));
}

/**
 * Format a date for display
 * 
 * @param dateString ISO date string
 * @param locale Locale string (default: 'en-IN')
 * @returns Formatted date string
 */
export function formatDate(dateString: string, locale: string = 'en-IN'): string {
  const date = new Date(dateString);
  return date.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Format loan term with appropriate units based on frequency
 * 
 * @param term The term value (number of periods)
 * @param frequency The frequency of the term ('daily', 'weekly', 'biweekly', 'monthly', 'yearly')
 * @returns Formatted term string with appropriate units
 */
export function formatLoanTerm(term: number, frequency: string): string {
  if (!term) return '';
  
  // If frequency is not provided or empty, use default
  if (!frequency) return `${term} ${term === 1 ? 'period' : 'periods'}`;
  
  switch(frequency.toLowerCase()) {
    case 'daily':
      return `${term} ${term === 1 ? 'day' : 'days'}`;
    case 'weekly':
      return `${term} ${term === 1 ? 'week' : 'weeks'}`;
    case 'biweekly':
      return `${term} ${term === 1 ? 'fortnight' : 'fortnights'}`;
    case 'monthly':
      return `${term} ${term === 1 ? 'month' : 'months'}`;
    case 'yearly':
      return `${term} ${term === 1 ? 'year' : 'years'}`;
    default:
      return `${term} ${term === 1 ? 'period' : 'periods'}`;
  }
}

/**
 * Format time for display
 * 
 * @param dateString ISO date string
 * @param locale Locale string (default: 'en-IN')
 * @returns Formatted time string
 */
export function formatTime(dateString: string, locale: string = 'en-IN'): string {
  const date = new Date(dateString);
  return date.toLocaleTimeString(locale, {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

/**
 * Format interest type for display, highlighting upfront interest
 * 
 * @param interestType Interest type ('flat', 'reducing', 'compound')
 * @param isUpfrontInterest Whether interest is deducted upfront
 * @returns Formatted interest type string
 */
export function formatInterestType(interestType: string, isUpfrontInterest: boolean): string {
  if (isUpfrontInterest) {
    return 'Upfront Interest';
  }
  
  switch(interestType.toLowerCase()) {
    case 'flat':
      return 'Flat Interest';
    case 'reducing':
      return 'Reducing Balance';
    case 'compound':
      return 'Compound Interest';
    default:
      return interestType;
  }
}