import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { generateAmortizationSchedule } from '@/utils/calculate-loan-payment';
import { formatCurrency, formatLoanTerm, formatInterestType } from '@/utils/format-utils';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Download, Search } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';

interface AmortizationScheduleProps {
  amount: number;
  interestRate: number;
  termMonths: number;
  interestType: string;
  startDate?: string;
  currencyCode?: string;
  locale?: string;
  paymentFrequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly';
  deductInterestUpfront?: boolean;
}

export const AmortizationSchedule: React.FC<AmortizationScheduleProps> = ({
  amount,
  interestRate,
  termMonths,
  interestType,
  startDate,
  currencyCode = 'INR',
  locale = 'en-IN',
  paymentFrequency = 'monthly',
  deductInterestUpfront = true,
}) => {
  const [showFullSchedule, setShowFullSchedule] = useState(false);
  const [schedule, setSchedule] = useState<any[]>([]);
  const [visiblePayments, setVisiblePayments] = useState<any[]>([]);
  const [searchMonth, setSearchMonth] = useState<string>('');
  const [totalInterest, setTotalInterest] = useState<number>(0);

  // Get the payment period name based on frequency
  const getPaymentPeriodName = () => {
    switch(paymentFrequency) {
      case 'daily': return 'Day';
      case 'weekly': return 'Week';
      case 'biweekly': return 'Fortnight';
      case 'monthly': return 'Month';
      default: return 'Payment';
    }
  };

  useEffect(() => {
    // Validate inputs before calculating
    if (amount > 0 && interestRate >= 0 && termMonths > 0) {
      // Use the startDate string directly or get current date string
      const dateString = startDate || new Date().toISOString().split('T')[0];
      const fullSchedule = generateAmortizationSchedule(
        amount,
        interestRate,
        termMonths,
        interestType,
        dateString,
        paymentFrequency,
        deductInterestUpfront
      );
      setSchedule(fullSchedule);
      
      // Calculate total interest for upfront display using the fixed flat rate formula
      // For flat interest, it's simply principal * rate / 100
      const interest = amount * (interestRate / 100);
      setTotalInterest(interest);
      
      // Initially show only first 3 and last payment
      updateVisiblePayments(fullSchedule, false);
    } else {
      setSchedule([]);
      setVisiblePayments([]);
      setTotalInterest(0);
    }
  }, [amount, interestRate, termMonths, interestType, startDate, paymentFrequency, deductInterestUpfront]);

  // Update visible payments based on showFullSchedule state
  const updateVisiblePayments = (fullSchedule: any[], showAll: boolean) => {
    if (fullSchedule.length === 0) {
      setVisiblePayments([]);
      return;
    }
    
    if (showAll) {
      setVisiblePayments(fullSchedule);
    } else {
      // Show first 3 and last payment
      const visible = [...fullSchedule.slice(0, 3)];
      if (fullSchedule.length > 3) {
        visible.push({ isEllipsis: true, paymentNumber: '...' });
        visible.push(fullSchedule[fullSchedule.length - 1]);
      }
      setVisiblePayments(visible);
    }
  };

  // Handle toggling full schedule view
  const toggleFullSchedule = () => {
    const newState = !showFullSchedule;
    setShowFullSchedule(newState);
    updateVisiblePayments(schedule, newState);
    // Reset search when toggling
    setSearchMonth('');
  };

  // Handle search by month number
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchMonth(value);
    
    if (!value.trim()) {
      // If search is cleared, reset to default view
      updateVisiblePayments(schedule, showFullSchedule);
      return;
    }
    
    // Convert to number and validate
    const monthNum = parseInt(value);
    if (!isNaN(monthNum) && monthNum > 0 && monthNum <= termMonths) {
      // Find the matching payment
      const found = schedule.find(payment => payment.paymentNumber === monthNum);
      if (found) {
        setVisiblePayments([found]);
      }
    } else {
      // Invalid month number, show default
      updateVisiblePayments(schedule, showFullSchedule);
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>Amortization Schedule</span>
          <div className="flex items-center gap-2">
            {schedule.length > 0 && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={toggleFullSchedule}
                className="text-xs h-8"
              >
                {showFullSchedule ? (
                  <>
                    <ChevronUp className="h-3 w-3 mr-1" />
                    Show Less
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-3 w-3 mr-1" />
                    Show All
                  </>
                )}
              </Button>
            )}
          </div>
        </CardTitle>
        <CardDescription>
          Payment schedule based on {formatCurrency(amount, currencyCode, locale)} at {interestRate}% {formatInterestType(interestType, deductInterestUpfront)}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {schedule.length > 0 ? (
          <div className="space-y-4">
            {/* Search input for large schedules */}
            {termMonths > 10 && (
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search payment by month (1-60)"
                  className="pl-8"
                  value={searchMonth}
                  onChange={handleSearch}
                  type="number"
                  min={1}
                  max={termMonths}
                />
              </div>
            )}
            
            {/* Payment schedule table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">{getPaymentPeriodName()}</TableHead>
                    <TableHead>Payment Date</TableHead>
                    <TableHead className="text-right">Payment</TableHead>
                    <TableHead className="text-right">Principal</TableHead>
                    <TableHead className="text-right">Interest</TableHead>
                    <TableHead className="text-right">Remaining</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {visiblePayments.length > 0 ? (
                    visiblePayments.map((payment, index) => 
                      payment.isEllipsis ? (
                        <TableRow key="ellipsis">
                          <TableCell colSpan={6} className="text-center py-2 text-muted-foreground">
                            • • • Click "Show All" to view all {termMonths} payments • • •
                          </TableCell>
                        </TableRow>
                      ) : (
                        <TableRow key={index}>
                          <TableCell className="font-medium">{payment.paymentNumber}</TableCell>
                          <TableCell>{formatDate(new Date(payment.paymentDate))}</TableCell>
                          <TableCell className="text-right">{formatCurrency(payment.paymentAmount, currencyCode, locale)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(payment.principalPayment, currencyCode, locale)}</TableCell>
                          <TableCell className="text-right text-amber-600">{formatCurrency(payment.interestPayment, currencyCode, locale)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(payment.remainingBalance, currencyCode, locale)}</TableCell>
                        </TableRow>
                      )
                    )
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                        No payment schedule available
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <p>Enter valid loan details to view payment schedule</p>
          </div>
        )}
      </CardContent>
      
      {schedule.length > 0 && (
        <CardFooter className="pt-0 flex flex-col gap-2">
          <div className="flex justify-between w-full">
            <div>
              <p className="text-xs text-muted-foreground">
                *This schedule is for informational purposes only.
              </p>
              {deductInterestUpfront && (
                <p className="text-xs text-amber-600 mt-1">
                  Note: Interest amount ({formatCurrency(totalInterest, currencyCode, locale)}) is deducted upfront, and customer receives {formatCurrency(amount - totalInterest, currencyCode, locale)}.
                </p>
              )}
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="text-xs"
              onClick={() => {
                // Create CSV content
                const headers = "Payment Number,Payment Date,Payment Amount,Principal Payment,Interest Payment,Remaining Balance\n";
                const csvContent = schedule.map(entry => {
                  return `${entry.paymentNumber},${entry.paymentDate},${entry.paymentAmount},${entry.principalPayment},${entry.interestPayment},${entry.remainingBalance}`;
                }).join("\n");
                
                // Create and download CSV file
                const blob = new Blob([headers + csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', `loan_schedule_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              <Download className="h-3 w-3 mr-1" />
              Export to CSV
            </Button>
          </div>
        </CardFooter>
      )}
    </Card>
  );
};

export default AmortizationSchedule;