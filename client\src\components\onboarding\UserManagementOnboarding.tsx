import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Users,
  Shield,
  UserCheck,
  Workflow,
  ArrowRight,
  ArrowLeft,
  CheckCircle,
  Info,
  Lightbulb,
  Target,
  BookOpen
} from 'lucide-react';
import { markTourCompleted } from '@/lib/tourTracking';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  content: React.ReactNode;
  experienceLevel: ('basic' | 'advanced' | 'expert')[];
}

interface UserManagementOnboardingProps {
  isOpen: boolean;
  onClose: () => void;
  userExperienceLevel: 'basic' | 'advanced' | 'expert';
  onExperienceLevelChange: (level: 'basic' | 'advanced' | 'expert') => void;
  userId: number;
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to User Management',
    description: 'Let\'s get you started with managing your team',
    icon: Users,
    experienceLevel: ['basic', 'advanced', 'expert'],
    content: (
      <div className="space-y-4">
        <div className="text-center">
          <Users className="h-16 w-16 mx-auto text-blue-500 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Welcome to User Management!</h3>
          <p className="text-muted-foreground">
            This system helps you control who can access what in your application. 
            Let's walk through the basics to get you started.
          </p>
        </div>
        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 dark:text-blue-100">Choose Your Experience Level</h4>
              <p className="text-sm text-blue-700 dark:text-blue-200 mt-1">
                We'll customize the interface based on your needs. You can change this anytime.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'experience-levels',
    title: 'Choose Your Experience Level',
    description: 'Customize the interface for your needs',
    icon: Target,
    experienceLevel: ['basic', 'advanced', 'expert'],
    content: (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold mb-4">What's your experience with user management?</h3>
        <div className="space-y-3">
          <Card className="border-2 border-green-500 bg-green-50 dark:bg-green-950">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <Users className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h4 className="font-medium">Basic - I'm new to this</h4>
                  <p className="text-sm text-muted-foreground">
                    Simple interface focused on adding users and assigning basic roles
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-2 border-transparent hover:border-blue-500 cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <Shield className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h4 className="font-medium">Advanced - I understand permissions</h4>
                  <p className="text-sm text-muted-foreground">
                    Access to permission matrix and role management features
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="border-2 border-transparent hover:border-purple-500 cursor-pointer">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <Workflow className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h4 className="font-medium">Expert - I need full control</h4>
                  <p className="text-sm text-muted-foreground">
                    All features including role hierarchies, workflows, and bulk operations
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  },
  {
    id: 'basic-concepts',
    title: 'Understanding the Basics',
    description: 'Learn about users, roles, and permissions',
    icon: BookOpen,
    experienceLevel: ['basic'],
    content: (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold mb-4">Key Concepts</h3>
        <div className="space-y-4">
          <div className="flex items-start gap-3 p-3 border rounded-lg">
            <Users className="h-6 w-6 text-blue-500 mt-1" />
            <div>
              <h4 className="font-medium">Users</h4>
              <p className="text-sm text-muted-foreground">
                People who can log into your system. Each user has an email and can be assigned roles.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3 p-3 border rounded-lg">
            <UserCheck className="h-6 w-6 text-green-500 mt-1" />
            <div>
              <h4 className="font-medium">Roles</h4>
              <p className="text-sm text-muted-foreground">
                Job functions like "Manager" or "Employee". Roles determine what users can do.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-3 p-3 border rounded-lg">
            <Shield className="h-6 w-6 text-purple-500 mt-1" />
            <div>
              <h4 className="font-medium">Permissions</h4>
              <p className="text-sm text-muted-foreground">
                Specific actions like "View Reports" or "Add Customers". Roles have collections of permissions.
              </p>
            </div>
          </div>
        </div>
        <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <Lightbulb className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-900 dark:text-yellow-100">Quick Tip</h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-200 mt-1">
                Start by creating roles that match your team structure, then assign users to those roles.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'getting-started',
    title: 'Getting Started',
    description: 'Your first steps in user management',
    icon: CheckCircle,
    experienceLevel: ['basic', 'advanced', 'expert'],
    content: (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold mb-4">Ready to get started?</h3>
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
            <div>
              <h4 className="font-medium">Add your first user</h4>
              <p className="text-sm text-muted-foreground">Click "Add User" to invite team members</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
            <div>
              <h4 className="font-medium">Create or assign roles</h4>
              <p className="text-sm text-muted-foreground">Set up roles that match your organization</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
            <div>
              <h4 className="font-medium">Test the permissions</h4>
              <p className="text-sm text-muted-foreground">Make sure users can access what they need</p>
            </div>
          </div>
        </div>
        <div className="bg-green-50 dark:bg-green-950 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900 dark:text-green-100">You're all set!</h4>
              <p className="text-sm text-green-700 dark:text-green-200 mt-1">
                Remember, you can always change the experience level in the top-right corner if you need more features.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }
];

export function UserManagementOnboarding({
  isOpen,
  onClose,
  userExperienceLevel,
  onExperienceLevelChange,
  userId
}: UserManagementOnboardingProps) {
  const [currentStep, setCurrentStep] = useState(0);
  
  const relevantSteps = onboardingSteps.filter(step => 
    step.experienceLevel.includes(userExperienceLevel)
  );
  
  const currentStepData = relevantSteps[currentStep];
  const isLastStep = currentStep === relevantSteps.length - 1;
  const isFirstStep = currentStep === 0;

  const handleNext = () => {
    if (isLastStep) {
      // Mark tour as completed when user finishes
      markTourCompleted(userId, 'user-management-onboarding', false);
      onClose();
    } else {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    // Mark tour as skipped when user skips
    markTourCompleted(userId, 'user-management-onboarding', true);
    onClose();
  };

  if (!currentStepData) return null;

  const IconComponent = currentStepData.icon;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <IconComponent className="h-5 w-5 text-white" />
            </div>
            <div>
              <DialogTitle>{currentStepData.title}</DialogTitle>
              <DialogDescription>{currentStepData.description}</DialogDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Progress value={(currentStep + 1) / relevantSteps.length * 100} className="flex-1" />
            <Badge variant="outline">
              {currentStep + 1} of {relevantSteps.length}
            </Badge>
          </div>
        </DialogHeader>

        <div className="py-4">
          {currentStepData.content}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="ghost" onClick={handleSkip}>
              Skip Tour
            </Button>
            {!isFirstStep && (
              <Button variant="outline" onClick={handlePrevious}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
            )}
          </div>
          <Button onClick={handleNext}>
            {isLastStep ? 'Get Started' : 'Next'}
            {!isLastStep && <ArrowRight className="h-4 w-4 ml-1" />}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
