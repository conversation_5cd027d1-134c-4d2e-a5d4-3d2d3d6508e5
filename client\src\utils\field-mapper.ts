/**
 * Field Mapper Utility
 * 
 * This utility provides consistent mapping between template field names (field_xx)
 * and standard/database field names for loan entities.
 * 
 * As per requirements, standard field names and database column names are kept identical
 * to maintain consistency.
 */

// Define the known template field mappings
export const templateFieldMap: Record<string, string> = {
  // Map dynamic template fields to standard names (which match DB column names)
  'field_91': 'interest_rate',
  'field_92': 'amount',
  'field_93': 'term',
  'field_94': 'customer_id',
  'field_95': 'interest_type',
  'field_96': 'start_date',
  'field_97': 'end_date',
  'field_98': 'first_name',
  'field_99': 'last_name',
  
  // New field mappings from the template form
  'field_100': 'interest_rate',
  'field_101': 'amount',
  'field_102': 'interest_type',
  'field_103': 'term',
  'field_104': 'start_date',
  'field_105': 'end_date',
  'field_107': 'notes',
  'field_109': 'customer_id',
  'field_123': 'customer_id',
  'field_120': 'interest_type',
  'field_121': 'amount',
  'field_122': 'interest_rate',
  'field_125': 'term',
  'field_124': 'start_date',
  'field_126': 'end_date',
  // These mappings can vary by template
};

// Create a mapper that transforms template form values to standardized values
export function mapTemplateToStandard(formValues: Record<string, any>): Record<string, any> {
  const standardValues: Record<string, any> = { ...formValues };
  
  // Map template-specific field names to standard names
  Object.entries(templateFieldMap).forEach(([templateField, standardField]) => {
    if (formValues[templateField] !== undefined) {
      standardValues[standardField] = formValues[templateField];
    }
  });
  
  return standardValues;
}

// Standard field names are now the same as database column names, so this function is simplified
// We keep it for backward compatibility with existing code
export function mapStandardToDatabase(standardValues: Record<string, any>): Record<string, any> {
  // Since standard field names match database column names, we just return the input
  return { ...standardValues };
}

// A utility function that maps directly from template fields to database fields
export function mapTemplateToDatabaseFields(formValues: Record<string, any>): Record<string, any> {
  // Simply map template fields to standard fields (which are the same as database fields)
  return mapTemplateToStandard(formValues);
}

// Additional function to find customer ID in various field patterns
function findCustomerId(formValues: Record<string, any>): number | null {
  // First check for standard customer_id field
  if (formValues.customer_id) {
    return typeof formValues.customer_id === 'string' 
      ? parseInt(formValues.customer_id, 10) 
      : Number(formValues.customer_id);
  }
  
  // Check for customer name field that contains a number (often used in customer select)
  // These fields may be named differently in various templates
  const customerFieldPattern = /^field_\d+$/;
  const customerNamePattern = /customer/i;
  
  // Look for any field with "customer" in its name
  const customerFields = Object.entries(formValues)
    .filter(([key, value]) => 
      // Check for fields that might contain customer data
      (key.includes('customer') || 
       (typeof value === 'string' && value.includes('Vetrivel'))) ||
      key === 'field_123' || key === 'field_109' || key === 'field_94'
    );
  
  if (customerFields.length > 0) {
    console.log('Found potential customer fields:', customerFields);
    
    // Try to extract a numeric customer ID
    for (const [key, value] of customerFields) {
      if (typeof value === 'string') {
        // Try to extract a number from the string value
        const matches = value.match(/^(\d+)/);
        if (matches && matches[1]) {
          return parseInt(matches[1], 10);
        }
      } else if (typeof value === 'number') {
        return value;
      }
    }
  }
  
  // No customer ID found
  return null;
}

// Advanced field detection to handle different templates
export function detectFieldValue(formValues: Record<string, any>, standardField: string, fieldPatterns: string[]): any {
  // First check for the standard field name
  if (formValues[standardField] !== undefined) {
    return formValues[standardField];
  }
  
  // Check each pattern in priority order
  for (const pattern of fieldPatterns) {
    if (formValues[pattern] !== undefined) {
      return formValues[pattern];
    }
  }
  
  // Check if any field maps to this standard field in templateFieldMap
  const matchingTemplateFields = Object.entries(templateFieldMap)
    .filter(([_, standardName]) => standardName === standardField)
    .map(([templateField]) => templateField);
  
  for (const templateField of matchingTemplateFields) {
    if (formValues[templateField] !== undefined) {
      return formValues[templateField];
    }
  }
  
  // Nothing found
  return undefined;
}

// Extract normalized loan data from form values (template or standard)
export function extractLoanData(formValues: Record<string, any>, companyId: number): Record<string, any> {
  // First map to standard fields to ensure we have consistent naming
  const standardFields = mapTemplateToStandard(formValues);
  
  console.log('Standard fields after mapping:', standardFields);
  
  // Use advanced detection for each field type with fallback patterns
  
  // CUSTOMER ID - Look for customer ID in various fields, patterns, and formats
  let customerIdValue = findCustomerId(standardFields) || 
                       detectFieldValue(standardFields, 'customer_id', ['field_123', 'field_109', 'field_94']);
  
  // Ensure customer_id is a valid number
  let customerId = null;
  
  if (customerIdValue) {
    if (typeof customerIdValue === 'number') {
      customerId = customerIdValue;
    } else if (typeof customerIdValue === 'string') {
      // Extract numeric ID if it's in a string like "1 - Customer Name"
      const matches = customerIdValue.match(/^(\d+)/);
      if (matches && matches[1]) {
        customerId = parseInt(matches[1], 10);
      } else {
        // Try to parse the entire string as a number
        const parsed = parseInt(customerIdValue, 10);
        if (!isNaN(parsed)) {
          customerId = parsed;
        }
      }
    } else if (typeof customerIdValue === 'object' && customerIdValue !== null && 'id' in customerIdValue) {
      // Handle case where it's an object with an id property (from dropdown)
      customerId = customerIdValue.id;
    }
  }
  
  if (!customerId) {
    console.error('Failed to extract valid customer ID from:', customerIdValue);
  } else {
    console.log('Successfully extracted customer ID:', customerId);
  }
  
  // AMOUNT - Look for amount field with various patterns and formats
  const amountValue = detectFieldValue(standardFields, 'amount', ['field_121', 'field_101', 'field_92']);
  let amount = 0;
  
  // Handle amount - complex logic to handle various formats including strings with commas or currency symbols
  if (amountValue) {
    if (typeof amountValue === 'number') {
      amount = amountValue;
    } else if (typeof amountValue === 'string') {
      // Remove currency symbols, commas, and other non-numeric characters
      const cleanedAmount = amountValue.replace(/[^0-9.-]/g, '');
      amount = parseFloat(cleanedAmount) || 0;
    }
  }
  
  // INTEREST RATE - Look for interest rate field with various patterns
  const interestRateValue = detectFieldValue(standardFields, 'interest_rate', ['field_122', 'field_100', 'field_91']);
  let interestRate = 0;
  
  // Handle interest rate - complex logic to handle various formats
  if (interestRateValue) {
    if (typeof interestRateValue === 'number') {
      interestRate = interestRateValue;
    } else if (typeof interestRateValue === 'string') {
      // Remove % symbol and other non-numeric characters
      const cleanedRate = interestRateValue.replace(/[^0-9.-]/g, '');
      interestRate = parseFloat(cleanedRate) || 0;
    }
  }
  
  // TERM - Look for term field with various patterns
  const termValue = detectFieldValue(standardFields, 'term', ['field_125', 'field_103', 'field_93']);
  let term = 1; // Default to 1 month
  
  // Handle term - complex logic to handle various formats
  if (termValue) {
    if (typeof termValue === 'number') {
      term = termValue;
    } else if (typeof termValue === 'string') {
      // Remove any non-numeric characters
      const cleanedTerm = termValue.replace(/[^0-9]/g, '');
      term = parseInt(cleanedTerm, 10) || 1;
    }
  }
  
  // INTEREST TYPE - Look for interest type field with various patterns
  let interestTypeValue = detectFieldValue(standardFields, 'interest_type', ['field_120', 'field_102', 'field_95']);
  let interestType = interestTypeValue || 'flat';
  
  // Handle if interest_type is an object with value property (from select component)
  if (typeof interestType === 'object' && interestType !== null) {
    console.log('Converting interest_type object to string value:', interestType);
    interestType = interestType.value || 'flat';
  }
  
  // Ensure interest_type is one of the allowed enum values: flat, reducing, compound
  // Map 'simple' to 'flat' as they are equivalent for our purposes
  if (interestType === 'simple') {
    console.log('Mapping interest type "simple" to "flat" to match database enum');
    interestType = 'flat';
  }
  
  // Only allow valid enum values
  if (!['flat', 'reducing', 'compound'].includes(String(interestType).toLowerCase())) {
    console.warn(`Interest type "${interestType}" is not valid, defaulting to "flat"`);
    interestType = 'flat';
  }
  
  // START DATE - Look for start date field with various patterns
  const startDateValue = detectFieldValue(standardFields, 'start_date', ['field_124', 'field_104', 'field_96']);
  // Always ensure start_date is formatted as YYYY-MM-DD
  let startDate = startDateValue || new Date().toISOString().split('T')[0];
  
  // Clean up date format if it's not in ISO format
  if (typeof startDate === 'string' && startDate.includes('/')) {
    // Convert from MM/DD/YYYY to YYYY-MM-DD
    const parts = startDate.split('/');
    if (parts.length === 3) {
      startDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
    }
  }
  
  // END DATE - Look for end date field or calculate based on start date and term
  const endDateValue = detectFieldValue(standardFields, 'end_date', ['field_126', 'field_105', 'field_97']);
  let endDate = endDateValue;
  
  // Clean up end date format if needed
  if (typeof endDate === 'string' && endDate.includes('/')) {
    // Convert from MM/DD/YYYY to YYYY-MM-DD
    const parts = endDate.split('/');
    if (parts.length === 3) {
      endDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
    }
  }
  
  // Calculate end date if not provided
  if (!endDate) {
    try {
      const startDateObj = new Date(startDate);
      if (!isNaN(startDateObj.getTime())) {
        startDateObj.setMonth(startDateObj.getMonth() + term);
        endDate = startDateObj.toISOString().split('T')[0];
        console.log(`Calculated end date: ${endDate} from start date: ${startDate} and term: ${term} months`);
      } else {
        console.warn(`Invalid start date: ${startDate}, using today + term for end date`);
        const today = new Date();
        today.setMonth(today.getMonth() + term);
        endDate = today.toISOString().split('T')[0];
      }
    } catch (error) {
      console.error(`Error calculating end date from start date: ${startDate}`, error);
      // Fallback to today + term
      const today = new Date();
      today.setMonth(today.getMonth() + term);
      endDate = today.toISOString().split('T')[0];
    }
  }
  
  // NOTES - Look for notes field with various patterns
  const notesValue = detectFieldValue(standardFields, 'notes', ['field_107']);
  const notes = notesValue || null;
  
  // Log the processed values for debugging
  console.log('Processed loan data:', {
    customer_id: customerId,
    amount,
    interest_rate: interestRate,
    term: term,
    start_date: startDate,
    end_date: endDate,
    interest_type: interestType,
    notes
  });
  
  // Return normalized loan data object with standard field names
  // that match exactly with database column names
  return {
    customer_id: customerId,
    company_id: companyId,
    amount: String(amount), // API expects a string
    interest_rate: String(interestRate), // API expects a string
    interest_type: interestType,
    term: term,
    start_date: startDate,
    end_date: endDate,
    status: 'active',
    notes: notes,
  };
}