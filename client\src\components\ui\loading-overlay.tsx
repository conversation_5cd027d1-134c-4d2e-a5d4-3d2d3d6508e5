import React from 'react';
import { Spinner } from './spinner';

interface LoadingOverlayProps {
  message?: string;
  isLoading: boolean;
  progress?: number;
  showProgress?: boolean;
}

export function LoadingOverlay({
  message = 'Processing your request...',
  isLoading,
  progress,
  showProgress = false,
}: LoadingOverlayProps) {
  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
        <div className="flex flex-col items-center">
          <Spinner size="lg" className="mb-4" />
          <h3 className="text-lg font-medium mb-2">{message}</h3>
          
          {showProgress && typeof progress === 'number' && (
            <div className="w-full mt-4">
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-primary transition-all duration-300 ease-in-out"
                  style={{ width: `${Math.max(5, Math.min(100, progress))}%` }}
                />
              </div>
              <p className="text-sm text-center mt-1">{Math.round(progress)}%</p>
            </div>
          )}
          
          <p className="text-sm text-gray-500 text-center mt-2">
            This may take a few moments
          </p>
        </div>
      </div>
    </div>
  );
}
