#!/usr/bin/env node
/**
 * Migration Script: Update Loan Reference Codes
 * Purpose: Update all existing loans to have an empty value in the loan_reference_code column
 * Usage: node scripts/migrations/update-loan-reference-codes.js
 */

import { runMigration, logMigrationStats } from '../utils/migration-runner.js';

await runMigration('Update Loan Reference Codes', async (pool, { dryRun }) => {
  // First, check if there are any loans in the database
  const countResult = await pool.query('SELECT COUNT(*) FROM loans');
  const loanCount = parseInt(countResult.rows[0].count);
  
  console.log(`Found ${loanCount} loans in the database`);

  if (loanCount === 0) {
    console.log('No loans to update. Exiting.');
    return;
  }

  if (dryRun) {
    // Check how many loans would be updated
    const nullCountResult = await pool.query(`
      SELECT COUNT(*) FROM loans WHERE loan_reference_code IS NULL
    `);
    const nullCount = parseInt(nullCountResult.rows[0].count);
    
    console.log(`Would update ${nullCount} loans with NULL loan_reference_code to empty string`);
    logMigrationStats({
      'Total loans': loanCount,
      'Loans with NULL reference code': nullCount,
      'Loans that would be updated': nullCount
    });
    return;
  }

  // Update all loans to have an empty value in the loan_reference_code column
  const updateResult = await pool.query(`
    UPDATE loans
    SET loan_reference_code = ''
    WHERE loan_reference_code IS NULL
    RETURNING id
  `);

  const updatedCount = updateResult.rows.length;
  console.log(`Updated ${updatedCount} loans with empty loan_reference_code`);

  // Verify the update
  const verifyResult = await pool.query(`
    SELECT COUNT(*) FROM loans WHERE loan_reference_code = ''
  `);

  const verifiedCount = parseInt(verifyResult.rows[0].count);
  console.log(`Verified ${verifiedCount} loans now have empty loan_reference_code`);

  logMigrationStats({
    'Total loans': loanCount,
    'Loans updated': updatedCount,
    'Loans with empty reference code': verifiedCount
  });
});
