FROM node:20-slim AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package.json and install dependencies
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build

# Production image, copy all the files and run the server
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Don't run as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 trackfina
RUN mkdir -p /app/data && chown -R trackfina:nodejs /app

# Copy necessary files from builder
COPY --from=builder --chown=trackfina:nodejs /app/dist ./dist
COPY --from=builder --chown=trackfina:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=trackfina:nodejs /app/package.json ./package.json

# Copy environment file example (to be overridden by mounted file)
COPY --chown=trackfina:nodejs .env ./.env

USER trackfina

EXPOSE 8080

# Set healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
  CMD curl -f http://localhost:8080/ || exit 1

# Run the application
CMD ["node", "dist/index.js"]