-- Access Monitoring System Schema
-- Migration: 018_access_monitoring_system.sql
-- Description: Add comprehensive access monitoring with real-time detection and automated responses

-- Security event type enum
CREATE TYPE security_event_type AS ENUM (
  'login_attempt', 'login_success', 'login_failure', 'logout',
  'permission_denied', 'permission_granted', 'data_access',
  'suspicious_activity', 'anomaly_detected', 'rate_limit_exceeded',
  'geo_anomaly', 'device_anomaly', 'time_anomaly', 'behavior_anomaly',
  'brute_force_attempt', 'session_hijack_attempt', 'privilege_escalation',
  'data_export', 'bulk_operation', 'admin_action', 'emergency_access'
);

-- Security alert severity enum
CREATE TYPE alert_severity AS ENUM ('low', 'medium', 'high', 'critical');

-- Security response action enum
CREATE TYPE security_response_action AS ENUM (
  'log_only', 'alert_admin', 'block_ip', 'suspend_user', 'terminate_session',
  'require_mfa', 'require_fresh_auth', 'block_device', 'escalate_to_admin'
);

-- Security events table for comprehensive monitoring
CREATE TABLE IF NOT EXISTS "security_events" (
  "id" SERIAL PRIMARY KEY,
  "event_id" varchar(255) NOT NULL UNIQUE,
  "user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  "session_id" varchar(255),
  
  -- Event classification
  "event_type" security_event_type NOT NULL,
  "severity" alert_severity NOT NULL DEFAULT 'low',
  "risk_score" integer DEFAULT 0, -- 0-100 risk assessment
  
  -- Event details
  "event_description" text NOT NULL,
  "event_source" varchar(100), -- 'auth', 'permission', 'session', 'api', 'manual'
  "resource_accessed" varchar(255),
  "operation_performed" varchar(100),
  
  -- Request context
  "ip_address" inet,
  "user_agent" text,
  "device_fingerprint" varchar(255),
  "device_type" varchar(50),
  "endpoint" varchar(255),
  "method" varchar(10),
  "status_code" integer,
  "response_time_ms" integer,
  
  -- Geographic context
  "location_country" varchar(10),
  "location_region" varchar(100),
  "location_city" varchar(100),
  "is_geo_anomaly" boolean DEFAULT false,
  
  -- Temporal context
  "timestamp" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "is_time_anomaly" boolean DEFAULT false,
  "is_weekend" boolean DEFAULT false,
  "is_business_hours" boolean DEFAULT true,
  
  -- Detection context
  "detection_rules" jsonb DEFAULT '[]', -- Rules that triggered this event
  "anomaly_indicators" jsonb DEFAULT '{}', -- Specific anomaly details
  "correlation_id" varchar(255), -- For grouping related events
  
  -- Response tracking
  "auto_response_triggered" boolean DEFAULT false,
  "response_actions" jsonb DEFAULT '[]',
  "admin_notified" boolean DEFAULT false,
  "resolved" boolean DEFAULT false,
  "resolved_at" timestamp,
  "resolved_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Additional metadata
  "metadata" jsonb DEFAULT '{}',
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Security rules table for configurable detection rules
CREATE TABLE IF NOT EXISTS "security_rules" (
  "id" SERIAL PRIMARY KEY,
  "rule_name" varchar(255) NOT NULL,
  "rule_description" text,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Rule configuration
  "event_types" text[] NOT NULL, -- Event types this rule applies to
  "conditions" jsonb NOT NULL, -- Rule conditions (JSON query)
  "threshold_config" jsonb DEFAULT '{}', -- Thresholds and time windows
  
  -- Response configuration
  "severity" alert_severity NOT NULL DEFAULT 'medium',
  "auto_response_actions" jsonb DEFAULT '[]',
  "notification_config" jsonb DEFAULT '{}',
  
  -- Rule status
  "is_active" boolean DEFAULT true,
  "priority" integer DEFAULT 0,
  "last_triggered" timestamp,
  "trigger_count" integer DEFAULT 0,
  
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- User behavior baselines for anomaly detection
CREATE TABLE IF NOT EXISTS "user_behavior_baselines" (
  "id" SERIAL PRIMARY KEY,
  "user_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Temporal patterns
  "typical_login_hours" jsonb DEFAULT '{}', -- Hour distribution
  "typical_login_days" jsonb DEFAULT '{}', -- Day of week distribution
  "average_session_duration" integer DEFAULT 0, -- in seconds
  "typical_locations" jsonb DEFAULT '[]', -- Common countries/cities
  
  -- Device patterns
  "known_devices" jsonb DEFAULT '[]', -- Device fingerprints
  "typical_device_types" jsonb DEFAULT '{}', -- Device type distribution
  "known_ip_ranges" jsonb DEFAULT '[]', -- Common IP addresses/ranges
  
  -- Activity patterns
  "typical_endpoints" jsonb DEFAULT '{}', -- Endpoint access patterns
  "average_requests_per_hour" numeric(10,2) DEFAULT 0,
  "typical_operations" jsonb DEFAULT '{}', -- Operation type distribution
  
  -- Risk indicators
  "failed_login_rate" numeric(5,4) DEFAULT 0, -- Percentage of failed logins
  "permission_denied_rate" numeric(5,4) DEFAULT 0, -- Percentage of denied requests
  "anomaly_score" integer DEFAULT 0, -- 0-100 baseline anomaly score
  
  -- Baseline metadata
  "baseline_period_start" timestamp NOT NULL,
  "baseline_period_end" timestamp NOT NULL,
  "sample_size" integer DEFAULT 0, -- Number of events in baseline
  "confidence_score" integer DEFAULT 0, -- 0-100 confidence in baseline
  
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE("user_id", "company_id")
);

-- Real-time monitoring alerts
CREATE TABLE IF NOT EXISTS "security_alerts" (
  "id" SERIAL PRIMARY KEY,
  "alert_id" varchar(255) NOT NULL UNIQUE,
  "security_event_id" integer REFERENCES "security_events"("id") ON DELETE CASCADE,
  "user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Alert details
  "alert_type" varchar(100) NOT NULL,
  "severity" alert_severity NOT NULL,
  "title" varchar(255) NOT NULL,
  "description" text NOT NULL,
  "recommendation" text,
  
  -- Alert status
  "status" varchar(50) DEFAULT 'open', -- 'open', 'investigating', 'resolved', 'false_positive'
  "assigned_to" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "acknowledged" boolean DEFAULT false,
  "acknowledged_at" timestamp,
  "acknowledged_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Response tracking
  "auto_response_applied" boolean DEFAULT false,
  "manual_response_required" boolean DEFAULT false,
  "escalated" boolean DEFAULT false,
  "escalated_at" timestamp,
  
  -- Resolution
  "resolved_at" timestamp,
  "resolved_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "resolution_notes" text,
  
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Automated security responses log
CREATE TABLE IF NOT EXISTS "security_responses" (
  "id" SERIAL PRIMARY KEY,
  "response_id" varchar(255) NOT NULL UNIQUE,
  "security_event_id" integer REFERENCES "security_events"("id") ON DELETE CASCADE,
  "security_alert_id" integer REFERENCES "security_alerts"("id") ON DELETE SET NULL,
  "triggered_by_rule_id" integer REFERENCES "security_rules"("id") ON DELETE SET NULL,
  
  -- Response details
  "response_action" security_response_action NOT NULL,
  "action_description" text NOT NULL,
  "target_user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "target_session_id" varchar(255),
  "target_ip_address" inet,
  
  -- Response configuration
  "action_parameters" jsonb DEFAULT '{}',
  "duration_seconds" integer, -- For temporary actions
  "expires_at" timestamp,
  
  -- Execution tracking
  "executed" boolean DEFAULT false,
  "executed_at" timestamp,
  "execution_result" text,
  "execution_error" text,
  
  -- Reversal tracking
  "reversible" boolean DEFAULT false,
  "reversed" boolean DEFAULT false,
  "reversed_at" timestamp,
  "reversed_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Rate limiting tracking
CREATE TABLE IF NOT EXISTS "rate_limit_violations" (
  "id" SERIAL PRIMARY KEY,
  "user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "ip_address" inet NOT NULL,
  "endpoint" varchar(255),
  "method" varchar(10),
  
  -- Rate limit details
  "limit_type" varchar(50) NOT NULL, -- 'per_user', 'per_ip', 'per_endpoint'
  "limit_window" integer NOT NULL, -- Window in seconds
  "limit_threshold" integer NOT NULL, -- Max requests per window
  "actual_requests" integer NOT NULL, -- Actual requests made
  
  -- Violation context
  "violation_timestamp" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "user_agent" text,
  "session_id" varchar(255),
  
  -- Response
  "blocked" boolean DEFAULT true,
  "block_duration" integer, -- Block duration in seconds
  "block_expires_at" timestamp,
  
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS "idx_security_events_user_id" ON "security_events"("user_id");
CREATE INDEX IF NOT EXISTS "idx_security_events_company_id" ON "security_events"("company_id");
CREATE INDEX IF NOT EXISTS "idx_security_events_event_type" ON "security_events"("event_type");
CREATE INDEX IF NOT EXISTS "idx_security_events_severity" ON "security_events"("severity");
CREATE INDEX IF NOT EXISTS "idx_security_events_timestamp" ON "security_events"("timestamp");
CREATE INDEX IF NOT EXISTS "idx_security_events_ip_address" ON "security_events"("ip_address");
CREATE INDEX IF NOT EXISTS "idx_security_events_correlation_id" ON "security_events"("correlation_id");

CREATE INDEX IF NOT EXISTS "idx_security_rules_company_id" ON "security_rules"("company_id");
CREATE INDEX IF NOT EXISTS "idx_security_rules_active" ON "security_rules"("is_active");
CREATE INDEX IF NOT EXISTS "idx_security_rules_event_types" ON "security_rules" USING GIN("event_types");

CREATE INDEX IF NOT EXISTS "idx_user_behavior_baselines_user_id" ON "user_behavior_baselines"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_behavior_baselines_company_id" ON "user_behavior_baselines"("company_id");

CREATE INDEX IF NOT EXISTS "idx_security_alerts_user_id" ON "security_alerts"("user_id");
CREATE INDEX IF NOT EXISTS "idx_security_alerts_company_id" ON "security_alerts"("company_id");
CREATE INDEX IF NOT EXISTS "idx_security_alerts_status" ON "security_alerts"("status");
CREATE INDEX IF NOT EXISTS "idx_security_alerts_severity" ON "security_alerts"("severity");

CREATE INDEX IF NOT EXISTS "idx_security_responses_event_id" ON "security_responses"("security_event_id");
CREATE INDEX IF NOT EXISTS "idx_security_responses_action" ON "security_responses"("response_action");
CREATE INDEX IF NOT EXISTS "idx_security_responses_executed" ON "security_responses"("executed");

CREATE INDEX IF NOT EXISTS "idx_rate_limit_violations_ip" ON "rate_limit_violations"("ip_address");
CREATE INDEX IF NOT EXISTS "idx_rate_limit_violations_user_id" ON "rate_limit_violations"("user_id");
CREATE INDEX IF NOT EXISTS "idx_rate_limit_violations_timestamp" ON "rate_limit_violations"("violation_timestamp");

-- Insert default security rules
INSERT INTO "security_rules" (
  "rule_name", "rule_description", "event_types", "conditions", 
  "threshold_config", "severity", "auto_response_actions"
) VALUES 
-- Brute force detection
('Brute Force Login Attempts', 'Detect multiple failed login attempts', 
 ARRAY['login_failure'], 
 '{"ip_address": {"exists": true}}',
 '{"max_attempts": 5, "time_window": 300, "block_duration": 900}',
 'high',
 '["block_ip", "alert_admin"]'
),

-- Suspicious geographic activity
('Geographic Anomaly Detection', 'Detect logins from unusual locations',
 ARRAY['login_success'],
 '{"is_geo_anomaly": true}',
 '{"confidence_threshold": 0.8}',
 'medium',
 '["require_mfa", "alert_admin"]'
),

-- Unusual time access
('Off-Hours Access Detection', 'Detect access during unusual hours',
 ARRAY['login_success', 'data_access'],
 '{"is_time_anomaly": true, "is_business_hours": false}',
 '{"risk_threshold": 70}',
 'medium',
 '["log_only", "alert_admin"]'
),

-- Privilege escalation attempts
('Privilege Escalation Detection', 'Detect attempts to access unauthorized resources',
 ARRAY['permission_denied'],
 '{"risk_score": {"gte": 80}}',
 '{"max_attempts": 3, "time_window": 600}',
 'high',
 '["require_fresh_auth", "alert_admin"]'
),

-- Bulk data operations
('Bulk Data Access Detection', 'Detect large-scale data operations',
 ARRAY['data_export', 'bulk_operation'],
 '{"operation_performed": {"in": ["export", "bulk_download", "mass_update"]}}',
 '{"volume_threshold": 1000}',
 'medium',
 '["alert_admin", "require_mfa"]'
),

-- Rate limiting violations
('Rate Limit Violation Detection', 'Detect API rate limit violations',
 ARRAY['rate_limit_exceeded'],
 '{"status_code": 429}',
 '{"violations_per_hour": 10}',
 'low',
 '["block_ip"]'
);

-- Function to generate unique event ID
CREATE OR REPLACE FUNCTION generate_event_id()
RETURNS varchar AS $$
BEGIN
  RETURN 'evt_' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDD') || '_' || 
         LPAD(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::text, 10, '0') || '_' ||
         LPAD(floor(random() * 10000)::text, 4, '0');
END;
$$ LANGUAGE plpgsql;

-- Function to calculate risk score based on multiple factors
CREATE OR REPLACE FUNCTION calculate_risk_score(
  p_event_type security_event_type,
  p_is_geo_anomaly boolean DEFAULT false,
  p_is_time_anomaly boolean DEFAULT false,
  p_failed_attempts integer DEFAULT 0,
  p_device_trust_score integer DEFAULT 50
) RETURNS integer AS $$
DECLARE
  base_score integer := 0;
  risk_score integer := 0;
BEGIN
  -- Base score by event type
  CASE p_event_type
    WHEN 'login_failure' THEN base_score := 20;
    WHEN 'permission_denied' THEN base_score := 30;
    WHEN 'suspicious_activity' THEN base_score := 60;
    WHEN 'anomaly_detected' THEN base_score := 50;
    WHEN 'brute_force_attempt' THEN base_score := 80;
    WHEN 'session_hijack_attempt' THEN base_score := 90;
    WHEN 'privilege_escalation' THEN base_score := 85;
    ELSE base_score := 10;
  END CASE;
  
  risk_score := base_score;
  
  -- Add anomaly factors
  IF p_is_geo_anomaly THEN
    risk_score := risk_score + 25;
  END IF;
  
  IF p_is_time_anomaly THEN
    risk_score := risk_score + 15;
  END IF;
  
  -- Add failed attempts factor
  risk_score := risk_score + (p_failed_attempts * 5);
  
  -- Adjust for device trust
  risk_score := risk_score + (50 - p_device_trust_score) / 2;
  
  -- Cap at 100
  IF risk_score > 100 THEN
    risk_score := 100;
  END IF;
  
  RETURN risk_score;
END;
$$ LANGUAGE plpgsql;
