-- Migration to add partner_reference_code column to partners table
-- This column will store company-specific partner identifiers

-- Add partner_reference_code column to partners table
ALTER TABLE "partners" 
  ADD COLUMN IF NOT EXISTS "partner_reference_code" TEXT;

-- Create an index for faster lookups by partner_reference_code
CREATE INDEX IF NOT EXISTS idx_partners_reference_code ON partners(partner_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN partners.partner_reference_code IS 'Company-specific partner identifier string (e.g., GS-001) that is unique within each company';
