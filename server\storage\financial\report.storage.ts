import { db } from '../../db';
import { eq, and, gte, lte, sql } from 'drizzle-orm';
import { transactions, loans, collections, payments, expenses } from '@shared/schema';
import errorLogger from '../../utils/errorLogger';
import { IReportStorage } from './interfaces';

export class ReportStorage implements IReportStorage {
  async getProfitLossReport(
    companyId: number, 
    dateRange: { startDate: string, endDate: string }
  ): Promise<any> {
    try {
      // Get income (payments received)
      const [incomeResult] = await db.select({
        total: sql`SUM(CAST(${payments.amount} AS DECIMAL(10,2)))`
      })
        .from(payments)
        .where(
          and(
            eq(payments.company_id, companyId),
            gte(payments.payment_date, dateRange.startDate),
            lte(payments.payment_date, dateRange.endDate)
          )
        );

      // Get expenses
      const [expensesResult] = await db.select({
        total: sql`SUM(CAST(${expenses.amount} AS DECIMAL(10,2)))`
      })
        .from(expenses)
        .where(
          and(
            eq(expenses.company_id, companyId),
            gte(expenses.expense_date, dateRange.startDate),
            lte(expenses.expense_date, dateRange.endDate)
          )
        );

      // Get loan disbursements (outgoing)
      const [loansResult] = await db.select({
        total: sql`SUM(CAST(${loans.amount} AS DECIMAL(10,2)))`
      })
        .from(loans)
        .where(
          and(
            eq(loans.company_id, companyId),
            gte(loans.start_date, dateRange.startDate),
            lte(loans.start_date, dateRange.endDate)
          )
        );

      const income = incomeResult.total ? parseFloat(incomeResult.total.toString()) : 0;
      const expenses = expensesResult.total ? parseFloat(expensesResult.total.toString()) : 0;
      const loanDisbursements = loansResult.total ? parseFloat(loansResult.total.toString()) : 0;

      // Calculate profit/loss
      const profit = income - expenses;

      // Get income breakdown by category
      const incomeBreakdown = await this.getIncomeBreakdown(companyId, dateRange);
      
      // Get expense breakdown by category
      const expenseBreakdown = await this.getExpenseBreakdown(companyId, dateRange);

      return {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        income,
        expenses,
        loanDisbursements,
        profit,
        incomeBreakdown,
        expenseBreakdown
      };
    } catch (error) {
      errorLogger.logError(`Error generating profit/loss report for company id=${companyId}`, 'profit-loss-report', error as Error);
      throw error;
    }
  }

  async getCashFlowReport(
    companyId: number, 
    dateRange: { startDate: string, endDate: string }
  ): Promise<any> {
    try {
      // Get all transactions in the date range
      const transactions = await db.select()
        .from(this.transactions)
        .where(
          and(
            eq(this.transactions.company_id, companyId),
            gte(this.transactions.transaction_date, dateRange.startDate),
            lte(this.transactions.transaction_date, dateRange.endDate)
          )
        );

      // Group transactions by date
      const transactionsByDate: Record<string, { credits: number, debits: number }> = {};
      
      for (const transaction of transactions) {
        const date = transaction.transaction_date.split('T')[0];
        
        if (!transactionsByDate[date]) {
          transactionsByDate[date] = { credits: 0, debits: 0 };
        }
        
        const amount = parseFloat(transaction.amount);
        
        if (transaction.transaction_type === 'credit') {
          transactionsByDate[date].credits += amount;
        } else if (transaction.transaction_type === 'debit') {
          transactionsByDate[date].debits += amount;
        }
      }

      // Convert to array and calculate net
      const cashFlowData = Object.entries(transactionsByDate).map(([date, data]) => ({
        date,
        credits: data.credits,
        debits: data.debits,
        net: data.credits - data.debits
      }));

      // Sort by date
      cashFlowData.sort((a, b) => a.date.localeCompare(b.date));

      // Calculate running balance
      let runningBalance = 0;
      const cashFlowWithBalance = cashFlowData.map(day => {
        runningBalance += day.net;
        return {
          ...day,
          balance: runningBalance
        };
      });

      return {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        cashFlow: cashFlowWithBalance
      };
    } catch (error) {
      errorLogger.logError(`Error generating cash flow report for company id=${companyId}`, 'cash-flow-report', error as Error);
      throw error;
    }
  }

  async getCollectionReport(
    companyId: number, 
    dateRange: { startDate: string, endDate: string }
  ): Promise<any> {
    try {
      // Get all collections in the date range
      const allCollections = await db.select()
        .from(collections)
        .where(
          and(
            eq(collections.company_id, companyId),
            gte(collections.scheduled_date, dateRange.startDate),
            lte(collections.scheduled_date, dateRange.endDate)
          )
        );

      // Count collections by status
      const collectionsByStatus: Record<string, number> = {
        pending: 0,
        in_progress: 0,
        completed: 0,
        failed: 0,
        cancelled: 0
      };

      let totalAmount = 0;
      let collectedAmount = 0;

      for (const collection of allCollections) {
        const status = collection.status;
        collectionsByStatus[status] = (collectionsByStatus[status] || 0) + 1;
        
        const amount = parseFloat(collection.amount);
        totalAmount += amount;
        
        if (status === 'completed') {
          collectedAmount += amount;
        }
      }

      // Calculate collection rate
      const collectionRate = totalAmount > 0 ? (collectedAmount / totalAmount) * 100 : 0;

      // Get collections by day
      const collectionsByDay = await this.getCollectionsByDay(companyId, dateRange);

      return {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        totalCollections: allCollections.length,
        collectionsByStatus,
        totalAmount,
        collectedAmount,
        collectionRate,
        collectionsByDay
      };
    } catch (error) {
      errorLogger.logError(`Error generating collection report for company id=${companyId}`, 'collection-report', error as Error);
      throw error;
    }
  }

  private async getIncomeBreakdown(
    companyId: number, 
    dateRange: { startDate: string, endDate: string }
  ): Promise<any[]> {
    try {
      // In a real implementation, we would categorize income by source
      // For now, we'll just return a simple breakdown
      
      // Get payments
      const [paymentsResult] = await db.select({
        total: sql`SUM(CAST(${payments.amount} AS DECIMAL(10,2)))`
      })
        .from(payments)
        .where(
          and(
            eq(payments.company_id, companyId),
            gte(payments.payment_date, dateRange.startDate),
            lte(payments.payment_date, dateRange.endDate)
          )
        );

      const paymentsTotal = paymentsResult.total ? parseFloat(paymentsResult.total.toString()) : 0;

      return [
        { category: 'Loan Payments', amount: paymentsTotal }
      ];
    } catch (error) {
      errorLogger.logError(`Error generating income breakdown for company id=${companyId}`, 'income-breakdown', error as Error);
      return [];
    }
  }

  private async getExpenseBreakdown(
    companyId: number, 
    dateRange: { startDate: string, endDate: string }
  ): Promise<any[]> {
    try {
      // Get expenses grouped by category
      const expensesByCategory = await db.select({
        category: expenses.category,
        total: sql`SUM(CAST(${expenses.amount} AS DECIMAL(10,2)))`
      })
        .from(expenses)
        .where(
          and(
            eq(expenses.company_id, companyId),
            gte(expenses.expense_date, dateRange.startDate),
            lte(expenses.expense_date, dateRange.endDate)
          )
        )
        .groupBy(expenses.category);

      return expensesByCategory.map(item => ({
        category: item.category,
        amount: parseFloat(item.total.toString())
      }));
    } catch (error) {
      errorLogger.logError(`Error generating expense breakdown for company id=${companyId}`, 'expense-breakdown', error as Error);
      return [];
    }
  }

  private async getCollectionsByDay(
    companyId: number, 
    dateRange: { startDate: string, endDate: string }
  ): Promise<any[]> {
    try {
      // Get completed collections grouped by day
      const collectionsByDay = await db.select({
        date: sql`DATE(${collections.collection_date})`,
        total: sql`SUM(CAST(${collections.amount} AS DECIMAL(10,2)))`
      })
        .from(collections)
        .where(
          and(
            eq(collections.company_id, companyId),
            eq(collections.status, 'completed'),
            gte(collections.collection_date, dateRange.startDate),
            lte(collections.collection_date, dateRange.endDate)
          )
        )
        .groupBy(sql`DATE(${collections.collection_date})`);

      return collectionsByDay.map(item => ({
        date: item.date,
        amount: parseFloat(item.total.toString())
      }));
    } catch (error) {
      errorLogger.logError(`Error generating collections by day for company id=${companyId}`, 'collections-by-day', error as Error);
      return [];
    }
  }

  // For TypeScript compatibility in the class
  private transactions = transactions;
}
