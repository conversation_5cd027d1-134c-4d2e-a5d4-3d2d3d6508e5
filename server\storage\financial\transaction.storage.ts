import { db } from '../../db';
import { eq, and, desc, gte, lte, sql, like } from 'drizzle-orm';
import { transactions, accounts } from '@shared/schema';
import { Transaction, InsertTransaction } from '@shared/schema';
import errorLogger from '../../utils/errorLogger';
import { ITransactionStorage } from './interfaces';

export class TransactionStorage implements ITransactionStorage {
  async getTransaction(id: number): Promise<Transaction | undefined> {
    try {
      const [transaction] = await db.select()
        .from(transactions)
        .where(eq(transactions.id, id));
      return transaction;
    } catch (error) {
      errorLogger.logError(`Error fetching transaction id=${id}`, 'transaction-fetch', error as Error);
      return undefined;
    }
  }

  async getTransactionsByCompany(
    companyId: number,
    dateRange?: { startDate: string, endDate: string },
    accountId?: number,
    transactionType?: string
  ): Promise<Transaction[]> {
    try {
      let query = db.select()
        .from(transactions)
        .where(eq(transactions.company_id, companyId));

      // Apply date range filter if provided
      if (dateRange) {
        query = query.where(
          and(
            gte(transactions.transaction_date, dateRange.startDate),
            lte(transactions.transaction_date, dateRange.endDate)
          )
        );
      }

      // Apply account filter if provided
      if (accountId) {
        query = query.where(eq(transactions.account_id, accountId));
      }

      // Apply transaction type filter if provided
      if (transactionType) {
        query = query.where(eq(transactions.transaction_type, transactionType));
      }

      // Order by transaction date descending
      query = query.orderBy(desc(transactions.transaction_date));

      const companyTransactions = await query;
      return companyTransactions;
    } catch (error) {
      errorLogger.logError(`Error fetching transactions for company id=${companyId}`, 'transaction-fetch', error as Error);
      return [];
    }
  }

  async getTransactionsByAccount(accountId: number, companyId: number): Promise<Transaction[]> {
    try {
      // First check if the account exists and belongs to the company
      const [account] = await db.select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, accountId),
            eq(accounts.company_id, companyId)
          )
        );

      if (!account) {
        return [];
      }

      const accountTransactions = await db.select()
        .from(transactions)
        .where(eq(transactions.account_id, accountId))
        .orderBy(desc(transactions.transaction_date));

      return accountTransactions;
    } catch (error) {
      errorLogger.logError(`Error fetching transactions for account id=${accountId}`, 'transaction-fetch', error as Error);
      return [];
    }
  }

  async createTransaction(transactionData: InsertTransaction): Promise<Transaction> {
    try {
      // Start a transaction
      return await db.transaction(async (tx) => {
        // Validate that the account exists and belongs to the company
        const [account] = await tx.select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, transactionData.account_id),
              eq(accounts.company_id, transactionData.company_id)
            )
          );

        if (!account) {
          throw new Error(`Account with id=${transactionData.account_id} not found for company id=${transactionData.company_id}`);
        }

        // Create the transaction
        const [transaction] = await tx.insert(transactions)
          .values(transactionData)
          .returning();

        // Update the account balance
        const currentBalance = parseFloat(account.current_balance);
        const transactionAmount = parseFloat(transactionData.amount);

        let newBalance: number;
        if (transactionData.transaction_type === 'credit') {
          newBalance = currentBalance + transactionAmount;
        } else if (transactionData.transaction_type === 'debit') {
          newBalance = currentBalance - transactionAmount;
        } else {
          throw new Error(`Invalid transaction type: ${transactionData.transaction_type}`);
        }

        // Update the account balance
        await tx.update(accounts)
          .set({ current_balance: newBalance.toString() })
          .where(eq(accounts.id, transactionData.account_id));

        return transaction;
      });
    } catch (error) {
      errorLogger.logError(`Error creating transaction`, 'transaction-create', error as Error);
      throw error;
    }
  }

  async updateTransaction(id: number, companyId: number, transactionData: Partial<InsertTransaction>): Promise<Transaction> {
    try {
      // First check if the transaction exists and belongs to the company
      const [existingTransaction] = await db.select()
        .from(transactions)
        .where(
          and(
            eq(transactions.id, id),
            eq(transactions.company_id, companyId)
          )
        );

      if (!existingTransaction) {
        throw new Error(`Transaction with id=${id} not found for company id=${companyId}`);
      }

      // Don't allow changing company_id or account_id
      delete transactionData.company_id;
      delete transactionData.account_id;

      // Don't allow changing transaction_type or amount as it would affect account balances
      // In a real implementation, we would need to handle this by adjusting the account balance
      delete transactionData.transaction_type;
      delete transactionData.amount;

      const [updatedTransaction] = await db.update(transactions)
        .set(transactionData)
        .where(eq(transactions.id, id))
        .returning();

      return updatedTransaction;
    } catch (error) {
      errorLogger.logError(`Error updating transaction id=${id}`, 'transaction-update', error as Error);
      throw error;
    }
  }

  async deleteTransaction(id: number, companyId: number): Promise<boolean> {
    try {
      // Start a transaction
      return await db.transaction(async (tx) => {
        // First check if the transaction exists and belongs to the company
        const [existingTransaction] = await tx.select()
          .from(transactions)
          .where(
            and(
              eq(transactions.id, id),
              eq(transactions.company_id, companyId)
            )
          );

        if (!existingTransaction) {
          return false;
        }

        // Get the account
        const [account] = await tx.select()
          .from(accounts)
          .where(eq(accounts.id, existingTransaction.account_id));

        if (!account) {
          throw new Error(`Account with id=${existingTransaction.account_id} not found`);
        }

        // Reverse the effect on the account balance
        const currentBalance = parseFloat(account.current_balance);
        const transactionAmount = parseFloat(existingTransaction.amount);

        let newBalance: number;
        if (existingTransaction.transaction_type === 'credit') {
          newBalance = currentBalance - transactionAmount;
        } else if (existingTransaction.transaction_type === 'debit') {
          newBalance = currentBalance + transactionAmount;
        } else {
          throw new Error(`Invalid transaction type: ${existingTransaction.transaction_type}`);
        }

        // Update the account balance
        await tx.update(accounts)
          .set({ current_balance: newBalance.toString() })
          .where(eq(accounts.id, existingTransaction.account_id));

        // Delete the transaction
        await tx.delete(transactions)
          .where(eq(transactions.id, id));

        return true;
      });
    } catch (error) {
      errorLogger.logError(`Error deleting transaction id=${id}`, 'transaction-delete', error as Error);
      return false;
    }
  }

  async getTransactionSummary(
    companyId: number,
    dateRange: { startDate: string, endDate: string }
  ): Promise<{ credits: string, debits: string, net: string }> {
    try {
      // Get the sum of all credits
      const [creditResult] = await db.select({
        total: sql`SUM(CAST(${transactions.amount} AS DECIMAL(10,2)))`
      })
        .from(transactions)
        .where(
          and(
            eq(transactions.company_id, companyId),
            eq(transactions.transaction_type, 'credit'),
            gte(transactions.transaction_date, dateRange.startDate),
            lte(transactions.transaction_date, dateRange.endDate)
          )
        );

      // Get the sum of all debits
      const [debitResult] = await db.select({
        total: sql`SUM(CAST(${transactions.amount} AS DECIMAL(10,2)))`
      })
        .from(transactions)
        .where(
          and(
            eq(transactions.company_id, companyId),
            eq(transactions.transaction_type, 'debit'),
            gte(transactions.transaction_date, dateRange.startDate),
            lte(transactions.transaction_date, dateRange.endDate)
          )
        );

      const credits = creditResult.total ? creditResult.total.toString() : '0';
      const debits = debitResult.total ? debitResult.total.toString() : '0';
      const net = (parseFloat(credits) - parseFloat(debits)).toString();

      return { credits, debits, net };
    } catch (error) {
      errorLogger.logError(`Error generating transaction summary for company id=${companyId}`, 'transaction-summary', error as Error);
      return { credits: '0', debits: '0', net: '0' };
    }
  }

  /**
   * Get the highest serial number for a given company and prefix (e.g., 'GS-T-')
   * Returns the highest serial as a number, or 0 if none found.
   * Only considers transactions from the specific company.
   */
  async getHighestTransactionSerial(companyId: number, prefix: string): Promise<number> {
    try {
      // Find the max serial for this company and prefix
      // transaction_reference_code is like 'GS-T-001', 'GS-T-002', ...
      const result = await db.select({ maxString: sql`MAX(${transactions.transaction_reference_code})` })
        .from(transactions)
        .where(
          and(
            eq(transactions.company_id, companyId),
            like(transactions.transaction_reference_code, `${prefix}%`)
          )
        );
      const maxString = result[0]?.maxString as string | undefined;
      if (!maxString) return 0;

      // Extract the serial part (e.g., 'GS-T-012' => 12)
      const match = maxString.match(/^(.*-T-)(\d{3})$/);
      if (match) {
        return parseInt(match[2], 10);
      }
      return 0;
    } catch (error) {
      errorLogger.logError(`Error getting highest transaction serial for company ${companyId} and prefix ${prefix}`, 'transaction-serial', error as Error);
      return 0;
    }
  }
}
