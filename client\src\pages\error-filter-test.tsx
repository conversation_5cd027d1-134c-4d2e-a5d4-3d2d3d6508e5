import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  detectBrowserExtensions, 
  shouldFilterError,
  isBrowserExtensionError,
  isExternalServiceError,
  isNetworkError
} from '@/lib/browserExtensionFilter';

/**
 * Test page for browser extension error filtering
 * This page helps verify that the error filtering system is working correctly
 */
export default function ErrorFilterTest() {
  const [extensions, setExtensions] = React.useState<string[]>([]);
  const [testResults, setTestResults] = React.useState<Array<{
    error: string;
    filtered: boolean;
    category: string;
  }>>([]);

  React.useEffect(() => {
    // Detect browser extensions on component mount
    const detectedExtensions = detectBrowserExtensions();
    setExtensions(detectedExtensions);
  }, []);

  const testErrors = [
    {
      error: "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received",
      category: "Browser Extension"
    },
    {
      error: "Extension context invalidated",
      category: "Browser Extension"
    },
    {
      error: "chrome-extension://abc123/content.js:1 Uncaught Error",
      category: "Browser Extension"
    },
    {
      error: "Failed to fetch from replit.com/api/metrics",
      category: "External Service"
    },
    {
      error: "NetworkError: Failed to fetch",
      category: "Network Error"
    },
    {
      error: "Application error: Invalid user input",
      category: "Application Error"
    },
    {
      error: "TypeError: Cannot read property 'name' of undefined",
      category: "Application Error"
    }
  ];

  const runFilterTests = () => {
    const results = testErrors.map(({ error, category }) => ({
      error,
      category,
      filtered: shouldFilterError(error)
    }));
    setTestResults(results);
  };

  const triggerTestErrors = () => {
    // Trigger various types of errors for testing
    console.error("Test application error - should appear");
    console.error("A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received");
    console.error("chrome-extension://test/script.js: Test extension error");
    console.warn("Failed to fetch from replit.com - test external service error");
    console.error("NetworkError: Test network error");
  };

  const toggleFiltering = () => {
    const current = localStorage.getItem('filter-extension-errors');
    if (current === 'true') {
      localStorage.removeItem('filter-extension-errors');
      alert('Error filtering disabled. Reload the page to see all errors.');
    } else {
      localStorage.setItem('filter-extension-errors', 'true');
      alert('Error filtering enabled. Reload the page to filter extension errors.');
    }
  };

  const isFilteringEnabled = localStorage.getItem('filter-extension-errors') === 'true' || 
                            process.env.NODE_ENV === 'production';

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Browser Extension Error Filter Test</h1>
        <p className="text-muted-foreground mt-2">
          Test and verify the browser extension error filtering system
        </p>
      </div>

      {/* Extension Detection */}
      <Card>
        <CardHeader>
          <CardTitle>Browser Extension Detection</CardTitle>
          <CardDescription>
            Detected browser extensions and extension APIs
          </CardDescription>
        </CardHeader>
        <CardContent>
          {extensions.length > 0 ? (
            <div className="space-y-2">
              {extensions.map((ext, index) => (
                <Badge key={index} variant="secondary">
                  {ext}
                </Badge>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">No browser extensions detected</p>
          )}
        </CardContent>
      </Card>

      {/* Filter Status */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Status</CardTitle>
          <CardDescription>
            Current error filtering configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Error Filtering:</span>
            <Badge variant={isFilteringEnabled ? "default" : "destructive"}>
              {isFilteringEnabled ? "Enabled" : "Disabled"}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span>Environment:</span>
            <Badge variant="outline">
              {process.env.NODE_ENV || 'development'}
            </Badge>
          </div>
          <Button onClick={toggleFiltering} variant="outline">
            {isFilteringEnabled ? "Disable" : "Enable"} Filtering
          </Button>
        </CardContent>
      </Card>

      {/* Error Filter Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Error Filter Tests</CardTitle>
          <CardDescription>
            Test various error patterns against the filtering system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={runFilterTests}>Run Filter Tests</Button>
          
          {testResults.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-semibold">Test Results:</h4>
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex-1">
                    <p className="text-sm font-medium">{result.category}</p>
                    <p className="text-xs text-muted-foreground truncate">
                      {result.error}
                    </p>
                  </div>
                  <Badge variant={result.filtered ? "default" : "destructive"}>
                    {result.filtered ? "Filtered" : "Not Filtered"}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Console Test */}
      <Card>
        <CardHeader>
          <CardTitle>Console Error Test</CardTitle>
          <CardDescription>
            Trigger test errors to verify console filtering (check browser console)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={triggerTestErrors} variant="outline">
            Trigger Test Errors
          </Button>
          <p className="text-sm text-muted-foreground mt-2">
            Click the button and check the browser console. Extension-related errors should be filtered if filtering is enabled.
          </p>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <ol className="list-decimal list-inside space-y-1">
            <li>Check the "Browser Extension Detection" section to see detected extensions</li>
            <li>Run the "Error Filter Tests" to see which errors would be filtered</li>
            <li>Use "Trigger Test Errors" to test console filtering (check browser console)</li>
            <li>Toggle filtering on/off to compare behavior</li>
            <li>Reload the page after changing filter settings</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
}
