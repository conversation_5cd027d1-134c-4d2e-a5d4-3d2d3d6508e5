import { QueryClient, QueryFunction, QueryCache, MutationCache } from "@tanstack/react-query";
import errorLogger from './errorLogger';

// Check if we're in the browser environment
const isBrowser = typeof window !== 'undefined';

// Track if we've already shown an auth error to avoid spamming the user
let authErrorShown = false;

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    let errorData;

    try {
      // Try to parse the response as JSON
      errorData = await res.json();
    } catch (e) {
      // If it's not JSON, use the text
      const text = await res.text() || res.statusText;
      errorData = { message: text };
    }

    // Handle specific error types
    if (res.status === 401) {
      // Only redirect for 401 errors if we're NOT on the login page
      // Login page 401 errors are for invalid credentials, not expired tokens
      const currentPath = isBrowser ? window.location.pathname : '';
      const isLoginPage = currentPath === '/login' || currentPath === '/register';

      if (!authErrorShown && isBrowser && !isLoginPage) {
        authErrorShown = true;

        // Immediately clear the token and alert the user
        console.log('Authentication expired. Redirecting to login page...');
        localStorage.removeItem('auth_token');

        // Redirect immediately to prevent further API calls with expired token
        window.location.href = '/login';
      } else if (isLoginPage) {
        // On login page, 401 errors are for invalid credentials, not expired tokens
        // Don't clear token or redirect, just let the error propagate normally
        console.log('Login failed: Invalid credentials');
      }
    }

    // Throw the actual error data from the server
    throw errorData;
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  // Get auth token from localStorage
  const token = isBrowser ? localStorage.getItem('auth_token') : null;
  const headers: Record<string, string> = {};

  // Add cache-busting headers for financial data accuracy
  headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
  headers['Pragma'] = 'no-cache';
  headers['Expires'] = '0';

  // Add content type if we have data
  if (data) {
    headers['Content-Type'] = 'application/json';
  }

  // Add auth token if we have it
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Add cache-busting parameter to URL for GET requests
  let finalUrl = url;
  if (method === 'GET') {
    const separator = url.includes('?') ? '&' : '?';
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(7);
    finalUrl = `${url}${separator}_t=${timestamp}&_r=${random}`;
  }

  try {
    const res = await fetch(finalUrl, {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined,
      credentials: "include",
      // Disable browser caching
      cache: 'no-store',
    });

    if (!res.ok) {
      await throwIfResNotOk(res);
    }

    return res;
  } catch (error) {
    // Log the error with our error logger
    errorLogger.error(
      `API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'apiRequest',
      { method, url: finalUrl, error }
    );

    // If it's already a structured error object from our throwIfResNotOk function, just rethrow it
    if (error && typeof error === 'object' && ('message' in error || 'field' in error || 'error' in error)) {
      throw error;
    }

    // Otherwise, wrap it in a standard error object
    throw { message: error instanceof Error ? error.message : 'Unknown error' };
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Get auth token from localStorage
    const token = isBrowser ? localStorage.getItem('auth_token') : null;
    const headers: Record<string, string> = {};

    // Add cache-busting headers for financial data accuracy
    headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
    headers['Pragma'] = 'no-cache';
    headers['Expires'] = '0';

    // Add auth token if we have it
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      let url = queryKey[0] as string;

      // Add cache-busting parameters
      const separator = url.includes('?') ? '&' : '?';
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(7);
      url = `${url}${separator}_t=${timestamp}&_r=${random}`;

      const res = await fetch(url, {
        method: "GET",
        headers,
        credentials: "include",
        // Disable browser caching
        cache: 'no-store',
      });

      if (unauthorizedBehavior === "returnNull" && res.status === 401) {
        return null;
      }

      if (!res.ok) {
        await throwIfResNotOk(res);
        return null; // This line won't be reached, but TypeScript needs it
      }

      return await res.json();
    } catch (error) {
      // Log the error with our error logger
      errorLogger.error(
        `Query failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'queryFn',
        { queryKey, error }
      );

      // If it's a 401 error and we're configured to return null
      if (error && typeof error === 'object' && 'status' in error && error.status === 401) {
        if (unauthorizedBehavior === "returnNull") {
          return null;
        }
      }

      // If it's already a structured error object from our throwIfResNotOk function, just rethrow it
      if (error && typeof error === 'object' && ('message' in error || 'field' in error || 'error' in error)) {
        throw error;
      }

      // Otherwise, wrap it in a standard error object
      throw { message: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

// Create a custom logging function to track query errors
const logQueryError = (error: unknown) => {
  const message = error instanceof Error ? error.message : String(error);
  errorLogger.error(`React Query error: ${message}`, 'react-query', error);
  // Return the error so the query can continue with its normal error flow
  return error;
};

// Create a new query client with NO CACHING for financial data accuracy
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      // DISABLE ALL CACHING FOR FINANCIAL DATA ACCURACY
      staleTime: 0, // Data is immediately stale - always refetch
      cacheTime: 0, // Don't cache data at all - remove from memory immediately
      refetchOnMount: 'always', // Always refetch when component mounts
      refetchOnWindowFocus: true, // Refetch when window gains focus
      refetchOnReconnect: true, // Refetch when network reconnects
      refetchInterval: false, // Don't auto-refetch on interval
      retry: 1, // Minimal retry for network issues
    },
    mutations: {
      retry: 1, // Minimal retry for mutations
    },
  },
  // Global error handler
  queryCache: new QueryCache({
    onError: (error, query) => {
      const queryKey = query.queryKey || [];
      const queryKeyString = JSON.stringify(queryKey);
      errorLogger.error(`Query error for key: ${queryKeyString}`, 'react-query', error);
    },
  }),
  mutationCache: new MutationCache({
    onError: (error, _variables, _context, mutation) => {
      const mutationKey = mutation.options.mutationKey || [];
      const mutationKeyString = JSON.stringify(mutationKey);
      errorLogger.error(`Mutation error for key: ${mutationKeyString}`, 'react-query', error);
    },
  }),
});
