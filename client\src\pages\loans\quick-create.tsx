import React, { useEffect } from "react";
import { useLocation } from "wouter";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { QuickLoanForm } from "@/components/loan/QuickLoanForm";
import { useCompany } from "@/lib/companies";
import { useAuth } from "@/lib/auth";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export default function QuickLoanPage() {
  const [_, setLocation] = useLocation();
  // Get company ID from both sources to ensure we have the most up-to-date value
  const { currentCompany } = useCompany();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();

  // Use user.company_id as the primary source of truth, falling back to currentCompany
  const companyId = user?.company_id || currentCompany?.company_id;

  console.log('QuickLoanPage - Company ID sources:', {
    userCompanyId: user?.company_id,
    currentCompanyId: currentCompany?.company_id,
    effectiveCompanyId: companyId
  });

  // Extract frequency from URL query parameters
  const urlParams = new URLSearchParams(window.location.search);
  const frequency = urlParams.get('frequency') as 'daily' | 'weekly' | 'monthly' || 'daily';

  // Get the title based on the frequency
  const getTitle = () => {
    switch(frequency) {
      case 'daily': return 'Create Daily Loan';
      case 'weekly': return 'Create Weekly Loan';
      case 'monthly': return 'Create Monthly Loan';
      default: return 'Create Loan';
    }
  };

  // Get the description based on the frequency
  const getDescription = () => {
    switch(frequency) {
      case 'daily': return 'Create a loan with daily payment schedule';
      case 'weekly': return 'Create a loan with weekly payment schedule';
      case 'monthly': return 'Create a loan with monthly payment schedule';
      default: return 'Create a new loan with automatic calculations';
    }
  };

  // Handle success
  const handleSuccess = () => {
    // Navigate back to the loans list
    setLocation('/loans');
  };

  // Handle cancel
  const handleCancel = () => {
    setLocation('/loans');
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setLocation('/loans')}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Loans
        </Button>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">{getTitle()}</h1>
          <p className="mt-1 text-sm text-gray-500">
            {getDescription()}
          </p>
        </div>
      </div>

      {companyId ? (
        <QuickLoanForm
          companyId={companyId}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
          presetFrequency={frequency}
        />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Company Required</CardTitle>
            <CardDescription>
              You need to select a company to create loans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Please select a company from the company selector in the top navigation.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}