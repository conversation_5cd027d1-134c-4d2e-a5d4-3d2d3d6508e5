/**
 * Migration Runner Utility
 * 
 * Provides standardized migration execution with error handling and logging
 */

import { withDatabasePool } from './database-connection.js';

/**
 * Execute a migration with standardized error handling and logging
 * @param {string} migrationName - Name of the migration for logging
 * @param {Function} migrationFunction - Async function that performs the migration
 * @param {Object} options - Migration options
 * @param {boolean} options.dryRun - If true, log what would be done without executing
 * @returns {Promise<void>}
 */
export async function runMigration(migrationName, migrationFunction, options = {}) {
  const { dryRun = false } = options;
  
  console.log(`\n=== ${migrationName} ===`);
  console.log(`Started at: ${new Date().toISOString()}`);
  
  if (dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }
  
  const startTime = Date.now();
  
  try {
    await withDatabasePool(async (pool) => {
      await migrationFunction(pool, { dryRun });
    });
    
    const duration = Date.now() - startTime;
    console.log(`✅ ${migrationName} completed successfully in ${duration}ms`);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ ${migrationName} failed after ${duration}ms`);
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    
    process.exit(1);
  }
}

/**
 * Execute multiple migrations in sequence
 * @param {Array} migrations - Array of migration objects with name and function
 * @param {Object} options - Migration options
 * @returns {Promise<void>}
 */
export async function runMigrations(migrations, options = {}) {
  console.log(`\n🚀 Running ${migrations.length} migrations...`);
  
  for (const migration of migrations) {
    await runMigration(migration.name, migration.function, options);
  }
  
  console.log(`\n🎉 All migrations completed successfully!`);
}

/**
 * Validate migration prerequisites
 * @param {Function} validationFunction - Function that validates prerequisites
 * @returns {Promise<void>}
 */
export async function validateMigrationPrerequisites(validationFunction) {
  console.log('🔍 Validating migration prerequisites...');
  
  try {
    await withDatabasePool(validationFunction);
    console.log('✅ Prerequisites validation passed');
  } catch (error) {
    console.error('❌ Prerequisites validation failed:', error.message);
    throw error;
  }
}

/**
 * Create a backup before running migration
 * @param {string} backupName - Name for the backup
 * @returns {Promise<void>}
 */
export async function createBackup(backupName) {
  console.log(`📦 Creating backup: ${backupName}`);
  // Note: Actual backup implementation would depend on your backup strategy
  // This is a placeholder for backup functionality
  console.log('⚠️  Backup functionality not implemented - ensure you have database backups');
}

/**
 * Log migration statistics
 * @param {Object} stats - Migration statistics
 */
export function logMigrationStats(stats) {
  console.log('\n📊 Migration Statistics:');
  Object.entries(stats).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
}
