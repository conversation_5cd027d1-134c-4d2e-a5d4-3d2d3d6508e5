import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { apiRequest } from '@/lib/api';
import { Loader2, Save, Eye, AlertCircle, CheckCircle } from 'lucide-react';

interface PrefixSettings {
  id?: number;
  company_id: number;
  loan_prefix: string;
  loan_start_number: number;
  collection_prefix: string;
  collection_start_number: number;
  customer_prefix: string;
  customer_start_number: number;
  partner_prefix: string;
  partner_start_number: number;
  agent_prefix: string;
  agent_start_number: number;
}

export default function CompanyPrefixSettings() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [settings, setSettings] = useState<PrefixSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPreview, setShowPreview] = useState(false);

  const companyId = user?.company_id;

  // Check if user has permission to manage prefix settings
  const canManage = user?.role === 'saas_admin' || user?.role === 'owner';

  useEffect(() => {
    if (companyId && canManage) {
      fetchPrefixSettings();
    }
  }, [companyId, canManage]);

  const fetchPrefixSettings = async () => {
    try {
      setLoading(true);
      const response = await apiRequest('GET', `/api/companies/${companyId}/prefix-settings`);
      
      if (response.ok) {
        const data = await response.json();
        if (data.data) {
          setSettings(data.data);
        } else {
          // No settings found, create default ones
          setSettings({
            company_id: companyId!,
            loan_prefix: 'LOAN',
            loan_start_number: 1,
            collection_prefix: 'COL',
            collection_start_number: 1,
            customer_prefix: 'CUST',
            customer_start_number: 1,
            partner_prefix: 'PART',
            partner_start_number: 1,
            agent_prefix: 'AGT',
            agent_start_number: 1,
          });
        }
      } else {
        throw new Error('Failed to fetch prefix settings');
      }
    } catch (error) {
      console.error('Error fetching prefix settings:', error);
      toast({
        title: "Error",
        description: "Failed to load prefix settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const validateSettings = (data: PrefixSettings): Record<string, string> => {
    const errors: Record<string, string> = {};

    // Validate prefixes
    const prefixFields = ['loan_prefix', 'collection_prefix', 'customer_prefix', 'partner_prefix', 'agent_prefix'];
    
    prefixFields.forEach(field => {
      const value = data[field as keyof PrefixSettings] as string;
      if (!value || value.length < 2 || value.length > 6) {
        errors[field] = 'Prefix must be between 2 and 6 characters';
      } else if (!/^[A-Z0-9]+$/.test(value)) {
        errors[field] = 'Prefix must contain only uppercase letters and numbers';
      }
    });

    // Validate start numbers
    const numberFields = ['loan_start_number', 'collection_start_number', 'customer_start_number', 'partner_start_number', 'agent_start_number'];
    
    numberFields.forEach(field => {
      const value = data[field as keyof PrefixSettings] as number;
      if (!value || value < 1 || value > 999999) {
        errors[field] = 'Start number must be between 1 and 999999';
      }
    });

    return errors;
  };

  const handleSave = async () => {
    if (!settings) return;

    const validationErrors = validateSettings(settings);
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before saving",
        variant: "destructive",
      });
      return;
    }

    try {
      setSaving(true);
      
      const method = settings.id ? 'PUT' : 'POST';
      const response = await apiRequest(method, `/api/companies/${companyId}/prefix-settings`, settings);

      if (response.ok) {
        const data = await response.json();
        setSettings(data.data || data);
        toast({
          title: "Success",
          description: "Prefix settings saved successfully",
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save prefix settings');
      }
    } catch (error) {
      console.error('Error saving prefix settings:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save prefix settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof PrefixSettings, value: string | number) => {
    if (!settings) return;
    
    setSettings({
      ...settings,
      [field]: value
    });
    
    // Clear error for this field
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: ''
      });
    }
  };

  const generatePreview = (prefix: string, startNumber: number, entityType: string) => {
    return `${prefix}-${String(startNumber).padStart(3, '0')} (${entityType})`;
  };

  if (!canManage) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You don't have permission to manage company prefix settings. Only owners and system administrators can access this page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading prefix settings...</span>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load prefix settings. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Company Prefix Settings</h1>
          <p className="text-muted-foreground">
            Configure reference code prefixes for all entity types in your company
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowPreview(!showPreview)}
          >
            <Eye className="h-4 w-4 mr-2" />
            {showPreview ? 'Hide' : 'Show'} Preview
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Settings
          </Button>
        </div>
      </div>

      {showPreview && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              Reference Code Preview
            </CardTitle>
            <CardDescription>
              Preview of how reference codes will look with current settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Loans</Label>
                <Badge variant="outline">{generatePreview(settings.loan_prefix, settings.loan_start_number, 'Loan')}</Badge>
              </div>
              <div className="space-y-2">
                <Label>Collections</Label>
                <Badge variant="outline">{generatePreview(settings.collection_prefix, settings.collection_start_number, 'Collection')}</Badge>
              </div>
              <div className="space-y-2">
                <Label>Customers</Label>
                <Badge variant="outline">{generatePreview(settings.customer_prefix, settings.customer_start_number, 'Customer')}</Badge>
              </div>
              <div className="space-y-2">
                <Label>Partners</Label>
                <Badge variant="outline">{generatePreview(settings.partner_prefix, settings.partner_start_number, 'Partner')}</Badge>
              </div>
              <div className="space-y-2">
                <Label>Agents</Label>
                <Badge variant="outline">{generatePreview(settings.agent_prefix, settings.agent_start_number, 'Agent')}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Loan Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Loan Settings</CardTitle>
            <CardDescription>Configure loan reference code format</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="loan_prefix">Loan Prefix</Label>
              <Input
                id="loan_prefix"
                value={settings.loan_prefix}
                onChange={(e) => handleInputChange('loan_prefix', e.target.value.toUpperCase())}
                placeholder="LOAN"
                className={errors.loan_prefix ? 'border-red-500' : ''}
              />
              {errors.loan_prefix && (
                <p className="text-sm text-red-500">{errors.loan_prefix}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="loan_start_number">Starting Number</Label>
              <Input
                id="loan_start_number"
                type="number"
                value={settings.loan_start_number}
                onChange={(e) => handleInputChange('loan_start_number', parseInt(e.target.value) || 1)}
                min="1"
                max="999999"
                className={errors.loan_start_number ? 'border-red-500' : ''}
              />
              {errors.loan_start_number && (
                <p className="text-sm text-red-500">{errors.loan_start_number}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Collection Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Collection Settings</CardTitle>
            <CardDescription>Configure collection reference code format</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="collection_prefix">Collection Prefix</Label>
              <Input
                id="collection_prefix"
                value={settings.collection_prefix}
                onChange={(e) => handleInputChange('collection_prefix', e.target.value.toUpperCase())}
                placeholder="COL"
                className={errors.collection_prefix ? 'border-red-500' : ''}
              />
              {errors.collection_prefix && (
                <p className="text-sm text-red-500">{errors.collection_prefix}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="collection_start_number">Starting Number</Label>
              <Input
                id="collection_start_number"
                type="number"
                value={settings.collection_start_number}
                onChange={(e) => handleInputChange('collection_start_number', parseInt(e.target.value) || 1)}
                min="1"
                max="999999"
                className={errors.collection_start_number ? 'border-red-500' : ''}
              />
              {errors.collection_start_number && (
                <p className="text-sm text-red-500">{errors.collection_start_number}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Customer Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Settings</CardTitle>
            <CardDescription>Configure customer reference code format</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="customer_prefix">Customer Prefix</Label>
              <Input
                id="customer_prefix"
                value={settings.customer_prefix}
                onChange={(e) => handleInputChange('customer_prefix', e.target.value.toUpperCase())}
                placeholder="CUST"
                className={errors.customer_prefix ? 'border-red-500' : ''}
              />
              {errors.customer_prefix && (
                <p className="text-sm text-red-500">{errors.customer_prefix}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="customer_start_number">Starting Number</Label>
              <Input
                id="customer_start_number"
                type="number"
                value={settings.customer_start_number}
                onChange={(e) => handleInputChange('customer_start_number', parseInt(e.target.value) || 1)}
                min="1"
                max="999999"
                className={errors.customer_start_number ? 'border-red-500' : ''}
              />
              {errors.customer_start_number && (
                <p className="text-sm text-red-500">{errors.customer_start_number}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Partner Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Partner Settings</CardTitle>
            <CardDescription>Configure partner reference code format</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="partner_prefix">Partner Prefix</Label>
              <Input
                id="partner_prefix"
                value={settings.partner_prefix}
                onChange={(e) => handleInputChange('partner_prefix', e.target.value.toUpperCase())}
                placeholder="PART"
                className={errors.partner_prefix ? 'border-red-500' : ''}
              />
              {errors.partner_prefix && (
                <p className="text-sm text-red-500">{errors.partner_prefix}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="partner_start_number">Starting Number</Label>
              <Input
                id="partner_start_number"
                type="number"
                value={settings.partner_start_number}
                onChange={(e) => handleInputChange('partner_start_number', parseInt(e.target.value) || 1)}
                min="1"
                max="999999"
                className={errors.partner_start_number ? 'border-red-500' : ''}
              />
              {errors.partner_start_number && (
                <p className="text-sm text-red-500">{errors.partner_start_number}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Agent Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Agent Settings</CardTitle>
            <CardDescription>Configure agent reference code format</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="agent_prefix">Agent Prefix</Label>
              <Input
                id="agent_prefix"
                value={settings.agent_prefix}
                onChange={(e) => handleInputChange('agent_prefix', e.target.value.toUpperCase())}
                placeholder="AGT"
                className={errors.agent_prefix ? 'border-red-500' : ''}
              />
              {errors.agent_prefix && (
                <p className="text-sm text-red-500">{errors.agent_prefix}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="agent_start_number">Starting Number</Label>
              <Input
                id="agent_start_number"
                type="number"
                value={settings.agent_start_number}
                onChange={(e) => handleInputChange('agent_start_number', parseInt(e.target.value) || 1)}
                min="1"
                max="999999"
                className={errors.agent_start_number ? 'border-red-500' : ''}
              />
              {errors.agent_start_number && (
                <p className="text-sm text-red-500">{errors.agent_start_number}</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            Configuration Guidelines
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• Prefixes must be 2-6 characters long and contain only uppercase letters and numbers</p>
            <p>• Starting numbers must be between 1 and 999,999</p>
            <p>• Reference codes will be generated as: PREFIX-NUMBER (e.g., LOAN-001, CUST-001)</p>
            <p>• Changes will apply to new entities created after saving these settings</p>
            <p>• Existing entities will keep their current reference codes</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
