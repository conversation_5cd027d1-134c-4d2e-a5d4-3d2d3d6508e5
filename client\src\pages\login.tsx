import { useState } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/lib/auth";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { loginSchema } from "@shared/schema";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Key, LogIn } from "lucide-react";

const formSchema = loginSchema;

export default function Login() {
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const [, navigate] = useLocation();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    console.log("Login attempt with email:", values.email);

    try {
      // Call login function
      const response = await login(values.email, values.password);

      console.log("Login response:", response);

      if (response) {
        // Check user data to ensure company name is present
        console.log("Login successful, user data:", response.user);

        if (response.user?.company_id && !response.user?.company_name) {
          console.warn("Warning: company_id exists but company_name is missing");
        }

        toast({
          title: "Login successful",
          description: `Welcome back, ${response.user.full_name}!`,
        });

        // Navigate to dashboard after a short delay to ensure data is saved to localStorage
        setTimeout(() => navigate("/dashboard"), 100);
      } else {
        console.error("Login failed: No response data");
        toast({
          title: "Login failed",
          description: "Invalid credentials or server error",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Login error:", error);

      // Extract error message with comprehensive error handling
      let errorMessage = "An unknown error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        // Handle error objects from apiRequest (like {message: "Invalid email or password"})
        if ('message' in error && error.message) {
          errorMessage = String(error.message);
        } else if ('error' in error && error.error) {
          // Handle nested error objects
          if (typeof error.error === 'string') {
            errorMessage = error.error;
          } else if (typeof error.error === 'object' && error.error.message) {
            errorMessage = String(error.error.message);
          }
        }
      }

      toast({
        title: "Login failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Function to handle quick login for company admin
  const handleQuickLoginAdmin = async () => {
    form.setValue("email", "<EMAIL>");
    form.setValue("password", "Pass@2022");

    setIsLoading(true);
    try {
      const response = await login("<EMAIL>", "Pass@2022");

      if (response) {
        toast({
          title: "Quick login successful",
          description: `Welcome back, ${response.user.full_name}!`,
        });

        // Navigate to dashboard after a short delay to ensure data is saved to localStorage
        setTimeout(() => navigate("/dashboard"), 100);
      } else {
        toast({
          title: "Login failed",
          description: "Invalid credentials or server error",
          variant: "destructive"
        });
      }
    } catch (error) {
      // Extract error message with comprehensive error handling
      let errorMessage = "An unknown error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        if ('message' in error && error.message) {
          errorMessage = String(error.message);
        } else if ('error' in error && error.error) {
          if (typeof error.error === 'string') {
            errorMessage = error.error;
          } else if (typeof error.error === 'object' && error.error.message) {
            errorMessage = String(error.error.message);
          }
        }
      }

      toast({
        title: "Login failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle quick login for SaaS admin
  const handleQuickLoginSaasAdmin = async () => {
    form.setValue("email", "<EMAIL>");
    form.setValue("password", "Pass@2020");

    setIsLoading(true);
    try {
      const response = await login("<EMAIL>", "Pass@2020");

      if (response) {
        toast({
          title: "Quick login successful",
          description: `Welcome back, ${response.user.full_name}!`,
        });

        // Navigate to dashboard after a short delay to ensure data is saved to localStorage
        setTimeout(() => navigate("/dashboard"), 100);
      } else {
        toast({
          title: "Login failed",
          description: "Invalid credentials or server error",
          variant: "destructive"
        });
      }
    } catch (error) {
      // Extract error message with comprehensive error handling
      let errorMessage = "An unknown error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        if ('message' in error && error.message) {
          errorMessage = String(error.message);
        } else if ('error' in error && error.error) {
          if (typeof error.error === 'string') {
            errorMessage = error.error;
          } else if (typeof error.error === 'object' && error.error.message) {
            errorMessage = String(error.error.message);
          }
        }
      }

      toast({
        title: "Login failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/20 via-background to-background">
      <div className="absolute inset-0 z-0 bg-[url('/assets/grid-pattern.svg')] bg-center opacity-5"></div>
      <Card className="w-full max-w-md mx-4 shadow-xl border-primary/20 overflow-hidden relative z-10">
        <div className="w-full bg-gradient-to-b from-primary/10 to-primary/5 border-b border-primary/20">
          <CardHeader className="space-y-2 pb-4 pt-6">
            <div className="flex justify-center mb-3">
              <div className="h-16 w-16 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center shadow-md border border-primary/30 relative">
                <div className="absolute inset-0 rounded-full bg-primary/5 animate-pulse opacity-70"></div>
                <svg className="h-10 w-10 text-primary relative z-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                    stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
            <CardTitle className="text-xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">TrackFina</CardTitle>
            <CardDescription className="text-center text-sm">
              Enter your credentials to access your account
            </CardDescription>
          </CardHeader>
        </div>
        <CardContent className="pt-5 pb-5">
          <Form {...form}>
            <form onSubmit={(e) => {
              e.preventDefault(); // Prevent default form submission behavior
              form.handleSubmit(onSubmit)(e);
            }} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-sm font-medium">Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter your email"
                        className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-sm font-medium">Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter your password"
                        className="h-10 text-sm shadow-sm"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end">
                <Button
                  variant="link"
                  className="px-0 font-normal text-xs h-6"
                  type="button"
                  onClick={() => navigate("/reset-password")}
                >
                  Forgot password?
                </Button>
              </div>

              <Button
                type="submit"
                className="w-full h-10 mt-2 font-semibold text-sm shadow-md"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Logging in...
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Login
                  </>
                )}
              </Button>
            </form>
          </Form>

          {/* Quick Login Button */}
          {/* <div className="mt-4 flex flex-col">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-muted" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-2 text-muted-foreground">Quick Login Options</span>
              </div>
            </div>
            <div className="mt-4 grid gap-2">
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={handleQuickLoginAdmin}
                disabled={isLoading}
              >
                <Key className="h-4 w-4" />
                <span>Company Admin Login</span>
              </Button>
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={handleQuickLoginSaasAdmin}
                disabled={isLoading}
              >
                <Key className="h-4 w-4" />
                <span>SaaS Admin Login</span>
              </Button>
            </div>
          </div> */}
        </CardContent>
        <CardFooter className="flex flex-col pt-3 pb-5 border-t">
          <div className="text-center">
            <p className="text-muted-foreground mb-2 text-sm">Don't have an account?</p>
            <Button
              variant="secondary"
              className="h-10 px-8 text-sm font-semibold bg-primary/10 hover:bg-primary/20 text-primary border border-primary/30 transition-all duration-200 hover:scale-105"
              onClick={() => navigate("/register")}
            >
              Register Now
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
