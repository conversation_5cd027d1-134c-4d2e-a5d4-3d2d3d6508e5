/**
 * Utility functions for exporting data to various formats
 */

export interface ExportColumn<T> {
  key: keyof T;
  header: string;
  formatter?: (value: any) => string;
}

/**
 * Generate CSV content from data array
 */
export function generateCsv<T extends Record<string, any>>(
  data: T[],
  columns: ExportColumn<T>[]
): string {
  if (data.length === 0) {
    return columns.map(col => `"${col.header}"`).join(',');
  }
  
  // Create header row
  const headers = columns.map(col => `"${col.header}"`).join(',');
  
  // Create data rows
  const rows = data.map(item => {
    return columns.map(col => {
      let value = item[col.key];
      
      // Apply formatter if provided
      if (col.formatter && value !== undefined && value !== null) {
        value = col.formatter(value);
      }
      
      const stringValue = value !== undefined && value !== null ? String(value) : '';
      
      // Escape quotes and wrap in quotes if contains comma, quote, or newline
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',');
  });
  
  return [headers, ...rows].join('\n');
}

/**
 * Download CSV content as a file
 */
export function downloadCsv(csvContent: string, filename: string): void {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  URL.revokeObjectURL(url);
}

/**
 * Export data to CSV and trigger download
 */
export function exportToCsv<T extends Record<string, any>>(
  data: T[],
  columns: ExportColumn<T>[],
  filename: string
): void {
  const csvContent = generateCsv(data, columns);
  downloadCsv(csvContent, filename);
}

/**
 * Format date for export
 */
export function formatDateForExport(date: string | Date | null | undefined): string {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-IN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return '';
  }
}

/**
 * Format currency for export
 */
export function formatCurrencyForExport(amount: number | string | null | undefined): string {
  if (amount === null || amount === undefined) return '';
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numAmount)) return '';
  
  return `₹${numAmount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
}

/**
 * Format phone number for export
 */
export function formatPhoneForExport(phone: string | null | undefined): string {
  if (!phone) return '';
  
  // Remove any non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // If it starts with +91, format as +91 XXXXX XXXXX
  if (cleaned.startsWith('+91') && cleaned.length === 13) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 8)} ${cleaned.slice(8)}`;
  }
  
  return phone;
}

/**
 * Common export columns for users
 */
export const userExportColumns: ExportColumn<any>[] = [
  { key: 'id', header: 'User ID' },
  { key: 'full_name', header: 'Full Name' },
  { key: 'username', header: 'Username' },
  { key: 'email', header: 'Email' },
  { key: 'role', header: 'Role' },
  {
    key: 'mobile_number',
    header: 'Mobile Number',
    formatter: formatPhoneForExport
  },
  { key: 'branch_id', header: 'Branch ID' },
  { key: 'department_id', header: 'Department ID' },
  { key: 'manager_id', header: 'Manager ID' },
  { 
    key: 'created_at', 
    header: 'Created At',
    formatter: formatDateForExport
  },
  { 
    key: 'updated_at', 
    header: 'Updated At',
    formatter: formatDateForExport
  }
];

/**
 * Common export columns for customers
 */
export const customerExportColumns: ExportColumn<any>[] = [
  { key: 'id', header: 'Customer ID' },
  { key: 'customer_reference_code', header: 'Reference Code' },
  { key: 'full_name', header: 'Full Name' },
  { 
    key: 'phone', 
    header: 'Phone',
    formatter: formatPhoneForExport
  },
  { key: 'email', header: 'Email' },
  { key: 'address', header: 'Address' },
  { key: 'credit_score', header: 'Credit Score' },
  { key: 'kyc_verified', header: 'KYC Verified' },
  { 
    key: 'created_at', 
    header: 'Created At',
    formatter: formatDateForExport
  }
];

/**
 * Common export columns for loans
 */
export const loanExportColumns: ExportColumn<any>[] = [
  { key: 'id', header: 'Loan ID' },
  { key: 'loan_reference_code', header: 'Loan Reference' },
  { key: 'customer_name', header: 'Customer Name' },
  { 
    key: 'principal_amount', 
    header: 'Principal Amount',
    formatter: formatCurrencyForExport
  },
  { key: 'interest_rate', header: 'Interest Rate (%)' },
  { key: 'tenure_months', header: 'Tenure (Months)' },
  { 
    key: 'emi_amount', 
    header: 'EMI Amount',
    formatter: formatCurrencyForExport
  },
  { key: 'status', header: 'Status' },
  { 
    key: 'disbursement_date', 
    header: 'Disbursement Date',
    formatter: formatDateForExport
  },
  { 
    key: 'created_at', 
    header: 'Created At',
    formatter: formatDateForExport
  }
];

/**
 * Common export columns for collections
 */
export const collectionExportColumns: ExportColumn<any>[] = [
  { key: 'id', header: 'Collection ID' },
  { key: 'collection_reference_code', header: 'Reference Code' },
  { key: 'customer_name', header: 'Customer Name' },
  { key: 'loan_reference', header: 'Loan Reference' },
  { 
    key: 'amount', 
    header: 'Amount',
    formatter: formatCurrencyForExport
  },
  { key: 'status', header: 'Status' },
  { key: 'payment_method', header: 'Payment Method' },
  { 
    key: 'scheduled_date', 
    header: 'Scheduled Date',
    formatter: formatDateForExport
  },
  { 
    key: 'collection_date', 
    header: 'Collection Date',
    formatter: formatDateForExport
  },
  { key: 'agent_name', header: 'Agent Name' }
];

/**
 * Generate filename with timestamp
 */
export function generateFilename(prefix: string, extension: string = 'csv'): string {
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  return `${prefix}_${timestamp}.${extension}`;
}

/**
 * Validate CSV file
 */
export function validateCsvFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  if (!file.type.includes('csv') && !file.name.endsWith('.csv')) {
    return { valid: false, error: 'Please select a CSV file' };
  }
  
  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 5MB' };
  }
  
  return { valid: true };
}

/**
 * Read CSV file content
 */
export function readCsvFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const content = event.target?.result as string;
      resolve(content);
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    reader.readAsText(file);
  });
}
