import { Request, Response, NextFunction } from 'express';
import { db } from '../db';
import {
  users, userRoles, rolePermissions, permissions,
  groupUsers, groupRoles, customRoles, temporaryPermissions
} from '../../shared/schema';
import { eq, and, inArray, gte, isNull } from 'drizzle-orm';
import { AuthRequest } from './auth';

// Cache for user permissions to avoid repeated database queries
const permissionCache: Record<string, {
  permissions: string[],
  timestamp: number
}> = {};

// Cache expiration time in milliseconds (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

/**
 * Check if a user has a specific permission
 * @param userId User ID
 * @param permissionCode Permission code to check
 * @returns Promise<boolean> True if user has permission, false otherwise
 */
export async function hasPermission(userId: number, permissionCode: string): Promise<boolean> {
  try {
    // Check if user is in cache and cache is not expired
    const cacheKey = `${userId}`;
    const now = Date.now();

    if (permissionCache[cacheKey] && (now - permissionCache[cacheKey].timestamp < CACHE_EXPIRATION)) {
      // Return from cache
      return permissionCache[cacheKey].permissions.includes(permissionCode);
    }

    // Get user's role
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId));

    if (!user) {
      return false;
    }

    // System admins have all permissions
    if (user.role === 'saas_admin') {
      // Cache all permissions for saas_admin
      permissionCache[cacheKey] = {
        permissions: ['*'], // Wildcard for all permissions
        timestamp: now
      };
      return true;
    }

    // Get directly assigned roles
    const userRolesData = await db
      .select({
        role_id: userRoles.role_id
      })
      .from(userRoles)
      .where(eq(userRoles.user_id, userId));

    const directRoleIds = userRolesData.map(r => r.role_id);

    // Get roles from groups
    const userGroups = await db
      .select({
        group_id: groupUsers.group_id
      })
      .from(groupUsers)
      .where(eq(groupUsers.user_id, userId));

    const groupIds = userGroups.map(g => g.group_id);

    let groupRoleIds: number[] = [];

    if (groupIds.length > 0) {
      const groupRolesData = await db
        .select({
          role_id: groupRoles.role_id
        })
        .from(groupRoles)
        .where(inArray(groupRoles.group_id, groupIds));

      groupRoleIds = groupRolesData.map(r => r.role_id);
    }

    // Combine all role IDs
    const allRoleIds = [...new Set([...directRoleIds, ...groupRoleIds])];

    if (allRoleIds.length === 0) {
      // No roles assigned, cache empty permissions
      permissionCache[cacheKey] = {
        permissions: [],
        timestamp: now
      };
      return false;
    }

    // Get permission codes for all roles
    const permissionData = await db
      .select({
        permission_code: permissions.code
      })
      .from(rolePermissions)
      .leftJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
      .where(inArray(rolePermissions.role_id, allRoleIds));

    const userPermissions = permissionData.map(p => p.permission_code);

    // Check for temporary permissions (don't cache these as they can expire)
    const hasTemporaryPermission = await checkTemporaryPermission(userId, permissionCode);

    // If user has temporary permission, return true immediately
    if (hasTemporaryPermission) {
      return true;
    }

    // Cache user permissions (excluding temporary ones)
    permissionCache[cacheKey] = {
      permissions: userPermissions,
      timestamp: now
    };

    return userPermissions.includes(permissionCode);
  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}

/**
 * Check if a user has a temporary permission
 * @param userId User ID
 * @param permissionCode Permission code to check
 * @returns Promise<boolean> True if user has active temporary permission
 */
async function checkTemporaryPermission(userId: number, permissionCode: string): Promise<boolean> {
  try {
    const [result] = await db
      .select({ id: temporaryPermissions.id })
      .from(temporaryPermissions)
      .innerJoin(permissions, eq(temporaryPermissions.permission_id, permissions.id))
      .where(
        and(
          eq(temporaryPermissions.user_id, userId),
          eq(permissions.code, permissionCode),
          isNull(temporaryPermissions.revoked_at),
          gte(temporaryPermissions.expires_at, new Date())
        )
      )
      .limit(1);

    return !!result;
  } catch (error) {
    console.error('Error checking temporary permission:', error);
    return false;
  }
}

/**
 * Clear permission cache for a specific user
 * @param userId User ID
 */
export function clearPermissionCache(userId: number): void {
  const cacheKey = `${userId}`;
  delete permissionCache[cacheKey];
}

/**
 * Middleware to check if user has required permission
 * @param permissionCode Permission code to check
 * @returns Middleware function
 */
export function requirePermission(permissionCode: string) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Check if user has permission
      const hasAccess = await hasPermission(req.user.id, permissionCode);

      if (!hasAccess) {
        return res.status(403).json({
          message: 'Access denied. You do not have the required permission.',
          required_permission: permissionCode
        });
      }

      next();
    } catch (error) {
      console.error('Error in permission middleware:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  };
}

/**
 * Middleware to check if user has any of the required permissions
 * @param permissionCodes Array of permission codes to check
 * @returns Middleware function
 */
export function requireAnyPermission(permissionCodes: string[]) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // System admins have all permissions
      if (req.user.role === 'saas_admin') {
        return next();
      }

      // Check if user has any of the required permissions
      for (const code of permissionCodes) {
        const hasAccess = await hasPermission(req.user.id, code);
        if (hasAccess) {
          return next();
        }
      }

      return res.status(403).json({
        message: 'Access denied. You do not have any of the required permissions.',
        required_permissions: permissionCodes
      });
    } catch (error) {
      console.error('Error in permission middleware:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  };
}

/**
 * Middleware to check if user has all of the required permissions
 * @param permissionCodes Array of permission codes to check
 * @returns Middleware function
 */
export function requireAllPermissions(permissionCodes: string[]) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // System admins have all permissions
      if (req.user.role === 'saas_admin') {
        return next();
      }

      // Check if user has all required permissions
      for (const code of permissionCodes) {
        const hasAccess = await hasPermission(req.user.id, code);
        if (!hasAccess) {
          return res.status(403).json({
            message: 'Access denied. You do not have all required permissions.',
            missing_permission: code,
            required_permissions: permissionCodes
          });
        }
      }

      next();
    } catch (error) {
      console.error('Error in permission middleware:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  };
}
