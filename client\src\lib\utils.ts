import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(
  amount: number,
  currency: string = 'INR',
  locale: string = 'en-IN',
  currencySymbol?: string
): string {
  if (currencySymbol) {
    // Use custom currency symbol
    return currencySymbol + new Intl.NumberFormat(locale, {
      style: 'decimal',
      maximumFractionDigits: 0,
    }).format(amount);
  }

  // Use standard currency formatting
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
}

export function formatDate(dateString: string, format?: string): string {
  const date = new Date(dateString);

  if (format) {
    // Use custom date format
    return formatDateWithPattern(date, format);
  }

  // Use default formatting
  return date.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

// Helper function to format date with pattern
export function formatDateWithPattern(date: Date, pattern: string): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const monthShort = date.toLocaleString('en', { month: 'short' });

  return pattern
    .replace('dd', day)
    .replace('MM', month)
    .replace('yyyy', year.toString())
    .replace('MMM', monthShort);
}

export function formatTime(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-IN', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

export function getStatusBadgeVariant(status: string): "default" | "destructive" | "outline" | "secondary" | null | undefined {
  switch (status.toLowerCase()) {
    case 'completed':
      return "outline"; // "success" is not a valid variant for Badge
    case 'pending':
      return "default"; // "warning" is not a valid variant for Badge
    case 'overdue':
      return "destructive";
    case 'cancelled':
      return "secondary";
    default:
      return "default";
  }
}
