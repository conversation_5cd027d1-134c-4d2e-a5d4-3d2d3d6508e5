import { Router } from 'express';
import { enhancedSessionService } from '../services/enhancedSessionService';
import { enhancedAuthMiddleware, requireFreshAuth, requireMFA, type EnhancedAuthRequest } from '../middleware/enhancedAuth';
import { requireRole } from '../middleware/auth';
import { z } from 'zod';

const router = Router();

// Apply enhanced auth middleware to all routes
router.use(enhancedAuthMiddleware);

/**
 * GET /api/sessions/current
 * Get current session information
 */
router.get('/current', async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.session || !req.user) {
      return res.status(401).json({ message: 'No active session' });
    }

    const sessionStats = await enhancedSessionService.getSessionStatistics({
      userId: req.user.id,
      days: 1
    });

    res.json({
      session: req.session,
      deviceInfo: req.deviceInfo,
      user: req.user,
      todayStats: sessionStats
    });
  } catch (error) {
    console.error('Error getting current session:', error);
    res.status(500).json({ message: 'Failed to get session information' });
  }
});

/**
 * GET /api/sessions/active
 * Get all active sessions for current user
 */
router.get('/active', async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const activeSessions = await enhancedSessionService.getUserActiveSessions(req.user.id);
    
    // Don't expose sensitive session data
    const safeSessions = activeSessions.map(session => ({
      id: session.id,
      session_id: session.session_id,
      device_type: session.device_type,
      device_name: session.device_name,
      ip_address: session.ip_address,
      location_country: session.location_country,
      location_city: session.location_city,
      created_at: session.created_at,
      last_activity: session.last_activity,
      expires_at: session.expires_at,
      is_trusted_device: session.is_trusted_device,
      mfa_verified: session.mfa_verified,
      isCurrent: session.session_id === req.session?.sessionId
    }));

    res.json({ sessions: safeSessions });
  } catch (error) {
    console.error('Error getting active sessions:', error);
    res.status(500).json({ message: 'Failed to get active sessions' });
  }
});

/**
 * DELETE /api/sessions/:sessionId
 * Terminate a specific session
 */
router.delete('/:sessionId', async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { sessionId } = req.params;
    
    // Verify the session belongs to the current user
    const activeSessions = await enhancedSessionService.getUserActiveSessions(req.user.id);
    const targetSession = activeSessions.find(s => s.session_id === sessionId);
    
    if (!targetSession) {
      return res.status(404).json({ message: 'Session not found or not owned by user' });
    }

    await enhancedSessionService.terminateSession(
      sessionId, 
      'Terminated by user', 
      req.user.id
    );

    res.json({ message: 'Session terminated successfully' });
  } catch (error) {
    console.error('Error terminating session:', error);
    res.status(500).json({ message: 'Failed to terminate session' });
  }
});

/**
 * DELETE /api/sessions/terminate-others
 * Terminate all other sessions except current
 */
router.delete('/terminate-others', requireFreshAuth(300), async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user || !req.session) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const terminatedCount = await enhancedSessionService.terminateOtherSessions(
      req.user.id,
      req.session.sessionId,
      'Terminated by user - logout other devices'
    );

    res.json({ 
      message: `${terminatedCount} sessions terminated successfully`,
      terminatedCount 
    });
  } catch (error) {
    console.error('Error terminating other sessions:', error);
    res.status(500).json({ message: 'Failed to terminate other sessions' });
  }
});

/**
 * POST /api/sessions/trust-device
 * Mark current device as trusted
 */
router.post('/trust-device', requireMFA(), async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user || !req.deviceInfo) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const deviceNameSchema = z.object({
      deviceName: z.string().min(1).max(255).optional()
    });

    const { deviceName } = deviceNameSchema.parse(req.body);

    const trustedDevice = await enhancedSessionService.registerTrustedDevice({
      userId: req.user.id,
      deviceFingerprint: req.deviceInfo.fingerprint,
      deviceName: deviceName || `${req.deviceInfo.type} device`,
      deviceType: req.deviceInfo.type,
      userAgent: req.deviceInfo.userAgent,
      ipAddress: req.deviceInfo.ipAddress,
      location: req.deviceInfo.location,
      trustedBy: req.user.id
    });

    res.json({ 
      message: 'Device marked as trusted',
      trustedDevice: {
        id: trustedDevice.id,
        device_name: trustedDevice.device_name,
        device_type: trustedDevice.device_type,
        trusted_at: trustedDevice.trusted_at,
        trust_level: trustedDevice.trust_level
      }
    });
  } catch (error) {
    console.error('Error trusting device:', error);
    res.status(500).json({ message: 'Failed to trust device' });
  }
});

/**
 * GET /api/sessions/statistics
 * Get session statistics for current user
 */
router.get('/statistics', async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const daysSchema = z.object({
      days: z.coerce.number().min(1).max(365).default(30)
    });

    const { days } = daysSchema.parse(req.query);

    const statistics = await enhancedSessionService.getSessionStatistics({
      userId: req.user.id,
      days
    });

    res.json({ statistics });
  } catch (error) {
    console.error('Error getting session statistics:', error);
    res.status(500).json({ message: 'Failed to get session statistics' });
  }
});

/**
 * POST /api/sessions/cleanup
 * Manually trigger session cleanup (admin only)
 */
router.post('/cleanup', requireRole(['admin', 'super_admin']), async (req: EnhancedAuthRequest, res) => {
  try {
    const cleanedCount = await enhancedSessionService.cleanupExpiredSessions();
    
    res.json({ 
      message: `${cleanedCount} expired sessions cleaned up`,
      cleanedCount 
    });
  } catch (error) {
    console.error('Error cleaning up sessions:', error);
    res.status(500).json({ message: 'Failed to cleanup sessions' });
  }
});

/**
 * GET /api/sessions/company-statistics
 * Get session statistics for company (admin only)
 */
router.get('/company-statistics', requireRole(['admin', 'super_admin']), async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user?.company_id) {
      return res.status(400).json({ message: 'Company ID required' });
    }

    const daysSchema = z.object({
      days: z.coerce.number().min(1).max(365).default(30)
    });

    const { days } = daysSchema.parse(req.query);

    const statistics = await enhancedSessionService.getSessionStatistics({
      companyId: req.user.company_id,
      days
    });

    res.json({ statistics });
  } catch (error) {
    console.error('Error getting company session statistics:', error);
    res.status(500).json({ message: 'Failed to get company session statistics' });
  }
});

/**
 * POST /api/sessions/verify-mfa
 * Verify MFA for current session
 */
router.post('/verify-mfa', async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user || !req.session) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const mfaSchema = z.object({
      mfaCode: z.string().min(6).max(8),
      mfaType: z.enum(['totp', 'sms', 'email']).default('totp')
    });

    const { mfaCode, mfaType } = mfaSchema.parse(req.body);

    // TODO: Implement actual MFA verification logic here
    // For now, we'll simulate MFA verification
    const isValidMFA = mfaCode === '123456'; // Placeholder

    if (!isValidMFA) {
      return res.status(400).json({ message: 'Invalid MFA code' });
    }

    // Update session to mark MFA as verified
    await enhancedSessionService['updateSessionActivity'](req.session.sessionId);
    
    // TODO: Update session MFA status in database
    
    res.json({ 
      message: 'MFA verified successfully',
      mfaVerified: true 
    });
  } catch (error) {
    console.error('Error verifying MFA:', error);
    res.status(500).json({ message: 'Failed to verify MFA' });
  }
});

/**
 * POST /api/sessions/refresh-auth
 * Refresh authentication for current session
 */
router.post('/refresh-auth', async (req: EnhancedAuthRequest, res) => {
  try {
    if (!req.user || !req.session) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const passwordSchema = z.object({
      password: z.string().min(1)
    });

    const { password } = passwordSchema.parse(req.body);

    // TODO: Implement password verification logic here
    // For now, we'll simulate password verification
    const isValidPassword = password.length > 0; // Placeholder

    if (!isValidPassword) {
      return res.status(400).json({ message: 'Invalid password' });
    }

    // Update session to mark as fresh auth
    await enhancedSessionService['updateSessionActivity'](req.session.sessionId);
    
    // TODO: Update session fresh_auth status in database
    
    res.json({ 
      message: 'Authentication refreshed successfully',
      freshAuth: true 
    });
  } catch (error) {
    console.error('Error refreshing authentication:', error);
    res.status(500).json({ message: 'Failed to refresh authentication' });
  }
});

export default router;
