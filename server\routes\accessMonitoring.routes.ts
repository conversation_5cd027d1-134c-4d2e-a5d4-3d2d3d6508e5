import { Router } from 'express';
import { accessMonitoringService } from '../services/accessMonitoringService';
import { enhancedAuthMiddleware, type EnhancedAuthRequest } from '../middleware/enhancedAuth';
import { requireSecurityAdmin } from '../middleware/securityMonitoring';
import { db } from '../db';
import { 
  securityEvents, securityAlerts, securityRules, securityResponses,
  userBehaviorBaselines, rateLimitViolations
} from '@shared/schema';
import { eq, and, desc, count, gte, lte, sql } from 'drizzle-orm';
import { z } from 'zod';

const router = Router();

// Apply enhanced auth middleware to all routes
router.use(enhancedAuthMiddleware);

/**
 * GET /api/monitoring/events
 * Get security events with filtering and pagination
 */
router.get('/events', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const querySchema = z.object({
      page: z.coerce.number().min(1).default(1),
      limit: z.coerce.number().min(1).max(100).default(20),
      eventType: z.string().optional(),
      severity: z.enum(['low', 'medium', 'high', 'critical']).optional(),
      userId: z.coerce.number().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      resolved: z.coerce.boolean().optional()
    });

    const query = querySchema.parse(req.query);
    const offset = (query.page - 1) * query.limit;

    // Build where conditions
    let whereConditions = [];
    
    if (req.user?.company_id) {
      whereConditions.push(eq(securityEvents.company_id, req.user.company_id));
    }
    
    if (query.eventType) {
      whereConditions.push(eq(securityEvents.event_type, query.eventType as any));
    }
    
    if (query.severity) {
      whereConditions.push(eq(securityEvents.severity, query.severity));
    }
    
    if (query.userId) {
      whereConditions.push(eq(securityEvents.user_id, query.userId));
    }
    
    if (query.startDate) {
      whereConditions.push(gte(securityEvents.timestamp, new Date(query.startDate)));
    }
    
    if (query.endDate) {
      whereConditions.push(lte(securityEvents.timestamp, new Date(query.endDate)));
    }
    
    if (query.resolved !== undefined) {
      whereConditions.push(eq(securityEvents.resolved, query.resolved));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get events with pagination
    const events = await db.select()
      .from(securityEvents)
      .where(whereClause)
      .orderBy(desc(securityEvents.timestamp))
      .limit(query.limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await db.select({ total: count() })
      .from(securityEvents)
      .where(whereClause);

    res.json({
      events,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        pages: Math.ceil(total / query.limit)
      }
    });
  } catch (error) {
    console.error('Error getting security events:', error);
    res.status(500).json({ message: 'Failed to get security events' });
  }
});

/**
 * GET /api/monitoring/alerts
 * Get security alerts with filtering
 */
router.get('/alerts', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const querySchema = z.object({
      status: z.enum(['open', 'investigating', 'resolved', 'false_positive']).optional(),
      severity: z.enum(['low', 'medium', 'high', 'critical']).optional(),
      assignedTo: z.coerce.number().optional(),
      limit: z.coerce.number().min(1).max(100).default(50)
    });

    const query = querySchema.parse(req.query);

    let whereConditions = [];
    
    if (req.user?.company_id) {
      whereConditions.push(eq(securityAlerts.company_id, req.user.company_id));
    }
    
    if (query.status) {
      whereConditions.push(eq(securityAlerts.status, query.status));
    }
    
    if (query.severity) {
      whereConditions.push(eq(securityAlerts.severity, query.severity));
    }
    
    if (query.assignedTo) {
      whereConditions.push(eq(securityAlerts.assigned_to, query.assignedTo));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    const alerts = await db.select()
      .from(securityAlerts)
      .where(whereClause)
      .orderBy(desc(securityAlerts.created_at))
      .limit(query.limit);

    res.json({ alerts });
  } catch (error) {
    console.error('Error getting security alerts:', error);
    res.status(500).json({ message: 'Failed to get security alerts' });
  }
});

/**
 * PUT /api/monitoring/alerts/:alertId/acknowledge
 * Acknowledge a security alert
 */
router.put('/alerts/:alertId/acknowledge', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const { alertId } = req.params;
    
    const [alert] = await db.update(securityAlerts)
      .set({
        acknowledged: true,
        acknowledged_at: new Date(),
        acknowledged_by: req.user!.id,
        updated_at: new Date()
      })
      .where(eq(securityAlerts.alert_id, alertId))
      .returning();

    if (!alert) {
      return res.status(404).json({ message: 'Alert not found' });
    }

    res.json({ message: 'Alert acknowledged', alert });
  } catch (error) {
    console.error('Error acknowledging alert:', error);
    res.status(500).json({ message: 'Failed to acknowledge alert' });
  }
});

/**
 * PUT /api/monitoring/alerts/:alertId/resolve
 * Resolve a security alert
 */
router.put('/alerts/:alertId/resolve', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const { alertId } = req.params;
    const { resolution_notes } = req.body;
    
    const [alert] = await db.update(securityAlerts)
      .set({
        status: 'resolved',
        resolved_at: new Date(),
        resolved_by: req.user!.id,
        resolution_notes,
        updated_at: new Date()
      })
      .where(eq(securityAlerts.alert_id, alertId))
      .returning();

    if (!alert) {
      return res.status(404).json({ message: 'Alert not found' });
    }

    res.json({ message: 'Alert resolved', alert });
  } catch (error) {
    console.error('Error resolving alert:', error);
    res.status(500).json({ message: 'Failed to resolve alert' });
  }
});

/**
 * GET /api/monitoring/dashboard
 * Get security monitoring dashboard data
 */
router.get('/dashboard', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const last7Days = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    // Get event counts by severity for last 24 hours
    const eventCounts = await db.select({
      severity: securityEvents.severity,
      count: count()
    })
    .from(securityEvents)
    .where(and(
      gte(securityEvents.timestamp, last24Hours),
      req.user?.company_id ? eq(securityEvents.company_id, req.user.company_id) : sql`true`
    ))
    .groupBy(securityEvents.severity);

    // Get open alerts count
    const [{ openAlerts }] = await db.select({ openAlerts: count() })
      .from(securityAlerts)
      .where(and(
        eq(securityAlerts.status, 'open'),
        req.user?.company_id ? eq(securityAlerts.company_id, req.user.company_id) : sql`true`
      ));

    // Get top event types for last 7 days
    const topEventTypes = await db.select({
      event_type: securityEvents.event_type,
      count: count()
    })
    .from(securityEvents)
    .where(and(
      gte(securityEvents.timestamp, last7Days),
      req.user?.company_id ? eq(securityEvents.company_id, req.user.company_id) : sql`true`
    ))
    .groupBy(securityEvents.event_type)
    .orderBy(desc(count()))
    .limit(10);

    // Get recent high-severity events
    const recentHighSeverityEvents = await db.select()
      .from(securityEvents)
      .where(and(
        gte(securityEvents.timestamp, last24Hours),
        eq(securityEvents.severity, 'high'),
        req.user?.company_id ? eq(securityEvents.company_id, req.user.company_id) : sql`true`
      ))
      .orderBy(desc(securityEvents.timestamp))
      .limit(10);

    // Get active security rules count
    const [{ activeRules }] = await db.select({ activeRules: count() })
      .from(securityRules)
      .where(and(
        eq(securityRules.is_active, true),
        req.user?.company_id ? eq(securityRules.company_id, req.user.company_id) : sql`true`
      ));

    res.json({
      summary: {
        eventCounts: eventCounts.reduce((acc, item) => {
          acc[item.severity] = item.count;
          return acc;
        }, {} as Record<string, number>),
        openAlerts,
        activeRules
      },
      topEventTypes,
      recentHighSeverityEvents
    });
  } catch (error) {
    console.error('Error getting dashboard data:', error);
    res.status(500).json({ message: 'Failed to get dashboard data' });
  }
});

/**
 * GET /api/monitoring/rules
 * Get security rules
 */
router.get('/rules', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const rules = await db.select()
      .from(securityRules)
      .where(
        req.user?.company_id ? 
          or(
            eq(securityRules.company_id, req.user.company_id),
            eq(securityRules.company_id, null) // Global rules
          ) : 
          sql`true`
      )
      .orderBy(desc(securityRules.priority));

    res.json({ rules });
  } catch (error) {
    console.error('Error getting security rules:', error);
    res.status(500).json({ message: 'Failed to get security rules' });
  }
});

/**
 * POST /api/monitoring/rules
 * Create a new security rule
 */
router.post('/rules', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const ruleSchema = z.object({
      rule_name: z.string().min(1).max(255),
      rule_description: z.string().optional(),
      event_types: z.array(z.string()).min(1),
      conditions: z.record(z.any()),
      threshold_config: z.record(z.any()).default({}),
      severity: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
      auto_response_actions: z.array(z.string()).default([]),
      notification_config: z.record(z.any()).default({}),
      priority: z.number().default(0)
    });

    const ruleData = ruleSchema.parse(req.body);

    const [rule] = await db.insert(securityRules)
      .values({
        ...ruleData,
        company_id: req.user!.company_id
      })
      .returning();

    res.status(201).json({ message: 'Security rule created', rule });
  } catch (error) {
    console.error('Error creating security rule:', error);
    res.status(500).json({ message: 'Failed to create security rule' });
  }
});

/**
 * PUT /api/monitoring/rules/:ruleId
 * Update a security rule
 */
router.put('/rules/:ruleId', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const { ruleId } = req.params;
    
    const updateSchema = z.object({
      rule_name: z.string().min(1).max(255).optional(),
      rule_description: z.string().optional(),
      event_types: z.array(z.string()).min(1).optional(),
      conditions: z.record(z.any()).optional(),
      threshold_config: z.record(z.any()).optional(),
      severity: z.enum(['low', 'medium', 'high', 'critical']).optional(),
      auto_response_actions: z.array(z.string()).optional(),
      notification_config: z.record(z.any()).optional(),
      is_active: z.boolean().optional(),
      priority: z.number().optional()
    });

    const updateData = updateSchema.parse(req.body);

    const [rule] = await db.update(securityRules)
      .set({
        ...updateData,
        updated_at: new Date()
      })
      .where(and(
        eq(securityRules.id, parseInt(ruleId)),
        req.user?.company_id ? eq(securityRules.company_id, req.user.company_id) : sql`true`
      ))
      .returning();

    if (!rule) {
      return res.status(404).json({ message: 'Security rule not found' });
    }

    res.json({ message: 'Security rule updated', rule });
  } catch (error) {
    console.error('Error updating security rule:', error);
    res.status(500).json({ message: 'Failed to update security rule' });
  }
});

/**
 * GET /api/monitoring/responses
 * Get security responses
 */
router.get('/responses', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const querySchema = z.object({
      executed: z.coerce.boolean().optional(),
      action: z.string().optional(),
      limit: z.coerce.number().min(1).max(100).default(50)
    });

    const query = querySchema.parse(req.query);

    let whereConditions = [];
    
    if (query.executed !== undefined) {
      whereConditions.push(eq(securityResponses.executed, query.executed));
    }
    
    if (query.action) {
      whereConditions.push(eq(securityResponses.response_action, query.action as any));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    const responses = await db.select()
      .from(securityResponses)
      .where(whereClause)
      .orderBy(desc(securityResponses.created_at))
      .limit(query.limit);

    res.json({ responses });
  } catch (error) {
    console.error('Error getting security responses:', error);
    res.status(500).json({ message: 'Failed to get security responses' });
  }
});

/**
 * POST /api/monitoring/test-event
 * Create a test security event (for testing purposes)
 */
router.post('/test-event', requireSecurityAdmin(), async (req: EnhancedAuthRequest, res) => {
  try {
    const testEventSchema = z.object({
      eventType: z.string(),
      description: z.string(),
      severity: z.enum(['low', 'medium', 'high', 'critical']).default('low')
    });

    const { eventType, description, severity } = testEventSchema.parse(req.body);

    const event = await accessMonitoringService.recordSecurityEvent({
      eventType,
      eventDescription: description,
      context: {
        userId: req.user!.id,
        companyId: req.user!.company_id,
        sessionId: req.session?.sessionId,
        ipAddress: req.deviceInfo?.ipAddress,
        userAgent: req.deviceInfo?.userAgent,
        endpoint: '/api/monitoring/test-event',
        method: 'POST',
        metadata: { test: true }
      },
      severity,
      eventSource: 'manual_test'
    });

    res.json({ message: 'Test event created', event });
  } catch (error) {
    console.error('Error creating test event:', error);
    res.status(500).json({ message: 'Failed to create test event' });
  }
});

export default router;
