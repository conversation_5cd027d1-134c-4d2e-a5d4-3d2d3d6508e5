import { db } from '../db';
import {
  users, userTemplates, bulkOperationLogs, userRoles, customRoles, userCompanies,
  type User, type InsertUser, type UserTemplate, type InsertUserTemplate,
  type BulkOperationLog, type InsertBulkOperationLog,
  type BulkUserImportData, type BulkOperationResult, type BulkRoleAssignmentData,
  type UserTemplateConfig
} from '@shared/schema';
import { eq, and, inArray } from 'drizzle-orm';
import bcrypt from 'bcrypt';
import errorLogger from '../utils/errorLogger';
import { parseUserImportCsv, generateCsv, userExportColumns, CsvParseResult } from '../utils/csvParser';

export class BulkOperationsService {
  /**
   * Create a new user template
   */
  async createUserTemplate(templateData: InsertUserTemplate): Promise<UserTemplate> {
    try {
      const [template] = await db
        .insert(userTemplates)
        .values(templateData)
        .returning();

      errorLogger.logInfo(
        `Created user template: ${template.name} for company ${template.company_id}`,
        'bulk-operations-service'
      );

      return template;
    } catch (error) {
      errorLogger.logError(
        `Failed to create user template: ${templateData.name}`,
        error,
        'bulk-operations-service'
      );
      throw error;
    }
  }

  /**
   * Get user templates for a company
   */
  async getUserTemplates(companyId: number): Promise<UserTemplate[]> {
    try {
      return await db
        .select()
        .from(userTemplates)
        .where(eq(userTemplates.company_id, companyId));
    } catch (error) {
      errorLogger.logError(
        `Failed to get user templates for company ${companyId}`,
        error,
        'bulk-operations-service'
      );
      throw error;
    }
  }

  /**
   * Get a specific user template
   */
  async getUserTemplate(id: number, companyId: number): Promise<UserTemplate | undefined> {
    try {
      const [template] = await db
        .select()
        .from(userTemplates)
        .where(and(
          eq(userTemplates.id, id),
          eq(userTemplates.company_id, companyId)
        ));

      return template;
    } catch (error) {
      errorLogger.logError(
        `Failed to get user template ${id} for company ${companyId}`,
        error,
        'bulk-operations-service'
      );
      return undefined;
    }
  }

  /**
   * Update a user template
   */
  async updateUserTemplate(
    id: number,
    companyId: number,
    updates: Partial<InsertUserTemplate>
  ): Promise<UserTemplate | undefined> {
    try {
      const [template] = await db
        .update(userTemplates)
        .set({ ...updates, updated_at: new Date() })
        .where(and(
          eq(userTemplates.id, id),
          eq(userTemplates.company_id, companyId)
        ))
        .returning();

      if (template) {
        errorLogger.logInfo(
          `Updated user template ${id} for company ${companyId}`,
          'bulk-operations-service'
        );
      }

      return template;
    } catch (error) {
      errorLogger.logError(
        `Failed to update user template ${id} for company ${companyId}`,
        error,
        'bulk-operations-service'
      );
      throw error;
    }
  }

  /**
   * Delete a user template
   */
  async deleteUserTemplate(id: number, companyId: number): Promise<boolean> {
    try {
      const result = await db
        .delete(userTemplates)
        .where(and(
          eq(userTemplates.id, id),
          eq(userTemplates.company_id, companyId)
        ));

      const deleted = result.rowCount > 0;
      if (deleted) {
        errorLogger.logInfo(
          `Deleted user template ${id} for company ${companyId}`,
          'bulk-operations-service'
        );
      }

      return deleted;
    } catch (error) {
      errorLogger.logError(
        `Failed to delete user template ${id} for company ${companyId}`,
        error,
        'bulk-operations-service'
      );
      throw error;
    }
  }

  /**
   * Create user from template
   */
  async createUserFromTemplate(
    templateId: number,
    userData: Partial<BulkUserImportData>,
    companyId: number
  ): Promise<User> {
    try {
      // Get the template
      const template = await this.getUserTemplate(templateId, companyId);
      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }

      const config = template.template_config as UserTemplateConfig;

      // Merge template defaults with provided data
      const userToCreate: InsertUser = {
        company_id: companyId,
        full_name: userData.full_name || config.defaultFields?.full_name || '',
        username: userData.username || '',
        email: userData.email || config.defaultFields?.email || '',
        password: userData.password || this.generateRandomPassword(),
        role: userData.role || template.default_role,
        phone: userData.phone || config.defaultFields?.phone,
        branch_id: userData.branch_id || template.default_branch_id || config.organizationalDefaults?.branch_id,
        department_id: userData.department_id || template.default_department_id || config.organizationalDefaults?.department_id,
        manager_id: userData.manager_id || config.organizationalDefaults?.manager_id,
      };

      // Hash password
      const hashedPassword = await bcrypt.hash(userToCreate.password, 10);
      userToCreate.password = hashedPassword;

      // Create user
      const [user] = await db
        .insert(users)
        .values(userToCreate)
        .returning();

      // Create user-company association
      await db
        .insert(userCompanies)
        .values({
          user_id: user.id,
          company_id: companyId,
          is_primary: true,
          role: user.role
        });

      // Assign default roles if specified
      const defaultRoles = template.default_roles as number[];
      if (defaultRoles && defaultRoles.length > 0) {
        for (const roleId of defaultRoles) {
          await db
            .insert(userRoles)
            .values({
              user_id: user.id,
              role_id: roleId
            });
        }
      }

      // Update template usage count
      await db
        .update(userTemplates)
        .set({ usage_count: template.usage_count + 1 })
        .where(eq(userTemplates.id, templateId));

      return user;
    } catch (error) {
      errorLogger.logError(
        `Failed to create user from template ${templateId}`,
        error,
        'bulk-operations-service'
      );
      throw error;
    }
  }

  /**
   * Import users from CSV
   */
  async importUsersFromCsv(
    csvContent: string,
    companyId: number,
    initiatedBy: number
  ): Promise<BulkOperationResult> {
    const startTime = Date.now();

    // Create operation log
    const [operationLog] = await db
      .insert(bulkOperationLogs)
      .values({
        operation_type: 'user_import',
        company_id: companyId,
        initiated_by: initiatedBy,
        status: 'processing',
        operation_data: { csvLength: csvContent.length }
      })
      .returning();

    try {
      // Parse CSV
      const parseResult = parseUserImportCsv(csvContent);

      const successfulRecords: User[] = [];
      const failedRecords: Array<{ record: BulkUserImportData; error: string }> = [];

      // Process each valid record
      for (const userData of parseResult.data) {
        try {
          let user: User;

          if (userData.template_id) {
            // Create from template
            user = await this.createUserFromTemplate(userData.template_id, userData, companyId);
          } else {
            // Create directly
            const hashedPassword = await bcrypt.hash(
              userData.password || this.generateRandomPassword(),
              10
            );

            const [createdUser] = await db
              .insert(users)
              .values({
                ...userData,
                company_id: companyId,
                password: hashedPassword
              })
              .returning();

            // Create user-company association
            await db
              .insert(userCompanies)
              .values({
                user_id: createdUser.id,
                company_id: companyId,
                is_primary: true,
                role: createdUser.role
              });

            user = createdUser;
          }

          successfulRecords.push(user);
        } catch (error) {
          failedRecords.push({
            record: userData,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Add CSV parsing errors to failed records
      for (const error of parseResult.errors) {
        failedRecords.push({
          record: error.rawData,
          error: `Row ${error.row}: ${error.message}`
        });
      }

      const result: BulkOperationResult = {
        success: successfulRecords.length,
        failed: failedRecords.length,
        errors: failedRecords.map(f => f.error),
        details: {
          successfulRecords,
          failedRecords
        }
      };

      // Update operation log
      await db
        .update(bulkOperationLogs)
        .set({
          status: 'completed',
          total_records: parseResult.totalRows,
          successful_records: successfulRecords.length,
          failed_records: failedRecords.length,
          results: result,
          completed_at: new Date(),
          processing_time_ms: Date.now() - startTime
        })
        .where(eq(bulkOperationLogs.id, operationLog.id));

      return result;
    } catch (error) {
      // Update operation log with error
      await db
        .update(bulkOperationLogs)
        .set({
          status: 'failed',
          results: { error: error instanceof Error ? error.message : 'Unknown error' },
          completed_at: new Date(),
          processing_time_ms: Date.now() - startTime
        })
        .where(eq(bulkOperationLogs.id, operationLog.id));

      throw error;
    }
  }

  /**
   * Export users to CSV
   */
  async exportUsersToCsv(companyId: number, initiatedBy: number): Promise<string> {
    try {
      // Get users for the company
      const companyUsers = await db
        .select()
        .from(users)
        .where(eq(users.company_id, companyId));

      // Generate CSV
      const csvContent = generateCsv(companyUsers, userExportColumns);

      // Log the operation
      await db
        .insert(bulkOperationLogs)
        .values({
          operation_type: 'user_export',
          company_id: companyId,
          initiated_by: initiatedBy,
          status: 'completed',
          total_records: companyUsers.length,
          successful_records: companyUsers.length,
          failed_records: 0,
          results: { exportedCount: companyUsers.length },
          completed_at: new Date()
        });

      return csvContent;
    } catch (error) {
      errorLogger.logError(
        `Failed to export users for company ${companyId}`,
        error,
        'bulk-operations-service'
      );
      throw error;
    }
  }

  /**
   * Generate a random password
   */
  private generateRandomPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * Bulk role assignment
   */
  async bulkRoleAssignment(
    data: BulkRoleAssignmentData,
    companyId: number,
    initiatedBy: number
  ): Promise<BulkOperationResult> {
    const startTime = Date.now();

    // Create operation log
    const [operationLog] = await db
      .insert(bulkOperationLogs)
      .values({
        operation_type: 'bulk_role_assign',
        company_id: companyId,
        initiated_by: initiatedBy,
        status: 'processing',
        operation_data: data
      })
      .returning();

    try {
      const successfulRecords: any[] = [];
      const failedRecords: Array<{ record: any; error: string }> = [];

      // Process each user
      for (const userId of data.user_ids) {
        try {
          // Verify user exists and belongs to company
          const [user] = await db
            .select()
            .from(users)
            .where(and(
              eq(users.id, userId),
              eq(users.company_id, companyId)
            ));

          if (!user) {
            failedRecords.push({
              record: { userId },
              error: `User ${userId} not found or not in company`
            });
            continue;
          }

          // Process each role
          for (const roleId of data.role_ids) {
            try {
              if (data.action === 'assign') {
                // Check if assignment already exists
                const [existing] = await db
                  .select()
                  .from(userRoles)
                  .where(and(
                    eq(userRoles.user_id, userId),
                    eq(userRoles.role_id, roleId)
                  ));

                if (!existing) {
                  await db
                    .insert(userRoles)
                    .values({
                      user_id: userId,
                      role_id: roleId,
                      assigned_by: initiatedBy,
                      justification: data.justification
                    });

                  successfulRecords.push({
                    userId,
                    roleId,
                    action: 'assigned'
                  });
                }
              } else if (data.action === 'remove') {
                const result = await db
                  .delete(userRoles)
                  .where(and(
                    eq(userRoles.user_id, userId),
                    eq(userRoles.role_id, roleId)
                  ));

                if (result.rowCount > 0) {
                  successfulRecords.push({
                    userId,
                    roleId,
                    action: 'removed'
                  });
                }
              }
            } catch (error) {
              failedRecords.push({
                record: { userId, roleId },
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }
        } catch (error) {
          failedRecords.push({
            record: { userId },
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      const result: BulkOperationResult = {
        success: successfulRecords.length,
        failed: failedRecords.length,
        errors: failedRecords.map(f => f.error),
        details: {
          successfulRecords,
          failedRecords
        }
      };

      // Update operation log
      await db
        .update(bulkOperationLogs)
        .set({
          status: 'completed',
          total_records: data.user_ids.length * data.role_ids.length,
          successful_records: successfulRecords.length,
          failed_records: failedRecords.length,
          results: result,
          completed_at: new Date(),
          processing_time_ms: Date.now() - startTime
        })
        .where(eq(bulkOperationLogs.id, operationLog.id));

      return result;
    } catch (error) {
      // Update operation log with error
      await db
        .update(bulkOperationLogs)
        .set({
          status: 'failed',
          results: { error: error instanceof Error ? error.message : 'Unknown error' },
          completed_at: new Date(),
          processing_time_ms: Date.now() - startTime
        })
        .where(eq(bulkOperationLogs.id, operationLog.id));

      throw error;
    }
  }

  /**
   * Get bulk operation logs for a company
   */
  async getBulkOperationLogs(companyId: number): Promise<BulkOperationLog[]> {
    try {
      return await db
        .select()
        .from(bulkOperationLogs)
        .where(eq(bulkOperationLogs.company_id, companyId))
        .orderBy(bulkOperationLogs.created_at);
    } catch (error) {
      errorLogger.logError(
        `Failed to get bulk operation logs for company ${companyId}`,
        error,
        'bulk-operations-service'
      );
      throw error;
    }
  }
}

export const bulkOperationsService = new BulkOperationsService();
