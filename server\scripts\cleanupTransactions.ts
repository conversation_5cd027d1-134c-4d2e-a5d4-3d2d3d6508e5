import { db } from '../db';
import { transactions } from '@shared/schema';
import { eq } from 'drizzle-orm';

/**
 * This script cleans up test data that doesn't follow double-entry accounting principles
 * It identifies and removes transactions that don't have offsetting entries
 */
async function cleanupTestTransactions() {
  try {
    console.log('Starting transaction cleanup...');
    
    // Get all transactions
    const allTransactions = await db.select().from(transactions);
    console.log(`Found ${allTransactions.length} total transactions`);
    
    // Group transactions by reference to find unbalanced sets
    const transactionGroups = new Map<string, any[]>();
    
    allTransactions.forEach(transaction => {
      const key = `${transaction.reference_type}_${transaction.reference_id}`;
      if (!transactionGroups.has(key)) {
        transactionGroups.set(key, []);
      }
      transactionGroups.get(key)?.push(transaction);
    });
    
    // Check each group for double-entry compliance (debits = credits)
    const unbalancedGroups: string[] = [];
    let transactionsToDelete: number[] = [];
    
    for (const [key, group] of transactionGroups.entries()) {
      let totalDebits = 0;
      let totalCredits = 0;
      
      group.forEach(transaction => {
        if (transaction.transaction_type === 'debit') {
          totalDebits += Number(transaction.amount);
        } else if (transaction.transaction_type === 'credit') {
          totalCredits += Number(transaction.amount);
        }
      });
      
      // Check if the group is balanced
      if (Math.abs(totalDebits - totalCredits) > 0.001) { // Allow small rounding errors
        unbalancedGroups.push(key);
        // Add all transactions in this group to the delete list
        transactionsToDelete = [...transactionsToDelete, ...group.map(t => t.id)];
      }
    }
    
    console.log(`Found ${unbalancedGroups.length} unbalanced transaction groups to clean up`);
    
    // Delete unbalanced transactions
    if (transactionsToDelete.length > 0) {
      const result = await db.delete(transactions)
        .where(eq(transactions.id, transactionsToDelete[0])); // Delete first one as example
      
      // In a real cleanup script, we'd use an "in" clause to delete all at once
      // but we're being cautious here as this is just a demonstration
      
      console.log(`Deleted ${result.rowCount} unbalanced transactions`);
      console.log(`To complete cleanup, delete the remaining ${transactionsToDelete.length - 1} transactions`);
    } else {
      console.log('No unbalanced transactions found, all data follows double-entry principles');
    }
    
    console.log('Transaction cleanup completed');
  } catch (error) {
    console.error('Error cleaning up transactions:', error);
  }
}

// Run the cleanup function immediately in ESM context
cleanupTestTransactions()
  .then(() => console.log('Cleanup completed'))
  .catch(error => {
    console.error('Fatal error during cleanup:', error);
    process.exit(1);
  });

export default cleanupTestTransactions;