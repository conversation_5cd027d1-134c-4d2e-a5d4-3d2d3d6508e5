import { db } from '../db';
import { eq, and, like, sql } from 'drizzle-orm';
import { agents, users } from '@shared/schema';
import { Agent, InsertAgent, User } from '@shared/schema';
import errorLogger from '../utils/errorLogger';
import { IAgentStorage } from './interfaces';

export class AgentStorage implements IAgentStorage {
  async getAgent(id: number): Promise<(Agent & { full_name?: string, email?: string, phone?: string }) | undefined> {
    try {
      const result = await db
        .select({
          agent: agents,
          user: users
        })
        .from(agents)
        .leftJoin(users, eq(agents.user_id, users.id))
        .where(eq(agents.id, id));

      if (result.length === 0) {
        return undefined;
      }

      // Combine agent and user data
      return {
        ...result[0].agent,
        full_name: result[0].user?.full_name,
        email: result[0].user?.email,
        phone: result[0].user?.phone || undefined
      };
    } catch (error) {
      errorLogger.logError(`Error fetching agent id=${id}`, 'agent-fetch', error as Error);
      return undefined;
    }
  }

  async getAgentsByCompany(companyId: number): Promise<(Agent & { full_name?: string, email?: string, phone?: string })[]> {
    try {
      const result = await db
        .select({
          agent: agents,
          user: users
        })
        .from(agents)
        .leftJoin(users, eq(agents.user_id, users.id))
        .where(eq(agents.company_id, companyId));

      // Combine agent and user data
      return result.map(row => ({
        ...row.agent,
        full_name: row.user?.full_name,
        email: row.user?.email,
        phone: row.user?.phone || undefined
      }));
    } catch (error) {
      errorLogger.logError(`Error fetching agents for company id=${companyId}`, 'agent-fetch', error as Error);
      return [];
    }
  }

  async getAgentByUserAndCompany(userId: number, companyId: number): Promise<Agent | undefined> {
    try {
      // Get agent with specific user ID and company ID
      const [agent] = await db
        .select()
        .from(agents)
        .where(and(
          eq(agents.user_id, userId),
          eq(agents.company_id, companyId)
        ));

      return agent;
    } catch (error) {
      errorLogger.logError(`Error fetching agent for user id=${userId} and company id=${companyId}`, 'agent-fetch', error as Error);
      return undefined;
    }
  }

  async createAgent(agentData: InsertAgent): Promise<Agent> {
    try {
      const [agent] = await db
        .insert(agents)
        .values(agentData)
        .returning();

      return agent;
    } catch (error) {
      errorLogger.logError(`Error creating agent`, 'agent-create', error as Error);
      throw error;
    }
  }

  async updateAgent(id: number, companyId: number, agentData: Partial<InsertAgent>): Promise<Agent | undefined> {
    try {
      // First check if the agent exists and belongs to the specified company
      const [existingAgent] = await db
        .select()
        .from(agents)
        .where(and(
          eq(agents.id, id),
          eq(agents.company_id, companyId)
        ));

      if (!existingAgent) {
        return undefined;
      }

      // Update the agent
      const [updatedAgent] = await db
        .update(agents)
        .set(agentData)
        .where(and(
          eq(agents.id, id),
          eq(agents.company_id, companyId)
        ))
        .returning();

      return updatedAgent;
    } catch (error) {
      errorLogger.logError(`Error updating agent id=${id}`, 'agent-update', error as Error);
      throw error;
    }
  }

  async deleteAgent(id: number, companyId: number): Promise<boolean> {
    try {
      // First check if the agent exists and belongs to the specified company
      const [existingAgent] = await db
        .select()
        .from(agents)
        .where(and(
          eq(agents.id, id),
          eq(agents.company_id, companyId)
        ));

      if (!existingAgent) {
        return false;
      }

      // Delete the agent
      await db
        .delete(agents)
        .where(and(
          eq(agents.id, id),
          eq(agents.company_id, companyId)
        ));

      return true;
    } catch (error) {
      errorLogger.logError(`Error deleting agent id=${id}`, 'agent-delete', error as Error);
      throw error;
    }
  }

  /**
   * Get the highest serial number for a given company and prefix (e.g., 'GS-')
   * Returns the highest serial as a number, or 0 if none found.
   * Only considers agents from the specific company.
   */
  async getHighestAgentSerial(companyId: number, prefix: string): Promise<number> {
    try {
      // Find the max serial for this company and prefix
      // agent_reference_code is like 'GS-001', 'GS-002', ...
      const result = await db.select({ maxString: sql`MAX(${agents.agent_reference_code})` })
        .from(agents)
        .where(
          and(
            eq(agents.company_id, companyId),
            like(agents.agent_reference_code, `${prefix}%`)
          )
        );
      const maxString = result[0]?.maxString as string | undefined;
      if (!maxString) return 0;

      // Extract the serial part (e.g., 'GS-012' => 12)
      const match = maxString.match(/^(.*-)(\d{3})$/);
      if (match) {
        return parseInt(match[2], 10);
      }
      return 0;
    } catch (error) {
      errorLogger.logError(`Error getting highest agent serial for company ${companyId} and prefix ${prefix}`, 'agent-serial', error as Error);
      return 0;
    }
  }
}
