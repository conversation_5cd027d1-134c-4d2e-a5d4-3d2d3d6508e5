import { db } from '../db';
import { companyPrefixSettings } from '@shared/schema';
import { eq } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';
import { CompanyPrefixSettings } from '@shared/schema';

export interface ICompanyPrefixSettingsStorage {
  getCompanyPrefixSettings(companyId: number): Promise<CompanyPrefixSettings | undefined>;
  createCompanyPrefixSettings(settings: CompanyPrefixSettings): Promise<CompanyPrefixSettings>;
  updateCompanyPrefixSettings(companyId: number, settings: Partial<CompanyPrefixSettings>): Promise<CompanyPrefixSettings>;
  deleteCompanyPrefixSettings(companyId: number): Promise<void>;
}

export class CompanyPrefixSettingsStorage implements ICompanyPrefixSettingsStorage {
  async getCompanyPrefixSettings(companyId: number): Promise<CompanyPrefixSettings | undefined> {
    try {
      const settings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });
      return settings;
    } catch (error) {
      errorLogger.logError(`Error fetching company prefix settings for company id=${companyId}`, 'prefix-settings-fetch', error as Error);
      return undefined;
    }
  }

  async createCompanyPrefixSettings(settings: CompanyPrefixSettings): Promise<CompanyPrefixSettings> {
    try {
      const [createdSettings] = await db.insert(companyPrefixSettings)
        .values(settings)
        .returning();

      return createdSettings;
    } catch (error) {
      errorLogger.logError(`Error creating company prefix settings for company id=${settings.company_id}`, 'prefix-settings-create', error as Error);
      throw error;
    }
  }

  async updateCompanyPrefixSettings(companyId: number, settings: Partial<CompanyPrefixSettings>): Promise<CompanyPrefixSettings> {
    try {
      // Check if settings exist
      const existingSettings = await this.getCompanyPrefixSettings(companyId);

      if (!existingSettings) {
        // If settings don't exist, create them
        return await this.createCompanyPrefixSettings({
          company_id: companyId,
          ...settings,
        } as CompanyPrefixSettings);
      }

      // Update existing settings
      const [updatedSettings] = await db.update(companyPrefixSettings)
        .set({
          ...settings,
          updated_at: new Date(),
        })
        .where(eq(companyPrefixSettings.company_id, companyId))
        .returning();

      return updatedSettings;
    } catch (error) {
      errorLogger.logError(`Error updating company prefix settings for company id=${companyId}`, 'prefix-settings-update', error as Error);
      throw error;
    }
  }

  async deleteCompanyPrefixSettings(companyId: number): Promise<void> {
    try {
      await db.delete(companyPrefixSettings)
        .where(eq(companyPrefixSettings.company_id, companyId));
    } catch (error) {
      errorLogger.logError(`Error deleting company prefix settings for company id=${companyId}`, 'prefix-settings-delete', error as Error);
      throw error;
    }
  }
}
