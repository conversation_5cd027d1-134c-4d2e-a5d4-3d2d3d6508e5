import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Users, Shield, Settings, Info } from 'lucide-react';
import { RoleHierarchyBuilder } from '@/components/roles/RoleHierarchyBuilder';
import { useAuth } from '@/lib/auth';

export default function RoleHierarchyPage() {
  const [location, navigate] = useLocation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('builder');

  const currentCompany = user?.companies?.[0];
  const companyId = currentCompany?.company_id || currentCompany?.id;

  const handleEditRole = (roleId: number) => {
    navigate(`/user-management/roles/${roleId}`);
  };

  const handleCreateRole = () => {
    // This would open a role creation dialog or navigate to a creation page
    navigate('/user-management/roles/create');
  };

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => navigate('/user-management')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Role Hierarchy Management</h1>
            <p className="text-muted-foreground">
              Design and manage role hierarchies with inheritance controls
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => navigate('/user-management')}>
            <Users className="h-4 w-4 mr-2" />
            User Management
          </Button>
          <Button variant="outline" onClick={() => navigate('/user-management/permissions')}>
            <Shield className="h-4 w-4 mr-2" />
            Permissions
          </Button>
        </div>
      </div>

      {/* Company Context */}
      {currentCompany && (
        <div className="mb-6">
          <Card>
            <CardContent className="flex items-center justify-between p-4">
              <div className="flex items-center gap-2">
                <Badge variant="outline">Current Company</Badge>
                <span className="font-medium">{currentCompany.name}</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Company ID: {companyId}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="builder">Hierarchy Builder</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="builder">
          <RoleHierarchyBuilder
            companyId={companyId}
            onEditRole={handleEditRole}
            onCreateRole={handleCreateRole}
          />
        </TabsContent>

        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Hierarchy Statistics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Hierarchy Statistics
                </CardTitle>
                <CardDescription>
                  Overview of your role hierarchy structure
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Total Roles</span>
                    <Badge variant="outline">-</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Root Roles</span>
                    <Badge variant="outline">-</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Maximum Depth</span>
                    <Badge variant="outline">-</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">System Roles</span>
                    <Badge variant="outline">-</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Inheritance Types */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Inheritance Types
                </CardTitle>
                <CardDescription>
                  Distribution of inheritance types in your hierarchy
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Inherit</span>
                    <Badge variant="default">-</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Override</span>
                    <Badge variant="secondary">-</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Deny</span>
                    <Badge variant="destructive">-</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Hierarchy Settings
              </CardTitle>
              <CardDescription>
                Configure role hierarchy behavior and validation rules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Validation Settings */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Validation Rules</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Prevent Circular Dependencies</div>
                        <div className="text-sm text-muted-foreground">
                          Automatically detect and prevent circular role hierarchies
                        </div>
                      </div>
                      <Badge variant="default">Enabled</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Maximum Hierarchy Depth</div>
                        <div className="text-sm text-muted-foreground">
                          Limit the maximum depth of role hierarchies
                        </div>
                      </div>
                      <Badge variant="outline">10 levels</Badge>
                    </div>
                  </div>
                </div>

                {/* Default Settings */}
                <div>
                  <h3 className="text-lg font-medium mb-3">Default Behavior</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Default Inheritance Type</div>
                        <div className="text-sm text-muted-foreground">
                          Default inheritance type for new role relationships
                        </div>
                      </div>
                      <Badge variant="default">Inherit</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Auto-expand New Relationships</div>
                        <div className="text-sm text-muted-foreground">
                          Automatically expand tree when new relationships are created
                        </div>
                      </div>
                      <Badge variant="default">Enabled</Badge>
                    </div>
                  </div>
                </div>

                {/* Information */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">About Role Hierarchies</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        Role hierarchies allow you to create parent-child relationships between roles,
                        enabling inheritance of permissions and creating organizational structures.
                        Use inheritance types to control how permissions flow through the hierarchy.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
