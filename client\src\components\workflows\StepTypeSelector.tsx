import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ArrowRight, GitBranch, Users, CheckCircle, UserCheck, Info } from 'lucide-react';

export type StepType = 'sequential' | 'parallel' | 'any_one' | 'majority' | 'unanimous';

interface StepTypeSelectorProps {
  value: StepType;
  onChange: (value: StepType) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showTooltip?: boolean;
}

interface StepTypeDisplayProps {
  type: StepType;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showTooltip?: boolean;
}

const stepTypeConfig = {
  sequential: {
    label: 'Sequential',
    description: 'Steps execute one after another in order. Each step must complete before the next begins.',
    icon: ArrowRight,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    variant: 'default' as const,
  },
  parallel: {
    label: 'Parallel',
    description: 'Multiple approvers can act simultaneously. Configurable number of approvals required.',
    icon: GitBranch,
    color: 'bg-green-100 text-green-800 border-green-200',
    variant: 'secondary' as const,
  },
  any_one: {
    label: 'Any One',
    description: 'Any single approver can approve this step. First approval completes the step.',
    icon: UserCheck,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    variant: 'outline' as const,
  },
  majority: {
    label: 'Majority',
    description: 'Majority of assigned approvers must approve. More than 50% approval required.',
    icon: Users,
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    variant: 'secondary' as const,
  },
  unanimous: {
    label: 'Unanimous',
    description: 'All assigned approvers must approve. 100% approval required.',
    icon: CheckCircle,
    color: 'bg-red-100 text-red-800 border-red-200',
    variant: 'destructive' as const,
  },
};

export function StepTypeDisplay({ 
  type, 
  size = 'md', 
  showIcon = true, 
  showTooltip = true 
}: StepTypeDisplayProps) {
  const config = stepTypeConfig[type];
  const Icon = config.icon;
  
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-sm px-2 py-1',
    lg: 'text-base px-3 py-1.5',
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };

  const badge = (
    <Badge 
      variant={config.variant}
      className={`${sizeClasses[size]} ${config.color} flex items-center gap-1`}
    >
      {showIcon && <Icon className={iconSizes[size]} />}
      {config.label}
    </Badge>
  );

  if (!showTooltip) {
    return badge;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {badge}
        </TooltipTrigger>
        <TooltipContent>
          <div className="flex items-start gap-2 max-w-xs">
            <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <span>{config.description}</span>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function StepTypeSelector({ 
  value, 
  onChange, 
  disabled = false, 
  size = 'md',
  showIcon = true,
  showTooltip = true 
}: StepTypeSelectorProps) {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12',
  };

  return (
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className={`w-full ${sizeClasses[size]}`}>
        <SelectValue>
          <StepTypeDisplay 
            type={value} 
            size={size} 
            showIcon={showIcon} 
            showTooltip={false} 
          />
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {Object.entries(stepTypeConfig).map(([type, config]) => (
          <SelectItem key={type} value={type}>
            <div className="flex items-center gap-2">
              <config.icon className="h-4 w-4" />
              <div className="flex flex-col">
                <span className="font-medium">{config.label}</span>
                {showTooltip && (
                  <span className="text-xs text-muted-foreground">
                    {config.description}
                  </span>
                )}
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

// Utility function to get step type configuration
export function getStepTypeConfig(type: StepType) {
  return stepTypeConfig[type];
}

// Utility function to get all step types
export function getAllStepTypes(): StepType[] {
  return Object.keys(stepTypeConfig) as StepType[];
}

// Utility function to get step type requirements
export function getStepTypeRequirements(type: StepType, totalApprovers: number): {
  minRequired: number;
  maxRequired: number;
  defaultRequired: number;
  description: string;
} {
  switch (type) {
    case 'sequential':
      return {
        minRequired: 1,
        maxRequired: 1,
        defaultRequired: 1,
        description: 'Sequential steps require exactly 1 approver per step',
      };
    case 'parallel':
      return {
        minRequired: 1,
        maxRequired: totalApprovers,
        defaultRequired: Math.min(2, totalApprovers),
        description: 'Parallel steps can require any number of approvers',
      };
    case 'any_one':
      return {
        minRequired: 1,
        maxRequired: 1,
        defaultRequired: 1,
        description: 'Any one step requires exactly 1 approval from any approver',
      };
    case 'majority':
      return {
        minRequired: Math.ceil(totalApprovers / 2),
        maxRequired: Math.ceil(totalApprovers / 2),
        defaultRequired: Math.ceil(totalApprovers / 2),
        description: `Majority requires ${Math.ceil(totalApprovers / 2)} out of ${totalApprovers} approvers`,
      };
    case 'unanimous':
      return {
        minRequired: totalApprovers,
        maxRequired: totalApprovers,
        defaultRequired: totalApprovers,
        description: `Unanimous requires all ${totalApprovers} approvers`,
      };
    default:
      return {
        minRequired: 1,
        maxRequired: totalApprovers,
        defaultRequired: 1,
        description: 'Unknown step type',
      };
  }
}
