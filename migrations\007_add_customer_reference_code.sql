-- Migration to add customer_reference_code column to customers table
-- This column will store company-specific customer identifiers

-- Add customer_reference_code column to customers table
ALTER TABLE "customers" 
  ADD COLUMN IF NOT EXISTS "customer_reference_code" TEXT;

-- Create an index for faster lookups by customer_reference_code
CREATE INDEX IF NOT EXISTS idx_customers_reference_code ON customers(customer_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN customers.customer_reference_code IS 'Company-specific customer identifier string (e.g., GS-001) that is unique within each company';
