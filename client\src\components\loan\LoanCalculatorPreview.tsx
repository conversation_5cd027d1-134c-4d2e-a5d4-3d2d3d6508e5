import React, { useState } from 'react';
import { 
  Calculator, 
  DollarSign, 
  Percent, 
  FileText,
  ChevronDown, 
  ChevronUp,
  RefreshCw,
  ArrowRight,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { calculateLoanPayment, calculateTotalInterestAmount } from '@/utils/calculate-loan-payment';
import { formatCurrency } from '@/utils/format-utils';

interface LoanCalculatorPreviewProps {
  amount: number;
  interestRate: number;
  termMonths: number;
  interestType: string;
  startDate?: string;
  currencyCode?: string;
  locale?: string;
  paymentFrequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'yearly';
  deductInterestUpfront?: boolean;
  onRecalculate?: () => void;
}

export function LoanCalculatorPreview({
  amount,
  interestRate,
  termMonths,
  interestType,
  startDate,
  currencyCode = 'INR',
  locale = 'en-IN',
  paymentFrequency = 'monthly',
  deductInterestUpfront = true,
  onRecalculate
}: LoanCalculatorPreviewProps) {
  const [expanded, setExpanded] = useState(false);
  
  // Check for missing or invalid values
  const missingValues = !amount || !interestRate || !termMonths;
  
  // Calculate total interest
  const totalInterest = calculateTotalInterestAmount(
    amount, 
    interestRate, 
    termMonths, 
    interestType,
    paymentFrequency
  );
  
  // IMPORTANT FIX FOR UPFRONT INTEREST CALCULATION
  
  // Calculate total interest first - this determines how much is deducted upfront
  console.log(`LoanCalculatorPreview calculating interest: amount=${amount}, rate=${interestRate}%, term=${termMonths}`);
  
  // Calculate disbursed amount (if interest is deducted upfront)
  // When upfront interest is enabled, customer receives principal minus interest
  // This is the key business logic of this system - handle it carefully
  const disbursedAmount = deductInterestUpfront 
    ? (amount - totalInterest) // Customer gets principal minus interest
    : amount;                  // Customer gets full principal amount
  
  // Calculate total repayable amount
  // When upfront interest is enabled, customer repays the full principal amount
  // When interest is not upfront, customer repays principal plus interest
  const totalRepayable = deductInterestUpfront 
    ? amount                   // Must repay full principal amount
    : (amount + totalInterest); // Repays principal plus interest
  
  console.log(`FINAL CALCULATION VALUES:`);
  console.log(`Principal amount: ${amount}`);
  console.log(`Interest rate: ${interestRate}%`);
  console.log(`Term: ${termMonths} ${paymentFrequency} units`);  
  console.log(`Interest type: ${interestType}`);
  console.log(`Upfront interest? ${deductInterestUpfront ? 'YES' : 'NO'}`);
  console.log(`Total interest amount: ${totalInterest}`);
  console.log(`DISBURSED AMOUNT: ${disbursedAmount}`);
  console.log(`TOTAL REPAYABLE: ${totalRepayable}`);
  console.log(`PAYMENT PER PERIOD: ${totalRepayable / termMonths}`);
  
  // Calculate per-period payment
  const periodPayment = calculateLoanPayment(
    amount, 
    interestRate, 
    termMonths, 
    interestType,
    paymentFrequency,
    deductInterestUpfront
  );
  
  // Format payment frequency for display
  const getPaymentFrequencyLabel = () => {
    switch(paymentFrequency) {
      case 'daily': return 'Daily';
      case 'weekly': return 'Weekly';
      case 'biweekly': return 'Bi-Weekly';
      case 'monthly': return 'Monthly';
      case 'yearly': return 'Yearly';
      default: return 'Monthly';
    }
  };
  
  // Format the term in appropriate units
  const formatTermUnits = () => {
    if (!termMonths) return '';
    
    switch(paymentFrequency) {
      case 'daily': 
        return `${termMonths} ${termMonths === 1 ? 'day' : 'days'}`;
      case 'weekly': 
        return `${termMonths} ${termMonths === 1 ? 'week' : 'weeks'}`;
      case 'biweekly': 
        return `${termMonths} ${termMonths === 1 ? 'fortnight' : 'fortnights'}`;
      case 'yearly': 
        return `${termMonths} ${termMonths === 1 ? 'year' : 'years'}`;
      case 'monthly':
      default: 
        return `${termMonths} ${termMonths === 1 ? 'month' : 'months'}`;
    }
  };
  
  return (
    <Card className="mt-6 border border-dashed border-muted-foreground/50">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center cursor-pointer" onClick={() => setExpanded(!expanded)}>
          <div className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Loan Payment Preview</CardTitle>
          </div>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </div>
        <CardDescription>
          Preview payment amounts and loan details before creating
        </CardDescription>
      </CardHeader>
      
      {missingValues ? (
        <CardContent>
          <div className="flex items-center justify-center p-4 text-muted-foreground">
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            <span>Please fill in loan amount, interest rate, and term to see payment preview</span>
          </div>
        </CardContent>
      ) : expanded ? (
        <>
          <CardContent className="pt-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">{getPaymentFrequencyLabel()} Payment</span>
                <div className="flex items-center mt-1">
                  <DollarSign className="h-4 w-4 text-primary mr-1" />
                  <span className="text-2xl font-semibold">
                    {formatCurrency(periodPayment, currencyCode, locale)}
                  </span>
                </div>
                <Badge variant="outline" className="mt-2 w-fit">
                  {deductInterestUpfront ? 'Upfront Interest' : 'Interest in Installments'}
                </Badge>
              </div>
              
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Interest Amount</span>
                <div className="flex items-center mt-1">
                  <Percent className="h-4 w-4 text-amber-500 mr-1" />
                  <span className="text-2xl font-semibold">
                    {formatCurrency(totalInterest, currencyCode, locale)}
                  </span>
                </div>
                <span className="text-xs text-muted-foreground mt-2">
                  {interestRate}% on {formatCurrency(amount, currencyCode, locale)}
                </span>
              </div>
              
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Disbursed Amount</span>
                <div className="flex items-center mt-1">
                  <FileText className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-2xl font-semibold">
                    {formatCurrency(disbursedAmount, currencyCode, locale)}
                  </span>
                </div>
                <div className="flex items-center text-xs text-muted-foreground mt-2">
                  <ArrowRight className="h-3 w-3 mr-1" />
                  <span>
                    {deductInterestUpfront 
                      ? `${formatCurrency(amount, currencyCode, locale)} - ${formatCurrency(totalInterest, currencyCode, locale)} interest` 
                      : "Full Principal Amount"}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="mt-4 pt-4 border-t">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-muted-foreground mr-2" />
                  <div>
                    <div className="text-sm font-medium">Principal Amount</div>
                    <div>{formatCurrency(amount, currencyCode, locale)}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-muted-foreground mr-2" />
                  <div>
                    <div className="text-sm font-medium">Loan Duration</div>
                    <div>{formatTermUnits()}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <FileText className="h-4 w-4 text-muted-foreground mr-2" />
                  <div>
                    <div className="text-sm font-medium">Total Repayable</div>
                    <div>{formatCurrency(totalRepayable, currencyCode, locale)}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          
          {onRecalculate && (
            <CardFooter className="pt-0">
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2 w-full text-xs" 
                onClick={onRecalculate}
              >
                <RefreshCw className="h-3 w-3 mr-2" />
                Recalculate with changes
              </Button>
            </CardFooter>
          )}
        </>
      ) : (
        <CardContent className="py-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 text-primary mr-1 flex-shrink-0" />
              <span className="font-medium mr-1">{getPaymentFrequencyLabel()} Payment:</span>
              <span>{formatCurrency(periodPayment, currencyCode, locale)}</span>
            </div>
            
            <Badge variant="outline">
              {deductInterestUpfront ? 'Upfront Interest' : 'Interest in Installments'}
            </Badge>
          </div>
        </CardContent>
      )}
    </Card>
  );
}