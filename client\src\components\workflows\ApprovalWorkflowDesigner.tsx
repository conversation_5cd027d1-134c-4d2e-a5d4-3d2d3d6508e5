import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { DragDropContext, Droppable, DropResult } from '@hello-pangea/dnd';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Loader2,
  Plus,
  Save,
  AlertTriangle,
  <PERSON>ting<PERSON>,
  Play,
  Eye,
  FileText,
  Workflow,
  Clock,
  Users,
} from 'lucide-react';
import { WorkflowStep } from './WorkflowStep';
import { useApprovalWorkflows, type WorkflowType, type CreateWorkflowRequest, type WorkflowStep as WorkflowStepType } from '@/hooks/useApprovalWorkflows';
import { useToast } from '@/components/ui/use-toast';

interface ApprovalWorkflowDesignerProps {
  workflowId?: number;
  companyId?: number;
  onSave?: (workflowId: number) => void;
  onCancel?: () => void;
  className?: string;
}

const workflowTypeConfig = {
  permission_elevation: { label: 'Permission Elevation', description: 'Temporary permission requests' },
  loan_approval: { label: 'Loan Approval', description: 'Loan application approvals' },
  customer_data_access: { label: 'Customer Data Access', description: 'Sensitive data access requests' },
  emergency_access: { label: 'Emergency Access', description: 'Emergency permission grants' },
  role_assignment: { label: 'Role Assignment', description: 'Role change approvals' },
  custom: { label: 'Custom', description: 'Organization-specific workflows' },
};

export function ApprovalWorkflowDesigner({
  workflowId,
  companyId,
  onSave,
  onCancel,
  className,
}: ApprovalWorkflowDesignerProps) {
  const { toast } = useToast();
  const { createWorkflow, updateWorkflow, getWorkflow, isCreating, isUpdating } = useApprovalWorkflows(companyId);

  // Workflow state
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [workflowType, setWorkflowType] = useState<WorkflowType>('custom');
  const [autoEscalationHours, setAutoEscalationHours] = useState(24);
  const [maxEscalationLevels, setMaxEscalationLevels] = useState(3);
  const [isActive, setIsActive] = useState(true);
  const [triggerConditions, setTriggerConditions] = useState<Record<string, any>>({});

  // Steps state
  const [steps, setSteps] = useState<(WorkflowStepType & { id: string })[]>([]);
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState('designer');

  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isValid, setIsValid] = useState(false);

  // Load existing workflow if editing
  React.useEffect(() => {
    if (workflowId) {
      loadWorkflow(workflowId);
    }
  }, [workflowId]);

  const loadWorkflow = async (id: number) => {
    try {
      const workflow = await getWorkflow(id);
      setWorkflowName(workflow.name);
      setWorkflowDescription(workflow.description || '');
      setWorkflowType(workflow.workflow_type);
      setAutoEscalationHours(workflow.auto_escalation_hours);
      setMaxEscalationLevels(workflow.max_escalation_levels);
      setIsActive(workflow.is_active);
      setTriggerConditions(workflow.trigger_conditions || {});
      
      if (workflow.steps) {
        const stepsWithIds = workflow.steps.map((step, index) => ({
          ...step,
          id: `step-${index}`,
        }));
        setSteps(stepsWithIds);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load workflow',
        variant: 'destructive',
      });
    }
  };

  // Validation
  const validateWorkflow = useCallback(() => {
    const newErrors: Record<string, string> = {};

    if (!workflowName.trim()) {
      newErrors.name = 'Workflow name is required';
    }

    if (steps.length === 0) {
      newErrors.steps = 'At least one workflow step is required';
    }

    steps.forEach((step, index) => {
      if (!step.stepName.trim()) {
        newErrors[`step-${index}-name`] = 'Step name is required';
      }

      const totalApprovers = (step.approverRoles?.length || 0) + (step.approverUsers?.length || 0);
      if (totalApprovers === 0) {
        newErrors[`step-${index}-approvers`] = 'At least one approver is required';
      }

      if (step.requiredApprovers > totalApprovers) {
        newErrors[`step-${index}-required`] = 'Required approvers cannot exceed total approvers';
      }
    });

    setErrors(newErrors);
    setIsValid(Object.keys(newErrors).length === 0);
    return Object.keys(newErrors).length === 0;
  }, [workflowName, steps]);

  React.useEffect(() => {
    validateWorkflow();
  }, [validateWorkflow]);

  // Step management
  const handleAddStep = () => {
    const newStep: WorkflowStepType & { id: string } = {
      id: `step-${Date.now()}`,
      stepName: `Step ${steps.length + 1}`,
      stepType: 'sequential',
      requiredApprovers: 1,
      approverRoles: [],
      approverUsers: [],
      escalationRoles: [],
      stepTimeoutHours: 24,
      isOptional: false,
      conditions: {},
    };
    setSteps([...steps, newStep]);
    setExpandedSteps(new Set([...expandedSteps, newStep.id]));
  };

  const handleUpdateStep = (stepId: string, updates: Partial<WorkflowStepType>) => {
    setSteps(steps.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const handleDeleteStep = (stepId: string) => {
    setSteps(steps.filter(step => step.id !== stepId));
    setExpandedSteps(new Set([...expandedSteps].filter(id => id !== stepId)));
  };

  const handleDuplicateStep = (stepId: string) => {
    const stepToDuplicate = steps.find(step => step.id === stepId);
    if (stepToDuplicate) {
      const newStep = {
        ...stepToDuplicate,
        id: `step-${Date.now()}`,
        stepName: `${stepToDuplicate.stepName} (Copy)`,
      };
      const stepIndex = steps.findIndex(step => step.id === stepId);
      const newSteps = [...steps];
      newSteps.splice(stepIndex + 1, 0, newStep);
      setSteps(newSteps);
    }
  };

  const handleToggleStepExpansion = (stepId: string) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(stepId)) {
      newExpanded.delete(stepId);
    } else {
      newExpanded.add(stepId);
    }
    setExpandedSteps(newExpanded);
  };

  // Drag and drop
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const newSteps = Array.from(steps);
    const [reorderedStep] = newSteps.splice(result.source.index, 1);
    newSteps.splice(result.destination.index, 0, reorderedStep);

    setSteps(newSteps);
  };

  // Save workflow
  const handleSave = async () => {
    if (!validateWorkflow()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors before saving',
        variant: 'destructive',
      });
      return;
    }

    const workflowData: CreateWorkflowRequest = {
      name: workflowName,
      description: workflowDescription,
      workflowType,
      triggerConditions,
      autoEscalationHours,
      maxEscalationLevels,
      steps: steps.map(({ id, ...step }) => step),
    };

    try {
      if (workflowId) {
        await updateWorkflow({ id: workflowId, data: workflowData });
      } else {
        const newWorkflow = await createWorkflow(workflowData);
        if (onSave) {
          onSave(newWorkflow.id);
        }
      }
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const isLoading = isCreating || isUpdating;

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Workflow className="h-5 w-5" />
              {workflowId ? 'Edit Workflow' : 'Create Approval Workflow'}
            </CardTitle>
            <CardDescription>
              Design approval workflows with drag-and-drop steps and escalation rules
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={isValid ? 'default' : 'destructive'}>
              {isValid ? 'Valid' : 'Invalid'}
            </Badge>
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button onClick={handleSave} disabled={!isValid || isLoading}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-1" />
              )}
              {workflowId ? 'Update' : 'Create'} Workflow
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="designer" className="flex items-center gap-2">
              <Workflow className="h-4 w-4" />
              Designer
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="designer" className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="workflowName">Workflow Name *</Label>
                    <Input
                      id="workflowName"
                      value={workflowName}
                      onChange={(e) => setWorkflowName(e.target.value)}
                      placeholder="Enter workflow name..."
                      className={errors.name ? 'border-red-300' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-600 mt-1">{errors.name}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="workflowType">Workflow Type</Label>
                    <Select value={workflowType} onValueChange={(value: WorkflowType) => setWorkflowType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(workflowTypeConfig).map(([key, config]) => (
                          <SelectItem key={key} value={key}>
                            <div>
                              <div className="font-medium">{config.label}</div>
                              <div className="text-xs text-muted-foreground">{config.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="workflowDescription">Description</Label>
                  <Textarea
                    id="workflowDescription"
                    value={workflowDescription}
                    onChange={(e) => setWorkflowDescription(e.target.value)}
                    placeholder="Describe the purpose and scope of this workflow..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Workflow Steps */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">Workflow Steps</CardTitle>
                    <CardDescription>
                      Design the approval process with sequential or parallel steps
                    </CardDescription>
                  </div>
                  <Button onClick={handleAddStep}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Step
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {steps.length === 0 ? (
                  <div className="text-center py-12 border-2 border-dashed border-gray-200 rounded-lg">
                    <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Steps Added</h3>
                    <p className="text-gray-500 mb-4">
                      Add your first approval step to get started
                    </p>
                    <Button onClick={handleAddStep}>
                      <Plus className="h-4 w-4 mr-1" />
                      Add First Step
                    </Button>
                  </div>
                ) : (
                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="workflow-steps" type="STEP">
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.droppableProps}
                          className="space-y-4"
                        >
                          {steps.map((step, index) => (
                            <WorkflowStep
                              key={step.id}
                              step={step}
                              index={index}
                              isExpanded={expandedSteps.has(step.id)}
                              onToggleExpand={handleToggleStepExpansion}
                              onUpdateStep={handleUpdateStep}
                              onDeleteStep={handleDeleteStep}
                              onDuplicateStep={handleDuplicateStep}
                              companyId={companyId}
                              hasErrors={Object.keys(errors).some(key => key.startsWith(`step-${index}`))}
                              errorMessage={Object.entries(errors)
                                .filter(([key]) => key.startsWith(`step-${index}`))
                                .map(([, message]) => message)
                                .join(', ')}
                            />
                          ))}
                          {provided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </DragDropContext>
                )}

                {errors.steps && (
                  <Alert variant="destructive" className="mt-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{errors.steps}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            {/* Global Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Global Settings</CardTitle>
                <CardDescription>
                  Configure workflow-wide settings and escalation behavior
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="autoEscalation">Auto Escalation (hours)</Label>
                    <Input
                      id="autoEscalation"
                      type="number"
                      min="1"
                      max="8760"
                      value={autoEscalationHours}
                      onChange={(e) => setAutoEscalationHours(parseInt(e.target.value) || 24)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxEscalation">Max Escalation Levels</Label>
                    <Input
                      id="maxEscalation"
                      type="number"
                      min="1"
                      max="10"
                      value={maxEscalationLevels}
                      onChange={(e) => setMaxEscalationLevels(parseInt(e.target.value) || 3)}
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    checked={isActive}
                    onCheckedChange={setIsActive}
                  />
                  <Label>Activate this workflow</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-6">
            {/* Workflow Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Workflow Preview</CardTitle>
                <CardDescription>
                  Review your workflow configuration before saving
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <Label className="text-xs text-muted-foreground">Name</Label>
                      <p className="font-medium">{workflowName || 'Untitled Workflow'}</p>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Type</Label>
                      <p className="font-medium">{workflowTypeConfig[workflowType].label}</p>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Steps</Label>
                      <p className="font-medium">{steps.length} steps</p>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Status</Label>
                      <Badge variant={isActive ? 'default' : 'secondary'}>
                        {isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>

                  {workflowDescription && (
                    <div>
                      <Label className="text-xs text-muted-foreground">Description</Label>
                      <p className="text-sm">{workflowDescription}</p>
                    </div>
                  )}

                  <Separator />

                  <div>
                    <Label className="text-sm font-medium">Step Summary</Label>
                    <div className="mt-2 space-y-2">
                      {steps.map((step, index) => (
                        <div key={step.id} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{index + 1}</Badge>
                            <span className="font-medium">{step.stepName}</span>
                            <Badge variant="secondary">{step.stepType}</Badge>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Users className="h-4 w-4" />
                            <span>{(step.approverRoles?.length || 0) + (step.approverUsers?.length || 0)} approvers</span>
                            <Clock className="h-4 w-4" />
                            <span>{step.stepTimeoutHours}h</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
