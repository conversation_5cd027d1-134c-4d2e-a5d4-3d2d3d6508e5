# TrackFina Docker Deployment Guide

This guide will help you deploy the TrackFina application using Docker. Docker allows you to build and run the application in a containerized environment, making it easy to deploy on any system that supports Docker.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/) installed on your system
- [Docker Compose](https://docs.docker.com/compose/install/) installed on your system
- Basic understanding of Docker and containerization

## Setting Up

1. Clone the repository or download the source code to your local machine.

2. Navigate to the project root directory.

3. Create a `.env` file based on the provided `.env.example`:
   ```bash
   cp .env.example .env
   ```

4. Edit the `.env` file to set appropriate values for your environment, especially:
   - `SESSION_SECRET`: A random string used for encrypting session data
   - Update any other configuration values as needed

## Building and Running with Docker

### Option 1: Using Docker Compose (Recommended)

To start the complete application stack (web application and PostgreSQL database):

```bash
docker-compose up -d
```

This will:
- Build the application container
- Start a PostgreSQL database container
- Initialize the database with required schema
- Connect the application to the database
- Expose the application on port 3000

To stop the services:

```bash
docker-compose down
```

To stop the services and remove all data volumes (this will delete all database data):

```bash
docker-compose down -v
```

### Option 2: Building and Running Separately

If you prefer to manage the containers independently:

1. Build the Docker image:
   ```bash
   docker build -t trackfina .
   ```

2. Run the PostgreSQL database:
   ```bash
   docker run -d --name trackfina-db \
     -e POSTGRES_USER=trackfina \
     -e POSTGRES_PASSWORD=trackfina_password \
     -e POSTGRES_DB=trackfina \
     -v $(pwd)/schema.sql:/docker-entrypoint-initdb.d/schema.sql \
     -p 5432:5432 \
     postgres:15-alpine
   ```

3. Run the TrackFina application:
   ```bash
   docker run -d --name trackfina-app \
     --link trackfina-db:postgres \
     -e DATABASE_URL=*****************************************************/trackfina \
     -e PORT=3000 \
     -e NODE_ENV=production \
     -e SESSION_SECRET=your_session_secret_here \
     -p 3000:3000 \
     trackfina
   ```

## Accessing the Application

Once the containers are running, access the application by navigating to:

```
http://localhost:3000
```

Default login credentials:
- Username: admin
- Password: admin123

⚠️ **Important Security Note:** Change the default admin password immediately after the first login.

## Database Management

The PostgreSQL database is automatically initialized with the required schema when first started. To connect to the database directly:

```bash
docker exec -it trackfina-postgres psql -U trackfina -d trackfina
```

To backup the database:

```bash
docker exec -t trackfina-postgres pg_dump -U trackfina trackfina > backup_$(date +%Y-%m-%d_%H-%M-%S).sql
```

To restore from a backup:

```bash
cat backup_file.sql | docker exec -i trackfina-postgres psql -U trackfina -d trackfina
```

## Production Deployment Considerations

When deploying to production, consider the following:

1. Use a proper domain name with HTTPS (secure TLS/SSL)
2. Set up proper backup procedures for the database volume
3. Use a stronger password for the PostgreSQL database
4. Change all default credentials
5. Consider using a dedicated database server or managed PostgreSQL service
6. Set up monitoring and logging solutions
7. Configure proper resource limits for containers

## Troubleshooting

### Application Container Won't Start

Check the logs:
```bash
docker logs trackfina-app
```

### Database Issues

Check if the database is running:
```bash
docker ps | grep trackfina-postgres
```

Check database logs:
```bash
docker logs trackfina-postgres
```

### Network Issues

Ensure ports are not being used by other services:
```bash
netstat -tuln | grep 3000
netstat -tuln | grep 5432
```

## Updating the Application

To update to a new version:

1. Pull the latest code or checkout the desired version
2. Rebuild and restart the containers:
   ```bash
   docker-compose down
   docker-compose up -d --build
   ```

## Need Help?

If you encounter issues, please refer to:
- The project documentation
- Docker documentation at https://docs.docker.com/
- Submit an issue in the project repository