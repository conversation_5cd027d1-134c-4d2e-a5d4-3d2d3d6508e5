import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { Briefcase, DollarSign, Users, CreditCard } from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface TopReseller {
  name: string;
  revenue: number;
  percentage: number;
}

interface PartnerProgramMetrics {
  activeResellers: number;
  resellerRevenue: number;
  activeReferrals: number;
  referralCommissions: number;
  topResellers: TopReseller[];
}

interface PartnerMetricsProps {
  metrics: PartnerProgramMetrics;
  isLoading: boolean;
}

export default function PartnerMetrics({ metrics, isLoading }: PartnerMetricsProps) {
  // Function to render a metric card
  const renderMetricCard = (
    icon: React.ReactNode,
    iconClass: string,
    title: string,
    value: React.ReactNode
  ) => {
    return (
      <div className="bg-gray-50 overflow-hidden shadow-sm rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className={`metric-card-icon ${iconClass}`}>
              {icon}
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
                <dd className="flex items-baseline">
                  <div className="text-2xl font-semibold text-gray-900">
                    {isLoading ? (
                      <Skeleton className="h-7 w-16" />
                    ) : (
                      value
                    )}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Function to render top resellers list
  const renderTopResellers = () => {
    if (isLoading) {
      return Array(4)
        .fill(0)
        .map((_, index) => (
          <div className="py-2" key={`skeleton-${index}`}>
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-40" />
              <Skeleton className="h-5 w-20" />
            </div>
            <Skeleton className="mt-1 h-2 w-full" />
          </div>
        ));
    }

    return metrics.topResellers.map((reseller, index) => (
      <div className="py-2" key={index}>
        <div className="flex items-center justify-between">
          <div className="font-medium text-gray-900">{reseller.name}</div>
          <div className="font-mono text-gray-900">
            {formatCurrency(reseller.revenue, 'INR', 'en-IN')}
          </div>
        </div>
        <div className="mt-1">
          <Progress 
            value={reseller.percentage} 
            className="h-2" 
            indicatorClassName="bg-secondary-600" 
          />
        </div>
      </div>
    ));
  };

  return (
    <Card className="mt-8">
      <CardHeader className="border-b border-border">
        <CardTitle>Partner Programs</CardTitle>
        <CardDescription>Reseller and referral program metrics</CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {/* Resellers Card */}
          {renderMetricCard(
            <Briefcase className="h-6 w-6 text-white" />,
            "success-gradient",
            "Active Resellers",
            metrics.activeResellers
          )}
          
          {/* Reseller Revenue Card */}
          {renderMetricCard(
            <DollarSign className="h-6 w-6 text-white" />,
            "success-gradient",
            "Reseller Revenue",
            formatCurrency(metrics.resellerRevenue, 'INR', 'en-IN')
          )}
          
          {/* Active Referrals Card */}
          {renderMetricCard(
            <Users className="h-6 w-6 text-white" />,
            "accent-gradient",
            "Active Referrals",
            metrics.activeReferrals
          )}
          
          {/* Referral Commissions Card */}
          {renderMetricCard(
            <CreditCard className="h-6 w-6 text-white" />,
            "accent-gradient",
            "Referral Commissions",
            formatCurrency(metrics.referralCommissions, 'INR', 'en-IN')
          )}
        </div>
        
        {/* Top Resellers Graph */}
        <div className="mt-6">
          <h4 className="text-base font-medium text-gray-900">Top Resellers by Revenue</h4>
          <div className="mt-2">
            {renderTopResellers()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
