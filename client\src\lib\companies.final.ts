import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';
import { Company } from '@shared/schema';

// Types
export interface CompanyWithAccess {
  id: number;
  user_id: number;
  company_id: number;
  company: Company;
  is_primary: boolean;
  created_at: Date | string;
  updated_at?: Date | string;
  name?: string;
}

interface CompanyContextType {
  currentCompany: CompanyWithAccess | null;
  userCompanies: CompanyWithAccess[];
  isLoading: boolean;
  error: Error | null;
  switchCompany: (companyId: number, companyName: string, setAsPrimary?: boolean) => Promise<void>;
}

// Create context with default values
const defaultContext: CompanyContextType = {
  currentCompany: null,
  userCompanies: [],
  isLoading: false,
  error: null,
  switchCompany: async () => {}
};

// Create context
const CompanyContext = createContext<CompanyContextType>(defaultContext);

// Provider component
export const CompanyProvider = ({ children }: { children: ReactNode }) => {
  const auth = useAuth();
  const { toast } = useToast();
  const [currentCompany, setCurrentCompany] = useState<CompanyWithAccess | null>(null);
  const [userCompanies, setUserCompanies] = useState<CompanyWithAccess[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function loadCompanies() {
      try {
        setIsLoading(true);
        const user = auth.getCurrentUser();
        
        if (!user) {
          setIsLoading(false);
          return;
        }

        const companies = await auth.getUserCompanies(user.id);
        setUserCompanies(companies);

        // Find primary company or current selected company
        const primaryCompany = companies.find(c => c.is_primary);
        const currentUserCompany = companies.find(c => c.company_id === user.company_id);
        
        if (currentUserCompany) {
          setCurrentCompany(currentUserCompany);
        } else if (primaryCompany) {
          setCurrentCompany(primaryCompany);
        } else if (companies.length > 0) {
          setCurrentCompany(companies[0]);
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load companies'));
        toast({
          title: 'Error',
          description: 'Failed to load company information',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }

    loadCompanies();
  }, [auth, toast]);

  const handleSwitchCompany = async (companyId: number, companyName: string, setAsPrimary: boolean = false) => {
    try {
      await auth.switchCompany(companyId, companyName, setAsPrimary);
      
      // Update current company in state
      const newCurrentCompany = userCompanies.find(c => c.company_id === companyId);
      if (newCurrentCompany) {
        setCurrentCompany(newCurrentCompany);
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to switch company',
        variant: 'destructive',
      });
    }
  };

  // Return the JSX for provider
  return React.createElement(
    CompanyContext.Provider,
    { 
      value: {
        currentCompany,
        userCompanies,
        isLoading,
        error,
        switchCompany: handleSwitchCompany
      }
    },
    children
  );
};

// Hook
export function useCompany() {
  const context = useContext(CompanyContext);
  return context;
}