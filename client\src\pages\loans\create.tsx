import React, { useMemo } from "react";
import { useLocation } from "wouter";
import { useCompany } from "@/lib/companies";
import { useAuth } from "@/lib/auth";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { DirectLoanForm } from "@/components/loan/DirectLoanForm";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function CreateLoanPage() {
  const { currentCompany, isLoading: companyLoading } = useCompany();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const [, navigate] = useLocation();

  // Use company context first, fall back to user's authenticated company
  const companyId = currentCompany?.company_id || user?.company_id;

  // Parse URL parameters for customer pre-selection and return navigation
  const urlParams = useMemo(() => {
    const params = new URLSearchParams(window.location.search);
    return {
      customerId: params.get('customer_id') ? parseInt(params.get('customer_id')!, 10) : undefined,
      returnTo: params.get('return_to') || undefined,
      source: params.get('source') || undefined,
    };
  }, []);

  console.log('CreateLoanPage URL params:', urlParams);
  
  // Handle successful loan creation with smart navigation
  const handleLoanCreated = (loan: any) => {
    if (loan && loan.id) {
      console.log('Loan created successfully:', loan);
      console.log('Navigation params:', urlParams);

      // Determine where to navigate based on the source
      if (urlParams.returnTo) {
        // Use explicit return URL if provided
        navigate(urlParams.returnTo);
      } else if (urlParams.customerId && urlParams.source === 'customer') {
        // If we came from a customer page, return to that customer's detail page
        navigate(`/customers/${urlParams.customerId}`);
      } else if (urlParams.customerId) {
        // If we have a customer ID but no specific source, go to customer page
        navigate(`/customers/${urlParams.customerId}`);
      } else {
        // Default: navigate to the loans list page
        navigate('/loans');
      }
    }
  };
  
  // Show loading state while company context is loading
  if (companyLoading && !companyId) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate("/loans")}
              className="text-sm"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Loans
            </Button>
            <h1 className="text-2xl font-bold">
              Create New Loan
            </h1>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-6 w-48" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-64" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => navigate("/loans")}
            className="text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Loans
          </Button>
          <h1 className="text-2xl font-bold">
            Create New Loan
          </h1>
        </div>
      </div>

      {companyId ? (
        <DirectLoanForm
          companyId={companyId}
          onSuccess={handleLoanCreated}
          onCancel={() => {
            // Smart cancel navigation - return to where we came from
            if (urlParams.returnTo) {
              navigate(urlParams.returnTo);
            } else if (urlParams.customerId && urlParams.source === 'customer') {
              navigate(`/customers/${urlParams.customerId}`);
            } else {
              navigate("/loans");
            }
          }}
          initialData={urlParams.customerId ? { customer_id: urlParams.customerId } : undefined}
        />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Company Required</CardTitle>
            <CardDescription>
              You need to select a company to create loans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p>Please select a company from the navigation panel to continue.</p>
              <div className="text-sm text-muted-foreground">
                <p>If you don't see any companies available:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Make sure you have been assigned to at least one company</li>
                  <li>Try refreshing the page</li>
                  <li>Contact your administrator if the problem persists</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}