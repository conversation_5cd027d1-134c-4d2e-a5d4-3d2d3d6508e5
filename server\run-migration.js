import { pool } from './db.js';

async function runMigration() {
  try {
    console.log('Running migration: Creating company_settings table');

    // Create company_settings table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS company_settings (
        id SERIAL PRIMARY KEY,
        company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
        date_format TEXT NOT NULL DEFAULT 'dd-MM-yyyy',
        currency_symbol TEXT NOT NULL DEFAULT '₹',
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        UNIQUE(company_id)
      );
    `);

    // Create index for faster lookups
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_company_settings_company_id ON company_settings(company_id);
    `);

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

runMigration();
