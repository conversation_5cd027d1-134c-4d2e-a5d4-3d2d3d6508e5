# Route Conflicts and Caching Issues Troubleshooting Guide

## Overview

This document provides a comprehensive troubleshooting methodology for diagnosing and resolving route conflicts, caching issues, and data persistence problems in the FinancialTracker application. It's based on the resolution of critical branch functionality issues and provides reusable patterns for similar problems across all modules.

## Table of Contents

1. [Root Cause Analysis: Branch Functionality Case Study](#root-cause-analysis)
2. [Systematic Debugging Methodology](#systematic-debugging-methodology)
3. [Common Route Conflict Patterns](#common-route-conflict-patterns)
4. [Frontend Caching Issues](#frontend-caching-issues)
5. [CRUD Operations Testing Framework](#crud-operations-testing-framework)
6. [Prevention Guidelines](#prevention-guidelines)
7. [Quick Reference Commands](#quick-reference-commands)

## Root Cause Analysis

### Case Study: Branch Functionality Issue

**Problem**: Branch data not appearing after logout/login cycles, showing "No branches found" despite successful creation.

**Root Causes Identified**:

1. **Conflicting Route Registrations**
   - Multiple route handlers for the same endpoint: `/api/companies/:id/branches`
   - Company routes (registered first): Hardcoded `return res.json([])` 
   - Branch routes (registered second): Proper implementation but never reached
   - Express uses first matching route, masking correct implementation

2. **Missing API Endpoints**
   - PUT route for branch updates was missing (only PATCH implemented)
   - Caused 404 errors during update operations

3. **Compilation Errors**
   - Duplicate object keys in React components preventing proper builds
   - TypeScript compilation failures blocking route registration

4. **Frontend Caching Issues**
   - Zustand store persistence without proper cleanup on logout
   - React Query cache not being invalidated correctly
   - Stale data persisting across authentication sessions

## Systematic Debugging Methodology

### Phase 1: Initial Problem Assessment

```bash
# 1. Check server startup logs for route registration
npm start
# Look for: "Branch routes registered" or similar messages

# 2. Test API endpoints directly
curl -X GET "http://localhost:8080/api/companies/13/branches"
# Or using PowerShell:
Invoke-WebRequest -Uri "http://localhost:8080/api/companies/13/branches" -Method GET

# 3. Check compilation status
npm run build
# Look for TypeScript errors or warnings
```

### Phase 2: Route Conflict Detection

```bash
# 1. Search for duplicate route definitions
grep -r "api/companies.*branches" server/routes/
# Or in PowerShell:
Select-String -Path "server/routes/*.ts" -Pattern "api/companies.*branches"

# 2. Check route registration order in server/routes/index.ts
# Earlier registrations take precedence

# 3. Test route precedence
# Create test endpoints with different responses to identify which is being called
```

### Phase 3: Database and Backend Verification

```javascript
// Create test script to verify database operations
const testDatabaseOperations = async () => {
  // 1. Test direct database queries
  const branches = await db.select().from(branches).where(eq(branches.company_id, 13));
  console.log('Direct DB query result:', branches);
  
  // 2. Test individual API endpoints
  const response = await fetch('/api/branches/1');
  console.log('Individual fetch result:', await response.json());
  
  // 3. Test authentication and permissions
  const userCompany = await getUserCompanyRole(userId, companyId);
  console.log('User permissions:', userCompany);
};
```

### Phase 4: Frontend Cache Investigation

```javascript
// 1. Check Zustand store state
import { useBranchStore } from '@/lib/branches';
const { branches, currentBranchId } = useBranchStore.getState();
console.log('Zustand state:', { branches, currentBranchId });

// 2. Check React Query cache
import { useQueryClient } from '@tanstack/react-query';
const queryClient = useQueryClient();
const cachedData = queryClient.getQueryData(['branches']);
console.log('React Query cache:', cachedData);

// 3. Check localStorage persistence
console.log('LocalStorage auth:', localStorage.getItem('auth_token'));
console.log('LocalStorage user:', localStorage.getItem('user_data'));
```

## Common Route Conflict Patterns

### Pattern 1: Overlapping Route Definitions

```typescript
// ❌ PROBLEMATIC: Multiple handlers for same route
// In company.routes.ts
app.get('/api/companies/:id/branches', handler1);

// In branch.routes.ts  
app.get('/api/companies/:companyId/branches', handler2); // Never reached!
```

```typescript
// ✅ SOLUTION: Single source of truth
// Remove duplicate from company.routes.ts, keep only in branch.routes.ts
// Or use route delegation pattern
```

### Pattern 2: Parameter Name Conflicts

```typescript
// ❌ PROBLEMATIC: Different parameter names for same route
app.get('/api/companies/:id/branches', ...);      // Uses :id
app.get('/api/companies/:companyId/branches', ...); // Uses :companyId
```

```typescript
// ✅ SOLUTION: Consistent parameter naming
app.get('/api/companies/:companyId/branches', ...);
```

### Pattern 3: Method Conflicts

```typescript
// ❌ PROBLEMATIC: Missing HTTP methods
app.post('/api/branches', createBranch);
app.patch('/api/branches/:id', updateBranch);
// Missing PUT method causes 404 errors

// ✅ SOLUTION: Complete CRUD coverage
app.post('/api/branches', createBranch);
app.get('/api/branches/:id', getBranch);
app.put('/api/branches/:id', updateBranch);    // Add PUT
app.patch('/api/branches/:id', updateBranch);  // Keep PATCH
app.delete('/api/branches/:id', deleteBranch);
```

## Frontend Caching Issues

### Zustand Store Problems

```typescript
// ❌ PROBLEMATIC: No cleanup on logout
const useBranchStore = create(
  persist(
    (set, get) => ({
      branches: [],
      currentBranchId: null,
      // Missing cleanup function
    }),
    { name: 'branch-store' }
  )
);
```

```typescript
// ✅ SOLUTION: Add cleanup function
const useBranchStore = create(
  persist(
    (set, get) => ({
      branches: [],
      currentBranchId: null,
      
      clearBranchData: () => {
        set({ 
          currentBranchId: null, 
          branches: [], 
          isLoading: false, 
          error: null 
        });
        // Clear React Query cache
        queryClient.removeQueries({ queryKey: ['branches'] });
      }
    }),
    { name: 'branch-store' }
  )
);
```

### React Query Cache Issues

```typescript
// ❌ PROBLEMATIC: Incomplete cache invalidation
const logout = () => {
  localStorage.removeItem("auth_token");
  queryClient.clear(); // May not clear everything
};
```

```typescript
// ✅ SOLUTION: Comprehensive cache cleanup
const logout = () => {
  localStorage.removeItem("auth_token");
  localStorage.removeItem("user_data");
  
  // Clear all query cache
  queryClient.clear();
  
  // Clear Zustand stores
  import('./branches').then(({ useBranchStore }) => {
    const { clearBranchData } = useBranchStore.getState();
    clearBranchData();
  }).catch(console.error);
};
```

### Component State Synchronization

```typescript
// ❌ PROBLEMATIC: Missing dependencies in useEffect
useEffect(() => {
  if (companyId) {
    fetchBranches(companyId);
  }
}, [companyId, fetchBranches]); // Missing user dependency
```

```typescript
// ✅ SOLUTION: Complete dependency array
useEffect(() => {
  if (companyId && user) {
    fetchBranches(companyId);
  }
}, [companyId, fetchBranches, user?.id]); // Include user.id
```

## CRUD Operations Testing Framework

### Comprehensive Test Script Template

```javascript
// test-crud-functionality.js
import fetch from 'node-fetch';

async function testCRUDOperations(moduleName, baseEndpoint, testData) {
  console.log(`🧪 TESTING ${moduleName.toUpperCase()} CRUD OPERATIONS`);
  
  // 1. Authentication
  const { cookies } = await login();
  
  // 2. CREATE - Test creation
  const createResponse = await fetch(`${baseUrl}${baseEndpoint}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'Cookie': cookies },
    body: JSON.stringify(testData)
  });
  const created = await createResponse.json();
  console.log(`✅ CREATE: ${created.name || created.id}`);
  
  // 3. READ - Test listing
  const listResponse = await fetch(`${baseUrl}${baseEndpoint}`, {
    headers: { 'Cookie': cookies }
  });
  const list = await listResponse.json();
  console.log(`✅ READ: Found ${list.length} items`);
  
  // 4. UPDATE - Test update
  const updateResponse = await fetch(`${baseUrl}${baseEndpoint}/${created.id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', 'Cookie': cookies },
    body: JSON.stringify({ ...testData, name: `${testData.name} - Updated` })
  });
  console.log(`✅ UPDATE: ${updateResponse.ok ? 'Success' : 'Failed'}`);
  
  // 5. DELETE - Test deletion
  const deleteResponse = await fetch(`${baseUrl}${baseEndpoint}/${created.id}`, {
    method: 'DELETE',
    headers: { 'Cookie': cookies }
  });
  console.log(`✅ DELETE: ${deleteResponse.ok ? 'Success' : 'Failed'}`);
  
  // 6. PERSISTENCE - Test logout/login cycle
  await testLogoutLoginPersistence(baseEndpoint, cookies);
}
```

### Module-Specific Test Data

```javascript
// Test data templates for different modules
const testDataTemplates = {
  branches: {
    name: `Test Branch ${Date.now()}`,
    company_id: 13,
    address: '123 Test Street',
    phone: '+************',
    email: '<EMAIL>',
    manager_name: 'Test Manager'
  },
  
  customers: {
    name: `Test Customer ${Date.now()}`,
    company_id: 13,
    mobile_number: '+************',
    email: '<EMAIL>',
    address: '456 Customer Street'
  },
  
  loans: {
    customer_id: 1,
    company_id: 13,
    amount: 10000,
    interest_rate: 12.5,
    term_months: 12,
    loan_type: 'personal'
  }
};
```

## Prevention Guidelines

### 1. Route Organization Best Practices

```typescript
// ✅ Single responsibility: One route file per resource
// branch.routes.ts - handles ALL branch-related routes
// customer.routes.ts - handles ALL customer-related routes
// company.routes.ts - handles ONLY company-specific routes (not sub-resources)

// ✅ Consistent naming patterns
app.get('/api/branches', getAllBranches);
app.get('/api/branches/:id', getBranchById);
app.post('/api/branches', createBranch);
app.put('/api/branches/:id', updateBranch);
app.patch('/api/branches/:id', updateBranch);
app.delete('/api/branches/:id', deleteBranch);

// ✅ Company-scoped routes in resource files
app.get('/api/companies/:companyId/branches', getBranchesByCompany);
```

### 2. Cache Management Patterns

```typescript
// ✅ Implement cleanup functions in all stores
interface StoreState {
  data: any[];
  isLoading: boolean;
  error: string | null;
  clearData: () => void; // Always include cleanup
}

// ✅ Centralized logout handling
const useAuth = () => {
  const logout = () => {
    // Clear all authentication data
    localStorage.clear();
    
    // Clear all query caches
    queryClient.clear();
    
    // Clear all Zustand stores
    clearAllStores();
  };
};
```

### 3. Development Workflow

```bash
# ✅ Always check for conflicts before adding routes
grep -r "api/your-endpoint" server/routes/

# ✅ Test compilation after changes
npm run build

# ✅ Verify route registration in logs
npm start | grep "routes registered"

# ✅ Test CRUD operations end-to-end
node test-crud-functionality.js
```

## Quick Reference Commands

### Debugging Commands

```bash
# Check route conflicts
grep -r "api/companies.*branches" server/routes/
Select-String -Path "server/routes/*.ts" -Pattern "api/companies.*branches"

# Test API endpoints
curl -X GET "http://localhost:8080/api/test"
Invoke-WebRequest -Uri "http://localhost:8080/api/test" -Method GET

# Check compilation
npm run build 2>&1 | grep -i error
npm run build

# Monitor server logs
npm start | grep -E "(routes registered|error|404)"
```

### Browser Console Debugging

```javascript
// Check store states
console.log('Branch store:', useBranchStore.getState());
console.log('Auth store:', useAuthStore.getState());

// Check React Query cache
console.log('Query cache:', queryClient.getQueryCache().getAll());

// Check localStorage
console.log('Auth token:', localStorage.getItem('auth_token'));
console.log('User data:', localStorage.getItem('user_data'));

// Force cache clear
queryClient.clear();
localStorage.clear();
```

### Testing Patterns

```javascript
// Quick CRUD test
const testQuickCRUD = async (endpoint, data) => {
  const { cookies } = await login();
  
  // Create
  const created = await fetch(endpoint, { 
    method: 'POST', 
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json', 'Cookie': cookies }
  }).then(r => r.json());
  
  // Read
  const list = await fetch(endpoint, { 
    headers: { 'Cookie': cookies }
  }).then(r => r.json());
  
  console.log(`Created: ${created.id}, List count: ${list.length}`);
  
  // Cleanup
  await fetch(`${endpoint}/${created.id}`, { 
    method: 'DELETE', 
    headers: { 'Cookie': cookies }
  });
};
```

## Related Documentation

- [API 404 Fixes Summary](../api-404-fixes-summary.md)
- [Route Audit and Prefix Settings Summary](../route-audit-and-prefix-settings-summary.md)
- [Console Logging Cleanup Summary](../console-logging-cleanup-summary.md)
- [Development Best Practices](../development-best-practices.md)

## Case Study: Financial Account 404 Errors (December 2024)

### **Problem Description**
- **URLs Failing**: `/financial/accounts/457`, `/financial/accounts/460#transactions`
- **API Errors**: `GET http://localhost:8080/api/companies/13/accounts/458` returning 404 with 'API_ENDPOINT_NOT_FOUND'
- **Impact**: Core financial account functionality completely broken

### **Root Cause Analysis**
1. **Missing Company-Scoped Routes**: Frontend expected `/api/companies/:companyId/accounts/:accountId` but backend only had `/api/accounts/:id`
2. **Incomplete Route Migration**: During modularization from `routes.ts.old` to separate route files, company-scoped endpoints were not migrated
3. **Storage Method Signature Mismatch**: Some routes called `storage.getAccount(accountId)` but interface required `storage.getAccount(accountId, companyId)`

### **Solution Implementation**
1. **Added Missing Company-Scoped Routes**:
   - `GET /api/companies/:companyId/accounts/:accountId` - Get account details
   - `PUT /api/companies/:companyId/accounts/:accountId` - Update account
   - `DELETE /api/companies/:companyId/accounts/:accountId` - Delete account
   - `GET /api/companies/:companyId/accounts/:accountId/transactions` - Get account transactions

2. **Fixed Storage Method Calls**: Updated all `storage.getAccount()` calls to include `companyId` parameter

3. **Maintained Backward Compatibility**: Kept legacy `/api/accounts/:id` routes for existing integrations

4. **Currency Formatting Fix**: Updated all financial displays from USD ($) to INR (₹) symbols for Indian market consistency

### **Files Modified**
- `server/routes/financial/account.routes.ts` - Added company-scoped routes
- `client/src/pages/financial/accounts/[id].tsx` - Fixed currency formatting and icons
- `client/src/pages/financial/reports/components/CashFlowReport.tsx` - Updated currency display
- `client/src/pages/financial/reports/components/BalanceSheetReport.tsx` - Updated currency display

### **Verification Results**
- ✅ All account endpoints now return 200 OK instead of 404
- ✅ Account IDs 456, 457, 458, 460 all accessible
- ✅ Transactions endpoint working correctly
- ✅ Currency symbols consistently showing ₹ instead of $
- ✅ No breaking changes to existing functionality

### **Prevention Guidelines**
1. **Route Migration Checklist**: When modularizing routes, create comprehensive mapping of old vs new endpoints
2. **API Contract Testing**: Implement automated tests for all frontend-expected API endpoints
3. **Currency Consistency**: Use centralized currency formatting utilities to maintain consistency
4. **Company-Scoped Design**: Always design financial endpoints with company scope for multi-tenant architecture

---

**Last Updated**: January 2025
**Applies To**: FinancialTracker v1.0+
**Severity**: Critical - Data visibility and functionality issues
