import { db } from '../db';
import {
  users, userRoles, customRoles, sensitiveFieldDefinitions, fieldSecurityRules,
  groupUsers, groupRoles,
  type User, type FieldSecurityRule, type SensitiveFieldDefinition,
  type FieldAccessType, type FieldSensitivityLevel
} from '@shared/schema';
import { eq, and, inArray, or, isNull } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';
import { conditionalPermissionService, PermissionEvaluationContext } from './conditionalPermissionService';

export interface FieldSecurityContext {
  userId: number;
  companyId: number;
  tableName: string;
  operation: 'read' | 'write' | 'create' | 'update';
  entityId?: number;
  timestamp?: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface FieldAccessResult {
  allowed: boolean;
  accessType: FieldAccessType;
  maskingPattern?: string;
  reason?: string;
}

export interface FieldFilterResult {
  filteredData: any;
  maskedFields: string[];
  removedFields: string[];
  accessDeniedFields: string[];
}

export interface UserFieldPermissions {
  userId: number;
  roleIds: number[];
  fieldRules: Map<string, FieldSecurityRule>;
  sensitiveFields: Map<string, SensitiveFieldDefinition>;
}

export class FieldSecurityService {

  /**
   * Filter object fields based on user permissions and apply masking
   * @param context Field security context
   * @param data Object data to filter
   * @returns Promise<FieldFilterResult> Filtered data with metadata
   */
  async filterFieldsByPermissions(
    context: FieldSecurityContext,
    data: any
  ): Promise<FieldFilterResult> {
    try {
      if (!data || typeof data !== 'object') {
        return {
          filteredData: data,
          maskedFields: [],
          removedFields: [],
          accessDeniedFields: []
        };
      }

      // Get user's field permissions for this table
      const userPermissions = await this.getUserFieldPermissions(
        context.userId,
        context.companyId,
        context.tableName
      );

      const filteredData = { ...data };
      const maskedFields: string[] = [];
      const removedFields: string[] = [];
      const accessDeniedFields: string[] = [];

      // Process each field in the data
      for (const [fieldName, fieldValue] of Object.entries(data)) {
        const fieldAccess = await this.checkFieldAccess(
          context,
          fieldName,
          userPermissions
        );

        switch (fieldAccess.accessType) {
          case 'none':
            delete filteredData[fieldName];
            removedFields.push(fieldName);
            accessDeniedFields.push(fieldName);
            break;

          case 'masked':
            const maskedValue = this.maskFieldValue(
              fieldValue,
              fieldAccess.maskingPattern || '***'
            );
            filteredData[fieldName] = maskedValue;
            maskedFields.push(fieldName);
            break;

          case 'read':
          case 'write':
            // Field is accessible as-is
            break;

          default:
            // Unknown access type, deny access for security
            delete filteredData[fieldName];
            removedFields.push(fieldName);
            accessDeniedFields.push(fieldName);
        }
      }

      return {
        filteredData,
        maskedFields,
        removedFields,
        accessDeniedFields
      };
    } catch (error) {
      errorLogger.logError(
        `Error filtering fields for user ${context.userId} on table ${context.tableName}`,
        error,
        'field-security-service'
      );

      // On error, return original data but log the issue
      return {
        filteredData: data,
        maskedFields: [],
        removedFields: [],
        accessDeniedFields: []
      };
    }
  }

  /**
   * Check if user has access to a specific field
   * @param context Field security context
   * @param fieldName Name of the field to check
   * @param userPermissions Pre-loaded user permissions (optional for performance)
   * @returns Promise<FieldAccessResult> Field access result
   */
  async checkFieldAccess(
    context: FieldSecurityContext,
    fieldName: string,
    userPermissions?: UserFieldPermissions
  ): Promise<FieldAccessResult> {
    try {
      // Load user permissions if not provided
      const permissions = userPermissions || await this.getUserFieldPermissions(
        context.userId,
        context.companyId,
        context.tableName
      );

      // Check if field is defined as sensitive
      const sensitiveField = permissions.sensitiveFields.get(fieldName);
      if (!sensitiveField) {
        // Field is not sensitive, allow full access
        return {
          allowed: true,
          accessType: 'read'
        };
      }

      // Check if user has specific rule for this field
      const fieldRule = permissions.fieldRules.get(fieldName);
      if (fieldRule) {
        // Evaluate conditional access if configured
        if (fieldRule.condition_config) {
          const conditionResult = await this.evaluateFieldConditions(
            context,
            fieldRule.condition_config
          );

          if (!conditionResult.allowed) {
            return {
              allowed: false,
              accessType: 'none',
              reason: conditionResult.reason
            };
          }
        }

        // Return rule-based access
        return {
          allowed: fieldRule.access_type !== 'none',
          accessType: fieldRule.access_type,
          maskingPattern: fieldRule.override_masking_pattern || sensitiveField.masking_pattern || undefined
        };
      }

      // No specific rule, use default access from sensitive field definition
      return {
        allowed: sensitiveField.default_access_type !== 'none',
        accessType: sensitiveField.default_access_type,
        maskingPattern: sensitiveField.masking_pattern || undefined
      };
    } catch (error) {
      errorLogger.logError(
        `Error checking field access for ${fieldName} for user ${context.userId}`,
        error,
        'field-security-service'
      );

      // On error, deny access for security
      return {
        allowed: false,
        accessType: 'none',
        reason: 'Error evaluating field access'
      };
    }
  }

  /**
   * Apply masking pattern to field value
   * @param value Original field value
   * @param pattern Masking pattern (e.g., '***-**-####')
   * @returns string Masked value
   */
  maskFieldValue(value: any, pattern: string): string {
    if (value === null || value === undefined) {
      return value;
    }

    const stringValue = String(value);

    if (!pattern || pattern === '***') {
      // Simple masking - replace all characters with asterisks
      return '*'.repeat(Math.min(stringValue.length, 10));
    }

    // Email masking - special case for email patterns
    if (stringValue.includes('@') && pattern.includes('@')) {
      const [localPart, domain] = stringValue.split('@');
      if (localPart.length <= 2) {
        return `*@${domain}`;
      }
      const maskedLocal = localPart[0] + '*'.repeat(localPart.length - 2) + localPart[localPart.length - 1];
      return `${maskedLocal}@${domain}`;
    }

    // Phone number masking - special case for phone patterns
    if (/^\+?[\d\s\-\(\)]+$/.test(stringValue) && pattern.includes('-')) {
      const digits = stringValue.replace(/\D/g, '');
      if (digits.length >= 10) {
        return `***-***-${digits.slice(-4)}`;
      }
    }

    // Pattern-based masking with # placeholders
    if (pattern.includes('#')) {
      // Pattern with # placeholders (e.g., '***-**-####')
      let maskedValue = '';
      let valueIndex = 0;

      for (let i = 0; i < pattern.length && valueIndex < stringValue.length; i++) {
        if (pattern[i] === '#') {
          maskedValue += stringValue[valueIndex] || '*';
          valueIndex++;
        } else if (pattern[i] === '*') {
          maskedValue += '*';
          valueIndex++;
        } else {
          maskedValue += pattern[i];
        }
      }

      return maskedValue;
    }

    // Default masking for unknown patterns
    return '*'.repeat(Math.min(stringValue.length, 10));
  }

  /**
   * Get user's field permissions for a specific table
   * @param userId User ID
   * @param companyId Company ID
   * @param tableName Table name to get permissions for
   * @returns Promise<UserFieldPermissions> User's field permissions
   */
  async getUserFieldPermissions(
    userId: number,
    companyId: number,
    tableName: string
  ): Promise<UserFieldPermissions> {
    try {
      // Get user's roles (direct and through groups)
      const roleIds = await this.getUserRoleIds(userId, companyId);

      // Get sensitive field definitions for this table
      const sensitiveFields = await db
        .select()
        .from(sensitiveFieldDefinitions)
        .where(
          and(
            eq(sensitiveFieldDefinitions.table_name, tableName),
            eq(sensitiveFieldDefinitions.is_active, true)
          )
        );

      // Get field security rules for user's roles and this table's fields
      const sensitiveFieldIds = sensitiveFields.map(f => f.id);
      let fieldRules: FieldSecurityRule[] = [];

      if (roleIds.length > 0 && sensitiveFieldIds.length > 0) {
        fieldRules = await db
          .select()
          .from(fieldSecurityRules)
          .where(
            and(
              inArray(fieldSecurityRules.role_id, roleIds),
              inArray(fieldSecurityRules.sensitive_field_id, sensitiveFieldIds),
              eq(fieldSecurityRules.is_active, true)
            )
          );
      }

      // Create maps for efficient lookup
      const sensitiveFieldsMap = new Map<string, SensitiveFieldDefinition>();
      sensitiveFields.forEach(field => {
        sensitiveFieldsMap.set(field.field_name, field);
      });

      const fieldRulesMap = new Map<string, FieldSecurityRule>();
      fieldRules.forEach(rule => {
        const sensitiveField = sensitiveFields.find(f => f.id === rule.sensitive_field_id);
        if (sensitiveField) {
          // If multiple rules exist for same field, use highest priority
          const existingRule = fieldRulesMap.get(sensitiveField.field_name);
          if (!existingRule || rule.priority > existingRule.priority) {
            fieldRulesMap.set(sensitiveField.field_name, rule);
          }
        }
      });

      return {
        userId,
        roleIds,
        fieldRules: fieldRulesMap,
        sensitiveFields: sensitiveFieldsMap
      };
    } catch (error) {
      errorLogger.logError(
        `Error getting field permissions for user ${userId} on table ${tableName}`,
        error,
        'field-security-service'
      );

      // Return empty permissions on error
      return {
        userId,
        roleIds: [],
        fieldRules: new Map(),
        sensitiveFields: new Map()
      };
    }
  }

  /**
   * Get user's role IDs (direct roles and roles from groups)
   * @param userId User ID
   * @param companyId Company ID
   * @returns Promise<number[]> Array of role IDs
   */
  async getUserRoleIds(userId: number, companyId: number): Promise<number[]> {
    try {
      const roleIds = new Set<number>();

      // Get direct user roles
      const directRoles = await db
        .select({ role_id: userRoles.role_id })
        .from(userRoles)
        .innerJoin(customRoles, eq(userRoles.role_id, customRoles.id))
        .where(
          and(
            eq(userRoles.user_id, userId),
            or(
              eq(customRoles.company_id, companyId),
              isNull(customRoles.company_id) // System roles
            )
          )
        );

      directRoles.forEach(role => roleIds.add(role.role_id));

      // Get roles from groups
      const groupRoles = await db
        .select({ role_id: groupRoles.role_id })
        .from(groupUsers)
        .innerJoin(groupRoles, eq(groupUsers.group_id, groupRoles.group_id))
        .innerJoin(customRoles, eq(groupRoles.role_id, customRoles.id))
        .where(
          and(
            eq(groupUsers.user_id, userId),
            or(
              eq(customRoles.company_id, companyId),
              isNull(customRoles.company_id) // System roles
            )
          )
        );

      groupRoles.forEach(role => roleIds.add(role.role_id));

      return Array.from(roleIds);
    } catch (error) {
      errorLogger.logError(
        `Error getting role IDs for user ${userId}`,
        error,
        'field-security-service'
      );
      return [];
    }
  }

  /**
   * Evaluate conditional field access
   * @param context Field security context
   * @param conditionConfig JSON condition configuration
   * @returns Promise<{allowed: boolean, reason?: string}> Condition evaluation result
   */
  async evaluateFieldConditions(
    context: FieldSecurityContext,
    conditionConfig: any
  ): Promise<{allowed: boolean, reason?: string}> {
    try {
      if (!conditionConfig || typeof conditionConfig !== 'object') {
        return { allowed: true };
      }

      // Build permission evaluation context for conditional permission service
      const permissionContext: PermissionEvaluationContext = {
        userId: context.userId,
        permissionCode: 'field_access', // Generic permission for field access
        timestamp: context.timestamp || new Date(),
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        amount: conditionConfig.amount,
        location: conditionConfig.location,
        deviceInfo: conditionConfig.deviceInfo,
        sessionInfo: conditionConfig.sessionInfo
      };

      // Use conditional permission service to evaluate conditions
      const result = await conditionalPermissionService.evaluatePermissionConditions(permissionContext);

      return {
        allowed: result.passed,
        reason: result.reason
      };
    } catch (error) {
      errorLogger.logError(
        `Error evaluating field conditions for user ${context.userId}`,
        error,
        'field-security-service'
      );

      // On error, deny access for security
      return {
        allowed: false,
        reason: 'Error evaluating field conditions'
      };
    }
  }

  /**
   * Filter array of objects with field-level security
   * @param context Field security context
   * @param dataArray Array of objects to filter
   * @returns Promise<any[]> Filtered array with field-level security applied
   */
  async filterArrayFieldsByPermissions(
    context: FieldSecurityContext,
    dataArray: any[]
  ): Promise<any[]> {
    if (!Array.isArray(dataArray)) {
      return dataArray;
    }

    // Get user permissions once for efficiency
    const userPermissions = await this.getUserFieldPermissions(
      context.userId,
      context.companyId,
      context.tableName
    );

    // Filter each object in the array
    const filteredArray = await Promise.all(
      dataArray.map(async (item) => {
        const result = await this.filterFieldsByPermissions(
          { ...context, entityId: item.id },
          item
        );
        return result.filteredData;
      })
    );

    return filteredArray;
  }

  /**
   * Check if user can write to a specific field
   * @param context Field security context
   * @param fieldName Field name to check
   * @returns Promise<boolean> True if user can write to field
   */
  async canWriteField(
    context: FieldSecurityContext,
    fieldName: string
  ): Promise<boolean> {
    const fieldAccess = await this.checkFieldAccess(context, fieldName);
    return fieldAccess.allowed && fieldAccess.accessType === 'write';
  }

  /**
   * Get list of fields user can access for a table
   * @param context Field security context
   * @returns Promise<{readable: string[], writable: string[], masked: string[]}> Field access lists
   */
  async getAccessibleFields(
    context: FieldSecurityContext
  ): Promise<{readable: string[], writable: string[], masked: string[]}> {
    try {
      const userPermissions = await this.getUserFieldPermissions(
        context.userId,
        context.companyId,
        context.tableName
      );

      const readable: string[] = [];
      const writable: string[] = [];
      const masked: string[] = [];

      // Check access for each sensitive field
      for (const [fieldName, sensitiveField] of userPermissions.sensitiveFields) {
        const fieldAccess = await this.checkFieldAccess(
          context,
          fieldName,
          userPermissions
        );

        if (fieldAccess.allowed) {
          switch (fieldAccess.accessType) {
            case 'read':
              readable.push(fieldName);
              break;
            case 'write':
              readable.push(fieldName);
              writable.push(fieldName);
              break;
            case 'masked':
              readable.push(fieldName);
              masked.push(fieldName);
              break;
          }
        }
      }

      return { readable, writable, masked };
    } catch (error) {
      errorLogger.logError(
        `Error getting accessible fields for user ${context.userId} on table ${context.tableName}`,
        error,
        'field-security-service'
      );

      return { readable: [], writable: [], masked: [] };
    }
  }
}

// Export singleton instance
export const fieldSecurityService = new FieldSecurityService();
