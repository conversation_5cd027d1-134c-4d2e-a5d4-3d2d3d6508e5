import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockFetchSuccess, createMockPermission, createMockPermissionCondition } from '../../../../tests/utils';
import { ConditionalPermissionEditor } from '../ConditionalPermissionEditor';

describe('ConditionalPermissionEditor', () => {
  const mockOnSave = vi.fn();
  const mockOnCancel = vi.fn();

  const mockPermission = createMockPermission({
    id: 1,
    code: 'loan_approve',
    name: 'Approve Loans',
    description: 'Permission to approve loan applications',
    category: 'loans',
  });

  const mockConditions = [
    createMockPermissionCondition({
      id: 1,
      permission_id: 1,
      condition_type: 'time',
      condition_config: {
        start_time: '09:00',
        end_time: '17:00',
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        timezone: 'UTC',
      },
      is_active: true,
      priority: 1,
      description: 'Business hours only',
    }),
    createMockPermissionCondition({
      id: 2,
      permission_id: 1,
      condition_type: 'amount',
      condition_config: {
        min_amount: 0,
        max_amount: 50000,
        currency: 'USD',
      },
      is_active: true,
      priority: 2,
      description: 'Amount limit for approval',
    }),
  ];

  const defaultProps = {
    permission: mockPermission,
    onSave: mockOnSave,
    onCancel: mockOnCancel,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetchSuccess(mockConditions);
  });

  describe('Initial Rendering', () => {
    it('should render permission information', () => {
      render(<ConditionalPermissionEditor {...defaultProps} />);

      expect(screen.getByText('Approve Loans')).toBeInTheDocument();
      expect(screen.getByText('Permission to approve loan applications')).toBeInTheDocument();
    });

    it('should load existing conditions on mount', async () => {
      render(<ConditionalPermissionEditor {...defaultProps} />);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/permissions/loan_approve/conditions');
      });
    });

    it('should show loading state when loading prop is true', () => {
      render(<ConditionalPermissionEditor {...defaultProps} loading={true} />);

      const saveButton = screen.getByRole('button', { name: /save/i });
      expect(saveButton).toBeDisabled();
    });
  });

  describe('Tab Navigation', () => {
    it('should switch between conditions and preview tabs', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      // Check initial tab (Conditions)
      expect(screen.getByRole('tab', { name: /conditions/i })).toHaveAttribute('aria-selected', 'true');

      // Switch to Preview tab
      const previewTab = screen.getByRole('tab', { name: /preview/i });
      await user.click(previewTab);

      expect(previewTab).toHaveAttribute('aria-selected', 'true');
    });
  });

  describe('Condition Management', () => {
    it('should display existing conditions', async () => {
      render(<ConditionalPermissionEditor {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Business hours only')).toBeInTheDocument();
        expect(screen.getByText('Amount limit for approval')).toBeInTheDocument();
      });
    });

    it('should allow adding new conditions', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Should show condition type selector
      expect(screen.getByText(/select condition type/i)).toBeInTheDocument();
    });

    it('should allow removing conditions', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Business hours only')).toBeInTheDocument();
      });

      const removeButtons = screen.getAllByRole('button', { name: /remove/i });
      await user.click(removeButtons[0]);

      // Condition should be marked for removal or removed from UI
      expect(screen.queryByText('Business hours only')).not.toBeInTheDocument();
    });
  });

  describe('Condition Types', () => {
    it('should render time condition configuration', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Select time condition
      const timeOption = screen.getByText(/time/i);
      await user.click(timeOption);

      // Should show time configuration fields
      expect(screen.getByLabelText(/start time/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/end time/i)).toBeInTheDocument();
    });

    it('should render location condition configuration', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Select location condition
      const locationOption = screen.getByText(/location/i);
      await user.click(locationOption);

      // Should show location configuration fields
      expect(screen.getByLabelText(/allowed ip ranges/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/allowed countries/i)).toBeInTheDocument();
    });

    it('should render amount condition configuration', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Select amount condition
      const amountOption = screen.getByText(/amount/i);
      await user.click(amountOption);

      // Should show amount configuration fields
      expect(screen.getByLabelText(/min amount/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/max amount/i)).toBeInTheDocument();
    });

    it('should render approval condition configuration', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Select approval condition
      const approvalOption = screen.getByText(/approval/i);
      await user.click(approvalOption);

      // Should show approval configuration fields
      expect(screen.getByLabelText(/requires approval/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/approval threshold/i)).toBeInTheDocument();
    });

    it('should render device condition configuration', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Select device condition
      const deviceOption = screen.getByText(/device/i);
      await user.click(deviceOption);

      // Should show device configuration fields
      expect(screen.getByLabelText(/allowed device types/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/max devices per user/i)).toBeInTheDocument();
    });

    it('should render session condition configuration', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Select session condition
      const sessionOption = screen.getByText(/session/i);
      await user.click(sessionOption);

      // Should show session configuration fields
      expect(screen.getByLabelText(/max session age/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/require mfa/i)).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should validate required fields', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      // Select time condition
      const timeOption = screen.getByText(/time/i);
      await user.click(timeOption);

      // Try to save without filling required fields
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Should show validation errors
      expect(screen.getByText(/description is required/i)).toBeInTheDocument();
    });

    it('should validate time range', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      const timeOption = screen.getByText(/time/i);
      await user.click(timeOption);

      // Set invalid time range (end before start)
      const startTime = screen.getByLabelText(/start time/i);
      const endTime = screen.getByLabelText(/end time/i);

      await user.clear(startTime);
      await user.type(startTime, '17:00');
      await user.clear(endTime);
      await user.type(endTime, '09:00');

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Should show validation error
      expect(screen.getByText(/end time must be after start time/i)).toBeInTheDocument();
    });

    it('should validate amount range', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      const amountOption = screen.getByText(/amount/i);
      await user.click(amountOption);

      // Set invalid amount range (min > max)
      const minAmount = screen.getByLabelText(/min amount/i);
      const maxAmount = screen.getByLabelText(/max amount/i);

      await user.clear(minAmount);
      await user.type(minAmount, '100000');
      await user.clear(maxAmount);
      await user.type(maxAmount, '50000');

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Should show validation error
      expect(screen.getByText(/max amount must be greater than min amount/i)).toBeInTheDocument();
    });
  });

  describe('Save and Cancel', () => {
    it('should call onSave when save button is clicked', async () => {
      const user = userEvent.setup();

      // Mock successful save
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockConditions),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true }),
        });

      render(<ConditionalPermissionEditor {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Business hours only')).toBeInTheDocument();
      });

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalled();
      });
    });

    it('should call onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('should handle save errors gracefully', async () => {
      const user = userEvent.setup();

      // Mock failed save
      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockConditions),
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          json: () => Promise.resolve({ error: 'Save failed' }),
        });

      render(<ConditionalPermissionEditor {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Business hours only')).toBeInTheDocument();
      });

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText(/error saving conditions/i)).toBeInTheDocument();
      });
    });
  });

  describe('Preview Tab', () => {
    it('should display condition preview', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      await waitFor(() => {
        expect(screen.getByText('Business hours only')).toBeInTheDocument();
      });

      // Switch to preview tab
      const previewTab = screen.getByRole('tab', { name: /preview/i });
      await user.click(previewTab);

      // Should show condition configurations
      expect(screen.getByText(/priority: 1/i)).toBeInTheDocument();
      expect(screen.getByText(/priority: 2/i)).toBeInTheDocument();
    });

    it('should show no conditions message when no conditions exist', async () => {
      const user = userEvent.setup();
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve([]),
      });

      render(<ConditionalPermissionEditor {...defaultProps} />);

      const previewTab = screen.getByRole('tab', { name: /preview/i });
      await user.click(previewTab);

      await waitFor(() => {
        expect(screen.getByText(/no conditions configured/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      const addButton = screen.getByRole('button', { name: /add condition/i });
      await user.click(addButton);

      const timeOption = screen.getByText(/time/i);
      await user.click(timeOption);

      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/priority/i)).toBeInTheDocument();
    });

    it('should have accessible tab navigation', () => {
      render(<ConditionalPermissionEditor {...defaultProps} />);

      expect(screen.getByRole('tablist')).toBeInTheDocument();

      const tabs = screen.getAllByRole('tab');
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected');
      });
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<ConditionalPermissionEditor {...defaultProps} />);

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByRole('tab', { name: /conditions/i })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('tab', { name: /preview/i })).toHaveFocus();
    });
  });
});
