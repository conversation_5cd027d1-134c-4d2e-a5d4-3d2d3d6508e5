import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { db } from '../db';
import { companySettings } from '@/shared/schema';
import { eq } from 'drizzle-orm';

// Mock company ID for testing
const TEST_COMPANY_ID = 1;

describe('Company Settings', () => {
  // Clean up before tests
  beforeAll(async () => {
    // Delete any existing test settings
    await db.delete(companySettings)
      .where(eq(companySettings.company_id, TEST_COMPANY_ID));
  });

  // Clean up after tests
  afterAll(async () => {
    // Delete test settings
    await db.delete(companySettings)
      .where(eq(companySettings.company_id, TEST_COMPANY_ID));
  });

  it('should create company settings', async () => {
    // Insert test settings
    const result = await db.insert(companySettings)
      .values({
        company_id: TEST_COMPANY_ID,
        date_format: 'MM/dd/yyyy',
        currency_symbol: '$',
      })
      .returning();

    expect(result.length).toBe(1);
    expect(result[0].company_id).toBe(TEST_COMPANY_ID);
    expect(result[0].date_format).toBe('MM/dd/yyyy');
    expect(result[0].currency_symbol).toBe('$');
  });

  it('should retrieve company settings', async () => {
    // Get test settings
    const result = await db.query.companySettings.findFirst({
      where: eq(companySettings.company_id, TEST_COMPANY_ID),
    });

    expect(result).not.toBeNull();
    expect(result?.company_id).toBe(TEST_COMPANY_ID);
    expect(result?.date_format).toBe('MM/dd/yyyy');
    expect(result?.currency_symbol).toBe('$');
  });

  it('should update company settings', async () => {
    // Update test settings
    const result = await db.update(companySettings)
      .set({
        date_format: 'yyyy-MM-dd',
        currency_symbol: '€',
      })
      .where(eq(companySettings.company_id, TEST_COMPANY_ID))
      .returning();

    expect(result.length).toBe(1);
    expect(result[0].date_format).toBe('yyyy-MM-dd');
    expect(result[0].currency_symbol).toBe('€');

    // Verify the update
    const updated = await db.query.companySettings.findFirst({
      where: eq(companySettings.company_id, TEST_COMPANY_ID),
    });

    expect(updated).not.toBeNull();
    expect(updated?.date_format).toBe('yyyy-MM-dd');
    expect(updated?.currency_symbol).toBe('€');
  });
});
