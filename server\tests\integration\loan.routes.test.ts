import { describe, it, expect, beforeEach, vi } from 'vitest';
import express from 'express';
import request from 'supertest';
import { testUsers, testData, mockDatabase } from './setup';

// Mock the database
const mockDb = mockDatabase();
vi.mock('../../db', () => ({
  db: mockDb
}));

// Mock the storage
vi.mock('../../storage', () => ({
  storage: {
    getLoan: vi.fn(),
    createLoan: vi.fn(),
    updateLoan: vi.fn(),
    getLoansByCompany: vi.fn(),
    getUserCompanies: vi.fn()
  }
}));

// Mock the enhanced permission service
const mockPermissionService = {
  checkLoanCreationPermission: vi.fn(),
  checkLoanApprovalPermission: vi.fn(),
  checkLoanDisbursementPermission: vi.fn(),
  getUserPermissions: vi.fn()
};

vi.mock('../../services/enhancedPermissionService', () => ({
  EnhancedPermissionService: vi.fn().mockImplementation(() => mockPermissionService)
}));

// Mock the schema
vi.mock('@shared/schema', () => ({
  insertLoanSchema: {
    safeParse: vi.fn().mockReturnValue({
      success: true,
      data: testData.loan
    })
  }
}));

describe('Loan Routes Integration Tests', () => {
  let app: express.Express;

  beforeEach(async () => {
    // Create a fresh Express app for each test
    app = express();
    app.use(express.json());

    // Mock authentication middleware
    app.use((req: any, res, next) => {
      req.user = testUsers.loanOfficer; // Default to loan officer
      next();
    });

    // Mock prefix settings middleware
    app.use((req: any, res, next) => {
      next(); // Skip prefix validation for tests
    });

    // Import and register routes after mocking
    const { registerLoanRoutes } = await import('../../routes/loan.routes');
    registerLoanRoutes(app);
  });

  describe('POST /api/loans - Loan Creation with Permissions', () => {
    it('should create loan successfully with sufficient permissions', async () => {
      // Mock permission check to pass
      mockPermissionService.checkLoanCreationPermission.mockResolvedValueOnce(true);
      
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.createLoan).mockResolvedValueOnce({
        id: 1,
        ...testData.loan,
        company_id: 1,
        reference_code: 'LOAN-001'
      });

      const response = await request(app)
        .post('/api/loans')
        .send(testData.loan)
        .expect(201);

      expect(response.body).toHaveProperty('id', 1);
      expect(response.body).toHaveProperty('reference_code', 'LOAN-001');
      expect(mockPermissionService.checkLoanCreationPermission).toHaveBeenCalledWith(
        testUsers.loanOfficer.id,
        parseFloat(testData.loan.amount)
      );
    });

    it('should return 403 when user lacks loan creation permission', async () => {
      // Mock permission check to fail
      mockPermissionService.checkLoanCreationPermission.mockResolvedValueOnce(false);
      mockPermissionService.getUserPermissions.mockResolvedValueOnce(['customer_view_basic']);

      const response = await request(app)
        .post('/api/loans')
        .send(testData.loan)
        .expect(403);

      expect(response.body).toMatchObject({
        message: 'Insufficient permissions for loan creation',
        required_amount_limit: parseFloat(testData.loan.amount),
        action: 'create_loan'
      });
      expect(response.body).toHaveProperty('user_permissions');
    });

    it('should return 400 for invalid loan data', async () => {
      // Mock permission check to pass
      mockPermissionService.checkLoanCreationPermission.mockResolvedValueOnce(true);
      
      // Mock schema validation to fail
      const { insertLoanSchema } = await import('@shared/schema');
      vi.mocked(insertLoanSchema.safeParse).mockReturnValueOnce({
        success: false,
        error: {
          errors: [
            { path: ['amount'], message: 'Amount must be a positive number' }
          ]
        }
      });

      const invalidLoanData = { ...testData.loan, amount: '-1000' };

      const response = await request(app)
        .post('/api/loans')
        .send(invalidLoanData)
        .expect(400);

      expect(response.body).toHaveProperty('message', 'Invalid input');
      expect(response.body).toHaveProperty('errors');
    });

    it('should handle permission service errors gracefully', async () => {
      // Mock permission check to throw error
      mockPermissionService.checkLoanCreationPermission.mockRejectedValueOnce(
        new Error('Permission service unavailable')
      );

      const response = await request(app)
        .post('/api/loans')
        .send(testData.loan)
        .expect(500);

      expect(response.body).toHaveProperty('message', 'Permission check failed');
    });
  });

  describe('POST /api/loans/:id/approve - Loan Approval with Permissions', () => {
    const loanId = 1;
    const mockLoan = {
      id: loanId,
      amount: '25000',
      status: 'pending',
      company_id: 1,
      customer_id: 1
    };

    it('should approve loan successfully with sufficient permissions', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(mockLoan);
      vi.mocked(storage.updateLoan).mockResolvedValueOnce({
        ...mockLoan,
        status: 'approved',
        approved_by: testUsers.loanOfficer.id,
        approved_at: new Date()
      });

      // Mock permission check to pass
      mockPermissionService.checkLoanApprovalPermission.mockResolvedValueOnce(true);

      const response = await request(app)
        .post(`/api/loans/${loanId}/approve`)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Loan approved successfully');
      expect(response.body.loan).toHaveProperty('status', 'approved');
      expect(mockPermissionService.checkLoanApprovalPermission).toHaveBeenCalledWith(
        testUsers.loanOfficer.id,
        parseFloat(mockLoan.amount)
      );
    });

    it('should return 403 when user lacks loan approval permission', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(mockLoan);

      // Mock permission check to fail
      mockPermissionService.checkLoanApprovalPermission.mockResolvedValueOnce(false);

      const response = await request(app)
        .post(`/api/loans/${loanId}/approve`)
        .expect(403);

      expect(response.body).toMatchObject({
        message: 'Insufficient permissions for loan approval',
        required_amount_limit: parseFloat(mockLoan.amount),
        action: 'approve_loan'
      });
    });

    it('should return 404 for non-existent loan', async () => {
      // Mock storage to return null
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(null);

      const response = await request(app)
        .post(`/api/loans/999/approve`)
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Loan not found');
    });

    it('should return 400 for already approved loan', async () => {
      const approvedLoan = { ...mockLoan, status: 'approved' };
      
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(approvedLoan);

      const response = await request(app)
        .post(`/api/loans/${loanId}/approve`)
        .expect(400);

      expect(response.body).toHaveProperty('message', 'Loan is already approved');
    });

    it('should check company access for non-admin users', async () => {
      const differentCompanyLoan = { ...mockLoan, company_id: 2 };
      
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(differentCompanyLoan);
      vi.mocked(storage.getUserCompanies).mockResolvedValueOnce([
        { company_id: 1, role: 'loan_officer' }
      ]);

      const response = await request(app)
        .post(`/api/loans/${loanId}/approve`)
        .expect(403);

      expect(response.body).toHaveProperty('message', 'Access denied to this loan');
    });
  });

  describe('POST /api/loans/:id/disburse - Loan Disbursement with Permissions', () => {
    const loanId = 1;
    const mockLoan = {
      id: loanId,
      amount: '15000',
      status: 'approved',
      company_id: 1,
      customer_id: 1
    };

    it('should disburse loan successfully with sufficient permissions', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(mockLoan);
      vi.mocked(storage.updateLoan).mockResolvedValueOnce({
        ...mockLoan,
        status: 'active',
        disbursed_by: testUsers.loanOfficer.id,
        disbursed_at: new Date(),
        disbursement_method: 'bank_transfer'
      });

      // Mock permission check to pass
      mockPermissionService.checkLoanDisbursementPermission.mockResolvedValueOnce(true);

      const disbursementData = {
        disbursement_method: 'bank_transfer',
        disbursement_reference: 'TXN123456'
      };

      const response = await request(app)
        .post(`/api/loans/${loanId}/disburse`)
        .send(disbursementData)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Loan disbursed successfully');
      expect(response.body.loan).toHaveProperty('status', 'active');
      expect(mockPermissionService.checkLoanDisbursementPermission).toHaveBeenCalledWith(
        testUsers.loanOfficer.id,
        parseFloat(mockLoan.amount)
      );
    });

    it('should return 403 when user lacks disbursement permission', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(mockLoan);

      // Mock permission check to fail
      mockPermissionService.checkLoanDisbursementPermission.mockResolvedValueOnce(false);

      const response = await request(app)
        .post(`/api/loans/${loanId}/disburse`)
        .expect(403);

      expect(response.body).toMatchObject({
        message: 'Insufficient permissions for loan disbursement',
        required_amount_limit: parseFloat(mockLoan.amount),
        action: 'disburse_loan'
      });
    });

    it('should return 400 for non-approved loan', async () => {
      const pendingLoan = { ...mockLoan, status: 'pending' };
      
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getLoan).mockResolvedValueOnce(pendingLoan);

      const response = await request(app)
        .post(`/api/loans/${loanId}/disburse`)
        .expect(400);

      expect(response.body).toHaveProperty('message', 'Loan must be approved before disbursement');
    });
  });
});
