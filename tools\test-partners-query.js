#!/usr/bin/env node
/**
 * Test Script: Partners Table Query Test
 * Purpose: Test the partners table functionality and data access
 * Usage: node tools/test-partners-query.js
 */

import { withDatabasePool, tableExists, getTableColumns, countTableRows } from '../scripts/utils/database-connection.js';

async function testPartnersTable() {
  await withDatabasePool(async (pool) => {
    console.log('=== PARTNERS TABLE TEST ===\n');
    
    // Test basic connection
    console.log('1. Testing database connection...');
    const testResult = await pool.query('SELECT 1 as test');
    console.log('✅ Database connection successful:', testResult.rows[0]);
    
    // Check if the partners table exists
    console.log('\n2. Checking if partners table exists...');
    const exists = await tableExists('partners');
    console.log('Partners table exists:', exists);
    
    if (!exists) {
      console.log('❌ Partners table does not exist. Please run the migration first.');
      return;
    }
    
    // Get table structure
    console.log('\n3. Getting table structure...');
    const columns = await getTableColumns('partners');
    console.log('Partners table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name} (${col.data_type})`);
    });
    
    // Count total partners
    console.log('\n4. Counting total partners...');
    const totalCount = await countTableRows('partners');
    console.log(`Total partners in database: ${totalCount}`);
    
    // Test company-specific query
    console.log('\n5. Testing company-specific queries...');
    const companyId = 2;
    
    const partnersResult = await pool.query(
      'SELECT * FROM partners WHERE company_id = $1', 
      [companyId]
    );
    
    console.log(`Found ${partnersResult.rows.length} partners for company_id = ${companyId}`);
    
    if (partnersResult.rows.length > 0) {
      console.log('\nSample partner data:');
      const sample = partnersResult.rows[0];
      console.log({
        id: sample.id,
        name: sample.name,
        type: sample.type,
        email: sample.email,
        phone: sample.phone,
        active: sample.active,
        created_at: sample.created_at
      });
    } else {
      console.log(`No partners found for company_id = ${companyId}`);
      
      // Offer to insert test data
      console.log('\n6. Inserting test partner data...');
      const insertResult = await pool.query(`
        INSERT INTO partners (
          company_id, name, type, email, phone, address, website,
          contact_person, investment_amount, active
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
        ) RETURNING *
      `, [
        companyId, 'Test Partner', 'investor', '<EMAIL>', '1234567890',
        '123 Test St', 'www.testpartner.com', 'Test Contact', 10000.00, true
      ]);
      
      console.log('✅ Test partner inserted:');
      console.log({
        id: insertResult.rows[0].id,
        name: insertResult.rows[0].name,
        type: insertResult.rows[0].type,
        company_id: insertResult.rows[0].company_id
      });
    }
    
    // Test different partner types
    console.log('\n7. Testing partner type distribution...');
    const typeResult = await pool.query(`
      SELECT type, COUNT(*) as count
      FROM partners 
      WHERE company_id = $1
      GROUP BY type
      ORDER BY count DESC
    `, [companyId]);
    
    if (typeResult.rows.length > 0) {
      console.log('Partner types for company:');
      typeResult.rows.forEach(row => {
        console.log(`  - ${row.type}: ${row.count} partners`);
      });
    } else {
      console.log('No partner type data available');
    }
    
    // Test active vs inactive partners
    console.log('\n8. Testing active/inactive partner distribution...');
    const statusResult = await pool.query(`
      SELECT active, COUNT(*) as count
      FROM partners 
      WHERE company_id = $1
      GROUP BY active
    `, [companyId]);
    
    if (statusResult.rows.length > 0) {
      console.log('Partner status distribution:');
      statusResult.rows.forEach(row => {
        const status = row.active ? 'Active' : 'Inactive';
        console.log(`  - ${status}: ${row.count} partners`);
      });
    }
    
    console.log('\n=== TEST RESULTS ===');
    console.log('✅ Database connection: Working');
    console.log(`✅ Partners table: ${exists ? 'Exists' : 'Missing'}`);
    console.log(`✅ Table columns: ${columns.length} columns`);
    console.log(`✅ Total partners: ${totalCount}`);
    console.log(`✅ Company ${companyId} partners: ${partnersResult.rows.length}`);
    
    console.log('\n=== RECOMMENDATIONS ===');
    if (totalCount === 0) {
      console.log('- Consider adding sample partner data for testing');
    }
    if (partnersResult.rows.length === 0) {
      console.log(`- No partners found for company ${companyId} - test data was inserted`);
    }
    console.log('- Partners table is functioning correctly');
    console.log('- Company isolation is working properly');
  });
}

// Run the test
testPartnersTable().catch(console.error);
