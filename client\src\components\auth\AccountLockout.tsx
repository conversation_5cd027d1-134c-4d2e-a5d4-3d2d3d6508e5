import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Shield, Clock, AlertTriangle, Mail } from 'lucide-react';

interface AccountLockoutProps {
  email?: string;
  onBackToLogin?: () => void;
}

interface LockoutStatus {
  isLocked: boolean;
  failedAttempts: number;
  maxAttempts: number;
  remainingLockoutMinutes: number;
  lockoutExpiresAt?: string;
}

export function AccountLockout({ email, onBackToLogin }: AccountLockoutProps) {
  const [lockoutStatus, setLockoutStatus] = useState<LockoutStatus | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (email) {
      fetchLockoutStatus();
    }
  }, [email]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (lockoutStatus?.isLocked && lockoutStatus.lockoutExpiresAt) {
      interval = setInterval(() => {
        const now = new Date().getTime();
        const expiry = new Date(lockoutStatus.lockoutExpiresAt!).getTime();
        const difference = expiry - now;

        if (difference > 0) {
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((difference % (1000 * 60)) / 1000);
          setTimeRemaining(`${minutes}:${seconds.toString().padStart(2, '0')}`);
        } else {
          setTimeRemaining('');
          fetchLockoutStatus(); // Refresh status when lockout expires
        }
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [lockoutStatus]);

  const fetchLockoutStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/auth/lockout-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (result.success) {
        setLockoutStatus(result.data);
      } else {
        setError(result.message || 'Failed to fetch account status');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const requestAccountUnlock = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/auth/request-unlock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (result.success) {
        setError('');
        // Show success message or redirect
      } else {
        setError(result.message || 'Failed to request account unlock');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !lockoutStatus) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking account status...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!lockoutStatus) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle>Unable to Check Account Status</CardTitle>
          <CardDescription>
            We couldn't retrieve your account information at this time.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="flex gap-2">
            <Button onClick={fetchLockoutStatus} disabled={loading} className="flex-1">
              Try Again
            </Button>
            {onBackToLogin && (
              <Button variant="outline" onClick={onBackToLogin} className="flex-1">
                Back to Login
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  const progressPercentage = ((lockoutStatus.maxAttempts - lockoutStatus.failedAttempts) / lockoutStatus.maxAttempts) * 100;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
          {lockoutStatus.isLocked ? (
            <Shield className="h-8 w-8 text-red-600" />
          ) : (
            <AlertTriangle className="h-8 w-8 text-yellow-600" />
          )}
        </div>
        <CardTitle>
          {lockoutStatus.isLocked ? 'Account Temporarily Locked' : 'Security Warning'}
        </CardTitle>
        <CardDescription>
          {lockoutStatus.isLocked 
            ? 'Your account has been temporarily locked due to multiple failed login attempts.'
            : 'Multiple failed login attempts detected on your account.'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Failed Attempts Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Failed Attempts</span>
            <span>{lockoutStatus.failedAttempts} / {lockoutStatus.maxAttempts}</span>
          </div>
          <Progress 
            value={100 - progressPercentage} 
            className="h-2"
          />
          <p className="text-xs text-gray-600">
            {lockoutStatus.isLocked 
              ? 'Maximum attempts reached'
              : `${lockoutStatus.maxAttempts - lockoutStatus.failedAttempts} attempts remaining`
            }
          </p>
        </div>

        {/* Lockout Timer */}
        {lockoutStatus.isLocked && timeRemaining && (
          <div className="text-center p-4 bg-red-50 rounded-lg">
            <Clock className="h-6 w-6 text-red-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-red-800">
              Account unlocks in: <span className="font-mono text-lg">{timeRemaining}</span>
            </p>
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Security Tips */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Security Tips:</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Ensure you're using the correct password</li>
            <li>• Check if Caps Lock is enabled</li>
            <li>• Consider resetting your password if you've forgotten it</li>
            <li>• Contact support if you suspect unauthorized access</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3">
          {lockoutStatus.isLocked ? (
            <>
              <Button 
                onClick={requestAccountUnlock}
                disabled={loading}
                className="w-full"
              >
                <Mail className="mr-2 h-4 w-4" />
                Request Account Unlock
              </Button>
              <p className="text-xs text-gray-600 text-center">
                An unlock request will be sent to your email address
              </p>
            </>
          ) : (
            <Button 
              onClick={onBackToLogin}
              className="w-full"
            >
              Try Login Again
            </Button>
          )}
          
          <Button 
            variant="outline"
            onClick={() => window.location.href = '/forgot-password'}
            className="w-full"
          >
            Reset Password
          </Button>
          
          {onBackToLogin && (
            <Button 
              variant="ghost"
              onClick={onBackToLogin}
              className="w-full"
            >
              Back to Login
            </Button>
          )}
        </div>

        <div className="text-center text-sm text-gray-600">
          <p>
            Need help? <a href="/support" className="text-blue-600 hover:underline">Contact Support</a>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
