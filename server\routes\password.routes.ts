import { Express, Request, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, AuthRequest } from '../middleware/auth';
import { users, passwordHistory } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { db } from '../db';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import { z } from 'zod';
import { validatePassword, isPasswordInHistory, addPasswordToHistory, hasPasswordExpired, PASSWORD_POLICY } from '../utils/password-policy';

// Password reset request schema
const passwordResetRequestSchema = z.object({
  email: z.string().email(),
});

// Password reset schema
const passwordResetSchema = z.object({
  token: z.string(),
  password: z.string(),
  confirmPassword: z.string(),
});

// Change password schema
const changePasswordSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string(),
  confirmPassword: z.string(),
});

export function registerPasswordRoutes(app: Express): void {
  // Request password reset
  app.post('/api/auth/forgot-password', async (req: Request, res: Response) => {
    try {
      const result = passwordResetRequestSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const { email } = result.data;
      const user = await storage.getUserByEmail(email);

      if (!user) {
        // Don't reveal that the email doesn't exist
        return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link.' });
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 3600000); // 1 hour

      // Update user with reset token
      await db
        .update(users)
        .set({
          password_reset_token: resetToken,
          password_reset_expires: resetExpires,
        })
        .where(eq(users.id, user.id));

      // In a real application, send an email with the reset link
      // For now, just return the token for testing
      console.log(`Password reset token for ${email}: ${resetToken}`);

      return res.status(200).json({ 
        message: 'If your email is registered, you will receive a password reset link.',
        // Only include token in development environment
        ...(process.env.NODE_ENV !== 'production' && { token: resetToken })
      });
    } catch (error) {
      console.error('Password reset request error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Reset password with token
  app.post('/api/auth/reset-password', async (req: Request, res: Response) => {
    try {
      const result = passwordResetSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const { token, password, confirmPassword } = result.data;

      if (password !== confirmPassword) {
        return res.status(400).json({ message: 'Passwords do not match' });
      }

      // Validate password against policy
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.valid) {
        return res.status(400).json({ message: passwordValidation.message });
      }

      // Find user with this token
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.password_reset_token, token));

      if (!user) {
        return res.status(400).json({ message: 'Invalid or expired token' });
      }

      // Check if token is expired
      if (!user.password_reset_expires || new Date(user.password_reset_expires) < new Date()) {
        return res.status(400).json({ message: 'Token has expired' });
      }

      // Check if password is in history
      const isInHistory = await isPasswordInHistory(user.id, password);
      if (isInHistory) {
        return res.status(400).json({ 
          message: `Cannot reuse one of your last ${PASSWORD_POLICY.HISTORY_SIZE} passwords` 
        });
      }

      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // Update user's password
      await db
        .update(users)
        .set({
          password: hashedPassword,
          password_reset_token: null,
          password_reset_expires: null,
          password_updated_at: new Date(),
        })
        .where(eq(users.id, user.id));

      // Add password to history
      await addPasswordToHistory(user.id, hashedPassword);

      return res.status(200).json({ message: 'Password has been reset successfully' });
    } catch (error) {
      console.error('Password reset error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Change password (authenticated)
  app.post('/api/auth/change-password', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = changePasswordSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const { currentPassword, newPassword, confirmPassword } = result.data;

      if (newPassword !== confirmPassword) {
        return res.status(400).json({ message: 'Passwords do not match' });
      }

      // Validate password against policy
      const passwordValidation = validatePassword(newPassword);
      if (!passwordValidation.valid) {
        return res.status(400).json({ message: passwordValidation.message });
      }

      // Get user from database
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, req.user.id));

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isPasswordValid) {
        return res.status(400).json({ message: 'Current password is incorrect' });
      }

      // Check if new password is the same as current
      if (currentPassword === newPassword) {
        return res.status(400).json({ message: 'New password must be different from current password' });
      }

      // Check if password is in history
      const isInHistory = await isPasswordInHistory(user.id, newPassword);
      if (isInHistory) {
        return res.status(400).json({ 
          message: `Cannot reuse one of your last ${PASSWORD_POLICY.HISTORY_SIZE} passwords` 
        });
      }

      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // Update user's password
      await db
        .update(users)
        .set({
          password: hashedPassword,
          password_updated_at: new Date(),
        })
        .where(eq(users.id, user.id));

      // Add password to history
      await addPasswordToHistory(user.id, hashedPassword);

      return res.status(200).json({ message: 'Password changed successfully' });
    } catch (error) {
      console.error('Change password error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Check password expiration
  app.get('/api/auth/password-status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const isExpired = await hasPasswordExpired(req.user.id);
      
      return res.status(200).json({ 
        expired: isExpired,
        expiration_days: PASSWORD_POLICY.EXPIRATION_DAYS,
        policy: {
          min_length: PASSWORD_POLICY.MIN_LENGTH,
          require_uppercase: PASSWORD_POLICY.REQUIRE_UPPERCASE,
          require_lowercase: PASSWORD_POLICY.REQUIRE_LOWERCASE,
          require_number: PASSWORD_POLICY.REQUIRE_NUMBER,
          require_special: PASSWORD_POLICY.REQUIRE_SPECIAL,
        }
      });
    } catch (error) {
      console.error('Password status check error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
