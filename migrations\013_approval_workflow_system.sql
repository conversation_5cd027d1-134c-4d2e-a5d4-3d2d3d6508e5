-- Migration: 013_approval_workflow_system.sql
-- Description: Create approval workflow system for advanced approval chains and escalation rules
-- Dependencies: 012_temporary_permissions.sql

-- Create enums for approval workflow system
CREATE TYPE approval_workflow_type AS ENUM (
  'permission_elevation',
  'loan_approval',
  'customer_data_access',
  'emergency_access',
  'role_assignment',
  'custom'
);

CREATE TYPE approval_step_type AS ENUM (
  'sequential',
  'parallel',
  'any_one',
  'majority',
  'unanimous'
);

CREATE TYPE approval_action AS ENUM (
  'approve',
  'deny',
  'delegate',
  'escalate',
  'request_info'
);

CREATE TYPE workflow_status AS ENUM (
  'pending',
  'in_progress',
  'approved',
  'denied',
  'escalated',
  'cancelled',
  'expired'
);

CREATE TYPE step_status AS ENUM (
  'pending',
  'in_progress',
  'completed',
  'skipped',
  'escalated'
);

-- Create approval workflows table - defines workflow templates
CREATE TABLE approval_workflows (
  id SERIAL PRIMARY KEY,
  company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  workflow_type approval_workflow_type NOT NULL,
  trigger_conditions JSONB, -- Conditions that trigger this workflow
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  auto_escalation_hours INTEGER DEFAULT 24, -- Auto-escalate after X hours
  max_escalation_levels INTEGER DEFAULT 3,
  created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,

  CONSTRAINT unique_workflow_name UNIQUE (company_id, name)
);

-- Create approval workflow steps table - defines individual steps in workflows
CREATE TABLE approval_workflow_steps (
  id SERIAL PRIMARY KEY,
  workflow_id INTEGER NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
  step_order INTEGER NOT NULL,
  step_name TEXT NOT NULL,
  step_type approval_step_type DEFAULT 'sequential' NOT NULL,
  required_approvers INTEGER DEFAULT 1 NOT NULL, -- For majority/unanimous
  approver_roles JSONB, -- Array of role IDs that can approve this step
  approver_users JSONB, -- Array of specific user IDs that can approve
  escalation_roles JSONB, -- Roles to escalate to if step times out
  step_timeout_hours INTEGER DEFAULT 24,
  is_optional BOOLEAN DEFAULT FALSE NOT NULL,
  conditions JSONB, -- Additional conditions for this step
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,

  CONSTRAINT unique_step_order UNIQUE (workflow_id, step_order),
  CONSTRAINT check_required_approvers CHECK (required_approvers > 0),
  CONSTRAINT check_step_order CHECK (step_order > 0)
);

-- Create approval workflow instances table - tracks active workflow executions
CREATE TABLE approval_workflow_instances (
  id SERIAL PRIMARY KEY,
  workflow_id INTEGER NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
  company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  request_id TEXT NOT NULL, -- Reference to the original request (e.g., elevation request ID)
  request_type TEXT NOT NULL, -- Type of request (permission_elevation, loan_approval, etc.)
  requester_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  request_data JSONB, -- Original request data
  current_step_order INTEGER DEFAULT 1 NOT NULL,
  status workflow_status DEFAULT 'pending' NOT NULL,
  priority elevation_priority DEFAULT 'medium' NOT NULL,
  started_at TIMESTAMP DEFAULT NOW() NOT NULL,
  completed_at TIMESTAMP,
  expires_at TIMESTAMP,
  escalation_level INTEGER DEFAULT 0 NOT NULL,
  final_decision approval_action,
  final_decision_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
  final_decision_at TIMESTAMP,
  final_decision_notes TEXT,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,

  CONSTRAINT check_current_step_order CHECK (current_step_order > 0),
  CONSTRAINT check_escalation_level CHECK (escalation_level >= 0),
  CONSTRAINT check_completed_after_started CHECK (completed_at IS NULL OR completed_at >= started_at)
);

-- Create approval workflow step instances table - tracks progress of individual steps
CREATE TABLE approval_workflow_step_instances (
  id SERIAL PRIMARY KEY,
  workflow_instance_id INTEGER NOT NULL REFERENCES approval_workflow_instances(id) ON DELETE CASCADE,
  workflow_step_id INTEGER NOT NULL REFERENCES approval_workflow_steps(id) ON DELETE CASCADE,
  step_order INTEGER NOT NULL,
  status step_status DEFAULT 'pending' NOT NULL,
  assigned_to JSONB, -- Array of user IDs assigned to this step
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  timeout_at TIMESTAMP,
  escalated_at TIMESTAMP,
  escalated_to JSONB, -- Array of user IDs escalated to
  approvals_received INTEGER DEFAULT 0 NOT NULL,
  approvals_required INTEGER DEFAULT 1 NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,

  CONSTRAINT check_approvals_received CHECK (approvals_received >= 0),
  CONSTRAINT check_approvals_required CHECK (approvals_required > 0),
  CONSTRAINT check_step_order_positive CHECK (step_order > 0)
);

-- Create approval actions table - tracks individual approval actions
CREATE TABLE approval_actions (
  id SERIAL PRIMARY KEY,
  workflow_instance_id INTEGER NOT NULL REFERENCES approval_workflow_instances(id) ON DELETE CASCADE,
  step_instance_id INTEGER NOT NULL REFERENCES approval_workflow_step_instances(id) ON DELETE CASCADE,
  approver_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  action approval_action NOT NULL,
  comments TEXT,
  delegated_to INTEGER REFERENCES users(id) ON DELETE SET NULL,
  action_data JSONB, -- Additional data for the action
  created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create approval escalation rules table - defines escalation behavior
CREATE TABLE approval_escalation_rules (
  id SERIAL PRIMARY KEY,
  company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  workflow_id INTEGER REFERENCES approval_workflows(id) ON DELETE CASCADE,
  rule_name TEXT NOT NULL,
  trigger_condition TEXT NOT NULL, -- 'timeout', 'priority', 'amount', etc.
  trigger_value JSONB, -- Configuration for the trigger
  escalation_action TEXT NOT NULL, -- 'notify', 'reassign', 'auto_approve', etc.
  escalation_target JSONB, -- Who to escalate to
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create indexes for performance
CREATE INDEX idx_approval_workflows_company_type ON approval_workflows(company_id, workflow_type);
CREATE INDEX idx_approval_workflows_active ON approval_workflows(is_active) WHERE is_active = TRUE;

CREATE INDEX idx_approval_workflow_steps_workflow ON approval_workflow_steps(workflow_id, step_order);

CREATE INDEX idx_approval_workflow_instances_status ON approval_workflow_instances(status);
CREATE INDEX idx_approval_workflow_instances_company ON approval_workflow_instances(company_id);
CREATE INDEX idx_approval_workflow_instances_requester ON approval_workflow_instances(requester_id);
CREATE INDEX idx_approval_workflow_instances_request ON approval_workflow_instances(request_type, request_id);

CREATE INDEX idx_approval_workflow_step_instances_workflow ON approval_workflow_step_instances(workflow_instance_id);
CREATE INDEX idx_approval_workflow_step_instances_status ON approval_workflow_step_instances(status);

CREATE INDEX idx_approval_actions_workflow ON approval_actions(workflow_instance_id);
CREATE INDEX idx_approval_actions_approver ON approval_actions(approver_id);
CREATE INDEX idx_approval_actions_step ON approval_actions(step_instance_id);

CREATE INDEX idx_approval_escalation_rules_company ON approval_escalation_rules(company_id);
CREATE INDEX idx_approval_escalation_rules_workflow ON approval_escalation_rules(workflow_id);

-- Create update triggers for timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_approval_workflows_updated_at BEFORE UPDATE ON approval_workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_approval_workflow_steps_updated_at BEFORE UPDATE ON approval_workflow_steps FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_approval_workflow_instances_updated_at BEFORE UPDATE ON approval_workflow_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_approval_workflow_step_instances_updated_at BEFORE UPDATE ON approval_workflow_step_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_approval_escalation_rules_updated_at BEFORE UPDATE ON approval_escalation_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE approval_workflows IS 'Defines approval workflow templates for different types of requests';
COMMENT ON TABLE approval_workflow_steps IS 'Defines individual steps within approval workflows';
COMMENT ON TABLE approval_workflow_instances IS 'Tracks active workflow executions for specific requests';
COMMENT ON TABLE approval_workflow_step_instances IS 'Tracks progress of individual workflow steps';
COMMENT ON TABLE approval_actions IS 'Records individual approval actions taken by users';
COMMENT ON TABLE approval_escalation_rules IS 'Defines escalation rules for workflows';

COMMENT ON COLUMN approval_workflows.trigger_conditions IS 'JSONB configuration defining when this workflow should be triggered';
COMMENT ON COLUMN approval_workflow_steps.approver_roles IS 'Array of role IDs that can approve this step';
COMMENT ON COLUMN approval_workflow_steps.approver_users IS 'Array of specific user IDs that can approve this step';
COMMENT ON COLUMN approval_workflow_instances.request_data IS 'Original request data that triggered this workflow';
COMMENT ON COLUMN approval_escalation_rules.trigger_value IS 'JSONB configuration for escalation trigger conditions';

-- Insert default workflow templates for common scenarios
-- Note: These will be inserted for each company during setup, this is just the template structure

-- Example: Simple Permission Elevation Workflow
-- INSERT INTO approval_workflows (company_id, name, description, workflow_type, trigger_conditions, created_by)
-- VALUES (
--   1, -- company_id will be replaced during setup
--   'Simple Permission Elevation',
--   'Basic two-step approval for permission elevation requests',
--   'permission_elevation',
--   '{"priority": ["medium", "high"], "permission_types": ["loan_approve", "customer_data_access"]}',
--   1 -- created_by will be replaced during setup
-- );

-- Example workflow steps would be:
-- Step 1: Direct Manager Approval
-- Step 2: Department Head Approval (for high priority)

-- Example: Emergency Access Workflow
-- INSERT INTO approval_workflows (company_id, name, description, workflow_type, trigger_conditions, created_by)
-- VALUES (
--   1, -- company_id will be replaced during setup
--   'Emergency Access Approval',
--   'Fast-track approval for emergency access requests',
--   'emergency_access',
--   '{"priority": ["emergency"], "max_duration_hours": 4}',
--   1 -- created_by will be replaced during setup
-- );

-- Example: Loan Approval Workflow
-- INSERT INTO approval_workflows (company_id, name, description, workflow_type, trigger_conditions, created_by)
-- VALUES (
--   1, -- company_id will be replaced during setup
--   'Loan Approval Chain',
--   'Multi-tier approval for loan applications based on amount',
--   'loan_approval',
--   '{"amount_tiers": [{"min": 0, "max": 50000, "steps": 1}, {"min": 50001, "max": 200000, "steps": 2}, {"min": 200001, "max": null, "steps": 3}]}',
--   1 -- created_by will be replaced during setup
-- );

-- Example escalation rules
-- INSERT INTO approval_escalation_rules (company_id, rule_name, trigger_condition, trigger_value, escalation_action, escalation_target)
-- VALUES (
--   1, -- company_id will be replaced during setup
--   'Timeout Escalation',
--   'timeout',
--   '{"hours": 24}',
--   'notify',
--   '{"roles": ["manager", "admin"], "users": []}'
-- );

-- INSERT INTO approval_escalation_rules (company_id, rule_name, trigger_condition, trigger_value, escalation_action, escalation_target)
-- VALUES (
--   1, -- company_id will be replaced during setup
--   'High Priority Auto-Escalation',
--   'priority',
--   '{"priority": "emergency", "hours": 2}',
--   'reassign',
--   '{"roles": ["admin"], "users": []}'
-- );
