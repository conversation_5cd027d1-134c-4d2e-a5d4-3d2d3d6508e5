import { describe, it, expect, beforeEach, vi } from 'vitest';
import express from 'express';
import request from 'supertest';
import { testUsers, testData, mockDatabase } from './setup';

// Mock the database
const mockDb = mockDatabase();
vi.mock('../../db', () => ({
  db: mockDb
}));

// Mock the schema
vi.mock('@shared/schema', () => ({
  permissions: { id: 'id', code: 'code' },
  permissionConditions: { 
    id: 'id',
    permission_id: 'permission_id',
    condition_type: 'condition_type',
    configuration: 'configuration',
    is_active: 'is_active',
    priority: 'priority'
  },
  insertPermissionConditionSchema: {
    safeParse: vi.fn().mockReturnValue({
      success: true,
      data: testData.permissionCondition
    })
  }
}));

describe('Permission Conditions Routes Integration Tests', () => {
  let app: express.Express;

  beforeEach(async () => {
    // Create a fresh Express app for each test
    app = express();
    app.use(express.json());

    // Mock authentication middleware
    app.use((req: any, res, next) => {
      req.user = testUsers.saasAdmin; // Default to admin user
      next();
    });

    // Import and register routes after mocking
    const { registerPermissionConditionsRoutes } = await import('../../routes/permissionConditions.routes');
    registerPermissionConditionsRoutes(app);
  });

  describe('GET /api/permissions/:permissionCode/conditions', () => {
    it('should return conditions for a valid permission code', async () => {
      const permissionCode = 'loan_create_basic';
      const mockConditions = [
        {
          id: 1,
          permission_id: 1,
          condition_type: 'time',
          configuration: {
            start_time: '09:00',
            end_time: '17:00',
            days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
          },
          is_active: true,
          priority: 1
        },
        {
          id: 2,
          permission_id: 1,
          condition_type: 'amount',
          configuration: {
            max_amount: 10000
          },
          is_active: true,
          priority: 2
        }
      ];

      // Mock database operations
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission lookup
      mockDb.select.mockResolvedValueOnce(mockConditions); // Conditions lookup

      const response = await request(app)
        .get(`/api/permissions/${permissionCode}/conditions`)
        .expect(200);

      expect(response.body).toHaveLength(2);
      expect(response.body[0]).toMatchObject({
        id: 1,
        condition_type: 'time',
        is_active: true,
        priority: 1
      });
      expect(response.body[1]).toMatchObject({
        id: 2,
        condition_type: 'amount',
        is_active: true,
        priority: 2
      });
    });

    it('should return 404 for non-existent permission code', async () => {
      const permissionCode = 'non_existent_permission';

      // Mock database to return empty result
      mockDb.select.mockResolvedValueOnce([]);

      const response = await request(app)
        .get(`/api/permissions/${permissionCode}/conditions`)
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Permission not found');
    });

    it('should return empty array for permission with no conditions', async () => {
      const permissionCode = 'loan_create_basic';

      // Mock database operations
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists
      mockDb.select.mockResolvedValueOnce([]); // No conditions

      const response = await request(app)
        .get(`/api/permissions/${permissionCode}/conditions`)
        .expect(200);

      expect(response.body).toHaveLength(0);
    });

    it('should return 403 for non-admin user', async () => {
      // Override user for this test
      app.use((req: any, res, next) => {
        req.user = testUsers.limitedUser;
        next();
      });

      const response = await request(app)
        .get('/api/permissions/loan_create_basic/conditions')
        .expect(403);

      expect(response.body).toHaveProperty('message');
    });
  });

  describe('POST /api/permission-conditions', () => {
    it('should create permission condition successfully', async () => {
      const conditionData = {
        permission_id: 1,
        condition_type: 'time',
        configuration: {
          start_time: '09:00',
          end_time: '17:00',
          days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        },
        is_active: true,
        priority: 1
      };

      // Mock database operations
      mockDb.select.mockResolvedValueOnce([{ id: 1, code: 'loan_create_basic' }]); // Permission exists
      mockDb.insert.mockResolvedValueOnce([{
        id: 1,
        ...conditionData,
        created_at: new Date(),
        updated_at: new Date()
      }]);

      const response = await request(app)
        .post('/api/permission-conditions')
        .send(conditionData)
        .expect(201);

      expect(response.body).toHaveProperty('id', 1);
      expect(response.body).toHaveProperty('condition_type', 'time');
      expect(response.body).toHaveProperty('is_active', true);
      expect(response.body).toHaveProperty('message', 'Permission condition created successfully');
    });

    it('should return 400 for invalid condition data', async () => {
      const invalidConditionData = {
        // Missing required fields
        condition_type: 'time'
      };

      // Mock schema validation to fail
      const { insertPermissionConditionSchema } = await import('@shared/schema');
      vi.mocked(insertPermissionConditionSchema.safeParse).mockReturnValueOnce({
        success: false,
        error: {
          errors: [
            { path: ['permission_id'], message: 'Permission ID is required' }
          ]
        }
      });

      const response = await request(app)
        .post('/api/permission-conditions')
        .send(invalidConditionData)
        .expect(400);

      expect(response.body).toHaveProperty('message', 'Invalid input');
      expect(response.body).toHaveProperty('errors');
    });

    it('should return 404 for non-existent permission', async () => {
      const conditionData = {
        permission_id: 999,
        condition_type: 'time',
        configuration: { start_time: '09:00', end_time: '17:00' },
        is_active: true,
        priority: 1
      };

      // Mock database to return no permission
      mockDb.select.mockResolvedValueOnce([]);

      const response = await request(app)
        .post('/api/permission-conditions')
        .send(conditionData)
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Permission not found');
    });

    it('should return 403 for non-admin user', async () => {
      // Override user for this test
      app.use((req: any, res, next) => {
        req.user = testUsers.loanOfficer;
        next();
      });

      const conditionData = {
        permission_id: 1,
        condition_type: 'time',
        configuration: { start_time: '09:00', end_time: '17:00' },
        is_active: true,
        priority: 1
      };

      const response = await request(app)
        .post('/api/permission-conditions')
        .send(conditionData)
        .expect(403);

      expect(response.body).toHaveProperty('message');
    });
  });

  describe('PUT /api/permission-conditions/:id', () => {
    const conditionId = 1;

    it('should update permission condition successfully', async () => {
      const updateData = {
        configuration: {
          start_time: '08:00',
          end_time: '18:00',
          days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
        },
        is_active: false
      };

      // Mock database operations
      mockDb.select.mockResolvedValueOnce([{
        id: conditionId,
        permission_id: 1,
        condition_type: 'time',
        configuration: { start_time: '09:00', end_time: '17:00' },
        is_active: true,
        priority: 1
      }]);
      mockDb.update.mockResolvedValueOnce([{
        id: conditionId,
        permission_id: 1,
        condition_type: 'time',
        configuration: updateData.configuration,
        is_active: false,
        priority: 1,
        updated_at: new Date()
      }]);

      const response = await request(app)
        .put(`/api/permission-conditions/${conditionId}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('id', conditionId);
      expect(response.body).toHaveProperty('is_active', false);
      expect(response.body.configuration).toMatchObject(updateData.configuration);
      expect(response.body).toHaveProperty('message', 'Permission condition updated successfully');
    });

    it('should return 404 for non-existent condition', async () => {
      // Mock database to return no condition
      mockDb.select.mockResolvedValueOnce([]);

      const updateData = { is_active: false };

      const response = await request(app)
        .put('/api/permission-conditions/999')
        .send(updateData)
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Permission condition not found');
    });
  });

  describe('DELETE /api/permission-conditions/:id', () => {
    const conditionId = 1;

    it('should delete permission condition successfully', async () => {
      // Mock database operations
      mockDb.select.mockResolvedValueOnce([{
        id: conditionId,
        permission_id: 1,
        condition_type: 'time'
      }]);
      mockDb.delete.mockResolvedValueOnce([{ id: conditionId }]);

      const response = await request(app)
        .delete(`/api/permission-conditions/${conditionId}`)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Permission condition deleted successfully');
    });

    it('should return 404 for non-existent condition', async () => {
      // Mock database to return no condition
      mockDb.select.mockResolvedValueOnce([]);

      const response = await request(app)
        .delete('/api/permission-conditions/999')
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Permission condition not found');
    });
  });

  describe('GET /api/permission-conditions/types', () => {
    it('should return available condition types and schemas', async () => {
      const response = await request(app)
        .get('/api/permission-conditions/types')
        .expect(200);

      expect(response.body).toBeInstanceOf(Array);
      expect(response.body.length).toBeGreaterThan(0);

      // Check for expected condition types
      const conditionTypes = response.body.map((type: any) => type.type);
      expect(conditionTypes).toContain('time');
      expect(conditionTypes).toContain('amount');
      expect(conditionTypes).toContain('location');
      expect(conditionTypes).toContain('approval');

      // Check schema structure for time condition
      const timeCondition = response.body.find((type: any) => type.type === 'time');
      expect(timeCondition).toHaveProperty('name', 'Time-based');
      expect(timeCondition).toHaveProperty('description');
      expect(timeCondition).toHaveProperty('schema');
      expect(timeCondition.schema).toHaveProperty('start_time');
      expect(timeCondition.schema).toHaveProperty('end_time');
    });
  });
});
