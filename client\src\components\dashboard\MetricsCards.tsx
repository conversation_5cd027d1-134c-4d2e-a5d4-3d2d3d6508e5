import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  IndianRupee,
  Clock,
  Users,
  User
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";

interface MetricsCardsProps {
  metrics: {
    totalCollections: number;
    pendingCollections: number;
    activeAgentsCount: number;
    totalCustomersCount: number;
  };
  trends?: {
    collectionsGrowth?: number;
    pendingCollectionsChange?: number;
    agentsGrowth?: number;
    customersGrowth?: number;
  } | null;
  isLoading: boolean;
}

export default function MetricsCards({ metrics, trends, isLoading }: MetricsCardsProps) {
  // Function to format Indian currency
  const formatIndianCurrency = (amount: number) => {
    return formatCurrency(amount, 'INR', 'en-IN');
  };

  // Function to render skeleton or actual content
  const renderCard = (
    icon: React.ReactNode,
    iconClass: string,
    title: string,
    value: React.ReactNode,
    trend?: { 
      value: number;
      isPositive: boolean;
    }
  ) => {
    return (
      <Card>
        <CardContent className="px-4 py-5 sm:p-6">
          <div className="flex items-center">
            <div className={`metric-card-icon ${iconClass}`}>
              {icon}
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-muted-foreground truncate">{title}</dt>
                <dd className="flex items-baseline">
                  <div className="text-2xl font-semibold text-foreground font-mono">
                    {isLoading ? (
                      <Skeleton className="h-7 w-28" />
                    ) : (
                      value
                    )}
                  </div>
                  
                  {!isLoading && trend && (
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${trend.isPositive ? 'text-emerald-600' : 'text-red-600'}`}>
                      <svg className="self-center flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path 
                          fillRule="evenodd" 
                          d={trend.isPositive 
                            ? "M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                            : "M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                          } 
                          clipRule="evenodd" 
                        />
                      </svg>
                      <span className="sr-only">{trend.isPositive ? 'Increased by' : 'Decreased by'}</span>
                      {trend.value}%
                    </div>
                  )}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      {/* Total Collections Card */}
      {renderCard(
        <IndianRupee className="h-6 w-6 text-white" />,
        "primary-gradient",
        "Total Collections",
        metrics.totalCollections.toString(),
        trends && typeof trends.collectionsGrowth !== 'undefined' ? {
          value: Math.abs(trends.collectionsGrowth),
          isPositive: trends.collectionsGrowth >= 0
        } : undefined
      )}
      
      {/* Pending Collections Card */}
      {renderCard(
        <Clock className="h-6 w-6 text-white" />,
        "warning-gradient",
        "Pending Collections",
        metrics.pendingCollections.toString(),
        trends && typeof trends.pendingCollectionsChange !== 'undefined' ? {
          value: Math.abs(trends.pendingCollectionsChange),
          // For pending collections, a decrease is positive
          isPositive: trends.pendingCollectionsChange <= 0
        } : undefined
      )}
      
      {/* Active Agents Card */}
      {renderCard(
        <Users className="h-6 w-6 text-white" />,
        "success-gradient",
        "Active Agents",
        metrics.activeAgentsCount.toString(),
        trends && typeof trends.agentsGrowth !== 'undefined' ? {
          value: Math.abs(trends.agentsGrowth),
          isPositive: trends.agentsGrowth >= 0
        } : undefined
      )}
      
      {/* Total Customers Card */}
      {renderCard(
        <User className="h-6 w-6 text-white" />,
        "accent-gradient",
        "Total Customers",
        metrics.totalCustomersCount.toString(),
        trends && typeof trends.customersGrowth !== 'undefined' ? {
          value: Math.abs(trends.customersGrowth),
          isPositive: trends.customersGrowth >= 0
        } : undefined
      )}
    </div>
  );
}
