<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Authentication Debug Tool</h1>

    <div class="section">
        <h2>Local Storage Check</h2>
        <div id="localStorage-status"></div>
    </div>

    <div class="section">
        <h2>API Test</h2>
        <button onclick="testAPI()">Test /api/roles</button>
        <button onclick="testAPIWithAuth()">Test /api/roles with Auth</button>
        <div id="api-results"></div>
    </div>

    <div class="section">
        <h2>Login Test</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button onclick="testLogin()">Login</button>
        <div id="login-results"></div>
    </div>

    <script>
        // Check localStorage on page load
        function checkLocalStorage() {
            const authToken = localStorage.getItem('auth_token');
            const userData = localStorage.getItem('user_data');
            const user = localStorage.getItem('user');

            let html = '<h3>Tokens and User Data:</h3>';
            html += `<p><strong>auth_token:</strong> ${authToken ? 'Present' : 'Missing'}</p>`;
            if (authToken) {
                html += `<pre>${authToken}</pre>`;
            }

            html += `<p><strong>user_data:</strong> ${userData ? 'Present' : 'Missing'}</p>`;
            if (userData) {
                try {
                    const parsed = JSON.parse(userData);
                    html += `<pre>${JSON.stringify(parsed, null, 2)}</pre>`;
                } catch (e) {
                    html += `<pre class="error">Invalid JSON: ${userData}</pre>`;
                }
            }

            html += `<p><strong>user:</strong> ${user ? 'Present' : 'Missing'}</p>`;
            if (user) {
                try {
                    const parsed = JSON.parse(user);
                    html += `<pre>${JSON.stringify(parsed, null, 2)}</pre>`;
                } catch (e) {
                    html += `<pre class="error">Invalid JSON: ${user}</pre>`;
                }
            }

            document.getElementById('localStorage-status').innerHTML = html;
        }

        async function testAPI() {
            try {
                const response = await fetch('/api/roles');
                const data = await response.json();

                let html = `<h3>API Test Results:</h3>`;
                html += `<p class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</p>`;
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;

                document.getElementById('api-results').innerHTML = html;
            } catch (error) {
                document.getElementById('api-results').innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        async function testAPIWithAuth() {
            try {
                const token = localStorage.getItem('auth_token');
                const headers = {
                    'Content-Type': 'application/json'
                };

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('/api/roles', {
                    headers,
                    credentials: 'include'
                });
                const data = await response.json();

                let html = `<h3>Authenticated API Test Results:</h3>`;
                html += `<p><strong>Token used:</strong> ${token ? 'Yes' : 'No'}</p>`;
                html += `<p class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</p>`;
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;

                document.getElementById('api-results').innerHTML = html;
            } catch (error) {
                document.getElementById('api-results').innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        async function testLogin() {
            try {
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;

                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password }),
                    credentials: 'include'
                });

                const data = await response.json();

                let html = `<h3>Login Test Results:</h3>`;
                html += `<p class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</p>`;
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;

                if (response.ok && data.token) {
                    localStorage.setItem('auth_token', data.token);
                    if (data.user) {
                        localStorage.setItem('user_data', JSON.stringify(data.user));
                    }
                    html += `<p class="success">Token saved to localStorage!</p>`;
                    checkLocalStorage(); // Refresh the localStorage display

                    // Test the API with the new token
                    setTimeout(testAPIWithAuth, 1000);
                }

                document.getElementById('login-results').innerHTML = html;
            } catch (error) {
                document.getElementById('login-results').innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        // Initialize on page load
        checkLocalStorage();
    </script>
</body>
</html>
