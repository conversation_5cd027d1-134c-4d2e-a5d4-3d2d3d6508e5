import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mfaService } from '../services/mfaService';
import { accountLockoutService } from '../services/accountLockoutService';
import { emailVerificationService } from '../services/emailVerificationService';
import { db } from '../db';
import { users, userMfaSettings, mfaVerificationAttempts, emailVerificationTokens } from '@shared/schema';
import { eq } from 'drizzle-orm';

// Mock speakeasy and qrcode
vi.mock('speakeasy', () => ({
  default: {
    generateSecret: vi.fn(() => ({
      base32: 'JBSWY3DPEHPK3PXP',
      otpauth_url: 'otpauth://totp/TrackFina%20(<EMAIL>)?secret=JBSWY3DPEHPK3PXP&issuer=TrackFina'
    })),
    totp: {
      verify: vi.fn(() => true)
    }
  }
}));

vi.mock('qrcode', () => ({
  default: {
    toDataURL: vi.fn(() => Promise.resolve('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='))
  }
}));

describe('MFA Service', () => {
  const testUserId = 999;
  const testEmail = '<EMAIL>';

  beforeEach(async () => {
    // Clean up test data
    await db.delete(mfaVerificationAttempts).where(eq(mfaVerificationAttempts.user_id, testUserId));
    await db.delete(userMfaSettings).where(eq(userMfaSettings.user_id, testUserId));
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(mfaVerificationAttempts).where(eq(mfaVerificationAttempts.user_id, testUserId));
    await db.delete(userMfaSettings).where(eq(userMfaSettings.user_id, testUserId));
  });

  describe('generateMFASecret', () => {
    it('should generate MFA secret and QR code', async () => {
      const result = await mfaService.generateMFASecret(testUserId, testEmail);

      expect(result).toHaveProperty('secret');
      expect(result).toHaveProperty('qrCodeUrl');
      expect(result).toHaveProperty('backupCodes');
      expect(result.backupCodes).toHaveLength(10);
      expect(result.secret).toBe('JBSWY3DPEHPK3PXP');
    });

    it('should store MFA settings in database', async () => {
      await mfaService.generateMFASecret(testUserId, testEmail);

      const [mfaSettings] = await db
        .select()
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, testUserId));

      expect(mfaSettings).toBeDefined();
      expect(mfaSettings.secret).toBe('JBSWY3DPEHPK3PXP');
      expect(mfaSettings.is_enabled).toBe(false);
      expect(mfaSettings.backup_codes).toHaveLength(10);
    });
  });

  describe('verifyMFAToken', () => {
    beforeEach(async () => {
      // Set up MFA for test user
      await mfaService.generateMFASecret(testUserId, testEmail);
    });

    it('should verify valid TOTP token', async () => {
      const result = await mfaService.verifyMFAToken(testUserId, '123456');
      expect(result).toBe(true);
    });

    it('should verify valid backup code', async () => {
      // Get backup codes
      const [mfaSettings] = await db
        .select()
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, testUserId));

      const backupCode = (mfaSettings.backup_codes as string[])[0];
      const result = await mfaService.verifyMFAToken(testUserId, backupCode);
      expect(result).toBe(true);

      // Verify backup code is removed after use
      const [updatedSettings] = await db
        .select()
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, testUserId));

      expect((updatedSettings.backup_codes as string[]).includes(backupCode)).toBe(false);
    });

    it('should reject invalid token', async () => {
      // Mock speakeasy to return false
      const speakeasy = await import('speakeasy');
      vi.mocked(speakeasy.default.totp.verify).mockReturnValueOnce(false);

      const result = await mfaService.verifyMFAToken(testUserId, 'invalid');
      expect(result).toBe(false);
    });

    it('should log verification attempts', async () => {
      await mfaService.verifyMFAToken(testUserId, '123456', '127.0.0.1', 'test-agent');

      const attempts = await db
        .select()
        .from(mfaVerificationAttempts)
        .where(eq(mfaVerificationAttempts.user_id, testUserId));

      expect(attempts).toHaveLength(1);
      expect(attempts[0].success).toBe(true);
      expect(attempts[0].ip_address).toBe('127.0.0.1');
      expect(attempts[0].user_agent).toBe('test-agent');
    });
  });

  describe('enableMFA', () => {
    beforeEach(async () => {
      await mfaService.generateMFASecret(testUserId, testEmail);
    });

    it('should enable MFA for user', async () => {
      await mfaService.enableMFA(testUserId);

      const [mfaSettings] = await db
        .select()
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, testUserId));

      expect(mfaSettings.is_enabled).toBe(true);
    });
  });

  describe('disableMFA', () => {
    beforeEach(async () => {
      await mfaService.generateMFASecret(testUserId, testEmail);
      await mfaService.enableMFA(testUserId);
    });

    it('should disable MFA for user', async () => {
      await mfaService.disableMFA(testUserId);

      const [mfaSettings] = await db
        .select()
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, testUserId));

      expect(mfaSettings.is_enabled).toBe(false);
    });
  });

  describe('getMFAStatus', () => {
    it('should return correct status for user without MFA', async () => {
      const status = await mfaService.getMFAStatus(testUserId);

      expect(status.enabled).toBe(false);
      expect(status.hasBackupCodes).toBe(false);
      expect(status.backupCodesCount).toBe(0);
    });

    it('should return correct status for user with MFA enabled', async () => {
      await mfaService.generateMFASecret(testUserId, testEmail);
      await mfaService.enableMFA(testUserId);

      const status = await mfaService.getMFAStatus(testUserId);

      expect(status.enabled).toBe(true);
      expect(status.hasBackupCodes).toBe(true);
      expect(status.backupCodesCount).toBe(10);
    });
  });

  describe('regenerateBackupCodes', () => {
    beforeEach(async () => {
      await mfaService.generateMFASecret(testUserId, testEmail);
    });

    it('should generate new backup codes', async () => {
      const originalCodes = await mfaService.getMFAStatus(testUserId);
      const newCodes = await mfaService.regenerateBackupCodes(testUserId);

      expect(newCodes).toHaveLength(10);
      expect(newCodes).not.toEqual(originalCodes);

      const status = await mfaService.getMFAStatus(testUserId);
      expect(status.backupCodesCount).toBe(10);
    });
  });
});

describe('Account Lockout Service', () => {
  const testUserId = 998;

  beforeEach(async () => {
    // Reset user lockout status
    await db.update(users)
      .set({
        failed_login_attempts: 0,
        locked_until: null,
        last_login_attempt: null
      })
      .where(eq(users.id, testUserId));
  });

  describe('recordFailedAttempt', () => {
    it('should increment failed attempts', async () => {
      const result = await accountLockoutService.recordFailedAttempt(testUserId, '127.0.0.1');

      expect(result.failedAttempts).toBe(1);
      expect(result.isLocked).toBe(false);
    });

    it('should lock account after max attempts', async () => {
      // Record 5 failed attempts
      for (let i = 0; i < 5; i++) {
        await accountLockoutService.recordFailedAttempt(testUserId, '127.0.0.1');
      }

      const result = await accountLockoutService.recordFailedAttempt(testUserId, '127.0.0.1');

      expect(result.isLocked).toBe(true);
      expect(result.remainingLockoutMinutes).toBeGreaterThan(0);
    });
  });

  describe('recordSuccessfulLogin', () => {
    it('should reset failed attempts', async () => {
      // Record some failed attempts
      await accountLockoutService.recordFailedAttempt(testUserId, '127.0.0.1');
      await accountLockoutService.recordFailedAttempt(testUserId, '127.0.0.1');

      // Record successful login
      await accountLockoutService.recordSuccessfulLogin(testUserId);

      const status = await accountLockoutService.isAccountLocked(testUserId);
      expect(status.failedAttempts).toBe(0);
      expect(status.isLocked).toBe(false);
    });
  });

  describe('isAccountLocked', () => {
    it('should return correct lock status', async () => {
      const status = await accountLockoutService.isAccountLocked(testUserId);

      expect(status.isLocked).toBe(false);
      expect(status.failedAttempts).toBe(0);
    });
  });

  describe('unlockAccount', () => {
    it('should unlock locked account', async () => {
      // Lock the account
      for (let i = 0; i < 6; i++) {
        await accountLockoutService.recordFailedAttempt(testUserId, '127.0.0.1');
      }

      // Unlock the account
      await accountLockoutService.unlockAccount(testUserId, 1);

      const status = await accountLockoutService.isAccountLocked(testUserId);
      expect(status.isLocked).toBe(false);
      expect(status.failedAttempts).toBe(0);
    });
  });
});

describe('Email Verification Service', () => {
  const testUserId = 997;
  const testEmail = '<EMAIL>';

  beforeEach(async () => {
    // Clean up test data
    await db.delete(emailVerificationTokens).where(eq(emailVerificationTokens.user_id, testUserId));
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(emailVerificationTokens).where(eq(emailVerificationTokens.user_id, testUserId));
  });

  describe('generateVerificationToken', () => {
    it('should generate verification token', async () => {
      const token = await emailVerificationService.generateVerificationToken(testUserId);

      expect(token).toBeDefined();
      expect(token).toHaveLength(64);

      // Verify token is stored in database
      const [tokenRecord] = await db
        .select()
        .from(emailVerificationTokens)
        .where(eq(emailVerificationTokens.user_id, testUserId));

      expect(tokenRecord).toBeDefined();
      expect(tokenRecord.token).toBe(token);
    });
  });

  describe('verifyEmail', () => {
    it('should verify valid token', async () => {
      const token = await emailVerificationService.generateVerificationToken(testUserId);
      const result = await emailVerificationService.verifyEmail(token);

      expect(result.success).toBe(true);
      expect(result.userId).toBe(testUserId);

      // Verify token is deleted after use
      const [tokenRecord] = await db
        .select()
        .from(emailVerificationTokens)
        .where(eq(emailVerificationTokens.token, token));

      expect(tokenRecord).toBeUndefined();
    });

    it('should reject invalid token', async () => {
      const result = await emailVerificationService.verifyEmail('invalid-token');

      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid or expired');
    });
  });

  describe('isEmailVerified', () => {
    it('should return verification status', async () => {
      const isVerified = await emailVerificationService.isEmailVerified(testUserId);
      expect(typeof isVerified).toBe('boolean');
    });
  });
});
