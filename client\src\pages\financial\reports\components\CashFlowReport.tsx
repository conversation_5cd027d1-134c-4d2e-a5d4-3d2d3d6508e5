import React from 'react';
import { format } from 'date-fns';
import { useReportData } from '../hooks/useReportData';
import { CashFlowReport as CashFlowReportType, COLORS } from '../types';
import { DateRangePicker } from './DateRangePicker';
import { ReportDownloadButton } from './ReportDownloadButton';
import { ReportCard } from './ReportCard';
import { Separator } from '@/components/ui/separator';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { TrendingDown, ArrowUpDown, IndianRupee } from 'lucide-react';

interface CashFlowReportProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  onDateRangeChange: (range: { from: Date; to: Date }) => void;
}

export function CashFlowReport({
  dateRange,
  onDateRangeChange,
}: CashFlowReportProps) {
  const { data, isLoading, isError, refetch } = useReportData<CashFlowReportType>({
    reportType: 'cash-flow',
    startDate: dateRange.from,
    endDate: dateRange.to,
  });

  // Format currency in Indian Rupees
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Prepare chart data
  const barChartData = React.useMemo(() => {
    if (!data || !data.operatingActivities || !data.investingActivities || !data.financingActivities) return [];

    return [
      {
        name: 'Operating',
        amount: data.operatingActivities.netOperatingCashflow || 0,
      },
      {
        name: 'Investing',
        amount: data.investingActivities.netInvestingCashflow || 0,
      },
      {
        name: 'Financing',
        amount: data.financingActivities.netFinancingCashflow || 0,
      },
      {
        name: 'Net Cashflow',
        amount: data.netCashflow || 0,
      },
    ];
  }, [data]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <DateRangePicker
          dateRange={dateRange}
          onDateRangeChange={onDateRangeChange}
        />
        <div className="flex gap-2">
          <ReportDownloadButton
            reportType="cash-flow"
            format="pdf"
            startDate={dateRange.from}
            endDate={dateRange.to}
            disabled={isLoading || isError}
          />
          <ReportDownloadButton
            reportType="cash-flow"
            format="csv"
            startDate={dateRange.from}
            endDate={dateRange.to}
            disabled={isLoading || isError}
          />
        </div>
      </div>

      <ReportCard
        title="Cash Flow Statement"
        description={`For the period ${format(dateRange.from, 'LLL dd, y')} to ${format(dateRange.to, 'LLL dd, y')}`}
        isLoading={isLoading}
        isError={isError}
        onRetry={() => refetch()}
      >
        {data && data.operatingActivities && data.investingActivities && data.financingActivities && (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <IndianRupee className="h-5 w-5 text-blue-500 mr-2" />
                  <h3 className="text-sm font-medium">Opening Balance</h3>
                </div>
                <p className="text-2xl font-bold mt-2">{formatCurrency(data.openingBalance || 0)}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <ArrowUpDown className="h-5 w-5 text-green-500 mr-2" />
                  <h3 className="text-sm font-medium">Net Cash Flow</h3>
                </div>
                <p className="text-2xl font-bold mt-2">{formatCurrency(data.netCashflow || 0)}</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <IndianRupee className="h-5 w-5 text-purple-500 mr-2" />
                  <h3 className="text-sm font-medium">Closing Balance</h3>
                </div>
                <p className="text-2xl font-bold mt-2">{formatCurrency(data.closingBalance || 0)}</p>
              </div>
            </div>

            {/* Chart */}
            <div className="h-80">
              <h3 className="text-lg font-medium mb-2">Cash Flow Summary</h3>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={barChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Amount']} />
                  <Legend />
                  <Bar dataKey="amount" fill="#0088FE" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Detailed Tables */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Operating Activities</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr className="bg-gray-50">
                        <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Inflows</td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900"></td>
                      </tr>
                      {data.operatingActivities.inflows && Array.isArray(data.operatingActivities.inflows) && data.operatingActivities.inflows.map((item, index) => (
                        <tr key={`inflow-${index}`}>
                          <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{item.category || 'Unknown'}</td>
                          <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">{formatCurrency(item.amount || 0)}</td>
                        </tr>
                      ))}
                      <tr className="bg-gray-50">
                        <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Outflows</td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900"></td>
                      </tr>
                      {data.operatingActivities.outflows && Array.isArray(data.operatingActivities.outflows) && data.operatingActivities.outflows.map((item, index) => (
                        <tr key={`outflow-${index}`}>
                          <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{item.category || 'Unknown'}</td>
                          <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">-{formatCurrency(Math.abs(item.amount || 0))}</td>
                        </tr>
                      ))}
                      <tr className="bg-blue-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Net Operating Cash Flow</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">{formatCurrency(data.operatingActivities.netOperatingCashflow || 0)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Investing Activities</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr className="bg-gray-50">
                        <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Inflows</td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900"></td>
                      </tr>
                      {data.investingActivities.inflows && Array.isArray(data.investingActivities.inflows) && data.investingActivities.inflows.map((item, index) => (
                        <tr key={`inflow-${index}`}>
                          <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{item.category || 'Unknown'}</td>
                          <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">{formatCurrency(item.amount || 0)}</td>
                        </tr>
                      ))}
                      <tr className="bg-gray-50">
                        <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Outflows</td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900"></td>
                      </tr>
                      {data.investingActivities.outflows && Array.isArray(data.investingActivities.outflows) && data.investingActivities.outflows.map((item, index) => (
                        <tr key={`outflow-${index}`}>
                          <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{item.category || 'Unknown'}</td>
                          <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">-{formatCurrency(Math.abs(item.amount || 0))}</td>
                        </tr>
                      ))}
                      <tr className="bg-blue-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Net Investing Cash Flow</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">{formatCurrency(data.investingActivities.netInvestingCashflow || 0)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Financing Activities</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr className="bg-gray-50">
                        <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Inflows</td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900"></td>
                      </tr>
                      {data.financingActivities.inflows && Array.isArray(data.financingActivities.inflows) && data.financingActivities.inflows.map((item, index) => (
                        <tr key={`inflow-${index}`}>
                          <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{item.category || 'Unknown'}</td>
                          <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">{formatCurrency(item.amount || 0)}</td>
                        </tr>
                      ))}
                      <tr className="bg-gray-50">
                        <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Outflows</td>
                        <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900"></td>
                      </tr>
                      {data.financingActivities.outflows && Array.isArray(data.financingActivities.outflows) && data.financingActivities.outflows.map((item, index) => (
                        <tr key={`outflow-${index}`}>
                          <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{item.category || 'Unknown'}</td>
                          <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">-{formatCurrency(Math.abs(item.amount || 0))}</td>
                        </tr>
                      ))}
                      <tr className="bg-blue-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Net Financing Cash Flow</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">{formatCurrency(data.financingActivities.netFinancingCashflow || 0)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <Separator />

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex justify-between">
                  <h3 className="text-lg font-bold">Net Cash Flow</h3>
                  <p className="text-lg font-bold">{formatCurrency(data.netCashflow || 0)}</p>
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-bold">Closing Balance</h3>
                    <p className="text-sm text-gray-500">Opening Balance + Net Cash Flow</p>
                  </div>
                  <p className="text-lg font-bold">{formatCurrency(data.closingBalance || 0)}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </ReportCard>
    </div>
  );
}
