import { db } from '../db';
import { users } from '@shared/schema';
import { eq } from 'drizzle-orm';

export interface LockoutPolicy {
  maxFailedAttempts: number;
  lockoutDurationMinutes: number;
  progressiveLockout: boolean;
  resetAfterHours: number;
}

export interface LockoutStatus {
  isLocked: boolean;
  failedAttempts: number;
  lockedUntil?: Date;
  remainingLockoutMinutes?: number;
}

export class AccountLockoutService {
  private defaultPolicy: LockoutPolicy = {
    maxFailedAttempts: 5,
    lockoutDurationMinutes: 15,
    progressiveLockout: true,
    resetAfterHours: 24
  };

  /**
   * Record a failed login attempt
   */
  async recordFailedAttempt(userId: number, ipAddress?: string): Promise<LockoutStatus> {
    try {
      const [user] = await db
        .select({
          failed_login_attempts: users.failed_login_attempts,
          locked_until: users.locked_until,
          last_login_attempt: users.last_login_attempt
        })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        throw new Error('User not found');
      }

      const now = new Date();
      const currentAttempts = user.failed_login_attempts || 0;
      const newAttempts = currentAttempts + 1;

      // Check if we should reset attempts (after reset period)
      const shouldReset = user.last_login_attempt && 
        (now.getTime() - user.last_login_attempt.getTime()) > (this.defaultPolicy.resetAfterHours * 60 * 60 * 1000);

      const finalAttempts = shouldReset ? 1 : newAttempts;
      let lockedUntil: Date | null = null;

      // Calculate lockout if max attempts exceeded
      if (finalAttempts >= this.defaultPolicy.maxFailedAttempts) {
        const lockoutMinutes = this.calculateLockoutDuration(finalAttempts);
        lockedUntil = new Date(now.getTime() + (lockoutMinutes * 60 * 1000));
      }

      // Update user record
      await db.update(users)
        .set({
          failed_login_attempts: finalAttempts,
          locked_until: lockedUntil,
          last_login_attempt: now,
          updated_at: now
        })
        .where(eq(users.id, userId));

      // Log the failed attempt
      console.log(`Failed login attempt for user ${userId} from IP ${ipAddress}. Attempts: ${finalAttempts}`);

      return {
        isLocked: !!lockedUntil && lockedUntil > now,
        failedAttempts: finalAttempts,
        lockedUntil: lockedUntil || undefined,
        remainingLockoutMinutes: lockedUntil ? Math.ceil((lockedUntil.getTime() - now.getTime()) / (60 * 1000)) : undefined
      };
    } catch (error) {
      console.error('Error recording failed attempt:', error);
      throw new Error('Failed to record login attempt');
    }
  }

  /**
   * Record a successful login (resets failed attempts)
   */
  async recordSuccessfulLogin(userId: number): Promise<void> {
    try {
      await db.update(users)
        .set({
          failed_login_attempts: 0,
          locked_until: null,
          last_login_attempt: new Date(),
          updated_at: new Date()
        })
        .where(eq(users.id, userId));

      console.log(`Successful login for user ${userId}. Failed attempts reset.`);
    } catch (error) {
      console.error('Error recording successful login:', error);
      throw new Error('Failed to record successful login');
    }
  }

  /**
   * Check if account is currently locked
   */
  async isAccountLocked(userId: number): Promise<LockoutStatus> {
    try {
      const [user] = await db
        .select({
          failed_login_attempts: users.failed_login_attempts,
          locked_until: users.locked_until
        })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        throw new Error('User not found');
      }

      const now = new Date();
      const isLocked = user.locked_until && user.locked_until > now;

      return {
        isLocked: !!isLocked,
        failedAttempts: user.failed_login_attempts || 0,
        lockedUntil: user.locked_until || undefined,
        remainingLockoutMinutes: isLocked 
          ? Math.ceil((user.locked_until!.getTime() - now.getTime()) / (60 * 1000))
          : undefined
      };
    } catch (error) {
      console.error('Error checking account lock status:', error);
      throw new Error('Failed to check account lock status');
    }
  }

  /**
   * Unlock account (admin function)
   */
  async unlockAccount(userId: number, adminUserId: number): Promise<void> {
    try {
      await db.update(users)
        .set({
          failed_login_attempts: 0,
          locked_until: null,
          updated_at: new Date()
        })
        .where(eq(users.id, userId));

      console.log(`Account ${userId} unlocked by admin ${adminUserId}`);
    } catch (error) {
      console.error('Error unlocking account:', error);
      throw new Error('Failed to unlock account');
    }
  }

  /**
   * Get all locked accounts (admin function)
   */
  async getLockedAccounts(): Promise<Array<{
    id: number;
    email: string;
    full_name: string;
    failed_attempts: number;
    locked_until: Date;
    remaining_minutes: number;
  }>> {
    try {
      const now = new Date();
      
      const lockedUsers = await db
        .select({
          id: users.id,
          email: users.email,
          full_name: users.full_name,
          failed_login_attempts: users.failed_login_attempts,
          locked_until: users.locked_until
        })
        .from(users)
        .where(eq(users.locked_until, now)); // This needs to be a proper comparison

      // Filter and format results
      return lockedUsers
        .filter(user => user.locked_until && user.locked_until > now)
        .map(user => ({
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          failed_attempts: user.failed_login_attempts || 0,
          locked_until: user.locked_until!,
          remaining_minutes: Math.ceil((user.locked_until!.getTime() - now.getTime()) / (60 * 1000))
        }));
    } catch (error) {
      console.error('Error getting locked accounts:', error);
      throw new Error('Failed to get locked accounts');
    }
  }

  /**
   * Calculate lockout duration based on failed attempts (progressive lockout)
   */
  private calculateLockoutDuration(failedAttempts: number): number {
    if (!this.defaultPolicy.progressiveLockout) {
      return this.defaultPolicy.lockoutDurationMinutes;
    }

    // Progressive lockout: 15min, 30min, 1hr, 2hr, 4hr, 8hr, 24hr
    const baseDuration = this.defaultPolicy.lockoutDurationMinutes;
    const multiplier = Math.min(Math.pow(2, failedAttempts - this.defaultPolicy.maxFailedAttempts), 96); // Max 24 hours
    
    return baseDuration * multiplier;
  }

  /**
   * Get lockout policy
   */
  getLockoutPolicy(): LockoutPolicy {
    return { ...this.defaultPolicy };
  }

  /**
   * Update lockout policy (admin function)
   */
  updateLockoutPolicy(policy: Partial<LockoutPolicy>): void {
    this.defaultPolicy = { ...this.defaultPolicy, ...policy };
    console.log('Lockout policy updated:', this.defaultPolicy);
  }

  /**
   * Clean up expired lockouts (should be run periodically)
   */
  async cleanupExpiredLockouts(): Promise<number> {
    try {
      const now = new Date();
      
      const result = await db.update(users)
        .set({
          locked_until: null,
          updated_at: now
        })
        .where(eq(users.locked_until, now)); // This needs to be a proper comparison for expired lockouts

      console.log(`Cleaned up ${result.rowCount || 0} expired account lockouts`);
      return result.rowCount || 0;
    } catch (error) {
      console.error('Error cleaning up expired lockouts:', error);
      return 0;
    }
  }

  /**
   * Get account security summary
   */
  async getSecuritySummary(userId: number): Promise<{
    failedAttempts: number;
    isLocked: boolean;
    lastAttempt?: Date;
    lockoutHistory: number;
  }> {
    try {
      const [user] = await db
        .select({
          failed_login_attempts: users.failed_login_attempts,
          locked_until: users.locked_until,
          last_login_attempt: users.last_login_attempt
        })
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        throw new Error('User not found');
      }

      const now = new Date();
      const isLocked = user.locked_until && user.locked_until > now;

      return {
        failedAttempts: user.failed_login_attempts || 0,
        isLocked: !!isLocked,
        lastAttempt: user.last_login_attempt || undefined,
        lockoutHistory: 0 // TODO: Implement lockout history tracking
      };
    } catch (error) {
      console.error('Error getting security summary:', error);
      throw new Error('Failed to get security summary');
    }
  }
}

export const accountLockoutService = new AccountLockoutService();
