-- Enhanced Session Management Schema
-- Migration: 017_enhanced_session_management.sql
-- Description: Add enhanced session management with concurrent session limits, device tracking, and timeout policies

-- Session timeout policy enum
CREATE TYPE session_timeout_policy AS ENUM ('idle', 'absolute', 'rolling', 'hybrid');

-- Device type enum
CREATE TYPE device_type AS ENUM ('desktop', 'laptop', 'mobile', 'tablet', 'unknown');

-- Session status enum
CREATE TYPE session_status AS ENUM ('active', 'expired', 'terminated', 'suspended');

-- Enhanced user sessions table
CREATE TABLE IF NOT EXISTS "user_sessions" (
  "id" SERIAL PRIMARY KEY,
  "session_id" varchar(255) NOT NULL UNIQUE,
  "user_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Device and location information
  "device_fingerprint" varchar(255),
  "device_type" device_type NOT NULL DEFAULT 'unknown',
  "device_name" varchar(255),
  "user_agent" text,
  "ip_address" inet,
  "location_country" varchar(10),
  "location_region" varchar(100),
  "location_city" varchar(100),
  
  -- Session timing
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "last_activity" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "expires_at" timestamp,
  "idle_timeout" integer DEFAULT 3600, -- seconds
  "absolute_timeout" integer DEFAULT 86400, -- seconds
  
  -- Session status and metadata
  "status" session_status NOT NULL DEFAULT 'active',
  "terminated_at" timestamp,
  "terminated_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "termination_reason" varchar(255),
  
  -- Security flags
  "is_trusted_device" boolean DEFAULT false,
  "requires_mfa" boolean DEFAULT false,
  "mfa_verified" boolean DEFAULT false,
  "fresh_auth" boolean DEFAULT true,
  
  -- Additional metadata
  "session_data" jsonb DEFAULT '{}',
  "security_flags" jsonb DEFAULT '{}',
  
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Session policies table for configurable session management
CREATE TABLE IF NOT EXISTS "session_policies" (
  "id" SERIAL PRIMARY KEY,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  "role" varchar(50),
  "policy_name" varchar(255) NOT NULL,
  
  -- Concurrent session limits
  "max_concurrent_sessions" integer DEFAULT 5,
  "max_sessions_per_device_type" jsonb DEFAULT '{"desktop": 3, "laptop": 3, "mobile": 2, "tablet": 2}',
  
  -- Timeout policies
  "timeout_policy" session_timeout_policy DEFAULT 'hybrid',
  "idle_timeout_seconds" integer DEFAULT 3600, -- 1 hour
  "absolute_timeout_seconds" integer DEFAULT 86400, -- 24 hours
  "rolling_timeout_seconds" integer DEFAULT 28800, -- 8 hours
  
  -- Device policies
  "allow_mobile_access" boolean DEFAULT true,
  "require_device_registration" boolean DEFAULT false,
  "trusted_device_timeout_days" integer DEFAULT 30,
  
  -- Security policies
  "require_mfa_for_new_device" boolean DEFAULT true,
  "require_fresh_auth_for_sensitive" boolean DEFAULT true,
  "auto_logout_on_suspicious_activity" boolean DEFAULT true,
  
  -- Geographic restrictions
  "allowed_countries" text[], -- ISO country codes
  "blocked_countries" text[], -- ISO country codes
  "require_vpn" boolean DEFAULT false,
  
  "is_active" boolean DEFAULT true,
  "priority" integer DEFAULT 0, -- Higher priority policies override lower ones
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE("company_id", "role", "policy_name")
);

-- Session activity logs for tracking and auditing
CREATE TABLE IF NOT EXISTS "session_activity_logs" (
  "id" SERIAL PRIMARY KEY,
  "session_id" varchar(255) NOT NULL,
  "user_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Activity details
  "activity_type" varchar(100) NOT NULL, -- 'login', 'logout', 'timeout', 'activity', 'security_event'
  "activity_description" text,
  "endpoint" varchar(255),
  "method" varchar(10),
  "status_code" integer,
  
  -- Request context
  "ip_address" inet,
  "user_agent" text,
  "referer" text,
  "request_id" varchar(255),
  
  -- Security context
  "risk_score" integer DEFAULT 0, -- 0-100 risk assessment
  "security_flags" jsonb DEFAULT '{}',
  "anomaly_detected" boolean DEFAULT false,
  
  -- Timing
  "timestamp" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "response_time_ms" integer,
  
  -- Additional context
  "metadata" jsonb DEFAULT '{}'
);

-- Device registry for trusted device management
CREATE TABLE IF NOT EXISTS "trusted_devices" (
  "id" SERIAL PRIMARY KEY,
  "user_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "device_fingerprint" varchar(255) NOT NULL,
  "device_name" varchar(255),
  "device_type" device_type NOT NULL,
  
  -- Device details
  "user_agent" text,
  "last_ip_address" inet,
  "last_location_country" varchar(10),
  "last_location_region" varchar(100),
  "last_location_city" varchar(100),
  
  -- Trust status
  "is_trusted" boolean DEFAULT false,
  "trust_level" integer DEFAULT 0, -- 0-100 trust score
  "trusted_at" timestamp,
  "trusted_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Usage tracking
  "first_seen" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "last_seen" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "login_count" integer DEFAULT 0,
  "last_login" timestamp,
  
  -- Security
  "is_blocked" boolean DEFAULT false,
  "blocked_at" timestamp,
  "blocked_reason" text,
  "blocked_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE("user_id", "device_fingerprint")
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS "idx_user_sessions_user_id" ON "user_sessions"("user_id");
CREATE INDEX IF NOT EXISTS "idx_user_sessions_session_id" ON "user_sessions"("session_id");
CREATE INDEX IF NOT EXISTS "idx_user_sessions_status" ON "user_sessions"("status");
CREATE INDEX IF NOT EXISTS "idx_user_sessions_last_activity" ON "user_sessions"("last_activity");
CREATE INDEX IF NOT EXISTS "idx_user_sessions_expires_at" ON "user_sessions"("expires_at");
CREATE INDEX IF NOT EXISTS "idx_user_sessions_device_fingerprint" ON "user_sessions"("device_fingerprint");

CREATE INDEX IF NOT EXISTS "idx_session_policies_company_role" ON "session_policies"("company_id", "role");
CREATE INDEX IF NOT EXISTS "idx_session_policies_active" ON "session_policies"("is_active");

CREATE INDEX IF NOT EXISTS "idx_session_activity_logs_session_id" ON "session_activity_logs"("session_id");
CREATE INDEX IF NOT EXISTS "idx_session_activity_logs_user_id" ON "session_activity_logs"("user_id");
CREATE INDEX IF NOT EXISTS "idx_session_activity_logs_timestamp" ON "session_activity_logs"("timestamp");
CREATE INDEX IF NOT EXISTS "idx_session_activity_logs_activity_type" ON "session_activity_logs"("activity_type");

CREATE INDEX IF NOT EXISTS "idx_trusted_devices_user_id" ON "trusted_devices"("user_id");
CREATE INDEX IF NOT EXISTS "idx_trusted_devices_fingerprint" ON "trusted_devices"("device_fingerprint");
CREATE INDEX IF NOT EXISTS "idx_trusted_devices_trusted" ON "trusted_devices"("is_trusted");

-- Insert default session policies for different roles
INSERT INTO "session_policies" (
  "company_id", "role", "policy_name", 
  "max_concurrent_sessions", "idle_timeout_seconds", "absolute_timeout_seconds",
  "require_mfa_for_new_device", "require_fresh_auth_for_sensitive"
) VALUES 
-- Admin policies (stricter)
(NULL, 'admin', 'Admin Security Policy', 3, 1800, 28800, true, true),
(NULL, 'super_admin', 'Super Admin Security Policy', 2, 1800, 14400, true, true),

-- Manager policies (moderate)
(NULL, 'manager', 'Manager Security Policy', 4, 3600, 43200, true, true),
(NULL, 'branch_manager', 'Branch Manager Security Policy', 4, 3600, 43200, true, true),

-- Employee policies (standard)
(NULL, 'employee', 'Employee Security Policy', 5, 7200, 86400, false, false),
(NULL, 'loan_officer', 'Loan Officer Security Policy', 4, 3600, 43200, true, false),
(NULL, 'collection_agent', 'Collection Agent Security Policy', 3, 3600, 43200, false, false),

-- Customer service policies
(NULL, 'customer_service', 'Customer Service Security Policy', 3, 3600, 43200, false, false),

-- Auditor policies (very strict)
(NULL, 'auditor', 'Auditor Security Policy', 2, 1800, 14400, true, true);

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS void AS $$
BEGIN
  -- Update expired sessions
  UPDATE "user_sessions" 
  SET 
    "status" = 'expired',
    "terminated_at" = CURRENT_TIMESTAMP,
    "termination_reason" = 'Session expired'
  WHERE 
    "status" = 'active' 
    AND (
      "expires_at" < CURRENT_TIMESTAMP 
      OR "last_activity" + INTERVAL '1 second' * "idle_timeout" < CURRENT_TIMESTAMP
    );
    
  -- Log cleanup activity
  INSERT INTO "session_activity_logs" (
    "session_id", "user_id", "company_id", "activity_type", 
    "activity_description", "timestamp"
  )
  SELECT 
    s."session_id", s."user_id", s."company_id", 'timeout',
    'Session automatically expired due to timeout', CURRENT_TIMESTAMP
  FROM "user_sessions" s
  WHERE s."status" = 'expired' AND s."terminated_at" > CURRENT_TIMESTAMP - INTERVAL '1 minute';
END;
$$ LANGUAGE plpgsql;

-- Function to update session activity
CREATE OR REPLACE FUNCTION update_session_activity(p_session_id varchar, p_ip_address inet DEFAULT NULL)
RETURNS boolean AS $$
BEGIN
  UPDATE "user_sessions" 
  SET 
    "last_activity" = CURRENT_TIMESTAMP,
    "ip_address" = COALESCE(p_ip_address, "ip_address"),
    "updated_at" = CURRENT_TIMESTAMP
  WHERE 
    "session_id" = p_session_id 
    AND "status" = 'active';
    
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER update_user_sessions_updated_at 
  BEFORE UPDATE ON "user_sessions" 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_policies_updated_at 
  BEFORE UPDATE ON "session_policies" 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trusted_devices_updated_at 
  BEFORE UPDATE ON "trusted_devices" 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
