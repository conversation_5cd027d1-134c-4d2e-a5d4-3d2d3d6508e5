# Scripts Directory

This directory contains utility scripts, migration scripts, and development tools for the TrackFina application.

## Directory Structure

```
scripts/
├── migrations/          # Database migration scripts
├── utils/              # Shared utility modules
├── debug/              # Debug and troubleshooting scripts
└── README.md           # This file
```

## Shared Utilities

### `utils/env-loader.js`
Standardized environment variable loading and validation.

```javascript
import { setupEnvironment } from './utils/env-loader.js';

// Load and validate required environment variables
setupEnvironment(['DATABASE_URL', 'JWT_SECRET']);
```

### `utils/database-connection.js`
Database connection management with automatic cleanup.

```javascript
import { withDatabasePool, executeQuery } from './utils/database-connection.js';

// Execute operation with automatic connection management
await withDatabasePool(async (pool) => {
  const result = await pool.query('SELECT * FROM users');
  return result.rows;
});

// Simple query execution
const result = await executeQuery('SELECT COUNT(*) FROM loans');
```

### `utils/migration-runner.js`
Standardized migration execution with error handling and logging.

```javascript
import { runMigration } from './utils/migration-runner.js';

await runMigration('Update Loan Reference Codes', async (pool, { dryRun }) => {
  if (dryRun) {
    console.log('Would update loan reference codes');
    return;
  }
  
  const result = await pool.query('UPDATE loans SET reference_code = ?');
  console.log(`Updated ${result.rowCount} loans`);
});
```

## Usage Guidelines

### Creating New Migration Scripts

1. Use the migration template:
```javascript
#!/usr/bin/env node
/**
 * Migration Script: [Description]
 * Purpose: [What this script does]
 * Usage: node scripts/migrations/[script-name].js
 */

import { runMigration } from '../utils/migration-runner.js';

await runMigration('Migration Name', async (pool, { dryRun }) => {
  // Migration logic here
  if (dryRun) {
    console.log('Would perform migration actions');
    return;
  }
  
  // Actual migration code
});
```

2. Place in appropriate subdirectory:
   - `migrations/` for database schema changes
   - `debug/` for troubleshooting tools
   - Root `scripts/` for general utilities

### Running Scripts

All scripts should be run from the project root directory:

```bash
# Run a migration script
node scripts/migrations/update-loan-reference-codes.js

# Run in dry-run mode (if supported)
node scripts/migrations/update-loan-reference-codes.js --dry-run

# Run a debug script
node scripts/debug/check-company-context.js
```

### Best Practices

1. **Use ES Modules**: All scripts should use `import/export` syntax
2. **Error Handling**: Use the migration runner for consistent error handling
3. **Logging**: Provide clear, descriptive log messages
4. **Documentation**: Include purpose and usage in script headers
5. **Validation**: Validate prerequisites before making changes
6. **Dry Run**: Support dry-run mode for testing when possible

### Environment Variables

Scripts automatically load environment variables from `.env` file. Required variables:

- `DATABASE_URL`: Database connection string
- Additional variables as needed by specific scripts

### Error Handling

All scripts use standardized error handling:
- Automatic database connection cleanup
- Detailed error logging with stack traces
- Proper exit codes for CI/CD integration
- Clear success/failure messages

## Migration Scripts

Migration scripts in the `migrations/` directory handle database schema changes and data updates. They follow a standardized pattern for consistency and reliability.

## Debug Scripts

Debug scripts in the `debug/` directory help troubleshoot issues and provide diagnostic information. They are designed to be safe to run in any environment.

## Contributing

When adding new scripts:

1. Follow the established patterns and templates
2. Use the shared utilities for common operations
3. Add appropriate documentation and comments
4. Test scripts in development environment first
5. Update this README if adding new categories or utilities
