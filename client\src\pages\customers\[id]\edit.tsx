
import React from "react";
import { useParams, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useContextData } from "@/lib/useContextData";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { DirectCustomerForm } from "@/components/customer/DirectCustomerForm";

export default function EditCustomerPage() {
  const { id } = useParams();
  const customerId = parseInt(id, 10);
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const { companyId } = useContextData();
  const [, navigate] = useLocation();

  const { data: customer, isLoading } = useQuery({
    queryKey: [`/api/customers/${customerId}?companyId=${companyId}`],
    queryFn: async () => {
      if (!customerId) throw new Error("Customer ID is required");

      console.log(`Fetching customer ${customerId} with company context ${companyId}`);
      const response = await apiRequest('GET', `/api/customers/${customerId}?companyId=${companyId}`);
      return await response.json();
    },
    enabled: !!customerId && !!companyId,
  });

  const handleCustomerUpdated = (customer: any) => {
    if (customer && customer.id) {
      navigate(`/customers/${customer.id}`);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!customer) {
    return <div>Customer not found</div>;
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => navigate(`/customers/${customerId}`)}
            className="text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Customer
          </Button>
          <h1 className="text-2xl font-bold">
            Edit Customer
          </h1>
        </div>
      </div>

      <DirectCustomerForm
        companyId={companyId}
        onSuccess={handleCustomerUpdated}
        onCancel={() => navigate(`/customers/${customerId}`)}
        initialData={customer}
        isEdit={true}
        customerId={customerId}
      />
    </div>
  );
}
