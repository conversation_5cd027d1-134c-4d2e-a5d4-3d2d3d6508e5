
import { useLocation } from "wouter";
import { Helmet } from "react-helmet";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ArrowRight,
  CheckCircle,
  BarChart3,
  Users,
  Calendar,
  Shield,
  CreditCard,
  Layers,
  Search,
  Menu,
  Clock,
  User,
  ChevronDown,
  IndianRupee,
  Wallet,
  Coins,
  DollarSign,
  BanknoteIcon,
  PiggyBank,
  Receipt,
  Building,
  FileText,
  Globe,
  Lock,
  Mail,
  Phone
} from "lucide-react";

export default function Landing() {
  const [, navigate] = useLocation();

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-background to-background/80 overflow-hidden relative">
      {/* Enhanced SEO with comprehensive meta tags */}
      <Helmet>
        <title>TrackFina | Comprehensive Loan Management Software</title>
        <meta name="description" content="TrackFina is a powerful SaaS platform for financial institutions to manage loans, collections, and customer relationships with ease." />
        <meta name="keywords" content="loan management, financial software, collection management, loan tracking, financial institution software, loan portfolio management" />
        <meta name="author" content="TrackFina" />
        <meta name="robots" content="index, follow" />
        <meta name="revisit-after" content="7 days" />
        <meta name="language" content="English" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />

        {/* Open Graph tags for social media sharing */}
        <meta property="og:title" content="TrackFina | Comprehensive Loan Management Software" />
        <meta property="og:description" content="TrackFina is a powerful SaaS platform for financial institutions to manage loans, collections, and customer relationships with ease." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://trackfina.com" />
        <meta property="og:image" content="https://trackfina.com/og-image.jpg" />

        {/* Twitter Card tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="TrackFina | Comprehensive Loan Management Software" />
        <meta name="twitter:description" content="TrackFina is a powerful SaaS platform for financial institutions to manage loans, collections, and customer relationships with ease." />
        <meta name="twitter:image" content="https://trackfina.com/twitter-image.jpg" />

        {/* Canonical URL */}
        <link rel="canonical" href="https://trackfina.com" />

        {/* Structured Data (JSON-LD) */}
        <script type="application/ld+json">
          {`
            {
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "TrackFina",
              "applicationCategory": "BusinessApplication",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "9999",
                "priceCurrency": "INR"
              },
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "127"
              },
              "description": "TrackFina is a comprehensive loan management software for financial institutions."
            }
          `}
        </script>
        <script type="application/ld+json">
          {`
            {
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "TrackFina",
              "url": "https://trackfina.com",
              "logo": "https://trackfina.com/logo.png",
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+91-9876543210",
                "contactType": "customer service"
              },
              "sameAs": [
                "https://facebook.com/trackfina",
                "https://twitter.com/trackfina",
                "https://linkedin.com/company/trackfina"
              ]
            }
          `}
        </script>
      </Helmet>
      {/* Enhanced background elements */}
      <div className="absolute inset-0 z-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/20 via-background to-background"></div>
      <div className="absolute inset-0 z-0 opacity-5 bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:24px_24px]"></div>

      {/* Enhanced Money-themed background pattern with more financial elements */}
      <div className="absolute inset-0 z-0 opacity-[0.04] pointer-events-none">
        {/* Financial icons with improved positioning and animations */}
        <div className="absolute top-1/4 left-1/4 h-32 w-32 animate-float" style={{ animationDuration: '15s' }}>
          <div className="h-full w-full bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
            <IndianRupee className="h-3/4 w-3/4 text-blue-900" />
          </div>
        </div>
        <div className="absolute top-1/3 right-1/4 h-40 w-40 animate-float" style={{ animationDuration: '18s', animationDelay: '2s' }}>
          <div className="h-full w-full bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center">
            <Coins className="h-3/4 w-3/4 text-purple-900" />
          </div>
        </div>
        <div className="absolute bottom-1/4 left-1/3 h-36 w-36 animate-float" style={{ animationDuration: '20s', animationDelay: '1s' }}>
          <div className="h-full w-full bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center">
            <Wallet className="h-3/4 w-3/4 text-green-900" />
          </div>
        </div>
        <div className="absolute top-2/3 right-1/3 h-28 w-28 animate-float" style={{ animationDuration: '17s', animationDelay: '3s' }}>
          <div className="h-full w-full bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
            <CreditCard className="h-3/4 w-3/4 text-blue-900" />
          </div>
        </div>
        <div className="absolute bottom-1/3 right-1/5 h-24 w-24 animate-float" style={{ animationDuration: '19s', animationDelay: '4s' }}>
          <div className="h-full w-full bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center">
            <Receipt className="h-3/4 w-3/4 text-purple-900" />
          </div>
        </div>
        <div className="absolute top-1/2 left-1/6 h-28 w-28 animate-float" style={{ animationDuration: '16s', animationDelay: '2.5s' }}>
          <div className="h-full w-full bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center">
            <BanknoteIcon className="h-3/4 w-3/4 text-green-900" />
          </div>
        </div>
        <div className="absolute bottom-1/5 right-1/4 h-32 w-32 animate-float" style={{ animationDuration: '21s', animationDelay: '1.5s' }}>
          <div className="h-full w-full bg-gradient-to-br from-amber-100 to-amber-200 rounded-full flex items-center justify-center">
            <PiggyBank className="h-3/4 w-3/4 text-amber-900" />
          </div>
        </div>
        <div className="absolute top-1/5 right-1/6 h-36 w-36 animate-spin-slow" style={{ animationDuration: '30s' }}>
          <div className="h-full w-full bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
            <IndianRupee className="h-3/4 w-3/4 text-blue-900" />
          </div>
        </div>
      </div>

      {/* Enhanced colorful decorative elements with improved gradients */}
      <div className="absolute top-[20%] left-[10%] w-72 h-72 bg-gradient-to-br from-blue-500/15 to-blue-600/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute top-[40%] right-[15%] w-80 h-80 bg-gradient-to-br from-purple-500/15 to-purple-600/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
      <div className="absolute bottom-[10%] left-[20%] w-96 h-96 bg-gradient-to-br from-emerald-500/15 to-emerald-600/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      <div className="absolute top-[60%] right-[5%] w-64 h-64 bg-gradient-to-br from-amber-500/15 to-amber-600/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
      <div className="absolute top-[30%] left-[30%] w-48 h-48 bg-gradient-to-br from-green-500/10 to-green-600/5 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }}></div>

      {/* Enhanced Top Navigation Bar */}
      <div className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center">
          <div
            className="flex items-center space-x-3 cursor-pointer"
            onClick={() => {
              // Check if user is already logged in
              const token = localStorage.getItem("auth_token");
              if (token) {
                console.log("User already logged in, navigating directly to dashboard from logo...");
                navigate("/dashboard");
              } else {
                // If not logged in, navigate to the landing page (which is already loaded)
                console.log("User not logged in, staying on landing page...");
              }
            }}
          >
            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg border border-white/20 group-hover:scale-110 transition-transform duration-300">
              <svg className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                  stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-blue-500">TrackFina</h1>
          </div>

          <div className="flex-1 flex justify-center px-2">
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-sm font-medium relative group">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400 group-hover:from-purple-600 group-hover:to-purple-400 transition-all duration-300">Features</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#pricing" className="text-sm font-medium relative group">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400 group-hover:from-purple-600 group-hover:to-purple-400 transition-all duration-300">Pricing</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#testimonials" className="text-sm font-medium relative group">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400 group-hover:from-purple-600 group-hover:to-purple-400 transition-all duration-300">Testimonials</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#contact" className="text-sm font-medium relative group">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-blue-400 group-hover:from-purple-600 group-hover:to-purple-400 transition-all duration-300">Contact</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
            </nav>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => {
                // Check if user is already logged in
                const token = localStorage.getItem("auth_token");
                if (token) {
                  console.log("User already logged in, navigating directly to dashboard from nav bar...");
                  navigate("/dashboard");
                } else {
                  console.log("Navigating to login page from nav bar...");
                  navigate("/login");
                }
              }}
              className="hidden sm:flex hover:bg-blue-50 hover:text-blue-600 transition-all duration-300"
            >
              Login
            </Button>
            <Button
              onClick={() => {
                console.log("Navigating to register page from nav bar...");
                window.location.href = "/register";
              }}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              Get Started
            </Button>
            <Button variant="ghost" size="icon" className="md:hidden hover:bg-blue-50">
              <Menu className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Hero Section with improved visual design */}
      <header className="relative z-10 w-full max-w-7xl mx-auto px-4 pt-2 md:pt-4">
        <div className="flex flex-col md:flex-row items-center justify-between py-4 md:py-6 gap-6">
          <div className="flex-1 md:flex-none md:w-[40%] space-y-6 text-center md:text-left relative">
            {/* Enhanced decorative elements */}
            <div className="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-blue-600/10 rounded-full blur-xl animate-pulse-slow" aria-hidden="true"></div>
            <div className="absolute -bottom-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-purple-600/10 rounded-full blur-xl animate-pulse-slow" style={{ animationDelay: '1s' }} aria-hidden="true"></div>

            {/* Financial icon decoration */}
            <div className="absolute -top-5 -right-5 w-20 h-20 opacity-10">
              <IndianRupee className="h-full w-full text-blue-900" />
            </div>

            <div className="relative">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100/80 to-purple-100/80 text-blue-700 text-sm font-medium mb-4 shadow-md border border-blue-200/50">
                <div className="h-6 w-6 rounded-full bg-blue-600 flex items-center justify-center mr-2 shadow-sm">
                  <IndianRupee className="h-3.5 w-3.5 text-white" aria-hidden="true" />
                </div>
                <span>Financial Management Simplified</span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-tight">
                <span className="block mb-2">Complete Loan</span>
                <span className="block mb-2">Management</span>
                <span className="relative">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-blue-500">Made Simple</span>
                  <span className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-blue-600/30 via-purple-600/30 to-blue-500/30 rounded-full"></span>
                </span>
              </h1>
            </div>

            <p className="text-base md:text-lg text-gray-600 max-w-xl leading-relaxed">
              TrackFina is a comprehensive SaaS platform for financial institutions to manage loans, collections, and customer relationships with ease and efficiency.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start pt-2">
              <Button
                size="lg"
                onClick={() => {
                  console.log("Navigating to register page from hero section...");
                  window.location.href = "/register";
                }}
                className="gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 h-14 px-8 rounded-lg text-base"
              >
                Get Started <ArrowRight size={18} />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => {
                  // Check if user is already logged in
                  const token = localStorage.getItem("auth_token");
                  if (token) {
                    console.log("User already logged in, navigating directly to dashboard...");
                    navigate("/dashboard");
                  } else {
                    console.log("Navigating to login page from hero section...");
                    navigate("/login");
                  }
                }}
                className="border-2 border-blue-200 hover:border-blue-300 text-blue-600 hover:bg-blue-50 transition-all duration-300 h-14 px-8 rounded-lg text-base"
              >
                Login to Dashboard
              </Button>
            </div>

            {/* Enhanced Trust badges */}
            <div className="pt-6">
              <p className="text-sm text-gray-600 mb-3 font-medium">Trusted by financial institutions</p>
              <div className="flex flex-wrap justify-center md:justify-start gap-5">
                <div className="h-8 w-20 bg-gradient-to-r from-gray-200 to-gray-100 rounded-md shadow-sm border border-gray-300/30 flex items-center justify-center">
                  <Building className="h-4 w-4 text-gray-500" />
                </div>
                <div className="h-8 w-24 bg-gradient-to-r from-gray-200 to-gray-100 rounded-md shadow-sm border border-gray-300/30 flex items-center justify-center">
                  <Building className="h-4 w-4 text-gray-500 mr-1" />
                  <span className="text-xs text-gray-500 font-medium">Finance</span>
                </div>
                <div className="h-8 w-20 bg-gradient-to-r from-gray-200 to-gray-100 rounded-md shadow-sm border border-gray-300/30 flex items-center justify-center">
                  <Building className="h-4 w-4 text-gray-500" />
                </div>
                <div className="h-8 w-28 bg-gradient-to-r from-gray-200 to-gray-100 rounded-md shadow-sm border border-gray-300/30 flex items-center justify-center">
                  <Building className="h-4 w-4 text-gray-500 mr-1" />
                  <span className="text-xs text-gray-500 font-medium">Loans Inc</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex-1 md:flex-none md:w-[60%] relative">
            <div className="relative w-full max-w-2xl mx-auto">
              {/* Enhanced dashboard mockup with animations and effects */}
              <div
                className="bg-card rounded-2xl shadow-[0_25px_60px_rgba(8,112,184,0.25)] border border-blue-100 overflow-hidden"
              >
                {/* Removed animated glow effect for Task 4 */}

                {/* Floating money elements */}
                <div className="absolute -top-6 -right-6 h-12 w-12 bg-green-100 rounded-full flex items-center justify-center shadow-lg border border-green-200 animate-float z-10">
                  <IndianRupee className="h-6 w-6 text-green-600" />
                </div>
                <div className="absolute top-1/4 -left-4 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center shadow-md border border-blue-200 animate-float-delayed z-10">
                  <CreditCard className="h-4 w-4 text-blue-600" />
                </div>
                <div className="absolute bottom-1/3 -right-3 h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center shadow-md border border-purple-200 animate-float-slow z-10">
                  <Wallet className="h-5 w-5 text-purple-600" />
                </div>

                {/* Top navigation bar */}
                <div className="flex items-center bg-gradient-to-r from-blue-900 to-blue-800 text-white p-3 justify-between relative z-10">
                  <div className="flex items-center space-x-2">
                    <div className="h-7 w-7 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center shadow-inner">
                      <svg className="h-4 w-4 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                          stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    <span className="text-sm font-bold tracking-wide">TrackFina</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                        <Search className="h-3 w-3 text-white/70" />
                      </div>
                      <input type="text" className="h-7 w-28 bg-blue-800/50 border border-blue-700 rounded-md text-[10px] pl-6 pr-2 placeholder-white/50 focus:outline-none focus:ring-1 focus:ring-blue-400" placeholder="Search..." />
                    </div>
                    <div className="h-7 w-7 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center text-[10px] font-bold shadow-md">
                      VP
                    </div>
                  </div>
                </div>

                {/* Sidebar and content */}
                <div className="flex h-[300px]">
                  {/* Sidebar */}
                  <div className="w-[90px] bg-gradient-to-b from-blue-900 to-blue-950 text-white p-3 flex flex-col items-center space-y-5">
                    <div
                      className="flex flex-col items-center"
                    >
                      <div className="h-6 w-6 rounded-md bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mb-1 shadow-md">
                        <BarChart3 className="h-3 w-3 text-white" />
                      </div>
                      <span className="text-[8px] text-white/90">Dashboard</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-6 w-6 rounded-md bg-white/10 flex items-center justify-center mb-1">
                        <Calendar className="h-3 w-3 text-white/80" />
                      </div>
                      <span className="text-[8px] text-white/70">Collections</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-6 w-6 rounded-md bg-white/10 flex items-center justify-center mb-1">
                        <Users className="h-3 w-3 text-white/80" />
                      </div>
                      <span className="text-[8px] text-white/70">Agents</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-6 w-6 rounded-md bg-white/10 flex items-center justify-center mb-1">
                        <User className="h-3 w-3 text-white/80" />
                      </div>
                      <span className="text-[8px] text-white/70">Customers</span>
                    </div>
                  </div>

                  {/* Main content */}
                  <div className="flex-1 p-3 bg-gradient-to-br from-gray-50 to-blue-50">
                    <div className="flex justify-between items-center mb-2">
                      <div>
                        <h3 className="text-sm font-bold text-gray-800">Dashboard</h3>
                        <p className="text-[10px] text-blue-600">Welcome back, Vetrivel D</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="text-[10px] bg-white border border-blue-100 rounded-md px-2 py-1 flex items-center shadow-sm">
                          Last 30 Days <ChevronDown className="h-3 w-3 ml-1 text-blue-500" />
                        </div>
                        <div className="text-[10px] bg-gradient-to-r from-blue-600 to-blue-500 text-white rounded-md px-3 py-1 shadow-sm">
                          Export
                        </div>
                      </div>
                    </div>

                    {/* Metric cards */}
                    <div className="grid grid-cols-4 gap-2 mb-2">
                      <div className="bg-white rounded-lg p-2 border border-blue-100 shadow-sm">
                        <div className="flex items-center mb-1">
                          <div className="h-6 w-6 rounded-md bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-2 shadow-sm">
                            <span className="text-[10px] text-white font-bold">₹</span>
                          </div>
                          <span className="text-[10px] text-gray-600">Total Collections</span>
                        </div>
                        <div className="text-sm font-bold text-gray-800">₹24.5M</div>
                      </div>
                      <div className="bg-white rounded-lg p-2 border border-blue-100 shadow-sm">
                        <div className="flex items-center mb-1">
                          <div className="h-6 w-6 rounded-md bg-gradient-to-br from-amber-500 to-amber-600 flex items-center justify-center mr-2 shadow-sm">
                            <Clock className="h-3 w-3 text-white" />
                          </div>
                          <span className="text-[10px] text-gray-600">Pending Collections</span>
                        </div>
                        <div className="text-sm font-bold text-gray-800">₹3.8M</div>
                      </div>
                      <div className="bg-white rounded-lg p-2 border border-blue-100 shadow-sm">
                        <div className="flex items-center mb-1">
                          <div className="h-6 w-6 rounded-md bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center mr-2 shadow-sm">
                            <Users className="h-3 w-3 text-white" />
                          </div>
                          <span className="text-[10px] text-gray-600">Active Agents</span>
                        </div>
                        <div className="text-sm font-bold text-gray-800">42</div>
                      </div>
                      <div className="bg-white rounded-lg p-2 border border-blue-100 shadow-sm">
                        <div className="flex items-center mb-1">
                          <div className="h-6 w-6 rounded-md bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mr-2 shadow-sm">
                            <User className="h-3 w-3 text-white" />
                          </div>
                          <span className="text-[10px] text-gray-600">Total Customers</span>
                        </div>
                        <div className="text-sm font-bold text-gray-800">1,248</div>
                      </div>
                    </div>

                    {/* Collection trends chart */}
                    <div className="bg-white rounded-lg p-3 border border-blue-100 shadow-sm">
                      <div className="flex justify-between items-center mb-3">
                        <div>
                          <h4 className="text-xs font-bold text-gray-800">Collection Trends</h4>
                          <p className="text-[8px] text-gray-500">Daily collection amounts for the last 30 days</p>
                        </div>
                        <div className="text-[8px] bg-white border border-blue-100 rounded-md px-2 py-1 flex items-center shadow-sm">
                          Last 30 Days <ChevronDown className="h-2 w-2 ml-1 text-blue-500" />
                        </div>
                      </div>
                      <div className="h-[100px] w-full relative">
                        {/* Enhanced chart representation */}
                        <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-gray-200"></div>
                        <div className="absolute left-0 bottom-0 top-0 w-[1px] bg-gray-200"></div>

                        {/* X-axis labels */}
                        <div className="absolute bottom-[-12px] left-0 text-[6px] text-gray-400">22 Apr</div>
                        <div className="absolute bottom-[-12px] left-[20%] text-[6px] text-gray-400">1 May</div>
                        <div className="absolute bottom-[-12px] left-[40%] text-[6px] text-gray-400">8 May</div>
                        <div className="absolute bottom-[-12px] left-[60%] text-[6px] text-gray-400">15 May</div>
                        <div className="absolute bottom-[-12px] left-[80%] text-[6px] text-gray-400">22 May</div>

                        {/* Y-axis labels */}
                        <div className="absolute top-0 left-[-12px] text-[6px] text-gray-400">₹3k</div>
                        <div className="absolute top-[33%] left-[-12px] text-[6px] text-gray-400">₹2k</div>
                        <div className="absolute top-[66%] left-[-12px] text-[6px] text-gray-400">₹1k</div>
                        <div className="absolute bottom-0 left-[-12px] text-[6px] text-gray-400">₹0</div>

                        {/* Chart bars with animation */}
                        <div className="absolute bottom-0 left-[5%] w-[2%] h-[20%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[10%] w-[2%] h-[15%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[15%] w-[2%] h-[25%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[20%] w-[2%] h-[30%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[25%] w-[2%] h-[20%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[30%] w-[2%] h-[40%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[35%] w-[2%] h-[35%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[40%] w-[2%] h-[25%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[45%] w-[2%] h-[30%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[50%] w-[2%] h-[45%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[55%] w-[2%] h-[50%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[60%] w-[2%] h-[40%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[65%] w-[2%] h-[35%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[70%] w-[2%] h-[45%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[75%] w-[2%] h-[55%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[80%] w-[2%] h-[60%] bg-blue-200 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[85%] w-[2%] h-[75%] bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm"></div>
                        <div className="absolute bottom-0 left-[90%] w-[2%] h-[90%] bg-gradient-to-t from-blue-600 to-blue-400 rounded-t-sm"></div>

                        {/* Trend line */}
                        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                          <path d="M5,80 L10,85 L15,75 L20,70 L25,80 L30,60 L35,65 L40,75 L45,70 L50,55 L55,50 L60,60 L65,65 L70,55 L75,45 L80,40 L85,25 L90,10"
                            fill="none"
                            stroke="rgba(59, 130, 246, 0.5)"
                            strokeWidth="1"
                            strokeLinecap="round"
                            strokeLinejoin="round" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Removed decorative elements for Task 4 */}
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section id="features" className="relative z-10 py-6 md:py-10 bg-gradient-to-b from-background/50 to-background overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-12 bg-gradient-to-b from-blue-50/50 to-transparent" aria-hidden="true"></div>
        <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-t from-blue-50/50 to-transparent" aria-hidden="true"></div>
        <div className="absolute top-[20%] right-[10%] w-48 h-48 bg-purple-500/5 rounded-full blur-3xl" aria-hidden="true"></div>
        <div className="absolute bottom-[20%] left-[10%] w-48 h-48 bg-blue-500/5 rounded-full blur-3xl" aria-hidden="true"></div>

        <div className="max-w-7xl mx-auto px-4 relative">
          <header className="text-center mb-6">
            <div className="inline-block mb-1">
              <span className="inline-block px-3 py-1 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-600 text-xs font-medium shadow-sm">
                <span>Powerful Features</span>
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600">
              Comprehensive Loan Management
            </h2>
            <p className="text-base text-gray-600 max-w-2xl mx-auto">
              Everything you need to manage your financial institution's loan operations in one powerful platform
            </p>
          </header>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
            {/* Feature 1 - Loan Processing */}
            <div className="group relative">
              {/* Animated background glow on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-0 group-hover:opacity-20 transition duration-500"></div>

              <Card className="border border-blue-100 bg-white shadow-md hover:shadow-xl transition-all duration-500 relative overflow-hidden h-full">
                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-b from-blue-500/5 to-transparent rounded-bl-full"></div>

                {/* Money-themed decorative elements */}
                <div className="absolute bottom-4 right-4 opacity-5 w-20 h-20">
                  <IndianRupee className="w-full h-full text-blue-900 animate-spin-slow" />
                </div>

                <CardHeader className="pb-2">
                  <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mb-2 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110">
                    <CreditCard className="h-5 w-5 text-white" />
                  </div>
                  <CardTitle className="text-lg text-gray-800 group-hover:text-blue-600 transition-colors duration-300">Loan Processing</CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    Create and manage loans with customizable templates and automated calculations
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-1">
                  <ul className="space-y-1">
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-xs text-gray-600">Customizable loan templates</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-xs text-gray-600">Automated interest calculations</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-xs text-gray-600">Payment schedule generation</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Feature 2 - Collections Management */}
            <div className="group relative">
              {/* Animated background glow on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-500 to-amber-600 rounded-2xl blur opacity-0 group-hover:opacity-20 transition duration-500"></div>

              <Card className="border border-amber-100 bg-white shadow-md hover:shadow-xl transition-all duration-500 relative overflow-hidden h-full">
                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-b from-amber-500/5 to-transparent rounded-bl-full"></div>

                <CardHeader className="pb-3">
                  <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-amber-500 to-amber-600 flex items-center justify-center mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-lg text-gray-800 group-hover:text-amber-600 transition-colors duration-300">Collections Management</CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    Track payments, manage collections, and generate receipts
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-amber-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-amber-600" />
                      </div>
                      <span className="text-xs text-gray-600">Payment tracking and processing</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-amber-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-amber-600" />
                      </div>
                      <span className="text-xs text-gray-600">Collection scheduling</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-amber-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-amber-600" />
                      </div>
                      <span className="text-xs text-gray-600">Receipt generation</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Feature 3 - Customer Management */}
            <div className="group relative">
              {/* Animated background glow on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl blur opacity-0 group-hover:opacity-20 transition duration-500"></div>

              <Card className="border border-purple-100 bg-white shadow-md hover:shadow-xl transition-all duration-500 relative overflow-hidden h-full">
                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-b from-purple-500/5 to-transparent rounded-bl-full"></div>

                <CardHeader className="pb-3">
                  <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-lg text-gray-800 group-hover:text-purple-600 transition-colors duration-300">Customer Management</CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    Manage customer information and loan history
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-purple-600" />
                      </div>
                      <span className="text-xs text-gray-600">Customer information storage</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-purple-600" />
                      </div>
                      <span className="text-xs text-gray-600">Advanced search capabilities</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-purple-600" />
                      </div>
                      <span className="text-xs text-gray-600">Loan history tracking</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Feature 4 - Reporting & Analytics */}
            <div className="group relative">
              {/* Animated background glow on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl blur opacity-0 group-hover:opacity-20 transition duration-500"></div>

              <Card className="border border-emerald-100 bg-white shadow-md hover:shadow-xl transition-all duration-500 relative overflow-hidden h-full">
                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-b from-emerald-500/5 to-transparent rounded-bl-full"></div>

                <CardHeader className="pb-3">
                  <div
                    className="h-12 w-12 rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110"
                  >
                    <BarChart3 className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle
                    className="text-lg text-gray-800 group-hover:text-emerald-600 transition-colors duration-300"
                  >
                    Reporting & Analytics
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    Generate reports and gain insights into your loan portfolio
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li
                      className="flex items-center gap-2"
                    >
                      <div className="h-5 w-5 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-emerald-600" />
                      </div>
                      <span className="text-xs text-gray-600">Dashboard metrics</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-emerald-600" />
                      </div>
                      <span className="text-xs text-gray-600">Collection analytics</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-emerald-600" />
                      </div>
                      <span className="text-xs text-gray-600">Financial reports</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Feature 5 - Multi-tenant Architecture */}
            <div className="group relative">
              {/* Animated background glow on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-2xl blur opacity-0 group-hover:opacity-20 transition duration-500"></div>

              <Card className="border border-indigo-100 bg-white shadow-md hover:shadow-xl transition-all duration-500 relative overflow-hidden h-full">
                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-b from-indigo-500/5 to-transparent rounded-bl-full"></div>

                <CardHeader className="pb-3">
                  <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 flex items-center justify-center mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110">
                    <Layers className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-lg text-gray-800 group-hover:text-indigo-600 transition-colors duration-300">Multi-tenant Architecture</CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    Support for multiple companies and branches
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-xs text-gray-600">Company isolation</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-xs text-gray-600">Branch management</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-indigo-600" />
                      </div>
                      <span className="text-xs text-gray-600">Company switching</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Feature 6 - Security & Access Control */}
            <div className="group relative">
              {/* Animated background glow on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl blur opacity-0 group-hover:opacity-20 transition duration-500"></div>

              <Card className="border border-blue-100 bg-white shadow-md hover:shadow-xl transition-all duration-500 relative overflow-hidden h-full">
                {/* Decorative corner accent */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-b from-blue-500/5 to-transparent rounded-bl-full"></div>

                <CardHeader className="pb-3">
                  <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 transform group-hover:scale-110">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-lg text-gray-800 group-hover:text-blue-700 transition-colors duration-300">Security & Access Control</CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    Secure your data with role-based access control
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-blue-700" />
                      </div>
                      <span className="text-xs text-gray-600">Role-based permissions</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-blue-700" />
                      </div>
                      <span className="text-xs text-gray-600">Data isolation</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                        <CheckCircle className="h-3 w-3 text-blue-700" />
                      </div>
                      <span className="text-xs text-gray-600">Secure authentication</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="relative z-10 py-6 md:py-10 bg-gradient-to-b from-background to-background/90">
        <div className="max-w-7xl mx-auto px-4">
          <header className="text-center mb-5">
            <h2 className="text-3xl md:text-4xl font-bold mb-2">Simple, Transparent Pricing</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Choose the plan that works best for your financial institution
            </p>
          </header>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-5 max-w-5xl mx-auto">
            {/* Starter Plan */}
            <Card className="border-primary/10 bg-card/50 backdrop-blur-sm relative overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
              {/* Money icon decoration */}
              <div className="absolute top-6 right-6 opacity-10">
                <Coins className="h-16 w-16 text-blue-600" />
              </div>
              <CardHeader>
                <CardTitle>Starter</CardTitle>
                <CardDescription>
                  Perfect for small financial institutions
                </CardDescription>
                <div className="mt-4 flex items-center">
                  <IndianRupee className="h-6 w-6 text-blue-600 mr-1" />
                  <span className="text-3xl font-bold">9,999</span>
                  <span className="text-muted-foreground ml-2">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Up to 500 loans</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">5 user accounts</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Basic reporting</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Email support</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full" variant="outline" onClick={() => navigate("/register")}>
                  Get Started
                </Button>
              </CardFooter>
            </Card>

            {/* Professional Plan */}
            <Card className="border-primary bg-card/50 backdrop-blur-sm relative overflow-hidden shadow-lg">
              <div className="absolute -top-6 -right-6 w-32 h-32 bg-primary/10 rounded-full"></div>
              <div className="absolute top-0 left-0 bg-primary text-primary-foreground text-xs font-medium px-3 py-1 rounded-br-lg">
                Most Popular
              </div>
              {/* Money icon decoration */}
              <div className="absolute top-6 right-6 opacity-10">
                <PiggyBank className="h-16 w-16 text-purple-600" />
              </div>
              <CardHeader>
                <CardTitle>Professional</CardTitle>
                <CardDescription>
                  Ideal for growing financial businesses
                </CardDescription>
                <div className="mt-4 flex items-center">
                  <IndianRupee className="h-6 w-6 text-purple-600 mr-1" />
                  <span className="text-3xl font-bold">24,999</span>
                  <span className="text-muted-foreground ml-2">/month</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Up to 2,000 loans</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">20 user accounts</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Advanced reporting</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Priority support</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">API access</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full" onClick={() => navigate("/register")}>
                  Get Started
                </Button>
              </CardFooter>
            </Card>

            {/* Enterprise Plan */}
            <Card className="border-primary/10 bg-card/50 backdrop-blur-sm relative overflow-hidden">
              <div className="absolute top-0 right-0 w-24 h-24 bg-primary/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
              {/* Money icon decoration */}
              <div className="absolute top-6 right-6 opacity-10">
                <BanknoteIcon className="h-16 w-16 text-green-600" />
              </div>
              <CardHeader>
                <CardTitle>Enterprise</CardTitle>
                <CardDescription>
                  For large financial institutions
                </CardDescription>
                <div className="mt-4 flex items-center">
                  <IndianRupee className="h-6 w-6 text-green-600 mr-1" />
                  <span className="text-3xl font-bold">Custom</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Unlimited loans</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Unlimited users</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Custom reporting</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">24/7 dedicated support</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-chart-2" />
                    <span className="text-sm">Custom integrations</span>
                  </li>
                </ul>
              </CardContent>
              <CardFooter>
                <Button className="w-full" variant="outline" onClick={() => navigate("/register")}>
                  Contact Sales
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="relative z-10 py-6 md:py-10 bg-gradient-to-b from-background/90 to-background">
        <div className="max-w-7xl mx-auto px-4">
          <header className="text-center mb-5">
            <h2 className="text-3xl md:text-4xl font-bold mb-2">What Our Customers Say</h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              Trusted by financial institutions across the country
            </p>
          </header>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
            {/* Testimonial 1 */}
            <Card className="border-primary/10 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4">
                <div className="flex flex-col items-center mb-3">
                  <div className="h-12 w-12 rounded-full bg-primary/10 mb-3"></div>
                  <div className="text-center">
                    <h3 className="font-medium text-sm">Rajesh Kumar</h3>
                    <p className="text-xs text-muted-foreground">CEO, Microfinance Solutions</p>
                  </div>
                </div>
                <p className="text-xs text-center italic">
                  "TrackFina has transformed how we manage our loan portfolio. The collections tracking feature has improved our recovery rates by 35% in just three months."
                </p>
              </CardContent>
            </Card>

            {/* Testimonial 2 */}
            <Card className="border-primary/10 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4">
                <div className="flex flex-col items-center mb-3">
                  <div className="h-12 w-12 rounded-full bg-primary/10 mb-3"></div>
                  <div className="text-center">
                    <h3 className="font-medium text-sm">Priya Sharma</h3>
                    <p className="text-xs text-muted-foreground">COO, Urban Credit</p>
                  </div>
                </div>
                <p className="text-xs text-center italic">
                  "The multi-tenant architecture allows us to manage multiple branches seamlessly. Our agents love the mobile interface and the real-time updates."
                </p>
              </CardContent>
            </Card>

            {/* Testimonial 3 */}
            <Card className="border-primary/10 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4">
                <div className="flex flex-col items-center mb-3">
                  <div className="h-12 w-12 rounded-full bg-primary/10 mb-3"></div>
                  <div className="text-center">
                    <h3 className="font-medium text-sm">Vikram Singh</h3>
                    <p className="text-xs text-muted-foreground">CTO, Fintech Innovations</p>
                  </div>
                </div>
                <p className="text-xs text-center italic">
                  "The reporting capabilities are exceptional. We can now make data-driven decisions that have significantly improved our operational efficiency."
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section id="contact" className="relative z-10 py-6 md:py-10 overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-background via-blue-50/30 to-background" aria-hidden="true"></div>
        <div className="absolute top-[20%] right-[10%] w-64 h-64 bg-blue-500/5 rounded-full blur-3xl" aria-hidden="true"></div>
        <div className="absolute bottom-[20%] left-[10%] w-64 h-64 bg-purple-500/5 rounded-full blur-3xl" aria-hidden="true"></div>

        <div className="max-w-5xl mx-auto px-4 relative">
          <Card className="border-blue-200 bg-white shadow-xl overflow-hidden relative">
            {/* Card glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur opacity-10 group-hover:opacity-20 transition duration-1000"></div>

            {/* Decorative corner elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-b from-blue-500/5 to-transparent rounded-bl-full"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-b from-purple-500/5 to-transparent rounded-tr-full"></div>

            {/* Money-themed decorative elements */}
            <div className="absolute top-10 right-10 h-16 w-16 opacity-10">
              <IndianRupee className="h-full w-full text-green-600 animate-spin-slow" />
            </div>
            <div className="absolute bottom-10 left-10 h-14 w-14 opacity-10">
              <Coins className="h-full w-full text-blue-600 animate-float" />
            </div>
            <div className="absolute top-1/2 right-1/4 h-12 w-12 opacity-10">
              <Wallet className="h-full w-full text-purple-600 animate-float-delayed" />
            </div>

            <CardContent className="p-5 md:p-6 relative">
              <div className="text-center space-y-2">
                <header>
                  <div className="inline-block mb-1">
                    <span className="inline-block px-3 py-1 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-600 text-sm font-medium shadow-sm">
                      Get Started Today
                    </span>
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600">
                    Ready to streamline your loan operations?
                  </h2>
                  <p className="text-base text-gray-600 max-w-2xl mx-auto">
                    Join thousands of financial institutions using TrackFina to manage their loan portfolios efficiently.
                  </p>
                </header>
                <div className="flex flex-col sm:flex-row gap-3 justify-center pt-2">
                  <Button
                    size="lg"
                    onClick={() => {
                      console.log("Navigating to register page from CTA section...");
                      window.location.href = "/register";
                    }}
                    className="gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 h-12 px-8 rounded-lg"
                  >
                    Get Started <ArrowRight size={16} />
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    onClick={() => {
                      // Check if user is already logged in
                      const token = localStorage.getItem("auth_token");
                      if (token) {
                        console.log("User already logged in, navigating directly to dashboard from CTA section...");
                        navigate("/dashboard");
                      } else {
                        console.log("Navigating to login page from CTA section...");
                        navigate("/login");
                      }
                    }}
                    className="border-2 border-blue-200 hover:border-blue-300 text-blue-600 hover:bg-blue-50 transition-all duration-300 h-12 px-8 rounded-lg"
                  >
                    Login to Dashboard
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Enhanced Footer with Improved Design */}
      <footer className="relative z-10 py-8 md:py-12 bg-gradient-to-b from-background via-blue-50/40 to-purple-50/40 mt-auto overflow-hidden">
        {/* Enhanced Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-blue-500/30" aria-hidden="true"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-full blur-3xl animate-pulse-slow" aria-hidden="true"></div>
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-emerald-500/10 via-blue-500/10 to-purple-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }} aria-hidden="true"></div>
        <div className="absolute bottom-1/3 left-2/3 w-48 h-48 bg-gradient-to-br from-amber-500/10 via-pink-500/10 to-amber-500/10 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }} aria-hidden="true"></div>

        <div className="max-w-7xl mx-auto px-4 relative">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 lg:gap-6 mb-6">
            <div className="col-span-1 md:col-span-3">
              <div className="bg-gradient-to-br from-blue-100/90 via-blue-50 to-purple-100/90 border border-blue-200/60 rounded-xl p-5 group hover:shadow-xl transition-all duration-300 relative overflow-hidden">
                {/* Enhanced decorative elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-b from-purple-500/15 to-transparent rounded-bl-full"></div>
                <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-b from-blue-500/15 to-transparent rounded-tr-full"></div>
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-48 h-48 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-full blur-xl"></div>

                {/* Money-themed decorative elements */}
                <div className="absolute top-6 right-6 opacity-5">
                  <IndianRupee className="h-16 w-16 text-blue-900" />
                </div>
                <div className="absolute bottom-6 left-6 opacity-5">
                  <Coins className="h-14 w-14 text-purple-900" />
                </div>

                <div
                  className="flex items-center space-x-3 mb-5 relative cursor-pointer"
                  onClick={() => {
                    // Check if user is already logged in
                    const token = localStorage.getItem("auth_token");
                    if (token) {
                      console.log("User already logged in, navigating directly to dashboard from footer logo...");
                      navigate("/dashboard");
                    } else {
                      // If not logged in, navigate to the landing page (which is already loaded)
                      console.log("User not logged in, staying on landing page...");
                    }
                  }}
                >
                  <div className="h-14 w-14 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center shadow-lg border border-white/20 transform group-hover:scale-110 transition-transform duration-300">
                    <svg className="h-7 w-7 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img">
                      <title>TrackFina Logo</title>
                      <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                        stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-purple-700">TrackFina</span>
                </div>

                <div className="space-y-4 relative">
                  <p className="text-sm text-gray-700 font-medium">
                    Comprehensive loan management software for financial institutions.
                  </p>

                  <div className="flex items-center space-x-5 py-2">
                    <a href="#" className="text-gray-600 hover:text-blue-700 transition-colors transform hover:scale-110 transition-transform duration-200 bg-white/70 p-2 rounded-full shadow-sm border border-blue-100/50">
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd"></path>
                      </svg>
                    </a>
                    <a href="#" className="text-gray-600 hover:text-blue-500 transition-colors transform hover:scale-110 transition-transform duration-200 bg-white/70 p-2 rounded-full shadow-sm border border-blue-100/50">
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                      </svg>
                    </a>
                    <a href="#" className="text-gray-600 hover:text-purple-700 transition-colors transform hover:scale-110 transition-transform duration-200 bg-white/70 p-2 rounded-full shadow-sm border border-blue-100/50">
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-span-1 md:col-span-3">
              <div className="bg-gradient-to-br from-emerald-100/90 via-blue-50 to-emerald-50/90 border border-emerald-200/60 rounded-xl p-5 group hover:shadow-xl transition-all duration-300 relative overflow-hidden h-full">
                {/* Enhanced decorative elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-b from-emerald-500/15 to-transparent rounded-bl-full"></div>
                <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-b from-blue-500/15 to-transparent rounded-tr-full"></div>

                {/* Money-themed decorative elements */}
                <div className="absolute top-6 right-6 opacity-5">
                  <PiggyBank className="h-16 w-16 text-emerald-900" />
                </div>
                <div className="absolute bottom-6 left-6 opacity-5">
                  <Wallet className="h-14 w-14 text-emerald-900" />
                </div>

                <h3 className="text-lg font-bold mb-4 text-emerald-800 flex items-center">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center mr-3 shadow-md border border-emerald-200/50">
                    <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-700 to-emerald-500">Product</span>
                </h3>

                <div className="space-y-3">
                  <a href="#features" className="flex items-center p-3 bg-white/80 rounded-lg border border-emerald-100 hover:bg-emerald-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center mr-3 shadow-md">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                      </svg>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Features</span>
                      <p className="text-xs text-gray-600">Explore our capabilities</p>
                    </div>
                  </a>

                  <a href="#pricing" className="flex items-center p-3 bg-white/80 rounded-lg border border-amber-100 hover:bg-amber-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-amber-400 to-amber-600 flex items-center justify-center mr-3 shadow-md">
                      <IndianRupee className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Pricing</span>
                      <p className="text-xs text-gray-600">Transparent plans</p>
                    </div>
                  </a>

                  <a href="#testimonials" className="flex items-center p-3 bg-white/80 rounded-lg border border-indigo-100 hover:bg-indigo-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-400 to-indigo-600 flex items-center justify-center mr-3 shadow-md">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Testimonials</span>
                      <p className="text-xs text-gray-600">Customer success stories</p>
                    </div>
                  </a>

                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-purple-100 hover:bg-purple-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center mr-3 shadow-md">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">FAQ</span>
                      <p className="text-xs text-gray-600">Common questions</p>
                    </div>
                  </a>
                </div>
              </div>
            </div>

            <div className="col-span-1 md:col-span-3">
              <div className="bg-gradient-to-br from-blue-100/90 via-blue-50 to-blue-50/90 border border-blue-200/60 rounded-xl p-5 group hover:shadow-xl transition-all duration-300 relative overflow-hidden h-full">
                {/* Enhanced decorative elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-b from-blue-500/15 to-transparent rounded-bl-full"></div>
                <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-b from-blue-500/15 to-transparent rounded-tr-full"></div>

                {/* Money-themed decorative elements */}
                <div className="absolute top-6 right-6 opacity-5">
                  <Building className="h-16 w-16 text-blue-900" />
                </div>
                <div className="absolute bottom-6 left-6 opacity-5">
                  <CreditCard className="h-14 w-14 text-blue-900" />
                </div>

                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center mr-3 shadow-md border border-blue-200/50">
                    <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-blue-500">Company</span>
                </h3>

                <div className="space-y-3">
                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-blue-100 hover:bg-blue-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center mr-3 shadow-md">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">About Us</span>
                      <p className="text-xs text-gray-600">Our mission and values</p>
                    </div>
                  </a>

                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-green-100 hover:bg-green-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center mr-3 shadow-md">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Careers</span>
                      <p className="text-xs text-gray-600">Join our growing team</p>
                    </div>
                  </a>

                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-pink-100 hover:bg-pink-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-pink-400 to-pink-600 flex items-center justify-center mr-3 shadow-md">
                      <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                      </svg>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Blog</span>
                      <p className="text-xs text-gray-600">Latest news and insights</p>
                    </div>
                  </a>

                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-cyan-100 hover:bg-cyan-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-cyan-400 to-cyan-600 flex items-center justify-center mr-3 shadow-md">
                      <Globe className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Global Presence</span>
                      <p className="text-xs text-gray-600">Serving clients worldwide</p>
                    </div>
                  </a>
                </div>
              </div>
            </div>

            <div className="col-span-1 md:col-span-3">
              <div className="bg-gradient-to-br from-purple-100/90 via-blue-50 to-purple-50/90 border border-purple-200/60 rounded-xl p-5 group hover:shadow-xl transition-all duration-300 relative overflow-hidden h-full">
                {/* Enhanced decorative elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-b from-purple-500/15 to-transparent rounded-bl-full"></div>
                <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-b from-blue-500/15 to-transparent rounded-tr-full"></div>

                {/* Money-themed decorative elements */}
                <div className="absolute top-6 right-6 opacity-5">
                  <FileText className="h-16 w-16 text-purple-900" />
                </div>
                <div className="absolute bottom-6 left-6 opacity-5">
                  <Lock className="h-14 w-14 text-purple-900" />
                </div>

                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center mr-3 shadow-md border border-purple-200/50">
                    <svg className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-700 to-purple-500">Legal</span>
                </h3>

                <div className="space-y-3">
                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-blue-100 hover:bg-blue-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center mr-3 shadow-md">
                      <Shield className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Privacy Policy</span>
                      <p className="text-xs text-gray-600">How we protect your data</p>
                    </div>
                  </a>

                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-purple-100 hover:bg-purple-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center mr-3 shadow-md">
                      <FileText className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Terms of Service</span>
                      <p className="text-xs text-gray-600">Rules for using our platform</p>
                    </div>
                  </a>

                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-amber-100 hover:bg-amber-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-amber-400 to-amber-600 flex items-center justify-center mr-3 shadow-md">
                      <Lock className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Security</span>
                      <p className="text-xs text-gray-600">Data protection measures</p>
                    </div>
                  </a>

                  <a href="#" className="flex items-center p-3 bg-white/80 rounded-lg border border-red-100 hover:bg-red-50/90 transition-colors group-hover:shadow-md">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center mr-3 shadow-md">
                      <Mail className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-800">Contact Support</span>
                      <p className="text-xs text-gray-600">Get help with your account</p>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="pt-6 mt-2">
            <div className="bg-gradient-to-r from-blue-100/80 via-white to-purple-100/80 border border-blue-200/60 rounded-xl p-5 shadow-md relative overflow-hidden">
              {/* Enhanced decorative elements */}
              <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-b from-blue-500/10 to-transparent rounded-bl-full"></div>
              <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-b from-purple-500/10 to-transparent rounded-tr-full"></div>

              {/* Money-themed decorative elements */}
              <div className="absolute top-6 right-6 opacity-5">
                <IndianRupee className="h-16 w-16 text-blue-900" />
              </div>
              <div className="absolute bottom-6 left-6 opacity-5">
                <Coins className="h-14 w-14 text-purple-900" />
              </div>

              <div className="flex flex-col md:flex-row justify-between items-center gap-4 relative">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center shadow-md border border-white/20 mr-3">
                    <svg className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                        stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div>
                    <span className="text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-purple-700">TrackFina</span>
                    <p className="text-xs text-gray-600">© 2023 All rights reserved.</p>
                  </div>
                </div>

                <div className="flex flex-wrap justify-center gap-4 md:gap-6">
                  <a href="#" className="flex items-center gap-2 text-sm text-gray-600 hover:text-blue-700 transition-colors bg-white/80 px-3 py-2 rounded-lg shadow-sm border border-blue-100/50 hover:shadow-md">
                    <span className="h-7 w-7 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                      <FileText className="h-3.5 w-3.5 text-white" />
                    </span>
                    Terms
                  </a>
                  <a href="#" className="flex items-center gap-2 text-sm text-gray-600 hover:text-purple-700 transition-colors bg-white/80 px-3 py-2 rounded-lg shadow-sm border border-purple-100/50 hover:shadow-md">
                    <span className="h-7 w-7 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
                      <Shield className="h-3.5 w-3.5 text-white" />
                    </span>
                    Privacy
                  </a>
                  <a href="#" className="flex items-center gap-2 text-sm text-gray-600 hover:text-emerald-700 transition-colors bg-white/80 px-3 py-2 rounded-lg shadow-sm border border-emerald-100/50 hover:shadow-md">
                    <span className="h-7 w-7 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                      <Mail className="h-3.5 w-3.5 text-white" />
                    </span>
                    Contact
                  </a>
                  <div className="flex items-center gap-2 text-sm text-gray-600 bg-white/80 px-3 py-2 rounded-lg shadow-sm border border-pink-100/50">
                    <span className="h-7 w-7 rounded-full bg-gradient-to-br from-pink-400 to-pink-600 flex items-center justify-center">
                      <span className="text-white text-xs">❤️</span>
                    </span>
                    Made for financial institutions
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
