import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Plus, X } from 'lucide-react';

interface ConfigProps {
  config: any;
  onUpdate: (updates: any) => void;
}

export function TimeConditionConfig({ config, onUpdate }: ConfigProps) {
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  const toggleDay = (day: string) => {
    const currentDays = config.days || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter((d: string) => d !== day)
      : [...currentDays, day];
    onUpdate({ days: newDays });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="start_time">Start Time</Label>
          <Input
            id="start_time"
            type="time"
            value={config.start_time || '09:00'}
            onChange={(e) => onUpdate({ start_time: e.target.value })}
          />
        </div>
        <div>
          <Label htmlFor="end_time">End Time</Label>
          <Input
            id="end_time"
            type="time"
            value={config.end_time || '17:00'}
            onChange={(e) => onUpdate({ end_time: e.target.value })}
          />
        </div>
      </div>

      <div>
        <Label>Allowed Days</Label>
        <div className="flex flex-wrap gap-2 mt-2">
          {days.map(day => (
            <Badge
              key={day}
              variant={config.days?.includes(day) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleDay(day)}
            >
              {day.charAt(0).toUpperCase() + day.slice(1)}
            </Badge>
          ))}
        </div>
      </div>

      <div>
        <Label htmlFor="timezone">Timezone</Label>
        <Select value={config.timezone || 'UTC'} onValueChange={(value) => onUpdate({ timezone: value })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="UTC">UTC</SelectItem>
            <SelectItem value="America/New_York">Eastern Time</SelectItem>
            <SelectItem value="America/Chicago">Central Time</SelectItem>
            <SelectItem value="America/Denver">Mountain Time</SelectItem>
            <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

export function LocationConditionConfig({ config, onUpdate }: ConfigProps) {
  const addIPRange = () => {
    const ranges = config.allowed_ip_ranges || [];
    onUpdate({ allowed_ip_ranges: [...ranges, ''] });
  };

  const updateIPRange = (index: number, value: string) => {
    const ranges = [...(config.allowed_ip_ranges || [])];
    ranges[index] = value;
    onUpdate({ allowed_ip_ranges: ranges });
  };

  const removeIPRange = (index: number) => {
    const ranges = config.allowed_ip_ranges || [];
    onUpdate({ allowed_ip_ranges: ranges.filter((_: any, i: number) => i !== index) });
  };

  const addCountry = (type: 'allowed' | 'blocked', country: string) => {
    if (!country) return;
    const key = type === 'allowed' ? 'allowed_countries' : 'blocked_countries';
    const countries = config[key] || [];
    if (!countries.includes(country)) {
      onUpdate({ [key]: [...countries, country] });
    }
  };

  const removeCountry = (type: 'allowed' | 'blocked', country: string) => {
    const key = type === 'allowed' ? 'allowed_countries' : 'blocked_countries';
    const countries = config[key] || [];
    onUpdate({ [key]: countries.filter((c: string) => c !== country) });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label>Allowed IP Ranges</Label>
        <div className="space-y-2 mt-2">
          {(config.allowed_ip_ranges || []).map((range: string, index: number) => (
            <div key={index} className="flex gap-2">
              <Input
                value={range}
                onChange={(e) => updateIPRange(index, e.target.value)}
                placeholder="***********/24"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeIPRange(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button variant="outline" size="sm" onClick={addIPRange}>
            <Plus className="h-4 w-4 mr-2" />
            Add IP Range
          </Button>
        </div>
      </div>

      <Separator />

      <div>
        <Label>Allowed Countries</Label>
        <div className="flex flex-wrap gap-2 mt-2">
          {(config.allowed_countries || []).map((country: string) => (
            <Badge key={country} variant="default" className="cursor-pointer">
              {country}
              <X
                className="h-3 w-3 ml-1"
                onClick={() => removeCountry('allowed', country)}
              />
            </Badge>
          ))}
        </div>
        <div className="flex gap-2 mt-2">
          <Input
            placeholder="Country code (e.g., US, CA)"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                addCountry('allowed', (e.target as HTMLInputElement).value.toUpperCase());
                (e.target as HTMLInputElement).value = '';
              }
            }}
          />
        </div>
      </div>

      <div>
        <Label>Blocked Countries</Label>
        <div className="flex flex-wrap gap-2 mt-2">
          {(config.blocked_countries || []).map((country: string) => (
            <Badge key={country} variant="destructive" className="cursor-pointer">
              {country}
              <X
                className="h-3 w-3 ml-1"
                onClick={() => removeCountry('blocked', country)}
              />
            </Badge>
          ))}
        </div>
        <div className="flex gap-2 mt-2">
          <Input
            placeholder="Country code (e.g., CN, RU)"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                addCountry('blocked', (e.target as HTMLInputElement).value.toUpperCase());
                (e.target as HTMLInputElement).value = '';
              }
            }}
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="require_vpn"
          checked={config.require_vpn || false}
          onCheckedChange={(checked) => onUpdate({ require_vpn: checked })}
        />
        <Label htmlFor="require_vpn">Require VPN Connection</Label>
      </div>
    </div>
  );
}

export function AmountConditionConfig({ config, onUpdate }: ConfigProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="min_amount">Minimum Amount</Label>
          <Input
            id="min_amount"
            type="number"
            value={config.min_amount || 0}
            onChange={(e) => onUpdate({ min_amount: parseFloat(e.target.value) || 0 })}
            min="0"
            step="0.01"
          />
        </div>
        <div>
          <Label htmlFor="max_amount">Maximum Amount</Label>
          <Input
            id="max_amount"
            type="number"
            value={config.max_amount || 10000}
            onChange={(e) => onUpdate({ max_amount: parseFloat(e.target.value) || 0 })}
            min="0"
            step="0.01"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="currency">Currency</Label>
        <Select value={config.currency || 'USD'} onValueChange={(value) => onUpdate({ currency: value })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="USD">USD - US Dollar</SelectItem>
            <SelectItem value="EUR">EUR - Euro</SelectItem>
            <SelectItem value="GBP">GBP - British Pound</SelectItem>
            <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
            <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

export function ApprovalConditionConfig({ config, onUpdate }: ConfigProps) {
  const addApproverRole = (role: string) => {
    if (!role) return;
    const roles = config.approver_roles || [];
    if (!roles.includes(role)) {
      onUpdate({ approver_roles: [...roles, role] });
    }
  };

  const removeApproverRole = (role: string) => {
    const roles = config.approver_roles || [];
    onUpdate({ approver_roles: roles.filter((r: string) => r !== role) });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Switch
          id="requires_approval"
          checked={config.requires_approval || false}
          onCheckedChange={(checked) => onUpdate({ requires_approval: checked })}
        />
        <Label htmlFor="requires_approval">Requires Approval</Label>
      </div>

      {config.requires_approval && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="approval_threshold">Approval Threshold</Label>
              <Input
                id="approval_threshold"
                type="number"
                value={config.approval_threshold || 25000}
                onChange={(e) => onUpdate({ approval_threshold: parseFloat(e.target.value) || 0 })}
                min="0"
                step="0.01"
              />
            </div>
            <div>
              <Label htmlFor="auto_approve_below">Auto-approve Below</Label>
              <Input
                id="auto_approve_below"
                type="number"
                value={config.auto_approve_below || 5000}
                onChange={(e) => onUpdate({ auto_approve_below: parseFloat(e.target.value) || 0 })}
                min="0"
                step="0.01"
              />
            </div>
          </div>

          <div>
            <Label>Approver Roles</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {(config.approver_roles || []).map((role: string) => (
                <Badge key={role} variant="default" className="cursor-pointer">
                  {role}
                  <X
                    className="h-3 w-3 ml-1"
                    onClick={() => removeApproverRole(role)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 mt-2">
              <Input
                placeholder="Role name (e.g., manager, director)"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addApproverRole((e.target as HTMLInputElement).value);
                    (e.target as HTMLInputElement).value = '';
                  }
                }}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export function DeviceConditionConfig({ config, onUpdate }: ConfigProps) {
  const deviceTypes = ['desktop', 'laptop', 'mobile', 'tablet'];

  const toggleDeviceType = (type: string, listType: 'allowed' | 'blocked') => {
    const key = listType === 'allowed' ? 'allowed_device_types' : 'blocked_device_types';
    const currentTypes = config[key] || [];
    const newTypes = currentTypes.includes(type)
      ? currentTypes.filter((t: string) => t !== type)
      : [...currentTypes, type];
    onUpdate({ [key]: newTypes });
  };

  return (
    <div className="space-y-4">
      <div>
        <Label>Allowed Device Types</Label>
        <div className="flex flex-wrap gap-2 mt-2">
          {deviceTypes.map(type => (
            <Badge
              key={type}
              variant={config.allowed_device_types?.includes(type) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleDeviceType(type, 'allowed')}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Badge>
          ))}
        </div>
      </div>

      <div>
        <Label>Blocked Device Types</Label>
        <div className="flex flex-wrap gap-2 mt-2">
          {deviceTypes.map(type => (
            <Badge
              key={type}
              variant={config.blocked_device_types?.includes(type) ? "destructive" : "outline"}
              className="cursor-pointer"
              onClick={() => toggleDeviceType(type, 'blocked')}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Badge>
          ))}
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="require_registered_device"
          checked={config.require_registered_device || false}
          onCheckedChange={(checked) => onUpdate({ require_registered_device: checked })}
        />
        <Label htmlFor="require_registered_device">Require Registered Device</Label>
      </div>

      <div>
        <Label htmlFor="max_devices_per_user">Max Devices Per User</Label>
        <Input
          id="max_devices_per_user"
          type="number"
          value={config.max_devices_per_user || 3}
          onChange={(e) => onUpdate({ max_devices_per_user: parseInt(e.target.value) || 1 })}
          min="1"
          max="10"
        />
      </div>
    </div>
  );
}

export function SessionConditionConfig({ config, onUpdate }: ConfigProps) {
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="max_session_age">Maximum Session Age (seconds)</Label>
        <Input
          id="max_session_age"
          type="number"
          value={config.max_session_age || 3600}
          onChange={(e) => onUpdate({ max_session_age: parseInt(e.target.value) || 3600 })}
          min="300"
          max="86400"
        />
        <p className="text-sm text-muted-foreground mt-1">
          Current: {formatDuration(config.max_session_age || 3600)}
        </p>
      </div>

      <div>
        <Label htmlFor="max_idle_time">Maximum Idle Time (seconds)</Label>
        <Input
          id="max_idle_time"
          type="number"
          value={config.max_idle_time || 1800}
          onChange={(e) => onUpdate({ max_idle_time: parseInt(e.target.value) || 1800 })}
          min="60"
          max="7200"
        />
        <p className="text-sm text-muted-foreground mt-1">
          Current: {formatDuration(config.max_idle_time || 1800)}
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="require_mfa"
          checked={config.require_mfa || false}
          onCheckedChange={(checked) => onUpdate({ require_mfa: checked })}
        />
        <Label htmlFor="require_mfa">Require Multi-Factor Authentication</Label>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="require_fresh_auth"
          checked={config.require_fresh_auth || false}
          onCheckedChange={(checked) => onUpdate({ require_fresh_auth: checked })}
        />
        <Label htmlFor="require_fresh_auth">Require Fresh Authentication</Label>
      </div>
    </div>
  );
}
