-- Migration for Password Management

-- Add password management fields to users table
ALTER TABLE "users" 
  ADD COLUMN IF NOT EXISTS "password_updated_at" TIMESTAMP,
  ADD COLUMN IF NOT EXISTS "password_reset_token" TEXT,
  ADD COLUMN IF NOT EXISTS "password_reset_expires" TIMESTAMP;

-- Create password history table
CREATE TABLE IF NOT EXISTS "password_history" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
  "password_hash" TEXT NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
