import React, { useState, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Search,
  Shield,
  Users,
  CreditCard,
  DollarSign,
  BarChart,
  UserCheck,
  Building,
  Settings,
  Lock,
  ArrowDown,
  ArrowRight,
  Clock,
  AlertTriangle,
  Info,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  permissions?: number[];
  parent_roles?: number[];
  child_roles?: number[];
  hierarchy_depth?: number;
  effective_permissions?: number[];
  inherited_permissions?: number[];
  temporary_permissions?: TemporaryPermission[];
}

interface TemporaryPermission {
  id: number;
  permission_id: number;
  granted_by: number;
  granted_at: string;
  expires_at: string;
  priority: 'low' | 'medium' | 'high' | 'emergency';
  reason?: string;
  is_active: boolean;
}

interface RoleHierarchy {
  role_id: number;
  parent_role_id: number;
  inheritance_type: 'inherit' | 'override' | 'deny';
}

interface PermissionCategory {
  category: string;
  metadata: {
    name: string;
    description: string;
    icon: string;
  };
  permissions: Permission[];
}

interface PermissionMatrixProps {
  roles: Role[];
  permissionCategories: PermissionCategory[];
  roleHierarchies?: RoleHierarchy[];
  onPermissionChange: (roleId: number, permissionId: number, granted: boolean) => Promise<void>;
  onBulkAssign?: (assignments: Array<{role_id: number, permission_ids: number[], action: 'grant' | 'revoke'}>) => Promise<void>;
  onTemporaryPermissionGrant?: (roleId: number, permissionId: number, expiresAt: string, priority: string, reason?: string) => Promise<void>;
  onTemporaryPermissionRevoke?: (temporaryPermissionId: number) => Promise<void>;
  loading?: boolean;
  showHierarchy?: boolean;
  showTemporaryPermissions?: boolean;
  showEffectivePermissions?: boolean;
}

const categoryIcons: Record<string, React.ComponentType<any>> = {
  CreditCard,
  Users,
  DollarSign,
  BarChart,
  UserCheck,
  Shield,
  Building,
  Settings,
  Lock
};

export function PermissionMatrix({
  roles,
  permissionCategories,
  roleHierarchies = [],
  onPermissionChange,
  onBulkAssign,
  onTemporaryPermissionGrant,
  onTemporaryPermissionRevoke,
  loading = false,
  showHierarchy = true,
  showTemporaryPermissions = true,
  showEffectivePermissions = true
}: PermissionMatrixProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [bulkSelection, setBulkSelection] = useState<{roleId: number, permissionIds: number[]}>({
    roleId: 0,
    permissionIds: []
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [viewMode, setViewMode] = useState<'direct' | 'effective' | 'inherited'>('effective');
  const [showHierarchyView, setShowHierarchyView] = useState(showHierarchy);
  const [showTempPermissions, setShowTempPermissions] = useState(showTemporaryPermissions);
  const [selectedRole, setSelectedRole] = useState<number | null>(null);

  // Initialize expanded categories
  useEffect(() => {
    if (permissionCategories.length > 0) {
      setExpandedCategories(new Set(permissionCategories.map(cat => cat.category)));
    }
  }, [permissionCategories]);

  // Filter permissions based on search term and selected category
  const filteredCategories = permissionCategories.filter(category => {
    if (selectedCategory && category.category !== selectedCategory) return false;

    if (searchTerm) {
      return category.permissions.some(permission =>
        permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.code.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return true;
  }).map(category => ({
    ...category,
    permissions: category.permissions.filter(permission =>
      !searchTerm ||
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.code.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }));

  const handlePermissionToggle = async (roleId: number, permissionId: number, currentlyGranted: boolean) => {
    setIsUpdating(true);
    try {
      await onPermissionChange(roleId, permissionId, !currentlyGranted);
      toast({
        title: "Permission Updated",
        description: `Permission ${currentlyGranted ? 'revoked from' : 'granted to'} role successfully.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update permission. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleBulkAssign = async (action: 'grant' | 'revoke') => {
    if (!onBulkAssign || bulkSelection.permissionIds.length === 0) return;

    setIsUpdating(true);
    try {
      await onBulkAssign([{
        role_id: bulkSelection.roleId,
        permission_ids: bulkSelection.permissionIds,
        action
      }]);

      setBulkSelection({ roleId: 0, permissionIds: [] });
      toast({
        title: "Bulk Assignment Complete",
        description: `${bulkSelection.permissionIds.length} permissions ${action === 'grant' ? 'granted' : 'revoked'} successfully.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to perform bulk assignment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  // Helper functions for permission calculations
  const getEffectivePermissions = (role: Role): number[] => {
    if (viewMode === 'direct') {
      return role.permissions || [];
    }

    if (viewMode === 'inherited') {
      return role.inherited_permissions || [];
    }

    // Effective permissions (direct + inherited + temporary)
    const directPermissions = new Set(role.permissions || []);
    const inheritedPermissions = new Set(role.inherited_permissions || []);
    const temporaryPermissions = new Set(
      role.temporary_permissions?.filter(tp => tp.is_active && new Date(tp.expires_at) > new Date())
        .map(tp => tp.permission_id) || []
    );

    return Array.from(new Set([...directPermissions, ...inheritedPermissions, ...temporaryPermissions]));
  };

  const isPermissionGranted = (role: Role, permissionId: number): boolean => {
    const effectivePermissions = getEffectivePermissions(role);
    return effectivePermissions.includes(permissionId);
  };

  const getPermissionSource = (role: Role, permissionId: number): 'direct' | 'inherited' | 'temporary' | 'none' => {
    if (role.permissions?.includes(permissionId)) return 'direct';
    if (role.inherited_permissions?.includes(permissionId)) return 'inherited';
    if (role.temporary_permissions?.some(tp =>
      tp.permission_id === permissionId &&
      tp.is_active &&
      new Date(tp.expires_at) > new Date()
    )) return 'temporary';
    return 'none';
  };

  const getTemporaryPermission = (role: Role, permissionId: number): TemporaryPermission | null => {
    return role.temporary_permissions?.find(tp =>
      tp.permission_id === permissionId &&
      tp.is_active &&
      new Date(tp.expires_at) > new Date()
    ) || null;
  };

  const getRoleHierarchyDepth = (roleId: number): number => {
    const role = roles.find(r => r.id === roleId);
    return role?.hierarchy_depth || 0;
  };

  const sortRolesByHierarchy = (rolesToSort: Role[]): Role[] => {
    if (!showHierarchyView) return rolesToSort;

    return [...rolesToSort].sort((a, b) => {
      const depthA = getRoleHierarchyDepth(a.id);
      const depthB = getRoleHierarchyDepth(b.id);

      if (depthA !== depthB) {
        return depthA - depthB;
      }

      return a.name.localeCompare(b.name);
    });
  };

  const getCategoryIcon = (iconName: string) => {
    const IconComponent = categoryIcons[iconName] || Lock;
    return <IconComponent className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Enhanced Permission Matrix</h2>
            <p className="text-muted-foreground">
              Manage role permissions with hierarchy and temporary access controls
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              {roles.length} Roles • {permissionCategories.reduce((acc, cat) => acc + cat.permissions.length, 0)} Permissions
            </Badge>
            {roleHierarchies.length > 0 && (
              <Badge variant="secondary" className="text-sm">
                {roleHierarchies.length} Hierarchies
              </Badge>
            )}
          </div>
        </div>

        {/* View Mode Controls */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">View Mode:</label>
                  <div className="flex gap-1">
                    <Button
                      variant={viewMode === 'effective' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('effective')}
                    >
                      Effective
                    </Button>
                    <Button
                      variant={viewMode === 'direct' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('direct')}
                    >
                      Direct
                    </Button>
                    <Button
                      variant={viewMode === 'inherited' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('inherited')}
                    >
                      Inherited
                    </Button>
                  </div>
                </div>
              </div>

              <Separator orientation="vertical" className="h-6 hidden sm:block" />

              <div className="flex items-center gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showHierarchyView}
                    onCheckedChange={setShowHierarchyView}
                  />
                  <label className="text-sm">Show Hierarchy</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showTempPermissions}
                    onCheckedChange={setShowTempPermissions}
                  />
                  <label className="text-sm">Show Temporary</label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search permissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2">
            <Button
              variant={selectedCategory ? "outline" : "default"}
              onClick={() => setSelectedCategory(null)}
              size="sm"
            >
              All Categories
            </Button>
            {permissionCategories.slice(0, 3).map((category) => (
              <Button
                key={category.category}
                variant={selectedCategory === category.category ? "default" : "outline"}
                onClick={() => setSelectedCategory(
                  selectedCategory === category.category ? null : category.category
                )}
                size="sm"
                className="hidden sm:flex"
              >
                {getCategoryIcon(category.metadata.icon)}
                <span className="ml-1">{category.metadata.name}</span>
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permission Assignment Matrix
          </CardTitle>
          <CardDescription>
            Click checkboxes to grant or revoke permissions for each role
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[600px] w-full">
            <div className="space-y-6">
              {filteredCategories.map((category) => (
                <div key={category.category} className="space-y-3">
                  {/* Category Header */}
                  <div
                    className="flex items-center justify-between p-3 bg-muted/50 rounded-lg cursor-pointer hover:bg-muted/70 transition-colors"
                    onClick={() => toggleCategory(category.category)}
                  >
                    <div className="flex items-center gap-3">
                      {getCategoryIcon(category.metadata.icon)}
                      <div>
                        <h3 className="font-semibold">{category.metadata.name}</h3>
                        <p className="text-sm text-muted-foreground">{category.metadata.description}</p>
                      </div>
                    </div>
                    <Badge variant="secondary">
                      {category.permissions.length} permissions
                    </Badge>
                  </div>

                  {/* Permissions Table */}
                  {expandedCategories.has(category.category) && (
                    <div className="border rounded-lg overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead className="bg-muted/30">
                            <tr>
                              <th className="text-left p-3 font-medium min-w-[300px]">Permission</th>
                              {sortRolesByHierarchy(roles).map(role => (
                                <th key={role.id} className="text-center p-3 font-medium min-w-[140px]">
                                  <div className="flex flex-col items-center gap-1">
                                    <div className="flex items-center gap-1">
                                      {showHierarchyView && role.hierarchy_depth && role.hierarchy_depth > 0 && (
                                        <div className="flex items-center">
                                          {'  '.repeat(role.hierarchy_depth)}
                                          <ArrowRight className="h-3 w-3 text-muted-foreground" />
                                        </div>
                                      )}
                                      <span className="text-sm font-medium">{role.name}</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      {role.is_system && (
                                        <Badge variant="outline" className="text-xs">System</Badge>
                                      )}
                                      {showHierarchyView && role.hierarchy_depth !== undefined && (
                                        <Badge variant="secondary" className="text-xs">
                                          L{role.hierarchy_depth}
                                        </Badge>
                                      )}
                                      {showTempPermissions && role.temporary_permissions && role.temporary_permissions.length > 0 && (
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger>
                                              <Clock className="h-3 w-3 text-orange-500" />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                              <p>{role.temporary_permissions.length} temporary permissions</p>
                                            </TooltipContent>
                                          </Tooltip>
                                        </TooltipProvider>
                                      )}
                                    </div>
                                  </div>
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {category.permissions.map((permission, index) => (
                              <tr
                                key={permission.id}
                                className={`border-t hover:bg-muted/20 ${index % 2 === 0 ? 'bg-background' : 'bg-muted/10'}`}
                              >
                                <td className="p-3">
                                  <div className="space-y-1">
                                    <div className="font-medium">{permission.name}</div>
                                    <div className="text-sm text-muted-foreground">{permission.description}</div>
                                    <code className="text-xs bg-muted px-2 py-1 rounded">{permission.code}</code>
                                  </div>
                                </td>
                                {sortRolesByHierarchy(roles).map(role => {
                                  const isGranted = isPermissionGranted(role, permission.id);
                                  const source = getPermissionSource(role, permission.id);
                                  const tempPermission = getTemporaryPermission(role, permission.id);

                                  return (
                                    <td key={role.id} className="p-3 text-center">
                                      <div className="flex flex-col items-center gap-1">
                                        <div className="relative">
                                          <Checkbox
                                            checked={isGranted}
                                            onCheckedChange={(checked) =>
                                              handlePermissionToggle(role.id, permission.id, isGranted)
                                            }
                                            disabled={loading || isUpdating || source === 'inherited'}
                                            className={`mx-auto ${
                                              source === 'direct' ? 'border-blue-500' :
                                              source === 'inherited' ? 'border-green-500' :
                                              source === 'temporary' ? 'border-orange-500' :
                                              ''
                                            }`}
                                          />
                                          {source !== 'none' && source !== 'direct' && (
                                            <div className="absolute -top-1 -right-1">
                                              {source === 'inherited' && (
                                                <TooltipProvider>
                                                  <Tooltip>
                                                    <TooltipTrigger>
                                                      <ArrowDown className="h-3 w-3 text-green-600" />
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                      <p>Inherited from parent role</p>
                                                    </TooltipContent>
                                                  </Tooltip>
                                                </TooltipProvider>
                                              )}
                                              {source === 'temporary' && (
                                                <TooltipProvider>
                                                  <Tooltip>
                                                    <TooltipTrigger>
                                                      <Clock className="h-3 w-3 text-orange-600" />
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                      <div className="space-y-1">
                                                        <p>Temporary permission</p>
                                                        {tempPermission && (
                                                          <>
                                                            <p className="text-xs">
                                                              Expires: {new Date(tempPermission.expires_at).toLocaleDateString()}
                                                            </p>
                                                            <p className="text-xs">
                                                              Priority: {tempPermission.priority}
                                                            </p>
                                                            {tempPermission.reason && (
                                                              <p className="text-xs">
                                                                Reason: {tempPermission.reason}
                                                              </p>
                                                            )}
                                                          </>
                                                        )}
                                                      </div>
                                                    </TooltipContent>
                                                  </Tooltip>
                                                </TooltipProvider>
                                              )}
                                            </div>
                                          )}
                                        </div>

                                        {/* Permission source indicator */}
                                        {viewMode === 'effective' && source !== 'none' && (
                                          <div className="flex items-center gap-1">
                                            {source === 'direct' && (
                                              <Badge variant="default" className="text-xs px-1 py-0">D</Badge>
                                            )}
                                            {source === 'inherited' && (
                                              <Badge variant="secondary" className="text-xs px-1 py-0">I</Badge>
                                            )}
                                            {source === 'temporary' && (
                                              <Badge variant="outline" className="text-xs px-1 py-0 border-orange-500 text-orange-700">T</Badge>
                                            )}
                                          </div>
                                        )}
                                      </div>
                                    </td>
                                  );
                                })}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Legend */}
      {(showHierarchyView || showTempPermissions || viewMode === 'effective') && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Legend</CardTitle>
            <CardDescription>
              Understanding permission sources and indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Permission Sources */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Permission Sources</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant="default" className="text-xs px-1 py-0">D</Badge>
                    <span>Direct - Assigned directly to role</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs px-1 py-0">I</Badge>
                    <span>Inherited - From parent role</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs px-1 py-0 border-orange-500 text-orange-700">T</Badge>
                    <span>Temporary - Time-limited access</span>
                  </div>
                </div>
              </div>

              {/* Visual Indicators */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Visual Indicators</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <ArrowDown className="h-3 w-3 text-green-600" />
                    <span>Inherited permission</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-orange-600" />
                    <span>Temporary permission</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ArrowRight className="h-3 w-3 text-muted-foreground" />
                    <span>Role hierarchy level</span>
                  </div>
                </div>
              </div>

              {/* View Modes */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">View Modes</h4>
                <div className="space-y-1 text-sm">
                  <div><strong>Effective:</strong> All active permissions</div>
                  <div><strong>Direct:</strong> Only directly assigned</div>
                  <div><strong>Inherited:</strong> Only from parent roles</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Operations */}
      {onBulkAssign && (
        <Card>
          <CardHeader>
            <CardTitle>Bulk Operations</CardTitle>
            <CardDescription>
              Perform bulk permission assignments across multiple permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <label className="text-sm font-medium">Selected Permissions</label>
                <p className="text-sm text-muted-foreground">
                  {bulkSelection.permissionIds.length} permissions selected
                </p>
              </div>
              <Button
                onClick={() => handleBulkAssign('grant')}
                disabled={bulkSelection.permissionIds.length === 0 || isUpdating}
                variant="default"
              >
                Grant Selected
              </Button>
              <Button
                onClick={() => handleBulkAssign('revoke')}
                disabled={bulkSelection.permissionIds.length === 0 || isUpdating}
                variant="outline"
              >
                Revoke Selected
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
