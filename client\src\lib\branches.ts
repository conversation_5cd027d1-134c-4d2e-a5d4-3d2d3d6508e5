import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiRequest } from './queryClient';
import { queryClient } from './queryClient';
import { useToast } from '@/hooks/use-toast';

export interface Branch {
  id: number;
  company_id: number;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  manager_name?: string;
  status: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface BranchState {
  currentBranchId: number | null;
  branches: Branch[];
  isLoading: boolean;
  error: string | null;

  // Actions
  setCurrentBranch: (branchId: number | null) => void;
  fetchBranches: (companyId: number) => Promise<void>;
  createBranch: (branch: Partial<Branch>) => Promise<Branch | null>;
  updateBranch: (branchId: number, branch: Partial<Branch>) => Promise<Branch | null>;
  deleteBranch: (branchId: number) => Promise<boolean>;
  getCurrentBranch: () => Branch | null;
  clearBranchData: () => void; // Add clear function for logout
}

export const useBranchStore = create<BranchState>()(
  persist(
    (set, get) => ({
      currentBranchId: null,
      branches: [],
      isLoading: false,
      error: null,

      setCurrentBranch: (branchId) => {
        set({ currentBranchId: branchId });
        // Invalidate any queries that might depend on the branch
        queryClient.invalidateQueries();
      },

      fetchBranches: async (companyId) => {
        set({ isLoading: true, error: null });
        try {
          const response = await apiRequest(
            'GET',
            `/api/companies/${companyId}/branches`
          );
          
          if (!response.ok) {
            throw new Error('Failed to fetch branches');
          }
          
          const branches = await response.json();
          set({ branches, isLoading: false });
          
          // If no current branch is selected and branches exist, select the first one
          const { currentBranchId } = get();
          if (currentBranchId === null && branches.length > 0) {
            set({ currentBranchId: branches[0].id });
          }
          
          return branches;
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'An unknown error occurred' 
          });
        }
      },

      createBranch: async (branch) => {
        set({ isLoading: true, error: null });
        try {
          const response = await apiRequest('POST', '/api/branches', branch);
          
          if (!response.ok) {
            throw new Error('Failed to create branch');
          }
          
          const newBranch = await response.json();
          set(state => ({ 
            branches: [...state.branches, newBranch],
            isLoading: false 
          }));
          
          // If this is the first branch, automatically select it
          if (get().branches.length === 1) {
            set({ currentBranchId: newBranch.id });
          }
          
          return newBranch;
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'An unknown error occurred' 
          });
          return null;
        }
      },

      updateBranch: async (branchId, branch) => {
        set({ isLoading: true, error: null });
        try {
          const response = await apiRequest(
            'PATCH',
            `/api/branches/${branchId}`,
            branch
          );
          
          if (!response.ok) {
            throw new Error('Failed to update branch');
          }
          
          const updatedBranch = await response.json();
          set(state => ({
            branches: state.branches.map(b => 
              b.id === branchId ? { ...b, ...updatedBranch } : b
            ),
            isLoading: false
          }));
          
          return updatedBranch;
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'An unknown error occurred' 
          });
          return null;
        }
      },

      deleteBranch: async (branchId) => {
        set({ isLoading: true, error: null });
        try {
          const response = await apiRequest(
            'DELETE',
            `/api/branches/${branchId}`
          );
          
          if (!response.ok) {
            throw new Error('Failed to delete branch');
          }
          
          set(state => ({
            branches: state.branches.filter(b => b.id !== branchId),
            // If we're deleting the current branch, select another one or null
            currentBranchId: state.currentBranchId === branchId 
              ? (state.branches.length > 1 
                ? state.branches.find(b => b.id !== branchId)?.id ?? null 
                : null) 
              : state.currentBranchId,
            isLoading: false
          }));
          
          return true;
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'An unknown error occurred' 
          });
          return false;
        }
      },

      getCurrentBranch: () => {
        const { currentBranchId, branches } = get();
        if (!currentBranchId) return null;
        return branches.find(branch => branch.id === currentBranchId) || null;
      },

      clearBranchData: () => {
        set({
          currentBranchId: null,
          branches: [],
          isLoading: false,
          error: null
        });
        // Also clear React Query cache for branches
        queryClient.removeQueries({ queryKey: ['branches'] });
        queryClient.removeQueries({ queryKey: ['/api/companies'] });
      }
    }),
    {
      name: 'branch-storage',
      // Only persist selected fields
      partialize: (state) => ({ 
        currentBranchId: state.currentBranchId
      }),
    }
  )
);

// Hook to make branch operations easier
export const useBranches = () => {
  const { toast } = useToast();
  const branchStore = useBranchStore();
  
  return {
    ...branchStore,
    
    // Enhanced operations with toast feedback
    createBranchWithFeedback: async (branch: Partial<Branch>) => {
      const result = await branchStore.createBranch(branch);
      if (result) {
        toast({
          title: "Branch created",
          description: `Branch "${result.name}" has been created successfully.`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "Failed to create branch",
          description: branchStore.error || "An unknown error occurred. Please try again.",
        });
      }
      return result;
    },
    
    updateBranchWithFeedback: async (branchId: number, branch: Partial<Branch>) => {
      const result = await branchStore.updateBranch(branchId, branch);
      if (result) {
        toast({
          title: "Branch updated",
          description: `Branch "${result.name}" has been updated successfully.`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "Failed to update branch",
          description: branchStore.error || "An unknown error occurred. Please try again.",
        });
      }
      return result;
    },
    
    deleteBranchWithFeedback: async (branchId: number, branchName: string) => {
      const result = await branchStore.deleteBranch(branchId);
      if (result) {
        toast({
          title: "Branch deleted",
          description: `Branch "${branchName}" has been deleted successfully.`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "Failed to delete branch",
          description: branchStore.error || "An unknown error occurred. Please try again.",
        });
      }
      return result;
    },
    
    switchBranch: (branchId: number | null, branchName?: string) => {
      branchStore.setCurrentBranch(branchId);
      if (branchId && branchName) {
        toast({
          title: "Branch switched",
          description: `You are now working with "${branchName}" branch.`,
        });
      }
    },

    // Clear all branch data (useful for logout)
    clearBranchData: () => {
      branchStore.clearBranchData();
    }
  };
};