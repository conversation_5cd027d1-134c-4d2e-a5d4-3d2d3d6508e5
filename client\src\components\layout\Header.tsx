import { useState, useEffect } from "react";
import { Bell, Search, ChevronDown, Menu, Building } from "lucide-react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu";
import { CompanyWithAccess } from "@/lib/auth";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/auth";
import { useLocation } from "wouter";

interface HeaderProps {
  openMobileSidebar: () => void;
}

export default function Header({ openMobileSidebar }: HeaderProps) {
  // Get auth functions from the context
  const { getCurrentUser, logout, getUserCompanies, switchCompany } = useAuth();
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [userCompanies, setUserCompanies] = useState<CompanyWithAccess[]>([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);

  // Instead of multiple state variables, use a single userData state
  // This prevents issues with state synchronization
  const [userData, setUserData] = useState<any>(null);

  // Function to fetch companies for a user
  const fetchUserCompanies = async (userId: number) => {
    setIsLoadingCompanies(true);
    try {
      const companies = await getUserCompanies(userId);
      console.log("User companies:", companies);
      setUserCompanies(companies);
    } catch (err) {
      console.error("Error fetching companies:", err);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // When userData changes, fetch companies
  useEffect(() => {
    if (userData?.id) {
      fetchUserCompanies(userData.id);
    }
  }, [userData?.id]);

  // Load user data on mount and periodically
  useEffect(() => {
    function loadUserData() {
      // Get user data directly from localStorage
      const rawData = localStorage.getItem("user_data");

      if (rawData) {
        try {
          const parsed = JSON.parse(rawData);

          // Debug log the company name specifically
          console.log("Company name in localStorage:", parsed?.company_name);

          // Update the state with all user data
          setUserData(parsed);
        } catch (err) {
          console.error("Error parsing user data:", err);
        }
      } else {
        console.log("No user data in localStorage");
      }
    }

    // Initial load
    loadUserData();

    // Set up interval to check for changes
    const intervalId = setInterval(loadUserData, 1000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, []);

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality
    console.log("Searching for:", searchQuery);
  };

  // Extract user data
  const fullName = userData?.full_name || "";
  const companyName = userData?.company_name || "";
  const companyId = userData?.company_id || "";

  // Generate user initials from full name
  const userInitials = fullName
    ? fullName
        .split(" ")
        .map((name: string) => name[0])
        .join("")
        .substring(0, 2)
        .toUpperCase()
    : "U";

  return (
    <header className="sticky top-0 z-30 flex h-16 bg-white shadow-sm">
      <div className="flex flex-1 items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* Search box */}
        <div className="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end">
          <form onSubmit={handleSearch} className="max-w-lg w-full relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <Input
              id="search"
              name="search"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
              placeholder="Search..."
              type="search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </form>
        </div>

        {/* Right side icons */}
        <div className="ml-4 flex items-center md:ml-6">
          {/* Notifications */}
          <Button
            variant="ghost"
            size="icon"
            className="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <span className="sr-only">View notifications</span>
            <Bell className="h-6 w-6" aria-hidden="true" />
          </Button>

          {/* Profile dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center gap-2 hover:bg-transparent ml-3"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src="" alt={fullName || "User"} />
                  <AvatarFallback className="bg-primary text-white">
                    {userInitials}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:flex flex-col">
                  <span className="text-sm font-medium text-gray-700">
                    {fullName ? fullName.split(" ")[0] : "User"}
                  </span>
                  {/* Show company name or debugging message */}
                  <span className="text-xs text-gray-500">
                    {companyName || "(No company name)"}
                  </span>
                </div>
                <ChevronDown className="hidden md:block h-4 w-4 text-gray-400" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                {companyName ? `${companyName} - My Account` : 'My Account'}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => navigate("/profile")}
                className="cursor-pointer"
              >
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => navigate("/settings")}
                className="cursor-pointer"
              >
                Settings
              </DropdownMenuItem>

              {/* Company Switcher */}
              {userCompanies.length > 1 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger className="cursor-pointer">
                      <Building className="mr-2 h-4 w-4" />
                      <span>Switch Company</span>
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent className="p-0">
                      {isLoadingCompanies ? (
                        <DropdownMenuItem disabled>
                          Loading...
                        </DropdownMenuItem>
                      ) : (
                        userCompanies.map((company) => (
                          <DropdownMenuItem
                            key={company.id}
                            className={`cursor-pointer ${company.id === companyId ? 'bg-muted' : ''}`}
                            onClick={() => {
                              if (company.id !== companyId) {
                                switchCompany(company.id, company.name || company.company?.name || "Unknown Company");
                              }
                            }}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span>{company.name}</span>
                              {company.id === companyId && (
                                <span className="text-primary text-xs font-semibold">Current</span>
                              )}
                            </div>
                          </DropdownMenuItem>
                        ))
                      )}
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                </>
              )}

              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleLogout}
                className="cursor-pointer text-red-500 hover:text-red-600"
              >
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
