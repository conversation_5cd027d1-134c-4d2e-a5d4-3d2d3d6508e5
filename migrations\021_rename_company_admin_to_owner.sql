-- Migration: Rename company_admin role to owner
-- Description: Complete refactoring of company_admin role to owner throughout the system
-- Date: 2025-01-27

BEGIN;

-- Step 1: Create new enum with owner instead of company_admin
CREATE TYPE user_role_new AS ENUM (
  'saas_admin',
  'reseller', 
  'owner',
  'employee',
  'agent',
  'customer',
  'partner'
);

-- Step 2: Remove default constraints temporarily
ALTER TABLE user_companies ALTER COLUMN role DROP DEFAULT;

-- Step 3: Update users table to use new enum
ALTER TABLE users ALTER COLUMN role TYPE user_role_new USING (
  CASE
    WHEN role::text = 'company_admin' THEN 'owner'::user_role_new
    ELSE role::text::user_role_new
  END
);

-- Step 4: Update user_companies table to use new enum
ALTER TABLE user_companies ALTER COLUMN role TYPE user_role_new USING (
  CASE
    WHEN role::text = 'company_admin' THEN 'owner'::user_role_new
    ELSE role::text::user_role_new
  END
);

-- Step 5: Update user_templates table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_templates') THEN
        ALTER TABLE user_templates ALTER COLUMN default_role TYPE user_role_new USING (
          CASE
            WHEN default_role::text = 'company_admin' THEN 'owner'::user_role_new
            ELSE default_role::text::user_role_new
          END
        );
    END IF;
END $$;

-- Step 6: Restore default constraint with new value
ALTER TABLE user_companies ALTER COLUMN role SET DEFAULT 'owner';

-- Step 7: Drop old enum and rename new one
DROP TYPE user_role;
ALTER TYPE user_role_new RENAME TO user_role;

-- Step 8: Verify the migration
DO $$
DECLARE
    admin_count INTEGER;
    owner_count INTEGER;
BEGIN
    -- Check that no company_admin roles remain
    SELECT COUNT(*) INTO admin_count FROM users WHERE role::text = 'company_admin';
    SELECT COUNT(*) INTO owner_count FROM users WHERE role::text = 'owner';
    
    IF admin_count > 0 THEN
        RAISE EXCEPTION 'Migration failed: % company_admin roles still exist', admin_count;
    END IF;
    
    RAISE NOTICE 'Migration successful: % users now have owner role', owner_count;
END $$;

COMMIT;
