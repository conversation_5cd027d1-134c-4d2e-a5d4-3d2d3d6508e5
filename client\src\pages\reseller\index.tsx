import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";
import { Building, Briefcase, FileText, Link, Loader2, Plus, Users } from "lucide-react";

interface Reseller {
  id: number;
  user_id: number;
  company_name: string;
  address: string;
  phone: string;
  website: string;
  commission_rate: number;
  parent_reseller_id: number | null;
  active: boolean;
}

interface ResellerClient {
  id: number;
  reseller_id: number;
  company_id: number;
  company: {
    id: number;
    name: string;
    email: string;
  };
}

export default function Reseller() {
  const { getCurrentUser, isAuthorized } = useAuth();
  const user = getCurrentUser();
  const isSaasAdmin = isAuthorized(['saas_admin']);
  const [activeTab, setActiveTab] = useState("dashboard");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // For reseller view, we need to get the reseller ID
  const { data: resellerData, isLoading: resellerLoading } = useQuery<Reseller>({
    queryKey: [`/api/resellers/user/${user?.id}`],
    enabled: !!user?.id && user?.role === 'reseller',
  });

  // For admin view, we fetch all resellers
  const { data: allResellersData, isLoading: allResellersLoading } = useQuery<Reseller[]>({
    queryKey: ['/api/resellers'],
    enabled: isSaasAdmin,
  });

  // Fetch clients for the reseller
  const { data: resellerClientsData, isLoading: clientsLoading } = useQuery<ResellerClient[]>({
    queryKey: [`/api/resellers/${resellerData?.id}/clients`],
    enabled: !!resellerData?.id,
  });

  // Mock data for reseller performance
  const resellerPerformance = {
    totalRevenue: 548000,
    pendingCommissions: 85000,
    activeClients: resellerClientsData?.length || 0,
    conversionRate: 68,
  };

  // Process for generating a referral link
  const generateReferralLink = () => {
    const baseUrl = window.location.origin;
    const referralCode = user?.id ? `REF${user.id}` : 'SAMPLE';
    return `${baseUrl}/register?ref=${referralCode}`;
  };

  const referralLink = generateReferralLink();

  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Reseller Program</h1>
          <p className="mt-1 text-sm text-gray-500">
            {isSaasAdmin 
              ? "Manage resellers and track their performance" 
              : "Manage your reseller account and clients"
            }
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          {isSaasAdmin && (
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="flex items-center gap-1">
                  <Plus size={16} />
                  <span>Add Reseller</span>
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Reseller</DialogTitle>
                  <DialogDescription>
                    Create a new reseller account to expand your business.
                  </DialogDescription>
                </DialogHeader>
                {/* Reseller form would go here in a real implementation */}
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button>Create Reseller</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {resellerLoading || allResellersLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <>
          {/* Reseller View */}
          {user?.role === 'reseller' && resellerData && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Reseller Profile</CardTitle>
                  <CardDescription>Your reseller account details</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-medium flex items-center">
                        <Building className="mr-2 h-5 w-5 text-gray-500" />
                        Company Information
                      </h3>
                      <div className="mt-4 space-y-3">
                        <div>
                          <p className="text-sm text-muted-foreground">Company Name</p>
                          <p className="font-medium">{resellerData.company_name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Phone</p>
                          <p className="font-medium">{resellerData.phone}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Website</p>
                          <p className="font-medium">{resellerData.website || "Not provided"}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Address</p>
                          <p className="font-medium">{resellerData.address || "Not provided"}</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium flex items-center">
                        <Briefcase className="mr-2 h-5 w-5 text-gray-500" />
                        Commission Details
                      </h3>
                      <div className="mt-4 space-y-3">
                        <div>
                          <p className="text-sm text-muted-foreground">Commission Rate</p>
                          <p className="font-medium">{resellerData.commission_rate}%</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Total Revenue</p>
                          <p className="font-medium">{formatCurrency(resellerPerformance.totalRevenue, 'INR', 'en-IN')}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Pending Commissions</p>
                          <p className="font-medium">{formatCurrency(resellerPerformance.pendingCommissions, 'INR', 'en-IN')}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Active Clients</p>
                          <p className="font-medium">{resellerPerformance.activeClients}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid grid-cols-3 w-full sm:w-auto">
                  <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                  <TabsTrigger value="clients">Clients</TabsTrigger>
                  <TabsTrigger value="referrals">Referrals</TabsTrigger>
                </TabsList>

                {/* Dashboard Tab */}
                <TabsContent value="dashboard">
                  <Card>
                    <CardHeader>
                      <CardTitle>Performance Overview</CardTitle>
                      <CardDescription>Track your reseller business performance</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                          <h3 className="text-lg font-medium mb-4">Monthly Revenue</h3>
                          <div className="h-64 bg-muted rounded flex items-center justify-center">
                            <p className="text-muted-foreground">Chart visualization would appear here</p>
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium mb-4">Key Metrics</h3>
                          <div className="space-y-4">
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm font-medium">Client Conversion Rate</span>
                                <span className="text-sm font-medium">{resellerPerformance.conversionRate}%</span>
                              </div>
                              <Progress value={resellerPerformance.conversionRate} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm font-medium">Commission Payout</span>
                                <span className="text-sm font-medium">85%</span>
                              </div>
                              <Progress value={85} className="h-2" />
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-sm font-medium">Client Retention</span>
                                <span className="text-sm font-medium">92%</span>
                              </div>
                              <Progress value={92} className="h-2" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Clients Tab */}
                <TabsContent value="clients">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <div>
                        <CardTitle>Your Clients</CardTitle>
                        <CardDescription>Companies you've referred to TrackFina</CardDescription>
                      </div>
                      <Button className="flex items-center gap-1">
                        <Plus size={16} />
                        <span>Add Client</span>
                      </Button>
                    </CardHeader>
                    <CardContent>
                      {clientsLoading ? (
                        <div className="space-y-4">
                          {[1, 2, 3].map((i) => (
                            <div key={i} className="flex items-center gap-4">
                              <Skeleton className="h-12 w-12 rounded-full" />
                              <div className="space-y-2">
                                <Skeleton className="h-4 w-48" />
                                <Skeleton className="h-4 w-32" />
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : resellerClientsData?.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          You haven't added any clients yet. Start by adding your first client!
                        </div>
                      ) : (
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Company Name</TableHead>
                                <TableHead>Email</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {resellerClientsData?.map(client => (
                                <TableRow key={client.id}>
                                  <TableCell className="font-medium">{client.company.name}</TableCell>
                                  <TableCell>{client.company.email}</TableCell>
                                  <TableCell>Active</TableCell>
                                  <TableCell className="text-right">
                                    <Button variant="ghost" size="sm">
                                      <FileText size={16} className="mr-1" />
                                      Details
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Referrals Tab */}
                <TabsContent value="referrals">
                  <Card>
                    <CardHeader>
                      <CardTitle>Referral Program</CardTitle>
                      <CardDescription>Refer new customers and earn commissions</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="p-4 bg-muted rounded-lg">
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium flex items-center">
                              <Link className="mr-2 h-5 w-5 text-primary" />
                              Your Referral Link
                            </h3>
                            <Button variant="outline" size="sm" onClick={() => {
                              navigator.clipboard.writeText(referralLink);
                            }}>
                              Copy
                            </Button>
                          </div>
                          <p className="mt-2 text-sm font-mono bg-background p-2 rounded border">
                            {referralLink}
                          </p>
                          <p className="mt-2 text-sm text-muted-foreground">
                            Share this link with potential customers. When they sign up, they'll be connected to your account.
                          </p>
                        </div>

                        <div>
                          <h3 className="text-lg font-medium mb-4 flex items-center">
                            <Users className="mr-2 h-5 w-5 text-primary" />
                            Referral Statistics
                          </h3>
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <Card>
                              <CardContent className="p-4">
                                <div className="text-2xl font-bold">24</div>
                                <div className="text-sm text-muted-foreground">Total Referrals</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardContent className="p-4">
                                <div className="text-2xl font-bold">16</div>
                                <div className="text-sm text-muted-foreground">Converted</div>
                              </CardContent>
                            </Card>
                            <Card>
                              <CardContent className="p-4">
                                <div className="text-2xl font-bold">{formatCurrency(65200, 'INR', 'en-IN')}</div>
                                <div className="text-sm text-muted-foreground">Commission Earned</div>
                              </CardContent>
                            </Card>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* Admin View */}
          {isSaasAdmin && (
            <Card>
              <CardHeader>
                <CardTitle>Resellers Management</CardTitle>
                <CardDescription>View and manage all resellers in the system</CardDescription>
              </CardHeader>
              <CardContent>
                {allResellersData?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No resellers found. Add your first reseller to get started!
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Company Name</TableHead>
                          <TableHead>Contact</TableHead>
                          <TableHead>Commission Rate</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {allResellersData?.map(reseller => (
                          <TableRow key={reseller.id}>
                            <TableCell className="font-medium">{reseller.company_name}</TableCell>
                            <TableCell>
                              <div>{reseller.phone}</div>
                              <div className="text-sm text-muted-foreground">{reseller.website}</div>
                            </TableCell>
                            <TableCell>{reseller.commission_rate}%</TableCell>
                            <TableCell>
                              <div className={`px-2 py-1 rounded-full text-xs font-semibold inline-block ${
                                reseller.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                              }`}>
                                {reseller.active ? 'Active' : 'Inactive'}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button variant="ghost" size="sm">
                                Edit
                              </Button>
                              <Button variant="ghost" size="sm" className="text-red-500">
                                Deactivate
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
