-- Migration to remove username field from users table
-- Username is redundant since the system uses email-based authentication

-- Step 1: Remove the unique constraint on username (if it exists)
ALTER TABLE "users" DROP CONSTRAINT IF EXISTS "users_username_key";

-- Step 2: Drop the username column
ALTER TABLE "users" DROP COLUMN IF EXISTS "username";

-- Step 3: Update the default admin user creation logic in schema.sql will need to be updated separately
-- The admin user insert statement should be updated to remove username reference

-- Comment on the change
COMMENT ON TABLE "users" IS 'Users table - uses email as primary identifier for authentication, username field removed as redundant';
