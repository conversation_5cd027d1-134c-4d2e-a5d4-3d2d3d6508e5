import { Express, Response } from 'express';
import { authMiddleware, requirePermission, AuthRequest } from '../middleware/auth';
import { advancedSearchService, type AdvancedSearchFilters } from '../services/advancedSearchService';
import { z } from 'zod';

// Validation schemas
const advancedSearchSchema = z.object({
  searchQuery: z.string().optional(),
  roles: z.array(z.string()).optional(),
  departments: z.array(z.number()).optional(),
  branches: z.array(z.number()).optional(),
  hasPermissions: z.array(z.string()).optional(),
  lacksPermissions: z.array(z.string()).optional(),
  permissionCount: z.object({
    min: z.number().optional(),
    max: z.number().optional()
  }).optional(),
  lastLoginAfter: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  lastLoginBefore: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  createdAfter: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  createdBefore: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  riskScoreMin: z.number().min(0).max(100).optional(),
  riskScoreMax: z.number().min(0).max(100).optional(),
  complianceStatus: z.array(z.string()).optional(),
  needsReview: z.boolean().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional()
});

export function registerAdvancedSearchRoutes(app: Express): void {
  
  // Advanced user search
  app.post('/api/advanced-search/users', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const result = advancedSearchSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid search parameters',
          errors: result.error.errors
        });
      }

      const searchResults = await advancedSearchService.searchUsers(companyId, result.data);
      return res.json(searchResults);
    } catch (error: any) {
      console.error('Error performing advanced user search:', error);
      return res.status(500).json({ message: error.message || 'Failed to perform advanced search' });
    }
  });

  // Get search suggestions (for autocomplete)
  app.get('/api/advanced-search/suggestions', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const { type, query } = req.query;
      
      if (!type || !query) {
        return res.status(400).json({ message: 'Type and query parameters are required' });
      }

      // This would implement autocomplete suggestions
      // For now, return empty array
      const suggestions: string[] = [];
      
      return res.json({ suggestions });
    } catch (error: any) {
      console.error('Error getting search suggestions:', error);
      return res.status(500).json({ message: error.message || 'Failed to get suggestions' });
    }
  });

  // Get permission usage report
  app.get('/api/advanced-search/permission-usage-report', authMiddleware, requirePermission('permission_management'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const report = await advancedSearchService.getPermissionUsageReport(companyId);
      return res.json(report);
    } catch (error: any) {
      console.error('Error generating permission usage report:', error);
      return res.status(500).json({ message: error.message || 'Failed to generate permission usage report' });
    }
  });

  // Get user access analytics
  app.get('/api/advanced-search/user-analytics', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const analytics = await advancedSearchService.getUserAccessAnalytics(companyId);
      return res.json(analytics);
    } catch (error: any) {
      console.error('Error generating user access analytics:', error);
      return res.status(500).json({ message: error.message || 'Failed to generate user access analytics' });
    }
  });

  // Export search results
  app.post('/api/advanced-search/export', authMiddleware, requirePermission('user_export'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const result = advancedSearchSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid search parameters',
          errors: result.error.errors
        });
      }

      // Get all results without pagination for export
      const exportFilters = { ...result.data, page: 1, limit: 10000 };
      const searchResults = await advancedSearchService.searchUsers(companyId, exportFilters);

      // Generate CSV content
      const csvHeaders = [
        'ID', 'Full Name', 'Username', 'Email', 'Role', 'Phone',
        'Branch', 'Department', 'Manager', 'Permission Count', 'Role Count',
        'Risk Score', 'Compliance Status', 'Created At', 'Last Login'
      ];

      const csvRows = searchResults.users.map(user => [
        user.id,
        user.full_name,
        user.username,
        user.email,
        user.role,
        user.phone || '',
        user.branch_name || '',
        user.department_name || '',
        user.manager_name || '',
        user.permission_count,
        user.role_count,
        user.risk_score,
        user.compliance_status,
        user.created_at.toISOString().split('T')[0],
        user.last_login ? user.last_login.toISOString().split('T')[0] : ''
      ]);

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n');

      const filename = `user_search_results_${new Date().toISOString().split('T')[0]}.csv`;
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      
      return res.send(csvContent);
    } catch (error: any) {
      console.error('Error exporting search results:', error);
      return res.status(500).json({ message: error.message || 'Failed to export search results' });
    }
  });

  // Get filter options (for dropdowns)
  app.get('/api/advanced-search/filter-options', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      // This would fetch available filter options from the database
      // For now, return static options
      const filterOptions = {
        roles: [
          { value: 'saas_admin', label: 'SaaS Admin' },
          { value: 'reseller', label: 'Reseller' },
          { value: 'owner', label: 'Owner' },
          { value: 'employee', label: 'Employee' },
          { value: 'agent', label: 'Agent' },
          { value: 'customer', label: 'Customer' },
          { value: 'partner', label: 'Partner' }
        ],
        departments: [
          { value: 1, label: 'Sales' },
          { value: 2, label: 'Operations' },
          { value: 3, label: 'Finance' },
          { value: 4, label: 'IT' },
          { value: 5, label: 'HR' }
        ],
        branches: [
          { value: 1, label: 'Main Branch' },
          { value: 2, label: 'North Branch' },
          { value: 3, label: 'South Branch' },
          { value: 4, label: 'East Branch' },
          { value: 5, label: 'West Branch' }
        ],
        complianceStatuses: [
          { value: 'compliant', label: 'Compliant' },
          { value: 'non_compliant', label: 'Non-Compliant' },
          { value: 'pending_review', label: 'Pending Review' },
          { value: 'under_review', label: 'Under Review' }
        ],
        permissions: [
          { value: 'customer_view', label: 'View Customers' },
          { value: 'customer_edit', label: 'Edit Customers' },
          { value: 'loan_view', label: 'View Loans' },
          { value: 'loan_edit', label: 'Edit Loans' },
          { value: 'loan_approve', label: 'Approve Loans' },
          { value: 'collection_view', label: 'View Collections' },
          { value: 'collection_edit', label: 'Edit Collections' },
          { value: 'user_view', label: 'View Users' },
          { value: 'user_edit', label: 'Edit Users' },
          { value: 'role_assign', label: 'Assign Roles' }
        ]
      };

      return res.json(filterOptions);
    } catch (error: any) {
      console.error('Error getting filter options:', error);
      return res.status(500).json({ message: error.message || 'Failed to get filter options' });
    }
  });

  // Save search preset
  app.post('/api/advanced-search/presets', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const { name, filters } = req.body;
      
      if (!name || !filters) {
        return res.status(400).json({ message: 'Name and filters are required' });
      }

      // This would save the search preset to the database
      // For now, just return success
      const preset = {
        id: Date.now(),
        name,
        filters,
        created_by: req.user.id,
        created_at: new Date()
      };

      return res.status(201).json(preset);
    } catch (error: any) {
      console.error('Error saving search preset:', error);
      return res.status(500).json({ message: error.message || 'Failed to save search preset' });
    }
  });

  // Get saved search presets
  app.get('/api/advanced-search/presets', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // This would fetch saved presets from the database
      // For now, return empty array
      const presets: any[] = [];

      return res.json(presets);
    } catch (error: any) {
      console.error('Error getting search presets:', error);
      return res.status(500).json({ message: error.message || 'Failed to get search presets' });
    }
  });
}
