import React from 'react';
import { format } from 'date-fns';
import { useReportData } from '../hooks/useReportData';
import { ProfitLossReport as ProfitLossReportType, COLORS } from '../types';
import { DateRangePicker } from './DateRangePicker';
import { ReportDownloadButton } from './ReportDownloadButton';
import { ReportCard } from './ReportCard';
import { Separator } from '@/components/ui/separator';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { TrendingUp, DollarSign, Percent } from 'lucide-react';

interface ProfitLossReportProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  onDateRangeChange: (range: { from: Date; to: Date }) => void;
}

export function ProfitLossReport({
  dateRange,
  onDateRangeChange,
}: ProfitLossReportProps) {
  const { data, isLoading, isError, refetch } = useReportData<ProfitLossReportType>({
    reportType: 'profit-loss',
    startDate: dateRange.from,
    endDate: dateRange.to,
  });

  // Prepare chart data
  const pieChartData = React.useMemo(() => {
    if (!data || !data.income || !data.expenses) return [];

    const incomeData = data.income.map(category => ({
      name: category.categoryName,
      value: Math.abs(category.totalAmount),
      type: 'Income'
    }));

    const expenseData = data.expenses.map(category => ({
      name: category.categoryName,
      value: Math.abs(category.totalAmount),
      type: 'Expense'
    }));

    return [...incomeData, ...expenseData];
  }, [data]);

  const barChartData = React.useMemo(() => {
    if (!data) return [];

    return [
      {
        name: 'Income',
        amount: data.totalIncome || 0,
      },
      {
        name: 'Expenses',
        amount: Math.abs(data.totalExpenses || 0),
      },
      {
        name: 'Net Profit',
        amount: data.netProfit || 0,
      },
    ];
  }, [data]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <DateRangePicker
          dateRange={dateRange}
          onDateRangeChange={onDateRangeChange}
        />
        <div className="flex gap-2">
          <ReportDownloadButton
            reportType="profit-loss"
            format="pdf"
            startDate={dateRange.from}
            endDate={dateRange.to}
            disabled={isLoading || isError}
          />
          <ReportDownloadButton
            reportType="profit-loss"
            format="csv"
            startDate={dateRange.from}
            endDate={dateRange.to}
            disabled={isLoading || isError}
          />
        </div>
      </div>

      <ReportCard
        title="Profit & Loss Statement"
        description={`For the period ${format(dateRange.from, 'LLL dd, y')} to ${format(dateRange.to, 'LLL dd, y')}`}
        isLoading={isLoading}
        isError={isError}
        onRetry={() => refetch()}
      >
        {data && data.income && data.expenses && (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-blue-500 mr-2" />
                  <h3 className="text-sm font-medium">Total Income</h3>
                </div>
                <p className="text-2xl font-bold mt-2">${(data.totalIncome || 0).toLocaleString()}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-red-500 mr-2" />
                  <h3 className="text-sm font-medium">Total Expenses</h3>
                </div>
                <p className="text-2xl font-bold mt-2">${Math.abs(data.totalExpenses || 0).toLocaleString()}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 text-green-500 mr-2" />
                  <h3 className="text-sm font-medium">Net Profit</h3>
                </div>
                <p className="text-2xl font-bold mt-2">${(data.netProfit || 0).toLocaleString()}</p>
                <p className="text-sm mt-1">
                  <Percent className="h-3 w-3 inline mr-1" />
                  {data.totalIncome ? ((data.netProfit / data.totalIncome) * 100).toFixed(2) : '0.00'}% margin
                </p>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="h-80">
                <h3 className="text-lg font-medium mb-2">Income vs Expenses</h3>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={barChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value}`, 'Amount']} />
                    <Legend />
                    <Bar dataKey="amount" fill="#0088FE" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="h-80">
                <h3 className="text-lg font-medium mb-2">Breakdown by Category</h3>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`$${value}`, 'Amount']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Detailed Tables */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Income</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.income.map((category, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{category.categoryName}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">${(category.totalAmount || 0).toLocaleString()}</td>
                        </tr>
                      ))}
                      <tr className="bg-blue-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Total Income</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">${(data.totalIncome || 0).toLocaleString()}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Expenses</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.expenses.map((category, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{category.categoryName}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">${Math.abs(category.totalAmount || 0).toLocaleString()}</td>
                        </tr>
                      ))}
                      <tr className="bg-red-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Total Expenses</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">${Math.abs(data.totalExpenses || 0).toLocaleString()}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <Separator />

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex justify-between">
                  <h3 className="text-lg font-bold">Net Profit</h3>
                  <p className="text-lg font-bold">${(data.netProfit || 0).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </ReportCard>
    </div>
  );
}
