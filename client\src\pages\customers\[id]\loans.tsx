import { useState } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, PlusCircle, Filter, Search } from "lucide-react";
import { Helmet } from "react-helmet";
import { useAuth } from "@/lib/auth";
import { formatLoanTerm } from "@/utils/format-utils";

import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getInitials } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface Customer {
  id: number;
  company_id: number;
  full_name: string;
  email: string;
  phone: string;
  address: string;
  profile_image?: string;
  credit_score?: number;
  kyc_verified?: boolean;
  active: boolean;
  notes?: string;
}

interface Loan {
  id: number;
  customer_id: number;
  amount: number;
  interest_rate: number;
  interest_type: string;
  term: number;
  terms_frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'yearly' | null;
  payment_frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'yearly' | null;
  start_date: string;
  end_date: string;
  status: string;
  notes?: string;
  loan_reference_code?: string;
}

export default function CustomerLoans() {
  const { id } = useParams();
  const customerId = parseInt(id, 10);
  const [, navigate] = useLocation();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const companyId = user?.company_id || 1;
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  const { data: customer, isLoading: customerLoading } = useQuery<Customer>({
    queryKey: [`/api/customers/${customerId}`],
    enabled: !!customerId,
  });

  const { data: loans = [], isLoading: loansLoading } = useQuery<Loan[]>({
    queryKey: [`/api/customers/${customerId}/loans`],
    enabled: !!customerId,
  });

  const getCreditScoreColor = (score?: number) => {
    if (!score) return "bg-gray-200";
    if (score >= 750) return "bg-green-500";
    if (score >= 650) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getStatusBadgeVariant = (status?: string) => {
    if (!status) return 'bg-gray-100 text-gray-800 hover:bg-gray-100';

    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'overdue': return 'bg-red-100 text-red-800 hover:bg-red-100';
      case 'completed': return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'pending': return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'rejected': return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      default: return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  // Helper function to capitalize loan status
  const capitalizeStatus = (status?: string) => {
    if (!status) return '';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const getStatusIcon = (status?: string) => {
    if (!status) return null;

    switch (status.toLowerCase()) {
      case 'active':
        return <div className="h-2 w-2 rounded-full bg-green-500 mr-1.5" />;
      case 'overdue':
        return <div className="h-2 w-2 rounded-full bg-red-500 mr-1.5" />;
      case 'completed':
        return <div className="h-2 w-2 rounded-full bg-blue-500 mr-1.5" />;
      case 'pending':
        return <div className="h-2 w-2 rounded-full bg-yellow-500 mr-1.5" />;
      default:
        return <div className="h-2 w-2 rounded-full bg-gray-500 mr-1.5" />;
    }
  };

  const filteredLoans = loans
    .filter(loan => {
      if (activeTab === "all") return true;
      return loan.status === activeTab;
    })
    .filter(loan => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      // Search through loan reference code, amount, dates
      return (
        (loan.loan_reference_code && loan.loan_reference_code.toLowerCase().includes(query)) ||
        loan.amount.toString().includes(query) ||
        (loan.status && loan.status.toLowerCase().includes(query)) ||
        (loan.start_date && loan.start_date.toLowerCase().includes(query)) ||
        (loan.end_date && loan.end_date.toLowerCase().includes(query)) ||
        (loan.notes && loan.notes.toLowerCase().includes(query))
      );
    });

  if (customerLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-pulse text-center">
          <p className="text-muted-foreground">Loading customer details...</p>
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="flex flex-col items-center justify-center h-96 gap-4">
        <h2 className="text-2xl font-semibold">Customer not found</h2>
        <p className="text-muted-foreground">
          The customer you're looking for doesn't exist or you don't have permission to view it.
        </p>
        <Button onClick={() => navigate("/customers")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to customers
        </Button>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{customer.full_name} | Loans | TrackFina</title>
      </Helmet>

      <div className="mb-6">
        <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/customers/${customer.id}`)}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Profile
            </Button>
            <h1 className="text-2xl font-semibold">{customer.full_name}'s Loans</h1>
          </div>

          <Button
            onClick={() => navigate(`/loans/create?customer_id=${customerId}&source=customer&return_to=${encodeURIComponent(`/customers/${customerId}/loans`)}`)}
            className="gap-1"
          >
            <PlusCircle className="h-4 w-4" />
            <span>New Loan</span>
          </Button>
        </div>
      </div>

      {/* Customer Summary Card */}
      <Card className="mb-6">
        <CardContent className="py-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center sm:items-start">
            <Avatar className="h-16 w-16">
              <AvatarFallback className="text-lg">
                {getInitials(customer.full_name)}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 text-center sm:text-left">
              <h2 className="text-xl font-semibold">{customer.full_name}</h2>
              <div className="flex flex-wrap gap-2 justify-center sm:justify-start mt-1 mb-2">
                <Badge variant={customer.active ? "default" : "secondary"}>
                  {customer.active ? "Active" : "Inactive"}
                </Badge>
                {customer.kyc_verified && (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    KYC Verified
                  </Badge>
                )}
              </div>
              <div className="text-sm text-muted-foreground">
                {customer.email && <p>{customer.email}</p>}
                {customer.phone && <p>{customer.phone}</p>}
              </div>
            </div>

            <div className="text-center sm:text-right">
              <div className="flex items-center gap-2 justify-center sm:justify-end">
                <span className="text-sm text-muted-foreground">Credit Score:</span>
                {customer.credit_score ? (
                  <div className="flex items-center gap-1">
                    <span
                      className={`h-3 w-3 rounded-full ${getCreditScoreColor(customer.credit_score)}`}
                    />
                    <span className="font-medium">{customer.credit_score}</span>
                  </div>
                ) : (
                  <span className="text-sm">Not available</span>
                )}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                Total Loans: {loans.length || 0}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Loans List */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center md:justify-between">
            <div>
              <CardTitle>Loan History</CardTitle>
              <CardDescription>
                View and manage all loans for this customer
              </CardDescription>
            </div>

            <div className="w-full md:w-auto flex flex-col md:flex-row gap-3">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search loans..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-3">
            <TabsList className="w-full sm:w-auto">
              <TabsTrigger value="all">
                All Loans <span className="ml-1 text-xs">({loans.length})</span>
              </TabsTrigger>
              <TabsTrigger value="active">
                Active <span className="ml-1 text-xs">({loans.filter(l => l.status === 'active').length})</span>
              </TabsTrigger>
              <TabsTrigger value="overdue">
                Overdue <span className="ml-1 text-xs">({loans.filter(l => l.status === 'overdue').length})</span>
              </TabsTrigger>
              <TabsTrigger value="completed">
                Completed <span className="ml-1 text-xs">({loans.filter(l => l.status === 'completed').length})</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent>
          {loansLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-pulse text-center">
                <p className="text-muted-foreground">Loading loans...</p>
              </div>
            </div>
          ) : filteredLoans.length === 0 ? (
            <div className="text-center py-12 border rounded-md bg-muted/50">
              <p className="text-muted-foreground mb-4">No loans found matching your criteria</p>
              <Button
                onClick={() => navigate(`/loans/create?customer_id=${customer.id}`)}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Create New Loan
              </Button>
            </div>
          ) : (
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Loan ID</TableHead>
                    <TableHead>Date Range</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Term</TableHead>
                    <TableHead>Interest</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLoans.map((loan) => (
                    <TableRow key={loan.id}>
                      <TableCell className="font-medium">{loan.loan_reference_code || `#${loan.id}`}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p>{new Date(loan.start_date).toLocaleDateString()}</p>
                          <p>{new Date(loan.end_date).toLocaleDateString()}</p>
                        </div>
                      </TableCell>
                      <TableCell>₹{loan.amount.toLocaleString()}</TableCell>
                      <TableCell>{formatLoanTerm(loan.term, loan.terms_frequency || 'monthly')}</TableCell>
                      <TableCell>{loan.interest_rate}% {loan.interest_type}</TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeVariant(loan.status)}>
                          <div className="flex items-center">
                            {getStatusIcon(loan.status)}
                            <span>{capitalizeStatus(loan.status)}</span>
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/loans/${loan.id}`)}
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}