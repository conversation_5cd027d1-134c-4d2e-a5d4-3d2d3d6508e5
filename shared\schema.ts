import { pgTable, text, serial, integer, boolean, timestamp, numeric, pgEnum, uniqueIndex, foreignKey, jsonb, unique } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { relations, sql } from "drizzle-orm";
import { z } from "zod";

// Role hierarchy specific types
export type InheritanceType = 'inherit' | 'override' | 'deny';

// Temporary permission specific types
export type ElevationStatus = 'pending' | 'approved' | 'denied' | 'expired' | 'revoked';
export type ElevationPriority = 'low' | 'medium' | 'high' | 'emergency';

// Data scope specific types
export type DataScopeType = 'branch' | 'department' | 'hierarchy' | 'group' | 'company' | 'custom';

// Field security specific types
export type FieldAccessType = 'read' | 'write' | 'none' | 'masked';
export type FieldSensitivityLevel = 'public' | 'internal' | 'confidential' | 'restricted' | 'top_secret';

// Enums
export const userRoleEnum = pgEnum('user_role', [
  'saas_admin',
  'reseller',
  'owner',
  'employee',
  'agent',
  'customer',
  'partner'
]);

// Permission category enum
export const permissionCategoryEnum = pgEnum('permission_category', [
  'user_management',
  'role_management',
  'group_management',
  'permission_management',
  'company_management',
  'customer_management',
  'loan_management',
  'financial_management',
  'report_management',
  'system_settings'
]);

// Role inheritance type enum
export const inheritanceTypeEnum = pgEnum('inheritance_type', [
  'inherit',
  'override',
  'deny'
]);

// Temporary permission enums
export const elevationStatusEnum = pgEnum('elevation_status', [
  'pending',
  'approved',
  'denied',
  'expired',
  'revoked'
]);

export const elevationPriorityEnum = pgEnum('elevation_priority', [
  'low',
  'medium',
  'high',
  'emergency'
]);

// Loan Type Enum
export const loanTypeEnum = pgEnum('loan_type', [
  'personal',
  'business',
  'education',
  'housing',
  'vehicle',
  'agriculture',
  'microfinance',
  'other'
]);

// Loan Status Enum
export const loanStatusEnum = pgEnum('loan_status', [
  'active',
  'overdue',
  'completed'
]);

// Field type enum for dynamic forms
export const fieldTypeEnum = pgEnum('field_type', [
  'text',
  'textarea',
  'number',
  'date',
  'time',
  'select',
  'multiselect',
  'checkbox',
  'radio',
  'file',
  'image',
  'email',
  'phone',
  'url'
]);

export const collectionStatusEnum = pgEnum('collection_status', [
  'pending',
  'completed',
  'overdue',
  'cancelled',
  'partial',
  'rescheduled'
]);

export const paymentMethodEnum = pgEnum('payment_method', [
  'cash',
  'upi',
  'bank_transfer',
  'cheque',
  'card_payment',
  'online_transfer',
  'mobile_wallet'
]);

// Fine calculation type enum
export const fineTypeEnum = pgEnum('fine_type', [
  'percentage', // Fine calculated as percentage of amount due
  'fixed'       // Fixed amount fine
]);

export const expenseTypeEnum = pgEnum('expense_type', [
  'rent',
  'salary',
  'utilities',
  'office_supplies',
  'marketing',
  'transport',
  'loan',
  'other'
]);

// Transaction types for double-entry accounting
export const transactionTypeEnum = pgEnum('transaction_type', [
  'debit',
  'credit'
]);

// Reference types for transactions
export const referenceTypeEnum = pgEnum('reference_type', [
  'loan',
  'collection',
  'expense',
  'investment',
  'withdrawal',
  'transfer',
  'adjustment'
]);

// Account types for chart of accounts
export const accountTypeEnum = pgEnum('account_type', [
  'asset',
  'liability',
  'equity',
  'income',
  'expense'
]);

// Shareholder types
export const shareholderTypeEnum = pgEnum('shareholder_type', [
  'individual',
  'company',
  'trust',
  'partnership'
]);

// Investment transaction types
export const investmentTransactionTypeEnum = pgEnum('investment_transaction_type', [
  'capital_injection',
  'withdrawal',
  'profit_distribution',
  'share_transfer',
  'valuation_change'
]);

// Balance sheet item types
export const balanceSheetItemTypeEnum = pgEnum('balance_sheet_item_type', [
  'current_asset',
  'fixed_asset',
  'other_asset',
  'current_liability',
  'long_term_liability',
  'equity'
]);

export const collectionTimeOfDayEnum = pgEnum('collection_time_of_day', [
  'morning',
  'afternoon',
  'evening'
]);

export const interestTypeEnum = pgEnum('interest_type', [
  'flat',
  'reducing',
  'compound'
]);

export const paymentFrequencyEnum = pgEnum('payment_frequency', [
  'daily',
  'weekly',
  'biweekly',
  'monthly'
]);

// Create a terms frequency enum with the same options as payment frequency
export const termsFrequencyEnum = pgEnum('terms_frequency', [
  'daily',
  'weekly',
  'biweekly',
  'monthly'
]);

export const subscriptionStatusEnum = pgEnum('subscription_status', [
  'pending',
  'active',
  'cancelled',
  'expired'
]);

export const billingCycleEnum = pgEnum('billing_cycle', [
  'monthly',
  'annual'
]);

export const subscriptionPaymentMethodEnum = pgEnum('subscription_payment_method', [
  'credit_card',
  'bank_transfer',
  'paypal'
]);

export const dataScopeTypeEnum = pgEnum('data_scope_type', [
  'branch',
  'department',
  'hierarchy',
  'group',
  'company',
  'custom'
]);

export const fieldAccessTypeEnum = pgEnum('field_access_type', [
  'read',
  'write',
  'none',
  'masked'
]);

export const fieldSensitivityLevelEnum = pgEnum('field_sensitivity_level', [
  'public',
  'internal',
  'confidential',
  'restricted',
  'top_secret'
]);

// Companies (Tenants)
export const companies = pgTable('companies', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  address: text('address'),
  phone: text('phone'),
  email: text('email'),
  website: text('website'),
  logo: text('logo'),
  // MFA policy settings
  mfa_required: boolean('mfa_required').default(false).notNull(),
  mfa_grace_period_days: integer('mfa_grace_period_days').default(30).notNull(),
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// User-Company many-to-many relation
export const userCompanies = pgTable('user_companies', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  company_id: integer('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  is_primary: boolean('is_primary').default(false),
  role: userRoleEnum('role').default('owner'),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Define branches and groups tables first
export const branches = pgTable('branches', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  address: text('address'),
  phone: text('phone'),
  email: text('email'),
  manager_name: text('manager_name'),
  status: text('status').default('active').notNull(),
  notes: text('notes'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const groups = pgTable('groups', {
  id: serial('id').primaryKey(),
  branch_id: integer('branch_id').references(() => branches.id, { onDelete: 'cascade' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  description: text('description'),
  leader_name: text('leader_name'),
  status: text('status').default('active').notNull(),
  meeting_day: text('meeting_day'),
  meeting_time: text('meeting_time'),
  location: text('location'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Departments table for organizational structure
export const departments = pgTable('departments', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').notNull(),
  branch_id: integer('branch_id'),
  name: text('name').notNull(),
  description: text('description'),
  manager_id: integer('manager_id'),
  parent_department_id: integer('parent_department_id'),
  status: text('status').default('active').notNull(),
  budget: numeric('budget', { precision: 15, scale: 2 }),
  cost_center: text('cost_center'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Permissions table
export const permissions = pgTable('permissions', {
  id: serial('id').primaryKey(),
  code: text('code').notNull().unique(),
  name: text('name').notNull(),
  description: text('description'),
  category: permissionCategoryEnum('category').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Custom roles table
export const customRoles = pgTable('custom_roles', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  name: text('name').notNull(),
  description: text('description'),
  is_system: boolean('is_system').default(false).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Permission conditions table for conditional permissions
export const permissionConditions = pgTable('permission_conditions', {
  id: serial('id').primaryKey(),
  permission_id: integer('permission_id').references(() => permissions.id, { onDelete: 'cascade' }).notNull(),
  condition_type: text('condition_type').notNull(), // 'time', 'location', 'amount', 'approval', 'device', 'session'
  condition_config: jsonb('condition_config').notNull(),
  is_active: boolean('is_active').default(true).notNull(),
  priority: integer('priority').default(0).notNull(),
  description: text('description'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Data scope rules table for organizational access control
export const dataScopeRules = pgTable('data_scope_rules', {
  id: serial('id').primaryKey(),
  role_id: integer('role_id').references(() => customRoles.id, { onDelete: 'cascade' }).notNull(),
  scope_type: dataScopeTypeEnum('scope_type').notNull(),
  scope_config: jsonb('scope_config').notNull(),
  resource_type: text('resource_type').notNull(), // 'customers', 'loans', 'payments', 'reports', etc.
  access_level: text('access_level').default('read').notNull(), // 'read', 'write', 'delete', 'admin'
  is_active: boolean('is_active').default(true).notNull(),
  priority: integer('priority').default(0).notNull(),
  description: text('description'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Sensitive field definitions table for field-level security
export const sensitiveFieldDefinitions = pgTable('sensitive_field_definitions', {
  id: serial('id').primaryKey(),
  table_name: text('table_name').notNull(),
  field_name: text('field_name').notNull(),
  sensitivity_level: fieldSensitivityLevelEnum('sensitivity_level').notNull(),
  default_access_type: fieldAccessTypeEnum('default_access_type').default('read').notNull(),
  masking_pattern: text('masking_pattern'), // Pattern for masking (e.g., '***-**-####' for SSN)
  description: text('description'),
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueTableField: sql`UNIQUE (table_name, field_name)`,
}));

// Field security rules table for role-based field access control
export const fieldSecurityRules = pgTable('field_security_rules', {
  id: serial('id').primaryKey(),
  role_id: integer('role_id').references(() => customRoles.id, { onDelete: 'cascade' }).notNull(),
  sensitive_field_id: integer('sensitive_field_id').references(() => sensitiveFieldDefinitions.id, { onDelete: 'cascade' }).notNull(),
  access_type: fieldAccessTypeEnum('access_type').notNull(),
  condition_config: jsonb('condition_config'), // JSON conditions for conditional access
  override_masking_pattern: text('override_masking_pattern'), // Override default masking pattern
  is_active: boolean('is_active').default(true).notNull(),
  priority: integer('priority').default(0).notNull(),
  description: text('description'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueRoleField: sql`UNIQUE (role_id, sensitive_field_id)`,
}));

// Role-permission association table
export const rolePermissions = pgTable('role_permissions', {
  id: serial('id').primaryKey(),
  role_id: integer('role_id').references(() => customRoles.id, { onDelete: 'cascade' }).notNull(),
  permission_id: integer('permission_id').references(() => permissions.id, { onDelete: 'cascade' }).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// User-role association table
export const userRoles = pgTable('user_roles', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  role_id: integer('role_id').references(() => customRoles.id, { onDelete: 'cascade' }).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Group-role association table
export const groupRoles = pgTable('group_roles', {
  id: serial('id').primaryKey(),
  group_id: integer('group_id').references(() => groups.id, { onDelete: 'cascade' }).notNull(),
  role_id: integer('role_id').references(() => customRoles.id, { onDelete: 'cascade' }).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Group-user association table
export const groupUsers = pgTable('group_users', {
  id: serial('id').primaryKey(),
  group_id: integer('group_id').references(() => groups.id, { onDelete: 'cascade' }).notNull(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Role hierarchy table for role inheritance
export const roleHierarchy = pgTable('role_hierarchy', {
  id: serial('id').primaryKey(),
  parent_role_id: integer('parent_role_id').references(() => customRoles.id, { onDelete: 'cascade' }).notNull(),
  child_role_id: integer('child_role_id').references(() => customRoles.id, { onDelete: 'cascade' }).notNull(),
  inheritance_type: inheritanceTypeEnum('inheritance_type').default('inherit').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  // Prevent duplicate parent-child relationships
  uniqueParentChild: uniqueIndex('unique_parent_child').on(table.parent_role_id, table.child_role_id),
  // Prevent self-referencing roles
  checkNotSelfReferencing: sql`CHECK (parent_role_id != child_role_id)`,
}));

// Role templates for quick role setup
export const roleTemplates = pgTable('role_templates', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  template_config: jsonb('template_config').notNull(),
  industry: text('industry'),
  is_system: boolean('is_system').default(false).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  // Ensure unique template names
  uniqueTemplateName: uniqueIndex('unique_template_name').on(table.name),
}));

// Temporary permissions table for time-limited elevated access
export const temporaryPermissions = pgTable('temporary_permissions', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  permission_id: integer('permission_id').references(() => permissions.id, { onDelete: 'cascade' }).notNull(),
  granted_by: integer('granted_by').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  reason: text('reason').notNull(),
  granted_at: timestamp('granted_at').defaultNow().notNull(),
  expires_at: timestamp('expires_at').notNull(),
  revoked_at: timestamp('revoked_at'),
  revoked_by: integer('revoked_by').references(() => users.id, { onDelete: 'set null' }),
  revoke_reason: text('revoke_reason'),
  is_emergency: boolean('is_emergency').default(false).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  checkExpiresAfterGranted: sql`CHECK (expires_at > granted_at)`,
  checkRevokedAfterGranted: sql`CHECK (revoked_at IS NULL OR revoked_at >= granted_at)`,
}));

// Permission elevation requests table for approval workflow
export const permissionElevationRequests = pgTable('permission_elevation_requests', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  requested_by: integer('requested_by').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  permission_id: integer('permission_id').references(() => permissions.id, { onDelete: 'cascade' }).notNull(),
  reason: text('reason').notNull(),
  justification: text('justification'),
  priority: elevationPriorityEnum('priority').default('medium').notNull(),
  duration_hours: integer('duration_hours').default(24).notNull(),
  status: elevationStatusEnum('status').default('pending').notNull(),
  requested_at: timestamp('requested_at').defaultNow().notNull(),
  reviewed_at: timestamp('reviewed_at'),
  reviewed_by: integer('reviewed_by').references(() => users.id, { onDelete: 'set null' }),
  review_notes: text('review_notes'),
  approved_until: timestamp('approved_until'),
  is_emergency: boolean('is_emergency').default(false).notNull(),
  emergency_contact: text('emergency_contact'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  checkDurationPositive: sql`CHECK (duration_hours > 0)`,
  checkDurationReasonable: sql`CHECK (duration_hours <= 8760)`, // Max 1 year
  checkReviewedWhenNotPending: sql`CHECK (
    (status = 'pending') OR
    (status != 'pending' AND reviewed_at IS NOT NULL AND reviewed_by IS NOT NULL)
  )`,
}));

// Emergency access logs table for tracking emergency access usage
export const emergencyAccessLogs = pgTable('emergency_access_logs', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  permission_code: text('permission_code').notNull(),
  action_performed: text('action_performed').notNull(),
  emergency_reason: text('emergency_reason').notNull(),
  access_granted_at: timestamp('access_granted_at').defaultNow().notNull(),
  session_id: text('session_id'),
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),
  resource_accessed: text('resource_accessed'),
  additional_context: jsonb('additional_context'),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// ==================== APPROVAL WORKFLOW SYSTEM ====================

// Enums for approval workflow system
export const approvalWorkflowTypeEnum = pgEnum('approval_workflow_type', [
  'permission_elevation',
  'loan_approval',
  'customer_data_access',
  'emergency_access',
  'role_assignment',
  'custom'
]);

export const approvalStepTypeEnum = pgEnum('approval_step_type', [
  'sequential',
  'parallel',
  'any_one',
  'majority',
  'unanimous'
]);

export const approvalActionEnum = pgEnum('approval_action', [
  'approve',
  'deny',
  'delegate',
  'escalate',
  'request_info'
]);

export const workflowStatusEnum = pgEnum('workflow_status', [
  'pending',
  'in_progress',
  'approved',
  'denied',
  'escalated',
  'cancelled',
  'expired'
]);

export const stepStatusEnum = pgEnum('step_status', [
  'pending',
  'in_progress',
  'completed',
  'skipped',
  'escalated'
]);

// Approval workflows table - defines workflow templates
export const approvalWorkflows = pgTable('approval_workflows', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  description: text('description'),
  workflow_type: approvalWorkflowTypeEnum('workflow_type').notNull(),
  trigger_conditions: jsonb('trigger_conditions'), // Conditions that trigger this workflow
  is_active: boolean('is_active').default(true).notNull(),
  auto_escalation_hours: integer('auto_escalation_hours').default(24), // Auto-escalate after X hours
  max_escalation_levels: integer('max_escalation_levels').default(3),
  created_by: integer('created_by').references(() => users.id, { onDelete: 'set null' }),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueWorkflowName: unique().on(table.company_id, table.name),
}));

// Approval workflow steps table - defines individual steps in workflows
export const approvalWorkflowSteps = pgTable('approval_workflow_steps', {
  id: serial('id').primaryKey(),
  workflow_id: integer('workflow_id').references(() => approvalWorkflows.id, { onDelete: 'cascade' }).notNull(),
  step_order: integer('step_order').notNull(),
  step_name: text('step_name').notNull(),
  step_type: approvalStepTypeEnum('step_type').default('sequential').notNull(),
  required_approvers: integer('required_approvers').default(1).notNull(), // For majority/unanimous
  approver_roles: jsonb('approver_roles'), // Array of role IDs that can approve this step
  approver_users: jsonb('approver_users'), // Array of specific user IDs that can approve
  escalation_roles: jsonb('escalation_roles'), // Roles to escalate to if step times out
  step_timeout_hours: integer('step_timeout_hours').default(24),
  is_optional: boolean('is_optional').default(false).notNull(),
  conditions: jsonb('conditions'), // Additional conditions for this step
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueStepOrder: unique().on(table.workflow_id, table.step_order),
  checkRequiredApprovers: sql`CHECK (required_approvers > 0)`,
  checkStepOrder: sql`CHECK (step_order > 0)`,
}));

// Approval workflow instances table - tracks active workflow executions
export const approvalWorkflowInstances = pgTable('approval_workflow_instances', {
  id: serial('id').primaryKey(),
  workflow_id: integer('workflow_id').references(() => approvalWorkflows.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  request_id: text('request_id').notNull(), // Reference to the original request (e.g., elevation request ID)
  request_type: text('request_type').notNull(), // Type of request (permission_elevation, loan_approval, etc.)
  requester_id: integer('requester_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  request_data: jsonb('request_data'), // Original request data
  current_step_order: integer('current_step_order').default(1).notNull(),
  status: workflowStatusEnum('status').default('pending').notNull(),
  priority: elevationPriorityEnum('priority').default('medium').notNull(),
  started_at: timestamp('started_at').defaultNow().notNull(),
  completed_at: timestamp('completed_at'),
  expires_at: timestamp('expires_at'),
  escalation_level: integer('escalation_level').default(0).notNull(),
  final_decision: approvalActionEnum('final_decision'),
  final_decision_by: integer('final_decision_by').references(() => users.id, { onDelete: 'set null' }),
  final_decision_at: timestamp('final_decision_at'),
  final_decision_notes: text('final_decision_notes'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  checkCurrentStepOrder: sql`CHECK (current_step_order > 0)`,
  checkEscalationLevel: sql`CHECK (escalation_level >= 0)`,
  checkCompletedAfterStarted: sql`CHECK (completed_at IS NULL OR completed_at >= started_at)`,
}));

// Approval workflow step instances table - tracks progress of individual steps
export const approvalWorkflowStepInstances = pgTable('approval_workflow_step_instances', {
  id: serial('id').primaryKey(),
  workflow_instance_id: integer('workflow_instance_id').references(() => approvalWorkflowInstances.id, { onDelete: 'cascade' }).notNull(),
  workflow_step_id: integer('workflow_step_id').references(() => approvalWorkflowSteps.id, { onDelete: 'cascade' }).notNull(),
  step_order: integer('step_order').notNull(),
  status: stepStatusEnum('status').default('pending').notNull(),
  assigned_to: jsonb('assigned_to'), // Array of user IDs assigned to this step
  started_at: timestamp('started_at'),
  completed_at: timestamp('completed_at'),
  timeout_at: timestamp('timeout_at'),
  escalated_at: timestamp('escalated_at'),
  escalated_to: jsonb('escalated_to'), // Array of user IDs escalated to
  approvals_received: integer('approvals_received').default(0).notNull(),
  approvals_required: integer('approvals_required').default(1).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  checkApprovalsReceived: sql`CHECK (approvals_received >= 0)`,
  checkApprovalsRequired: sql`CHECK (approvals_required > 0)`,
  checkStepOrder: sql`CHECK (step_order > 0)`,
}));

// Approval actions table - tracks individual approval actions
export const approvalActions = pgTable('approval_actions', {
  id: serial('id').primaryKey(),
  workflow_instance_id: integer('workflow_instance_id').references(() => approvalWorkflowInstances.id, { onDelete: 'cascade' }).notNull(),
  step_instance_id: integer('step_instance_id').references(() => approvalWorkflowStepInstances.id, { onDelete: 'cascade' }).notNull(),
  approver_id: integer('approver_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  action: approvalActionEnum('action').notNull(),
  comments: text('comments'),
  delegated_to: integer('delegated_to').references(() => users.id, { onDelete: 'set null' }),
  action_data: jsonb('action_data'), // Additional data for the action
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Approval escalation rules table - defines escalation behavior
export const approvalEscalationRules = pgTable('approval_escalation_rules', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  workflow_id: integer('workflow_id').references(() => approvalWorkflows.id, { onDelete: 'cascade' }),
  rule_name: text('rule_name').notNull(),
  trigger_condition: text('trigger_condition').notNull(), // 'timeout', 'priority', 'amount', etc.
  trigger_value: jsonb('trigger_value'), // Configuration for the trigger
  escalation_action: text('escalation_action').notNull(), // 'notify', 'reassign', 'auto_approve', etc.
  escalation_target: jsonb('escalation_target'), // Who to escalate to
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Relations for permissions
export const permissionsRelations = relations(permissions, ({ many }) => ({
  rolePermissions: many(rolePermissions),
  permissionConditions: many(permissionConditions),
}));

// Relations for permission conditions
export const permissionConditionsRelations = relations(permissionConditions, ({ one }) => ({
  permission: one(permissions, {
    fields: [permissionConditions.permission_id],
    references: [permissions.id],
  }),
}));

// Relations for custom roles
export const customRolesRelations = relations(customRoles, ({ one, many }) => ({
  company: one(companies, {
    fields: [customRoles.company_id],
    references: [companies.id],
  }),
  rolePermissions: many(rolePermissions),
  userRoles: many(userRoles),
  groupRoles: many(groupRoles),
  dataScopeRules: many(dataScopeRules),
  fieldSecurityRules: many(fieldSecurityRules),
  // Role hierarchy relations
  parentRoles: many(roleHierarchy, { relationName: 'childRole' }),
  childRoles: many(roleHierarchy, { relationName: 'parentRole' }),
}));

// Relations for role permissions
export const rolePermissionsRelations = relations(rolePermissions, ({ one }) => ({
  role: one(customRoles, {
    fields: [rolePermissions.role_id],
    references: [customRoles.id],
  }),
  permission: one(permissions, {
    fields: [rolePermissions.permission_id],
    references: [permissions.id],
  }),
}));

// Relations for user roles
export const userRolesRelations = relations(userRoles, ({ one }) => ({
  user: one(users, {
    fields: [userRoles.user_id],
    references: [users.id],
  }),
  role: one(customRoles, {
    fields: [userRoles.role_id],
    references: [customRoles.id],
  }),
}));

// Relations for group roles
export const groupRolesRelations = relations(groupRoles, ({ one }) => ({
  group: one(groups, {
    fields: [groupRoles.group_id],
    references: [groups.id],
  }),
  role: one(customRoles, {
    fields: [groupRoles.role_id],
    references: [customRoles.id],
  }),
}));

// Relations for group users
export const groupUsersRelations = relations(groupUsers, ({ one }) => ({
  group: one(groups, {
    fields: [groupUsers.group_id],
    references: [groups.id],
  }),
  user: one(users, {
    fields: [groupUsers.user_id],
    references: [users.id],
  }),
}));

// Relations for role hierarchy
export const roleHierarchyRelations = relations(roleHierarchy, ({ one }) => ({
  parentRole: one(customRoles, {
    fields: [roleHierarchy.parent_role_id],
    references: [customRoles.id],
    relationName: 'parentRole',
  }),
  childRole: one(customRoles, {
    fields: [roleHierarchy.child_role_id],
    references: [customRoles.id],
    relationName: 'childRole',
  }),
}));

// Relations for role templates (no foreign key relations, standalone table)
export const roleTemplatesRelations = relations(roleTemplates, ({ }) => ({}));

// Relations for departments
export const departmentsRelations = relations(departments, ({ one, many }) => ({
  company: one(companies, {
    fields: [departments.company_id],
    references: [companies.id],
  }),
  branch: one(branches, {
    fields: [departments.branch_id],
    references: [branches.id],
  }),
  manager: one(users, {
    fields: [departments.manager_id],
    references: [users.id],
  }),
  parentDepartment: one(departments, {
    fields: [departments.parent_department_id],
    references: [departments.id],
    relationName: 'parentDepartment'
  }),
  childDepartments: many(departments, { relationName: 'parentDepartment' }),
  users: many(users),
}));

// Relations for data scope rules
export const dataScopeRulesRelations = relations(dataScopeRules, ({ one }) => ({
  role: one(customRoles, {
    fields: [dataScopeRules.role_id],
    references: [customRoles.id],
  }),
}));

// Relations for sensitive field definitions
export const sensitiveFieldDefinitionsRelations = relations(sensitiveFieldDefinitions, ({ many }) => ({
  fieldSecurityRules: many(fieldSecurityRules),
}));

// Relations for field security rules
export const fieldSecurityRulesRelations = relations(fieldSecurityRules, ({ one }) => ({
  role: one(customRoles, {
    fields: [fieldSecurityRules.role_id],
    references: [customRoles.id],
  }),
  sensitiveField: one(sensitiveFieldDefinitions, {
    fields: [fieldSecurityRules.sensitive_field_id],
    references: [sensitiveFieldDefinitions.id],
  }),
}));

// Relations for approval workflow tables
export const approvalWorkflowsRelations = relations(approvalWorkflows, ({ one, many }) => ({
  company: one(companies, {
    fields: [approvalWorkflows.company_id],
    references: [companies.id],
  }),
  createdBy: one(users, {
    fields: [approvalWorkflows.created_by],
    references: [users.id],
  }),
  steps: many(approvalWorkflowSteps),
  instances: many(approvalWorkflowInstances),
  escalationRules: many(approvalEscalationRules),
}));

export const approvalWorkflowStepsRelations = relations(approvalWorkflowSteps, ({ one, many }) => ({
  workflow: one(approvalWorkflows, {
    fields: [approvalWorkflowSteps.workflow_id],
    references: [approvalWorkflows.id],
  }),
  stepInstances: many(approvalWorkflowStepInstances),
}));

export const approvalWorkflowInstancesRelations = relations(approvalWorkflowInstances, ({ one, many }) => ({
  workflow: one(approvalWorkflows, {
    fields: [approvalWorkflowInstances.workflow_id],
    references: [approvalWorkflows.id],
  }),
  company: one(companies, {
    fields: [approvalWorkflowInstances.company_id],
    references: [companies.id],
  }),
  requester: one(users, {
    fields: [approvalWorkflowInstances.requester_id],
    references: [users.id],
  }),
  finalDecisionBy: one(users, {
    fields: [approvalWorkflowInstances.final_decision_by],
    references: [users.id],
  }),
  stepInstances: many(approvalWorkflowStepInstances),
  actions: many(approvalActions),
}));

export const approvalWorkflowStepInstancesRelations = relations(approvalWorkflowStepInstances, ({ one, many }) => ({
  workflowInstance: one(approvalWorkflowInstances, {
    fields: [approvalWorkflowStepInstances.workflow_instance_id],
    references: [approvalWorkflowInstances.id],
  }),
  workflowStep: one(approvalWorkflowSteps, {
    fields: [approvalWorkflowStepInstances.workflow_step_id],
    references: [approvalWorkflowSteps.id],
  }),
  actions: many(approvalActions),
}));

export const approvalActionsRelations = relations(approvalActions, ({ one }) => ({
  workflowInstance: one(approvalWorkflowInstances, {
    fields: [approvalActions.workflow_instance_id],
    references: [approvalWorkflowInstances.id],
  }),
  stepInstance: one(approvalWorkflowStepInstances, {
    fields: [approvalActions.step_instance_id],
    references: [approvalWorkflowStepInstances.id],
  }),
  approver: one(users, {
    fields: [approvalActions.approver_id],
    references: [users.id],
  }),
  delegatedTo: one(users, {
    fields: [approvalActions.delegated_to],
    references: [users.id],
  }),
}));

export const approvalEscalationRulesRelations = relations(approvalEscalationRules, ({ one }) => ({
  company: one(companies, {
    fields: [approvalEscalationRules.company_id],
    references: [companies.id],
  }),
  workflow: one(approvalWorkflows, {
    fields: [approvalEscalationRules.workflow_id],
    references: [approvalWorkflows.id],
  }),
}));

// Company Settings
export const companySettings = pgTable('company_settings', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull().unique(),
  date_format: text('date_format').default('dd-MM-yyyy').notNull(),
  currency_symbol: text('currency_symbol').default('₹').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Company Prefix Settings
export const companyPrefixSettings = pgTable('company_prefix_settings', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull().unique(),
  loan_prefix: text('loan_prefix').notNull(),
  loan_start_number: integer('loan_start_number').default(1).notNull(),
  collection_prefix: text('collection_prefix').notNull(),
  collection_start_number: integer('collection_start_number').default(1).notNull(),
  customer_prefix: text('customer_prefix').notNull(),
  customer_start_number: integer('customer_start_number').default(1).notNull(),
  partner_prefix: text('partner_prefix').notNull(),
  partner_start_number: integer('partner_start_number').default(1).notNull(),
  agent_prefix: text('agent_prefix').notNull(),
  agent_start_number: integer('agent_start_number').default(1).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Create Zod schema for company settings
export const companySettingsSchema = z.object({
  company_id: z.number(),
  date_format: z.string().default('dd-MM-yyyy'),
  currency_symbol: z.string().default('₹'),
});

// Create Zod schema for company prefix settings
export const companyPrefixSettingsSchema = z.object({
  company_id: z.number(),
  loan_prefix: z.string().max(5),
  loan_start_number: z.number().default(1),
  collection_prefix: z.string().max(5),
  collection_start_number: z.number().default(1),
  customer_prefix: z.string().max(5),
  customer_start_number: z.number().default(1),
  partner_prefix: z.string().max(5),
  partner_start_number: z.number().default(1),
  agent_prefix: z.string().max(5),
  agent_start_number: z.number().default(1),
});

export type CompanySettings = z.infer<typeof companySettingsSchema>;
export type CompanyPrefixSettings = z.infer<typeof companyPrefixSettingsSchema>;

// Create insert schema
export const insertCompanySettingsSchema = createInsertSchema(companySettings);
export const insertCompanyPrefixSettingsSchema = createInsertSchema(companyPrefixSettings);

export const companySettingsRelations = relations(companySettings, ({ one }) => ({
  company: one(companies, {
    fields: [companySettings.company_id],
    references: [companies.id],
  }),
}));

export const companyPrefixSettingsRelations = relations(companyPrefixSettings, ({ one }) => ({
  company: one(companies, {
    fields: [companyPrefixSettings.company_id],
    references: [companies.id],
  }),
}));

export const companiesRelations = relations(companies, ({ many, one }) => ({
  users: many(users),
  userCompanies: many(userCompanies),
  collections: many(collections),
  customers: many(customers),
  agents: many(agents),
  loans: many(loans),
  subscriptions: many(subscriptions),
  branches: many(branches),
  groups: many(groups),
  departments: many(departments),
  customRoles: many(customRoles),
  formTemplates: many(formTemplates),
  formSubmissions: many(formSubmissions),
  loanConfigurations: many(loanConfigurations),
  expenses: many(expenses),
  fines: many(fines),
  // Financial management relations
  accounts: many(accounts),
  transactions: many(transactions),
  accountBalances: many(accountBalances),
  accountingPeriods: many(accountingPeriods),
  shareholders: many(shareholders),
  shareholdings: many(shareholdings),
  investmentTransactions: many(investmentTransactions),
  profitDistributions: many(profitDistributions),
  balanceSheets: many(balanceSheets),
  fixedAssets: many(fixedAssets),
  // Settings
  settings: one(companySettings),
  prefixSettings: one(companyPrefixSettings),
}));

// Users
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  email: text('email').notNull(),
  password: text('password').notNull(),
  full_name: text('full_name').notNull(),
  role: userRoleEnum('role').notNull(),
  mobile_number: text('mobile_number'),
  profile_image: text('profile_image'),
  // Organizational structure columns for data scope
  branch_id: integer('branch_id'),
  department_id: integer('department_id'),
  manager_id: integer('manager_id'),
  // Security and authentication columns
  password_reset_token: text('password_reset_token'),
  password_reset_expires: timestamp('password_reset_expires'),
  password_updated_at: timestamp('password_updated_at').defaultNow(),
  email_verified: boolean('email_verified').default(false).notNull(),
  // Account lockout columns
  failed_login_attempts: integer('failed_login_attempts').default(0).notNull(),
  locked_until: timestamp('locked_until'),
  last_login_attempt: timestamp('last_login_attempt'),
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const usersRelations = relations(users, ({ one, many }) => ({
  company: one(companies, {
    fields: [users.company_id],
    references: [companies.id],
  }),
  // Organizational structure relations
  branch: one(branches, {
    fields: [users.branch_id],
    references: [branches.id],
  }),
  department: one(departments, {
    fields: [users.department_id],
    references: [departments.id],
  }),
  manager: one(users, {
    fields: [users.manager_id],
    references: [users.id],
    relationName: 'manager'
  }),
  subordinates: many(users, { relationName: 'manager' }),
  userCompanies: many(userCompanies),
  userRoles: many(userRoles),
  groupUsers: many(groupUsers),
  referrals: many(referrals, { relationName: 'referrer' }),
  formSubmissions: many(formSubmissions, { relationName: 'submitter' }),
  passwordHistory: many(passwordHistory),
}));

export const userCompaniesRelations = relations(userCompanies, ({ one }) => ({
  user: one(users, {
    fields: [userCompanies.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [userCompanies.company_id],
    references: [companies.id],
  }),
}));

// Customers
export const customers = pgTable('customers', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  full_name: text('full_name').notNull(),
  email: text('email'),
  phone: text('phone').notNull(),
  address: text('address'),
  profile_image: text('profile_image'),
  credit_score: integer('credit_score'),
  kyc_verified: boolean('kyc_verified').default(false).notNull(),
  notes: text('notes'),
  customer_reference_code: text('customer_reference_code'),
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const customersRelations = relations(customers, ({ one, many }) => ({
  company: one(companies, {
    fields: [customers.company_id],
    references: [companies.id],
  }),
  loans: many(loans),
  collections: many(collections),
}));

// Agents
export const agents = pgTable('agents', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  commission_rate: numeric('commission_rate', { precision: 5, scale: 2 }).notNull(),
  territory: text('territory'),
  notes: text('notes'),
  agent_reference_code: text('agent_reference_code'),
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const agentsRelations = relations(agents, ({ one, many }) => ({
  company: one(companies, {
    fields: [agents.company_id],
    references: [companies.id],
  }),
  user: one(users, {
    fields: [agents.user_id],
    references: [users.id],
  }),
  collections: many(collections),
}));

// Loans
export const loans = pgTable('loans', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  customer_id: integer('customer_id').references(() => customers.id, { onDelete: 'cascade' }).notNull(),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  interest_rate: numeric('interest_rate', { precision: 5, scale: 2 }).notNull(),
  interest_type: interestTypeEnum('interest_type').notNull(),
  loan_type: loanTypeEnum('loan_type').default('personal').notNull(),
  term: integer('term').notNull(),
  terms_frequency: termsFrequencyEnum('terms_frequency').default('monthly'),
  is_upfront_interest: boolean('is_upfront_interest').default(true).notNull(),
  start_date: timestamp('start_date').notNull(),
  end_date: timestamp('end_date').notNull(),
  status: loanStatusEnum('status').default('active').notNull(),
  payment_frequency: paymentFrequencyEnum('payment_frequency').default('monthly'),
  payment_day: text('payment_day'),
  payment_time_of_day: collectionTimeOfDayEnum('payment_time_of_day'),
  disbursement_amount: numeric('disbursement_amount', { precision: 10, scale: 2 }),
  commission_type: text('commission_type'),
  commission_amount: numeric('commission_amount', { precision: 10, scale: 2 }),
  total_repayment_amount: numeric('total_repayment_amount', { precision: 10, scale: 2 }),
  loan_reference_code: text('loan_reference_code'),
  notes: text('notes'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Payment Schedule for Amortization Plans has been removed

export const loansRelations = relations(loans, ({ one, many }) => ({
  company: one(companies, {
    fields: [loans.company_id],
    references: [companies.id],
  }),
  customer: one(customers, {
    fields: [loans.customer_id],
    references: [customers.id],
  }),
  collections: many(collections),
  formSubmissions: many(formSubmissions),
  fines: many(fines),
}));

// Collections
// Fine Configuration
export const fineConfigurations = pgTable('fine_configurations', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  frequency: termsFrequencyEnum('frequency').notNull(), // daily, weekly, biweekly, monthly
  fine_type: fineTypeEnum('fine_type').default('percentage').notNull(),
  fine_value: numeric('fine_value', { precision: 10, scale: 2 }).notNull(), // percentage or fixed amount
  grace_period_days: integer('grace_period_days').default(0).notNull(),
  active: boolean('active').default(true).notNull(),
  description: text('description'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const fineConfigurationsRelations = relations(fineConfigurations, ({ one }) => ({
  company: one(companies, {
    fields: [fineConfigurations.company_id],
    references: [companies.id],
  }),
}));

// Declaration only - will define after collections to avoid circular references
export const payments = pgTable('payments', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  collection_id: integer('collection_id').references(() => collections.id, { onDelete: 'cascade' }).notNull(),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  payment_date: timestamp('payment_date').defaultNow().notNull(),
  payment_method: paymentMethodEnum('payment_method').notNull(),
  fine_amount: numeric('fine_amount', { precision: 10, scale: 2 }).default('0'),
  receipt_number: text('receipt_number').notNull(),
  transaction_reference: text('transaction_reference'),
  notes: text('notes'),
  recorded_by: integer('recorded_by').references(() => users.id, { onDelete: 'set null' }),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const collections = pgTable('collections', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  loan_id: integer('loan_id').references(() => loans.id, { onDelete: 'cascade' }).notNull(),
  customer_id: integer('customer_id').references(() => customers.id, { onDelete: 'cascade' }).notNull(),
  agent_id: integer('agent_id').references(() => agents.id, { onDelete: 'set null' }),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  original_amount: numeric('original_amount', { precision: 10, scale: 2 }),
  scheduled_date: timestamp('scheduled_date').notNull(),
  collection_date: timestamp('collection_date'),
  status: collectionStatusEnum('status').notNull(),
  payment_method: paymentMethodEnum('payment_method'),
  receipt_id: text('receipt_id'),
  notes: text('notes'),
  time_of_day: collectionTimeOfDayEnum('time_of_day'),
  follow_up_date: timestamp('follow_up_date'),
  proof_of_payment: text('proof_of_payment'),
  remaining_balance: numeric('remaining_balance', { precision: 10, scale: 2 }),
  emi_number: integer('emi_number'),
  priority: integer('priority'),
  fine_amount: numeric('fine_amount', { precision: 10, scale: 2 }).default('0'),
  company_collection_string: text('company_collection_string'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const collectionsRelations = relations(collections, ({ one, many }) => ({
  company: one(companies, {
    fields: [collections.company_id],
    references: [companies.id],
  }),
  loan: one(loans, {
    fields: [collections.loan_id],
    references: [loans.id],
  }),
  customer: one(customers, {
    fields: [collections.customer_id],
    references: [customers.id],
  }),
  agent: one(agents, {
    fields: [collections.agent_id],
    references: [agents.id],
  }),
  payments: many(payments),
}));

// Expenses table for tracking financial outflows
export const expenses = pgTable('expenses', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  branch_id: integer('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  description: text('description').notNull(),
  expense_date: timestamp('expense_date').defaultNow().notNull(),
  expense_type: expenseTypeEnum('expense_type').notNull(),
  created_by: integer('created_by').references(() => users.id),
  reference_number: text('reference_number'),
  payment_method: paymentMethodEnum('payment_method').notNull(),
  notes: text('notes'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Chart of accounts for financial categorization
export const accounts = pgTable('accounts', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').notNull(),
  account_code: text('account_code').notNull(),
  account_name: text('account_name').notNull(),
  account_type: accountTypeEnum('account_type').notNull(),
  description: text('description'),
  parent_account_id: integer('parent_account_id'),
  is_system: boolean('is_system').default(false).notNull(),
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Transactions table for double-entry accounting
export const transactions = pgTable('transactions', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  branch_id: integer('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  transaction_date: timestamp('transaction_date').defaultNow().notNull(),
  account_id: integer('account_id').references(() => accounts.id).notNull(),
  transaction_type: transactionTypeEnum('transaction_type').notNull(),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  reference_type: referenceTypeEnum('reference_type').notNull(),
  reference_id: integer('reference_id').notNull(),
  running_balance: numeric('running_balance', { precision: 12, scale: 2 }),
  description: text('description'),
  transaction_reference_code: text('transaction_reference_code'),
  created_by: integer('created_by').references(() => users.id),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Daily balance tracking
export const accountBalances = pgTable('account_balances', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  account_id: integer('account_id').references(() => accounts.id, { onDelete: 'cascade' }).notNull(),
  balance_date: timestamp('balance_date').notNull(),
  opening_balance: numeric('opening_balance', { precision: 15, scale: 2 }).notNull(),
  closing_balance: numeric('closing_balance', { precision: 15, scale: 2 }).notNull(),
  total_debits: numeric('total_debits', { precision: 15, scale: 2 }).default('0').notNull(),
  total_credits: numeric('total_credits', { precision: 15, scale: 2 }).default('0').notNull(),
  is_reconciled: boolean('is_reconciled').default(false).notNull(),
  reconciled_by: integer('reconciled_by').references(() => users.id),
  reconciled_at: timestamp('reconciled_at'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Period management
export const accountingPeriods = pgTable('accounting_periods', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  period_name: text('period_name').notNull(),
  start_date: timestamp('start_date').notNull(),
  end_date: timestamp('end_date').notNull(),
  status: text('status').default('open').notNull(), // open, closed, locked
  closed_by: integer('closed_by').references(() => users.id),
  closed_at: timestamp('closed_at'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Shareholders table
export const shareholders = pgTable('shareholders', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  shareholder_type: shareholderTypeEnum('shareholder_type').notNull(),
  name: text('name').notNull(),
  email: text('email'),
  phone: text('phone'),
  address: text('address'),
  tax_id: text('tax_id'),
  document_ids: text('document_ids').array(),
  notes: text('notes'),
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Shareholdings table
export const shareholdings = pgTable('shareholdings', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  shareholder_id: integer('shareholder_id').references(() => shareholders.id, { onDelete: 'cascade' }).notNull(),
  shares_owned: numeric('shares_owned', { precision: 15, scale: 5 }).notNull(),
  ownership_percentage: numeric('ownership_percentage', { precision: 6, scale: 3 }).notNull(),
  initial_investment_date: timestamp('initial_investment_date').notNull(),
  total_invested: numeric('total_invested', { precision: 15, scale: 2 }).notNull(),
  total_withdrawn: numeric('total_withdrawn', { precision: 15, scale: 2 }).default('0').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Investment transactions
export const investmentTransactions = pgTable('investment_transactions', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  shareholder_id: integer('shareholder_id').references(() => shareholders.id, { onDelete: 'cascade' }).notNull(),
  transaction_type: investmentTransactionTypeEnum('transaction_type').notNull(),
  amount: numeric('amount', { precision: 15, scale: 2 }).notNull(),
  transaction_date: timestamp('transaction_date').defaultNow().notNull(),
  shares_affected: numeric('shares_affected', { precision: 15, scale: 5 }),
  notes: text('notes'),
  approved_by: integer('approved_by').references(() => users.id),
  approved_at: timestamp('approved_at'),
  status: text('status').default('pending').notNull(), // pending, approved, rejected, completed
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Profit distributions
export const profitDistributions = pgTable('profit_distributions', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  distribution_date: timestamp('distribution_date').notNull(),
  total_amount: numeric('total_amount', { precision: 15, scale: 2 }).notNull(),
  distribution_period_start: timestamp('distribution_period_start').notNull(),
  distribution_period_end: timestamp('distribution_period_end').notNull(),
  status: text('status').default('draft').notNull(), // draft, announced, completed, cancelled
  created_by: integer('created_by').references(() => users.id),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Balance sheets
export const balanceSheets = pgTable('balance_sheets', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  period_start: timestamp('period_start').notNull(),
  period_end: timestamp('period_end').notNull(),
  total_assets: numeric('total_assets', { precision: 15, scale: 2 }).notNull(),
  total_liabilities: numeric('total_liabilities', { precision: 15, scale: 2 }).notNull(),
  total_equity: numeric('total_equity', { precision: 15, scale: 2 }).notNull(),
  status: text('status').default('draft').notNull(), // draft, published, archived
  created_by: integer('created_by').references(() => users.id),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Balance sheet items
export const balanceSheetItems = pgTable('balance_sheet_items', {
  id: serial('id').primaryKey(),
  balance_sheet_id: integer('balance_sheet_id').references(() => balanceSheets.id, { onDelete: 'cascade' }).notNull(),
  account_id: integer('account_id').references(() => accounts.id, { onDelete: 'cascade' }).notNull(),
  item_type: balanceSheetItemTypeEnum('item_type').notNull(),
  amount: numeric('amount', { precision: 15, scale: 2 }).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Fixed assets with depreciation
export const fixedAssets = pgTable('fixed_assets', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  asset_name: text('asset_name').notNull(),
  asset_category: text('asset_category').notNull(),
  purchase_date: timestamp('purchase_date').notNull(),
  purchase_cost: numeric('purchase_cost', { precision: 15, scale: 2 }).notNull(),
  estimated_useful_life: integer('estimated_useful_life').notNull(), // in months
  salvage_value: numeric('salvage_value', { precision: 15, scale: 2 }).default('0').notNull(),
  accumulated_depreciation: numeric('accumulated_depreciation', { precision: 15, scale: 2 }).default('0').notNull(),
  current_value: numeric('current_value', { precision: 15, scale: 2 }).notNull(),
  depreciation_method: text('depreciation_method').default('straight_line').notNull(),
  is_disposed: boolean('is_disposed').default(false).notNull(),
  disposal_date: timestamp('disposal_date'),
  disposal_amount: numeric('disposal_amount', { precision: 15, scale: 2 }),
  asset_account_id: integer('asset_account_id').references(() => accounts.id),
  depreciation_account_id: integer('depreciation_account_id').references(() => accounts.id),
  created_by: integer('created_by').references(() => users.id),
  status: text('status').default('active').notNull(), // active, disposed, archived
  acquisition_date: timestamp('acquisition_date'),
  acquisition_cost: numeric('acquisition_cost', { precision: 15, scale: 2 }),
  gain_loss_on_disposal: numeric('gain_loss_on_disposal', { precision: 15, scale: 2 }),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Depreciation schedule for tracking asset depreciation
export const depreciationSchedules = pgTable('depreciation_schedules', {
  id: serial('id').primaryKey(),
  asset_id: integer('asset_id').references(() => fixedAssets.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  depreciation_date: timestamp('depreciation_date').notNull(),
  depreciation_amount: numeric('depreciation_amount', { precision: 15, scale: 2 }).notNull(),
  accumulated_depreciation: numeric('accumulated_depreciation', { precision: 15, scale: 2 }).notNull(),
  book_value_after: numeric('book_value_after', { precision: 15, scale: 2 }).notNull(),
  period_id: integer('period_id').references(() => accountingPeriods.id),
  created_by: integer('created_by').references(() => users.id),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const expensesRelations = relations(expenses, ({ one }) => ({
  company: one(companies, {
    fields: [expenses.company_id],
    references: [companies.id],
  }),
  branch: one(branches, {
    fields: [expenses.branch_id],
    references: [branches.id],
  }),
  createdBy: one(users, {
    fields: [expenses.created_by],
    references: [users.id],
  }),
}));

export const accountsRelations = relations(accounts, ({ one, many }) => ({
  company: one(companies, {
    fields: [accounts.company_id],
    references: [companies.id],
  }),
  parentAccount: one(accounts, {
    fields: [accounts.parent_account_id],
    references: [accounts.id],
  }),
  childAccounts: many(accounts, { relationName: 'parentAccount' }),
  transactions: many(transactions),
  balances: many(accountBalances),
  balanceSheetItems: many(balanceSheetItems),
}));

export const transactionsRelations = relations(transactions, ({ one }) => ({
  company: one(companies, {
    fields: [transactions.company_id],
    references: [companies.id],
  }),
  branch: one(branches, {
    fields: [transactions.branch_id],
    references: [branches.id],
  }),
  account: one(accounts, {
    fields: [transactions.account_id],
    references: [accounts.id],
  }),
  createdBy: one(users, {
    fields: [transactions.created_by],
    references: [users.id],
  }),
}));

export const accountBalancesRelations = relations(accountBalances, ({ one }) => ({
  company: one(companies, {
    fields: [accountBalances.company_id],
    references: [companies.id],
  }),
  account: one(accounts, {
    fields: [accountBalances.account_id],
    references: [accounts.id],
  }),
  reconciledBy: one(users, {
    fields: [accountBalances.reconciled_by],
    references: [users.id],
  }),
}));

export const accountingPeriodsRelations = relations(accountingPeriods, ({ one }) => ({
  company: one(companies, {
    fields: [accountingPeriods.company_id],
    references: [companies.id],
  }),
  closedBy: one(users, {
    fields: [accountingPeriods.closed_by],
    references: [users.id],
  }),
}));

export const shareholdersRelations = relations(shareholders, ({ one, many }) => ({
  company: one(companies, {
    fields: [shareholders.company_id],
    references: [companies.id],
  }),
  shareholdings: many(shareholdings),
  investmentTransactions: many(investmentTransactions),
}));

export const shareholdingsRelations = relations(shareholdings, ({ one }) => ({
  company: one(companies, {
    fields: [shareholdings.company_id],
    references: [companies.id],
  }),
  shareholder: one(shareholders, {
    fields: [shareholdings.shareholder_id],
    references: [shareholders.id],
  }),
}));

export const investmentTransactionsRelations = relations(investmentTransactions, ({ one }) => ({
  company: one(companies, {
    fields: [investmentTransactions.company_id],
    references: [companies.id],
  }),
  shareholder: one(shareholders, {
    fields: [investmentTransactions.shareholder_id],
    references: [shareholders.id],
  }),
  approvedBy: one(users, {
    fields: [investmentTransactions.approved_by],
    references: [users.id],
  }),
}));

export const profitDistributionsRelations = relations(profitDistributions, ({ one }) => ({
  company: one(companies, {
    fields: [profitDistributions.company_id],
    references: [companies.id],
  }),
  createdBy: one(users, {
    fields: [profitDistributions.created_by],
    references: [users.id],
  }),
}));

export const balanceSheetsRelations = relations(balanceSheets, ({ one, many }) => ({
  company: one(companies, {
    fields: [balanceSheets.company_id],
    references: [companies.id],
  }),
  createdBy: one(users, {
    fields: [balanceSheets.created_by],
    references: [users.id],
  }),
  items: many(balanceSheetItems),
}));

export const balanceSheetItemsRelations = relations(balanceSheetItems, ({ one }) => ({
  balanceSheet: one(balanceSheets, {
    fields: [balanceSheetItems.balance_sheet_id],
    references: [balanceSheets.id],
  }),
  account: one(accounts, {
    fields: [balanceSheetItems.account_id],
    references: [accounts.id],
  }),
}));

export const fixedAssetsRelations = relations(fixedAssets, ({ one, many }) => ({
  company: one(companies, {
    fields: [fixedAssets.company_id],
    references: [companies.id],
  }),
  assetAccount: one(accounts, {
    fields: [fixedAssets.asset_account_id],
    references: [accounts.id],
  }),
  depreciationAccount: one(accounts, {
    fields: [fixedAssets.depreciation_account_id],
    references: [accounts.id],
  }),
  createdBy: one(users, {
    fields: [fixedAssets.created_by],
    references: [users.id],
  }),
  depreciationSchedules: many(depreciationSchedules),
}));

export const depreciationSchedulesRelations = relations(depreciationSchedules, ({ one }) => ({
  asset: one(fixedAssets, {
    fields: [depreciationSchedules.asset_id],
    references: [fixedAssets.id],
  }),
  company: one(companies, {
    fields: [depreciationSchedules.company_id],
    references: [companies.id],
  }),
  period: one(accountingPeriods, {
    fields: [depreciationSchedules.period_id],
    references: [accountingPeriods.id],
  }),
  createdBy: one(users, {
    fields: [depreciationSchedules.created_by],
    references: [users.id],
  }),
}));

// Now define payment relations after collections is defined
// Fines table for tracking penalty income separately
export const fines = pgTable('fines', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  loan_id: integer('loan_id').references(() => loans.id, { onDelete: 'cascade' }).notNull(),
  payment_id: integer('payment_id').references(() => payments.id, { onDelete: 'cascade' }),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  fine_date: timestamp('fine_date').defaultNow().notNull(),
  fine_type: fineTypeEnum('fine_type').notNull(),
  is_paid: boolean('is_paid').default(false).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const finesRelations = relations(fines, ({ one }) => ({
  company: one(companies, {
    fields: [fines.company_id],
    references: [companies.id],
  }),
  loan: one(loans, {
    fields: [fines.loan_id],
    references: [loans.id],
  }),
  payment: one(payments, {
    fields: [fines.payment_id],
    references: [payments.id],
  }),
}));

export const paymentsRelations = relations(payments, ({ one }) => ({
  company: one(companies, {
    fields: [payments.company_id],
    references: [companies.id],
  }),
  collection: one(collections, {
    fields: [payments.collection_id],
    references: [collections.id],
  }),
  recordedBy: one(users, {
    fields: [payments.recorded_by],
    references: [users.id],
  }),
}));

// Resellers
export const resellers = pgTable('resellers', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').notNull(),
  company_name: text('company_name').notNull(),
  address: text('address'),
  phone: text('phone').notNull(),
  website: text('website'),
  commission_rate: numeric('commission_rate', { precision: 5, scale: 2 }).notNull(),
  parent_reseller_id: integer('parent_reseller_id'),
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const resellersRelations = relations(resellers, ({ one, many }) => ({
  user: one(users, {
    fields: [resellers.user_id],
    references: [users.id],
  }),
  parentReseller: one(resellers, {
    fields: [resellers.parent_reseller_id],
    references: [resellers.id],
  }),
  subResellers: many(resellers, { relationName: 'parent' }),
  clients: many(resellerClients),
  commissions: many(resellerCommissions),
}));

// Reseller clients
export const resellerClients = pgTable('reseller_clients', {
  id: serial('id').primaryKey(),
  reseller_id: integer('reseller_id').references(() => resellers.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const resellerClientsRelations = relations(resellerClients, ({ one }) => ({
  reseller: one(resellers, {
    fields: [resellerClients.reseller_id],
    references: [resellers.id],
  }),
  company: one(companies, {
    fields: [resellerClients.company_id],
    references: [companies.id],
  }),
}));

// Reseller commissions
export const resellerCommissions = pgTable('reseller_commissions', {
  id: serial('id').primaryKey(),
  reseller_id: integer('reseller_id').references(() => resellers.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  payment_date: timestamp('payment_date'),
  status: text('status').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const resellerCommissionsRelations = relations(resellerCommissions, ({ one }) => ({
  reseller: one(resellers, {
    fields: [resellerCommissions.reseller_id],
    references: [resellers.id],
  }),
  company: one(companies, {
    fields: [resellerCommissions.company_id],
    references: [companies.id],
  }),
}));

// Subscription Plans
export const subscriptionPlans = pgTable('subscription_plans', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  price: numeric('price', { precision: 10, scale: 2 }).notNull(),
  billing_period: billingCycleEnum('billing_period').notNull(),
  features: text('features').array(),
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Subscriptions
export const subscriptions = pgTable('subscriptions', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  plan_id: integer('plan_id').references(() => subscriptionPlans.id, { onDelete: 'restrict' }).notNull(),
  status: subscriptionStatusEnum('status').notNull(),
  start_date: timestamp('start_date').notNull(),
  end_date: timestamp('end_date').notNull(),
  billing_cycle: billingCycleEnum('billing_cycle').notNull(),
  last_payment_date: timestamp('last_payment_date'),
  next_payment_date: timestamp('next_payment_date').notNull(),
  payment_method: subscriptionPaymentMethodEnum('payment_method').notNull(),
  auto_renew: boolean('auto_renew').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Adding relations after both tables are defined to avoid circular references
export const subscriptionPlansRelations = relations(subscriptionPlans, ({ many }) => ({
  subscriptions: many(subscriptions),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
  company: one(companies, {
    fields: [subscriptions.company_id],
    references: [companies.id],
  }),
  plan: one(subscriptionPlans, {
    fields: [subscriptions.plan_id],
    references: [subscriptionPlans.id],
  }),
}));

// Referrals
export const referrals = pgTable('referrals', {
  id: serial('id').primaryKey(),
  referrer_id: integer('referrer_id').references(() => users.id, { onDelete: 'set null' }),
  referral_code: text('referral_code').notNull(),
  referral_email: text('referral_email'),
  status: text('status').notNull(),
  converted_company_id: integer('converted_company_id').references(() => companies.id, { onDelete: 'set null' }),
  commission_amount: numeric('commission_amount', { precision: 10, scale: 2 }),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const referralsRelations = relations(referrals, ({ one }) => ({
  referrer: one(users, {
    fields: [referrals.referrer_id],
    references: [users.id],
    relationName: 'referrer',
  }),
  convertedCompany: one(companies, {
    fields: [referrals.converted_company_id],
    references: [companies.id],
  }),
}));



// Partners
export const partnerTypeEnum = pgEnum('partner_type', [
  'investor',
  'strategic',
  'technology',
  'distribution'
]);

export const partners = pgTable('partners', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  type: partnerTypeEnum('type').notNull(),
  email: text('email'),
  phone: text('phone'),
  address: text('address'),
  website: text('website'),
  contact_person: text('contact_person'),
  investment_amount: numeric('investment_amount', { precision: 10, scale: 2 }),
  partnership_start_date: timestamp('partnership_start_date'),
  partnership_end_date: timestamp('partnership_end_date'),
  agreement_details: text('agreement_details'),
  notes: text('notes'),
  partner_reference_code: text('partner_reference_code'),
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const partnersRelations = relations(partners, ({ one }) => ({
  company: one(companies, {
    fields: [partners.company_id],
    references: [companies.id],
  }),
}));

// Dynamic Form Templates
export const formTemplates = pgTable('form_templates', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  branch_id: integer('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  name: text('name').notNull(),
  description: text('description'),
  category: text('category'),
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const formTemplatesRelations = relations(formTemplates, ({ one, many }) => ({
  company: one(companies, {
    fields: [formTemplates.company_id],
    references: [companies.id],
  }),
  branch: one(branches, {
    fields: [formTemplates.branch_id],
    references: [branches.id],
  }),
  fields: many(formFields),
  submissions: many(formSubmissions),
  loanConfigurations: many(loanConfigurations),
}));

// Loan Configuration - Maps form templates for loan creation
export const loanConfigurations = pgTable('loan_configurations', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  template_id: integer('template_id').references(() => formTemplates.id, { onDelete: 'cascade' }).notNull(),
  branch_id: integer('branch_id').references(() => branches.id, { onDelete: 'cascade' }),
  is_active: boolean('is_active').default(true).notNull(),
  order: integer('order').default(0),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const loanConfigurationsRelations = relations(loanConfigurations, ({ one }) => ({
  company: one(companies, {
    fields: [loanConfigurations.company_id],
    references: [companies.id],
  }),
  template: one(formTemplates, {
    fields: [loanConfigurations.template_id],
    references: [formTemplates.id],
  }),
  branch: one(branches, {
    fields: [loanConfigurations.branch_id],
    references: [branches.id],
  }),
}));

// Form Fields
export const formFields = pgTable('form_fields', {
  id: serial('id').primaryKey(),
  template_id: integer('template_id').references(() => formTemplates.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  label: text('label').notNull(),
  field_type: fieldTypeEnum('field_type').notNull(),
  is_required: boolean('is_required').default(false).notNull(),
  order: integer('order').notNull(),
  placeholder: text('placeholder'),
  help_text: text('help_text'),
  default_value: text('default_value'),
  options: jsonb('options'), // For select, multiselect, checkbox, radio options
  validation_rules: jsonb('validation_rules'), // JSON for min, max, regex, etc.
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const formFieldsRelations = relations(formFields, ({ one }) => ({
  template: one(formTemplates, {
    fields: [formFields.template_id],
    references: [formTemplates.id],
  }),
}));

// Form Submissions
export const formSubmissions = pgTable('form_submissions', {
  id: serial('id').primaryKey(),
  template_id: integer('template_id').references(() => formTemplates.id, { onDelete: 'set null' }),
  loan_id: integer('loan_id').references(() => loans.id, { onDelete: 'cascade' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  submitted_by: integer('submitted_by').references(() => users.id, { onDelete: 'set null' }),
  form_data: jsonb('form_data').notNull(), // Stores all form field values
  file_references: jsonb('file_references'), // Stores paths or reference IDs to uploaded files
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

export const formSubmissionsRelations = relations(formSubmissions, ({ one }) => ({
  template: one(formTemplates, {
    fields: [formSubmissions.template_id],
    references: [formTemplates.id],
  }),
  loan: one(loans, {
    fields: [formSubmissions.loan_id],
    references: [loans.id],
  }),
  company: one(companies, {
    fields: [formSubmissions.company_id],
    references: [companies.id],
  }),
  submitter: one(users, {
    fields: [formSubmissions.submitted_by],
    references: [users.id],
  }),
}));

// Insert schemas for all tables
export const insertCompanySchema = createInsertSchema(companies).omit({ id: true, created_at: true, updated_at: true });
export const insertUserSchema = createInsertSchema(users).omit({ id: true, created_at: true, updated_at: true });
export const insertUserCompanySchema = createInsertSchema(userCompanies).omit({ id: true, created_at: true });
export const insertCustomerSchema = createInsertSchema(customers).omit({ id: true, created_at: true, updated_at: true });
export const insertAgentSchema = createInsertSchema(agents)
  .omit({ id: true, created_at: true, updated_at: true })
  .extend({
    commission_rate: z.union([
      z.number(),
      z.string().transform(val => Number(val))
    ]),
    agent_reference_code: z.string().optional()
  });
// Create the base schema first
const baseLoanSchema = createInsertSchema(loans).omit({ id: true, created_at: true, updated_at: true });

// Modify it to handle date string conversions
export const insertLoanSchema = baseLoanSchema.extend({
  start_date: z.preprocess(
    (arg) => {
      if (typeof arg === 'string') return new Date(arg);
      return arg;
    },
    z.date()
  ),
  end_date: z.preprocess(
    (arg) => {
      if (typeof arg === 'string') return new Date(arg);
      return arg;
    },
    z.date()
  ),
});
export const insertCollectionSchema = createInsertSchema(collections)
  .omit({ id: true, created_at: true, updated_at: true })
  .extend({
    company_collection_string: z.string().optional(),
    scheduled_date: z.preprocess(
      (arg) => {
        if (typeof arg === 'string') return new Date(arg);
        return arg;
      },
      z.date()
    ),
    collection_date: z.preprocess(
      (arg) => {
        if (arg === null) return null;
        if (typeof arg === 'string') return new Date(arg);
        return arg;
      },
      z.date().nullable()
    ),
    follow_up_date: z.preprocess(
      (arg) => {
        if (arg === null) return null;
        if (typeof arg === 'string') return new Date(arg);
        return arg;
      },
      z.date().nullable()
    ),
  });
export const insertFineConfigurationSchema = createInsertSchema(fineConfigurations).omit({ id: true, created_at: true, updated_at: true });
// Use direct schema definition for payments to avoid drizzle-zod issues
export const insertPaymentSchema = z.object({
  company_id: z.number(),
  collection_id: z.number(),
  amount: z.string(),
  payment_date: z.date().optional(),
  payment_method: z.enum(['cash', 'upi', 'bank_transfer', 'cheque', 'card_payment', 'online_transfer', 'mobile_wallet']),
  fine_amount: z.string().optional(),
  receipt_number: z.string(),
  transaction_reference: z.string().optional(),
  notes: z.string().optional(),
  recorded_by: z.number().optional(),
});
export const insertSubscriptionPlanSchema = createInsertSchema(subscriptionPlans).omit({ id: true, created_at: true, updated_at: true });
export const insertSubscriptionSchema = createInsertSchema(subscriptions).omit({ id: true, created_at: true, updated_at: true });
export const insertResellerSchema = createInsertSchema(resellers).omit({ id: true, created_at: true, updated_at: true });
export const insertResellerClientSchema = createInsertSchema(resellerClients).omit({ id: true, created_at: true, updated_at: true });
export const insertResellerCommissionSchema = createInsertSchema(resellerCommissions).omit({ id: true, created_at: true, updated_at: true });
export const insertReferralSchema = createInsertSchema(referrals).omit({ id: true, created_at: true, updated_at: true });
export const insertPartnerSchema = createInsertSchema(partners)
  .omit({ id: true, created_at: true, updated_at: true })
  .extend({
    partner_reference_code: z.string().optional()
  });
export const insertBranchSchema = createInsertSchema(branches).omit({ id: true, created_at: true, updated_at: true });
export const insertGroupSchema = createInsertSchema(groups).omit({ id: true, created_at: true, updated_at: true });
export const insertPermissionSchema = createInsertSchema(permissions).omit({ id: true, created_at: true, updated_at: true });
export const insertPermissionConditionSchema = createInsertSchema(permissionConditions).omit({ id: true, created_at: true, updated_at: true });
export const insertCustomRoleSchema = createInsertSchema(customRoles).omit({ id: true, created_at: true, updated_at: true });
export const insertRolePermissionSchema = createInsertSchema(rolePermissions).omit({ id: true, created_at: true });
export const insertUserRoleSchema = createInsertSchema(userRoles).omit({ id: true, created_at: true });
export const insertGroupRoleSchema = createInsertSchema(groupRoles).omit({ id: true, created_at: true });
export const insertGroupUserSchema = createInsertSchema(groupUsers).omit({ id: true, created_at: true });
export const insertRoleHierarchySchema = createInsertSchema(roleHierarchy).omit({ id: true, created_at: true, updated_at: true });
export const insertRoleTemplateSchema = createInsertSchema(roleTemplates).omit({ id: true, created_at: true, updated_at: true });
export const insertTemporaryPermissionSchema = createInsertSchema(temporaryPermissions).omit({ id: true, created_at: true, updated_at: true });
export const insertDepartmentSchema = createInsertSchema(departments).omit({ id: true, created_at: true, updated_at: true });
export const insertDataScopeRuleSchema = createInsertSchema(dataScopeRules).omit({ id: true, created_at: true, updated_at: true });
export const insertSensitiveFieldDefinitionSchema = createInsertSchema(sensitiveFieldDefinitions).omit({ id: true, created_at: true, updated_at: true });
export const insertFieldSecurityRuleSchema = createInsertSchema(fieldSecurityRules).omit({ id: true, created_at: true, updated_at: true });
export const insertPermissionElevationRequestSchema = createInsertSchema(permissionElevationRequests).omit({ id: true, created_at: true, updated_at: true });
export const insertEmergencyAccessLogSchema = createInsertSchema(emergencyAccessLogs).omit({ id: true, created_at: true });

// Approval workflow insert schemas
export const insertApprovalWorkflowSchema = createInsertSchema(approvalWorkflows).omit({ id: true, created_at: true, updated_at: true });
export const insertApprovalWorkflowStepSchema = createInsertSchema(approvalWorkflowSteps).omit({ id: true, created_at: true, updated_at: true });
export const insertApprovalWorkflowInstanceSchema = createInsertSchema(approvalWorkflowInstances).omit({ id: true, created_at: true, updated_at: true });
export const insertApprovalWorkflowStepInstanceSchema = createInsertSchema(approvalWorkflowStepInstances).omit({ id: true, created_at: true, updated_at: true });
export const insertApprovalActionSchema = createInsertSchema(approvalActions).omit({ id: true, created_at: true });
export const insertApprovalEscalationRuleSchema = createInsertSchema(approvalEscalationRules).omit({ id: true, created_at: true, updated_at: true });

export const insertFormTemplateSchema = createInsertSchema(formTemplates).omit({ id: true, created_at: true, updated_at: true });
export const insertFormFieldSchema = createInsertSchema(formFields).omit({ id: true, created_at: true, updated_at: true });
export const insertFormSubmissionSchema = createInsertSchema(formSubmissions).omit({ id: true, created_at: true, updated_at: true });
export const insertLoanConfigurationSchema = createInsertSchema(loanConfigurations).omit({ id: true, created_at: true, updated_at: true });
export const insertExpenseSchema = createInsertSchema(expenses)
  .omit({ id: true, created_at: true, updated_at: true })
  .extend({
    amount: z.string().or(z.number()).transform(val => String(val)),
    expense_date: z.string().or(z.date()).transform(val => {
      if (val instanceof Date) {
        return val.toISOString();
      }
      return val;
    }),
  });
export const insertFineSchema = createInsertSchema(fines).omit({ id: true, created_at: true, updated_at: true });

// Financial management insert schemas
export const insertAccountSchema = createInsertSchema(accounts).omit({ id: true, created_at: true, updated_at: true });
export const insertTransactionSchema = createInsertSchema(transactions)
  .omit({ id: true, created_at: true, updated_at: true })
  .extend({
    transaction_reference_code: z.string().optional()
  });
export const insertAccountBalanceSchema = createInsertSchema(accountBalances).omit({ id: true, created_at: true, updated_at: true });
export const insertAccountingPeriodSchema = createInsertSchema(accountingPeriods).omit({ id: true, created_at: true, updated_at: true });
export const insertShareholderSchema = createInsertSchema(shareholders).omit({ id: true, created_at: true, updated_at: true });
export const insertShareholdingSchema = createInsertSchema(shareholdings).omit({ id: true, created_at: true, updated_at: true });
export const insertInvestmentTransactionSchema = createInsertSchema(investmentTransactions).omit({ id: true, created_at: true, updated_at: true });
export const insertProfitDistributionSchema = createInsertSchema(profitDistributions).omit({ id: true, created_at: true, updated_at: true });
export const insertBalanceSheetSchema = createInsertSchema(balanceSheets).omit({ id: true, created_at: true, updated_at: true });
export const insertBalanceSheetItemSchema = createInsertSchema(balanceSheetItems).omit({ id: true, created_at: true, updated_at: true });
export const insertFixedAssetSchema = createInsertSchema(fixedAssets).omit({ id: true, created_at: true, updated_at: true });

// Type definitions for insert schemas
export type InsertExpense = z.infer<typeof insertExpenseSchema>;
export type InsertFine = z.infer<typeof insertFineSchema>;
export type InsertAccount = z.infer<typeof insertAccountSchema>;
export type InsertTransaction = z.infer<typeof insertTransactionSchema>;
export type InsertAccountBalance = z.infer<typeof insertAccountBalanceSchema>;
export type InsertAccountingPeriod = z.infer<typeof insertAccountingPeriodSchema>;
export type InsertShareholder = z.infer<typeof insertShareholderSchema>;
export type InsertShareholding = z.infer<typeof insertShareholdingSchema>;
export type InsertInvestmentTransaction = z.infer<typeof insertInvestmentTransactionSchema>;
export type InsertProfitDistribution = z.infer<typeof insertProfitDistributionSchema>;
export type InsertBalanceSheet = z.infer<typeof insertBalanceSheetSchema>;
export type InsertBalanceSheetItem = z.infer<typeof insertBalanceSheetItemSchema>;
export type InsertFixedAsset = z.infer<typeof insertFixedAssetSchema>;

// Type definitions for select schemas
export type Expense = typeof expenses.$inferSelect;
export type Fine = typeof fines.$inferSelect;
export type Account = typeof accounts.$inferSelect;
export type Transaction = typeof transactions.$inferSelect;
export type AccountBalance = typeof accountBalances.$inferSelect;
export type AccountingPeriod = typeof accountingPeriods.$inferSelect;
export type Shareholder = typeof shareholders.$inferSelect;
export type Shareholding = typeof shareholdings.$inferSelect;
export type InvestmentTransaction = typeof investmentTransactions.$inferSelect;
export type ProfitDistribution = typeof profitDistributions.$inferSelect;
export type BalanceSheet = typeof balanceSheets.$inferSelect;
export type BalanceSheetItem = typeof balanceSheetItems.$inferSelect;
export type FixedAsset = typeof fixedAssets.$inferSelect;

// Login schema
export const loginSchema = z.object({
  email: z.string().email({ message: 'Valid email is required' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

// Registration schema with password confirmation
export const registerSchema = insertUserSchema.extend({
  confirmPassword: z.string().min(1, { message: 'Confirm password is required' }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

// Export types
export type Company = typeof companies.$inferSelect;
export type InsertCompany = z.infer<typeof insertCompanySchema>;

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Customer = typeof customers.$inferSelect;
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;

export type Agent = typeof agents.$inferSelect;
export type InsertAgent = z.infer<typeof insertAgentSchema>;

export type Loan = typeof loans.$inferSelect;
export type InsertLoan = z.infer<typeof insertLoanSchema>;

export type Collection = typeof collections.$inferSelect;
export type InsertCollection = z.infer<typeof insertCollectionSchema>;

export type Reseller = typeof resellers.$inferSelect;
export type InsertReseller = z.infer<typeof insertResellerSchema>;

export type ResellerClient = typeof resellerClients.$inferSelect;
export type InsertResellerClient = z.infer<typeof insertResellerClientSchema>;

export type ResellerCommission = typeof resellerCommissions.$inferSelect;
export type InsertResellerCommission = z.infer<typeof insertResellerCommissionSchema>;

export type SubscriptionPlan = typeof subscriptionPlans.$inferSelect;
export type InsertSubscriptionPlan = z.infer<typeof insertSubscriptionPlanSchema>;

export type Subscription = typeof subscriptions.$inferSelect;
export type InsertSubscription = z.infer<typeof insertSubscriptionSchema>;

export type UserCompany = typeof userCompanies.$inferSelect;
export type InsertUserCompany = z.infer<typeof insertUserCompanySchema>;

export type Referral = typeof referrals.$inferSelect;
export type InsertReferral = z.infer<typeof insertReferralSchema>;

export type Partner = typeof partners.$inferSelect;
export type InsertPartner = z.infer<typeof insertPartnerSchema>;

export type Branch = typeof branches.$inferSelect;
export type InsertBranch = z.infer<typeof insertBranchSchema>;

export type Group = typeof groups.$inferSelect;
export type InsertGroup = z.infer<typeof insertGroupSchema>;

export type Permission = typeof permissions.$inferSelect;
export type InsertPermission = z.infer<typeof insertPermissionSchema>;

export type PermissionCondition = typeof permissionConditions.$inferSelect;
export type InsertPermissionCondition = z.infer<typeof insertPermissionConditionSchema>;

export type CustomRole = typeof customRoles.$inferSelect;
export type InsertCustomRole = z.infer<typeof insertCustomRoleSchema>;

export type RolePermission = typeof rolePermissions.$inferSelect;
export type InsertRolePermission = z.infer<typeof insertRolePermissionSchema>;

export type UserRole = typeof userRoles.$inferSelect;
export type InsertUserRole = z.infer<typeof insertUserRoleSchema>;

export type GroupRole = typeof groupRoles.$inferSelect;
export type InsertGroupRole = z.infer<typeof insertGroupRoleSchema>;

export type GroupUser = typeof groupUsers.$inferSelect;
export type InsertGroupUser = z.infer<typeof insertGroupUserSchema>;

export type RoleHierarchy = typeof roleHierarchy.$inferSelect;
export type InsertRoleHierarchy = z.infer<typeof insertRoleHierarchySchema>;

export type RoleTemplate = typeof roleTemplates.$inferSelect;
export type InsertRoleTemplate = z.infer<typeof insertRoleTemplateSchema>;

export type TemporaryPermission = typeof temporaryPermissions.$inferSelect;
export type InsertTemporaryPermission = z.infer<typeof insertTemporaryPermissionSchema>;

export type Department = typeof departments.$inferSelect;
export type InsertDepartment = z.infer<typeof insertDepartmentSchema>;

export type DataScopeRule = typeof dataScopeRules.$inferSelect;
export type InsertDataScopeRule = z.infer<typeof insertDataScopeRuleSchema>;

export type SensitiveFieldDefinition = typeof sensitiveFieldDefinitions.$inferSelect;
export type InsertSensitiveFieldDefinition = z.infer<typeof insertSensitiveFieldDefinitionSchema>;

export type FieldSecurityRule = typeof fieldSecurityRules.$inferSelect;
export type InsertFieldSecurityRule = z.infer<typeof insertFieldSecurityRuleSchema>;

export type PermissionElevationRequest = typeof permissionElevationRequests.$inferSelect;
export type InsertPermissionElevationRequest = z.infer<typeof insertPermissionElevationRequestSchema>;

export type EmergencyAccessLog = typeof emergencyAccessLogs.$inferSelect;
export type InsertEmergencyAccessLog = z.infer<typeof insertEmergencyAccessLogSchema>;

// Approval workflow types
export type ApprovalWorkflow = typeof approvalWorkflows.$inferSelect;
export type InsertApprovalWorkflow = z.infer<typeof insertApprovalWorkflowSchema>;

export type ApprovalWorkflowStep = typeof approvalWorkflowSteps.$inferSelect;
export type InsertApprovalWorkflowStep = z.infer<typeof insertApprovalWorkflowStepSchema>;

export type ApprovalWorkflowInstance = typeof approvalWorkflowInstances.$inferSelect;
export type InsertApprovalWorkflowInstance = z.infer<typeof insertApprovalWorkflowInstanceSchema>;

export type ApprovalWorkflowStepInstance = typeof approvalWorkflowStepInstances.$inferSelect;
export type InsertApprovalWorkflowStepInstance = z.infer<typeof insertApprovalWorkflowStepInstanceSchema>;

export type ApprovalAction = typeof approvalActions.$inferSelect;
export type InsertApprovalAction = z.infer<typeof insertApprovalActionSchema>;

export type ApprovalEscalationRule = typeof approvalEscalationRules.$inferSelect;
export type InsertApprovalEscalationRule = z.infer<typeof insertApprovalEscalationRuleSchema>;

// Enum types for approval workflow
export type ApprovalWorkflowType = typeof approvalWorkflowTypeEnum.enumValues[number];
export type ApprovalStepType = typeof approvalStepTypeEnum.enumValues[number];
export type ApprovalActionType = typeof approvalActionEnum.enumValues[number];
export type WorkflowStatus = typeof workflowStatusEnum.enumValues[number];
export type StepStatus = typeof stepStatusEnum.enumValues[number];

export type FormTemplate = typeof formTemplates.$inferSelect;
export type InsertFormTemplate = z.infer<typeof insertFormTemplateSchema>;

export type FormField = typeof formFields.$inferSelect;
export type InsertFormField = z.infer<typeof insertFormFieldSchema>;

export type FormSubmission = typeof formSubmissions.$inferSelect;
export type InsertFormSubmission = z.infer<typeof insertFormSubmissionSchema>;

export type LoanConfiguration = typeof loanConfigurations.$inferSelect;
export type InsertLoanConfiguration = z.infer<typeof insertLoanConfigurationSchema>;

export type FineConfiguration = typeof fineConfigurations.$inferSelect;
export type InsertFineConfiguration = z.infer<typeof insertFineConfigurationSchema>;

export type Payment = typeof payments.$inferSelect;
export type InsertPayment = typeof payments.$inferInsert;

export type Login = z.infer<typeof loginSchema>;
export type Register = z.infer<typeof registerSchema>;

// Report types
export interface DailyCollectionItem {
  id: number;
  loan_id: number;
  customer_id: number;
  agent_id: number | null;
  amount: string;
  scheduled_date: Date;
  collection_date: Date | null;
  status: string;
  payment_method: string | null;
  customerName: string;
  agentName: string | null;
  receipt_id: string | null;
}

export interface DailyGroupedData {
  date: string;
  collections: DailyCollectionItem[];
  totalAmount: number;
  completedAmount: number;
  pendingAmount: number;
}

export interface DailyCollectionReport {
  startDate: string;
  endDate: string;
  totalCollected: number;
  totalPending: number;
  dailyData: DailyGroupedData[];
  rawData: DailyCollectionItem[];
}

export interface DaySheetTransaction {
  id: number;
  type: 'collection' | 'expense' | 'loan_disbursement';
  amount: number;
  description: string;
  date: Date;
  reference: string | null;
  entity_name: string;
  payment_method: string;
}

export interface DaySheetReport {
  date: string;
  openingBalance: number;
  closingBalance: number;
  totalInflow: number;
  totalOutflow: number;
  transactions: DaySheetTransaction[];
  collectionsSummary: {
    total: number;
    completed: number;
    pending: number;
  };
  expensesSummary: {
    [category: string]: number;
  };
}

export interface CustomerLoan {
  id: number;
  amount: string;
  interest_rate: string;
  start_date: Date;
  end_date: Date;
  status: string;
  disbursement_amount: string | null;
  total_repayment_amount: string | null;
}

export interface CustomerPayment {
  id: number;
  amount: string;
  payment_date: Date;
  payment_method: string;
  collection_id: number;
  status: string;
  scheduled_date: Date;
}

export interface CustomerReport {
  customer: Customer;
  loans: CustomerLoan[];
  payments: CustomerPayment[];
  summary: {
    totalLoans: number;
    activeLoans: number;
    totalBorrowed: number;
    totalRepaid: number;
    totalOutstanding: number;
    onTimePaymentRate: number;
    averageDaysLate: number;
  };
}

export interface AgentCollection {
  id: number;
  loan_id: number;
  customer_id: number;
  amount: string;
  scheduled_date: Date;
  collection_date: Date | null;
  status: string;
  customerName: string;
}

export interface AgentPerformanceMetric {
  date: string;
  collections: number;
  amount: number;
  efficiency: number;
}

export interface AgentReport {
  agent: Agent & { user: User };
  collections: AgentCollection[];
  summary: {
    totalCollections: number;
    totalAmount: number;
    completedCollections: number;
    completedAmount: number;
    pendingCollections: number;
    pendingAmount: number;
    efficiencyRate: number;
    commission: number;
  };
  performanceTrend: AgentPerformanceMetric[];
}

export interface IncomeBreakdown {
  interestIncome: number;
  fineIncome: number;
  processingFeeIncome: number;
  otherIncome: number;
}

export interface ExpenseCategory {
  category: string;
  amount: number;
}

export interface ProfitLossReport {
  startDate: string;
  endDate: string;
  totalIncome: number;
  totalExpenses: number;
  netProfit: number;
  incomeBreakdown: IncomeBreakdown;
  expenseBreakdown: ExpenseCategory[];
  monthlyData: {
    month: string;
    income: number;
    expenses: number;
    profit: number;
  }[];
}

export interface ExpenseFilters {
  startDate?: string;
  endDate?: string;
  type?: string;
  branchId?: number;
  minAmount?: number;
  maxAmount?: number;
  paymentMethod?: string;
}

// Financial Management Report Interfaces
export interface AccountBalanceReport {
  accountId: number;
  accountName: string;
  accountCode: string;
  accountType: string;
  openingBalance: number;
  totalDebits: number;
  totalCredits: number;
  closingBalance: number;
}

export interface AccountStatement {
  account: Account;
  startDate: string;
  endDate: string;
  openingBalance: number;
  closingBalance: number;
  transactions: Transaction[];
}

export interface BalanceSheetDetail {
  categoryName: string;
  accounts: {
    id: number;
    name: string;
    code: string;
    balance: number;
  }[];
  totalAmount: number;
}

export interface BalanceSheetReport {
  asOfDate: string;
  assets: {
    currentAssets: BalanceSheetDetail;
    fixedAssets: BalanceSheetDetail;
    otherAssets: BalanceSheetDetail;
    totalAssets: number;
  };
  liabilities: {
    currentLiabilities: BalanceSheetDetail;
    longTermLiabilities: BalanceSheetDetail;
    totalLiabilities: number;
  };
  equity: {
    capital: BalanceSheetDetail;
    reserves: BalanceSheetDetail;
    retainedEarnings: number;
    totalEquity: number;
  };
}

export interface CashFlowReport {
  startDate: string;
  endDate: string;
  openingBalance: number;
  closingBalance: number;
  operatingActivities: {
    inflows: { category: string; amount: number }[];
    outflows: { category: string; amount: number }[];
    netCashFromOperations: number;
  };
  investingActivities: {
    inflows: { category: string; amount: number }[];
    outflows: { category: string; amount: number }[];
    netCashFromInvesting: number;
  };
  financingActivities: {
    inflows: { category: string; amount: number }[];
    outflows: { category: string; amount: number }[];
    netCashFromFinancing: number;
  };
  netCashFlow: number;
}

export interface ShareholderReport {
  shareholder: Shareholder;
  shareholding: Shareholding;
  investments: InvestmentTransaction[];
  distributions: {
    distributionDate: Date;
    amount: number;
    percentage: number;
  }[];
  summary: {
    totalInvested: number;
    totalWithdrawn: number;
    totalDistributions: number;
    currentValueOfShares: number;
    ownershipPercentage: number;
    returnOnInvestment: number;
  };
}

// Password history table
export const passwordHistory = pgTable('password_history', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  password_hash: text('password_hash').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Enhanced Session Management Tables

// Session timeout policy enum
export const sessionTimeoutPolicyEnum = pgEnum('session_timeout_policy', ['idle', 'absolute', 'rolling', 'hybrid']);

// Device type enum
export const deviceTypeEnum = pgEnum('device_type', ['desktop', 'laptop', 'mobile', 'tablet', 'unknown']);

// Session status enum
export const sessionStatusEnum = pgEnum('session_status', ['active', 'expired', 'terminated', 'suspended']);

// Enhanced user sessions table
export const userSessions = pgTable('user_sessions', {
  id: serial('id').primaryKey(),
  session_id: text('session_id').notNull().unique(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Device and location information
  device_fingerprint: text('device_fingerprint'),
  device_type: deviceTypeEnum('device_type').default('unknown').notNull(),
  device_name: text('device_name'),
  user_agent: text('user_agent'),
  ip_address: text('ip_address'), // Using text instead of inet for broader compatibility
  location_country: text('location_country'),
  location_region: text('location_region'),
  location_city: text('location_city'),

  // Session timing
  created_at: timestamp('created_at').defaultNow().notNull(),
  last_activity: timestamp('last_activity').defaultNow().notNull(),
  expires_at: timestamp('expires_at'),
  idle_timeout: integer('idle_timeout').default(3600), // seconds
  absolute_timeout: integer('absolute_timeout').default(86400), // seconds

  // Session status and metadata
  status: sessionStatusEnum('status').default('active').notNull(),
  terminated_at: timestamp('terminated_at'),
  terminated_by: integer('terminated_by').references(() => users.id, { onDelete: 'set null' }),
  termination_reason: text('termination_reason'),

  // Security flags
  is_trusted_device: boolean('is_trusted_device').default(false),
  requires_mfa: boolean('requires_mfa').default(false),
  mfa_verified: boolean('mfa_verified').default(false),
  fresh_auth: boolean('fresh_auth').default(true),

  // Additional metadata
  session_data: jsonb('session_data').default({}),
  security_flags: jsonb('security_flags').default({}),

  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Session policies table for configurable session management
export const sessionPolicies = pgTable('session_policies', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  role: text('role'),
  policy_name: text('policy_name').notNull(),

  // Concurrent session limits
  max_concurrent_sessions: integer('max_concurrent_sessions').default(5),
  max_sessions_per_device_type: jsonb('max_sessions_per_device_type').default({"desktop": 3, "laptop": 3, "mobile": 2, "tablet": 2}),

  // Timeout policies
  timeout_policy: sessionTimeoutPolicyEnum('timeout_policy').default('hybrid'),
  idle_timeout_seconds: integer('idle_timeout_seconds').default(3600), // 1 hour
  absolute_timeout_seconds: integer('absolute_timeout_seconds').default(86400), // 24 hours
  rolling_timeout_seconds: integer('rolling_timeout_seconds').default(28800), // 8 hours

  // Device policies
  allow_mobile_access: boolean('allow_mobile_access').default(true),
  require_device_registration: boolean('require_device_registration').default(false),
  trusted_device_timeout_days: integer('trusted_device_timeout_days').default(30),

  // Security policies
  require_mfa_for_new_device: boolean('require_mfa_for_new_device').default(true),
  require_fresh_auth_for_sensitive: boolean('require_fresh_auth_for_sensitive').default(true),
  auto_logout_on_suspicious_activity: boolean('auto_logout_on_suspicious_activity').default(true),

  // Geographic restrictions
  allowed_countries: text('allowed_countries').array(), // ISO country codes
  blocked_countries: text('blocked_countries').array(), // ISO country codes
  require_vpn: boolean('require_vpn').default(false),

  is_active: boolean('is_active').default(true),
  priority: integer('priority').default(0), // Higher priority policies override lower ones
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueCompanyRolePolicy: unique().on(table.company_id, table.role, table.policy_name),
}));

// Session activity logs for tracking and auditing
export const sessionActivityLogs = pgTable('session_activity_logs', {
  id: serial('id').primaryKey(),
  session_id: text('session_id').notNull(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Activity details
  activity_type: text('activity_type').notNull(), // 'login', 'logout', 'timeout', 'activity', 'security_event'
  activity_description: text('activity_description'),
  endpoint: text('endpoint'),
  method: text('method'),
  status_code: integer('status_code'),

  // Request context
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),
  referer: text('referer'),
  request_id: text('request_id'),

  // Security context
  risk_score: integer('risk_score').default(0), // 0-100 risk assessment
  security_flags: jsonb('security_flags').default({}),
  anomaly_detected: boolean('anomaly_detected').default(false),

  // Timing
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  response_time_ms: integer('response_time_ms'),

  // Additional context
  metadata: jsonb('metadata').default({}),
});

// Device registry for trusted device management
export const trustedDevices = pgTable('trusted_devices', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  device_fingerprint: text('device_fingerprint').notNull(),
  device_name: text('device_name'),
  device_type: deviceTypeEnum('device_type').notNull(),

  // Device details
  user_agent: text('user_agent'),
  last_ip_address: text('last_ip_address'),
  last_location_country: text('last_location_country'),
  last_location_region: text('last_location_region'),
  last_location_city: text('last_location_city'),

  // Trust status
  is_trusted: boolean('is_trusted').default(false),
  trust_level: integer('trust_level').default(0), // 0-100 trust score
  trusted_at: timestamp('trusted_at'),
  trusted_by: integer('trusted_by').references(() => users.id, { onDelete: 'set null' }),

  // Usage tracking
  first_seen: timestamp('first_seen').defaultNow().notNull(),
  last_seen: timestamp('last_seen').defaultNow().notNull(),
  login_count: integer('login_count').default(0),
  last_login: timestamp('last_login'),

  // Security
  is_blocked: boolean('is_blocked').default(false),
  blocked_at: timestamp('blocked_at'),
  blocked_reason: text('blocked_reason'),
  blocked_by: integer('blocked_by').references(() => users.id, { onDelete: 'set null' }),

  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueUserDevice: unique().on(table.user_id, table.device_fingerprint),
}));

// Relations for session management tables
export const userSessionsRelations = relations(userSessions, ({ one }) => ({
  user: one(users, {
    fields: [userSessions.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [userSessions.company_id],
    references: [companies.id],
  }),
  terminatedBy: one(users, {
    fields: [userSessions.terminated_by],
    references: [users.id],
  }),
}));

export const sessionPoliciesRelations = relations(sessionPolicies, ({ one }) => ({
  company: one(companies, {
    fields: [sessionPolicies.company_id],
    references: [companies.id],
  }),
}));

export const sessionActivityLogsRelations = relations(sessionActivityLogs, ({ one }) => ({
  user: one(users, {
    fields: [sessionActivityLogs.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [sessionActivityLogs.company_id],
    references: [companies.id],
  }),
}));

export const trustedDevicesRelations = relations(trustedDevices, ({ one }) => ({
  user: one(users, {
    fields: [trustedDevices.user_id],
    references: [users.id],
  }),
  trustedBy: one(users, {
    fields: [trustedDevices.trusted_by],
    references: [users.id],
  }),
  blockedBy: one(users, {
    fields: [trustedDevices.blocked_by],
    references: [users.id],
  }),
}));

// TypeScript types for session management
export type UserSession = typeof userSessions.$inferSelect;
export type InsertUserSession = typeof userSessions.$inferInsert;

export type SessionPolicy = typeof sessionPolicies.$inferSelect;
export type InsertSessionPolicy = typeof sessionPolicies.$inferInsert;

export type SessionActivityLog = typeof sessionActivityLogs.$inferSelect;
export type InsertSessionActivityLog = typeof sessionActivityLogs.$inferInsert;

export type TrustedDevice = typeof trustedDevices.$inferSelect;
export type InsertTrustedDevice = typeof trustedDevices.$inferInsert;

// Zod schemas for session management
export const userSessionSchema = createSelectSchema(userSessions);
export const insertUserSessionSchema = createInsertSchema(userSessions);

export const sessionPolicySchema = createSelectSchema(sessionPolicies);
export const insertSessionPolicySchema = createInsertSchema(sessionPolicies);

export const sessionActivityLogSchema = createSelectSchema(sessionActivityLogs);
export const insertSessionActivityLogSchema = createInsertSchema(sessionActivityLogs);

export const trustedDeviceSchema = createSelectSchema(trustedDevices);
export const insertTrustedDeviceSchema = createInsertSchema(trustedDevices);

// Additional session management types
export interface SessionInfo {
  sessionId: string;
  userId: number;
  companyId?: number;
  deviceFingerprint?: string;
  deviceType: 'desktop' | 'laptop' | 'mobile' | 'tablet' | 'unknown';
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  createdAt: Date;
  lastActivity: Date;
  expiresAt?: Date;
  status: 'active' | 'expired' | 'terminated' | 'suspended';
  isTrustedDevice: boolean;
  mfaVerified: boolean;
  freshAuth: boolean;
}

export interface SessionValidationResult {
  isValid: boolean;
  session?: UserSession;
  reason?: string;
  requiresAction?: 'mfa' | 'fresh_auth' | 'device_registration' | 'logout';
}

export interface DeviceFingerprint {
  userAgent: string;
  screen: {
    width: number;
    height: number;
    colorDepth: number;
  };
  timezone: string;
  language: string;
  platform: string;
  plugins: string[];
  canvas?: string;
  webgl?: string;
}

export interface SessionSecurityContext {
  riskScore: number;
  anomalyFlags: string[];
  geoLocation?: {
    country: string;
    region?: string;
    city?: string;
  };
  deviceTrust: {
    isKnown: boolean;
    isTrusted: boolean;
    trustScore: number;
  };
  behaviorAnalysis: {
    typicalLoginTime: boolean;
    typicalLocation: boolean;
    typicalDevice: boolean;
  };
}

// Access Monitoring System Tables

// Security event type enum
export const securityEventTypeEnum = pgEnum('security_event_type', [
  'login_attempt', 'login_success', 'login_failure', 'logout',
  'permission_denied', 'permission_granted', 'data_access',
  'suspicious_activity', 'anomaly_detected', 'rate_limit_exceeded',
  'geo_anomaly', 'device_anomaly', 'time_anomaly', 'behavior_anomaly',
  'brute_force_attempt', 'session_hijack_attempt', 'privilege_escalation',
  'data_export', 'bulk_operation', 'admin_action', 'emergency_access'
]);

// Security alert severity enum
export const alertSeverityEnum = pgEnum('alert_severity', ['low', 'medium', 'high', 'critical']);

// Security response action enum
export const securityResponseActionEnum = pgEnum('security_response_action', [
  'log_only', 'alert_admin', 'block_ip', 'suspend_user', 'terminate_session',
  'require_mfa', 'require_fresh_auth', 'block_device', 'escalate_to_admin'
]);

// Security events table for comprehensive monitoring
export const securityEvents = pgTable('security_events', {
  id: serial('id').primaryKey(),
  event_id: text('event_id').notNull().unique(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'set null' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  session_id: text('session_id'),

  // Event classification
  event_type: securityEventTypeEnum('event_type').notNull(),
  severity: alertSeverityEnum('severity').default('low').notNull(),
  risk_score: integer('risk_score').default(0), // 0-100 risk assessment

  // Event details
  event_description: text('event_description').notNull(),
  event_source: text('event_source'), // 'auth', 'permission', 'session', 'api', 'manual'
  resource_accessed: text('resource_accessed'),
  operation_performed: text('operation_performed'),

  // Request context
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),
  device_fingerprint: text('device_fingerprint'),
  device_type: text('device_type'),
  endpoint: text('endpoint'),
  method: text('method'),
  status_code: integer('status_code'),
  response_time_ms: integer('response_time_ms'),

  // Geographic context
  location_country: text('location_country'),
  location_region: text('location_region'),
  location_city: text('location_city'),
  is_geo_anomaly: boolean('is_geo_anomaly').default(false),

  // Temporal context
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  is_time_anomaly: boolean('is_time_anomaly').default(false),
  is_weekend: boolean('is_weekend').default(false),
  is_business_hours: boolean('is_business_hours').default(true),

  // Detection context
  detection_rules: jsonb('detection_rules').default([]), // Rules that triggered this event
  anomaly_indicators: jsonb('anomaly_indicators').default({}), // Specific anomaly details
  correlation_id: text('correlation_id'), // For grouping related events

  // Response tracking
  auto_response_triggered: boolean('auto_response_triggered').default(false),
  response_actions: jsonb('response_actions').default([]),
  admin_notified: boolean('admin_notified').default(false),
  resolved: boolean('resolved').default(false),
  resolved_at: timestamp('resolved_at'),
  resolved_by: integer('resolved_by').references(() => users.id, { onDelete: 'set null' }),

  // Additional metadata
  metadata: jsonb('metadata').default({}),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Security rules table for configurable detection rules
export const securityRules = pgTable('security_rules', {
  id: serial('id').primaryKey(),
  rule_name: text('rule_name').notNull(),
  rule_description: text('rule_description'),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Rule configuration
  event_types: text('event_types').array().notNull(), // Event types this rule applies to
  conditions: jsonb('conditions').notNull(), // Rule conditions (JSON query)
  threshold_config: jsonb('threshold_config').default({}), // Thresholds and time windows

  // Response configuration
  severity: alertSeverityEnum('severity').default('medium').notNull(),
  auto_response_actions: jsonb('auto_response_actions').default([]),
  notification_config: jsonb('notification_config').default({}),

  // Rule status
  is_active: boolean('is_active').default(true),
  priority: integer('priority').default(0),
  last_triggered: timestamp('last_triggered'),
  trigger_count: integer('trigger_count').default(0),

  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// User behavior baselines for anomaly detection
export const userBehaviorBaselines = pgTable('user_behavior_baselines', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Temporal patterns
  typical_login_hours: jsonb('typical_login_hours').default({}), // Hour distribution
  typical_login_days: jsonb('typical_login_days').default({}), // Day of week distribution
  average_session_duration: integer('average_session_duration').default(0), // in seconds
  typical_locations: jsonb('typical_locations').default([]), // Common countries/cities

  // Device patterns
  known_devices: jsonb('known_devices').default([]), // Device fingerprints
  typical_device_types: jsonb('typical_device_types').default({}), // Device type distribution
  known_ip_ranges: jsonb('known_ip_ranges').default([]), // Common IP addresses/ranges

  // Activity patterns
  typical_endpoints: jsonb('typical_endpoints').default({}), // Endpoint access patterns
  average_requests_per_hour: numeric('average_requests_per_hour', { precision: 10, scale: 2 }).default('0'),
  typical_operations: jsonb('typical_operations').default({}), // Operation type distribution

  // Risk indicators
  failed_login_rate: numeric('failed_login_rate', { precision: 5, scale: 4 }).default('0'), // Percentage of failed logins
  permission_denied_rate: numeric('permission_denied_rate', { precision: 5, scale: 4 }).default('0'), // Percentage of denied requests
  anomaly_score: integer('anomaly_score').default(0), // 0-100 baseline anomaly score

  // Baseline metadata
  baseline_period_start: timestamp('baseline_period_start').notNull(),
  baseline_period_end: timestamp('baseline_period_end').notNull(),
  sample_size: integer('sample_size').default(0), // Number of events in baseline
  confidence_score: integer('confidence_score').default(0), // 0-100 confidence in baseline

  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueUserCompany: unique().on(table.user_id, table.company_id),
}));

// Real-time monitoring alerts
export const securityAlerts = pgTable('security_alerts', {
  id: serial('id').primaryKey(),
  alert_id: text('alert_id').notNull().unique(),
  security_event_id: integer('security_event_id').references(() => securityEvents.id, { onDelete: 'cascade' }),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'set null' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Alert details
  alert_type: text('alert_type').notNull(),
  severity: alertSeverityEnum('severity').notNull(),
  title: text('title').notNull(),
  description: text('description').notNull(),
  recommendation: text('recommendation'),

  // Alert status
  status: text('status').default('open'), // 'open', 'investigating', 'resolved', 'false_positive'
  assigned_to: integer('assigned_to').references(() => users.id, { onDelete: 'set null' }),
  acknowledged: boolean('acknowledged').default(false),
  acknowledged_at: timestamp('acknowledged_at'),
  acknowledged_by: integer('acknowledged_by').references(() => users.id, { onDelete: 'set null' }),

  // Response tracking
  auto_response_applied: boolean('auto_response_applied').default(false),
  manual_response_required: boolean('manual_response_required').default(false),
  escalated: boolean('escalated').default(false),
  escalated_at: timestamp('escalated_at'),

  // Resolution
  resolved_at: timestamp('resolved_at'),
  resolved_by: integer('resolved_by').references(() => users.id, { onDelete: 'set null' }),
  resolution_notes: text('resolution_notes'),

  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Automated security responses log
export const securityResponses = pgTable('security_responses', {
  id: serial('id').primaryKey(),
  response_id: text('response_id').notNull().unique(),
  security_event_id: integer('security_event_id').references(() => securityEvents.id, { onDelete: 'cascade' }),
  security_alert_id: integer('security_alert_id').references(() => securityAlerts.id, { onDelete: 'set null' }),
  triggered_by_rule_id: integer('triggered_by_rule_id').references(() => securityRules.id, { onDelete: 'set null' }),

  // Response details
  response_action: securityResponseActionEnum('response_action').notNull(),
  action_description: text('action_description').notNull(),
  target_user_id: integer('target_user_id').references(() => users.id, { onDelete: 'set null' }),
  target_session_id: text('target_session_id'),
  target_ip_address: text('target_ip_address'),

  // Response configuration
  action_parameters: jsonb('action_parameters').default({}),
  duration_seconds: integer('duration_seconds'), // For temporary actions
  expires_at: timestamp('expires_at'),

  // Execution tracking
  executed: boolean('executed').default(false),
  executed_at: timestamp('executed_at'),
  execution_result: text('execution_result'),
  execution_error: text('execution_error'),

  // Reversal tracking
  reversible: boolean('reversible').default(false),
  reversed: boolean('reversed').default(false),
  reversed_at: timestamp('reversed_at'),
  reversed_by: integer('reversed_by').references(() => users.id, { onDelete: 'set null' }),

  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Rate limiting tracking
export const rateLimitViolations = pgTable('rate_limit_violations', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'set null' }),
  ip_address: text('ip_address').notNull(),
  endpoint: text('endpoint'),
  method: text('method'),

  // Rate limit details
  limit_type: text('limit_type').notNull(), // 'per_user', 'per_ip', 'per_endpoint'
  limit_window: integer('limit_window').notNull(), // Window in seconds
  limit_threshold: integer('limit_threshold').notNull(), // Max requests per window
  actual_requests: integer('actual_requests').notNull(), // Actual requests made

  // Violation context
  violation_timestamp: timestamp('violation_timestamp').defaultNow().notNull(),
  user_agent: text('user_agent'),
  session_id: text('session_id'),

  // Response
  blocked: boolean('blocked').default(true),
  block_duration: integer('block_duration'), // Block duration in seconds
  block_expires_at: timestamp('block_expires_at'),

  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Relations for access monitoring tables
export const securityEventsRelations = relations(securityEvents, ({ one }) => ({
  user: one(users, {
    fields: [securityEvents.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [securityEvents.company_id],
    references: [companies.id],
  }),
  resolvedBy: one(users, {
    fields: [securityEvents.resolved_by],
    references: [users.id],
  }),
}));

export const securityRulesRelations = relations(securityRules, ({ one }) => ({
  company: one(companies, {
    fields: [securityRules.company_id],
    references: [companies.id],
  }),
}));

export const userBehaviorBaselinesRelations = relations(userBehaviorBaselines, ({ one }) => ({
  user: one(users, {
    fields: [userBehaviorBaselines.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [userBehaviorBaselines.company_id],
    references: [companies.id],
  }),
}));

export const securityAlertsRelations = relations(securityAlerts, ({ one }) => ({
  securityEvent: one(securityEvents, {
    fields: [securityAlerts.security_event_id],
    references: [securityEvents.id],
  }),
  user: one(users, {
    fields: [securityAlerts.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [securityAlerts.company_id],
    references: [companies.id],
  }),
  assignedTo: one(users, {
    fields: [securityAlerts.assigned_to],
    references: [users.id],
  }),
  acknowledgedBy: one(users, {
    fields: [securityAlerts.acknowledged_by],
    references: [users.id],
  }),
  resolvedBy: one(users, {
    fields: [securityAlerts.resolved_by],
    references: [users.id],
  }),
}));

export const securityResponsesRelations = relations(securityResponses, ({ one }) => ({
  securityEvent: one(securityEvents, {
    fields: [securityResponses.security_event_id],
    references: [securityEvents.id],
  }),
  securityAlert: one(securityAlerts, {
    fields: [securityResponses.security_alert_id],
    references: [securityAlerts.id],
  }),
  triggeredByRule: one(securityRules, {
    fields: [securityResponses.triggered_by_rule_id],
    references: [securityRules.id],
  }),
  targetUser: one(users, {
    fields: [securityResponses.target_user_id],
    references: [users.id],
  }),
  reversedBy: one(users, {
    fields: [securityResponses.reversed_by],
    references: [users.id],
  }),
}));

export const rateLimitViolationsRelations = relations(rateLimitViolations, ({ one }) => ({
  user: one(users, {
    fields: [rateLimitViolations.user_id],
    references: [users.id],
  }),
}));

// TypeScript types for access monitoring
export type SecurityEvent = typeof securityEvents.$inferSelect;
export type InsertSecurityEvent = typeof securityEvents.$inferInsert;

export type SecurityRule = typeof securityRules.$inferSelect;
export type InsertSecurityRule = typeof securityRules.$inferInsert;

export type UserBehaviorBaseline = typeof userBehaviorBaselines.$inferSelect;
export type InsertUserBehaviorBaseline = typeof userBehaviorBaselines.$inferInsert;

export type SecurityAlert = typeof securityAlerts.$inferSelect;
export type InsertSecurityAlert = typeof securityAlerts.$inferInsert;

export type SecurityResponse = typeof securityResponses.$inferSelect;
export type InsertSecurityResponse = typeof securityResponses.$inferInsert;

export type RateLimitViolation = typeof rateLimitViolations.$inferSelect;
export type InsertRateLimitViolation = typeof rateLimitViolations.$inferInsert;

// Zod schemas for access monitoring
export const securityEventSchema = createSelectSchema(securityEvents);
export const insertSecurityEventSchema = createInsertSchema(securityEvents);

export const securityRuleSchema = createSelectSchema(securityRules);
export const insertSecurityRuleSchema = createInsertSchema(securityRules);

export const userBehaviorBaselineSchema = createSelectSchema(userBehaviorBaselines);
export const insertUserBehaviorBaselineSchema = createInsertSchema(userBehaviorBaselines);

export const securityAlertSchema = createSelectSchema(securityAlerts);
export const insertSecurityAlertSchema = createInsertSchema(securityAlerts);

export const securityResponseSchema = createSelectSchema(securityResponses);
export const insertSecurityResponseSchema = createInsertSchema(securityResponses);

export const rateLimitViolationSchema = createSelectSchema(rateLimitViolations);
export const insertRateLimitViolationSchema = createInsertSchema(rateLimitViolations);

// Additional access monitoring types
export interface SecurityEventContext {
  userId?: number;
  companyId?: number;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  deviceFingerprint?: string;
  deviceType?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  responseTime?: number;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  metadata?: any;
}

export interface AnomalyDetectionResult {
  isAnomaly: boolean;
  anomalyType: 'geo' | 'time' | 'device' | 'behavior' | 'volume';
  confidence: number; // 0-1
  indicators: string[];
  riskScore: number; // 0-100
  baseline?: any;
  current?: any;
}

export interface SecurityRuleEvaluation {
  ruleId: number;
  ruleName: string;
  triggered: boolean;
  conditions: any;
  thresholds: any;
  actualValues: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendedActions: string[];
}

// Audit action enum
export const auditActionEnum = pgEnum('audit_action', [
  'granted', 'revoked', 'used', 'denied', 'modified', 'viewed', 'created', 'updated', 'deleted', 'exported'
]);

// Audit entity type enum
export const auditEntityTypeEnum = pgEnum('audit_entity_type', [
  'permission', 'role', 'user', 'customer', 'loan', 'payment', 'collection', 'agent', 'company', 'session', 'data'
]);

// Compliance framework enum
export const complianceFrameworkEnum = pgEnum('compliance_framework', [
  'sox', 'gdpr', 'pci_dss', 'hipaa', 'iso27001', 'nist', 'coso', 'cobit', 'custom'
]);

// Compliance status enum
export const complianceStatusEnum = pgEnum('compliance_status', [
  'compliant', 'non_compliant', 'partially_compliant', 'under_review', 'not_assessed'
]);

// Certification status enum
export const certificationStatusEnum = pgEnum('certification_status', [
  'pending', 'in_progress', 'approved', 'rejected', 'expired', 'cancelled'
]);

// Risk level enum
export const riskLevelEnum = pgEnum('risk_level', [
  'very_low', 'low', 'medium', 'high', 'very_high', 'critical'
]);

// Request status enum
export const requestStatusEnum = pgEnum('request_status', [
  'pending', 'approved', 'rejected', 'cancelled', 'expired'
]);

// Request type enum
export const requestTypeEnum = pgEnum('request_type', [
  'permission_grant', 'permission_revoke', 'role_change', 'access_extension', 'temporary_access'
]);

// Request priority enum
export const requestPriorityEnum = pgEnum('request_priority', [
  'low', 'medium', 'high', 'urgent'
]);

// Permission audit logs table for tracking permission usage and changes
export const permissionAuditLogs = pgTable('permission_audit_logs', {
  id: serial('id').primaryKey(),
  audit_id: text('audit_id').notNull().unique(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'set null' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  session_id: text('session_id'),

  // Permission details
  permission_code: text('permission_code').notNull(),
  permission_name: text('permission_name'),
  action: auditActionEnum('action').notNull(),
  result: text('result').notNull(), // 'success', 'denied', 'error', 'expired'

  // Context information
  resource_type: text('resource_type'), // 'loan', 'customer', 'payment', etc.
  resource_id: text('resource_id'), // ID of the resource being accessed
  operation_type: text('operation_type'), // 'create', 'read', 'update', 'delete', 'approve', etc.
  operation_details: text('operation_details'), // Description of what was done

  // Request context
  endpoint: text('endpoint'),
  method: text('method'),
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),
  referer: text('referer'),

  // Additional context
  metadata: jsonb('metadata').default({}), // Additional context data
  risk_score: integer('risk_score').default(0), // 0-100 risk assessment
  is_sensitive_operation: boolean('is_sensitive_operation').default(false),
  compliance_flags: jsonb('compliance_flags').default([]), // Compliance-related flags

  // Timing
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  response_time_ms: integer('response_time_ms'),

  // Audit trail
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Data access audit logs table for tracking data access and modifications
export const dataAccessAuditLogs = pgTable('data_access_audit_logs', {
  id: serial('id').primaryKey(),
  audit_id: text('audit_id').notNull().unique(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'set null' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  session_id: text('session_id'),

  // Data access details
  entity_type: auditEntityTypeEnum('entity_type').notNull(),
  entity_id: text('entity_id').notNull(),
  action: auditActionEnum('action').notNull(),
  table_name: text('table_name').notNull(),
  field_names: jsonb('field_names').default([]), // Fields that were accessed/modified

  // Data details
  old_values: jsonb('old_values'), // Previous values for updates
  new_values: jsonb('new_values'), // New values for creates/updates
  query_filters: jsonb('query_filters'), // Filters used in the query
  record_count: integer('record_count').default(1), // Number of records affected

  // Access control
  data_scope: text('data_scope'), // 'branch', 'department', 'company', 'personal'
  field_access_level: text('field_access_level'), // 'full', 'masked', 'restricted'
  sensitive_fields_accessed: jsonb('sensitive_fields_accessed').default([]), // List of sensitive fields accessed

  // Request context
  endpoint: text('endpoint'),
  method: text('method'),
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),

  // Additional context
  metadata: jsonb('metadata').default({}),
  compliance_flags: jsonb('compliance_flags').default([]),
  retention_period_days: integer('retention_period_days'), // How long this audit log should be retained

  // Timing
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Permission change logs table for tracking permission grants, revokes, and modifications
export const permissionChangeLogs = pgTable('permission_change_logs', {
  id: serial('id').primaryKey(),
  audit_id: text('audit_id').notNull().unique(),
  changed_by: integer('changed_by').references(() => users.id, { onDelete: 'set null' }),
  target_user_id: integer('target_user_id').references(() => users.id, { onDelete: 'set null' }),
  target_role_id: integer('target_role_id').references(() => customRoles.id, { onDelete: 'set null' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Change details
  change_type: text('change_type').notNull(), // 'permission_grant', 'permission_revoke', 'role_assign', 'role_remove', 'role_modify'
  permission_code: text('permission_code'),
  permission_name: text('permission_name'),
  role_name: text('role_name'),

  // Change context
  action: auditActionEnum('action').notNull(),
  reason: text('reason'), // Reason for the change
  approval_workflow_id: integer('approval_workflow_id'), // If change required approval
  is_temporary: boolean('is_temporary').default(false),
  expires_at: timestamp('expires_at'), // For temporary permissions

  // Before/after state
  previous_state: jsonb('previous_state'), // Previous permission/role state
  new_state: jsonb('new_state'), // New permission/role state
  change_summary: text('change_summary'), // Human-readable summary of changes

  // Request context
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),
  session_id: text('session_id'),

  // Additional context
  metadata: jsonb('metadata').default({}),
  compliance_flags: jsonb('compliance_flags').default([]),
  risk_assessment: text('risk_assessment'), // 'low', 'medium', 'high', 'critical'

  // Timing
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Relations for audit tables
export const permissionAuditLogsRelations = relations(permissionAuditLogs, ({ one }) => ({
  user: one(users, {
    fields: [permissionAuditLogs.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [permissionAuditLogs.company_id],
    references: [companies.id],
  }),
}));

export const dataAccessAuditLogsRelations = relations(dataAccessAuditLogs, ({ one }) => ({
  user: one(users, {
    fields: [dataAccessAuditLogs.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [dataAccessAuditLogs.company_id],
    references: [companies.id],
  }),
}));

export const permissionChangeLogsRelations = relations(permissionChangeLogs, ({ one }) => ({
  changedBy: one(users, {
    fields: [permissionChangeLogs.changed_by],
    references: [users.id],
  }),
  targetUser: one(users, {
    fields: [permissionChangeLogs.target_user_id],
    references: [users.id],
  }),
  targetRole: one(customRoles, {
    fields: [permissionChangeLogs.target_role_id],
    references: [customRoles.id],
  }),
  company: one(companies, {
    fields: [permissionChangeLogs.company_id],
    references: [companies.id],
  }),
}));

// TypeScript types for audit tables
export type PermissionAuditLog = typeof permissionAuditLogs.$inferSelect;
export type InsertPermissionAuditLog = typeof permissionAuditLogs.$inferInsert;

export type DataAccessAuditLog = typeof dataAccessAuditLogs.$inferSelect;
export type InsertDataAccessAuditLog = typeof dataAccessAuditLogs.$inferInsert;

export type PermissionChangeLog = typeof permissionChangeLogs.$inferSelect;
export type InsertPermissionChangeLog = typeof permissionChangeLogs.$inferInsert;

// Zod schemas for audit tables
export const permissionAuditLogSchema = createSelectSchema(permissionAuditLogs);
export const insertPermissionAuditLogSchema = createInsertSchema(permissionAuditLogs);

export const dataAccessAuditLogSchema = createSelectSchema(dataAccessAuditLogs);
export const insertDataAccessAuditLogSchema = createInsertSchema(dataAccessAuditLogs);

export const permissionChangeLogSchema = createSelectSchema(permissionChangeLogs);
export const insertPermissionChangeLogSchema = createInsertSchema(permissionChangeLogs);

// Additional audit-related types
export interface AuditContext {
  userId?: number;
  companyId?: number;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  metadata?: any;
}

export interface PermissionUsageContext extends AuditContext {
  permissionCode: string;
  permissionName?: string;
  resourceType?: string;
  resourceId?: string;
  operationType?: string;
  operationDetails?: string;
  result: 'success' | 'denied' | 'error' | 'expired';
  isSensitiveOperation?: boolean;
  complianceFlags?: string[];
  riskScore?: number;
  responseTimeMs?: number;
}

export interface DataAccessContext extends AuditContext {
  entityType: string;
  entityId: string;
  tableName: string;
  fieldNames?: string[];
  oldValues?: any;
  newValues?: any;
  queryFilters?: any;
  recordCount?: number;
  dataScope?: string;
  fieldAccessLevel?: 'full' | 'masked' | 'restricted';
  sensitiveFieldsAccessed?: string[];
  retentionPeriodDays?: number;
}

export interface PermissionChangeContext extends AuditContext {
  changedBy: number;
  targetUserId?: number;
  targetRoleId?: number;
  changeType: 'permission_grant' | 'permission_revoke' | 'role_assign' | 'role_remove' | 'role_modify';
  permissionCode?: string;
  permissionName?: string;
  roleName?: string;
  reason?: string;
  approvalWorkflowId?: number;
  isTemporary?: boolean;
  expiresAt?: Date;
  previousState?: any;
  newState?: any;
  changeSummary?: string;
  riskAssessment?: 'low' | 'medium' | 'high' | 'critical';
}

// Compliance frameworks table
export const complianceFrameworks = pgTable('compliance_frameworks', {
  id: serial('id').primaryKey(),
  framework_code: text('framework_code').notNull().unique(),
  framework_name: text('framework_name').notNull(),
  framework_type: complianceFrameworkEnum('framework_type').notNull(),
  description: text('description'),
  version: text('version'),
  effective_date: timestamp('effective_date', { mode: 'date' }),
  is_active: boolean('is_active').default(true),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Compliance requirements table
export const complianceRequirements = pgTable('compliance_requirements', {
  id: serial('id').primaryKey(),
  framework_id: integer('framework_id').references(() => complianceFrameworks.id, { onDelete: 'cascade' }),
  requirement_code: text('requirement_code').notNull(),
  requirement_name: text('requirement_name').notNull(),
  description: text('description'),
  category: text('category'),
  subcategory: text('subcategory'),
  control_type: text('control_type'), // 'preventive', 'detective', 'corrective'
  risk_level: riskLevelEnum('risk_level').default('medium'),
  frequency: text('frequency'), // 'daily', 'weekly', 'monthly', 'quarterly', 'annually'
  automated_check: boolean('automated_check').default(false),
  check_query: text('check_query'), // SQL query for automated checks
  remediation_guidance: text('remediation_guidance'),
  is_active: boolean('is_active').default(true),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueFrameworkRequirement: unique().on(table.framework_id, table.requirement_code),
}));

// Compliance assessments table
export const complianceAssessments = pgTable('compliance_assessments', {
  id: serial('id').primaryKey(),
  assessment_id: text('assessment_id').notNull().unique(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  framework_id: integer('framework_id').references(() => complianceFrameworks.id, { onDelete: 'cascade' }),
  requirement_id: integer('requirement_id').references(() => complianceRequirements.id, { onDelete: 'cascade' }),
  assessed_by: integer('assessed_by').references(() => users.id, { onDelete: 'set null' }),

  // Assessment details
  assessment_date: timestamp('assessment_date', { mode: 'date' }).notNull(),
  assessment_period_start: timestamp('assessment_period_start', { mode: 'date' }),
  assessment_period_end: timestamp('assessment_period_end', { mode: 'date' }),
  status: complianceStatusEnum('status').notNull(),
  score: integer('score'), // 0-100 compliance score
  risk_rating: riskLevelEnum('risk_rating'),

  // Assessment results
  findings: text('findings'),
  evidence: jsonb('evidence').default([]), // Array of evidence documents/links
  gaps_identified: jsonb('gaps_identified').default([]), // Array of compliance gaps
  remediation_actions: jsonb('remediation_actions').default([]), // Array of required actions
  remediation_deadline: timestamp('remediation_deadline', { mode: 'date' }),
  remediation_owner: integer('remediation_owner').references(() => users.id, { onDelete: 'set null' }),

  // Review and approval
  reviewed_by: integer('reviewed_by').references(() => users.id, { onDelete: 'set null' }),
  reviewed_at: timestamp('reviewed_at'),
  approved_by: integer('approved_by').references(() => users.id, { onDelete: 'set null' }),
  approved_at: timestamp('approved_at'),

  // Additional context
  metadata: jsonb('metadata').default({}),
  attachments: jsonb('attachments').default([]), // Array of attachment references
  next_assessment_due: timestamp('next_assessment_due', { mode: 'date' }),

  // Audit trail
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Access certifications table for access review workflows
export const accessCertifications = pgTable('access_certifications', {
  id: serial('id').primaryKey(),
  certification_id: text('certification_id').notNull().unique(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  initiated_by: integer('initiated_by').references(() => users.id, { onDelete: 'set null' }),

  // Certification scope
  certification_type: text('certification_type').notNull(), // 'user_access', 'role_access', 'permission_access', 'data_access'
  scope_description: text('scope_description'),
  target_user_id: integer('target_user_id').references(() => users.id, { onDelete: 'set null' }),
  target_role_id: integer('target_role_id').references(() => customRoles.id, { onDelete: 'set null' }),
  target_permissions: jsonb('target_permissions').default([]), // Array of permission codes
  target_resources: jsonb('target_resources').default([]), // Array of resource identifiers

  // Certification workflow
  status: certificationStatusEnum('status').notNull().default('pending'),
  due_date: timestamp('due_date', { mode: 'date' }).notNull(),
  reminder_sent: boolean('reminder_sent').default(false),
  escalation_level: integer('escalation_level').default(0),

  // Reviewer information
  primary_reviewer: integer('primary_reviewer').references(() => users.id, { onDelete: 'set null' }),
  secondary_reviewer: integer('secondary_reviewer').references(() => users.id, { onDelete: 'set null' }),
  manager_reviewer: integer('manager_reviewer').references(() => users.id, { onDelete: 'set null' }),

  // Review results
  review_started_at: timestamp('review_started_at'),
  review_completed_at: timestamp('review_completed_at'),
  certification_decision: text('certification_decision'), // 'approve', 'revoke', 'modify', 'escalate'
  reviewer_comments: text('reviewer_comments'),
  justification: text('justification'),
  risk_assessment: riskLevelEnum('risk_assessment'),

  // Actions taken
  actions_required: jsonb('actions_required').default([]), // Array of required actions
  actions_completed: jsonb('actions_completed').default([]), // Array of completed actions
  permissions_revoked: jsonb('permissions_revoked').default([]), // Array of revoked permissions
  permissions_modified: jsonb('permissions_modified').default([]), // Array of modified permissions

  // Compliance context
  compliance_framework: text('compliance_framework'),
  regulatory_requirement: text('regulatory_requirement'),
  business_justification: text('business_justification'),

  // Additional context
  metadata: jsonb('metadata').default({}),
  evidence_provided: jsonb('evidence_provided').default([]), // Array of evidence documents

  // Audit trail
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Compliance violations table
export const complianceViolations = pgTable('compliance_violations', {
  id: serial('id').primaryKey(),
  violation_id: text('violation_id').notNull().unique(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),
  framework_id: integer('framework_id').references(() => complianceFrameworks.id, { onDelete: 'cascade' }),
  requirement_id: integer('requirement_id').references(() => complianceRequirements.id, { onDelete: 'cascade' }),
  detected_by: integer('detected_by').references(() => users.id, { onDelete: 'set null' }),

  // Violation details
  violation_type: text('violation_type').notNull(),
  severity: riskLevelEnum('severity').notNull(),
  detected_at: timestamp('detected_at').defaultNow().notNull(),
  description: text('description').notNull(),
  affected_systems: jsonb('affected_systems').default([]), // Array of affected systems/resources
  affected_users: jsonb('affected_users').default([]), // Array of affected user IDs
  affected_data: jsonb('affected_data').default([]), // Array of affected data types

  // Detection context
  detection_method: text('detection_method'), // 'automated', 'manual', 'audit', 'incident'
  detection_source: text('detection_source'), // Source system or process
  related_audit_logs: jsonb('related_audit_logs').default([]), // Array of related audit log IDs
  related_security_events: jsonb('related_security_events').default([]), // Array of related security event IDs

  // Impact assessment
  business_impact: text('business_impact'), // 'low', 'medium', 'high', 'critical'
  financial_impact: numeric('financial_impact', { precision: 15, scale: 2 }),
  regulatory_impact: text('regulatory_impact'),
  reputation_impact: text('reputation_impact'),

  // Response and remediation
  status: text('status').default('open'), // 'open', 'investigating', 'remediating', 'resolved', 'closed'
  assigned_to: integer('assigned_to').references(() => users.id, { onDelete: 'set null' }),
  response_plan: text('response_plan'),
  remediation_actions: jsonb('remediation_actions').default([]), // Array of remediation actions
  remediation_deadline: timestamp('remediation_deadline', { mode: 'date' }),
  resolution_date: timestamp('resolution_date', { mode: 'date' }),
  resolution_summary: text('resolution_summary'),

  // Reporting and notification
  reported_to_regulator: boolean('reported_to_regulator').default(false),
  regulator_reference: text('regulator_reference'),
  reported_at: timestamp('reported_at'),
  notification_sent: boolean('notification_sent').default(false),
  stakeholders_notified: jsonb('stakeholders_notified').default([]), // Array of notified stakeholders

  // Additional context
  metadata: jsonb('metadata').default({}),
  attachments: jsonb('attachments').default([]), // Array of attachment references
  lessons_learned: text('lessons_learned'),

  // Audit trail
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Relations for compliance tables
export const complianceFrameworksRelations = relations(complianceFrameworks, ({ many }) => ({
  requirements: many(complianceRequirements),
  assessments: many(complianceAssessments),
  violations: many(complianceViolations),
}));

export const complianceRequirementsRelations = relations(complianceRequirements, ({ one, many }) => ({
  framework: one(complianceFrameworks, {
    fields: [complianceRequirements.framework_id],
    references: [complianceFrameworks.id],
  }),
  assessments: many(complianceAssessments),
  violations: many(complianceViolations),
}));

export const complianceAssessmentsRelations = relations(complianceAssessments, ({ one }) => ({
  company: one(companies, {
    fields: [complianceAssessments.company_id],
    references: [companies.id],
  }),
  framework: one(complianceFrameworks, {
    fields: [complianceAssessments.framework_id],
    references: [complianceFrameworks.id],
  }),
  requirement: one(complianceRequirements, {
    fields: [complianceAssessments.requirement_id],
    references: [complianceRequirements.id],
  }),
  assessedBy: one(users, {
    fields: [complianceAssessments.assessed_by],
    references: [users.id],
  }),
  remediationOwner: one(users, {
    fields: [complianceAssessments.remediation_owner],
    references: [users.id],
  }),
  reviewedBy: one(users, {
    fields: [complianceAssessments.reviewed_by],
    references: [users.id],
  }),
  approvedBy: one(users, {
    fields: [complianceAssessments.approved_by],
    references: [users.id],
  }),
}));

export const accessCertificationsRelations = relations(accessCertifications, ({ one }) => ({
  company: one(companies, {
    fields: [accessCertifications.company_id],
    references: [companies.id],
  }),
  initiatedBy: one(users, {
    fields: [accessCertifications.initiated_by],
    references: [users.id],
  }),
  targetUser: one(users, {
    fields: [accessCertifications.target_user_id],
    references: [users.id],
  }),
  targetRole: one(customRoles, {
    fields: [accessCertifications.target_role_id],
    references: [customRoles.id],
  }),
  primaryReviewer: one(users, {
    fields: [accessCertifications.primary_reviewer],
    references: [users.id],
  }),
  secondaryReviewer: one(users, {
    fields: [accessCertifications.secondary_reviewer],
    references: [users.id],
  }),
  managerReviewer: one(users, {
    fields: [accessCertifications.manager_reviewer],
    references: [users.id],
  }),
}));

export const complianceViolationsRelations = relations(complianceViolations, ({ one }) => ({
  company: one(companies, {
    fields: [complianceViolations.company_id],
    references: [companies.id],
  }),
  framework: one(complianceFrameworks, {
    fields: [complianceViolations.framework_id],
    references: [complianceFrameworks.id],
  }),
  requirement: one(complianceRequirements, {
    fields: [complianceViolations.requirement_id],
    references: [complianceRequirements.id],
  }),
  detectedBy: one(users, {
    fields: [complianceViolations.detected_by],
    references: [users.id],
  }),
  assignedTo: one(users, {
    fields: [complianceViolations.assigned_to],
    references: [users.id],
  }),
}));

// TypeScript types for compliance tables
export type ComplianceFramework = typeof complianceFrameworks.$inferSelect;
export type InsertComplianceFramework = typeof complianceFrameworks.$inferInsert;

export type ComplianceRequirement = typeof complianceRequirements.$inferSelect;
export type InsertComplianceRequirement = typeof complianceRequirements.$inferInsert;

export type ComplianceAssessment = typeof complianceAssessments.$inferSelect;
export type InsertComplianceAssessment = typeof complianceAssessments.$inferInsert;

export type AccessCertification = typeof accessCertifications.$inferSelect;
export type InsertAccessCertification = typeof accessCertifications.$inferInsert;

export type ComplianceViolation = typeof complianceViolations.$inferSelect;
export type InsertComplianceViolation = typeof complianceViolations.$inferInsert;

// Zod schemas for compliance tables
export const complianceFrameworkSchema = createSelectSchema(complianceFrameworks);
export const insertComplianceFrameworkSchema = createInsertSchema(complianceFrameworks);

export const complianceRequirementSchema = createSelectSchema(complianceRequirements);
export const insertComplianceRequirementSchema = createInsertSchema(complianceRequirements);

export const complianceAssessmentSchema = createSelectSchema(complianceAssessments);
export const insertComplianceAssessmentSchema = createInsertSchema(complianceAssessments);

export const accessCertificationSchema = createSelectSchema(accessCertifications);
export const insertAccessCertificationSchema = createInsertSchema(accessCertifications);

export const complianceViolationSchema = createSelectSchema(complianceViolations);
export const insertComplianceViolationSchema = createInsertSchema(complianceViolations);

// Permission requests table
export const permissionRequests = pgTable('permission_requests', {
  id: serial('id').primaryKey(),
  request_id: text('request_id').notNull().unique(),
  requester_id: integer('requester_id').references(() => users.id, { onDelete: 'cascade' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Request details
  request_type: requestTypeEnum('request_type').notNull(),
  title: text('title').notNull(),
  description: text('description'),
  business_justification: text('business_justification').notNull(),
  priority: requestPriorityEnum('priority').default('medium'),
  urgency_reason: text('urgency_reason'),

  // Requested permissions/access
  requested_permissions: jsonb('requested_permissions').default([]), // Array of permission codes
  requested_role_id: integer('requested_role_id').references(() => customRoles.id, { onDelete: 'set null' }),
  requested_resources: jsonb('requested_resources').default([]), // Array of resource identifiers
  access_scope: text('access_scope'), // 'branch', 'department', 'company', 'specific_resources'
  temporary_access: boolean('temporary_access').default(false),
  access_start_date: timestamp('access_start_date'),
  access_end_date: timestamp('access_end_date'),

  // Request workflow
  status: requestStatusEnum('status').default('pending'),
  submitted_at: timestamp('submitted_at').defaultNow().notNull(),
  required_approvers: jsonb('required_approvers').default([]), // Array of required approver user IDs
  current_approver: integer('current_approver').references(() => users.id, { onDelete: 'set null' }),
  approval_workflow_id: integer('approval_workflow_id'), // Reference to approval workflow if exists

  // Approval tracking
  approved_by: integer('approved_by').references(() => users.id, { onDelete: 'set null' }),
  approved_at: timestamp('approved_at'),
  rejected_by: integer('rejected_by').references(() => users.id, { onDelete: 'set null' }),
  rejected_at: timestamp('rejected_at'),
  rejection_reason: text('rejection_reason'),
  cancelled_by: integer('cancelled_by').references(() => users.id, { onDelete: 'set null' }),
  cancelled_at: timestamp('cancelled_at'),
  cancellation_reason: text('cancellation_reason'),

  // Implementation tracking
  implemented_by: integer('implemented_by').references(() => users.id, { onDelete: 'set null' }),
  implemented_at: timestamp('implemented_at'),
  implementation_notes: text('implementation_notes'),
  expires_at: timestamp('expires_at'), // For temporary access requests

  // Additional context
  manager_id: integer('manager_id').references(() => users.id, { onDelete: 'set null' }),
  department: text('department'),
  cost_center: text('cost_center'),
  project_code: text('project_code'),
  compliance_requirements: jsonb('compliance_requirements').default([]), // Array of compliance requirements
  risk_assessment: text('risk_assessment'), // 'low', 'medium', 'high', 'critical'

  // Attachments and evidence
  attachments: jsonb('attachments').default([]), // Array of attachment references
  supporting_documents: jsonb('supporting_documents').default([]), // Array of supporting document references

  // Audit and metadata
  metadata: jsonb('metadata').default({}),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Request approvals table for multi-step approval workflows
export const requestApprovals = pgTable('request_approvals', {
  id: serial('id').primaryKey(),
  request_id: integer('request_id').references(() => permissionRequests.id, { onDelete: 'cascade' }),
  approver_id: integer('approver_id').references(() => users.id, { onDelete: 'cascade' }),
  approval_step: integer('approval_step').notNull(), // Order of approval in workflow
  approval_level: text('approval_level'), // 'manager', 'security', 'compliance', 'admin'

  // Approval details
  status: text('status').notNull().default('pending'), // 'pending', 'approved', 'rejected', 'delegated'
  decision_date: timestamp('decision_date'),
  comments: text('comments'),
  conditions: text('conditions'), // Any conditions attached to approval
  delegated_to: integer('delegated_to').references(() => users.id, { onDelete: 'set null' }),
  delegation_reason: text('delegation_reason'),

  // Timing
  due_date: timestamp('due_date'),
  reminder_sent: boolean('reminder_sent').default(false),
  escalated: boolean('escalated').default(false),
  escalated_to: integer('escalated_to').references(() => users.id, { onDelete: 'set null' }),
  escalation_date: timestamp('escalation_date'),

  // Audit trail
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueRequestApprover: unique().on(table.request_id, table.approver_id, table.approval_step),
}));

// Request comments/history table
export const requestComments = pgTable('request_comments', {
  id: serial('id').primaryKey(),
  request_id: integer('request_id').references(() => permissionRequests.id, { onDelete: 'cascade' }),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }),

  // Comment details
  comment_type: text('comment_type').notNull(), // 'comment', 'status_change', 'approval', 'rejection', 'question'
  comment: text('comment').notNull(),
  is_internal: boolean('is_internal').default(false), // Internal comments not visible to requester
  visibility: text('visibility').default('all'), // 'all', 'approvers_only', 'admins_only'

  // Attachments
  attachments: jsonb('attachments').default([]),

  // Audit trail
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// User access dashboard view for quick access status
export const userAccessSummary = pgTable('user_access_summary', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }),

  // Access summary
  total_permissions: integer('total_permissions').default(0),
  active_permissions: integer('active_permissions').default(0),
  temporary_permissions: integer('temporary_permissions').default(0),
  pending_requests: integer('pending_requests').default(0),
  recent_access_changes: integer('recent_access_changes').default(0),

  // Risk and compliance
  risk_score: integer('risk_score').default(0), // 0-100 risk score
  compliance_status: text('compliance_status').default('compliant'),
  last_access_review: timestamp('last_access_review'),
  next_access_review: timestamp('next_access_review'),

  // Activity summary
  last_login: timestamp('last_login'),
  last_permission_use: timestamp('last_permission_use'),
  failed_access_attempts: integer('failed_access_attempts').default(0),

  // Metadata
  summary_data: jsonb('summary_data').default({}), // Additional summary information
  last_updated: timestamp('last_updated').defaultNow().notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  uniqueUserCompany: unique().on(table.user_id, table.company_id),
}));

// User templates for template-based user creation
export const userTemplates = pgTable('user_templates', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),

  // Template configuration
  template_config: jsonb('template_config').notNull(), // User field defaults, role assignments, etc.
  default_role: userRoleEnum('default_role').notNull(),
  default_permissions: jsonb('default_permissions').default([]), // Array of permission codes
  default_roles: jsonb('default_roles').default([]), // Array of role IDs

  // Organizational defaults
  default_branch_id: integer('default_branch_id').references(() => branches.id, { onDelete: 'set null' }),
  default_department_id: integer('default_department_id').references(() => departments.id, { onDelete: 'set null' }),

  // Template metadata
  is_active: boolean('is_active').default(true).notNull(),
  usage_count: integer('usage_count').default(0).notNull(),
  created_by: integer('created_by').references(() => users.id, { onDelete: 'set null' }),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueNamePerCompany: unique().on(table.name, table.company_id),
}));

// Bulk operation logs for tracking bulk operations
export const bulkOperationLogs = pgTable('bulk_operation_logs', {
  id: serial('id').primaryKey(),
  operation_type: text('operation_type').notNull(), // 'user_import', 'user_export', 'bulk_role_assign', etc.
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  initiated_by: integer('initiated_by').references(() => users.id, { onDelete: 'set null' }),

  // Operation details
  total_records: integer('total_records').default(0).notNull(),
  successful_records: integer('successful_records').default(0).notNull(),
  failed_records: integer('failed_records').default(0).notNull(),

  // Operation data
  operation_data: jsonb('operation_data'), // Input parameters, file info, etc.
  results: jsonb('results'), // Success/failure details, error messages, etc.

  // Status and timing
  status: text('status').default('pending').notNull(), // 'pending', 'processing', 'completed', 'failed'
  started_at: timestamp('started_at').defaultNow().notNull(),
  completed_at: timestamp('completed_at'),

  // Metadata
  file_name: text('file_name'), // Original filename for imports
  file_size: integer('file_size'), // File size in bytes
  processing_time_ms: integer('processing_time_ms'), // Processing time in milliseconds

  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Relations for self-service portal tables
export const permissionRequestsRelations = relations(permissionRequests, ({ one, many }) => ({
  requester: one(users, {
    fields: [permissionRequests.requester_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [permissionRequests.company_id],
    references: [companies.id],
  }),
  requestedRole: one(customRoles, {
    fields: [permissionRequests.requested_role_id],
    references: [customRoles.id],
  }),
  currentApprover: one(users, {
    fields: [permissionRequests.current_approver],
    references: [users.id],
  }),
  approvedBy: one(users, {
    fields: [permissionRequests.approved_by],
    references: [users.id],
  }),
  rejectedBy: one(users, {
    fields: [permissionRequests.rejected_by],
    references: [users.id],
  }),
  cancelledBy: one(users, {
    fields: [permissionRequests.cancelled_by],
    references: [users.id],
  }),
  implementedBy: one(users, {
    fields: [permissionRequests.implemented_by],
    references: [users.id],
  }),
  manager: one(users, {
    fields: [permissionRequests.manager_id],
    references: [users.id],
  }),
  approvals: many(requestApprovals),
  comments: many(requestComments),
}));

export const requestApprovalsRelations = relations(requestApprovals, ({ one }) => ({
  request: one(permissionRequests, {
    fields: [requestApprovals.request_id],
    references: [permissionRequests.id],
  }),
  approver: one(users, {
    fields: [requestApprovals.approver_id],
    references: [users.id],
  }),
  delegatedTo: one(users, {
    fields: [requestApprovals.delegated_to],
    references: [users.id],
  }),
  escalatedTo: one(users, {
    fields: [requestApprovals.escalated_to],
    references: [users.id],
  }),
}));

export const requestCommentsRelations = relations(requestComments, ({ one }) => ({
  request: one(permissionRequests, {
    fields: [requestComments.request_id],
    references: [permissionRequests.id],
  }),
  user: one(users, {
    fields: [requestComments.user_id],
    references: [users.id],
  }),
}));

export const userAccessSummaryRelations = relations(userAccessSummary, ({ one }) => ({
  user: one(users, {
    fields: [userAccessSummary.user_id],
    references: [users.id],
  }),
  company: one(companies, {
    fields: [userAccessSummary.company_id],
    references: [companies.id],
  }),
}));

export const userTemplatesRelations = relations(userTemplates, ({ one }) => ({
  company: one(companies, {
    fields: [userTemplates.company_id],
    references: [companies.id],
  }),
  createdBy: one(users, {
    fields: [userTemplates.created_by],
    references: [users.id],
  }),
  defaultBranch: one(branches, {
    fields: [userTemplates.default_branch_id],
    references: [branches.id],
  }),
  defaultDepartment: one(departments, {
    fields: [userTemplates.default_department_id],
    references: [departments.id],
  }),
}));

export const bulkOperationLogsRelations = relations(bulkOperationLogs, ({ one }) => ({
  company: one(companies, {
    fields: [bulkOperationLogs.company_id],
    references: [companies.id],
  }),
  initiatedBy: one(users, {
    fields: [bulkOperationLogs.initiated_by],
    references: [users.id],
  }),
}));

// TypeScript types for self-service portal tables
export type PermissionRequest = typeof permissionRequests.$inferSelect;
export type InsertPermissionRequest = typeof permissionRequests.$inferInsert;

export type RequestApproval = typeof requestApprovals.$inferSelect;
export type InsertRequestApproval = typeof requestApprovals.$inferInsert;

export type RequestComment = typeof requestComments.$inferSelect;
export type InsertRequestComment = typeof requestComments.$inferInsert;

export type UserAccessSummary = typeof userAccessSummary.$inferSelect;
export type InsertUserAccessSummary = typeof userAccessSummary.$inferInsert;

export type UserTemplate = typeof userTemplates.$inferSelect;
export type InsertUserTemplate = typeof userTemplates.$inferInsert;

export type BulkOperationLog = typeof bulkOperationLogs.$inferSelect;
export type InsertBulkOperationLog = typeof bulkOperationLogs.$inferInsert;

// Zod schemas for self-service portal tables
export const permissionRequestSchema = createSelectSchema(permissionRequests);
export const insertPermissionRequestSchema = createInsertSchema(permissionRequests);

export const requestApprovalSchema = createSelectSchema(requestApprovals);
export const insertRequestApprovalSchema = createInsertSchema(requestApprovals);

export const requestCommentSchema = createSelectSchema(requestComments);
export const insertRequestCommentSchema = createInsertSchema(requestComments);

export const userAccessSummarySchema = createSelectSchema(userAccessSummary);
export const insertUserAccessSummarySchema = createInsertSchema(userAccessSummary);

export const userTemplateSchema = createSelectSchema(userTemplates);
export const insertUserTemplateSchema = createInsertSchema(userTemplates);

export const bulkOperationLogSchema = createSelectSchema(bulkOperationLogs);
export const insertBulkOperationLogSchema = createInsertSchema(bulkOperationLogs);

// Additional self-service portal types
export interface PermissionRequestContext {
  userId: number;
  companyId: number;
  requestType: 'permission_grant' | 'permission_revoke' | 'role_change' | 'access_extension' | 'temporary_access';
  title: string;
  description?: string;
  businessJustification: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  requestedPermissions?: string[];
  requestedRoleId?: number;
  temporaryAccess?: boolean;
  accessStartDate?: Date;
  accessEndDate?: Date;
  managerId?: number;
  department?: string;
  urgencyReason?: string;
}

export interface AccessStatusSummary {
  totalPermissions: number;
  activePermissions: number;
  temporaryPermissions: number;
  pendingRequests: number;
  recentChanges: number;
  riskScore: number;
  complianceStatus: string;
  lastAccessReview?: Date;
  nextAccessReview?: Date;
}

// Bulk operations types
export interface UserTemplateConfig {
  defaultFields: {
    full_name?: string;
    phone?: string;
    email?: string;
    address?: string;
  };
  organizationalDefaults: {
    branch_id?: number;
    department_id?: number;
    manager_id?: number;
  };
  accessDefaults: {
    role: string;
    permissions: string[];
    roles: number[];
  };
}

// ==================== MFA SYSTEM TABLES ====================

// User MFA settings table
export const userMfaSettings = pgTable('user_mfa_settings', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  is_enabled: boolean('is_enabled').default(false).notNull(),
  secret: text('secret'), // TOTP secret (base32 encoded)
  backup_codes: jsonb('backup_codes').default([]), // Array of backup codes
  last_used_at: timestamp('last_used_at'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  uniqueUserId: unique().on(table.user_id),
}));

// MFA verification attempts table
export const mfaVerificationAttempts = pgTable('mfa_verification_attempts', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  attempt_type: text('attempt_type').notNull(), // 'totp', 'backup_code'
  success: boolean('success').notNull(),
  ip_address: text('ip_address'),
  user_agent: text('user_agent'),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Email verification tokens table
export const emailVerificationTokens = pgTable('email_verification_tokens', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  token: text('token').notNull().unique(),
  expires_at: timestamp('expires_at').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Relations for MFA tables
export const userMfaSettingsRelations = relations(userMfaSettings, ({ one }) => ({
  user: one(users, {
    fields: [userMfaSettings.user_id],
    references: [users.id],
  }),
}));

export const mfaVerificationAttemptsRelations = relations(mfaVerificationAttempts, ({ one }) => ({
  user: one(users, {
    fields: [mfaVerificationAttempts.user_id],
    references: [users.id],
  }),
}));

export const emailVerificationTokensRelations = relations(emailVerificationTokens, ({ one }) => ({
  user: one(users, {
    fields: [emailVerificationTokens.user_id],
    references: [users.id],
  }),
}));

// ==================== TYPESCRIPT INTERFACES ====================

export interface BulkUserImportData {
  full_name: string;
  username: string;
  email: string;
  password?: string;
  role: string;
  phone?: string;
  branch_id?: number;
  department_id?: number;
  manager_id?: number;
  template_id?: number;
}

export interface BulkOperationResult {
  success: number;
  failed: number;
  errors: string[];
  details: {
    successfulRecords: any[];
    failedRecords: Array<{ record: any; error: string }>;
  };
}

export interface BulkRoleAssignmentData {
  user_ids: number[];
  role_ids: number[];
  action: 'assign' | 'remove';
  justification?: string;
}

// MFA interfaces
export interface MFASetupData {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export interface MFAVerificationData {
  token: string;
  type: 'totp' | 'backup_code';
}

// Type definitions for new tables
export type InsertUserMfaSettings = typeof userMfaSettings.$inferInsert;
export type SelectUserMfaSettings = typeof userMfaSettings.$inferSelect;
export type InsertMfaVerificationAttempt = typeof mfaVerificationAttempts.$inferInsert;
export type SelectMfaVerificationAttempt = typeof mfaVerificationAttempts.$inferSelect;
export type InsertEmailVerificationToken = typeof emailVerificationTokens.$inferInsert;
export type SelectEmailVerificationToken = typeof emailVerificationTokens.$inferSelect;