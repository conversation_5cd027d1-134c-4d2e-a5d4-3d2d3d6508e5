import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { useContextData } from '@/lib/useContextData';

// UI Components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';

// Icons
import {
  Plus, Search, MoreHorizontal, Edit, Eye, BarChart,
  ArrowUpDown, Check, XCircle
} from 'lucide-react';

// Interface for Account type
interface Account {
  id: number;
  company_id: number;
  account_code: string;
  account_name: string;
  account_type: 'asset' | 'liability' | 'equity' | 'income' | 'expense';
  parent_account_id: number | null;
  is_active: boolean;
  is_system: boolean;
  created_at: string;
  updated_at: string;
  // Additional fields that may be returned with account details
  parent_account?: {
    id: number;
    account_code: string;
    account_name: string;
  };
  balance?: number;
  description?: string;
}

const AccountsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [, navigate] = useLocation();
  const { companyId } = useContextData();

  // Fetch accounts
  const {
    data: accounts = [],
    isLoading,
    isError
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'accounts'],
    queryFn: async () => {
      if (!companyId) return [];
      const response = await apiRequest('GET', `/api/companies/${companyId}/accounts`);
      return await response.json();
    },
    enabled: !!companyId
  });

  // Filter accounts based on search term
  const filteredAccounts = accounts.filter((account: Account) =>
    account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.account_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group accounts by type for better organization
  const groupedAccounts = filteredAccounts.reduce((groups: Record<string, Account[]>, account: Account) => {
    const type = account.account_type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(account);
    return groups;
  }, {});

  // Format account types for display
  const formatAccountType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Determine badge color based on account type
  const getAccountTypeBadge = (type: string) => {
    switch (type) {
      case 'asset':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'liability':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'equity':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'income':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'expense':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center min-h-[60vh]">
        <Spinner size="lg" />
      </div>
    );
  }

  // Handle error state
  if (isError) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>Error Loading Accounts</CardTitle>
            <CardDescription>
              There was an error loading the accounts. Please try again.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center p-6">
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader className="flex flex-col space-y-1.5 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 pb-4">
          <div>
            <CardTitle className="text-2xl font-bold">Chart of Accounts</CardTitle>
            <CardDescription>
              Manage your financial accounts and view their balances
            </CardDescription>
          </div>
          <Button
            onClick={() => navigate('/financial/accounts/create')}
            className="w-full sm:w-auto mt-3 sm:mt-0"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Account
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between mb-6 space-y-2 sm:space-y-0 sm:space-x-2">
            <div className="relative w-full sm:w-1/3">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="Search accounts..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            {/* Additional filters could be added here */}
          </div>

          {filteredAccounts.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No accounts found matching your search criteria.</p>
              <Button onClick={() => navigate('/financial/accounts/create')}>
                <Plus className="mr-2 h-4 w-4" />
                Create New Account
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">Code</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead className="w-[150px]">
                      <div className="flex items-center">
                        Type
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead className="w-[120px] text-right">Balance</TableHead>
                    <TableHead className="w-[100px] text-center">Status</TableHead>
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {/* Flatten the structure to avoid using React.Fragment */}
                  {Object.keys(groupedAccounts).flatMap((accountType) => [
                    // Header row for each account type
                    <TableRow key={`header-${accountType}`} className="bg-blue-50">
                      <TableCell colSpan={6} className="font-medium">
                        {formatAccountType(accountType)} Accounts
                      </TableCell>
                    </TableRow>,
                    // Then map over the accounts of this type
                    ...groupedAccounts[accountType].map((account: Account) => (
                      <TableRow key={account.id}>
                        <TableCell className="font-mono">{account.account_code}</TableCell>
                        <TableCell className="font-medium">
                          {account.account_name}
                          {account.parent_account && (
                            <span className="text-xs text-gray-500 block">
                              Sub-account of: {account.parent_account.account_name}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge className={getAccountTypeBadge(account.account_type)}>
                            {formatAccountType(account.account_type)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          {account.balance !== undefined
                            ? new Intl.NumberFormat('en-IN', {
                                style: 'currency',
                                currency: 'INR',
                                maximumFractionDigits: 0
                              }).format(account.balance)
                            : '-'
                          }
                        </TableCell>
                        <TableCell className="text-center">
                          {account.is_active ? (
                            <div className="flex items-center justify-center">
                              <Check className="h-5 w-5 text-green-500" />
                            </div>
                          ) : (
                            <div className="flex items-center justify-center">
                              <XCircle className="h-5 w-5 text-red-500" />
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => navigate(`/financial/accounts/${account.id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              {!account.is_system && (
                                <DropdownMenuItem onClick={() => navigate(`/financial/accounts/${account.id}/edit`)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem onClick={() => navigate(`/financial/accounts/${account.id}#transactions`)}>
                                <BarChart className="mr-2 h-4 w-4" />
                                Transactions
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ])}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AccountsPage;