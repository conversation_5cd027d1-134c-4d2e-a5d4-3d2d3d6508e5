-- Migration: Streamline System Roles to Essential 4
-- Description: Remove unnecessary system roles and keep only Owner, Employee, Collection Agent, System Administrator
-- Date: 2025-01-27

BEGIN;

-- Step 1: Identify roles to be removed
-- Roles to remove: Loan Officer, Financial Analyst, Agent

-- Step 2: Handle user_roles assignments gracefully
-- Check if any users are assigned to roles that will be deleted
DO $$
DECLARE
    role_to_remove RECORD;
    user_assignment RECORD;
    owner_role_id INTEGER;
    employee_role_id INTEGER;
BEGIN
    -- Get the Owner and Employee role IDs for fallback assignments
    SELECT id INTO owner_role_id FROM custom_roles WHERE name = 'Owner' AND is_system = true AND company_id IS NULL;
    SELECT id INTO employee_role_id FROM custom_roles WHERE name = 'Employee' AND is_system = true AND company_id IS NULL;
    
    -- Process each role to be removed
    FOR role_to_remove IN 
        SELECT id, name FROM custom_roles 
        WHERE name IN ('Loan Officer', 'Financial Analyst', 'Agent') 
        AND is_system = true AND company_id IS NULL
    LOOP
        RAISE NOTICE 'Processing role: % (ID: %)', role_to_remove.name, role_to_remove.id;
        
        -- Check for user assignments
        FOR user_assignment IN 
            SELECT ur.user_id, u.email, u.role as user_enum_role
            FROM user_roles ur
            JOIN users u ON ur.user_id = u.id
            WHERE ur.role_id = role_to_remove.id
        LOOP
            RAISE NOTICE 'Found user assignment: % (%) has role %', user_assignment.email, user_assignment.user_id, role_to_remove.name;
            
            -- Reassign based on user's enum role
            IF user_assignment.user_enum_role = 'owner' AND owner_role_id IS NOT NULL THEN
                -- Reassign to Owner system role
                UPDATE user_roles 
                SET role_id = owner_role_id 
                WHERE user_id = user_assignment.user_id AND role_id = role_to_remove.id;
                RAISE NOTICE 'Reassigned user % to Owner role', user_assignment.email;
            ELSIF employee_role_id IS NOT NULL THEN
                -- Reassign to Employee system role
                UPDATE user_roles 
                SET role_id = employee_role_id 
                WHERE user_id = user_assignment.user_id AND role_id = role_to_remove.id;
                RAISE NOTICE 'Reassigned user % to Employee role', user_assignment.email;
            ELSE
                -- If no fallback available, remove the assignment
                DELETE FROM user_roles 
                WHERE user_id = user_assignment.user_id AND role_id = role_to_remove.id;
                RAISE NOTICE 'Removed role assignment for user % (no fallback available)', user_assignment.email;
            END IF;
        END LOOP;
    END LOOP;
END $$;

-- Step 3: Remove role_permissions for roles to be deleted
DELETE FROM role_permissions 
WHERE role_id IN (
    SELECT id FROM custom_roles 
    WHERE name IN ('Loan Officer', 'Financial Analyst', 'Agent') 
    AND is_system = true AND company_id IS NULL
);

-- Step 4: Remove the unwanted system roles
DELETE FROM custom_roles 
WHERE name IN ('Loan Officer', 'Financial Analyst', 'Agent') 
AND is_system = true AND company_id IS NULL;

-- Step 5: Verify the remaining system roles
-- Ensure we have exactly 4 system roles
DO $$
DECLARE
    system_role_count INTEGER;
    missing_roles TEXT[];
    role_name TEXT;
BEGIN
    -- Count system roles
    SELECT COUNT(*) INTO system_role_count 
    FROM custom_roles 
    WHERE is_system = true AND company_id IS NULL;
    
    -- Check for missing essential roles
    missing_roles := ARRAY[]::TEXT[];
    
    FOR role_name IN SELECT unnest(ARRAY['Owner', 'Employee', 'Collection Agent', 'System Administrator'])
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM custom_roles 
            WHERE name = role_name AND is_system = true AND company_id IS NULL
        ) THEN
            missing_roles := array_append(missing_roles, role_name);
        END IF;
    END LOOP;
    
    -- Report results
    RAISE NOTICE 'System roles after cleanup: %', system_role_count;
    
    IF array_length(missing_roles, 1) > 0 THEN
        RAISE NOTICE 'Missing essential system roles: %', array_to_string(missing_roles, ', ');
    ELSE
        RAISE NOTICE 'All essential system roles are present';
    END IF;
    
    -- Ensure we have exactly 4 system roles
    IF system_role_count != 4 THEN
        RAISE WARNING 'Expected 4 system roles, found %', system_role_count;
    END IF;
END $$;

-- Step 6: Create missing essential roles if they don't exist
INSERT INTO custom_roles (name, description, company_id, is_system, created_at, updated_at)
SELECT 'Owner', 'Company owner with full administrative access and control', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM custom_roles 
    WHERE name = 'Owner' AND is_system = true AND company_id IS NULL
);

INSERT INTO custom_roles (name, description, company_id, is_system, created_at, updated_at)
SELECT 'Employee', 'Standard employee access', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM custom_roles 
    WHERE name = 'Employee' AND is_system = true AND company_id IS NULL
);

INSERT INTO custom_roles (name, description, company_id, is_system, created_at, updated_at)
SELECT 'Collection Agent', 'Collection and recovery specialist', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM custom_roles 
    WHERE name = 'Collection Agent' AND is_system = true AND company_id IS NULL
);

INSERT INTO custom_roles (name, description, company_id, is_system, created_at, updated_at)
SELECT 'System Administrator', 'Full system access with all permissions', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
    SELECT 1 FROM custom_roles 
    WHERE name = 'System Administrator' AND is_system = true AND company_id IS NULL
);

-- Step 7: Ensure proper permissions for essential roles
-- Owner and System Administrator get all permissions
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    cr.id,
    p.id,
    CURRENT_TIMESTAMP
FROM custom_roles cr
CROSS JOIN permissions p
WHERE cr.name IN ('Owner', 'System Administrator') 
AND cr.is_system = true 
AND cr.company_id IS NULL
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = cr.id AND rp.permission_id = p.id
);

-- Employee gets basic permissions
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    cr.id,
    p.id,
    CURRENT_TIMESTAMP
FROM custom_roles cr
CROSS JOIN permissions p
WHERE cr.name = 'Employee' 
AND cr.is_system = true 
AND cr.company_id IS NULL
AND p.code IN ('customer_view', 'loan_view', 'transaction_view')
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = cr.id AND rp.permission_id = p.id
);

-- Collection Agent gets collection-related permissions
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    cr.id,
    p.id,
    CURRENT_TIMESTAMP
FROM custom_roles cr
CROSS JOIN permissions p
WHERE cr.name = 'Collection Agent' 
AND cr.is_system = true 
AND cr.company_id IS NULL
AND (p.code LIKE '%collection_%' OR p.code IN ('customer_view', 'loan_view'))
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = cr.id AND rp.permission_id = p.id
);

-- Step 8: Final verification and reporting
DO $$
DECLARE
    role_summary RECORD;
BEGIN
    RAISE NOTICE '=== FINAL SYSTEM ROLES SUMMARY ===';
    
    FOR role_summary IN 
        SELECT 
            cr.name,
            cr.id,
            COUNT(rp.permission_id) as permission_count
        FROM custom_roles cr
        LEFT JOIN role_permissions rp ON cr.id = rp.role_id
        WHERE cr.is_system = true AND cr.company_id IS NULL
        GROUP BY cr.id, cr.name
        ORDER BY cr.name
    LOOP
        RAISE NOTICE 'Role: % (ID: %) - % permissions', 
            role_summary.name, role_summary.id, role_summary.permission_count;
    END LOOP;
END $$;

COMMIT;
