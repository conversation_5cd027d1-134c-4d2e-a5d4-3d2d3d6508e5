import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

interface Collection {
  id: number;
  [key: string]: any;
}

interface PaginatedResponse {
  collections: Collection[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
}

export default function TestPagination() {
  const [data, setData] = useState<PaginatedResponse | null>(null);
  const [error, setError,] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [queryParams, setQueryParams] = useState('');
  const [forcedDebug, setForcedDebug] = useState(false);
  
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const companyId = user?.company_id || 2;

  const fetchData = async () => {
    setLoading(true);
    try {
      // Build the request URL
      let url = `/api/companies/${companyId}/collections`;
      
      // Add parameters
      const params = new URLSearchParams();
      params.append('page', currentPage.toString());
      params.append('limit', itemsPerPage.toString());
      
      // Add a random query parameter to prevent caching
      params.append('_nocache', Date.now().toString());
      
      // Add debug flag
      if (forcedDebug) {
        params.append('_debug', 'true');
      }
      
      const queryString = params.toString();
      setQueryParams(queryString);
      url = `${url}?${queryString}`;
      
      console.log(`Fetching data with URL: ${url}`);
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log('API Response:', responseData);
      setData(responseData);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [companyId, currentPage, itemsPerPage, forcedDebug]);
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Pagination Test</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}
      
      <div className="mb-6 bg-white p-4 border rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Request Configuration</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="mb-3">
              <label className="block text-sm font-medium mb-1">Current Page:</label>
              <div className="flex items-center gap-2">
                <button 
                  className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage <= 1 || loading}
                >
                  Previous
                </button>
                <span className="mx-2 px-3 py-1 border rounded">
                  {currentPage}
                </span>
                <button 
                  className="px-3 py-1 bg-blue-500 text-white rounded disabled:bg-gray-300"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={loading || (data ? currentPage >= data.pagination.totalPages : false)}
                >
                  Next
                </button>
              </div>
            </div>
            
            <div className="mb-3">
              <label className="block text-sm font-medium mb-1">Items per page:</label>
              <select 
                value={itemsPerPage} 
                onChange={(e) => setItemsPerPage(Number(e.target.value))}
                className="border rounded px-2 py-1"
                disabled={loading}
              >
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </div>
            
            <div className="mb-3">
              <label className="block text-sm font-medium mb-1">Debug Mode:</label>
              <input 
                type="checkbox" 
                checked={forcedDebug} 
                onChange={() => setForcedDebug(!forcedDebug)}
                className="mr-2"
              />
              <span className="text-sm">Enable extra debugging</span>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium mb-1">Request Details:</h3>
            <p className="text-sm mb-1"><strong>Endpoint:</strong> /api/companies/{companyId}/collections</p>
            <p className="text-sm mb-3"><strong>Query:</strong> {queryParams}</p>
            
            <button 
              className="px-4 py-2 bg-green-500 text-white rounded disabled:bg-gray-300 mt-4"
              onClick={fetchData}
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Refresh Data'}
            </button>
          </div>
        </div>
      </div>
      
      <div className="mb-6 bg-white p-4 border rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold mb-2">Pagination Summary</h2>
        {loading ? (
          <p>Loading data...</p>
        ) : data ? (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="p-3 border rounded bg-blue-50">
              <div className="text-sm text-gray-500">Total Records</div>
              <div className="text-2xl font-semibold">{data.pagination.totalCount}</div>
            </div>
            <div className="p-3 border rounded bg-green-50">
              <div className="text-sm text-gray-500">Current Page</div>
              <div className="text-2xl font-semibold">{data.pagination.page}</div>
            </div>
            <div className="p-3 border rounded bg-purple-50">
              <div className="text-sm text-gray-500">Total Pages</div>
              <div className="text-2xl font-semibold">{data.pagination.totalPages}</div>
            </div>
            <div className="p-3 border rounded bg-yellow-50">
              <div className="text-sm text-gray-500">Records on Page</div>
              <div className="text-2xl font-semibold">{data.collections.length}</div>
            </div>
          </div>
        ) : (
          <p>No data available</p>
        )}
      </div>
      
      <div className="mb-6 bg-white p-4 border rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold mb-2">Collection IDs on Current Page</h2>
        {loading ? (
          <p>Loading...</p>
        ) : data?.collections && data.collections.length > 0 ? (
          <>
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.collections.map(collection => (
                  <tr key={collection.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{collection.company_collection_string || `#${collection.id}`}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{collection.status}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{collection.amount}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{new Date(collection.scheduled_date).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {/* Enhanced Pagination Controls */}
            {data.pagination.totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => currentPage > 1 && setCurrentPage(prev => Math.max(prev - 1, 1))}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>

                    {/* First page */}
                    {currentPage > 2 && (
                      <PaginationItem>
                        <PaginationLink onClick={() => setCurrentPage(1)}>1</PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Ellipsis if needed */}
                    {currentPage > 3 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Previous page if not the first */}
                    {currentPage > 1 && (
                      <PaginationItem>
                        <PaginationLink onClick={() => setCurrentPage(currentPage - 1)}>
                          {currentPage - 1}
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Current page */}
                    <PaginationItem>
                      <PaginationLink isActive>{currentPage}</PaginationLink>
                    </PaginationItem>

                    {/* Next page if not the last */}
                    {currentPage < data.pagination.totalPages && (
                      <PaginationItem>
                        <PaginationLink onClick={() => setCurrentPage(currentPage + 1)}>
                          {currentPage + 1}
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    {/* Ellipsis if needed */}
                    {currentPage < data.pagination.totalPages - 2 && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}

                    {/* Last page */}
                    {currentPage < data.pagination.totalPages - 1 && (
                      <PaginationItem>
                        <PaginationLink onClick={() => setCurrentPage(data.pagination.totalPages)}>
                          {data.pagination.totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    )}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => currentPage < data.pagination.totalPages && setCurrentPage(prev => Math.min(prev + 1, data.pagination.totalPages))}
                        className={currentPage === data.pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
            
            {/* Page info */}
            <div className="mt-4 flex justify-between items-center">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">Items per page:</span>
                <Select value={itemsPerPage.toString()} onValueChange={(value) => {
                  setItemsPerPage(parseInt(value));
                  setCurrentPage(1); // Reset to first page when changing items per page
                }}>
                  <SelectTrigger className="w-16 h-8">
                    <SelectValue placeholder="10" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="text-sm text-gray-500">
                Showing {data.collections.length} of {data.pagination.totalCount} collections
              </div>
            </div>
          </>
        ) : (
          <p className="text-center py-4">No collections found</p>
        )}
      </div>
      
      <div>
        <h2 className="text-lg font-semibold mb-2">Raw Response Data</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-96">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    </div>
  );
}
