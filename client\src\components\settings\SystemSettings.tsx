import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, Settings as SettingsIcon } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { PrefixSettingsTab } from "@/components/settings/PrefixSettingsTab";

// Define the schema for system settings
const systemSettingsSchema = z.object({
  date_format: z.string().min(1, "Date format is required"),
  currency_symbol: z.string().min(1, "Currency symbol is required"),
});

type SystemSettingsFormValues = z.infer<typeof systemSettingsSchema>;

export function SystemSettings() {
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const companyId = user?.company_id;
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);

  // Fetch company settings
  const {
    data: settings,
    isLoading: isSettingsLoading,
    error: settingsError,
  } = useQuery({
    queryKey: [`/api/companies/${companyId}/settings`],
    enabled: !!companyId,
  });

  // Set up form with default values
  const form = useForm<SystemSettingsFormValues>({
    resolver: zodResolver(systemSettingsSchema),
    defaultValues: {
      date_format: "dd-MM-yyyy",
      currency_symbol: "₹",
    },
  });

  // Update form when settings are loaded
  useEffect(() => {
    if (settings) {
      form.reset({
        date_format: settings.date_format || "dd-MM-yyyy",
        currency_symbol: settings.currency_symbol || "₹",
      });
    }
  }, [settings, form]);

  // Save settings mutation
  const saveSettingsMutation = useMutation({
    mutationFn: async (data: SystemSettingsFormValues) => {
      if (!companyId) throw new Error("Company ID is required");

      try {
        console.log("Saving settings:", data);
        console.log("Company ID:", companyId);

        const response = await apiRequest(
          "PUT",
          `/api/companies/${companyId}/settings`,
          data
        );

        // Check if response is OK
        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error Response:", errorText);
          throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        console.error("Error in mutation function:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("Settings saved successfully:", data);
      // Invalidate the settings query to refetch the latest data
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/settings`] });

      toast({
        title: "Settings saved",
        description: "Your system settings have been updated successfully.",
      });
    },
    onError: (error: any) => {
      console.error("Error saving settings:", error);

      let errorMessage = "Failed to save settings. Please try again.";
      if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = (data: SystemSettingsFormValues) => {
    setIsLoading(true);

    // Use XMLHttpRequest instead of fetch for better compatibility
    const xhr = new XMLHttpRequest();
    xhr.open('PUT', `/api/companies/${companyId}/settings`);
    xhr.setRequestHeader('Content-Type', 'application/json');

    // Get auth token from localStorage
    const token = localStorage.getItem('auth_token');
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    }

    xhr.onload = function() {
      setIsLoading(false);
      if (xhr.status >= 200 && xhr.status < 300) {
        // Success
        console.log("Settings saved successfully");

        // Invalidate the settings query to refetch the latest data
        queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/settings`] });

        toast({
          title: "Settings saved",
          description: "Your system settings have been updated successfully.",
        });
      } else {
        // Error
        console.error("Error saving settings:", xhr.statusText);
        toast({
          title: "Error",
          description: "Failed to save settings. Please try again.",
          variant: "destructive",
        });
      }
    };

    xhr.onerror = function() {
      setIsLoading(false);
      console.error("Network error when saving settings");
      toast({
        title: "Network Error",
        description: "Could not connect to the server. Please check your connection.",
        variant: "destructive",
      });
    };

    // Send the request
    xhr.send(JSON.stringify(data));
  };

  // Date format options
  const dateFormatOptions = [
    { value: "dd-MM-yyyy", label: "DD-MM-YYYY (31-12-2023)" },
    { value: "MM-dd-yyyy", label: "MM-DD-YYYY (12-31-2023)" },
    { value: "yyyy-MM-dd", label: "YYYY-MM-DD (2023-12-31)" },
    { value: "dd/MM/yyyy", label: "DD/MM/YYYY (31/12/2023)" },
    { value: "MM/dd/yyyy", label: "MM/DD/YYYY (12/31/2023)" },
    { value: "dd MMM yyyy", label: "DD MMM YYYY (31 Dec 2023)" },
    { value: "MMM dd, yyyy", label: "MMM DD, YYYY (Dec 31, 2023)" },
  ];

  // Common currency symbols
  const currencySymbols = [
    { value: "₹", label: "₹ - Indian Rupee" },
    { value: "$", label: "$ - US Dollar" },
    { value: "€", label: "€ - Euro" },
    { value: "£", label: "£ - British Pound" },
    { value: "¥", label: "¥ - Japanese Yen" },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <SettingsIcon className="mr-2 h-5 w-5" />
          System Settings
        </CardTitle>
        <CardDescription>
          Configure system-wide settings for your organization
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="general">General Settings</TabsTrigger>
            <TabsTrigger value="prefix">Reference Codes</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Date Format */}
                  <FormField
                    control={form.control}
                    name="date_format"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date Format</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select date format" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {dateFormatOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose how dates will be displayed throughout the application
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Currency Symbol */}
                  <FormField
                    control={form.control}
                    name="currency_symbol"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency Symbol</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select currency symbol" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencySymbols.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose the currency symbol for financial values
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={isLoading || isSettingsLoading}>
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Save Settings
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="prefix">
            <PrefixSettingsTab />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
