// <PERSON>ript to add basic permissions that are missing from the database
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function addBasicPermissions() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 Adding basic permissions...');
    
    // Basic permissions that should exist
    const basicPermissions = [
      // User management permissions
      ['user_view', 'View Users', 'Can view user list and details', 'user_management'],
      ['user_create', 'Create Users', 'Can create new users', 'user_management'],
      ['user_edit', 'Edit Users', 'Can edit existing users', 'user_management'],
      ['user_delete', 'Delete Users', 'Can delete users', 'user_management'],
      
      // Role management permissions
      ['role_view', 'View Roles', 'Can view role list and details', 'role_management'],
      ['role_create', 'Create Roles', 'Can create new roles', 'role_management'],
      ['role_edit', 'Edit Roles', 'Can edit existing roles', 'role_management'],
      ['role_delete', 'Delete Roles', 'Can delete roles', 'role_management'],
      
      // Group management permissions
      ['group_view', 'View Groups', 'Can view group list and details', 'group_management'],
      ['group_create', 'Create Groups', 'Can create new groups', 'group_management'],
      ['group_edit', 'Edit Groups', 'Can edit existing groups', 'group_management'],
      ['group_delete', 'Delete Groups', 'Can delete groups', 'group_management'],
      
      // Permission management
      ['permission_view', 'View Permissions', 'Can view permission list', 'permission_management'],
      ['permission_assign', 'Assign Permissions', 'Can assign permissions to roles', 'permission_management'],
      
      // Company management
      ['company_view', 'View Companies', 'Can view company list and details', 'company_management'],
      ['company_create', 'Create Companies', 'Can create new companies', 'company_management'],
      ['company_edit', 'Edit Companies', 'Can edit existing companies', 'company_management'],
      ['company_delete', 'Delete Companies', 'Can delete companies', 'company_management'],
      
      // Customer management
      ['customer_view', 'View Customers', 'Can view customer list and details', 'customer_management'],
      ['customer_create', 'Create Customers', 'Can create new customers', 'customer_management'],
      ['customer_edit', 'Edit Customers', 'Can edit existing customers', 'customer_management'],
      ['customer_delete', 'Delete Customers', 'Can delete customers', 'customer_management'],
      
      // Loan management
      ['loan_view', 'View Loans', 'Can view loan list and details', 'loan_management'],
      ['loan_create', 'Create Loans', 'Can create new loans', 'loan_management'],
      ['loan_edit', 'Edit Loans', 'Can edit existing loans', 'loan_management'],
      ['loan_delete', 'Delete Loans', 'Can delete loans', 'loan_management'],
      
      // Collection management
      ['collection_view', 'View Collections', 'Can view collection list and details', 'financial_management'],
      ['collection_create', 'Create Collections', 'Can create new collections', 'financial_management'],
      ['collection_edit', 'Edit Collections', 'Can edit existing collections', 'financial_management'],
      ['collection_delete', 'Delete Collections', 'Can delete collections', 'financial_management']
    ];
    
    console.log('\n🔗 Adding permissions...');
    
    for (const [code, name, description, category] of basicPermissions) {
      // Check if permission already exists
      const existingResult = await client.query(`
        SELECT id FROM permissions WHERE code = $1
      `, [code]);
      
      if (existingResult.rows.length === 0) {
        await client.query(`
          INSERT INTO permissions (code, name, description, category, created_at, updated_at)
          VALUES ($1, $2, $3, $4, NOW(), NOW())
        `, [code, name, description, category]);
        console.log(`  ✅ Added ${code} permission`);
      } else {
        console.log(`  ⏭️  ${code} already exists`);
      }
    }
    
    console.log('\n🎉 Basic permissions added successfully!');
    
  } catch (error) {
    console.error('❌ Error adding permissions:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
addBasicPermissions()
  .then(() => {
    console.log('\n✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
