import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { requirePrefixSettings } from '../middleware/prefix-settings';
import { insertAgentSchema, companyPrefixSettings, companies } from '@shared/schema';
import { ZodError } from 'zod';
import { db } from '../db';
import { eq } from 'drizzle-orm';

// Helper to get company name from company_id (fallback for prefix generation)
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);
    let prefix = '';

    if (words.length === 0) {
      prefix = 'COMP';
    } else if (words.length === 1) {
      // Single word: use first 2-3 characters
      const word = words[0].toUpperCase();
      prefix = word.length >= 3 ? word.substring(0, 3) : word;
    } else if (words.length === 2) {
      // Two words: use first 2 chars of each
      prefix = words[0].substring(0, 2).toUpperCase() + words[1].substring(0, 2).toUpperCase();
    } else {
      // Multiple words: use first char of first 3-4 words
      prefix = words.slice(0, Math.min(4, words.length))
        .map(word => word.charAt(0).toUpperCase())
        .join('');
    }

    // Ensure prefix is at least 2 characters and at most 4
    if (prefix.length < 2) {
      prefix = prefix.padEnd(2, 'X');
    } else if (prefix.length > 4) {
      prefix = prefix.substring(0, 4);
    }

    console.log(`Generated company prefix: ${prefix} for company ${companyId}`);
    return prefix;
  } catch (error) {
    console.error(`Error getting company name for company ${companyId}:`, error);
    return `COMP`;
  }
}

// Helper to get prefix from company_prefix_settings table
async function getPrefixFromSettings(companyId: number, entityType: 'loan' | 'collection' | 'customer' | 'partner' | 'agent'): Promise<{ prefix: string, startNumber: number }> {
  try {
    // Get company prefix settings
    const settings = await db.query.companyPrefixSettings.findFirst({
      where: eq(companyPrefixSettings.company_id, companyId),
    });

    if (!settings) {
      console.log(`No prefix settings found for company ${companyId}, falling back to company name`);
      // Fall back to company name-based prefix if no settings found
      const prefix = await getCompanyName(companyId);
      return { prefix, startNumber: 1 };
    }

    // Extract the appropriate prefix and start number based on entity type
    let prefix: string;
    let startNumber: number;

    switch (entityType) {
      case 'loan':
        prefix = settings.loan_prefix;
        startNumber = settings.loan_start_number;
        break;
      case 'collection':
        prefix = settings.collection_prefix;
        startNumber = settings.collection_start_number;
        break;
      case 'customer':
        prefix = settings.customer_prefix;
        startNumber = settings.customer_start_number;
        break;
      case 'partner':
        prefix = settings.partner_prefix;
        startNumber = settings.partner_start_number;
        break;
      case 'agent':
        prefix = settings.agent_prefix;
        startNumber = settings.agent_start_number;
        break;
      default:
        throw new Error(`Unknown entity type: ${entityType}`);
    }

    console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for ${entityType}`);
    return { prefix, startNumber };
  } catch (error) {
    console.error(`Error fetching prefix settings for company ${companyId}:`, error);
    // Fall back to company name-based prefix if error occurs
    const prefix = await getCompanyName(companyId);
    return { prefix, startNumber: 1 };
  }
}

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

export function registerAgentRoutes(app: Express): void {
  // Get all agents for a company
  app.get('/api/companies/:companyId/agents', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      
      if (isNaN(companyId)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      const agents = await storage.getAgentsByCompany(companyId);
      return res.json(agents);
    } catch (error) {
      console.error('Get agents error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get agent by ID
  app.get('/api/agents/:agentId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const agentId = parseInt(req.params.agentId, 10);
      
      if (isNaN(agentId)) {
        return res.status(400).json({ message: 'Invalid agent ID' });
      }

      const agent = await storage.getAgent(agentId);
      
      if (!agent) {
        return res.status(404).json({ message: 'Agent not found' });
      }

      // Check if user has access to this agent's company
      if (req.user!.role !== 'saas_admin' && agent.company_id !== req.user!.company_id) {
        const userId = req.user!.id;
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === agent.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this agent' });
        }
      }

      return res.json(agent);
    } catch (error) {
      console.error('Get agent error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create a new agent
  app.post('/api/agents', authMiddleware, requirePrefixSettings, async (req: AuthRequest, res: Response) => {
    try {
      // Ensure user authentication and company context
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      if (req.user.company_id === null || req.user.company_id === undefined) {
        return res.status(400).json({ message: 'Company context required' });
      }

      const companyId = req.user.company_id;

      // Validate phone number format
      const phoneNumber = req.body.phone;
      if (!phoneNumber) {
        return res.status(400).json({ message: 'Phone number is required' });
      }

      // Check if phone number has the correct format (+91 followed by 10 digits)
      const phoneRegex = /^\+91\d{10}$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(400).json({
          message: 'Phone number must be exactly 10 digits with +91 country code',
          field: 'phone'
        });
      }

      // Validate agent data
      const result = insertAgentSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid agent data',
          errors: formatZodError(result.error)
        });
      }

      // Generate company-specific agent reference code
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'agent');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for agent`);

      // Get the highest existing agent reference code for this company
      const highestSerial = await storage.getHighestAgentSerial(companyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const agentReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated agent reference code: ${agentReferenceCode} for company ${companyId}`);

      // Add the reference code to the agent data
      const agentDataWithReferenceCode = {
        ...result.data,
        agent_reference_code: agentReferenceCode
      };

      const agent = await storage.createAgent(agentDataWithReferenceCode);
      return res.status(201).json(agent);
    } catch (error) {
      console.error('Create agent error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update an agent
  app.patch('/api/agents/:agentId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const agentId = parseInt(req.params.agentId, 10);
      const companyId = parseInt(req.body.company_id, 10);

      if (isNaN(agentId)) {
        return res.status(400).json({ message: 'Invalid agent ID' });
      }

      // Check if user has access to this company
      if (req.user!.role !== 'saas_admin' && companyId !== req.user!.company_id) {
        const userId = req.user!.id;
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === companyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Validate phone number format if provided
      if (req.body.phone) {
        const phoneRegex = /^\+91\d{10}$/;
        if (!phoneRegex.test(req.body.phone)) {
          return res.status(400).json({
            message: 'Phone number must be exactly 10 digits with +91 country code',
            field: 'phone'
          });
        }
      }

      const updatedAgent = await storage.updateAgent(agentId, companyId, req.body);
      
      if (!updatedAgent) {
        return res.status(404).json({ message: 'Agent not found' });
      }

      return res.json(updatedAgent);
    } catch (error) {
      console.error('Update agent error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Delete an agent
  app.delete('/api/agents/:agentId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const agentId = parseInt(req.params.agentId, 10);
      const companyId = parseInt(req.body.company_id, 10);

      if (isNaN(agentId)) {
        return res.status(400).json({ message: 'Invalid agent ID' });
      }

      // Check if user has access to this company
      if (req.user!.role !== 'saas_admin' && companyId !== req.user!.company_id) {
        const userId = req.user!.id;
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === companyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      const success = await storage.deleteAgent(agentId, companyId);
      
      if (!success) {
        return res.status(404).json({ message: 'Agent not found' });
      }

      return res.json({ message: 'Agent deleted successfully' });
    } catch (error) {
      console.error('Delete agent error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
