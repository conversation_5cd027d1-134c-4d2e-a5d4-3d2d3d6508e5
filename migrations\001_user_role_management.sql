-- Migration for User, Role, and Permission Management

-- Create permissions table
CREATE TABLE IF NOT EXISTS "permissions" (
  "id" SERIAL PRIMARY KEY,
  "code" VARCHAR(100) NOT NULL UNIQUE,
  "name" VARCHAR(100) NOT NULL,
  "description" TEXT,
  "category" VARCHAR(50) NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create custom roles table
CREATE TABLE IF NOT EXISTS "custom_roles" (
  "id" SERIAL PRIMARY KEY,
  "company_id" INTEGER REFERENCES "companies" ("id") ON DELETE CASCADE,
  "name" VARCHAR(100) NOT NULL,
  "description" TEXT,
  "is_system" BOOLEAN NOT NULL DEFAULT FALSE,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("company_id", "name")
);

-- Create role-permission association table
CREATE TABLE IF NOT EXISTS "role_permissions" (
  "id" SERIAL PRIMARY KEY,
  "role_id" INTEGER NOT NULL REFERENCES "custom_roles" ("id") ON DELETE CASCADE,
  "permission_id" INTEGER NOT NULL REFERENCES "permissions" ("id") ON DELETE CASCADE,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("role_id", "permission_id")
);

-- Create user-role association table
CREATE TABLE IF NOT EXISTS "user_roles" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
  "role_id" INTEGER NOT NULL REFERENCES "custom_roles" ("id") ON DELETE CASCADE,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("user_id", "role_id")
);

-- Create group-role association table
CREATE TABLE IF NOT EXISTS "group_roles" (
  "id" SERIAL PRIMARY KEY,
  "group_id" INTEGER NOT NULL REFERENCES "groups" ("id") ON DELETE CASCADE,
  "role_id" INTEGER NOT NULL REFERENCES "custom_roles" ("id") ON DELETE CASCADE,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("group_id", "role_id")
);

-- Create group-user association table
CREATE TABLE IF NOT EXISTS "group_users" (
  "id" SERIAL PRIMARY KEY,
  "group_id" INTEGER NOT NULL REFERENCES "groups" ("id") ON DELETE CASCADE,
  "user_id" INTEGER NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("group_id", "user_id")
);

-- Insert default permissions
INSERT INTO "permissions" ("code", "name", "description", "category")
VALUES
  -- User management permissions
  ('user_view', 'View Users', 'Can view user list and details', 'User Management'),
  ('user_create', 'Create Users', 'Can create new users', 'User Management'),
  ('user_edit', 'Edit Users', 'Can edit existing users', 'User Management'),
  ('user_delete', 'Delete Users', 'Can delete users', 'User Management'),
  
  -- Role management permissions
  ('role_view', 'View Roles', 'Can view role list and details', 'Role Management'),
  ('role_create', 'Create Roles', 'Can create new roles', 'Role Management'),
  ('role_edit', 'Edit Roles', 'Can edit existing roles', 'Role Management'),
  ('role_delete', 'Delete Roles', 'Can delete roles', 'Role Management'),
  
  -- Group management permissions
  ('group_view', 'View Groups', 'Can view group list and details', 'Group Management'),
  ('group_create', 'Create Groups', 'Can create new groups', 'Group Management'),
  ('group_edit', 'Edit Groups', 'Can edit existing groups', 'Group Management'),
  ('group_delete', 'Delete Groups', 'Can delete groups', 'Group Management'),
  
  -- Permission management
  ('permission_view', 'View Permissions', 'Can view permission list', 'Permission Management'),
  ('permission_assign', 'Assign Permissions', 'Can assign permissions to roles', 'Permission Management'),
  
  -- Company management
  ('company_view', 'View Companies', 'Can view company list and details', 'Company Management'),
  ('company_create', 'Create Companies', 'Can create new companies', 'Company Management'),
  ('company_edit', 'Edit Companies', 'Can edit existing companies', 'Company Management'),
  ('company_delete', 'Delete Companies', 'Can delete companies', 'Company Management');

-- Create default system roles
INSERT INTO "custom_roles" ("name", "description", "is_system")
VALUES
  ('System Administrator', 'Full system access with all permissions', TRUE),
  ('Owner', 'Full access to company resources', TRUE),
  ('Employee', 'Standard employee access', TRUE),
  ('Agent', 'Limited access for field agents', TRUE);

-- Assign all permissions to System Administrator role
INSERT INTO "role_permissions" ("role_id", "permission_id")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'System Administrator'), 
  id 
FROM "permissions";

-- Assign company-level permissions to Owner role
INSERT INTO "role_permissions" ("role_id", "permission_id")
SELECT
  (SELECT id FROM "custom_roles" WHERE name = 'Owner'),
  id
FROM "permissions"
WHERE "code" NOT IN ('company_create', 'company_delete');
