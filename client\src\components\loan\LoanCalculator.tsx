import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { calculateLoanPayment, calculateTotalInterestAmount } from '@/utils/calculate-loan-payment';
import { formatCurrency, formatLoanTerm, formatInterestType } from '@/utils/format-utils';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Calendar, DollarSign, Percent, Clock } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface LoanCalculatorProps {
  amount: number;
  interestRate: number;
  termMonths: number;
  interestType: string;
  startDate?: string;
  currencyCode?: string;
  locale?: string;
  paymentFrequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly';
  deductInterestUpfront?: boolean;
}

export const LoanCalculator: React.FC<LoanCalculatorProps> = ({
  amount,
  interestRate,
  termMonths,
  interestType,
  startDate,
  currencyCode = 'INR',
  locale = 'en-IN',
  paymentFrequency = 'monthly' as 'daily' | 'weekly' | 'biweekly' | 'monthly',
  deductInterestUpfront = true,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [periodPayment, setPeriodPayment] = useState<number>(0);
  const [totalInterest, setTotalInterest] = useState<number>(0);
  const [disbursedAmount, setDisbursedAmount] = useState<number>(0);
  const [totalRepayable, setTotalRepayable] = useState<number>(0);
  const [interestPercentage, setInterestPercentage] = useState<number>(0);

  // Format the payment frequency for display
  const formatPaymentFrequency = () => {
    switch(paymentFrequency) {
      case 'daily': return 'Daily';
      case 'weekly': return 'Weekly';
      case 'biweekly': return 'Biweekly';
      case 'monthly': return 'Monthly';
      default: return 'Monthly';
    }
  };

  // Using the utility function to format the term with appropriate units
  const formatTermUnits = () => {
    return formatLoanTerm(termMonths, paymentFrequency);
  };

  useEffect(() => {
    // Validate inputs before calculating
    if (amount > 0 && interestRate >= 0 && termMonths > 0) {
      // Calculate interest
      const interest = calculateTotalInterestAmount(
        amount, 
        interestRate, 
        termMonths, 
        interestType, 
        paymentFrequency
      );

      // Calculate disbursed amount (if interest is deducted upfront)
      const disbursed = deductInterestUpfront ? amount - interest : amount;

      // Calculate total repayable amount
      const repayable = amount; // In upfront model, you repay the principal
      
      // Calculate period payment
      const payment = calculateLoanPayment(
        amount, 
        interestRate, 
        termMonths, 
        interestType, 
        paymentFrequency, 
        deductInterestUpfront
      );

      // Interest as a percentage of total
      const interestPercent = (interest / amount) * 100;

      setPeriodPayment(payment);
      setTotalInterest(interest);
      setDisbursedAmount(disbursed);
      setTotalRepayable(repayable);
      setInterestPercentage(interestPercent);
    } else {
      // Set defaults for invalid inputs
      setPeriodPayment(0);
      setTotalInterest(0);
      setDisbursedAmount(0);
      setTotalRepayable(0);
      setInterestPercentage(0);
    }
  }, [amount, interestRate, termMonths, interestType, paymentFrequency, deductInterestUpfront]);

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>Loan Payment Summary</span>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowDetails(!showDetails)}
            className="w-8 h-8 p-0"
          >
            {showDetails ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CardTitle>
        <CardDescription>
          {formatCurrency(amount, currencyCode, locale)} at {interestRate}% {interestType} interest
          {deductInterestUpfront && " (deducted upfront)"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Period Payment */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">
                {formatPaymentFrequency()} Payment
              </h4>
              <p className="text-2xl font-bold">{formatCurrency(periodPayment, currencyCode, locale)}</p>
            </div>
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <Calendar className="h-5 w-5 text-primary" />
            </div>
          </div>
          
          <Separator />
          
          {/* Expanded Details */}
          {showDetails && (
            <div className="space-y-4 pt-2 animate-in fade-in duration-200">
              {/* Principal Breakdown */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium">Principal Amount</h4>
                  <span>{formatCurrency(amount, currencyCode, locale)}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium">Interest ({interestRate}%)</h4>
                  <span className="text-amber-600">{formatCurrency(totalInterest, currencyCode, locale)}</span>
                </div>
                
                {deductInterestUpfront && (
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium">Disbursed Amount</h4>
                    <span className="text-green-600">{formatCurrency(disbursedAmount, currencyCode, locale)}</span>
                  </div>
                )}
                
                <div className="flex justify-between items-center font-medium">
                  <h4 className="text-sm">Total Repayable</h4>
                  <span>{formatCurrency(totalRepayable, currencyCode, locale)}</span>
                </div>
                
                {/* Visual breakdown */}
                <div className="pt-2">
                  <div className="flex justify-between text-xs mb-1">
                    <span>Principal</span>
                    <span>Interest</span>
                  </div>
                  <div className="flex h-2 overflow-hidden rounded-full bg-muted">
                    <div 
                      className="bg-primary" 
                      style={{ width: `${100 - interestPercentage}%` }}
                    />
                    <div 
                      className="bg-amber-500" 
                      style={{ width: `${interestPercentage}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs mt-1">
                    <span>{Math.round(100 - interestPercentage)}%</span>
                    <span>{Math.round(interestPercentage)}%</span>
                  </div>
                </div>
              </div>
              
              {/* Loan Details */}
              <div className="grid grid-cols-2 gap-4 pt-2">
                <div className="bg-muted/50 p-3 rounded-md">
                  <div className="flex items-center gap-2 text-sm font-medium mb-1">
                    <DollarSign className="h-4 w-4" />
                    <span>Loan Amount</span>
                  </div>
                  <p className="text-lg font-semibold">{formatCurrency(amount, currencyCode, locale)}</p>
                </div>
                <div className="bg-muted/50 p-3 rounded-md">
                  <div className="flex items-center gap-2 text-sm font-medium mb-1">
                    <Percent className="h-4 w-4" />
                    <span>Interest Rate</span>
                  </div>
                  <p className="text-lg font-semibold">{interestRate}% {deductInterestUpfront && "(upfront)"}</p>
                </div>
              </div>
              
              {/* Loan Term Details */}
              <div className="pt-2">
                <h4 className="text-sm font-medium mb-1">Loan Term</h4>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">{formatTermUnits()}</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {paymentFrequency === 'monthly' && (
                    <>
                      {Math.floor(termMonths / 12) > 0 && `${Math.floor(termMonths / 12)} years `}
                      {termMonths % 12 > 0 && `${termMonths % 12} months`}
                    </>
                  )}
                  {paymentFrequency === 'weekly' && (
                    <>
                      {Math.floor(termMonths / 52) > 0 && `${Math.floor(termMonths / 52)} years `}
                      {Math.floor((termMonths % 52) / 4) > 0 && `${Math.floor((termMonths % 52) / 4)} months `}
                      {termMonths % 4 > 0 && `${termMonths % 4} weeks`}
                    </>
                  )}
                  {paymentFrequency === 'daily' && (
                    <>
                      {Math.floor(termMonths / 365) > 0 && `${Math.floor(termMonths / 365)} years `}
                      {Math.floor((termMonths % 365) / 30) > 0 && `${Math.floor((termMonths % 365) / 30)} months `}
                      {termMonths % 30 > 0 && `${termMonths % 30} days`}
                    </>
                  )}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LoanCalculator;