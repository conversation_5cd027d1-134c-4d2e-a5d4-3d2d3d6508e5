import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { format } from "date-fns";

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Loader2,
  RefreshCw,
  Save
} from "lucide-react";

// Form components
import SimpleStaticLoanForm from "@/components/loan/SimpleStaticLoanForm";

// Interface definitions
interface Loan {
  id: number;
  customer_id: number;
  company_id: number;
  amount: string | number;
  interest_rate: string | number;
  interest_type: 'flat' | 'reducing' | 'upfront';
  loan_type: 'personal' | 'business' | 'education' | 'mortgage' | 'vehicle' | 'other';
  term: number;
  terms_frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly';
  payment_frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly';
  is_upfront_interest: boolean;
  start_date: string;
  end_date: string;
  status: string;
  created_at: string;
  updated_at: string;
  disbursed_amount?: string | number;
  total_repayable?: string | number;
  installment_amount?: string | number;
  customer?: {
    id: number;
    full_name: string;
    email?: string;
    phone?: string;
  };
  notes?: string | null;
}

export default function EditLoanPage() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const user = getCurrentUser();
  const companyId = user?.company_id || 1;
  const loanId = params.id ? parseInt(params.id, 10) : 0;
  
  // State for form data
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Fetch loan data
  const { 
    data: loan, 
    isLoading: isLoadingLoan, 
    error: loanError 
  } = useQuery<Loan>({
    queryKey: [`/api/loans/${loanId}`],
    enabled: !isNaN(loanId)
  });
  
  // Fetch form submission data to get custom fields
  const {
    data: formSubmissionData,
    isLoading: isLoadingFormSubmission,
    refetch: refetchFormSubmissions
  } = useQuery<any>({
    queryKey: [`/api/companies/${companyId}/loans/${loanId}/form-submissions`],
    enabled: !isNaN(loanId) && !isNaN(companyId),
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    staleTime: 0, // Always consider data stale to force refetch
    gcTime: 0  // Don't cache the data
  });

  // Function to directly fetch form submission data bypassing cache
  const forceRefreshFormData = async () => {
    setIsRefreshing(true);
    try {
      const response = await apiRequest('GET', `/api/companies/${companyId}/loans/${loanId}/form-submissions`);
      if (response.ok) {
        const data = await response.json();
        // Manually update the cache with fresh data
        queryClient.setQueryData(
          [`/api/companies/${companyId}/loans/${loanId}/form-submissions`], 
          data
        );
        // Force a refetch to be sure
        await refetchFormSubmissions();
        
        toast({
          title: "Data refreshed",
          description: "Form data has been refreshed from the server",
        });
        
        console.log("Form submission data manually refreshed", data);
      }
    } catch (error) {
      console.error("Error manually refreshing form data:", error);
      toast({
        title: "Refresh failed",
        description: "Failed to refresh form data. Try again.",
        variant: "destructive"
      });
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // Initialize form data when loan data is loaded
  useEffect(() => {
    if (loan) {
      // Prepare base form data with loan values
      const baseFormData: Record<string, any> = {
        // Standard fields
        customer_id: loan.customer_id,
        amount: typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount),
        interest_rate: typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate),
        // TypeScript fix: Use type assertion to allow comparison with 'simple'
        interest_type: (loan.interest_type as string) === 'simple' ? 'flat' : loan.interest_type,
        term: typeof loan.term === 'number' ? loan.term : parseInt(String(loan.term), 10),
        start_date: typeof loan.start_date === 'string' ? loan.start_date.split('T')[0] : loan.start_date,
        end_date: typeof loan.end_date === 'string' ? loan.end_date.split('T')[0] : loan.end_date,
        notes: loan.notes || '',
        
        // Field mappings for calculators to work
        field_101: typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount),
        field_100: typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate),
        // TypeScript fix: Use type assertion to allow comparison with 'simple'
        field_102: (loan.interest_type as string) === 'simple' ? 'flat' : loan.interest_type,
        field_103: typeof loan.term === 'number' ? loan.term : parseInt(String(loan.term), 10),
        field_104: typeof loan.start_date === 'string' ? loan.start_date.split('T')[0] : loan.start_date,
        field_105: typeof loan.end_date === 'string' ? loan.end_date.split('T')[0] : loan.end_date,
        field_109: loan.customer_id,
      };
      
      // Extract form submission data if available
      let submissionData: Record<string, any> = {};
      if (formSubmissionData && formSubmissionData.length > 0) {
        const rawFormData = formSubmissionData[0]?.form_data;
        
        if (rawFormData) {
          // Parse form data if it's a string
          const parsedFormData: Record<string, any> = typeof rawFormData === 'string' ? 
            JSON.parse(rawFormData) : rawFormData;
            
          // Process the form data
          submissionData = parsedFormData;
          
          // Map 'simple' to 'flat' in form data
          if (parsedFormData.interest_type === 'simple' || parsedFormData.field_120 === 'simple') {
            submissionData.interest_type = 'flat';
            submissionData.field_120 = 'flat';
          }
          
          // Process any special fields
          if (parsedFormData.field_123 && typeof parsedFormData.field_123 === 'string') {
            // If customer ID is stored as "1 - Customer Name", extract just the ID
            const matches = parsedFormData.field_123.match(/^(\d+)/);
            if (matches && matches[1]) {
              submissionData.field_123 = parseInt(matches[1], 10);
            }
          }
          
          // Process numeric fields to ensure they're properly formatted
          ['field_121', 'field_122', 'field_125'].forEach(field => {
            if (parsedFormData[field]) {
              if (typeof parsedFormData[field] === 'string') {
                // Clean any non-numeric characters except decimal point
                const cleanedValue = parsedFormData[field].replace(/[^0-9.-]/g, '');
                submissionData[field] = field === 'field_125' ? 
                  parseInt(cleanedValue, 10) : parseFloat(cleanedValue);
              }
            }
          });
          
          // Process date fields to ensure consistency
          ['field_124', 'field_126'].forEach(field => {
            if (parsedFormData[field] && typeof parsedFormData[field] === 'string') {
              if (parsedFormData[field].includes('/')) {
                // Convert MM/DD/YYYY to YYYY-MM-DD
                const parts = parsedFormData[field].split('/');
                if (parts.length === 3) {
                  submissionData[field] = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
                }
              }
            }
          });
        }
      }
      
      // Merge base data with submission data, giving priority to submission data
      const initialFormData = {
        ...baseFormData,
        ...submissionData
      };
      
      console.log("Initializing form data in edit page:", initialFormData);
      setFormData(initialFormData);
    }
  }, [loan, formSubmissionData]);

  // Mutation for updating loan with improved error handling and cache management
  const updateLoanMutation = useMutation({
    mutationFn: async (loanData: any) => {
      console.log("Sending loan update request with data:", loanData);
      
      // Validate essential fields
      if (!loanData.company_id) {
        throw new Error("Company ID is required");
      }
      
      if (!loanData.customer_id) {
        throw new Error("Customer ID is required");
      }
      
      // Add is_upfront_interest if it exists in formValues
      if (formData.is_upfront_interest !== undefined) {
        loanData.is_upfront_interest = 
          typeof formData.is_upfront_interest === 'string' 
            ? formData.is_upfront_interest === 'true'
            : !!formData.is_upfront_interest;
      }
      
      // Add upfront interest calculated fields if applicable
      if (loanData.is_upfront_interest && loanData.amount && loanData.interest_rate) {
        const amount = parseFloat(loanData.amount);
        const interestRate = parseFloat(loanData.interest_rate) / 100;
        const interestAmount = amount * interestRate;
        
        loanData.disbursed_amount = (amount - interestAmount).toString();
        loanData.total_repayable = amount.toString();
        
        if (loanData.term) {
          loanData.installment_amount = (amount / parseFloat(loanData.term)).toString();
        }
      }
      
      // Make the API request
      const response = await apiRequest(
        'PATCH',
        `/api/loans/${loanId}`,
        loanData
      );
      
      if (!response.ok) {
        // Try to parse error details
        try {
          const errorData = await response.json();
          console.error("Loan update server error:", errorData);
          throw new Error(errorData.message || errorData.error || "Failed to update loan");
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }
      
      try {
        const responseData = await response.json();
        console.log("Loan update successful, received data:", responseData);
        return responseData;
      } catch (parseError) {
        console.error("Failed to parse success response:", parseError);
        // Return a placeholder to indicate success even if parsing failed
        return { id: loanId, updated: true };
      }
    },
    onSuccess: (data) => {
      console.log("Loan update mutation success, refreshing cache");
      
      // Force a refetch of the specific loan to ensure UI is updated
      queryClient.fetchQuery({ queryKey: [`/api/loans/${loanId}`] });
      
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: [`/api/loans/${loanId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
      queryClient.invalidateQueries({ queryKey: [`/api/customers/${loan?.customer_id}/loans`] });
      queryClient.invalidateQueries({ queryKey: [`/api/loans`] }); 
      
      // Invalidate related payment schedules and collections
      queryClient.invalidateQueries({ queryKey: [`/api/loans/${loanId}/payment-schedules`] });
      queryClient.invalidateQueries({ queryKey: [`/api/loans/${loanId}/collections`] });
      
      // Show success toast
      toast({
        title: "Loan updated",
        description: "The loan has been successfully updated",
      });
    },
    onError: (error: Error) => {
      console.error("Loan update mutation error:", error);
      
      toast({
        title: "Failed to update loan",
        description: error.message || "An unknown error occurred",
        variant: "destructive"
      });
      
      // Enable the submit button again
      setIsSubmitting(false);
    }
  });
  
  const handleFormDataChange = (data: Record<string, any>) => {
    console.log("Form data changed:", data);
    setFormData(data);
  };

  const handleBackToLoan = () => {
    setLocation(`/loans/${loanId}`);
  };
  
  const handleSubmit = () => {
    // Validate the form data
    const templateData = formData;
    console.log("Submit form data:", templateData);
    
    if (templateData && templateData._validate && templateData._validate()) {
      setIsSubmitting(true);
      
      try {
        // Extract the form data
        const { _validate, ...formValues } = templateData;
        console.log("Extracted form values:", formValues);
        
        // Validate critical fields
        if (!companyId) {
          throw new Error("Company ID is missing");
        }
        
        if (!formValues.customer_id) {
          throw new Error("Customer ID is required");
        }
        
        if (!formValues.amount || parseFloat(formValues.amount?.toString() || "0") <= 0) {
          throw new Error("Loan amount must be greater than zero");
        }
        
        if (!formValues.term || parseInt(formValues.term?.toString() || "0") <= 0) {
          throw new Error("Loan term must be greater than zero");
        }
        
        // Format dates correctly
        let startDate = formValues.start_date;
        let endDate = formValues.end_date;
        
        // Ensure dates are in YYYY-MM-DD format
        if (startDate && typeof startDate === 'string') {
          // Handle different date formats
          if (startDate.includes('/')) {
            const parts = startDate.split('/');
            if (parts.length === 3) {
              startDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
            }
          } else if (startDate.includes('T')) {
            startDate = startDate.split('T')[0];
          }
        }
        
        if (endDate && typeof endDate === 'string') {
          // Handle different date formats
          if (endDate.includes('/')) {
            const parts = endDate.split('/');
            if (parts.length === 3) {
              endDate = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
            }
          } else if (endDate.includes('T')) {
            endDate = endDate.split('T')[0];
          }
        }
        
        // Handle is_upfront_interest correctly
        const isUpfrontInterest = 
          formValues.is_upfront_interest !== undefined 
            ? (typeof formValues.is_upfront_interest === 'string' 
                ? formValues.is_upfront_interest === 'true' 
                : !!formValues.is_upfront_interest)
            : false;
            
        console.log("Parsed is_upfront_interest value:", isUpfrontInterest);
        
        // Calculate derived fields based on loan parameters
        const amount = parseFloat(formValues.amount?.toString() || "0");
        const interestRate = parseFloat(formValues.interest_rate?.toString() || "0");
        const term = parseInt(formValues.term?.toString() || "0");
        
        // Calculate interest amount
        const interestAmount = amount * (interestRate / 100);
        
        // Calculate disbursed amount (loan amount minus upfront interest if applicable)
        const disbursedAmount = isUpfrontInterest 
          ? amount - interestAmount 
          : amount;
          
        // Calculate total repayable amount
        const totalRepayable = isUpfrontInterest 
          ? amount 
          : amount + interestAmount;
          
        // Calculate installment amount
        const installmentAmount = totalRepayable / term;
        
        console.log("Loan calculations:", {
          amount,
          interestRate,
          term,
          interestAmount,
          disbursedAmount,
          totalRepayable,
          installmentAmount,
          isUpfrontInterest
        });
        
        // Prepare the loan data using standard field names from the schema
        const loanData = {
          company_id: companyId,
          customer_id: formValues.customer_id,
          amount: amount.toString(),
          interest_rate: interestRate.toString(),
          interest_type: formValues.interest_type || 'flat',
          term: term,
          payment_frequency: formValues.payment_frequency || 'monthly',
          terms_frequency: formValues.terms_frequency || formValues.payment_frequency || 'monthly',
          start_date: startDate || new Date().toISOString().split('T')[0],
          end_date: endDate || new Date().toISOString().split('T')[0],
          notes: formValues.notes || null,
          is_upfront_interest: isUpfrontInterest,
          disbursed_amount: disbursedAmount.toString(),
          total_repayable: totalRepayable.toString(),
          installment_amount: installmentAmount.toString()
        };
      
        // Add any custom fields (like firstname, lastname) to save in form_submissions
        const customFieldData: Record<string, any> = {};
        Object.keys(formValues).forEach(key => {
          if (key.startsWith('field_')) {
            // Safely access values using index notation with type assertion
            customFieldData[key] = (formValues as Record<string, any>)[key];
          }
        });
        console.log("Final loan data being sent:", loanData);
        
        // Call the mutation to update the loan
        updateLoanMutation.mutate(loanData, {
          onSuccess: (updatedLoan) => {
            console.log("Loan successfully updated:", updatedLoan);
            
            // Next, handle form submission update if we have custom fields
            if (Object.keys(customFieldData).length > 0) {
              // Get the submission ID
              const submissionId = formSubmissionData && formSubmissionData[0]?.id;
              if (submissionId) {
                // Prepare the update data
                const formSubmissionUpdateData = {
                  form_data: {
                    ...loanData,  // Include the standard fields
                    ...customFieldData // Include custom fields
                  }
                };
                
                console.log("Updating form submission with ID:", submissionId, formSubmissionUpdateData);
                
                // Update the form submission
                apiRequest(
                  'PUT',
                  `/api/companies/${companyId}/form-submissions/${submissionId}`,
                  formSubmissionUpdateData
                )
                  .then(response => {
                    if (response.ok) {
                      console.log("Successfully updated form submission");
                      // Invalidate related queries to ensure fresh data
                      queryClient.invalidateQueries({ 
                        queryKey: [`/api/companies/${companyId}/loans/${loanId}/form-submissions`] 
                      });
                      
                      // Show a combined success message
                      toast({
                        title: "Updates Complete",
                        description: "Loan and form submissions updated successfully",
                      });
                      
                      // Redirect back to the loan details page after a short delay to allow for cache updates
                      setTimeout(() => {
                        setLocation(`/loans/${loanId}`);
                      }, 500);
                    } else {
                      console.error("Failed to update form submission:", response.statusText);
                      
                      toast({
                        title: "Partial Success",
                        description: "Loan updated but form submission update failed",
                        variant: "warning"
                      });
                      
                      // Still redirect after a delay, as the core loan data was updated
                      setTimeout(() => {
                        setLocation(`/loans/${loanId}`);
                      }, 1500);
                    }
                  })
                  .catch(error => {
                    console.error("Error updating form submission:", error);
                    
                    toast({
                      title: "Partial Success",
                      description: "Loan updated but form submission update failed: " + 
                        (error instanceof Error ? error.message : "Unknown error"),
                      variant: "warning"
                    });
                    
                    // Still redirect after a delay, as the core loan data was updated
                    setTimeout(() => {
                      setLocation(`/loans/${loanId}`);
                    }, 1500);
                  })
                  .finally(() => {
                    setIsSubmitting(false);
                  });
              } else {
                // No existing form submission found, but the loan was updated
                console.log("No form submission found to update");
                setIsSubmitting(false);
                
                toast({
                  title: "Loan Updated",
                  description: "Loan has been successfully updated",
                });
                
                // Redirect after a short delay
                setTimeout(() => {
                  setLocation(`/loans/${loanId}`);
                }, 500);
              }
            } else {
              // No custom fields to update
              console.log("No custom fields to update");
              setIsSubmitting(false);
              
              toast({
                title: "Loan Updated",
                description: "Loan has been successfully updated",
              });
              
              // Redirect after a short delay
              setTimeout(() => {
                setLocation(`/loans/${loanId}`);
              }, 500);
            }
          },
          onError: (error) => {
            console.error("Error updating loan:", error);
            
            toast({
              title: "Update Failed",
              description: error instanceof Error ? error.message : "Failed to update loan",
              variant: "destructive"
            });
            
            setIsSubmitting(false);
          }
        });
      } catch (validationError) {
        // Handle errors in the form data preparation
        console.error("Validation error:", validationError);
        
        toast({
          title: "Validation Error",
          description: validationError instanceof Error ? validationError.message : "Form validation failed",
          variant: "destructive"
        });
        
        setIsSubmitting(false);
      }
    } else {
      toast({
        title: "Form validation failed",
        description: "Please check all fields and try again",
        variant: "destructive"
      });
    }
  };
  
  // Handle errors
  if (loanError) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-destructive mb-2">Error Loading Loan</h2>
        <p className="text-muted-foreground mb-4">Failed to load loan details. Please try again.</p>
        <Button onClick={() => setLocation('/loans')}>Return to Loans</Button>
      </div>
    );
  }

  // Show loading state while loan details are fetched
  if (isLoadingLoan) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
        <span className="text-lg">Loading loan...</span>
      </div>
    );
  }

  // Show error if loan data is missing
  if (!loan) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-destructive mb-2">Loan Not Found</h2>
        <p className="text-muted-foreground mb-4">This loan does not exist or you don't have permission to view it.</p>
        <Button onClick={() => setLocation('/loans')}>Return to Loans</Button>
      </div>
    );
  }

  return (
    <div className="container px-4 py-8 max-w-5xl mx-auto">
      <Card className="border shadow-sm mb-8">
        <CardHeader className="border-b bg-muted/40 py-4">
          <div className="flex flex-col gap-1">
            <CardTitle className="text-xl sm:text-2xl">Edit Loan</CardTitle>
            <CardDescription>
              Update loan details for {loan.customer?.full_name || `Customer ID: ${loan.customer_id}`}
            </CardDescription>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-6">
            <div className="pb-4 mb-4 border-b">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Loan Details</h3>
                {/* Mobile refresh button */}
                <div className="md:hidden">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={forceRefreshFormData} 
                    disabled={isRefreshing}
                    className="h-8 px-2"
                  >
                    {isRefreshing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Edit the loan details below
              </p>
            </div>
            
            {!isRefreshing ? (
              <SimpleStaticLoanForm
                key={`loan-edit-${loanId}`}
                customerId={loan.customer_id}
                initialData={{
                  id: loan.id,
                  company_id: loan.company_id,
                  customer_id: loan.customer_id,
                  amount: loan.amount,
                  interest_rate: loan.interest_rate,
                  interest_type: loan.interest_type as "flat" | "reducing" | "upfront",
                  loan_type: loan.loan_type as "personal" | "business" | "education" | "mortgage" | "vehicle" | "other",
                  term: loan.term,
                  terms_frequency: loan.terms_frequency as "daily" | "weekly" | "biweekly" | "monthly",
                  is_upfront_interest: loan.is_upfront_interest,
                  start_date: loan.start_date ? format(new Date(loan.start_date), "yyyy-MM-dd") : "",
                  end_date: loan.end_date ? format(new Date(loan.end_date), "yyyy-MM-dd") : "",
                  payment_frequency: loan.payment_frequency as "daily" | "weekly" | "biweekly" | "monthly",
                  notes: loan.notes || "",
                }}
                onSuccess={() => {
                  // Refresh loans data
                  queryClient.invalidateQueries({ queryKey: [`/api/loans/${loanId}`] });
                  queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
                  // Navigate back to loan detail
                  setLocation(`/loans/${loanId}`);
                }}
                onCancel={handleBackToLoan}
              />
            ) : (
              <div className="flex justify-center items-center p-6">
                <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
                <span>Loading form fields...</span>
              </div>
            )}
          </div>
        </CardContent>
        
        {/* No additional footer buttons needed as the form has its own */}
      </Card>

      {/* No mobile action bar needed as the form has its own buttons */}
    </div>
  );
}