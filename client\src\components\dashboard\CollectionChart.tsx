import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { 
  ResponsiveContainer, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { formatCurrency } from "@/lib/utils";

// Sample data structure
interface CollectionData {
  date: string;
  amount: number;
}

interface CollectionChartProps {
  data: CollectionData[];
  isLoading: boolean;
  period?: string;
  onPeriodChange?: (period: string) => void;
}

export default function CollectionChart({ data, isLoading, period = "30", onPeriodChange }: CollectionChartProps) {

  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded shadow-sm">
          <p className="text-sm font-medium text-gray-600">{label}</p>
          <p className="text-sm font-semibold text-primary">
            {formatCurrency(payload[0].value, 'INR', 'en-IN')}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="mt-8">
      <CardHeader className="px-6 border-b border-border">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle>Collection Trends</CardTitle>
            <CardDescription>Daily collection amounts for the last {period} days</CardDescription>
          </div>          <div className="mt-4 sm:mt-0">
            <Select 
              value={period} 
              onValueChange={(value) => {
                if (onPeriodChange) {
                  onPeriodChange(value);
                }
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 Days</SelectItem>
                <SelectItem value="30">Last 30 Days</SelectItem>
                <SelectItem value="90">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-0 pt-0">
        {isLoading ? (
          <div className="p-6 flex flex-col items-center justify-center h-80">
            <Skeleton className="h-full w-full" />
          </div>
        ) : (
          <div className="h-80 p-6">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={data}>
                <defs>
                  <linearGradient id="colorAmount" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="date" 
                  tickMargin={10} 
                  tick={{ fontSize: 12 }}
                  stroke="hsl(var(--muted-foreground))"
                />
                <YAxis 
                  tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}k`}
                  tickMargin={10}
                  tick={{ fontSize: 12 }}
                  stroke="hsl(var(--muted-foreground))"
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="amount" 
                  name="Collection Amount"
                  stroke="hsl(var(--primary))" 
                  fillOpacity={1} 
                  fill="url(#colorAmount)" 
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
