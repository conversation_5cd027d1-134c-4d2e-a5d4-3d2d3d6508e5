import React, { useState } from 'react';
import { Draggable, Droppable } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  MoreVertical,
  Edit,
  Trash2,
  GripVertical,
  Clock,
  Users,
  AlertTriangle,
  ChevronDown,
  ChevronRight,
  Settings,
  Copy,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { StepTypeDisplay, StepTypeSelector, getStepTypeRequirements, type StepType } from './StepTypeSelector';
import { ApproverSelector } from './ApproverSelector';
import { EscalationRuleEditor, type EscalationRule } from './EscalationRuleEditor';
import type { WorkflowStep as WorkflowStepType } from '@/hooks/useApprovalWorkflows';

interface WorkflowStepProps {
  step: WorkflowStepType & { id: string };
  index: number;
  isExpanded: boolean;
  onToggleExpand: (stepId: string) => void;
  onUpdateStep: (stepId: string, updates: Partial<WorkflowStepType>) => void;
  onDeleteStep: (stepId: string) => void;
  onDuplicateStep: (stepId: string) => void;
  companyId?: number;
  isDragDisabled?: boolean;
  hasErrors?: boolean;
  errorMessage?: string;
}

export function WorkflowStep({
  step,
  index,
  isExpanded,
  onToggleExpand,
  onUpdateStep,
  onDeleteStep,
  onDuplicateStep,
  companyId,
  isDragDisabled = false,
  hasErrors = false,
  errorMessage,
}: WorkflowStepProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [localStep, setLocalStep] = useState<WorkflowStepType>(step);

  const totalApprovers = (step.approverRoles?.length || 0) + (step.approverUsers?.length || 0);
  const requirements = getStepTypeRequirements(step.stepType, totalApprovers);

  const handleSave = () => {
    onUpdateStep(step.id, localStep);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setLocalStep(step);
    setIsEditing(false);
  };

  const updateLocalStep = (updates: Partial<WorkflowStepType>) => {
    setLocalStep(prev => ({ ...prev, ...updates }));
  };

  const handleStepTypeChange = (stepType: StepType) => {
    const newRequirements = getStepTypeRequirements(stepType, totalApprovers);
    updateLocalStep({
      stepType,
      requiredApprovers: newRequirements.defaultRequired,
    });
  };

  const handleEscalationRulesChange = (rules: EscalationRule[]) => {
    // Convert escalation rules to the format expected by the backend
    // This would need to be implemented based on your specific requirements
    updateLocalStep({ conditions: { ...localStep.conditions, escalationRules: rules } });
  };

  return (
    <Draggable draggableId={step.id} index={index} isDragDisabled={isDragDisabled}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`mb-4 ${snapshot.isDragging ? 'opacity-50' : ''}`}
        >
          <Card className={`
            transition-all duration-200 hover:shadow-md
            ${snapshot.isDragging ? 'shadow-lg rotate-1' : ''}
            ${hasErrors ? 'border-red-300 bg-red-50' : ''}
            ${isExpanded ? 'ring-2 ring-blue-200' : ''}
          `}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {/* Drag Handle */}
                  <div
                    {...provided.dragHandleProps}
                    className={`
                      p-1 rounded cursor-grab active:cursor-grabbing
                      ${isDragDisabled ? 'opacity-30 cursor-not-allowed' : 'hover:bg-gray-100'}
                    `}
                  >
                    <GripVertical className="h-4 w-4 text-gray-400" />
                  </div>

                  {/* Step Number */}
                  <Badge variant="outline" className="font-mono">
                    {index + 1}
                  </Badge>

                  {/* Step Name and Type */}
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-sm">{step.stepName}</h3>
                    <StepTypeDisplay type={step.stepType} size="sm" />
                    {step.isOptional && <Badge variant="secondary" className="text-xs">Optional</Badge>}
                    {hasErrors && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{errorMessage}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onToggleExpand(step.id)}
                    className="p-1 h-6 w-6"
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setIsEditing(true)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Step
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onDuplicateStep(step.id)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate Step
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => onDeleteStep(step.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Step
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>

            {/* Step Summary */}
            <CardContent className="pt-0">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{totalApprovers} approvers</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{step.stepTimeoutHours}h timeout</span>
                </div>
                <div className="flex items-center gap-1">
                  <Settings className="h-4 w-4" />
                  <span>{step.requiredApprovers} required</span>
                </div>
              </div>

              {/* Expanded Content */}
              {isExpanded && (
                <div className="mt-4 space-y-6">
                  <Separator />

                  {isEditing ? (
                    /* Edit Mode */
                    <div className="space-y-6">
                      {/* Basic Settings */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="stepName">Step Name</Label>
                          <Input
                            id="stepName"
                            value={localStep.stepName}
                            onChange={(e) => updateLocalStep({ stepName: e.target.value })}
                            placeholder="Enter step name..."
                          />
                        </div>
                        <div>
                          <Label htmlFor="stepType">Step Type</Label>
                          <StepTypeSelector
                            value={localStep.stepType}
                            onChange={handleStepTypeChange}
                            size="sm"
                          />
                        </div>
                      </div>

                      {/* Approval Requirements */}
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="requiredApprovers">Required Approvals</Label>
                          <Input
                            id="requiredApprovers"
                            type="number"
                            min={requirements.minRequired}
                            max={requirements.maxRequired}
                            value={localStep.requiredApprovers}
                            onChange={(e) => updateLocalStep({ requiredApprovers: parseInt(e.target.value) || 1 })}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            {requirements.description}
                          </p>
                        </div>
                        <div>
                          <Label htmlFor="timeoutHours">Timeout (hours)</Label>
                          <Input
                            id="timeoutHours"
                            type="number"
                            min="1"
                            max="168"
                            value={localStep.stepTimeoutHours}
                            onChange={(e) => updateLocalStep({ stepTimeoutHours: parseInt(e.target.value) || 24 })}
                          />
                        </div>
                        <div className="flex items-center space-x-2 pt-6">
                          <Switch
                            checked={localStep.isOptional}
                            onCheckedChange={(checked) => updateLocalStep({ isOptional: checked })}
                          />
                          <Label>Optional Step</Label>
                        </div>
                      </div>

                      {/* Approver Selection */}
                      <div>
                        <Label>Approvers</Label>
                        <ApproverSelector
                          selectedRoles={localStep.approverRoles || []}
                          selectedUsers={localStep.approverUsers || []}
                          onRolesChange={(roles) => updateLocalStep({ approverRoles: roles })}
                          onUsersChange={(users) => updateLocalStep({ approverUsers: users })}
                          companyId={companyId}
                        />
                      </div>

                      {/* Escalation Rules */}
                      <div>
                        <EscalationRuleEditor
                          rules={localStep.conditions?.escalationRules || []}
                          onChange={handleEscalationRulesChange}
                          companyId={companyId}
                        />
                      </div>

                      {/* Actions */}
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={handleCancel}>
                          Cancel
                        </Button>
                        <Button onClick={handleSave}>
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  ) : (
                    /* View Mode */
                    <div className="space-y-4">
                      {/* Approvers */}
                      <div>
                        <Label className="text-sm font-medium">Approvers</Label>
                        <div className="mt-2">
                          <ApproverSelector
                            selectedRoles={step.approverRoles || []}
                            selectedUsers={step.approverUsers || []}
                            onRolesChange={() => {}}
                            onUsersChange={() => {}}
                            companyId={companyId}
                            disabled={true}
                          />
                        </div>
                      </div>

                      {/* Requirements Summary */}
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <Label className="text-xs text-muted-foreground">Required Approvals</Label>
                          <p className="font-medium">{step.requiredApprovers} of {totalApprovers}</p>
                        </div>
                        <div>
                          <Label className="text-xs text-muted-foreground">Timeout</Label>
                          <p className="font-medium">{step.stepTimeoutHours} hours</p>
                        </div>
                        <div>
                          <Label className="text-xs text-muted-foreground">Type</Label>
                          <p className="font-medium">{step.isOptional ? 'Optional' : 'Required'}</p>
                        </div>
                      </div>

                      {/* Edit Button */}
                      <div className="flex justify-end">
                        <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                          <Edit className="h-4 w-4 mr-1" />
                          Edit Step
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Drop Zone */}
          <Droppable droppableId={`step-${step.id}`} type="STEP">
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className={`
                  min-h-[8px] transition-all duration-200
                  ${snapshot.isDraggingOver ? 'bg-blue-100 border-2 border-dashed border-blue-300 rounded-md' : ''}
                `}
              >
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </div>
      )}
    </Draggable>
  );
}
