"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface Customer {
  id: number
  full_name: string
  company_id: number
  customer_reference_code?: string
  phone?: string
  email?: string
}

interface CustomerComboboxProps {
  customers: Customer[]
  value?: number
  onValueChange?: (value: number | undefined) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  disabled?: boolean
  className?: string
  triggerClassName?: string
  contentClassName?: string
}

export function CustomerCombobox({
  customers,
  value,
  onValueChange,
  placeholder = "Select customer...",
  searchPlaceholder = "Search customers by name, Customer ID, phone, or email...",
  emptyText = "No customers found.",
  disabled = false,
  className,
  triggerClassName,
  contentClassName,
}: CustomerComboboxProps) {
  const [open, setOpen] = React.useState(false)

  const selectedCustomer = customers.find((customer) => customer.id === value)

  // Create searchable text for each customer
  const getSearchableText = (customer: Customer) => {
    const parts = [
      customer.full_name,
      customer.customer_reference_code,
      customer.phone,
      customer.email,
    ].filter(Boolean)
    return parts.join(" ").toLowerCase()
  }

  const renderCustomerLabel = (customer: Customer, isSelected = false) => (
    <div className="flex flex-col w-full">
      <span className={cn("font-medium", isSelected && "text-sm")}>{customer.full_name}</span>
      {!isSelected && (
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          {customer.customer_reference_code && (
            <span className="bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded">
              {customer.customer_reference_code}
            </span>
          )}
          {customer.phone && <span>{customer.phone}</span>}
          {customer.email && <span>{customer.email}</span>}
        </div>
      )}
    </div>
  )

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between h-auto min-h-[2.5rem] py-2",
              !selectedCustomer && "text-muted-foreground",
              triggerClassName
            )}
            disabled={disabled}
          >
            <div className="flex-1 text-left">
              {selectedCustomer ? (
                renderCustomerLabel(selectedCustomer, true)
              ) : (
                <span>{placeholder}</span>
              )}
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn("w-full p-0", contentClassName)}
          align="start"
          style={{ width: "var(--radix-popover-trigger-width)" }}
        >
          <Command>
            <CommandInput placeholder={searchPlaceholder} />
            <CommandList>
              <CommandEmpty>{emptyText}</CommandEmpty>
              <CommandGroup>
                {customers.map((customer) => (
                  <CommandItem
                    key={customer.id}
                    value={getSearchableText(customer)}
                    onSelect={() => {
                      if (onValueChange) {
                        onValueChange(customer.id === value ? undefined : customer.id)
                      }
                      setOpen(false)
                    }}
                    className="py-3"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4 shrink-0",
                        value === customer.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {renderCustomerLabel(customer)}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
