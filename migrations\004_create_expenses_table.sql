-- Migration to create expenses table and related types
-- This migration adds expense tracking functionality to the application

-- Check and create the expense_type enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'expense_type') THEN
        CREATE TYPE expense_type AS ENUM (
            'rent',
            'salary',
            'utilities',
            'office_supplies',
            'marketing',
            'transport',
            'loan_disbursement',
            'other'
        );
    END IF;
END
$$;

-- Check if the expenses table already exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'expenses') THEN
        -- Create expenses table
        CREATE TABLE expenses (
            id SERIAL PRIMARY KEY,
            company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
            branch_id INTEGER REFERENCES branches(id) ON DELETE SET NULL,
            amount NUMERIC(10, 2) NOT NULL,
            description TEXT NOT NULL,
            expense_date TIMESTAMP NOT NULL DEFAULT NOW(),
            expense_type expense_type NOT NULL,
            created_by INTEGER REFERENCES users(id),
            reference_number TEXT,
            payment_method payment_method NOT NULL,
            notes TEXT,
            created_at TIMESTAMP NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP NOT NULL DEFAULT NOW()
        );

        -- Create indexes for better performance
        CREATE INDEX idx_expenses_company_id ON expenses(company_id);
        CREATE INDEX idx_expenses_branch_id ON expenses(branch_id);
        CREATE INDEX idx_expenses_expense_date ON expenses(expense_date);
        CREATE INDEX idx_expenses_expense_type ON expenses(expense_type);
        
        -- Add comments for documentation
        COMMENT ON TABLE expenses IS 'Stores all company expenses';
        COMMENT ON COLUMN expenses.expense_type IS 'Type of expense (rent, salary, etc.)';
        COMMENT ON COLUMN expenses.payment_method IS 'Method used for payment (cash, upi, etc.)';
    END IF;
END
$$;
