/**
 * Company Configuration
 *
 * This file contains centralized configuration for company-related settings.
 * Use these constants instead of hardcoding company IDs throughout the application.
 */

/**
 * Default company ID to use when no company is selected
 * This is used as a fallback when no company context or user company ID is available
 * This should only be used when absolutely necessary, as users should select their company
 */
export const DEFAULT_COMPANY_ID = 0; // Using 0 as a sentinel value to indicate "no company selected"

/**
 * Validates if a company ID is valid
 * @param companyId The company ID to validate
 * @returns True if the company ID is valid, false otherwise
 */
export function isValidCompanyId(companyId: number | undefined | null): boolean {
  // A valid company ID must be a positive integer
  return typeof companyId === 'number' && companyId > 0;
}

/**
 * Gets a safe company ID to use
 * If the provided company ID is invalid, returns undefined to indicate no selection
 * @param companyId The company ID to check
 * @returns A safe company ID to use, or undefined if invalid
 */
export function getSafeCompanyId(companyId: number | undefined | null): number | undefined {
  return isValidCompanyId(companyId) ? companyId : undefined;
}
