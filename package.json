{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:integration": "vitest run --config vitest.integration.config.ts", "test:unit": "vitest run --config vitest.config.ts", "test:frontend": "vitest run --config vitest.frontend.config.ts", "test:frontend:watch": "vitest --config vitest.frontend.config.ts"}, "dependencies": {"20": "^3.1.9", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.5", "@tanstack/react-table": "^8.21.3", "@types/cookie-parser": "^1.4.8", "@types/jsonwebtoken": "^9.0.9", "@types/pdfkit": "^0.13.9", "@types/pg": "^8.15.2", "@types/react-helmet": "^6.1.11", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "multer": "^2.0.1", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdfkit": "^0.17.1", "pg": "^8.16.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "react-use": "^17.6.0", "recharts": "^2.15.2", "speakeasy": "^2.0.0", "stripe": "^18.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.0.11", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/bcrypt": "^5.0.2", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/multer": "^1.4.13", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "@vitest/coverage-v8": "^2.1.8", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "jsdom": "^25.0.1", "postcss": "^8.4.47", "supertest": "^7.0.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14", "vitest": "^2.1.8"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}