import { db } from '../db';
import {
  users, userRoles, customRoles, dataScopeRules, departments, branches, groups, groupUsers,
  customers, loans, payments, collections,
  type User, type DataScopeRule, type DataScopeType
} from '@shared/schema';
import { eq, and, inArray, or, sql, isNull } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';

export interface DataScopeContext {
  userId: number;
  companyId: number;
  resourceType: 'customers' | 'loans' | 'payments' | 'reports' | 'collections' | 'agents';
  accessLevel: 'read' | 'write' | 'delete' | 'admin';
  entityId?: number;
}

export interface ScopeEvaluationResult {
  hasAccess: boolean;
  accessibleIds?: number[];
  reason?: string;
  appliedRules?: DataScopeRule[];
}

export interface UserOrganizationalInfo {
  userId: number;
  companyId: number;
  branchId?: number;
  departmentId?: number;
  managerId?: number;
  groupIds: number[];
  roleIds: number[];
}

export class DataScopeService {

  /**
   * Check if user has access to a specific entity
   * @param context Data scope context
   * @returns Promise<ScopeEvaluationResult> Access evaluation result
   */
  async checkDataAccess(context: DataScopeContext): Promise<ScopeEvaluationResult> {
    try {
      // Get user's organizational information
      const userInfo = await this.getUserOrganizationalInfo(context.userId, context.companyId);

      // Get applicable data scope rules for user's roles
      const scopeRules = await this.getUserDataScopeRules(userInfo, context.resourceType, context.accessLevel);

      if (scopeRules.length === 0) {
        // No specific rules found, check if user has company-wide access
        const hasCompanyAccess = await this.hasCompanyWideAccess(userInfo);
        return {
          hasAccess: hasCompanyAccess,
          reason: hasCompanyAccess ? 'Company-wide access' : 'No applicable scope rules found'
        };
      }

      // Evaluate each scope rule
      const evaluationResults = await Promise.all(
        scopeRules.map(rule => this.evaluateScopeRule(rule, userInfo, context))
      );

      // Check if any rule grants access
      const hasAccess = evaluationResults.some(result => result.hasAccess);
      const appliedRules = scopeRules.filter((_, index) => evaluationResults[index].hasAccess);

      return {
        hasAccess,
        appliedRules,
        reason: hasAccess ? 'Access granted by scope rules' : 'Access denied by all scope rules'
      };
    } catch (error) {
      errorLogger.logError(
        `Error checking data access for user ${context.userId}`,
        error,
        'data-scope-service'
      );
      return {
        hasAccess: false,
        reason: 'Error evaluating data access'
      };
    }
  }

  /**
   * Get IDs of entities user can access based on data scope rules
   * @param context Data scope context
   * @returns Promise<number[]> Array of accessible entity IDs
   */
  async getAccessibleEntityIds(context: DataScopeContext): Promise<number[]> {
    try {
      // Get user's organizational information
      const userInfo = await this.getUserOrganizationalInfo(context.userId, context.companyId);

      // Get applicable data scope rules
      const scopeRules = await this.getUserDataScopeRules(userInfo, context.resourceType, context.accessLevel);

      if (scopeRules.length === 0) {
        // No specific rules, check company-wide access
        const hasCompanyAccess = await this.hasCompanyWideAccess(userInfo);
        if (hasCompanyAccess) {
          return await this.getAllEntityIds(context.resourceType, context.companyId);
        }
        return [];
      }

      // Collect accessible IDs from all applicable rules
      const accessibleIdSets = await Promise.all(
        scopeRules.map(rule => this.getEntityIdsForScopeRule(rule, userInfo, context))
      );

      // Merge all accessible IDs (union)
      const allAccessibleIds = new Set<number>();
      accessibleIdSets.forEach(idSet => {
        idSet.forEach(id => allAccessibleIds.add(id));
      });

      return Array.from(allAccessibleIds);
    } catch (error) {
      errorLogger.logError(
        `Error getting accessible entity IDs for user ${context.userId}`,
        error,
        'data-scope-service'
      );
      return [];
    }
  }

  /**
   * Apply data scope filters to a database query
   * @param baseQuery Base database query
   * @param context Data scope context
   * @returns Modified query with scope filters applied
   */
  async filterQueryByScope(baseQuery: any, context: DataScopeContext): Promise<any> {
    try {
      const accessibleIds = await this.getAccessibleEntityIds(context);

      if (accessibleIds.length === 0) {
        // No access - return query that returns no results
        return baseQuery.where(sql`false`);
      }

      // Apply ID filter to the query
      const entityTable = this.getEntityTable(context.resourceType);
      return baseQuery.where(inArray(entityTable.id, accessibleIds));
    } catch (error) {
      errorLogger.logError(
        `Error filtering query by scope for user ${context.userId}`,
        error,
        'data-scope-service'
      );
      // On error, deny access
      return baseQuery.where(sql`false`);
    }
  }

  /**
   * Get user's organizational information including roles and groups
   * @param userId User ID
   * @param companyId Company ID
   * @returns Promise<UserOrganizationalInfo> User organizational information
   */
  async getUserOrganizationalInfo(userId: number, companyId: number): Promise<UserOrganizationalInfo> {
    try {
      // Get user basic info
      const [user] = await db
        .select({
          id: users.id,
          company_id: users.company_id,
          branch_id: users.branch_id,
          department_id: users.department_id,
          manager_id: users.manager_id,
        })
        .from(users)
        .where(and(eq(users.id, userId), eq(users.company_id, companyId)))
        .limit(1);

      if (!user) {
        throw new Error(`User ${userId} not found in company ${companyId}`);
      }

      // Get user's roles
      const userRolesList = await db
        .select({ role_id: userRoles.role_id })
        .from(userRoles)
        .where(eq(userRoles.user_id, userId));

      // Get user's groups
      const userGroupsList = await db
        .select({ group_id: groupUsers.group_id })
        .from(groupUsers)
        .where(eq(groupUsers.user_id, userId));

      return {
        userId: user.id,
        companyId: user.company_id || companyId,
        branchId: user.branch_id || undefined,
        departmentId: user.department_id || undefined,
        managerId: user.manager_id || undefined,
        groupIds: userGroupsList.map(g => g.group_id),
        roleIds: userRolesList.map(r => r.role_id),
      };
    } catch (error) {
      errorLogger.logError(
        `Error getting user organizational info for user ${userId}`,
        error,
        'data-scope-service'
      );
      throw error;
    }
  }

  /**
   * Get data scope rules applicable to user's roles
   * @param userInfo User organizational information
   * @param resourceType Type of resource
   * @param accessLevel Required access level
   * @returns Promise<DataScopeRule[]> Applicable scope rules
   */
  async getUserDataScopeRules(
    userInfo: UserOrganizationalInfo,
    resourceType: string,
    accessLevel: string
  ): Promise<DataScopeRule[]> {
    try {
      if (userInfo.roleIds.length === 0) {
        return [];
      }

      const rules = await db
        .select()
        .from(dataScopeRules)
        .where(
          and(
            inArray(dataScopeRules.role_id, userInfo.roleIds),
            eq(dataScopeRules.resource_type, resourceType),
            eq(dataScopeRules.is_active, true)
          )
        )
        .orderBy(dataScopeRules.priority);

      // Filter rules by access level hierarchy (admin > delete > write > read)
      const accessLevelHierarchy = ['read', 'write', 'delete', 'admin'];
      const requiredLevel = accessLevelHierarchy.indexOf(accessLevel);

      return rules.filter(rule => {
        const ruleLevel = accessLevelHierarchy.indexOf(rule.access_level);
        return ruleLevel >= requiredLevel;
      });
    } catch (error) {
      errorLogger.logError(
        `Error getting user data scope rules`,
        error,
        'data-scope-service'
      );
      return [];
    }
  }

  /**
   * Evaluate a single scope rule against user context
   * @param rule Data scope rule to evaluate
   * @param userInfo User organizational information
   * @param context Data scope context
   * @returns Promise<ScopeEvaluationResult> Evaluation result
   */
  async evaluateScopeRule(
    rule: DataScopeRule,
    userInfo: UserOrganizationalInfo,
    context: DataScopeContext
  ): Promise<ScopeEvaluationResult> {
    try {
      const config = rule.scope_config as any;

      switch (rule.scope_type) {
        case 'branch':
          return this.evaluateBranchScope(config, userInfo, context);

        case 'department':
          return this.evaluateDepartmentScope(config, userInfo, context);

        case 'hierarchy':
          return this.evaluateHierarchyScope(config, userInfo, context);

        case 'group':
          return this.evaluateGroupScope(config, userInfo, context);

        case 'company':
          return this.evaluateCompanyScope(config, userInfo, context);

        case 'custom':
          return this.evaluateCustomScope(config, userInfo, context);

        default:
          return {
            hasAccess: false,
            reason: `Unknown scope type: ${rule.scope_type}`
          };
      }
    } catch (error) {
      errorLogger.logError(
        `Error evaluating scope rule ${rule.id}`,
        error,
        'data-scope-service'
      );
      return {
        hasAccess: false,
        reason: 'Error evaluating scope rule'
      };
    }
  }

  // Helper methods for scope evaluation
  private async evaluateBranchScope(config: any, userInfo: UserOrganizationalInfo, context: DataScopeContext): Promise<ScopeEvaluationResult> {
    const allowedBranchIds = config.branch_ids || [];
    if (userInfo.branchId && allowedBranchIds.includes(userInfo.branchId)) {
      return { hasAccess: true };
    }
    return { hasAccess: false, reason: 'User branch not in allowed branches' };
  }

  private async evaluateDepartmentScope(config: any, userInfo: UserOrganizationalInfo, context: DataScopeContext): Promise<ScopeEvaluationResult> {
    const allowedDepartmentIds = config.department_ids || [];
    if (userInfo.departmentId && allowedDepartmentIds.includes(userInfo.departmentId)) {
      return { hasAccess: true };
    }
    return { hasAccess: false, reason: 'User department not in allowed departments' };
  }

  private async evaluateHierarchyScope(config: any, userInfo: UserOrganizationalInfo, context: DataScopeContext): Promise<ScopeEvaluationResult> {
    const includeSubordinates = config.include_subordinates || false;
    if (!includeSubordinates) {
      return { hasAccess: true };
    }
    const subordinateIds = await this.getSubordinateUsers(userInfo.userId, config.max_depth || 1);
    return { hasAccess: subordinateIds.length > 0 };
  }

  private async evaluateGroupScope(config: any, userInfo: UserOrganizationalInfo, context: DataScopeContext): Promise<ScopeEvaluationResult> {
    const allowedGroupIds = config.group_ids || [];
    const hasGroupAccess = userInfo.groupIds.some(groupId => allowedGroupIds.includes(groupId));
    return { hasAccess: hasGroupAccess };
  }

  private async evaluateCompanyScope(config: any, userInfo: UserOrganizationalInfo, context: DataScopeContext): Promise<ScopeEvaluationResult> {
    return { hasAccess: config.all_branches && config.all_departments };
  }

  private async evaluateCustomScope(config: any, userInfo: UserOrganizationalInfo, context: DataScopeContext): Promise<ScopeEvaluationResult> {
    if (config.user_ids?.includes(userInfo.userId)) return { hasAccess: true };
    if (config.branch_ids?.includes(userInfo.branchId)) return { hasAccess: true };
    if (config.department_ids?.includes(userInfo.departmentId)) return { hasAccess: true };
    return { hasAccess: false, reason: 'Custom scope conditions not met' };
  }

  // Utility methods
  private async getSubordinateUsers(managerId: number, maxDepth: number): Promise<number[]> {
    try {
      const subordinates: number[] = [];
      const visited = new Set<number>();

      const getSubordinatesRecursive = async (currentManagerId: number, currentDepth: number) => {
        if (currentDepth >= maxDepth || visited.has(currentManagerId)) {
          return;
        }

        visited.add(currentManagerId);

        const directSubordinates = await db
          .select({ id: users.id })
          .from(users)
          .where(eq(users.manager_id, currentManagerId));

        for (const subordinate of directSubordinates) {
          subordinates.push(subordinate.id);
          await getSubordinatesRecursive(subordinate.id, currentDepth + 1);
        }
      };

      await getSubordinatesRecursive(managerId, 0);
      return subordinates;
    } catch (error) {
      errorLogger.logError(`Error getting subordinate users for manager ${managerId}`, error, 'data-scope-service');
      return [];
    }
  }

  private async getManagedDepartments(userId: number): Promise<number[]> {
    try {
      const managedDepts = await db
        .select({ id: departments.id })
        .from(departments)
        .where(eq(departments.manager_id, userId));

      return managedDepts.map(dept => dept.id);
    } catch (error) {
      errorLogger.logError(`Error getting managed departments for user ${userId}`, error, 'data-scope-service');
      return [];
    }
  }

  private async hasCompanyWideAccess(userInfo: UserOrganizationalInfo): Promise<boolean> {
    // Check if user has any company-wide roles or permissions
    // This is a simplified check - in practice, you might want more sophisticated logic
    return userInfo.roleIds.length > 0;
  }

  private async getAllEntityIds(resourceType: string, companyId: number): Promise<number[]> {
    try {
      const entityTable = this.getEntityTable(resourceType);
      const entities = await db
        .select({ id: entityTable.id })
        .from(entityTable)
        .where(eq(entityTable.company_id, companyId));

      return entities.map(entity => entity.id);
    } catch (error) {
      errorLogger.logError(`Error getting all entity IDs for ${resourceType}`, error, 'data-scope-service');
      return [];
    }
  }

  private getEntityTable(resourceType: string) {
    switch (resourceType) {
      case 'customers':
        return customers;
      case 'loans':
        return loans;
      case 'payments':
        return payments;
      case 'collections':
        return collections;
      default:
        throw new Error(`Unknown resource type: ${resourceType}`);
    }
  }

  private async getEntityIdsForScopeRule(
    rule: DataScopeRule,
    userInfo: UserOrganizationalInfo,
    context: DataScopeContext
  ): Promise<number[]> {
    try {
      const evaluation = await this.evaluateScopeRule(rule, userInfo, context);

      if (!evaluation.hasAccess) {
        return [];
      }

      // For now, return all accessible IDs based on the scope type
      // In a more sophisticated implementation, you would filter based on the specific scope configuration
      return await this.getAllEntityIds(context.resourceType, context.companyId);
    } catch (error) {
      errorLogger.logError(`Error getting entity IDs for scope rule ${rule.id}`, error, 'data-scope-service');
      return [];
    }
  }
}