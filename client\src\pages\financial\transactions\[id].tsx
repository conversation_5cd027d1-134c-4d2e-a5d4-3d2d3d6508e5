import { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useContextData } from '@/lib/useContextData';
import { useToast } from '@/hooks/use-toast';

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

// Icons
import {
  <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, Download
} from 'lucide-react';

interface Transaction {
  id: number;
  account_id: number;
  company_id: number;
  transaction_date: string;
  transaction_type: 'debit' | 'credit';
  amount: number;
  description: string;
  reference_type?: string | null;
  reference_id?: number | null;
  created_at: string;
  updated_at: string;
  // Additional fields from joins
  account?: {
    id: number;
    account_code: string;
    account_name: string;
    account_type: string;
  };
  // Collection reference code when reference_type is 'collection'
  collection_reference?: string;
  // Loan reference code when reference_type is 'loan'
  loan_reference?: string;
}

export default function TransactionDetail() {
  const [, navigate] = useLocation();
  const [, params] = useRoute('/financial/transactions/:id');
  const { companyId } = useContextData();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const { toast } = useToast();

  const transactionId = params?.id ? parseInt(params.id, 10) : null;

  // Fetch transaction details
  const { data: transaction, isLoading, isError, error } = useQuery<Transaction>({
    queryKey: ['/api/companies', companyId, 'transactions', transactionId],
    queryFn: async () => {
      if (!companyId || !transactionId) return null;

      const response = await apiRequest('GET', `/api/companies/${companyId}/transactions/${transactionId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch transaction details');
      }

      return response.json();
    },
    enabled: !!companyId && !!transactionId
  });

  // Handle transaction delete
  const handleDelete = async () => {
    if (!companyId || !transactionId) return;

    try {
      const response = await apiRequest(
        'DELETE',
        `/api/companies/${companyId}/transactions/${transactionId}`
      );

      if (!response.ok) {
        throw new Error('Failed to delete transaction');
      }

      toast({
        title: "Transaction deleted",
        description: "The transaction has been successfully deleted.",
        variant: "default",
      });

      navigate('/financial/transactions');
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete transaction",
        variant: "destructive",
      });
    } finally {
      setDeleteDialogOpen(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center min-h-[60vh]">
        <Spinner size="lg" />
      </div>
    );
  }

  // Handle error state
  if (isError || !transaction) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>Error Loading Transaction</CardTitle>
            <CardDescription>
              {error instanceof Error ? error.message : "Failed to load transaction details"}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center p-6">
            <Button onClick={() => navigate('/financial/transactions')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Transactions
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Get transaction type badge color
  const getTransactionTypeBadge = (type: string) => {
    switch (type) {
      case 'debit':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'credit':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => navigate('/financial/transactions')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Transactions
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between pb-4">
            <div>
              <CardTitle className="text-2xl font-bold">Transaction Details</CardTitle>
              <CardDescription>
                View details for transaction #{transaction.id}
              </CardDescription>
            </div>
            <div className="flex mt-4 sm:mt-0 space-x-2">
              <Button
                variant="outline"
                onClick={() => navigate(`/financial/transactions/${transaction.id}/edit`)}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>

              <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className="flex items-center">
                      <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                      Confirm Deletion
                    </DialogTitle>
                    <DialogDescription>
                      Are you sure you want to delete this transaction? This action cannot be undone.
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button variant="destructive" onClick={handleDelete}>
                      Delete
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>

          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Transaction Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-gray-500">Transaction Date</div>
                        <div>{formatDate(transaction.transaction_date)}</div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-gray-500">Type</div>
                        <div>
                          <Badge className={getTransactionTypeBadge(transaction.transaction_type)}>
                            {transaction.transaction_type.charAt(0).toUpperCase() + transaction.transaction_type.slice(1)}
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-gray-500">Amount</div>
                        <div className="font-semibold">{formatCurrency(transaction.amount)}</div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div className="text-sm font-medium text-gray-500">Description</div>
                        <div>{transaction.description}</div>
                      </div>

                      {transaction.reference_type && (
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-gray-500">Reference</div>
                          <div>
                            {transaction.reference_type === 'collection' && transaction.collection_reference
                              ? transaction.collection_reference
                              : transaction.reference_type === 'loan' && transaction.loan_reference
                              ? transaction.loan_reference
                              : `${transaction.reference_type.replace('_', ' ')} #${transaction.reference_id}`
                            }
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Account Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {transaction.account ? (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-gray-500">Account Name</div>
                          <div>{transaction.account.account_name}</div>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-gray-500">Account Code</div>
                          <div>{transaction.account.account_code}</div>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <div className="text-sm font-medium text-gray-500">Account Type</div>
                          <div className="capitalize">{transaction.account.account_type}</div>
                        </div>

                        <div className="pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/financial/accounts/${transaction.account.id}`)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Account
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-gray-500">No account information available</div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">System Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-gray-500">Transaction ID</div>
                      <div>{transaction.id}</div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-gray-500">Created At</div>
                      <div>
                        {formatDate(transaction.created_at)} at {formatTime(transaction.created_at)}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <div className="text-sm font-medium text-gray-500">Last Updated</div>
                      <div>
                        {formatDate(transaction.updated_at)} at {formatTime(transaction.updated_at)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between pt-6">
            <Button
              variant="outline"
              onClick={() => navigate('/financial/transactions')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Transactions
            </Button>

            <Button variant="outline" disabled>
              <Download className="mr-2 h-4 w-4" />
              Export PDF
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}