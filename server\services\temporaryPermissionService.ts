import { db } from '../db';
import {
  temporaryPermissions,
  permissionElevationRequests,
  emergencyAccessLogs,
  permissions,
  users,
  type TemporaryPermission,
  type InsertTemporaryPermission,
  type PermissionElevationRequest,
  type InsertPermissionElevationRequest,
  type InsertEmergencyAccessLog,
  type ElevationStatus,
  type ElevationPriority,
} from '../../shared/schema';
import { eq, and, or, gte, lte, isNull, desc, asc } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';
import { ApprovalWorkflowService, WorkflowTriggerContext } from './approvalWorkflowService';

export interface TemporaryPermissionDetails extends TemporaryPermission {
  username: string;
  email: string;
  permission_code: string;
  permission_name: string;
  granted_by_username: string;
}

export interface ElevationRequestDetails extends PermissionElevationRequest {
  username: string;
  email: string;
  requested_by_username: string;
  permission_code: string;
  permission_name: string;
  reviewed_by_username?: string;
}

export interface EmergencyAccessContext {
  userId: number;
  permissionCode: string;
  actionPerformed: string;
  emergencyReason: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resourceAccessed?: string;
  additionalContext?: any;
}

export class TemporaryPermissionService {
  private workflowService: ApprovalWorkflowService;

  constructor() {
    this.workflowService = new ApprovalWorkflowService();
  }

  // ==================== TEMPORARY PERMISSION MANAGEMENT ====================

  /**
   * Grant temporary permission to a user
   * @param userId User to grant permission to
   * @param permissionId Permission to grant
   * @param grantedBy User granting the permission
   * @param reason Reason for granting
   * @param durationHours Duration in hours
   * @param isEmergency Whether this is emergency access
   * @returns Promise<TemporaryPermission> Created temporary permission
   */
  async grantTemporaryPermission(
    userId: number,
    permissionId: number,
    grantedBy: number,
    reason: string,
    durationHours: number = 24,
    isEmergency: boolean = false
  ): Promise<TemporaryPermission> {
    try {
      // Validate users and permission exist
      await this.validateUsersAndPermission([userId, grantedBy], permissionId);

      // Check if user already has active temporary permission for this permission
      const existing = await this.getActiveTemporaryPermission(userId, permissionId);
      if (existing) {
        throw new Error('User already has active temporary permission for this permission');
      }

      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + durationHours);

      const [tempPermission] = await db
        .insert(temporaryPermissions)
        .values({
          user_id: userId,
          permission_id: permissionId,
          granted_by: grantedBy,
          reason,
          expires_at: expiresAt,
          is_emergency: isEmergency,
        })
        .returning();

      errorLogger.logInfo(
        `Granted temporary permission: user ${userId}, permission ${permissionId}, expires ${expiresAt.toISOString()}`,
        'temporary-permission-service'
      );

      return tempPermission;
    } catch (error) {
      errorLogger.logError(
        `Failed to grant temporary permission: user ${userId}, permission ${permissionId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Revoke temporary permission
   * @param tempPermissionId Temporary permission ID
   * @param revokedBy User revoking the permission
   * @param reason Reason for revocation
   * @returns Promise<TemporaryPermission | null> Updated permission or null
   */
  async revokeTemporaryPermission(
    tempPermissionId: number,
    revokedBy: number,
    reason: string
  ): Promise<TemporaryPermission | null> {
    try {
      const [revoked] = await db
        .update(temporaryPermissions)
        .set({
          revoked_at: new Date(),
          revoked_by: revokedBy,
          revoke_reason: reason,
          updated_at: new Date(),
        })
        .where(
          and(
            eq(temporaryPermissions.id, tempPermissionId),
            isNull(temporaryPermissions.revoked_at)
          )
        )
        .returning();

      if (revoked) {
        errorLogger.logInfo(
          `Revoked temporary permission: ${tempPermissionId}`,
          'temporary-permission-service'
        );
      }

      return revoked || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to revoke temporary permission: ${tempPermissionId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Get active temporary permissions for a user
   * @param userId User ID
   * @param permissionId Optional specific permission ID
   * @returns Promise<TemporaryPermissionDetails[]> Active temporary permissions
   */
  async getActiveTemporaryPermissions(
    userId: number,
    permissionId?: number
  ): Promise<TemporaryPermissionDetails[]> {
    try {
      let query = db
        .select({
          id: temporaryPermissions.id,
          user_id: temporaryPermissions.user_id,
          permission_id: temporaryPermissions.permission_id,
          granted_by: temporaryPermissions.granted_by,
          reason: temporaryPermissions.reason,
          granted_at: temporaryPermissions.granted_at,
          expires_at: temporaryPermissions.expires_at,
          revoked_at: temporaryPermissions.revoked_at,
          revoked_by: temporaryPermissions.revoked_by,
          revoke_reason: temporaryPermissions.revoke_reason,
          is_emergency: temporaryPermissions.is_emergency,
          created_at: temporaryPermissions.created_at,
          updated_at: temporaryPermissions.updated_at,
          username: users.username,
          email: users.email,
          permission_code: permissions.code,
          permission_name: permissions.name,
          granted_by_username: users.username,
        })
        .from(temporaryPermissions)
        .innerJoin(users, eq(temporaryPermissions.user_id, users.id))
        .innerJoin(permissions, eq(temporaryPermissions.permission_id, permissions.id))
        .innerJoin(users, eq(temporaryPermissions.granted_by, users.id))
        .where(
          and(
            eq(temporaryPermissions.user_id, userId),
            isNull(temporaryPermissions.revoked_at),
            gte(temporaryPermissions.expires_at, new Date())
          )
        );

      if (permissionId) {
        query = query.where(eq(temporaryPermissions.permission_id, permissionId));
      }

      return await query.orderBy(desc(temporaryPermissions.granted_at));
    } catch (error) {
      errorLogger.logError(
        `Failed to get active temporary permissions for user ${userId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Get single active temporary permission
   * @param userId User ID
   * @param permissionId Permission ID
   * @returns Promise<TemporaryPermission | null> Active permission or null
   */
  private async getActiveTemporaryPermission(
    userId: number,
    permissionId: number
  ): Promise<TemporaryPermission | null> {
    try {
      const [permission] = await db
        .select()
        .from(temporaryPermissions)
        .where(
          and(
            eq(temporaryPermissions.user_id, userId),
            eq(temporaryPermissions.permission_id, permissionId),
            isNull(temporaryPermissions.revoked_at),
            gte(temporaryPermissions.expires_at, new Date())
          )
        )
        .limit(1);

      return permission || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to get active temporary permission: user ${userId}, permission ${permissionId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Check if user has temporary permission
   * @param userId User ID
   * @param permissionCode Permission code
   * @returns Promise<boolean> True if user has active temporary permission
   */
  async hasTemporaryPermission(userId: number, permissionCode: string): Promise<boolean> {
    try {
      const [result] = await db
        .select({ id: temporaryPermissions.id })
        .from(temporaryPermissions)
        .innerJoin(permissions, eq(temporaryPermissions.permission_id, permissions.id))
        .where(
          and(
            eq(temporaryPermissions.user_id, userId),
            eq(permissions.code, permissionCode),
            isNull(temporaryPermissions.revoked_at),
            gte(temporaryPermissions.expires_at, new Date())
          )
        )
        .limit(1);

      return !!result;
    } catch (error) {
      errorLogger.logError(
        `Failed to check temporary permission: user ${userId}, permission ${permissionCode}`,
        error,
        'temporary-permission-service'
      );
      return false;
    }
  }

  /**
   * Cleanup expired temporary permissions
   * @returns Promise<number> Number of permissions cleaned up
   */
  async cleanupExpiredPermissions(): Promise<number> {
    try {
      const result = await db
        .update(temporaryPermissions)
        .set({
          revoked_at: new Date(),
          revoke_reason: 'Automatically expired',
          updated_at: new Date(),
        })
        .where(
          and(
            lte(temporaryPermissions.expires_at, new Date()),
            isNull(temporaryPermissions.revoked_at)
          )
        );

      const count = result.rowCount || 0;
      if (count > 0) {
        errorLogger.logInfo(
          `Cleaned up ${count} expired temporary permissions`,
          'temporary-permission-service'
        );
      }

      return count;
    } catch (error) {
      errorLogger.logError(
        'Failed to cleanup expired temporary permissions',
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  // ==================== PERMISSION ELEVATION REQUESTS ====================

  /**
   * Create permission elevation request with workflow integration
   * @param requestData Elevation request data
   * @param useWorkflow Whether to use approval workflow (default: true)
   * @returns Promise<PermissionElevationRequest> Created request
   */
  async createElevationRequest(
    requestData: InsertPermissionElevationRequest,
    useWorkflow: boolean = true
  ): Promise<PermissionElevationRequest> {
    try {
      // Validate users and permission exist
      await this.validateUsersAndPermission([requestData.user_id, requestData.requested_by], requestData.permission_id);

      // Check for existing pending request
      const existing = await this.getPendingElevationRequest(requestData.user_id, requestData.permission_id);
      if (existing) {
        throw new Error('User already has pending elevation request for this permission');
      }

      const [request] = await db
        .insert(permissionElevationRequests)
        .values(requestData)
        .returning();

      // Start approval workflow if enabled
      if (useWorkflow) {
        try {
          const [user] = await db
            .select({ company_id: users.company_id })
            .from(users)
            .where(eq(users.id, requestData.user_id))
            .limit(1);

          if (user?.company_id) {
            const workflowContext: WorkflowTriggerContext = {
              requestType: 'permission_elevation',
              requestId: request.id.toString(),
              requesterId: requestData.requested_by,
              companyId: user.company_id,
              requestData: {
                elevationRequestId: request.id,
                userId: requestData.user_id,
                permissionId: requestData.permission_id,
                reason: requestData.reason,
                justification: requestData.justification,
                durationHours: requestData.duration_hours,
                isEmergency: requestData.is_emergency,
              },
              priority: requestData.priority,
              metadata: {
                originalRequestType: 'permission_elevation',
              },
            };

            await this.workflowService.startWorkflow(workflowContext);

            errorLogger.logInfo(
              `Started approval workflow for elevation request ${request.id}`,
              'temporary-permission-service'
            );
          }
        } catch (workflowError) {
          errorLogger.logError(
            `Failed to start workflow for elevation request ${request.id}, falling back to simple approval`,
            workflowError,
            'temporary-permission-service'
          );
        }
      }

      errorLogger.logInfo(
        `Created elevation request: user ${requestData.user_id}, permission ${requestData.permission_id}, priority ${requestData.priority}`,
        'temporary-permission-service'
      );

      return request;
    } catch (error) {
      errorLogger.logError(
        `Failed to create elevation request`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Review elevation request (approve/deny)
   * @param requestId Request ID
   * @param reviewedBy User reviewing the request
   * @param status New status (approved/denied)
   * @param reviewNotes Review notes
   * @returns Promise<PermissionElevationRequest | null> Updated request
   */
  async reviewElevationRequest(
    requestId: number,
    reviewedBy: number,
    status: 'approved' | 'denied',
    reviewNotes?: string
  ): Promise<PermissionElevationRequest | null> {
    try {
      const updateData: any = {
        status,
        reviewed_at: new Date(),
        reviewed_by: reviewedBy,
        review_notes: reviewNotes,
        updated_at: new Date(),
      };

      // If approved, set approved_until timestamp
      if (status === 'approved') {
        const request = await this.getElevationRequest(requestId);
        if (!request) {
          throw new Error(`Elevation request ${requestId} not found`);
        }

        const approvedUntil = new Date();
        approvedUntil.setHours(approvedUntil.getHours() + request.duration_hours);
        updateData.approved_until = approvedUntil;

        // Create temporary permission if approved
        await this.grantTemporaryPermission(
          request.user_id,
          request.permission_id,
          reviewedBy,
          `Approved elevation request: ${request.reason}`,
          request.duration_hours,
          request.is_emergency
        );
      }

      const [updated] = await db
        .update(permissionElevationRequests)
        .set(updateData)
        .where(eq(permissionElevationRequests.id, requestId))
        .returning();

      if (updated) {
        errorLogger.logInfo(
          `Reviewed elevation request ${requestId}: ${status}`,
          'temporary-permission-service'
        );
      }

      return updated || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to review elevation request ${requestId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Get pending elevation requests
   * @param priority Optional priority filter
   * @returns Promise<ElevationRequestDetails[]> Pending requests
   */
  async getPendingElevationRequests(priority?: ElevationPriority): Promise<ElevationRequestDetails[]> {
    try {
      let query = db
        .select({
          id: permissionElevationRequests.id,
          user_id: permissionElevationRequests.user_id,
          requested_by: permissionElevationRequests.requested_by,
          permission_id: permissionElevationRequests.permission_id,
          reason: permissionElevationRequests.reason,
          justification: permissionElevationRequests.justification,
          priority: permissionElevationRequests.priority,
          duration_hours: permissionElevationRequests.duration_hours,
          status: permissionElevationRequests.status,
          requested_at: permissionElevationRequests.requested_at,
          reviewed_at: permissionElevationRequests.reviewed_at,
          reviewed_by: permissionElevationRequests.reviewed_by,
          review_notes: permissionElevationRequests.review_notes,
          approved_until: permissionElevationRequests.approved_until,
          is_emergency: permissionElevationRequests.is_emergency,
          emergency_contact: permissionElevationRequests.emergency_contact,
          created_at: permissionElevationRequests.created_at,
          updated_at: permissionElevationRequests.updated_at,
          username: users.username,
          email: users.email,
          requested_by_username: users.username,
          permission_code: permissions.code,
          permission_name: permissions.name,
          reviewed_by_username: users.username,
        })
        .from(permissionElevationRequests)
        .innerJoin(users, eq(permissionElevationRequests.user_id, users.id))
        .innerJoin(users, eq(permissionElevationRequests.requested_by, users.id))
        .innerJoin(permissions, eq(permissionElevationRequests.permission_id, permissions.id))
        .leftJoin(users, eq(permissionElevationRequests.reviewed_by, users.id))
        .where(eq(permissionElevationRequests.status, 'pending'));

      if (priority) {
        query = query.where(eq(permissionElevationRequests.priority, priority));
      }

      return await query.orderBy(
        // Emergency first, then by priority, then by request time
        desc(permissionElevationRequests.is_emergency),
        asc(permissionElevationRequests.priority),
        asc(permissionElevationRequests.requested_at)
      );
    } catch (error) {
      errorLogger.logError(
        'Failed to get pending elevation requests',
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Get elevation request by ID
   * @param requestId Request ID
   * @returns Promise<PermissionElevationRequest | null> Request or null
   */
  async getElevationRequest(requestId: number): Promise<PermissionElevationRequest | null> {
    try {
      const [request] = await db
        .select()
        .from(permissionElevationRequests)
        .where(eq(permissionElevationRequests.id, requestId))
        .limit(1);

      return request || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to get elevation request ${requestId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Get pending elevation request for user and permission
   * @param userId User ID
   * @param permissionId Permission ID
   * @returns Promise<PermissionElevationRequest | null> Pending request or null
   */
  private async getPendingElevationRequest(
    userId: number,
    permissionId: number
  ): Promise<PermissionElevationRequest | null> {
    try {
      const [request] = await db
        .select()
        .from(permissionElevationRequests)
        .where(
          and(
            eq(permissionElevationRequests.user_id, userId),
            eq(permissionElevationRequests.permission_id, permissionId),
            eq(permissionElevationRequests.status, 'pending')
          )
        )
        .limit(1);

      return request || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to get pending elevation request: user ${userId}, permission ${permissionId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  // ==================== EMERGENCY ACCESS ====================

  /**
   * Log emergency access usage
   * @param context Emergency access context
   * @returns Promise<void>
   */
  async logEmergencyAccess(context: EmergencyAccessContext): Promise<void> {
    try {
      await db
        .insert(emergencyAccessLogs)
        .values({
          user_id: context.userId,
          permission_code: context.permissionCode,
          action_performed: context.actionPerformed,
          emergency_reason: context.emergencyReason,
          session_id: context.sessionId,
          ip_address: context.ipAddress,
          user_agent: context.userAgent,
          resource_accessed: context.resourceAccessed,
          additional_context: context.additionalContext,
        });

      errorLogger.logInfo(
        `Emergency access logged: user ${context.userId}, permission ${context.permissionCode}, action: ${context.actionPerformed}`,
        'temporary-permission-service'
      );
    } catch (error) {
      errorLogger.logError(
        `Failed to log emergency access for user ${context.userId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Grant emergency access (bypasses approval workflow)
   * @param userId User to grant emergency access to
   * @param permissionCode Permission code for emergency access
   * @param emergencyReason Reason for emergency access
   * @param grantedBy User granting emergency access
   * @param durationHours Duration in hours (default 4 hours for emergency)
   * @returns Promise<TemporaryPermission> Created emergency permission
   */
  async grantEmergencyAccess(
    userId: number,
    permissionCode: string,
    emergencyReason: string,
    grantedBy: number,
    durationHours: number = 4
  ): Promise<TemporaryPermission> {
    try {
      // Get permission by code
      const [permission] = await db
        .select({ id: permissions.id })
        .from(permissions)
        .where(eq(permissions.code, permissionCode))
        .limit(1);

      if (!permission) {
        throw new Error(`Permission not found: ${permissionCode}`);
      }

      // Grant temporary permission with emergency flag
      const tempPermission = await this.grantTemporaryPermission(
        userId,
        permission.id,
        grantedBy,
        `Emergency access: ${emergencyReason}`,
        durationHours,
        true // is_emergency = true
      );

      errorLogger.logInfo(
        `Emergency access granted: user ${userId}, permission ${permissionCode}, duration ${durationHours}h`,
        'temporary-permission-service'
      );

      return tempPermission;
    } catch (error) {
      errorLogger.logError(
        `Failed to grant emergency access: user ${userId}, permission ${permissionCode}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Check if user has emergency access to a permission
   * @param userId User ID
   * @param permissionCode Permission code
   * @returns Promise<boolean> True if user has active emergency access
   */
  async hasEmergencyAccess(userId: number, permissionCode: string): Promise<boolean> {
    try {
      const [result] = await db
        .select({ id: temporaryPermissions.id })
        .from(temporaryPermissions)
        .innerJoin(permissions, eq(temporaryPermissions.permission_id, permissions.id))
        .where(
          and(
            eq(temporaryPermissions.user_id, userId),
            eq(permissions.code, permissionCode),
            eq(temporaryPermissions.is_emergency, true),
            isNull(temporaryPermissions.revoked_at),
            gte(temporaryPermissions.expires_at, new Date())
          )
        )
        .limit(1);

      return !!result;
    } catch (error) {
      errorLogger.logError(
        `Failed to check emergency access: user ${userId}, permission ${permissionCode}`,
        error,
        'temporary-permission-service'
      );
      return false;
    }
  }

  /**
   * Get emergency access logs for a user
   * @param userId User ID
   * @param limit Number of logs to return
   * @returns Promise<EmergencyAccessLog[]> Emergency access logs
   */
  async getEmergencyAccessLogs(userId: number, limit: number = 50): Promise<any[]> {
    try {
      return await db
        .select()
        .from(emergencyAccessLogs)
        .where(eq(emergencyAccessLogs.user_id, userId))
        .orderBy(desc(emergencyAccessLogs.access_granted_at))
        .limit(limit);
    } catch (error) {
      errorLogger.logError(
        `Failed to get emergency access logs for user ${userId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Get all emergency access logs (admin function)
   * @param limit Number of logs to return
   * @returns Promise<any[]> All emergency access logs with user details
   */
  async getAllEmergencyAccessLogs(limit: number = 100): Promise<any[]> {
    try {
      return await db
        .select({
          id: emergencyAccessLogs.id,
          user_id: emergencyAccessLogs.user_id,
          username: users.username,
          email: users.email,
          permission_code: emergencyAccessLogs.permission_code,
          action_performed: emergencyAccessLogs.action_performed,
          emergency_reason: emergencyAccessLogs.emergency_reason,
          access_granted_at: emergencyAccessLogs.access_granted_at,
          session_id: emergencyAccessLogs.session_id,
          ip_address: emergencyAccessLogs.ip_address,
          user_agent: emergencyAccessLogs.user_agent,
          resource_accessed: emergencyAccessLogs.resource_accessed,
          additional_context: emergencyAccessLogs.additional_context,
          created_at: emergencyAccessLogs.created_at,
        })
        .from(emergencyAccessLogs)
        .innerJoin(users, eq(emergencyAccessLogs.user_id, users.id))
        .orderBy(desc(emergencyAccessLogs.access_granted_at))
        .limit(limit);
    } catch (error) {
      errorLogger.logError(
        'Failed to get all emergency access logs',
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }

  /**
   * Validate that users and permission exist
   * @param userIds Array of user IDs to validate
   * @param permissionId Permission ID to validate
   * @throws Error if any user or permission doesn't exist
   */
  private async validateUsersAndPermission(userIds: number[], permissionId: number): Promise<void> {
    try {
      // Check users exist
      const foundUsers = await db
        .select({ id: users.id })
        .from(users)
        .where(or(...userIds.map(id => eq(users.id, id))));

      const foundUserIds = foundUsers.map(u => u.id);
      const missingUserIds = userIds.filter(id => !foundUserIds.includes(id));

      if (missingUserIds.length > 0) {
        throw new Error(`Users not found: ${missingUserIds.join(', ')}`);
      }

      // Check permission exists
      const [permission] = await db
        .select({ id: permissions.id })
        .from(permissions)
        .where(eq(permissions.id, permissionId))
        .limit(1);

      if (!permission) {
        throw new Error(`Permission not found: ${permissionId}`);
      }
    } catch (error) {
      errorLogger.logError(
        `Failed to validate users and permission: users ${userIds.join(', ')}, permission ${permissionId}`,
        error,
        'temporary-permission-service'
      );
      throw error;
    }
  }
}
