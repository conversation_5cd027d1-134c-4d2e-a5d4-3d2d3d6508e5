import { Express, Response } from 'express';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { storage } from '../storage';
import errorLogger from '../utils/errorLogger';

export function registerDashboardRoutes(app: Express): void {
  // Get dashboard metrics for a company
  app.get('/api/companies/:companyId/dashboard-metrics', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      // Get period parameter from query (default to 30 days)
      const period = parseInt(req.query.period as string || '30', 10);

      console.log(`Dashboard metrics request for company ${companyId} by user ${req.user?.id} with period ${period}`);
      errorLogger.logInfo(
        `Fetching dashboard metrics for company ${companyId}`,
        'dashboard-metrics-route',
        { companyId, period, userId: req.user?.id }
      );

      // Get metrics including trend data
      const metrics = await storage.getDashboardMetrics(companyId, { days: period });
      console.log(`Dashboard metrics for company ${companyId}:`, metrics);

      return res.json(metrics);
    } catch (error) {
      console.error('Get dashboard metrics error:', error);
      errorLogger.logError(
        'Failed to fetch dashboard metrics',
        'dashboard-metrics-route',
        { error, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch dashboard metrics')
      );
    }
  });

  // Get recent collections for a company
  app.get('/api/companies/:companyId/recent-collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const limit = parseInt(req.query.limit as string || '5', 10);
      const period = parseInt(req.query.period as string || '30', 10);

      console.log(`Recent collections request for company ${companyId} by user ${req.user?.id} with period ${period}`);
      errorLogger.logInfo(
        `Fetching recent collections for company ${companyId}`,
        'recent-collections-route',
        { companyId, limit, period, userId: req.user?.id }
      );

      const recentCollections = await storage.getRecentCollections(companyId, limit, period);
      console.log(`Found ${recentCollections.length} recent collections for company ${companyId}`);

      return res.json(recentCollections);
    } catch (error) {
      console.error('Get recent collections error:', error);
      errorLogger.logError(
        'Failed to fetch recent collections',
        'recent-collections-route',
        { error, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch recent collections')
      );
    }
  });

  // Get collection trends for a company
  app.get('/api/companies/:companyId/collection-trends', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const period = parseInt(req.query.period as string || '30', 10);

      console.log(`Collection trends request for company ${companyId} by user ${req.user?.id}`);
      errorLogger.logInfo(
        `Fetching collection trends for company ${companyId}`,
        'collection-trends-route',
        { companyId, period, userId: req.user?.id }
      );

      // Get all collections for the company in the specified period
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - period);

      const collections = await storage.getCollectionsByCompany(companyId);
      
      // Filter collections within the date range
      const filteredCollections = collections.filter(collection => {
        const collectionDate = new Date(collection.created_at);
        return collectionDate >= startDate && collectionDate <= endDate;
      });

      // Group collections by date and calculate daily totals
      const trendData: { date: string; amount: number }[] = [];
      
      for (let i = 0; i < period; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);
        
        const formattedDateForCompare = currentDate.toISOString().split('T')[0];
        const formattedDateForDisplay = currentDate.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        });

        // Sum collections for this date
        const dailyTotal = filteredCollections.reduce((sum, collection) => {
          const collectionDate = new Date(collection.created_at).toISOString().split('T')[0];
          if (collectionDate === formattedDateForCompare && collection.status === 'completed') {
            // Parse the amount to a number before adding
            const amount = typeof collection.amount === 'string' ? parseFloat(collection.amount) : collection.amount;
            return sum + amount;
          }
          return sum;
        }, 0);

        trendData.push({
          date: formattedDateForDisplay,
          amount: dailyTotal
        });
      }

      console.log(`Generated ${trendData.length} trend data points for company ${companyId}`);
      return res.json(trendData);
    } catch (error) {
      console.error('Get collection trends error:', error);
      errorLogger.logError(
        'Failed to fetch collection trends',
        'collection-trends-route',
        { error, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch collection trends')
      );
    }
  });

  // Get top agents for a company
  app.get('/api/companies/:companyId/top-agents', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const limit = parseInt(req.query.limit as string || '5', 10);
      const days = parseInt(req.query.days as string || '30', 10);

      console.log(`Top agents request for company ${companyId} by user ${req.user?.id}`);
      errorLogger.logInfo(
        `Fetching top agents for company ${companyId}`,
        'top-agents-route',
        { companyId, limit, days, userId: req.user?.id }
      );

      const topAgents = await storage.getTopAgents(companyId, limit, days);
      console.log(`Found ${topAgents.length} top agents for company ${companyId}`);

      return res.json(topAgents);
    } catch (error) {
      console.error('Get top agents error:', error);
      errorLogger.logError(
        'Failed to fetch top agents',
        'top-agents-route',
        { error, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch top agents')
      );
    }
  });
}
