import { useState } from "react";
import { useAuth } from "@/lib/auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Helmet } from "react-helmet";
import {
  Plus,
  FileEdit,
  Trash2,
  MoreHorizontal,
  Users,
  Search,
  BadgeCheck,
  Phone,
  Mail,
  MapPin,
  Edit,
  MoreVertical,
  Percent as PercentIcon,
  User,
} from "lucide-react";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useIsMobile } from "@/hooks/use-mobile";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>itle,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { format } from "date-fns";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

// Define country code constant
const COUNTRY_CODE = '+91';

// Agent schema
const agentSchema = z.object({
  company_id: z.number(),
  full_name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  phone: z.string().refine(
    (value) => {
      // Accept only format with country code (+91) followed by exactly 10 digits
      return /^\+91\d{10}$/.test(value);
    },
    { message: "Phone number must be exactly 10 digits with +91 country code" }
  ),
  email: z.string().email({ message: "Please enter a valid email address" }).refine(
    (value) => value.trim() !== "",
    { message: "Email is required" }
  ),
  commission_rate: z.union([
    z.number().min(0, { message: "Commission rate must be a positive number" }),
    z.string().transform(val => {
      const num = Number(val);
      return isNaN(num) ? 0 : num;
    })
  ]),
  territory: z.string().optional().or(z.literal("")),
  active: z.boolean().default(true),
  notes: z.string().optional().or(z.literal("")),
  user_id: z.number().optional(),
});

type Agent = z.infer<typeof agentSchema>;

export default function AgentsPage() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const user = getCurrentUser();
  const isMobile = useIsMobile();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentAgent, setCurrentAgent] = useState<Agent | null>(null);
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Create form
  const createForm = useForm<Agent>({
    resolver: zodResolver(agentSchema),
    defaultValues: {
      company_id: user?.company_id,
      full_name: "",
      email: "",
      phone: "",
      commission_rate: 5, // Default to 5%
      territory: "",
      active: true,
      notes: ""
    }
  });

  // Edit form
  const editForm = useForm<Agent & { id?: number }>({
    resolver: zodResolver(agentSchema.extend({ id: z.number().optional() })),
    defaultValues: {
      company_id: user?.company_id,
      full_name: "",
      email: "",
      phone: "",
      commission_rate: 5, // Default to 5%
      territory: "",
      active: true,
      notes: ""
    }
  });

  // Fetch agents
  const { data: agents = [], isLoading } = useQuery({
    queryKey: ['/api/companies', user?.company_id, 'agents'],
    queryFn: async () => {
      if (!user?.company_id) return [];
      const response = await apiRequest("GET", `/api/companies/${user.company_id}/agents`);
      return await response.json();
    },
    enabled: !!user?.company_id
  });

  // Create agent mutation
  const createAgentMutation = useMutation({
    mutationFn: async (data: Agent) => {
      const response = await apiRequest("POST", "/api/agents", data);

      // Check if the response is not ok (status code outside the 200-299 range)
      if (!response.ok) {
        // Parse the error response
        const errorData = await response.json();
        // Throw an error with the response data to be caught in onError
        throw {
          message: errorData.message || "An error occurred while creating the agent.",
          field: errorData.field,
          error: errorData.error
        };
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'agents'] });
      setIsCreateDialogOpen(false);
      createForm.reset();
      toast({
        title: "Success",
        description: "Agent created successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error creating agent:", error);
      // Use the error message from the thrown error object
      const errorMessage = error.message || "An error occurred while creating the agent.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      // If there's a specific field with an error, focus on it
      if (error.field && createForm.getFieldState(error.field)) {
        createForm.setError(error.field as any, {
          type: 'manual',
          message: error.error || errorMessage
        });
      }
    }
  });

  // Update agent mutation
  const updateAgentMutation = useMutation({
    mutationFn: async (data: Agent & { id: number }) => {
      const { id, ...agentData } = data;
      console.log("Updating agent data:", agentData);
      const response = await apiRequest("PATCH", `/api/agents/${id}`, agentData);

      // Check if the response is not ok (status code outside the 200-299 range)
      if (!response.ok) {
        // Parse the error response
        const errorData = await response.json();
        // Throw an error with the response data to be caught in onError
        throw {
          message: errorData.message || "Failed to update agent. Please try again.",
          field: errorData.field,
          error: errorData.error
        };
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'agents'] });
      setIsEditDialogOpen(false);
      setCurrentAgent(null);
      toast({
        title: "Success",
        description: "Agent updated successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error updating agent:", error);
      // Use the error message from the thrown error object
      const errorMessage = error.message || "Failed to update agent. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      // If there's a specific field with an error, focus on it
      if (error.field && editForm.getFieldState(error.field)) {
        editForm.setError(error.field as any, {
          type: 'manual',
          message: error.error || errorMessage
        });
      }
    }
  });

  // Delete agent mutation
  const deleteAgentMutation = useMutation({
    mutationFn: async (id: number) => {
      console.log("Deleting agent ID:", id, "Company ID:", user?.company_id);
      const response = await apiRequest("DELETE", `/api/agents/${id}?companyId=${user?.company_id}`);

      // Check if the response is not ok (status code outside the 200-299 range)
      if (!response.ok) {
        // Parse the error response
        const errorData = await response.json();
        // Throw an error with the response data to be caught in onError
        throw {
          message: errorData.message || "Failed to delete agent. Please try again."
        };
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'agents'] });
      setIsDeleteDialogOpen(false);
      setCurrentAgent(null);
      toast({
        title: "Success",
        description: "Agent deleted successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error deleting agent:", error);
      // Use the error message from the thrown error object
      const errorMessage = error.message || "Failed to delete agent. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Handle create agent
  const handleCreateAgent = (data: Agent) => {
    // Ensure commission_rate is a number
    const commissionRate = typeof data.commission_rate === 'string'
      ? parseFloat(data.commission_rate)
      : data.commission_rate;

    if (isNaN(commissionRate)) {
      toast({
        title: "Error",
        description: "Commission rate must be a valid number",
        variant: "destructive",
      });
      return;
    }

    createAgentMutation.mutate({
      ...data,
      commission_rate: commissionRate
    });
  };

  // Handle edit agent
  const handleEditAgent = (data: any) => {
    if (data.id) {
      // Ensure commission_rate is a number
      const commissionRate = typeof data.commission_rate === 'string'
        ? parseFloat(data.commission_rate)
        : data.commission_rate;

      if (isNaN(commissionRate)) {
        toast({
          title: "Error",
          description: "Commission rate must be a valid number",
          variant: "destructive",
        });
        return;
      }

      updateAgentMutation.mutate({
        ...data,
        commission_rate: commissionRate
      } as Agent & { id: number });
    }
  };

  // Handle delete agent
  const handleDeleteAgent = () => {
    if (currentAgent && 'id' in currentAgent) {
      deleteAgentMutation.mutate(currentAgent.id as number);
    }
  };

  // Open edit dialog
  const openEditDialog = (agent: any) => {
    setCurrentAgent(agent);
    editForm.reset({
      id: agent.id,
      company_id: agent.company_id,
      user_id: agent.user_id,
      full_name: agent.full_name,
      email: agent.email,
      phone: agent.phone,
      commission_rate: agent.commission_rate,
      territory: agent.territory || "",
      active: agent.active !== false,
      notes: agent.notes || ""
    });
    setIsEditDialogOpen(true);
  };

  // Open delete dialog
  const openDeleteDialog = (agent: any) => {
    setCurrentAgent(agent);
    setIsDeleteDialogOpen(true);
  };

  // Filter and search agents
  const filteredAgents = agents
    // Sort agents in descending order by ID (newest first)
    .sort((a: any, b: any) => b.id - a.id)
    .filter((agent: any) => {
      if (activeTab === "all") return true;
      if (activeTab === "active") return agent.active !== false;
      if (activeTab === "inactive") return agent.active === false;
      return true;
    })
    .filter((agent: any) => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        (agent.full_name && agent.full_name.toLowerCase().includes(query)) ||
        (agent.phone && agent.phone.toLowerCase().includes(query)) ||
        (agent.email && agent.email.toLowerCase().includes(query)) ||
        (agent.territory && agent.territory.toLowerCase().includes(query))
      );
    });

  // Calculate agent performance stats
  const calculatePerformance = (agentId: number) => {
    // This is a placeholder for future implementation
    // In a real app, you would calculate the agent's performance based on collections
    const performance = Math.floor(Math.random() * 100);

    if (performance >= 75) {
      return { label: "Excellent", color: "bg-green-100 text-green-800" };
    } else if (performance >= 50) {
      return { label: "Good", color: "bg-blue-100 text-blue-800" };
    } else if (performance >= 25) {
      return { label: "Average", color: "bg-yellow-100 text-yellow-800" };
    } else {
      return { label: "Needs Improvement", color: "bg-red-100 text-red-800" };
    }
  };

  return (
    <>
      <Helmet>
        <title>Agents Management | TrackFina</title>
      </Helmet>

      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Agents Management</h1>
            <p className="text-muted-foreground mt-1">
              Manage your collection agents and their territories
            </p>
          </div>
          <Button
            className="mt-4 md:mt-0"
            onClick={() => {
              createForm.reset({
                company_id: user?.company_id,
                full_name: "",
                email: "",
                phone: "",
                commission_rate: 5, // Default to 5%
                territory: "",
                active: true,
                notes: ""
              });
              setIsCreateDialogOpen(true);
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Agent
          </Button>
        </div>

        <div className="mb-6 flex flex-col md:flex-row gap-4 items-center justify-between">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
            <TabsList>
              <TabsTrigger value="all">All Agents</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="relative w-full md:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search agents..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex justify-center items-center p-8">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
              </div>
            ) : filteredAgents.length === 0 ? (
              <div className="text-center p-8">
                <Users className="mx-auto h-12 w-12 text-muted-foreground/50" />
                <h3 className="mt-4 text-lg font-semibold">No agents found</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  {searchQuery
                    ? "No agents match your search criteria"
                    : "You don't have any agents yet. Click 'Add Agent' to create one."}
                </p>
              </div>
            ) : (
              <>
                {/* Desktop View - Table */}
                <div className="hidden md:block">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Agent Name</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Territory</TableHead>
                        <TableHead>Commission</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAgents.map((agent: any) => (
                        <TableRow key={agent.id}>
                          <TableCell>
                            <div className="font-medium">{agent.full_name}</div>
                            <div className="text-xs text-muted-foreground">
                              {/* ID: {agent.id} */}
                              {agent.agent_reference_code && (
                                <span className="ml-2 text-xs font-medium text-primary">
                                  Ref: {agent.agent_reference_code}
                                </span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{agent.phone}</div>
                            <div className="text-sm text-muted-foreground">{agent.email}</div>
                          </TableCell>
                          <TableCell>
                            {agent.territory ? (
                              <div className="flex items-center">
                                <MapPin className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                                <span className="text-sm">{agent.territory}</span>
                              </div>
                            ) : (
                              <span className="text-sm text-muted-foreground">Not assigned</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center text-sm">
                              <PercentIcon className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                              {agent.commission_rate}%
                            </div>
                          </TableCell>
                          <TableCell>
                            {agent.active !== false ? (
                              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                                Inactive
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => openEditDialog(agent)}>
                                  <FileEdit className="mr-2 h-4 w-4" />
                                  Edit Agent
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onClick={() => openDeleteDialog(agent)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Agent
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Mobile View - Cards */}
                <div className="md:hidden grid grid-cols-1 gap-4 p-4">
                  {filteredAgents.map((agent: any) => (
                    <Card key={agent.id} className="overflow-hidden">
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between items-start">
                          <div className="space-y-1">
                            <CardTitle className="text-lg">{agent.full_name}</CardTitle>
                            <div className="flex flex-wrap gap-2">
                              {agent.active !== false ? (
                                <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                                  Active
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                                  Inactive
                                </Badge>
                              )}
                            </div>
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => openEditDialog(agent)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => openDeleteDialog(agent)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="grid grid-cols-1 gap-3 mt-2">
                          <div className="flex items-start">
                            <Phone className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Phone</p>
                              <p className="text-sm text-muted-foreground">{agent.phone}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <Mail className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Email</p>
                              <p className="text-sm text-muted-foreground">{agent.email}</p>
                            </div>
                          </div>

                          <div className="flex items-start">
                            <PercentIcon className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Commission Rate</p>
                              <p className="text-sm text-muted-foreground">{agent.commission_rate}%</p>
                            </div>
                          </div>

                          {agent.territory && (
                            <div className="flex items-start">
                              <MapPin className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                              <div className="space-y-1">
                                <p className="text-sm font-medium">Territory</p>
                                <p className="text-sm text-muted-foreground">{agent.territory}</p>
                              </div>
                            </div>
                          )}

                          <div className="flex items-start">
                            <User className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Agent ID</p>
                              <p className="text-sm text-muted-foreground">
                                {agent.agent_reference_code || `AGT-${agent.id}`}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Create Agent Sheet */}
      <Sheet open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
          <SheetHeader className="mb-5">
            <SheetTitle>Add New Agent</SheetTitle>
            <SheetDescription>
              Add a new collection agent to your organization. Fill in the details below.
            </SheetDescription>
          </SheetHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(handleCreateAgent)} className="space-y-4">
              <FormField
                control={createForm.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="E.g., John Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email*</FormLabel>
                      <FormControl>
                        <Input placeholder="E.g., <EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={createForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number*</FormLabel>
                      <FormControl>
                        <div className="flex">
                          <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                            {COUNTRY_CODE}
                          </span>
                          <Input
                            className="rounded-l-none"
                            placeholder="E.g., 9087654321"
                            value={field.value?.replace(new RegExp(`^\\${COUNTRY_CODE}`), '')}
                            onChange={(e) => {
                              // Remove non-digit characters
                              const digitsOnly = e.target.value.replace(/\D/g, '');

                              // Trim to 10 digits max
                              const trimmed = digitsOnly.substring(0, 10);

                              // Update form value with country code prefix
                              field.onChange(`${COUNTRY_CODE}${trimmed}`);
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="commission_rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commission Rate (%)*</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="E.g., 5" {...field} />
                      </FormControl>
                      <FormDescription>
                        Percentage commission on collections
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={createForm.control}
                  name="territory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Territory</FormLabel>
                      <FormControl>
                        <Input placeholder="E.g., North Mumbai" {...field} />
                      </FormControl>
                      <FormDescription>
                        Geographical area of operation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={createForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Active Status</FormLabel>
                      <FormDescription>
                        Is this agent currently active?
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this agent"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={createAgentMutation.isPending}>
                  {createAgentMutation.isPending && (
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                  )}
                  Create Agent
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Edit Agent Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
          <SheetHeader className="mb-5">
            <SheetTitle>Edit Agent</SheetTitle>
            <SheetDescription>
              Update the details of your collection agent.
            </SheetDescription>
          </SheetHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditAgent)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="E.g., John Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email*</FormLabel>
                      <FormControl>
                        <Input placeholder="E.g., <EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number*</FormLabel>
                      <FormControl>
                        <div className="flex">
                          <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                            {COUNTRY_CODE}
                          </span>
                          <Input
                            className="rounded-l-none"
                            placeholder="E.g., 9087654321"
                            value={field.value?.replace(new RegExp(`^\\${COUNTRY_CODE}`), '')}
                            onChange={(e) => {
                              // Remove non-digit characters
                              const digitsOnly = e.target.value.replace(/\D/g, '');

                              // Trim to 10 digits max
                              const trimmed = digitsOnly.substring(0, 10);

                              // Update form value with country code prefix
                              field.onChange(`${COUNTRY_CODE}${trimmed}`);
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="commission_rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commission Rate (%)*</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="E.g., 5" {...field} />
                      </FormControl>
                      <FormDescription>
                        Percentage commission on collections
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="territory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Territory</FormLabel>
                      <FormControl>
                        <Input placeholder="E.g., North Mumbai" {...field} />
                      </FormControl>
                      <FormDescription>
                        Geographical area of operation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Active Status</FormLabel>
                      <FormDescription>
                        Is this agent currently active?
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this agent"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={updateAgentMutation.isPending}>
                  {updateAgentMutation.isPending && (
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                  )}
                  Update Agent
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Delete Agent Dialog - Keeping as Dialog for confirmation */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Agent</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this agent? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">
              Deleting this agent will remove all their associated data including commission records, territories, and collection assignments.
            </p>
          </div>
          <div className="flex justify-end gap-4 mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAgent}
              disabled={deleteAgentMutation.isPending}
            >
              {deleteAgentMutation.isPending && (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
              )}
              Delete Agent
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}