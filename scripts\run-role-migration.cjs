// <PERSON>ript to run the company_admin to owner role migration
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function runRoleMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting company_admin to owner role migration...\n');

    // 1. Check if migration is needed
    console.log('1. Checking if migration is needed...');
    const enumCheck = await client.query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'user_role'
      )
      AND enumlabel = 'company_admin';
    `);

    if (enumCheck.rows.length === 0) {
      console.log('   ✅ Migration not needed - company_admin role not found in enum');
      
      // Check if there are any users with company_admin role (shouldn't be possible)
      const usersCheck = await client.query(`
        SELECT COUNT(*) as count FROM users WHERE role::text = 'company_admin'
      `);
      
      if (usersCheck.rows[0].count > 0) {
        console.log(`   ⚠️  Warning: Found ${usersCheck.rows[0].count} users with company_admin role, but enum doesn't contain it`);
      }
      
      return;
    }

    console.log('   📋 Migration needed - company_admin role found in enum');

    // 2. Read migration file
    const migrationPath = path.join(__dirname, '..', 'migrations', '021_rename_company_admin_to_owner.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log('   ✅ Migration file loaded');

    // 3. Show what will be affected
    console.log('\n2. Checking what will be affected...');
    
    const usersToUpdate = await client.query(`
      SELECT COUNT(*) as count FROM users WHERE role::text = 'company_admin'
    `);
    console.log(`   - Users to update: ${usersToUpdate.rows[0].count}`);

    const userCompaniesToUpdate = await client.query(`
      SELECT COUNT(*) as count FROM user_companies WHERE role::text = 'company_admin'
    `);
    console.log(`   - User-company associations to update: ${userCompaniesToUpdate.rows[0].count}`);

    // 4. Ask for confirmation (in a real scenario, you might want to add a --confirm flag)
    console.log('\n3. Ready to run migration...');
    console.log('   This will:');
    console.log('   - Create new enum with owner instead of company_admin');
    console.log('   - Update all existing company_admin users to owner');
    console.log('   - Update user_companies table');
    console.log('   - Drop old enum and rename new one');
    
    // 5. Run the migration
    console.log('\n4. Executing migration...');
    
    try {
      await client.query(migrationSQL);
      console.log('   ✅ Migration executed successfully!');
    } catch (migrationError) {
      console.error('   ❌ Migration failed:', migrationError.message);
      throw migrationError;
    }

    // 6. Verify the migration
    console.log('\n5. Verifying migration results...');
    
    const finalEnumCheck = await client.query(`
      SELECT enumlabel 
      FROM pg_enum 
      WHERE enumtypid = (
        SELECT oid 
        FROM pg_type 
        WHERE typname = 'user_role'
      )
      ORDER BY enumsortorder;
    `);
    
    console.log('   Current enum values:', finalEnumCheck.rows.map(r => r.enumlabel).join(', '));
    
    const remainingCompanyAdmins = await client.query(`
      SELECT COUNT(*) as count FROM users WHERE role::text = 'company_admin'
    `);
    
    const newOwners = await client.query(`
      SELECT COUNT(*) as count FROM users WHERE role::text = 'owner'
    `);
    
    console.log(`   - Remaining company_admin users: ${remainingCompanyAdmins.rows[0].count}`);
    console.log(`   - Total owner users: ${newOwners.rows[0].count}`);
    
    if (remainingCompanyAdmins.rows[0].count === 0) {
      console.log('   ✅ Migration verification successful!');
    } else {
      console.log('   ⚠️  Warning: Some company_admin users still exist');
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('💡 You can now restart your application to use the updated role system.');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    console.log('\n🔄 If the migration failed, you may need to:');
    console.log('   1. Check the error message above');
    console.log('   2. Ensure no other processes are using the database');
    console.log('   3. Run the migration manually if needed');
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
runRoleMigration().catch(console.error);
