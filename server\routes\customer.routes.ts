import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { requirePrefixSettings } from '../middleware/prefix-settings';
import {
  requireCustomerDataAccess,
  requireCustomerExportPermission,
  requirePermissionWithContext
} from '../middleware/enhancedPermission';
import { insertCustomerSchema, companies, companyPrefixSettings } from '../../shared/schema';
import { ZodError } from 'zod';
import { CustomerStorage } from '../storage/customer.storage';
import { db } from '../db';
import { eq } from 'drizzle-orm';

// Helper to get company name from company_id
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);

    let prefix = '';
    if (words.length === 0) {
      prefix = `C${companyId}`;
    } else if (words.length === 1) {
      // If only one word, use first and last letter of that word
      const word = words[0];
      prefix = word.length > 1
        ? (word.charAt(0) + word.charAt(word.length - 1)).toUpperCase()
        : word.toUpperCase() + companyId;
    } else {
      // If multiple words, use first letter of first word and first letter of last word
      // This handles cases like "GOVINDARAJI S" correctly
      prefix = (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }

    console.log(`Generated prefix: "${prefix}" for company name: "${fullName}"`);
    return prefix;
  } catch (error) {
    console.error(`Error fetching company name for ID ${companyId}:`, error);
    // Return a fallback value in case of error
    return `C${companyId}`;
  }
}

// Helper to get prefix from company_prefix_settings table
async function getPrefixFromSettings(companyId: number, entityType: 'loan' | 'collection' | 'customer' | 'partner' | 'agent'): Promise<{ prefix: string, startNumber: number }> {
  try {
    // Get company prefix settings
    const settings = await db.query.companyPrefixSettings.findFirst({
      where: eq(companyPrefixSettings.company_id, companyId),
    });

    if (!settings) {
      console.log(`No prefix settings found for company ${companyId}, falling back to company name`);
      // Fall back to company name-based prefix if no settings found
      const prefix = await getCompanyName(companyId);
      return { prefix, startNumber: 1 };
    }

    // Extract the appropriate prefix and start number based on entity type
    let prefix: string;
    let startNumber: number;

    switch (entityType) {
      case 'loan':
        prefix = settings.loan_prefix;
        startNumber = settings.loan_start_number;
        break;
      case 'collection':
        prefix = settings.collection_prefix;
        startNumber = settings.collection_start_number;
        break;
      case 'customer':
        prefix = settings.customer_prefix;
        startNumber = settings.customer_start_number;
        break;
      case 'partner':
        prefix = settings.partner_prefix;
        startNumber = settings.partner_start_number;
        break;
      case 'agent':
        prefix = settings.agent_prefix;
        startNumber = settings.agent_start_number;
        break;
      default:
        throw new Error(`Unknown entity type: ${entityType}`);
    }

    console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for ${entityType}`);
    return { prefix, startNumber };
  } catch (error) {
    console.error(`Error fetching prefix settings for company ${companyId}:`, error);
    // Fall back to company name-based prefix if error occurs
    const prefix = await getCompanyName(companyId);
    return { prefix, startNumber: 1 };
  }
}

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

export function registerCustomerRoutes(app: Express): void {
  // Get all customers for a company
  app.get('/api/companies/:companyId/customers', authMiddleware, requireCompanyAccess, requireCustomerDataAccess('basic'), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Check if branch_id filter is provided
      const branchId = req.query.branch_id ? parseInt(req.query.branch_id as string) : undefined;

      let customers;
      if (branchId) {
        customers = await storage.getCustomersByBranch(branchId);
        // Filter by company_id as well for security
        customers = customers.filter(customer => customer.company_id === companyId);
      } else {
        customers = await storage.getCustomersByCompany(companyId);
      }

      // Apply field-level security based on user permissions
      const { EnhancedPermissionService } = await import('../services/enhancedPermissionService');
      const permissionService = new EnhancedPermissionService();

      // Check what level of customer data the user can access
      const canViewSensitive = await permissionService.checkCustomerDataAccess(req.user!.id, 'sensitive');
      const canViewFinancial = await permissionService.checkCustomerDataAccess(req.user!.id, 'financial');

      // Filter customer data based on permissions
      const filteredCustomers = customers.map(customer => {
        let filteredCustomer = { ...customer };

        if (!canViewSensitive) {
          // Mask sensitive fields
          if (filteredCustomer.ssn) {
            filteredCustomer.ssn = filteredCustomer.ssn.replace(/\d(?=\d{4})/g, '*');
          }
          if (filteredCustomer.id_number) {
            filteredCustomer.id_number = filteredCustomer.id_number.replace(/\d(?=\d{4})/g, '*');
          }
          // Remove highly sensitive fields
          delete filteredCustomer.credit_score;
          delete filteredCustomer.bank_account_number;
        }

        if (!canViewFinancial) {
          // Remove financial information
          delete filteredCustomer.annual_income;
          delete filteredCustomer.employment_details;
          delete filteredCustomer.bank_name;
          delete filteredCustomer.bank_account_number;
          delete filteredCustomer.credit_score;
        }

        return filteredCustomer;
      });

      return res.json(filteredCustomers);
    } catch (error) {
      console.error('Error fetching customers:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get customer by ID
  app.get('/api/customers/:id', authMiddleware, requireCustomerDataAccess('basic'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const customerId = parseInt(req.params.id);
      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Check if user has access to this customer's company
      if (req.user.role !== 'saas_admin' && customer.company_id !== req.user.company_id) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc => uc.company_id === customer.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this customer' });
        }
      }

      // Apply field-level security based on user permissions
      const { EnhancedPermissionService } = await import('../services/enhancedPermissionService');
      const permissionService = new EnhancedPermissionService();

      // Check what level of customer data the user can access
      const canViewSensitive = await permissionService.checkCustomerDataAccess(req.user.id, 'sensitive');
      const canViewFinancial = await permissionService.checkCustomerDataAccess(req.user.id, 'financial');
      const canViewBasic = await permissionService.checkCustomerDataAccess(req.user.id, 'basic');

      if (!canViewBasic) {
        return res.status(403).json({ message: 'Access denied to customer data' });
      }

      // Filter customer data based on permissions
      let filteredCustomer = { ...customer };

      if (!canViewSensitive) {
        // Mask sensitive fields
        if (filteredCustomer.ssn) {
          filteredCustomer.ssn = filteredCustomer.ssn.replace(/\d(?=\d{4})/g, '*');
        }
        if (filteredCustomer.id_number) {
          filteredCustomer.id_number = filteredCustomer.id_number.replace(/\d(?=\d{4})/g, '*');
        }
        // Remove highly sensitive fields
        delete filteredCustomer.credit_score;
        delete filteredCustomer.bank_account_number;
      }

      if (!canViewFinancial) {
        // Remove financial information
        delete filteredCustomer.annual_income;
        delete filteredCustomer.employment_details;
        delete filteredCustomer.bank_name;
        delete filteredCustomer.bank_account_number;
        delete filteredCustomer.credit_score;
      }

      return res.json(filteredCustomer);
    } catch (error) {
      console.error(`Error fetching customer ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Export customers
  app.get('/api/companies/:companyId/customers/export', authMiddleware, requireCompanyAccess, requireCustomerExportPermission('basic'), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const exportType = req.query.type as string || 'basic';

      // Validate export type and check permissions
      const validExportTypes = ['basic', 'financial', 'sensitive', 'bulk'];
      if (!validExportTypes.includes(exportType)) {
        return res.status(400).json({ message: 'Invalid export type' });
      }

      // Get customers based on export type
      let customers = await storage.getCustomersByCompany(companyId);

      // Filter data based on export type and permissions
      if (exportType === 'basic') {
        customers = customers.map(customer => ({
          id: customer.id,
          customer_reference_code: customer.customer_reference_code,
          name: customer.name,
          phone: customer.phone,
          email: customer.email,
          created_at: customer.created_at
        }));
      } else if (exportType === 'financial') {
        // Include financial information
        customers = customers.map(customer => ({
          ...customer,
          // Remove sensitive fields like SSN
          ssn: undefined,
          id_number: undefined
        }));
      }
      // For 'sensitive' and 'bulk', return full customer data

      return res.json({
        customers,
        export_type: exportType,
        total_count: customers.length,
        exported_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error exporting customers:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Send communication to customer
  app.post('/api/customers/:id/communicate', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const customerId = parseInt(req.params.id);
      const { communication_type, message, subject } = req.body;

      if (!communication_type || !['email', 'sms', 'call'].includes(communication_type)) {
        return res.status(400).json({ message: 'Valid communication_type is required (email, sms, call)' });
      }

      if (!message || message.trim().length === 0) {
        return res.status(400).json({ message: 'Message is required' });
      }

      // Get the customer to check company access
      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Check if user has access to this customer's company
      if (req.user.role !== 'saas_admin' && customer.company_id !== req.user.company_id) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc => uc.company_id === customer.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this customer' });
        }
      }

      // Check communication permission
      const { EnhancedPermissionService } = await import('../services/enhancedPermissionService');
      const permissionService = new EnhancedPermissionService();

      const hasCommunicationPermission = await permissionService.checkCustomerCommunicationPermission(req.user.id, communication_type);
      if (!hasCommunicationPermission) {
        return res.status(403).json({
          message: `Access denied to ${communication_type} customer communication`,
          required_permission: `customer_contact_${communication_type}`
        });
      }

      // In a real implementation, this would integrate with email/SMS services
      // For now, we'll just log the communication attempt
      const communicationRecord = {
        customer_id: customerId,
        user_id: req.user.id,
        communication_type,
        message,
        subject: subject || null,
        sent_at: new Date(),
        status: 'sent'
      };

      console.log('Customer communication:', communicationRecord);

      return res.json({
        message: `${communication_type.toUpperCase()} sent successfully to customer`,
        communication: communicationRecord
      });
    } catch (error) {
      console.error(`Error sending communication to customer ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create customer
  app.post('/api/customers', authMiddleware, requirePrefixSettings, requirePermissionWithContext('customer_data', 'basic', { logAccess: true }), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Validate input
      const result = insertCustomerSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: formatZodError(result.error)
        });
      }

      // Check if user has access to the company
      const companyId = result.data.company_id;
      if (req.user.role !== 'saas_admin' && companyId !== req.user.company_id) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc => uc.company_id === companyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Check if a customer with the same phone number already exists for this company
      const existingCustomer = await storage.getCustomerByPhone(result.data.phone, companyId);
      if (existingCustomer) {
        return res.status(400).json({
          message: 'Phone number already in use',
          error: 'This phone number is already registered with another customer. Please use a different phone number.',
          field: 'phone'
        });
      }

      // Generate company-specific customer reference code

      // Get prefix from company_prefix_settings
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'customer');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for customer`);

      // Get the highest existing customer reference code for this company
      const customerStorage = new CustomerStorage();
      const highestSerial = await customerStorage.getHighestCustomerSerial(companyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const customerReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated customer reference code: ${customerReferenceCode} for company ${companyId}`);

      // Add the reference code to the customer data
      const customerData = {
        ...result.data,
        customer_reference_code: customerReferenceCode
      };

      console.log(`Creating customer with reference code "${customerReferenceCode}"`);
      const customer = await storage.createCustomer(customerData);
      return res.status(201).json(customer);
    } catch (error) {
      console.error('Error creating customer:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update customer
  app.put('/api/customers/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const customerId = parseInt(req.params.id);

      // Get the customer to check company access
      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Check if user has access to this customer's company
      if (req.user.role !== 'saas_admin' && customer.company_id !== req.user.company_id) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc => uc.company_id === customer.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this customer' });
        }
      }

      // Validate input
      const result = insertCustomerSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: formatZodError(result.error)
        });
      }

      // Don't allow changing company_id
      delete result.data.company_id;

      // If customer_reference_code is not provided in the update, check if the existing customer has one
      if (!result.data.customer_reference_code) {
        // Check if the existing customer has a reference code
        if (!customer.customer_reference_code || customer.customer_reference_code.trim() === '') {
          // If not, generate a company-specific reference code using prefix settings
          const { prefix, startNumber } = await getPrefixFromSettings(customer.company_id, 'customer');
          const customerStorage = new CustomerStorage();
          const highestSerial = await customerStorage.getHighestCustomerSerial(customer.company_id, prefix);
          const nextSerial = Math.max(highestSerial + 1, startNumber);
          const serialString = nextSerial.toString().padStart(3, '0');
          const customerReferenceCode = `${prefix}-${serialString}`;

          result.data.customer_reference_code = customerReferenceCode;
          console.log(`Setting customer_reference_code to "${customerReferenceCode}" for customer without a reference code`);
        } else {
          // If it already has a reference code, preserve it
          result.data.customer_reference_code = customer.customer_reference_code;
        }
      }

      // If phone number is being updated, check if it already exists for another customer
      if (result.data.phone && result.data.phone !== customer.phone) {
        const existingCustomer = await storage.getCustomerByPhone(result.data.phone, customer.company_id);
        if (existingCustomer && existingCustomer.id !== customerId) {
          return res.status(400).json({
            message: 'Phone number already in use',
            error: 'This phone number is already registered with another customer. Please use a different phone number.',
            field: 'phone'
          });
        }
      }

      const updatedCustomer = await storage.updateCustomer(customerId, customer.company_id, result.data);

      if (!updatedCustomer) {
        return res.status(404).json({ message: 'Customer not found or access denied' });
      }

      return res.json(updatedCustomer);
    } catch (error) {
      console.error(`Error updating customer ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Delete customer
  app.delete('/api/customers/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const customerId = parseInt(req.params.id);

      // Get the customer to check company access
      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Check if user has access to this customer's company
      if (req.user.role !== 'saas_admin' && customer.company_id !== req.user.company_id) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc => uc.company_id === customer.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this customer' });
        }
      }

      const result = await storage.deleteCustomer(customerId, customer.company_id);

      if (!result.success) {
        // Check if the customer has loans
        if (result.loansCount && result.loansCount > 0) {
          return res.status(400).json({
            message: result.error,
            loansCount: result.loansCount,
            hint: 'Delete all associated loans before deleting this customer'
          });
        }

        return res.status(404).json({ message: result.error || 'Customer not found or access denied' });
      }

      return res.json({ message: 'Customer deleted successfully' });
    } catch (error) {
      console.error(`Error deleting customer ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update all customer reference codes to company-specific format
  app.post('/api/customers/update-reference-codes', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Get company ID from request or user context
      const companyId = req.body.company_id || req.user.company_id;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      // Ensure user has access to this company
      if (req.user.role !== 'saas_admin' && req.user.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Get all customers for the company
      const companyCustomers = await storage.getCustomersByCompany(companyId);
      console.log(`Found ${companyCustomers.length} customers for company ${companyId}`);

      if (companyCustomers.length === 0) {
        return res.status(200).json({
          message: 'No customers found for this company',
          updated: 0
        });
      }

      // Get company prefix for customer reference codes
      const companyPrefix = await getCompanyName(companyId);
      console.log(`Generated company prefix for customer reference codes: ${companyPrefix}`);

      // Create a CustomerStorage instance to use getHighestCustomerSerial
      const customerStorage = new CustomerStorage();

      // Update each customer with a company-specific reference code
      let updatedCount = 0;
      const errors = [];
      let highestSerial = await customerStorage.getHighestCustomerSerial(companyId, companyPrefix);

      for (const customer of companyCustomers) {
        try {
          // Only update customers that don't already have a reference code or have an empty reference code
          if (!customer.customer_reference_code || customer.customer_reference_code.trim() === '') {
            // Generate the next sequential reference code
            highestSerial++;
            const serialString = highestSerial.toString().padStart(3, '0');
            const customerReferenceCode = `${companyPrefix}-${serialString}`;

            console.log(`Generating reference code ${customerReferenceCode} for customer ID ${customer.id}`);

            await storage.updateCustomer(
              customer.id,
              companyId,
              { customer_reference_code: customerReferenceCode }
            );

            updatedCount++;
          }
        } catch (error) {
          console.error(`Error updating customer ID ${customer.id}:`, error);
          errors.push({
            customerId: customer.id,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      return res.status(200).json({
        message: `Updated ${updatedCount} customers with company-specific reference codes`,
        totalCustomers: companyCustomers.length,
        updatedCustomers: updatedCount,
        companyPrefix: companyPrefix,
        errors: errors.length > 0 ? errors : undefined
      });
    } catch (error) {
      console.error('Error updating customer reference codes:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });
}
