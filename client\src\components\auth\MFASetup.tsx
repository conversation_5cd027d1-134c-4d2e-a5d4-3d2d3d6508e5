import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Shield, Copy, Download, CheckCircle, AlertCircle } from 'lucide-react';

interface MFASetupProps {
  onSetupComplete?: () => void;
  onCancel?: () => void;
}

interface MFASetupData {
  qrCodeUrl: string;
  backupCodes: string[];
}

export function MFASetup({ onSetupComplete, onCancel }: MFASetupProps) {
  const [step, setStep] = useState<'setup' | 'verify' | 'complete'>('setup');
  const [mfaData, setMfaData] = useState<MFASetupData | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [backupCodesSaved, setBackupCodesSaved] = useState(false);

  const handleSetupMFA = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/mfa/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({}), // Email will be determined from session
      });

      const result = await response.json();

      if (result.success) {
        setMfaData(result.data);
        setStep('verify');
      } else {
        setError(result.message || 'Failed to setup MFA');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyMFA = async () => {
    if (!verificationCode.trim()) {
      setError('Please enter a verification code');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/mfa/verify-setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          token: verificationCode.trim()
        }),
      });

      const result = await response.json();

      if (result.success) {
        setStep('complete');
        setTimeout(() => {
          onSetupComplete?.();
        }, 2000);
      } else {
        setError(result.message || 'Invalid verification code');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const copyBackupCodes = () => {
    if (mfaData?.backupCodes) {
      navigator.clipboard.writeText(mfaData.backupCodes.join('\n'));
    }
  };

  const downloadBackupCodes = () => {
    if (mfaData?.backupCodes) {
      const content = `TrackFina MFA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\n${mfaData.backupCodes.join('\n')}\n\nKeep these codes safe! Each code can only be used once.`;
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'trackfina-backup-codes.txt';
      a.click();
      URL.revokeObjectURL(url);
      setBackupCodesSaved(true);
    }
  };

  if (step === 'setup') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
          <CardTitle>Enable Two-Factor Authentication</CardTitle>
          <CardDescription>
            Add an extra layer of security to your account with MFA
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">What you'll need:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• An authenticator app (Google Authenticator, Authy, etc.)</li>
              <li>• Your smartphone or tablet</li>
              <li>• A secure place to store backup codes</li>
            </ul>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button 
              onClick={handleSetupMFA} 
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Setting up...' : 'Start Setup'}
            </Button>
            {onCancel && (
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'verify') {
    return (
      <Card className="w-full max-w-lg mx-auto">
        <CardHeader className="text-center">
          <CardTitle>Scan QR Code</CardTitle>
          <CardDescription>
            Use your authenticator app to scan this QR code
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* QR Code */}
          <div className="flex justify-center">
            {mfaData?.qrCodeUrl && (
              <img 
                src={mfaData.qrCodeUrl} 
                alt="MFA QR Code" 
                className="border rounded-lg"
              />
            )}
          </div>

          {/* Backup Codes */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Backup Codes</h4>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyBackupCodes}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadBackupCodes}
                >
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2 p-3 bg-gray-50 rounded-lg">
              {mfaData?.backupCodes.map((code, index) => (
                <Badge key={index} variant="secondary" className="font-mono">
                  {code}
                </Badge>
              ))}
            </div>
            
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Save these backup codes in a secure location. Each code can only be used once.
              </AlertDescription>
            </Alert>
          </div>

          <Separator />

          {/* Verification */}
          <div className="space-y-3">
            <Label htmlFor="verification-code">
              Enter the 6-digit code from your authenticator app
            </Label>
            <Input
              id="verification-code"
              type="text"
              placeholder="000000"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              className="text-center text-lg font-mono"
              maxLength={6}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button 
              onClick={handleVerifyMFA} 
              disabled={loading || verificationCode.length !== 6}
              className="flex-1"
            >
              {loading ? 'Verifying...' : 'Verify & Enable MFA'}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setStep('setup')}
              disabled={loading}
            >
              Back
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'complete') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-fit">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle>MFA Enabled Successfully!</CardTitle>
          <CardDescription>
            Your account is now protected with two-factor authentication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              You'll now need to enter a code from your authenticator app each time you log in.
            </AlertDescription>
          </Alert>

          <Button 
            onClick={onSetupComplete} 
            className="w-full"
          >
            Continue
          </Button>
        </CardContent>
      </Card>
    );
  }

  return null;
}
