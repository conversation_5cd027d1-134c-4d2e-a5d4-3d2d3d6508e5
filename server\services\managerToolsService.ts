import { db } from '../db';
import {
  users,
  userRoles,
  rolePermissions,
  permissions,
  customRoles,
  departments,
  branches,
  type User,
  type Permission,
  type CustomRole
} from '@shared/schema';
import { eq, and, inArray, or, sql, desc, asc } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';

export interface TeamMember {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  department?: string;
  branch?: string;
  permissions: Permission[];
  roles: CustomRole[];
  last_login?: Date;
  active: boolean;
}

export interface TeamPermissionSummary {
  total_members: number;
  active_members: number;
  permission_distribution: Array<{
    permission_code: string;
    permission_name: string;
    user_count: number;
    percentage: number;
  }>;
  role_distribution: Array<{
    role_name: string;
    user_count: number;
    percentage: number;
  }>;
  recent_changes: Array<{
    user_name: string;
    action: string;
    permission_or_role: string;
    timestamp: Date;
  }>;
}

export interface BulkPermissionOperation {
  user_ids: number[];
  permission_codes?: string[];
  role_ids?: number[];
  action: 'assign' | 'remove';
  justification?: string;
}

export interface PermissionAnalytics {
  team_size: number;
  permission_coverage: {
    high_privilege_users: number;
    standard_users: number;
    limited_access_users: number;
  };
  permission_trends: Array<{
    date: string;
    new_permissions: number;
    removed_permissions: number;
    role_changes: number;
  }>;
  compliance_metrics: {
    users_with_excessive_permissions: number;
    users_needing_review: number;
    last_review_date?: Date;
  };
}

export class ManagerToolsService {
  /**
   * Get all team members for a manager
   * @param managerId Manager's user ID
   * @param companyId Company ID for filtering
   * @returns Promise<TeamMember[]> Array of team members with their permissions
   */
  async getTeamMembers(managerId: number, companyId?: number): Promise<TeamMember[]> {
    try {
      // Get direct reports
      let teamQuery = db
        .select({
          id: users.id,
          username: users.username,
          email: users.email,
          full_name: users.full_name,
          role: users.role,
          department_id: users.department_id,
          branch_id: users.branch_id,
          active: users.active,
          created_at: users.created_at,
          updated_at: users.updated_at,
        })
        .from(users)
        .where(eq(users.manager_id, managerId));

      if (companyId) {
        teamQuery = teamQuery.where(and(
          eq(users.manager_id, managerId),
          eq(users.company_id, companyId)
        ));
      }

      const teamMembers = await teamQuery;

      // If no team members found, return empty array (this is normal for many users)
      if (teamMembers.length === 0) {
        return [];
      }

      // Get additional info for each team member
      const enrichedMembers: TeamMember[] = [];

      for (const member of teamMembers) {
        // Get department name
        let departmentName: string | undefined;
        if (member.department_id) {
          const [dept] = await db
            .select({ name: departments.name })
            .from(departments)
            .where(eq(departments.id, member.department_id))
            .limit(1);
          departmentName = dept?.name;
        }

        // Get branch name
        let branchName: string | undefined;
        if (member.branch_id) {
          const [branch] = await db
            .select({ name: branches.name })
            .from(branches)
            .where(eq(branches.id, member.branch_id))
            .limit(1);
          branchName = branch?.name;
        }

        // Get user's roles
        const userRoleData = await db
          .select({
            role: customRoles,
          })
          .from(userRoles)
          .innerJoin(customRoles, eq(userRoles.role_id, customRoles.id))
          .where(eq(userRoles.user_id, member.id));

        const roles = userRoleData.map(r => r.role);

        // Get user's permissions through roles
        const userPermissions = await db
          .select({
            permission: permissions,
          })
          .from(userRoles)
          .innerJoin(rolePermissions, eq(userRoles.role_id, rolePermissions.role_id))
          .innerJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
          .where(eq(userRoles.user_id, member.id));

        const uniquePermissions = Array.from(
          new Map(userPermissions.map(p => [p.permission.id, p.permission])).values()
        );

        enrichedMembers.push({
          id: member.id,
          username: member.username,
          email: member.email,
          full_name: member.full_name,
          role: member.role,
          department: departmentName,
          branch: branchName,
          permissions: uniquePermissions,
          roles: roles,
          active: member.active,
        });
      }

      return enrichedMembers;
    } catch (error) {
      errorLogger.logError(
        `Failed to get team members for manager ${managerId}`,
        error,
        'manager-tools-service'
      );
      throw error;
    }
  }

  /**
   * Get team permission summary for a manager
   * @param managerId Manager's user ID
   * @param companyId Company ID for filtering
   * @returns Promise<TeamPermissionSummary> Summary of team permissions
   */
  async getTeamPermissionSummary(managerId: number, companyId?: number): Promise<TeamPermissionSummary> {
    try {
      const teamMembers = await this.getTeamMembers(managerId, companyId);
      const totalMembers = teamMembers.length;
      const activeMembers = teamMembers.filter(m => m.active).length;

      // Calculate permission distribution
      const permissionCounts = new Map<string, { name: string; count: number }>();
      const roleCounts = new Map<string, number>();

      for (const member of teamMembers) {
        // Count permissions
        for (const permission of member.permissions) {
          const current = permissionCounts.get(permission.code) || { name: permission.name, count: 0 };
          permissionCounts.set(permission.code, { name: current.name, count: current.count + 1 });
        }

        // Count roles
        for (const role of member.roles) {
          const current = roleCounts.get(role.name) || 0;
          roleCounts.set(role.name, current + 1);
        }
      }

      const permissionDistribution = Array.from(permissionCounts.entries()).map(([code, data]) => ({
        permission_code: code,
        permission_name: data.name,
        user_count: data.count,
        percentage: totalMembers > 0 ? Math.round((data.count / totalMembers) * 100) : 0,
      }));

      const roleDistribution = Array.from(roleCounts.entries()).map(([name, count]) => ({
        role_name: name,
        user_count: count,
        percentage: totalMembers > 0 ? Math.round((count / totalMembers) * 100) : 0,
      }));

      // TODO: Get recent changes from audit logs when available
      const recentChanges: TeamPermissionSummary['recent_changes'] = [];

      return {
        total_members: totalMembers,
        active_members: activeMembers,
        permission_distribution: permissionDistribution.sort((a, b) => b.user_count - a.user_count),
        role_distribution: roleDistribution.sort((a, b) => b.user_count - a.user_count),
        recent_changes: recentChanges,
      };
    } catch (error) {
      errorLogger.logError(
        `Failed to get team permission summary for manager ${managerId}`,
        error,
        'manager-tools-service'
      );
      throw error;
    }
  }

  /**
   * Perform bulk permission operations on team members
   * @param managerId Manager's user ID
   * @param operation Bulk operation details
   * @returns Promise<{ success: number; failed: number; errors: string[] }>
   */
  async performBulkPermissionOperation(
    managerId: number,
    operation: BulkPermissionOperation
  ): Promise<{ success: number; failed: number; errors: string[] }> {
    try {
      // Verify that all target users are managed by this manager
      const teamMembers = await this.getTeamMembers(managerId);
      const teamMemberIds = new Set(teamMembers.map(m => m.id));

      const invalidUserIds = operation.user_ids.filter(id => !teamMemberIds.has(id));
      if (invalidUserIds.length > 0) {
        throw new Error(`Cannot manage users not in your team: ${invalidUserIds.join(', ')}`);
      }

      let successCount = 0;
      let failedCount = 0;
      const errors: string[] = [];

      // Handle role operations
      if (operation.role_ids && operation.role_ids.length > 0) {
        for (const userId of operation.user_ids) {
          try {
            for (const roleId of operation.role_ids) {
              if (operation.action === 'assign') {
                // Check if role assignment already exists
                const [existing] = await db
                  .select()
                  .from(userRoles)
                  .where(and(
                    eq(userRoles.user_id, userId),
                    eq(userRoles.role_id, roleId)
                  ))
                  .limit(1);

                if (!existing) {
                  await db
                    .insert(userRoles)
                    .values({
                      user_id: userId,
                      role_id: roleId,
                    });
                }
              } else if (operation.action === 'remove') {
                await db
                  .delete(userRoles)
                  .where(and(
                    eq(userRoles.user_id, userId),
                    eq(userRoles.role_id, roleId)
                  ));
              }
            }
            successCount++;
          } catch (error) {
            failedCount++;
            errors.push(`Failed to ${operation.action} roles for user ${userId}: ${error}`);
          }
        }
      }

      // TODO: Handle direct permission operations when permission_codes are provided
      // This would require a user_permissions table or similar mechanism

      errorLogger.logInfo(
        `Bulk permission operation completed by manager ${managerId}: ${successCount} success, ${failedCount} failed`,
        'manager-tools-service'
      );

      return {
        success: successCount,
        failed: failedCount,
        errors: errors,
      };
    } catch (error) {
      errorLogger.logError(
        `Failed to perform bulk permission operation for manager ${managerId}`,
        error,
        'manager-tools-service'
      );
      throw error;
    }
  }

  /**
   * Get permission analytics for a manager's team
   * @param managerId Manager's user ID
   * @param companyId Company ID for filtering
   * @returns Promise<PermissionAnalytics> Analytics data
   */
  async getPermissionAnalytics(managerId: number, companyId?: number): Promise<PermissionAnalytics> {
    try {
      const teamMembers = await this.getTeamMembers(managerId, companyId);
      const teamSize = teamMembers.length;

      // Categorize users by permission level
      let highPrivilegeUsers = 0;
      let standardUsers = 0;
      let limitedAccessUsers = 0;

      for (const member of teamMembers) {
        const permissionCount = member.permissions.length;
        if (permissionCount >= 15) {
          highPrivilegeUsers++;
        } else if (permissionCount >= 5) {
          standardUsers++;
        } else {
          limitedAccessUsers++;
        }
      }

      // TODO: Get permission trends from audit logs when available
      const permissionTrends: PermissionAnalytics['permission_trends'] = [];

      // Basic compliance metrics
      const usersWithExcessivePermissions = teamMembers.filter(m => m.permissions.length > 20).length;
      const usersNeedingReview = teamMembers.filter(m => !m.active || m.permissions.length === 0).length;

      return {
        team_size: teamSize,
        permission_coverage: {
          high_privilege_users: highPrivilegeUsers,
          standard_users: standardUsers,
          limited_access_users: limitedAccessUsers,
        },
        permission_trends: permissionTrends,
        compliance_metrics: {
          users_with_excessive_permissions: usersWithExcessivePermissions,
          users_needing_review: usersNeedingReview,
        },
      };
    } catch (error) {
      errorLogger.logError(
        `Failed to get permission analytics for manager ${managerId}`,
        error,
        'manager-tools-service'
      );
      throw error;
    }
  }

  /**
   * Get available roles that a manager can assign to their team
   * @param managerId Manager's user ID
   * @param companyId Company ID for filtering
   * @returns Promise<CustomRole[]> Available roles
   */
  async getAvailableRoles(managerId: number, companyId?: number): Promise<CustomRole[]> {
    try {
      let rolesQuery = db.select().from(customRoles);

      if (companyId) {
        rolesQuery = rolesQuery.where(eq(customRoles.company_id, companyId));
      }

      const roles = await rolesQuery.orderBy(asc(customRoles.name));

      // TODO: Filter roles based on manager's authority level
      // For now, return all company roles
      return roles;
    } catch (error) {
      errorLogger.logError(
        `Failed to get available roles for manager ${managerId}`,
        error,
        'manager-tools-service'
      );
      throw error;
    }
  }

  /**
   * Check if a user is managed by a specific manager (direct or indirect)
   * @param managerId Manager's user ID
   * @param userId User ID to check
   * @returns Promise<boolean> True if user is in manager's hierarchy
   */
  async isUserInManagerHierarchy(managerId: number, userId: number): Promise<boolean> {
    try {
      // Check direct reports
      const [directReport] = await db
        .select()
        .from(users)
        .where(and(
          eq(users.id, userId),
          eq(users.manager_id, managerId)
        ))
        .limit(1);

      if (directReport) {
        return true;
      }

      // Check indirect reports (recursive)
      const directReports = await db
        .select({ id: users.id })
        .from(users)
        .where(eq(users.manager_id, managerId));

      for (const report of directReports) {
        const isIndirect = await this.isUserInManagerHierarchy(report.id, userId);
        if (isIndirect) {
          return true;
        }
      }

      return false;
    } catch (error) {
      errorLogger.logError(
        `Failed to check user hierarchy for manager ${managerId} and user ${userId}`,
        error,
        'manager-tools-service'
      );
      throw error;
    }
  }

  /**
   * Get manager's own permissions to determine what they can assign
   * @param managerId Manager's user ID
   * @returns Promise<Permission[]> Manager's permissions
   */
  async getManagerPermissions(managerId: number): Promise<Permission[]> {
    try {
      const managerPermissions = await db
        .select({
          permission: permissions,
        })
        .from(userRoles)
        .innerJoin(rolePermissions, eq(userRoles.role_id, rolePermissions.role_id))
        .innerJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
        .where(eq(userRoles.user_id, managerId));

      return Array.from(
        new Map(managerPermissions.map(p => [p.permission.id, p.permission])).values()
      );
    } catch (error) {
      errorLogger.logError(
        `Failed to get manager permissions for ${managerId}`,
        error,
        'manager-tools-service'
      );
      throw error;
    }
  }
}

export const managerToolsService = new ManagerToolsService();
