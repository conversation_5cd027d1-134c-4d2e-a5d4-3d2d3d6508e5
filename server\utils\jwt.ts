import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'trackfina-secret-key'; // In production, use environment variable
const JWT_EXPIRES_IN = '30d'; // Extended to 30 days for development ease

export function generateToken(payload: { userId: number, role: string, companyId?: number }) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

export function verifyToken(token: string): Promise<jwt.JwtPayload | string> {
  return new Promise((resolve, reject) => {
    jwt.verify(token, JWT_SECRET, (err: jwt.VerifyErrors | null, decoded: jwt.JwtPayload | string | undefined) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded as jwt.JwtPayload | string);
      }
    });
  });
}
