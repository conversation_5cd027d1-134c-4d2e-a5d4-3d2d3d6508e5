import React from 'react';
import { format } from 'date-fns';
import { useReportData } from '../hooks/useReportData';
import { BalanceSheetReport as BalanceSheetReportType, COLORS } from '../types';
import { SingleDatePicker } from './SingleDatePicker';
import { ReportDownloadButton } from './ReportDownloadButton';
import { ReportCard } from './ReportCard';
import { Separator } from '@/components/ui/separator';
import {
  PieChart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { BarChart3, IndianRupee } from 'lucide-react';

interface BalanceSheetReportProps {
  date: Date;
  onDateChange: (date: Date) => void;
}

export function BalanceSheetReport({
  date,
  onDateChange,
}: BalanceSheetReportProps) {
  const { data, isLoading, isError, refetch } = useReportData<BalanceSheetReportType>({
    reportType: 'balance-sheet',
    asOfDate: date,
  });

  // Format currency in Indian Rupees
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Prepare chart data
  const pieChartData = React.useMemo(() => {
    if (!data) return [];

    return [
      { name: 'Assets', value: data.totalAssets || 0 },
      { name: 'Liabilities', value: data.totalLiabilities || 0 },
      { name: 'Equity', value: data.totalEquity || 0 },
    ];
  }, [data]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <SingleDatePicker
          date={date}
          onDateChange={onDateChange}
          label="As of Date"
        />
        <div className="flex gap-2">
          <ReportDownloadButton
            reportType="balance-sheet"
            format="pdf"
            asOfDate={date}
            disabled={isLoading || isError}
          />
          <ReportDownloadButton
            reportType="balance-sheet"
            format="csv"
            asOfDate={date}
            disabled={isLoading || isError}
          />
        </div>
      </div>

      <ReportCard
        title="Balance Sheet"
        description={`As of ${format(date, 'LLL dd, y')}`}
        isLoading={isLoading}
        isError={isError}
        onRetry={() => refetch()}
      >
        {data && data.assets && data.liabilities && data.equity && (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <IndianRupee className="h-5 w-5 text-blue-500 mr-2" />
                  <h3 className="text-sm font-medium">Total Assets</h3>
                </div>
                <p className="text-2xl font-bold mt-2">{formatCurrency(data.totalAssets || 0)}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <IndianRupee className="h-5 w-5 text-red-500 mr-2" />
                  <h3 className="text-sm font-medium">Total Liabilities</h3>
                </div>
                <p className="text-2xl font-bold mt-2">{formatCurrency(data.totalLiabilities || 0)}</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <BarChart3 className="h-5 w-5 text-green-500 mr-2" />
                  <h3 className="text-sm font-medium">Total Equity</h3>
                </div>
                <p className="text-2xl font-bold mt-2">{formatCurrency(data.totalEquity || 0)}</p>
              </div>
            </div>

            {/* Chart */}
            <div className="h-80">
              <h3 className="text-lg font-medium mb-2">Balance Sheet Composition</h3>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {pieChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Amount']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Detailed Tables */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Assets</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.assets.map((category, index) => (
                        <React.Fragment key={index}>
                          <tr className="bg-gray-50">
                            <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{category.categoryName || 'Unknown'}</td>
                            <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900">{formatCurrency(category.totalAmount || 0)}</td>
                          </tr>
                          {category.accounts && Array.isArray(category.accounts) && category.accounts.map((account, accIndex) => (
                            <tr key={`${index}-${accIndex}`}>
                              <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{account.name || 'Unknown'}</td>
                              <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">{formatCurrency(account.balance || 0)}</td>
                            </tr>
                          ))}
                        </React.Fragment>
                      ))}
                      <tr className="bg-blue-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Total Assets</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">{formatCurrency(data.totalAssets || 0)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Liabilities</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.liabilities.map((category, index) => (
                        <React.Fragment key={index}>
                          <tr className="bg-gray-50">
                            <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{category.categoryName || 'Unknown'}</td>
                            <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900">{formatCurrency(category.totalAmount || 0)}</td>
                          </tr>
                          {category.accounts && Array.isArray(category.accounts) && category.accounts.map((account, accIndex) => (
                            <tr key={`${index}-${accIndex}`}>
                              <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{account.name || 'Unknown'}</td>
                              <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">{formatCurrency(account.balance || 0)}</td>
                            </tr>
                          ))}
                        </React.Fragment>
                      ))}
                      <tr className="bg-red-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Total Liabilities</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">{formatCurrency(data.totalLiabilities || 0)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Equity</h3>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {data.equity.map((category, index) => (
                        <React.Fragment key={index}>
                          <tr className="bg-gray-50">
                            <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{category.categoryName || 'Unknown'}</td>
                            <td className="px-6 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900">{formatCurrency(category.totalAmount || 0)}</td>
                          </tr>
                          {category.accounts && Array.isArray(category.accounts) && category.accounts.map((account, accIndex) => (
                            <tr key={`${index}-${accIndex}`}>
                              <td className="px-6 py-2 pl-10 whitespace-nowrap text-sm text-gray-500">{account.name || 'Unknown'}</td>
                              <td className="px-6 py-2 whitespace-nowrap text-sm text-right text-gray-500">{formatCurrency(account.balance || 0)}</td>
                            </tr>
                          ))}
                        </React.Fragment>
                      ))}
                      <tr className="bg-green-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Total Equity</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900">{formatCurrency(data.totalEquity || 0)}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <Separator />

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex justify-between">
                  <h3 className="text-lg font-bold">Total Liabilities & Equity</h3>
                  <p className="text-lg font-bold">{formatCurrency((data.totalLiabilities || 0) + (data.totalEquity || 0))}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </ReportCard>
    </div>
  );
}
