import { db } from '../db';
import { 
  users, userRoles, customRoles, permissions, rolePermissions, userCompanies,
  branches, departments, userAccessSummary, permissionAuditLogs, dataAccessAuditLogs,
  type User
} from '@shared/schema';
import { eq, and, or, like, ilike, inArray, gte, lte, desc, asc, count, sql } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';

export interface AdvancedSearchFilters {
  // Basic filters
  searchQuery?: string;
  roles?: string[];
  departments?: number[];
  branches?: number[];
  
  // Permission filters
  hasPermissions?: string[];
  lacksPermissions?: string[];
  permissionCount?: { min?: number; max?: number };
  
  // Activity filters
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
  createdAfter?: Date;
  createdBefore?: Date;
  
  // Risk and compliance
  riskScoreMin?: number;
  riskScoreMax?: number;
  complianceStatus?: string[];
  needsReview?: boolean;
  
  // Sorting and pagination
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface UserSearchResult {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  phone?: string;
  branch_id?: number;
  department_id?: number;
  manager_id?: number;
  created_at: Date;
  updated_at: Date;
  
  // Enriched data
  branch_name?: string;
  department_name?: string;
  manager_name?: string;
  permission_count: number;
  role_count: number;
  last_login?: Date;
  risk_score: number;
  compliance_status: string;
  total_permissions: number;
  active_permissions: number;
}

export interface SearchAnalytics {
  totalUsers: number;
  filteredUsers: number;
  averagePermissions: number;
  riskDistribution: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  roleDistribution: Array<{
    role: string;
    count: number;
  }>;
  departmentDistribution: Array<{
    department: string;
    count: number;
  }>;
}

export interface PermissionUsageReport {
  permission_code: string;
  permission_name: string;
  category: string;
  total_users: number;
  usage_count_30d: number;
  last_used: Date;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  is_sensitive: boolean;
}

export class AdvancedSearchService {
  /**
   * Perform advanced user search with complex filters
   */
  async searchUsers(
    companyId: number,
    filters: AdvancedSearchFilters
  ): Promise<{ users: UserSearchResult[]; analytics: SearchAnalytics; total: number }> {
    try {
      const { page = 1, limit = 50, sortBy = 'full_name', sortOrder = 'asc' } = filters;
      const offset = (page - 1) * limit;

      // Build the base query with joins
      let query = db
        .select({
          id: users.id,
          username: users.username,
          email: users.email,
          full_name: users.full_name,
          role: users.role,
          phone: users.phone,
          branch_id: users.branch_id,
          department_id: users.department_id,
          manager_id: users.manager_id,
          created_at: users.created_at,
          updated_at: users.updated_at,
          branch_name: branches.name,
          department_name: departments.name,
          manager_name: sql<string>`manager.full_name`,
          total_permissions: userAccessSummary.total_permissions,
          active_permissions: userAccessSummary.active_permissions,
          risk_score: userAccessSummary.risk_score,
          compliance_status: userAccessSummary.compliance_status,
        })
        .from(users)
        .leftJoin(branches, eq(users.branch_id, branches.id))
        .leftJoin(departments, eq(users.department_id, departments.id))
        .leftJoin(sql`users manager`, sql`${users.manager_id} = manager.id`)
        .leftJoin(userAccessSummary, and(
          eq(userAccessSummary.user_id, users.id),
          eq(userAccessSummary.company_id, companyId)
        ));

      // Build WHERE conditions
      const conditions = [eq(users.company_id, companyId)];

      // Basic search query
      if (filters.searchQuery) {
        const searchTerm = `%${filters.searchQuery}%`;
        conditions.push(
          or(
            ilike(users.full_name, searchTerm),
            ilike(users.username, searchTerm),
            ilike(users.email, searchTerm),
            ilike(users.phone, searchTerm)
          )
        );
      }

      // Role filters
      if (filters.roles && filters.roles.length > 0) {
        conditions.push(inArray(users.role, filters.roles));
      }

      // Department filters
      if (filters.departments && filters.departments.length > 0) {
        conditions.push(inArray(users.department_id, filters.departments));
      }

      // Branch filters
      if (filters.branches && filters.branches.length > 0) {
        conditions.push(inArray(users.branch_id, filters.branches));
      }

      // Date filters
      if (filters.createdAfter) {
        conditions.push(gte(users.created_at, filters.createdAfter));
      }
      if (filters.createdBefore) {
        conditions.push(lte(users.created_at, filters.createdBefore));
      }

      // Risk score filters
      if (filters.riskScoreMin !== undefined) {
        conditions.push(gte(userAccessSummary.risk_score, filters.riskScoreMin));
      }
      if (filters.riskScoreMax !== undefined) {
        conditions.push(lte(userAccessSummary.risk_score, filters.riskScoreMax));
      }

      // Compliance status filters
      if (filters.complianceStatus && filters.complianceStatus.length > 0) {
        conditions.push(inArray(userAccessSummary.compliance_status, filters.complianceStatus));
      }

      // Apply WHERE conditions
      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }

      // Apply sorting
      const sortColumn = this.getSortColumn(sortBy);
      if (sortOrder === 'desc') {
        query = query.orderBy(desc(sortColumn));
      } else {
        query = query.orderBy(asc(sortColumn));
      }

      // Get total count for pagination
      const totalQuery = db
        .select({ count: count() })
        .from(users)
        .leftJoin(userAccessSummary, and(
          eq(userAccessSummary.user_id, users.id),
          eq(userAccessSummary.company_id, companyId)
        ));

      if (conditions.length > 0) {
        totalQuery.where(and(...conditions));
      }

      const [{ count: total }] = await totalQuery;

      // Apply pagination
      const results = await query.limit(limit).offset(offset);

      // Enrich results with permission and role counts
      const enrichedResults = await this.enrichUserResults(results, companyId);

      // Generate analytics
      const analytics = await this.generateSearchAnalytics(companyId, filters);

      return {
        users: enrichedResults,
        analytics,
        total
      };
    } catch (error) {
      errorLogger.logError(
        `Failed to perform advanced user search for company ${companyId}`,
        error,
        'advanced-search-service'
      );
      throw error;
    }
  }

  /**
   * Get permission usage report
   */
  async getPermissionUsageReport(companyId: number): Promise<PermissionUsageReport[]> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const report = await db
        .select({
          permission_code: permissions.code,
          permission_name: permissions.name,
          category: permissions.category,
          total_users: count(sql`DISTINCT ${userRoles.user_id}`),
          usage_count_30d: count(permissionAuditLogs.id),
          last_used: sql<Date>`MAX(${permissionAuditLogs.timestamp})`,
        })
        .from(permissions)
        .leftJoin(rolePermissions, eq(permissions.id, rolePermissions.permission_id))
        .leftJoin(userRoles, eq(rolePermissions.role_id, userRoles.role_id))
        .leftJoin(users, and(
          eq(userRoles.user_id, users.id),
          eq(users.company_id, companyId)
        ))
        .leftJoin(permissionAuditLogs, and(
          eq(permissionAuditLogs.permission_code, permissions.code),
          eq(permissionAuditLogs.company_id, companyId),
          gte(permissionAuditLogs.timestamp, thirtyDaysAgo)
        ))
        .groupBy(permissions.id, permissions.code, permissions.name, permissions.category)
        .orderBy(desc(count(permissionAuditLogs.id)));

      // Add risk level and sensitivity flags
      return report.map(item => ({
        ...item,
        total_users: item.total_users || 0,
        usage_count_30d: item.usage_count_30d || 0,
        risk_level: this.calculatePermissionRiskLevel(item.permission_code),
        is_sensitive: this.isSensitivePermission(item.permission_code)
      }));
    } catch (error) {
      errorLogger.logError(
        `Failed to generate permission usage report for company ${companyId}`,
        error,
        'advanced-search-service'
      );
      throw error;
    }
  }

  /**
   * Get user access analytics
   */
  async getUserAccessAnalytics(companyId: number): Promise<{
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    highRiskUsers: number;
    averagePermissions: number;
    permissionDistribution: Array<{ range: string; count: number }>;
    activityTrends: Array<{ date: string; logins: number; permissionUsage: number }>;
  }> {
    try {
      // Get basic user counts
      const [userCounts] = await db
        .select({
          total: count(),
          active: count(sql`CASE WHEN ${userAccessSummary.last_login} > NOW() - INTERVAL '30 days' THEN 1 END`),
          highRisk: count(sql`CASE WHEN ${userAccessSummary.risk_score} >= 70 THEN 1 END`),
          avgPermissions: sql<number>`AVG(${userAccessSummary.total_permissions})`,
        })
        .from(users)
        .leftJoin(userAccessSummary, and(
          eq(userAccessSummary.user_id, users.id),
          eq(userAccessSummary.company_id, companyId)
        ))
        .where(eq(users.company_id, companyId));

      // Get permission distribution
      const permissionDistribution = await db
        .select({
          range: sql<string>`
            CASE 
              WHEN ${userAccessSummary.total_permissions} = 0 THEN '0'
              WHEN ${userAccessSummary.total_permissions} BETWEEN 1 AND 5 THEN '1-5'
              WHEN ${userAccessSummary.total_permissions} BETWEEN 6 AND 15 THEN '6-15'
              WHEN ${userAccessSummary.total_permissions} BETWEEN 16 AND 30 THEN '16-30'
              ELSE '30+'
            END
          `,
          count: count()
        })
        .from(userAccessSummary)
        .where(eq(userAccessSummary.company_id, companyId))
        .groupBy(sql`range`)
        .orderBy(sql`range`);

      // Get activity trends for last 7 days
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const activityTrends = await db
        .select({
          date: sql<string>`DATE(${permissionAuditLogs.timestamp})`,
          logins: count(sql`DISTINCT ${permissionAuditLogs.session_id}`),
          permissionUsage: count(permissionAuditLogs.id)
        })
        .from(permissionAuditLogs)
        .where(and(
          eq(permissionAuditLogs.company_id, companyId),
          gte(permissionAuditLogs.timestamp, sevenDaysAgo)
        ))
        .groupBy(sql`date`)
        .orderBy(sql`date`);

      return {
        totalUsers: userCounts.total,
        activeUsers: userCounts.active || 0,
        inactiveUsers: userCounts.total - (userCounts.active || 0),
        highRiskUsers: userCounts.highRisk || 0,
        averagePermissions: Math.round(userCounts.avgPermissions || 0),
        permissionDistribution,
        activityTrends
      };
    } catch (error) {
      errorLogger.logError(
        `Failed to generate user access analytics for company ${companyId}`,
        error,
        'advanced-search-service'
      );
      throw error;
    }
  }

  /**
   * Enrich user results with additional data
   */
  private async enrichUserResults(results: any[], companyId: number): Promise<UserSearchResult[]> {
    const userIds = results.map(r => r.id);
    
    // Get permission counts for each user
    const permissionCounts = await db
      .select({
        user_id: userRoles.user_id,
        permission_count: count(sql`DISTINCT ${rolePermissions.permission_id}`),
        role_count: count(sql`DISTINCT ${userRoles.role_id}`)
      })
      .from(userRoles)
      .leftJoin(rolePermissions, eq(userRoles.role_id, rolePermissions.role_id))
      .where(inArray(userRoles.user_id, userIds))
      .groupBy(userRoles.user_id);

    const permissionMap = new Map(
      permissionCounts.map(pc => [pc.user_id, { 
        permission_count: pc.permission_count || 0, 
        role_count: pc.role_count || 0 
      }])
    );

    return results.map(user => ({
      ...user,
      permission_count: permissionMap.get(user.id)?.permission_count || 0,
      role_count: permissionMap.get(user.id)?.role_count || 0,
      total_permissions: user.total_permissions || 0,
      active_permissions: user.active_permissions || 0,
      risk_score: user.risk_score || 0,
      compliance_status: user.compliance_status || 'unknown'
    }));
  }

  /**
   * Generate search analytics
   */
  private async generateSearchAnalytics(companyId: number, filters: AdvancedSearchFilters): Promise<SearchAnalytics> {
    // This would implement analytics generation based on the current filters
    // For now, return basic analytics
    const [totalUsers] = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.company_id, companyId));

    return {
      totalUsers: totalUsers.count,
      filteredUsers: totalUsers.count, // Would be calculated based on filters
      averagePermissions: 8, // Would be calculated
      riskDistribution: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
      },
      roleDistribution: [],
      departmentDistribution: []
    };
  }

  /**
   * Get sort column based on sort key
   */
  private getSortColumn(sortBy: string) {
    switch (sortBy) {
      case 'username': return users.username;
      case 'email': return users.email;
      case 'role': return users.role;
      case 'created_at': return users.created_at;
      case 'updated_at': return users.updated_at;
      case 'risk_score': return userAccessSummary.risk_score;
      case 'total_permissions': return userAccessSummary.total_permissions;
      default: return users.full_name;
    }
  }

  /**
   * Calculate permission risk level
   */
  private calculatePermissionRiskLevel(permissionCode: string): 'low' | 'medium' | 'high' | 'critical' {
    const highRiskPermissions = ['loan_approve', 'loan_disburse', 'user_delete', 'company_delete'];
    const mediumRiskPermissions = ['loan_edit', 'customer_edit', 'user_edit', 'role_assign'];
    
    if (highRiskPermissions.includes(permissionCode)) return 'critical';
    if (mediumRiskPermissions.includes(permissionCode)) return 'high';
    if (permissionCode.includes('edit') || permissionCode.includes('create')) return 'medium';
    return 'low';
  }

  /**
   * Check if permission is sensitive
   */
  private isSensitivePermission(permissionCode: string): boolean {
    const sensitivePermissions = [
      'loan_approve', 'loan_disburse', 'customer_export', 'user_export',
      'financial_report', 'audit_view', 'company_settings'
    ];
    return sensitivePermissions.includes(permissionCode);
  }
}

export const advancedSearchService = new AdvancedSearchService();
