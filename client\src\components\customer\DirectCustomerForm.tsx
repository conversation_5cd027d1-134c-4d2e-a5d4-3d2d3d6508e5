import React from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { useAuth } from "@/components/auth/auth-context";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Spinner } from "@/components/ui/spinner";

// Define schema for the customer form
const customerFormSchema = z.object({
  company_id: z.number(),
  full_name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  phone: z.string().refine(
    (value) => {
      // Accept only format with country code (+91)
      return /^\+91\d{10}$/.test(value);
    },
    { message: "Please enter a valid 10-digit mobile number with country code" }
  ),
  email: z.string().email({ message: "Please enter a valid email" }).optional().nullable(),
  address: z.string().optional().nullable(),
  credit_score: z.number().min(0).max(1000).optional().nullable(),
  kyc_verified: z.boolean().default(false),
  active: z.boolean().default(true),
  notes: z.string().optional().nullable(),
});

type CustomerFormValues = z.infer<typeof customerFormSchema>;

interface DirectCustomerFormProps {
  companyId: number;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  initialData?: Partial<CustomerFormValues>;
  isEdit?: boolean;
  customerId?: number;
}

export function DirectCustomerForm({
  companyId,
  onSuccess,
  onCancel,
  initialData,
  isEdit = false,
  customerId,
}: DirectCustomerFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const { user } = useAuth();

  // Verify company ID matches user context to avoid authorization errors
  const currentCompanyId = user?.company_id;

  // If companyId from props doesn't match user's current context, show warning
  React.useEffect(() => {
    if (currentCompanyId && companyId && currentCompanyId !== companyId) {
      console.warn(`Company ID mismatch: form using ${companyId}, user context has ${currentCompanyId}`);
      toast({
        title: "Company Context Mismatch",
        description: "Your company selection has changed. The form will use your current company.",
        variant: "destructive",
      });
    }
  }, [companyId, currentCompanyId, toast]);

  // Always use current user context for company_id to avoid authorization issues
  const effectiveCompanyId = currentCompanyId || companyId;

  // Set up form with default values
  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerFormSchema),
    defaultValues: {
      company_id: effectiveCompanyId,
      full_name: initialData?.full_name || "",
      phone: initialData?.phone || "",
      email: initialData?.email || "",
      address: initialData?.address || "",
      credit_score: initialData?.credit_score || null,
      kyc_verified: initialData?.kyc_verified || false,
      active: initialData?.active !== undefined ? initialData.active : true,
      notes: initialData?.notes || "",
    },
  });

  // Create or Update mutation
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CustomerFormValues) => {
      // Log the request before any modifications
      console.log(`Customer ${isEdit ? 'update' : 'create'} request:`, {
        original: { ...data },
        currentCompanyId
      });

      // Use whichever company ID was selected in the form
      // The backend will check user's access rights to this company

      if (isEdit && customerId) {
        // Include the company ID in the query parameters for better access control
        const response = await apiRequest(
          'PATCH',
          `/api/customers/${customerId}?companyId=${currentCompanyId || companyId}`,
          data
        );
        return await response.json();
      } else {
        const response = await apiRequest('POST', '/api/customers', data);
        return await response.json();
      }
    },
    onSuccess: (data) => {
      // Use the effective company ID for cache invalidation
      const effectiveId = currentCompanyId || companyId;

      console.log('Customer created/updated successfully:', data);

      // Invalidate the customer list query for the current company
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${effectiveId}/customers`] });

      // Also invalidate the individual customer query if this is an edit
      if (isEdit && customerId) {
        // Invalidate all queries that might contain this customer data
        // This ensures the profile page will be refreshed
        queryClient.invalidateQueries({ queryKey: [`/api/customers/${customerId}`] });
        queryClient.invalidateQueries({ queryKey: [`/api/customers/${customerId}?companyId=${effectiveId}`] });
        queryClient.invalidateQueries({ queryKey: [`/api/customers/${customerId}/loans`] });

        // Force refetch the customer data to ensure the UI is updated
        queryClient.refetchQueries({ queryKey: [`/api/customers/${customerId}?companyId=${effectiveId}`] });
      }

      toast({
        title: isEdit ? "Customer updated" : "Customer created",
        description: isEdit
          ? "The customer has been updated successfully"
          : "New customer has been created successfully",
      });

      if (onSuccess) {
        // Pass the parsed data to the success callback
        onSuccess(data);
      } else {
        // Default navigation if no success callback provided
        navigate("/customers");
      }
    },
    onError: (error: any) => {
      console.error('Error creating/updating customer:', error);

      // Check for specific error types
      let errorMessage = error.message || "Something went wrong";

      // Look for company access denied errors
      if (error.status === 403 || errorMessage.includes("Access denied") ||
          errorMessage.includes("not authorized") || errorMessage.includes("permission")) {
        errorMessage = "Access denied. Company context may be incorrect.";
        // Log details for debugging
        console.error("Company access error:", {
          formCompanyId: form.getValues().company_id,
          userCompanyId: currentCompanyId
        });
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = (values: CustomerFormValues) => {
    mutate(values);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{isEdit ? "Edit Customer" : "Create New Customer"}</CardTitle>
        <CardDescription>
          {isEdit
            ? "Update the customer information"
            : "Enter the customer details to create a new record"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Full Name */}
              <FormField
                control={form.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Full Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter customer's full name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Mobile */}
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Mobile Number</FormLabel>
                    <FormControl>
                      <div className="flex">
                        <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                          +91
                        </span>
                        <Input
                          className="rounded-l-none"
                          placeholder="Enter 10 digit mobile number"
                          maxLength={10}
                          {...field}
                          value={field.value?.replace('+91', '')}
                          onChange={(e) => {
                            // Allow only digits
                            const value = e.target.value.replace(/[^0-9]/g, '');

                            // Limit to 10 digits
                            if (value.length <= 10) {
                              // Always store with +91 prefix
                              field.onChange(value ? `+91${value}` : '');
                            }
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Email */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Email Address</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter email address"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Credit Score */}
              <FormField
                control={form.control}
                name="credit_score"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Credit Score</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter credit score (0-1000)"
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : null)}
                        value={field.value === null ? "" : field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* KYC Verified */}
              <FormField
                control={form.control}
                name="kyc_verified"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>KYC Verified</FormLabel>
                      <FormDescription>
                        Has this customer completed KYC verification?
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Active Status */}
              <FormField
                control={form.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Is this customer account active?
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Address */}
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Address</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter customer's address"
                      className="resize-none min-h-[80px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about this customer"
                      className="resize-none min-h-[80px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              {onCancel && (
                <Button variant="outline" type="button" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isPending}>
                {isPending && <Spinner className="mr-2 h-4 w-4" />}
                {isEdit ? "Update Customer" : "Create Customer"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}