-- Migration: Fix System Roles and Permissions
-- Description: Ensure all required system roles exist with proper permissions
-- Date: 2025-01-27

BEGIN;

-- Create missing system roles if they don't exist
INSERT INTO "custom_roles" ("name", "description", "company_id", "is_system", "created_at", "updated_at")
SELECT 'System Administrator', 'Full system access with all permissions', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
  SELECT 1 FROM "custom_roles" 
  WHERE "name" = 'System Administrator' AND "is_system" = true AND "company_id" IS NULL
);

INSERT INTO "custom_roles" ("name", "description", "company_id", "is_system", "created_at", "updated_at")
SELECT 'Owner', 'Company owner with full administrative access and control', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
  SELECT 1 FROM "custom_roles" 
  WHERE "name" = 'Owner' AND "is_system" = true AND "company_id" IS NULL
);

INSERT INTO "custom_roles" ("name", "description", "company_id", "is_system", "created_at", "updated_at")
SELECT 'Employee', 'Standard employee access', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
  SELECT 1 FROM "custom_roles" 
  WHERE "name" = 'Employee' AND "is_system" = true AND "company_id" IS NULL
);

INSERT INTO "custom_roles" ("name", "description", "company_id", "is_system", "created_at", "updated_at")
SELECT 'Agent', 'Limited access for field agents', NULL, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (
  SELECT 1 FROM "custom_roles" 
  WHERE "name" = 'Agent' AND "is_system" = true AND "company_id" IS NULL
);

-- Assign all permissions to System Administrator role
INSERT INTO "role_permissions" ("role_id", "permission_id", "created_at")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'System Administrator' AND is_system = true), 
  p.id,
  CURRENT_TIMESTAMP
FROM "permissions" p
WHERE NOT EXISTS (
  SELECT 1 FROM "role_permissions" rp
  WHERE rp.role_id = (SELECT id FROM "custom_roles" WHERE name = 'System Administrator' AND is_system = true)
  AND rp.permission_id = p.id
);

-- Assign all permissions to Owner role
INSERT INTO "role_permissions" ("role_id", "permission_id", "created_at")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'Owner' AND is_system = true), 
  p.id,
  CURRENT_TIMESTAMP
FROM "permissions" p
WHERE NOT EXISTS (
  SELECT 1 FROM "role_permissions" rp
  WHERE rp.role_id = (SELECT id FROM "custom_roles" WHERE name = 'Owner' AND is_system = true)
  AND rp.permission_id = p.id
);

-- Assign basic permissions to Employee role
INSERT INTO "role_permissions" ("role_id", "permission_id", "created_at")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'Employee' AND is_system = true), 
  p.id,
  CURRENT_TIMESTAMP
FROM "permissions" p
WHERE p.code IN ('customer_view', 'loan_view', 'transaction_view')
AND NOT EXISTS (
  SELECT 1 FROM "role_permissions" rp
  WHERE rp.role_id = (SELECT id FROM "custom_roles" WHERE name = 'Employee' AND is_system = true)
  AND rp.permission_id = p.id
);

-- Assign agent permissions to Agent role
INSERT INTO "role_permissions" ("role_id", "permission_id", "created_at")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'Agent' AND is_system = true), 
  p.id,
  CURRENT_TIMESTAMP
FROM "permissions" p
WHERE p.code IN ('customer_view', 'customer_create', 'collection_create')
AND NOT EXISTS (
  SELECT 1 FROM "role_permissions" rp
  WHERE rp.role_id = (SELECT id FROM "custom_roles" WHERE name = 'Agent' AND is_system = true)
  AND rp.permission_id = p.id
);

-- Fix existing system roles that may have missing permissions
-- Loan Officer permissions
INSERT INTO "role_permissions" ("role_id", "permission_id", "created_at")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'Loan Officer' AND is_system = true), 
  p.id,
  CURRENT_TIMESTAMP
FROM "permissions" p
WHERE (p.code LIKE '%loan_%' OR p.code LIKE '%customer_%' OR p.code LIKE '%collection_%')
AND NOT EXISTS (
  SELECT 1 FROM "role_permissions" rp
  WHERE rp.role_id = (SELECT id FROM "custom_roles" WHERE name = 'Loan Officer' AND is_system = true)
  AND rp.permission_id = p.id
);

-- Collection Agent permissions
INSERT INTO "role_permissions" ("role_id", "permission_id", "created_at")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'Collection Agent' AND is_system = true), 
  p.id,
  CURRENT_TIMESTAMP
FROM "permissions" p
WHERE (p.code LIKE '%collection_%' OR p.code = 'customer_view' OR p.code = 'loan_view')
AND NOT EXISTS (
  SELECT 1 FROM "role_permissions" rp
  WHERE rp.role_id = (SELECT id FROM "custom_roles" WHERE name = 'Collection Agent' AND is_system = true)
  AND rp.permission_id = p.id
);

-- Financial Analyst permissions
INSERT INTO "role_permissions" ("role_id", "permission_id", "created_at")
SELECT 
  (SELECT id FROM "custom_roles" WHERE name = 'Financial Analyst' AND is_system = true), 
  p.id,
  CURRENT_TIMESTAMP
FROM "permissions" p
WHERE (p.code LIKE '%report_%' OR p.code LIKE '%financial_%' OR p.code LIKE '%view%')
AND NOT EXISTS (
  SELECT 1 FROM "role_permissions" rp
  WHERE rp.role_id = (SELECT id FROM "custom_roles" WHERE name = 'Financial Analyst' AND is_system = true)
  AND rp.permission_id = p.id
);

-- Add comments for documentation
COMMENT ON TABLE "custom_roles" IS 'Roles table supporting both system roles (company_id = NULL) and company-specific roles';
COMMENT ON COLUMN "custom_roles"."is_system" IS 'True for system roles available to all companies, false for company-specific roles';
COMMENT ON COLUMN "custom_roles"."company_id" IS 'NULL for system roles, specific company ID for company-specific roles';

COMMIT;
