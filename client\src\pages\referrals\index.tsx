import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Mail, Plus, Share } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { formatDate } from "@/lib/utils";

// Define type for referral data
interface Referral {
  id: number;
  referrer_id: number;
  referral_code: string;
  referral_email: string;
  status: string;
  commission_amount: number | null;
  created_at: string;
  updated_at: string;
}

// Form schema for creating a referral
const referralFormSchema = z.object({
  referral_email: z.string().email({ message: "Please enter a valid email address" }),
  referral_code: z.string().optional(),
});

export default function Referrals() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const user = getCurrentUser();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch user's referrals
  const { data: referralsData, isLoading } = useQuery<Referral[]>({
    queryKey: ['/api/referrals'],
    enabled: !!user?.id,
  });

  // Form for creating a new referral
  const form = useForm<z.infer<typeof referralFormSchema>>({
    resolver: zodResolver(referralFormSchema),
    defaultValues: {
      referral_email: "",
      referral_code: "",
    },
  });

  // Mutation for creating a new referral
  const createReferralMutation = useMutation({
    mutationFn: async (values: z.infer<typeof referralFormSchema>) => {
      const response = await apiRequest("POST", "/api/referrals", values);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Referral Created",
        description: "Your referral has been successfully sent.",
      });
      setIsDialogOpen(false);
      form.reset();
      queryClient.invalidateQueries({ queryKey: ['/api/referrals'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create referral",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: z.infer<typeof referralFormSchema>) => {
    createReferralMutation.mutate(values);
  };

  // Function to get badge variant based on status
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "converted":
        return "success";
      case "pending":
        return "warning";
      case "declined":
        return "destructive";
      case "expired":
        return "secondary";
      case "paid":
        return "default";
      default:
        return "default";
    }
  };

  // Function to copy referral link
  const copyReferralLink = (code: string) => {
    const baseUrl = window.location.origin;
    const referralLink = `${baseUrl}/register?ref=${code}`;
    navigator.clipboard.writeText(referralLink);
    toast({
      title: "Link Copied",
      description: "Referral link copied to clipboard",
    });
  };

  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Referrals</h1>
          <p className="mt-1 text-sm text-gray-500">
            Refer new customers and earn commissions
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-1">
                <Plus size={16} />
                <span>Create Referral</span>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Referral</DialogTitle>
                <DialogDescription>
                  Send a referral invitation to a potential customer.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="referral_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter recipient's email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button 
                      type="submit"
                      disabled={createReferralMutation.isPending}
                    >
                      {createReferralMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Mail className="mr-2 h-4 w-4" />
                          Send Referral
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Referral Link Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Your Referral Link</CardTitle>
          <CardDescription>
            Share this link with potential customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Input 
              readOnly
              value={`${window.location.origin}/register?ref=REF${user?.id || ""}`}
              className="font-mono bg-muted flex-1"
            />
            <Button 
              onClick={() => copyReferralLink(`REF${user?.id || ""}`)}
              className="flex items-center gap-2"
            >
              <Share size={16} />
              Copy Link
            </Button>
          </div>
        </CardContent>
        <CardFooter className="bg-muted/50 border-t">
          <p className="text-sm text-muted-foreground">
            When someone registers using this link, you'll earn commission on their subscription.
          </p>
        </CardFooter>
      </Card>

      {/* Referrals List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Referrals</CardTitle>
          <CardDescription>
            Track the status of your referrals and earned commissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : referralsData?.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              You haven't made any referrals yet. Start referring customers to earn commissions!
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Referral Code</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Commission</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {referralsData?.map((referral) => (
                    <TableRow key={referral.id}>
                      <TableCell className="font-mono">
                        {referral.referral_code}
                      </TableCell>
                      <TableCell>{referral.referral_email}</TableCell>
                      <TableCell>{formatDate(referral.created_at)}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadge(referral.status)}>
                          {referral.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {referral.commission_amount 
                          ? new Intl.NumberFormat('en-IN', {
                              style: 'currency',
                              currency: 'INR',
                            }).format(referral.commission_amount)
                          : "—"
                        }
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyReferralLink(referral.referral_code)}
                        >
                          <Share size={14} className="mr-1" />
                          Share
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Referral Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                referralsData?.length || 0
              )}
            </div>
            <p className="text-sm text-muted-foreground">Total Referrals</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                referralsData?.filter(r => r.status === "converted").length || 0
              )}
            </div>
            <p className="text-sm text-muted-foreground">Converted</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Skeleton className="h-8 w-24" />
              ) : (
                new Intl.NumberFormat('en-IN', {
                  style: 'currency',
                  currency: 'INR',
                  maximumFractionDigits: 0,
                }).format(
                  referralsData?.reduce((sum, ref) => 
                    sum + (ref.commission_amount || 0), 0) || 0
                )
              )}
            </div>
            <p className="text-sm text-muted-foreground">Total Commission</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
