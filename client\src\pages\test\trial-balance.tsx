import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useContextData } from "@/lib/useContextData";
import { useState } from "react";

export default function TrialBalancePage() {
  const { toast } = useToast();
  const { companyId } = useContextData();
  const [report, setReport] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateReport = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get current date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];
      if (!companyId) {
        throw new Error('Company ID is required to generate the report');
      }
      const response = await apiRequest('GET', `/api/companies/${companyId}/reports/trial-balance?asOfDate=${today}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate trial balance report');
      }

      const data = await response.json();
      setReport(data);
      toast({
        title: "Report Generated",
        description: "Trial balance report has been generated successfully.",
      });
    } catch (err) {
      console.error('Error generating trial balance:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to generate trial balance report',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <Card>
        <CardHeader>
          <CardTitle>Trial Balance Report</CardTitle>
          <CardDescription>
            Generate a trial balance report to verify that all debits equal credits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-6">
            <Button
              onClick={generateReport}
              disabled={loading}
            >
              {loading ? "Generating..." : "Generate Trial Balance"}
            </Button>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 p-4 mb-6 rounded">
              {error}
            </div>
          )}

          {report && (
            <div className="overflow-x-auto">
              <h3 className="text-lg font-semibold mb-4">Trial Balance Summary</h3>
              <div className="bg-blue-50 p-4 mb-4 rounded">
                <p><strong>Total Debits:</strong> {report.totals.debit_total.toFixed(2)}</p>
                <p><strong>Total Credits:</strong> {report.totals.credit_total.toFixed(2)}</p>
                <p><strong>Difference:</strong> {report.totals.difference.toFixed(2)}</p>
              </div>

              <h3 className="text-lg font-semibold mb-4">Account Details</h3>
              <table className="min-w-full border-collapse border">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border p-2 text-left">Account Code</th>
                    <th className="border p-2 text-left">Account Name</th>
                    <th className="border p-2 text-left">Type</th>
                    <th className="border p-2 text-right">Debit</th>
                    <th className="border p-2 text-right">Credit</th>
                    <th className="border p-2 text-right">Balance</th>
                  </tr>
                </thead>
                <tbody>
                  {report.accounts.map((account: any) => (
                    <tr key={account.id}>
                      <td className="border p-2">{account.code}</td>
                      <td className="border p-2">{account.name}</td>
                      <td className="border p-2 capitalize">{account.type}</td>
                      <td className="border p-2 text-right">{account.debit_total.toFixed(2)}</td>
                      <td className="border p-2 text-right">{account.credit_total.toFixed(2)}</td>
                      <td className="border p-2 text-right">{account.balance.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}