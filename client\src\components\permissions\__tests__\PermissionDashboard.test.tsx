import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockFetchSuccess, mockFetchError, createMockRole, createMockPermissionCategory } from '../../../../tests/utils';
import { PermissionDashboard } from '../PermissionDashboard';

describe('PermissionDashboard', () => {
  const mockAnalytics = {
    total_roles: 5,
    total_permissions: 25,
    total_users_with_roles: 100,
    most_common_permissions: [
      { permission_code: 'loan_view', permission_name: 'View Loans', usage_count: 95 },
      { permission_code: 'customer_view', permission_name: 'View Customers', usage_count: 90 },
    ],
    least_used_permissions: [
      { permission_code: 'admin_delete', permission_name: 'Delete Admin Data', usage_count: 2 },
    ],
    roles_by_permission_count: [
      { role_name: 'Admin', permission_count: 20 },
      { role_name: 'Manager', permission_count: 15 },
    ],
  };

  const mockRoles = [
    createMockRole({ id: 1, name: 'Admin', permissions: [1, 2, 3] }),
    createMockRole({ id: 2, name: 'User', permissions: [1] }),
  ];

  const mockPermissionCategories = [
    createMockPermissionCategory({
      category: 'loans',
      metadata: { name: 'Loan Management', description: 'Loan operations', icon: 'CreditCard' },
      permissions: [
        { id: 1, code: 'loan_create', name: 'Create Loans', description: 'Create new loans', category: 'loans' },
        { id: 2, code: 'loan_approve', name: 'Approve Loans', description: 'Approve loan applications', category: 'loans' },
      ],
    }),
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock successful API responses
    global.fetch = vi.fn()
      .mockImplementationOnce(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockRoles),
      }))
      .mockImplementationOnce(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockPermissionCategories),
      }))
      .mockImplementationOnce(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockAnalytics),
      }));
  });

  describe('Loading State', () => {
    it('should show loading spinner initially', () => {
      render(<PermissionDashboard />);

      expect(screen.getByText(/loading permission data/i)).toBeInTheDocument();
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });
  });

  describe('Data Loading', () => {
    it('should load and display permission data successfully', async () => {
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      // Check if analytics data is displayed
      expect(screen.getByText('5')).toBeInTheDocument(); // total_roles
      expect(screen.getByText('25')).toBeInTheDocument(); // total_permissions
      expect(screen.getByText('100')).toBeInTheDocument(); // total_users_with_roles
    });

    it('should handle API errors gracefully', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('API Error'));

      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText(/error loading permission data/i)).toBeInTheDocument();
      });
    });

    it('should handle individual API endpoint failures', async () => {
      global.fetch = vi.fn()
        .mockImplementationOnce(() => Promise.resolve({
          ok: false,
          status: 500,
        }))
        .mockImplementationOnce(() => Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockPermissionCategories),
        }))
        .mockImplementationOnce(() => Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAnalytics),
        }));

      render(<PermissionDashboard />);

      await waitFor(() => {
        // Should still show some content even if one endpoint fails
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });
    });
  });

  describe('Tab Navigation', () => {
    it('should switch between tabs correctly', async () => {
      const user = userEvent.setup();
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      // Check initial tab (Permission Matrix)
      expect(screen.getByRole('tab', { name: /permission matrix/i })).toHaveAttribute('aria-selected', 'true');

      // Switch to Analytics tab
      const analyticsTab = screen.getByRole('tab', { name: /analytics/i });
      await user.click(analyticsTab);

      expect(analyticsTab).toHaveAttribute('aria-selected', 'true');

      // Switch to Role Overview tab
      const rolesTab = screen.getByRole('tab', { name: /role overview/i });
      await user.click(rolesTab);

      expect(rolesTab).toHaveAttribute('aria-selected', 'true');
    });
  });

  describe('Analytics Display', () => {
    it('should display analytics cards with correct data', async () => {
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      // Switch to analytics tab
      const analyticsTab = screen.getByRole('tab', { name: /analytics/i });
      await userEvent.setup().click(analyticsTab);

      // Check analytics cards
      expect(screen.getByText('Total Roles')).toBeInTheDocument();
      expect(screen.getByText('Total Permissions')).toBeInTheDocument();
      expect(screen.getByText('Users with Roles')).toBeInTheDocument();

      // Check most common permissions
      expect(screen.getByText('View Loans')).toBeInTheDocument();
      expect(screen.getByText('View Customers')).toBeInTheDocument();

      // Check least used permissions
      expect(screen.getByText('Delete Admin Data')).toBeInTheDocument();
    });
  });

  describe('Role Overview', () => {
    it('should display role information in role overview tab', async () => {
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      // Switch to role overview tab
      const rolesTab = screen.getByRole('tab', { name: /role overview/i });
      await userEvent.setup().click(rolesTab);

      // Check if roles are displayed
      expect(screen.getByText('Admin')).toBeInTheDocument();
      expect(screen.getByText('User')).toBeInTheDocument();
    });
  });

  describe('Refresh Functionality', () => {
    it('should refresh data when refresh button is clicked', async () => {
      const user = userEvent.setup();
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);

      // Should show loading state again
      expect(refreshButton).toBeDisabled();
    });
  });

  describe('Permission Matrix Integration', () => {
    it('should handle permission changes in the matrix', async () => {
      const user = userEvent.setup();
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      // Mock successful permission update
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      });

      // Find and click a permission checkbox
      const checkboxes = screen.getAllByRole('checkbox');
      if (checkboxes.length > 0) {
        await user.click(checkboxes[0]);

        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            expect.stringContaining('/api/permissions/assign'),
            expect.objectContaining({
              method: 'POST',
            })
          );
        });
      }
    });

    it('should handle bulk permission assignment', async () => {
      const user = userEvent.setup();
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      // Mock successful bulk assignment
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      });

      // Look for bulk assignment functionality (implementation specific)
      const bulkButtons = screen.queryAllByText(/bulk/i);
      if (bulkButtons.length > 0) {
        await user.click(bulkButtons[0]);

        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalledWith(
            expect.stringContaining('/api/permissions/bulk-assign'),
            expect.objectContaining({
              method: 'POST',
            })
          );
        });
      }
    });
  });

  describe('Company Context', () => {
    it('should load data for specific company when companyId is provided', async () => {
      render(<PermissionDashboard companyId={123} />);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('company_id=123'),
          expect.any(Object)
        );
      });
    });
  });

  describe('Error Handling', () => {
    it('should show error alert when updates fail', async () => {
      const user = userEvent.setup();
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
      });

      // Mock failed permission update
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ error: 'Update failed' }),
      });

      const checkboxes = screen.getAllByRole('checkbox');
      if (checkboxes.length > 0) {
        await user.click(checkboxes[0]);

        await waitFor(() => {
          expect(screen.getByText(/error/i)).toBeInTheDocument();
        });
      }
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', async () => {
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByRole('heading', { name: /permission management/i })).toBeInTheDocument();
      });
    });

    it('should have accessible tab navigation', async () => {
      render(<PermissionDashboard />);

      await waitFor(() => {
        expect(screen.getByRole('tablist')).toBeInTheDocument();
      });

      const tabs = screen.getAllByRole('tab');
      expect(tabs).toHaveLength(3);

      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected');
      });
    });
  });
});
