import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/lib/auth';
import { useCompany } from '@/lib/companies';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loader2, Plus, Search, UserPlus, Users, Shield, Settings, Eye, UserCheck, Workflow, RefreshCw, KeyRound } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useLocation } from 'wouter';
import BulkOperationsManager from '@/components/user-management/BulkOperationsManager';
import { UserManagementOnboarding } from '@/components/onboarding/UserManagementOnboarding';
import { isTourCompleted } from '@/lib/tourTracking';
import { ContextualHelp } from '@/components/help/ContextualHelp';


// Define types
interface User {
  id: number;
  email: string;
  full_name: string;
  role: string;
  company_id: number;
  mobile_number?: string;
  active: boolean;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  company_id: number;
  permissions?: Permission[];
}

interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
}

interface Group {
  id: number;
  name: string;
  description: string;
  company_id: number;
  branch_id: number | null;
  members?: User[];
  roles?: Role[];
}

export default function UserManagement() {
  const { toast } = useToast();
  const { currentCompany } = useCompany();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const [activeTab, setActiveTab] = useState('users');
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateUserDialogOpen, setIsCreateUserDialogOpen] = useState(false);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isCreateRoleDialogOpen, setIsCreateRoleDialogOpen] = useState(false);
  const [userExperienceLevel, setUserExperienceLevel] = useState<'basic' | 'advanced' | 'expert'>('basic');
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isCreateGroupDialogOpen, setIsCreateGroupDialogOpen] = useState(false);
  const [isPasswordResetDialogOpen, setIsPasswordResetDialogOpen] = useState(false);
  const [passwordResetData, setPasswordResetData] = useState<{
    userEmail: string;
    temporaryPassword: string;
  } | null>(null);



  // User form state
  const [userForm, setUserForm] = useState({
    email: '',
    full_name: '',
    password: '',
    confirmPassword: '',
    role: 'employee',
    mobile_number: '',
    company_id: currentCompany?.company_id || currentCompany?.id
  });

  // Edit user form state (without password fields)
  const [editUserForm, setEditUserForm] = useState({
    email: '',
    full_name: '',
    role: 'employee',
    mobile_number: '',
    company_id: currentCompany?.company_id || currentCompany?.id
  });



  // Role form state
  const [roleForm, setRoleForm] = useState({
    name: '',
    description: '',
    company_id: currentCompany?.company_id || currentCompany?.id,
    permissions: [] as number[]
  });

  // Group form state
  const [groupForm, setGroupForm] = useState({
    name: '',
    description: '',
    company_id: currentCompany?.company_id || currentCompany?.id,
    branch_id: null as number | null
  });

  // Get company ID with proper fallback logic
  const getCompanyId = () => {
    // Priority 1: Company context (when available)
    const contextCompanyId = currentCompany?.company_id || currentCompany?.id;
    // Priority 2: User's company ID (always available when logged in)
    const userCompanyId = user?.company_id;

    return contextCompanyId || userCompanyId;
  };

  const companyId = getCompanyId();

  // Fetch users with improved dependency management
  const {
    data: users,
    isLoading: isLoadingUsers,
    error: usersError,
    refetch: refetchUsers
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'users'],
    queryFn: async () => {
      const targetCompanyId = companyId;
      if (!targetCompanyId) {
        return [];
      }

      try {

        // Use the company-specific users endpoint
        const res = await apiRequest('GET', `/api/companies/${targetCompanyId}/users`);

        if (!res.ok) {
          throw new Error(`Failed to fetch users: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();

        // Make sure we include the current user if they're not already in the list
        if (user && data.every((u: any) => u.id !== user.id)) {
          return [...data, {
            id: user.id,
            email: user.email,
            full_name: user.full_name,
            role: user.role,
            company_id: targetCompanyId
          }];
        }

        return data;
      } catch (error) {
        // Fallback: If we can't get the users, at least show the current user
        if (user) {
          return [{
            id: user.id,
            email: user.email,
            full_name: user.full_name,
            role: user.role,
            company_id: targetCompanyId
          }];
        }

        throw error; // Re-throw to let React Query handle retries
      }
    },
    enabled: !!companyId && !!user, // Enable when we have both company ID and user
    retry: 3, // Retry failed requests
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    staleTime: 30000, // Consider data fresh for 30 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });



  // Show onboarding for new users (basic level with no users) - but only if they haven't seen it before
  useEffect(() => {
    if (userExperienceLevel === 'basic' && users && users.length <= 1 && user?.id) {
      // Check if user has already completed or skipped the tour
      const hasSeenTour = isTourCompleted(user.id, 'user-management-onboarding');

      if (!hasSeenTour) {
        // Only show if there's just the current user or no users AND they haven't seen the tour
        const timer = setTimeout(() => {
          setShowOnboarding(true);
        }, 1000); // Delay to let the page load

        return () => clearTimeout(timer);
      }
    }
  }, [users, userExperienceLevel, user?.id]);

  // Update form company IDs when current company changes and invalidate queries
  useEffect(() => {
    const newCompanyId = getCompanyId();
    if (newCompanyId) {
      setRoleForm(prev => ({ ...prev, company_id: newCompanyId }));
      setGroupForm(prev => ({ ...prev, company_id: newCompanyId }));
      setUserForm(prev => ({ ...prev, company_id: newCompanyId }));
      setEditUserForm(prev => ({ ...prev, company_id: newCompanyId }));

      // Invalidate all company-dependent queries when company changes
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) && (
            queryKey.includes('users') ||
            queryKey.includes('roles') ||
            queryKey.includes('groups')
          );
        }
      });
    }
  }, [currentCompany, queryClient]);

  // Fetch roles with improved dependency management
  const { data: roles, isLoading: isLoadingRoles, error: rolesError } = useQuery({
    queryKey: ['/api/roles', 'company', companyId],
    queryFn: async () => {
      const targetCompanyId = companyId;
      if (!targetCompanyId) {
        if (import.meta.env.DEV) {
          console.warn('No company ID available for fetching roles');
        }
        return [];
      }

      try {
        const url = `/api/roles?company_id=${targetCompanyId}`;


        const res = await apiRequest('GET', url);

        if (!res.ok) {
          console.error('Failed to fetch roles:', res.status, res.statusText);
          throw new Error(`Failed to fetch roles: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();



        // Ensure we return an array and apply proper ordering
        if (Array.isArray(data)) {
          // Sort roles: System roles first (alphabetically), then Custom roles (alphabetically)
          const sortedRoles = data.sort((a, b) => {
            // First, group by system vs custom roles
            if (a.is_system && !b.is_system) return -1; // System roles first
            if (!a.is_system && b.is_system) return 1;  // Custom roles second

            // Within each group, sort alphabetically by name
            return a.name.localeCompare(b.name);
          });



          return sortedRoles;
        } else {
          console.warn('Roles API returned non-array data:', data);
          return [];
        }
      } catch (error) {
        console.error('Error fetching roles:', error);
        throw error; // Re-throw to let React Query handle it
      }
    },
    enabled: !!companyId && !!user,
    retry: 2,
    staleTime: 30000
  });

  // Fetch permissions
  const { data: permissions, isLoading: isLoadingPermissions } = useQuery({
    queryKey: ['/api/permissions'],
    queryFn: async () => {
      try {

        const res = await apiRequest('GET', '/api/permissions');

        if (!res.ok) {
          throw new Error(`Failed to fetch permissions: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();


        // Ensure we always return an array
        if (Array.isArray(data)) {
          return data;
        } else if (data && Array.isArray(data.permissions)) {
          return data.permissions;
        } else {
          console.warn('Permissions API returned non-array data:', data);
          return [];
        }
      } catch (error) {
        console.error('Error fetching permissions:', error);
        return [];
      }
    }
  });

  // Fetch groups with improved dependency management
  const { data: groups, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['/api/group-management/groups', companyId],
    queryFn: async () => {
      const targetCompanyId = companyId;
      if (!targetCompanyId) return [];
      try {
        // Make sure we're passing the company_id as a query parameter
        const res = await apiRequest('GET', `/api/group-management/groups?company_id=${targetCompanyId}`);
        const data = await res.json();

        return data;
      } catch (error) {
        console.error('[GROUPS_FETCH] Error fetching groups:', error);
        return [];
      }
    },
    enabled: !!companyId && !!user,
    retry: 2,
    staleTime: 30000
  });

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: async (data: typeof userForm) => {
      const res = await apiRequest('POST', '/api/auth/register', data);

      if (!res.ok) {
        let errorData;
        try {
          errorData = await res.json();
        } catch (e) {
          errorData = { message: `HTTP ${res.status}: ${res.statusText}` };
        }

        throw new Error(errorData.message || `Failed to create user: ${res.status} ${res.statusText}`);
      }

      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.company_id || currentCompany?.id, 'users'] });
      toast({
        title: 'User created',
        description: 'The user has been created successfully.',
      });
      setIsCreateUserDialogOpen(false);
      resetUserForm();
    },
    onError: (error: any) => {
      let errorMessage = 'There was an error creating the user.';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object' && error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error creating user',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async (data: { userId: number; userData: typeof editUserForm }) => {
      const res = await apiRequest('PUT', `/api/users/${data.userId}`, data.userData);

      if (!res.ok) {
        let errorData;
        try {
          errorData = await res.json();
        } catch (e) {
          errorData = { message: `HTTP ${res.status}: ${res.statusText}` };
        }

        throw new Error(errorData.message || `Failed to update user: ${res.status} ${res.statusText}`);
      }

      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.company_id || currentCompany?.id, 'users'] });
      toast({
        title: 'User updated',
        description: 'The user has been updated successfully.',
      });
      setIsEditUserDialogOpen(false);
      setSelectedUser(null);
      resetEditUserForm();
    },
    onError: (error: any) => {
      let errorMessage = 'There was an error updating the user.';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object' && error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error updating user',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  });

  // Create role mutation
  const createRoleMutation = useMutation({
    mutationFn: async (data: typeof roleForm) => {


      const res = await apiRequest('POST', '/api/roles', data);

      if (!res.ok) {
        let errorData;
        try {
          errorData = await res.json();
        } catch (e) {
          errorData = { message: `HTTP ${res.status}: ${res.statusText}` };
        }

        if (import.meta.env.DEV) {
          console.error('Role creation failed:', {
            status: res.status,
            statusText: res.statusText,
            errorData,
            url: res.url
          });
        }

        // Handle specific error cases
        if (res.status === 400 && errorData.message === 'Role name already exists for this company') {
          throw new Error('A role with this name already exists. Please choose a different name.');
        } else if (res.status === 403) {
          throw new Error('You do not have permission to create roles for this company.');
        } else {
          throw new Error(errorData.message || `Failed to create role: ${res.status} ${res.statusText}`);
        }
      }

      return res.json();
    },
    onSuccess: () => {
      // With no-cache globally enabled, just invalidate the specific query
      queryClient.invalidateQueries({ queryKey: ['/api/roles'] });

      toast({
        title: 'Role created',
        description: 'The role has been created successfully.',
      });
      setIsCreateRoleDialogOpen(false);
      resetRoleForm();
    },
    onError: (error: any) => {
      console.error('Role creation error:', error);

      let errorMessage = 'There was an error creating the role.';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object' && error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error creating role',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  });

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: async (data: typeof groupForm) => {
      const res = await apiRequest('POST', '/api/groups', data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/group-management/groups', currentCompany?.company_id || currentCompany?.id] });
      toast({
        title: 'Group created',
        description: 'The group has been created successfully.',
      });
      setIsCreateGroupDialogOpen(false);
      resetGroupForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating group',
        description: error.message || 'There was an error creating the group.',
        variant: 'destructive',
      });
    }
  });

  // Password reset mutation
  const passwordResetMutation = useMutation({
    mutationFn: async (userId: number) => {
      const res = await apiRequest('POST', `/api/users/${userId}/reset-password`);

      if (!res.ok) {
        let errorData;
        try {
          errorData = await res.json();
        } catch (e) {
          errorData = { message: `HTTP ${res.status}: ${res.statusText}` };
        }

        throw new Error(errorData.message || `Failed to reset password: ${res.status} ${res.statusText}`);
      }

      return res.json();
    },
    onSuccess: (data) => {
      setPasswordResetData({
        userEmail: data.userEmail,
        temporaryPassword: data.temporaryPassword
      });
      setIsPasswordResetDialogOpen(true);
      toast({
        title: 'Password reset successful',
        description: 'A temporary password has been generated for the user.',
      });
    },
    onError: (error: any) => {
      let errorMessage = 'There was an error resetting the password.';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object' && error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Error resetting password',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  });

  // Handle form changes
  const handleUserFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserForm(prev => ({ ...prev, [name]: value }));
  };

  const handleEditUserFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditUserForm(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRoleForm(prev => ({ ...prev, [name]: value }));
  };

  const handleGroupFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGroupForm(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleChange = (value: string) => {
    setUserForm(prev => ({ ...prev, role: value }));
  };

  const handleEditRoleChange = (value: string) => {
    setEditUserForm(prev => ({ ...prev, role: value }));
  };

  const handleBranchChange = (value: string) => {
    setGroupForm(prev => ({ ...prev, branch_id: value === 'none' ? null : parseInt(value) }));
  };

  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    setRoleForm(prev => {
      if (checked) {
        return { ...prev, permissions: [...prev.permissions, permissionId] };
      } else {
        return { ...prev, permissions: prev.permissions.filter(id => id !== permissionId) };
      }
    });
  };

  // Form submission handlers
  const handleSubmitUser = (e: React.FormEvent) => {
    e.preventDefault();
    if (userForm.password !== userForm.confirmPassword) {
      toast({
        title: 'Passwords do not match',
        description: 'Please make sure your passwords match.',
        variant: 'destructive',
      });
      return;
    }

    // Validate mobile number format if provided
    if (userForm.mobile_number && userForm.mobile_number.trim() !== '') {
      const mobileRegex = /^\+91[0-9]{10}$/;
      if (!mobileRegex.test(userForm.mobile_number)) {
        toast({
          title: 'Invalid mobile number format',
          description: 'Mobile number must be +91 followed by exactly 10 digits (e.g., +919876543210)',
          variant: 'destructive',
        });
        return;
      }
    }

    createUserMutation.mutate(userForm);
  };

  const handleSubmitEditUser = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;

    // Validate mobile number format if provided
    if (editUserForm.mobile_number && editUserForm.mobile_number.trim() !== '') {
      const mobileRegex = /^\+91[0-9]{10}$/;
      if (!mobileRegex.test(editUserForm.mobile_number)) {
        toast({
          title: 'Invalid mobile number format',
          description: 'Mobile number must be +91 followed by exactly 10 digits (e.g., +919876543210)',
          variant: 'destructive',
        });
        return;
      }
    }

    updateUserMutation.mutate({
      userId: selectedUser.id,
      userData: editUserForm
    });
  };

  const handleSubmitRole = (e: React.FormEvent) => {
    e.preventDefault();
    createRoleMutation.mutate(roleForm);
  };

  const handleSubmitGroup = (e: React.FormEvent) => {
    e.preventDefault();
    createGroupMutation.mutate(groupForm);
  };

  // Reset form states
  const resetUserForm = () => {
    setUserForm({
      email: '',
      full_name: '',
      password: '',
      confirmPassword: '',
      role: 'employee',
      mobile_number: '',
      company_id: currentCompany?.company_id || currentCompany?.id
    });
  };

  const resetEditUserForm = () => {
    setEditUserForm({
      email: '',
      full_name: '',
      role: 'employee',
      mobile_number: '',
      company_id: currentCompany?.company_id || currentCompany?.id
    });
  };

  // Open edit dialog and populate form
  const openEditUserDialog = (user: User) => {
    setSelectedUser(user);
    setEditUserForm({
      email: user.email || '',
      full_name: user.full_name || '',
      role: user.role || 'employee',
      // Store the full mobile number with +91 prefix for internal handling
      mobile_number: user.mobile_number || '',
      company_id: user.company_id || currentCompany?.company_id || currentCompany?.id
    });
    setIsEditUserDialogOpen(true);
  };

  const resetRoleForm = () => {
    setRoleForm({
      name: '',
      description: '',
      company_id: getCompanyId(),
      permissions: []
    });
  };

  const resetGroupForm = () => {
    setGroupForm({
      name: '',
      description: '',
      company_id: currentCompany?.company_id || currentCompany?.id,
      branch_id: null
    });
  };

  // Define table columns
  const userColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'full_name',
      header: 'Name',
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'mobile_number',
      header: 'Mobile Number',
      cell: ({ row }) => (
        <span className="text-sm">
          {row.original.mobile_number || (
            <span className="text-muted-foreground italic">Not provided</span>
          )}
        </span>
      ),
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => (
        <Badge variant="outline">{row.original.role}</Badge>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openEditUserDialog(row.original)}
          >
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/user-management/users/${row.original.id}/roles`)}
          >
            <UserCheck className="h-4 w-4 mr-1" />
            Roles
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/user-management/permissions/user-permissions?userId=${row.original.id}`)}
          >
            <Eye className="h-4 w-4 mr-1" />
            Permissions
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => passwordResetMutation.mutate(row.original.id)}
            disabled={passwordResetMutation.isPending}
            className="text-orange-600 hover:text-orange-700"
          >
            {passwordResetMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <>
                <KeyRound className="h-4 w-4 mr-1" />
                Reset Password
              </>
            )}
          </Button>
        </div>
      ),
    },
  ];

  const roleColumns: ColumnDef<Role>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      accessorKey: 'is_system',
      header: 'System Role',
      cell: ({ row }) => (
        row.original.is_system ? <Badge>System</Badge> : <Badge variant="outline">Custom</Badge>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {!row.original.is_system && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(`/user-management/roles/${row.original.id}`)}
            >
              Edit
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/user-management/roles/${row.original.id}`)}
          >
            <Shield className="h-4 w-4 mr-1" />
            Permissions
          </Button>
          {row.original.is_system && (
            <span className="text-xs text-muted-foreground">Read-only</span>
          )}
        </div>
      ),
    },
  ];

  const groupColumns: ColumnDef<Group>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      id: 'members',
      header: 'Members',
      cell: ({ row }) => (
        <span>{row.original.members?.length || 0} members</span>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/user-management/groups/${row.original.id}`)}
          >
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/user-management/groups/${row.original.id}`)}
          >
            Members
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/user-management/groups/${row.original.id}`)}
          >
            Roles
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground">
            {userExperienceLevel === 'basic'
              ? 'Manage your team members and their access levels'
              : 'Manage users, roles, groups, and permissions'
            }
          </p>
        </div>
        <div className="flex items-center gap-4">
          {/* Experience Level Selector */}
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">View:</label>
            <select
              value={userExperienceLevel}
              onChange={(e) => setUserExperienceLevel(e.target.value as 'basic' | 'advanced' | 'expert')}
              className="text-sm border rounded px-2 py-1"
            >
              <option value="basic">Basic</option>
              <option value="advanced">Advanced</option>
              <option value="expert">Expert</option>
            </select>
          </div>

          {/* Help and Onboarding */}
          <div className="flex items-center gap-2">
            <ContextualHelp
              topic="user-management"
              userExperienceLevel={userExperienceLevel}
              variant="button"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowOnboarding(true)}
              title="Start the user management tour"
            >
              Quick Tour
            </Button>
          </div>

          {/* Advanced Features - Only show for advanced/expert users */}
          {userExperienceLevel !== 'basic' && (
            <>
              <Button
                variant="outline"
                onClick={() => navigate('/user-management/permissions')}
                className="flex items-center gap-2"
              >
                <Shield className="h-4 w-4" />
                Permission Matrix
              </Button>
              {userExperienceLevel === 'expert' && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/user-management/role-hierarchy')}
                    className="flex items-center gap-2"
                  >
                    <UserCheck className="h-4 w-4" />
                    Role Hierarchy
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/user-management/approval-workflows')}
                    className="flex items-center gap-2"
                  >
                    <Workflow className="h-4 w-4" />
                    Approval Workflows
                  </Button>
                </>
              )}
            </>
          )}
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {activeTab === 'users' && (
            <Button onClick={() => setIsCreateUserDialogOpen(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          )}
          {activeTab === 'roles' && (
            <Button onClick={() => setIsCreateRoleDialogOpen(true)}>
              <Shield className="mr-2 h-4 w-4" />
              Add Role
            </Button>
          )}
          {activeTab === 'groups' && (
            <Button onClick={() => setIsCreateGroupDialogOpen(true)}>
              <Users className="mr-2 h-4 w-4" />
              Add Group
            </Button>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                <p className="text-2xl font-bold">{users?.length || 0}</p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Roles</p>
                <p className="text-2xl font-bold">{roles?.length || 0}</p>
              </div>
              <Shield className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">User Groups</p>
                <p className="text-2xl font-bold">{groups?.length || 0}</p>
              </div>
              <Settings className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Permissions</p>
                <p className="text-2xl font-bold">{permissions?.length || 0}</p>
              </div>
              <UserCheck className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="users">Users</TabsTrigger>
          {userExperienceLevel !== 'basic' && (
            <TabsTrigger value="roles">Roles</TabsTrigger>
          )}
          {userExperienceLevel === 'expert' && (
            <>
              <TabsTrigger value="groups">Groups</TabsTrigger>
              <TabsTrigger value="bulk-operations">Bulk Operations</TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>Manage users in your organization.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingUsers ? (
                <div className="flex flex-col justify-center items-center h-40 space-y-2">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Loading users...</p>
                </div>
              ) : usersError ? (
                <div className="flex flex-col justify-center items-center h-40 space-y-4">
                  <div className="text-center">
                    <p className="text-sm font-medium text-destructive">Failed to load users</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {usersError instanceof Error ? usersError.message : 'Unknown error occurred'}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => refetchUsers()}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Retry
                  </Button>
                </div>
              ) : (
                <DataTable columns={userColumns} data={users || []} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Roles</CardTitle>
                <CardDescription>
                  Manage roles and permissions.
                </CardDescription>
              </div>
              <Button onClick={() => setIsCreateRoleDialogOpen(true)}>
                <UserCheck className="mr-2 h-4 w-4" />
                Add Role
              </Button>
            </CardHeader>
            <CardContent>
              {isLoadingRoles ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">Loading roles...</span>
                </div>
              ) : rolesError ? (
                <div className="flex flex-col items-center justify-center h-40 text-center">
                  <p className="text-destructive mb-2">Failed to load roles</p>
                  <p className="text-sm text-muted-foreground mb-4">
                    {rolesError instanceof Error ? rolesError.message : 'Unknown error'}
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    Retry
                  </Button>
                </div>
              ) : !roles || roles.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 text-center">
                  <UserCheck className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground mb-4">No roles found for this company</p>
                  <Button onClick={() => setIsCreateRoleDialogOpen(true)}>
                    <UserCheck className="mr-2 h-4 w-4" />
                    Create Your First Role
                  </Button>

                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <p className="text-sm text-muted-foreground">
                      {roles.length} role{roles.length !== 1 ? 's' : ''} found
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsCreateRoleDialogOpen(true)}
                    >
                      <UserCheck className="mr-2 h-4 w-4" />
                      Add Role
                    </Button>
                  </div>
                  <DataTable columns={roleColumns} data={roles} />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="groups">
          <Card>
            <CardHeader>
              <CardTitle>Groups</CardTitle>
              <CardDescription>Manage user groups.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingGroups ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <DataTable columns={groupColumns} data={groups || []} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk-operations">
          <BulkOperationsManager />
        </TabsContent>
      </Tabs>

      {/* Create User Dialog */}
      <Dialog open={isCreateUserDialogOpen} onOpenChange={setIsCreateUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user for your organization.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitUser}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  name="full_name"
                  value={userForm.full_name}
                  onChange={handleUserFormChange}
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={userForm.email}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={userForm.password}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={userForm.confirmPassword}
                  onChange={handleUserFormChange}
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="mobile_number">Mobile Number</Label>
                <div className="flex">
                  <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                    +91
                  </span>
                  <Input
                    id="mobile_number"
                    name="mobile_number"
                    type="tel"
                    className="rounded-l-none"
                    value={userForm.mobile_number?.replace('+91', '') || ''}
                    onChange={(e) => {
                      // Allow only digits and limit to 10 digits
                      const value = e.target.value.replace(/[^0-9]/g, '').substring(0, 10);
                      // Store with +91 prefix if value is not empty
                      const fullValue = value ? `+91${value}` : '';
                      setUserForm(prev => ({ ...prev, mobile_number: fullValue }));
                    }}
                    placeholder="9876543210"
                    maxLength={10}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Enter 10-digit mobile number (optional)
                </p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Select value={userForm.role} onValueChange={handleRoleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="owner">Owner</SelectItem>
                      <SelectItem value="employee">Employee</SelectItem>
                      <SelectItem value="agent">Agent</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetUserForm();
                  setIsCreateUserDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createUserMutation.isPending}>
                {createUserMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create User"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserDialogOpen} onOpenChange={setIsEditUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitEditUser}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit_full_name">Full Name</Label>
                <Input
                  id="edit_full_name"
                  name="full_name"
                  value={editUserForm.full_name}
                  onChange={handleEditUserFormChange}
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit_email">Email</Label>
                <Input
                  id="edit_email"
                  name="email"
                  type="email"
                  value={editUserForm.email}
                  onChange={handleEditUserFormChange}
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit_mobile_number">Mobile Number</Label>
                <div className="flex">
                  <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                    +91
                  </span>
                  <Input
                    id="edit_mobile_number"
                    name="mobile_number"
                    type="tel"
                    className="rounded-l-none"
                    value={editUserForm.mobile_number?.replace('+91', '') || ''}
                    onChange={(e) => {
                      // Allow only digits and limit to 10 digits
                      const value = e.target.value.replace(/[^0-9]/g, '').substring(0, 10);
                      // Store with +91 prefix if value is not empty
                      const fullValue = value ? `+91${value}` : '';
                      setEditUserForm(prev => ({ ...prev, mobile_number: fullValue }));
                    }}
                    placeholder="9876543210"
                    maxLength={10}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Enter 10-digit mobile number
                </p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit_role">Role</Label>
                <Select value={editUserForm.role} onValueChange={handleEditRoleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="owner">Owner</SelectItem>
                      <SelectItem value="employee">Employee</SelectItem>
                      <SelectItem value="agent">Agent</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsEditUserDialogOpen(false);
                  setSelectedUser(null);
                  resetEditUserForm();
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateUserMutation.isPending}>
                {updateUserMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  "Update User"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Role Dialog */}
      <Dialog open={isCreateRoleDialogOpen} onOpenChange={setIsCreateRoleDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Role</DialogTitle>
            <DialogDescription>
              Create a new role with specific permissions.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitRole}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Role Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={roleForm.name}
                  onChange={handleRoleFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  name="description"
                  value={roleForm.description}
                  onChange={handleRoleFormChange}
                />
              </div>
              <div className="grid gap-2">
                <Label>Permissions</Label>
                <div className="border rounded-md p-4 h-[200px] overflow-y-auto">
                  {isLoadingPermissions ? (
                    <div className="flex justify-center items-center h-full">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {Array.isArray(permissions) && permissions.length > 0 ? (
                        permissions.map((permission: Permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`permission-${permission.id}`}
                              checked={roleForm.permissions.includes(permission.id)}
                              onCheckedChange={(checked) => handlePermissionChange(permission.id, !!checked)}
                            />
                            <label
                              htmlFor={`permission-${permission.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {permission.name}
                            </label>
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-muted-foreground py-4">
                          No permissions available
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetRoleForm();
                  setIsCreateRoleDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createRoleMutation.isPending}>
                {createRoleMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Role"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Group Dialog */}
      <Dialog open={isCreateGroupDialogOpen} onOpenChange={setIsCreateGroupDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Group</DialogTitle>
            <DialogDescription>
              Create a new user group.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitGroup}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Group Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={groupForm.name}
                  onChange={handleGroupFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  name="description"
                  value={groupForm.description}
                  onChange={handleGroupFormChange}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="branch_id">Branch (Optional)</Label>
                <Select value={groupForm.branch_id?.toString() || 'none'} onValueChange={handleBranchChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a branch" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Branch</SelectItem>
                    {/* Branch options would be populated here */}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetGroupForm();
                  setIsCreateGroupDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createGroupMutation.isPending}>
                {createGroupMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Group"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Password Reset Dialog */}
      <Dialog open={isPasswordResetDialogOpen} onOpenChange={setIsPasswordResetDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Password Reset Successful</DialogTitle>
            <DialogDescription>
              A temporary password has been generated for the user.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>User Email</Label>
              <Input
                value={passwordResetData?.userEmail || ''}
                readOnly
                className="bg-muted"
              />
            </div>
            <div className="grid gap-2">
              <Label>Temporary Password</Label>
              <div className="flex gap-2">
                <Input
                  value={passwordResetData?.temporaryPassword || ''}
                  readOnly
                  className="bg-muted font-mono"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    if (passwordResetData?.temporaryPassword) {
                      navigator.clipboard.writeText(passwordResetData.temporaryPassword);
                      toast({
                        title: 'Copied to clipboard',
                        description: 'The temporary password has been copied to your clipboard.',
                      });
                    }
                  }}
                >
                  Copy
                </Button>
              </div>
            </div>
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <p className="text-sm text-yellow-800">
                <strong>Important:</strong> Please share this temporary password securely with the user.
                They will be required to change it on their next login.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={() => {
                setIsPasswordResetDialogOpen(false);
                setPasswordResetData(null);
              }}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Onboarding Tour */}
      {user?.id && (
        <UserManagementOnboarding
          isOpen={showOnboarding}
          onClose={() => setShowOnboarding(false)}
          userExperienceLevel={userExperienceLevel}
          onExperienceLevelChange={setUserExperienceLevel}
          userId={user.id}
        />
      )}
    </div>
  );
}
