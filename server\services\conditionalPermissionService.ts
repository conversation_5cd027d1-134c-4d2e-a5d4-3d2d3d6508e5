import { db } from '../db';
import {
  permissionConditions, permissions, users
} from '@shared/schema';
import { eq, and, inArray } from 'drizzle-orm';

export interface PermissionEvaluationContext {
  userId: number;
  permissionCode: string;
  amount?: number;
  timestamp?: Date;
  ipAddress?: string;
  userAgent?: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
  deviceInfo?: {
    type?: 'desktop' | 'laptop' | 'mobile' | 'tablet';
    id?: string;
    registered?: boolean;
  };
  sessionInfo?: {
    sessionAge?: number; // in seconds
    lastActivity?: Date;
    mfaVerified?: boolean;
    freshAuth?: boolean;
  };
  approvalInfo?: {
    requiresApproval?: boolean;
    approverIds?: number[];
    approvalStatus?: 'pending' | 'approved' | 'rejected';
  };
}

export interface ConditionEvaluationResult {
  passed: boolean;
  reason?: string;
  requiresApproval?: boolean;
  approverRoles?: string[];
  metadata?: any;
}

export class ConditionalPermissionService {

  /**
   * Evaluate all conditions for a permission
   * @param context Permission evaluation context
   * @returns Promise<ConditionEvaluationResult> Evaluation result
   */
  async evaluatePermissionConditions(context: PermissionEvaluationContext): Promise<ConditionEvaluationResult> {
    try {
      // Get permission ID from code
      const [permission] = await db
        .select({ id: permissions.id })
        .from(permissions)
        .where(eq(permissions.code, context.permissionCode))
        .limit(1);

      if (!permission) {
        return { passed: false, reason: 'Permission not found' };
      }

      // Get all active conditions for this permission, ordered by priority
      const conditions = await db
        .select()
        .from(permissionConditions)
        .where(and(
          eq(permissionConditions.permission_id, permission.id),
          eq(permissionConditions.is_active, true)
        ))
        .orderBy(permissionConditions.priority);

      if (conditions.length === 0) {
        // No conditions means permission is granted if user has the base permission
        return { passed: true };
      }

      // Evaluate each condition
      for (const condition of conditions) {
        const result = await this.evaluateCondition(condition, context);

        if (!result.passed) {
          return result;
        }

        // If condition requires approval, collect approval info
        if (result.requiresApproval) {
          return {
            passed: false,
            reason: 'Approval required',
            requiresApproval: true,
            approverRoles: result.approverRoles,
            metadata: result.metadata
          };
        }
      }

      return { passed: true };
    } catch (error) {
      console.error('Error evaluating permission conditions:', error);
      return { passed: false, reason: 'Evaluation error' };
    }
  }

  /**
   * Evaluate a single condition
   * @param condition Permission condition from database
   * @param context Evaluation context
   * @returns Promise<ConditionEvaluationResult> Condition result
   */
  private async evaluateCondition(
    condition: any,
    context: PermissionEvaluationContext
  ): Promise<ConditionEvaluationResult> {
    const config = condition.condition_config;

    switch (condition.condition_type) {
      case 'time':
        return this.evaluateTimeCondition(config, context);

      case 'location':
        return this.evaluateLocationCondition(config, context);

      case 'amount':
        return this.evaluateAmountCondition(config, context);

      case 'approval':
        return this.evaluateApprovalCondition(config, context);

      case 'device':
        return this.evaluateDeviceCondition(config, context);

      case 'session':
        return this.evaluateSessionCondition(config, context);

      default:
        return { passed: false, reason: `Unknown condition type: ${condition.condition_type}` };
    }
  }

  /**
   * Evaluate time-based conditions
   */
  private evaluateTimeCondition(config: any, context: PermissionEvaluationContext): ConditionEvaluationResult {
    // Handle null or undefined config
    if (!config) {
      return { passed: false, reason: 'Invalid time condition configuration' };
    }

    const now = context.timestamp || new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

    // Check time range
    if (config.start_time && config.end_time) {
      if (currentTime < config.start_time || currentTime > config.end_time) {
        return {
          passed: false,
          reason: `Access only allowed between ${config.start_time} and ${config.end_time}`
        };
      }
    }

    // Check allowed days
    if (config.days && Array.isArray(config.days)) {
      if (!config.days.includes(currentDay)) {
        return {
          passed: false,
          reason: `Access only allowed on: ${config.days.join(', ')}`
        };
      }
    }

    // Check timezone if specified
    if (config.timezone) {
      // For now, we'll assume UTC. In production, you'd want proper timezone handling
      console.log(`Timezone condition: ${config.timezone}`);
    }

    return { passed: true };
  }

  /**
   * Evaluate location-based conditions
   */
  private evaluateLocationCondition(config: any, context: PermissionEvaluationContext): ConditionEvaluationResult {
    // Check IP ranges
    if (config.allowed_ip_ranges && Array.isArray(config.allowed_ip_ranges) && context.ipAddress) {
      const isAllowedIP = this.isIPInRanges(context.ipAddress, config.allowed_ip_ranges);
      if (!isAllowedIP) {
        return {
          passed: false,
          reason: 'Access not allowed from this IP address'
        };
      }
    }

    // Check allowed countries
    if (config.allowed_countries && Array.isArray(config.allowed_countries) && context.location?.country) {
      if (!config.allowed_countries.includes(context.location.country)) {
        return {
          passed: false,
          reason: `Access not allowed from country: ${context.location.country}`
        };
      }
    }

    // Check blocked countries
    if (config.blocked_countries && Array.isArray(config.blocked_countries) && context.location?.country) {
      if (config.blocked_countries.includes(context.location.country)) {
        return {
          passed: false,
          reason: `Access blocked from country: ${context.location.country}`
        };
      }
    }

    // Check VPN requirement
    if (config.require_vpn && !this.isVPNConnection(context)) {
      return {
        passed: false,
        reason: 'VPN connection required for this operation'
      };
    }

    return { passed: true };
  }

  /**
   * Evaluate amount-based conditions
   */
  private evaluateAmountCondition(config: any, context: PermissionEvaluationContext): ConditionEvaluationResult {
    if (context.amount === undefined) {
      return { passed: true }; // No amount to check
    }

    const amount = context.amount;

    // Check minimum amount
    if (config.min_amount !== undefined && amount < config.min_amount) {
      return {
        passed: false,
        reason: `Amount must be at least ${config.min_amount}`
      };
    }

    // Check maximum amount
    if (config.max_amount !== undefined && amount > config.max_amount) {
      return {
        passed: false,
        reason: `Amount cannot exceed ${config.max_amount}`
      };
    }

    // Check currency if specified
    if (config.currency && config.currency !== 'USD') {
      // For now, we assume USD. In production, you'd handle currency conversion
      console.log(`Currency condition: ${config.currency}`);
    }

    return { passed: true };
  }

  /**
   * Evaluate approval-based conditions
   */
  private evaluateApprovalCondition(config: any, context: PermissionEvaluationContext): ConditionEvaluationResult {
    if (!config.requires_approval) {
      return { passed: true };
    }

    // Check if approval threshold applies
    if (config.approval_threshold && context.amount !== undefined) {
      if (context.amount < config.approval_threshold) {
        return { passed: true }; // Below threshold, no approval needed
      }
    }

    // Check auto-approve threshold
    if (config.auto_approve_below && context.amount !== undefined) {
      if (context.amount < config.auto_approve_below) {
        return { passed: true }; // Auto-approved
      }
    }

    // Check if already approved
    if (context.approvalInfo?.approvalStatus === 'approved') {
      return { passed: true };
    }

    // Requires approval
    return {
      passed: false,
      reason: 'Approval required for this operation',
      requiresApproval: true,
      approverRoles: config.approver_roles || [],
      metadata: {
        threshold: config.approval_threshold,
        autoApproveBelow: config.auto_approve_below
      }
    };
  }

  /**
   * Evaluate device-based conditions
   */
  private evaluateDeviceCondition(config: any, context: PermissionEvaluationContext): ConditionEvaluationResult {
    const deviceType = context.deviceInfo?.type;

    // Check allowed device types
    if (config.allowed_device_types && Array.isArray(config.allowed_device_types) && deviceType) {
      if (!config.allowed_device_types.includes(deviceType)) {
        return {
          passed: false,
          reason: `Access not allowed from ${deviceType} devices`
        };
      }
    }

    // Check blocked device types
    if (config.blocked_device_types && Array.isArray(config.blocked_device_types) && deviceType) {
      if (config.blocked_device_types.includes(deviceType)) {
        return {
          passed: false,
          reason: `Access blocked from ${deviceType} devices`
        };
      }
    }

    // Check device registration requirement
    if (config.require_registered_device && !context.deviceInfo?.registered) {
      return {
        passed: false,
        reason: 'Device must be registered for this operation'
      };
    }

    return { passed: true };
  }

  /**
   * Evaluate session-based conditions
   */
  private evaluateSessionCondition(config: any, context: PermissionEvaluationContext): ConditionEvaluationResult {
    const sessionInfo = context.sessionInfo;

    // Check maximum session age
    if (config.max_session_age && sessionInfo?.sessionAge !== undefined) {
      if (sessionInfo.sessionAge > config.max_session_age) {
        return {
          passed: false,
          reason: 'Session too old, please re-authenticate'
        };
      }
    }

    // Check MFA requirement
    if (config.require_mfa && !sessionInfo?.mfaVerified) {
      return {
        passed: false,
        reason: 'Multi-factor authentication required'
      };
    }

    // Check fresh authentication requirement
    if (config.require_fresh_auth && !sessionInfo?.freshAuth) {
      return {
        passed: false,
        reason: 'Fresh authentication required for this operation'
      };
    }

    // Check idle time
    if (config.max_idle_time && sessionInfo?.lastActivity) {
      const currentTime = context.timestamp || new Date();
      const idleTime = (currentTime.getTime() - sessionInfo.lastActivity.getTime()) / 1000;
      if (idleTime > config.max_idle_time) {
        return {
          passed: false,
          reason: 'Session idle too long, please re-authenticate'
        };
      }
    }

    return { passed: true };
  }

  /**
   * Helper method to check if IP is in allowed ranges
   */
  private isIPInRanges(ip: string, ranges: string[]): boolean {
    // Simplified IP range checking - in production, use a proper IP library
    for (const range of ranges) {
      if (range.includes('/')) {
        // CIDR notation - simplified check
        const [network, mask] = range.split('/');
        if (ip.startsWith(network.split('.').slice(0, parseInt(mask) / 8).join('.'))) {
          return true;
        }
      } else if (ip === range) {
        return true;
      }
    }
    return false;
  }

  /**
   * Helper method to detect VPN connections
   */
  private isVPNConnection(context: PermissionEvaluationContext): boolean {
    // Simplified VPN detection - in production, use proper VPN detection service
    // This could check for known VPN IP ranges, headers, etc.
    return context.userAgent?.includes('VPN') || false;
  }

  /**
   * Get all conditions for a permission
   * @param permissionCode Permission code
   * @returns Promise<any[]> Array of conditions
   */
  async getPermissionConditions(permissionCode: string): Promise<any[]> {
    try {
      const conditions = await db
        .select({
          id: permissionConditions.id,
          condition_type: permissionConditions.condition_type,
          condition_config: permissionConditions.condition_config,
          is_active: permissionConditions.is_active,
          priority: permissionConditions.priority,
          description: permissionConditions.description
        })
        .from(permissionConditions)
        .innerJoin(permissions, eq(permissionConditions.permission_id, permissions.id))
        .where(eq(permissions.code, permissionCode))
        .orderBy(permissionConditions.priority);

      return conditions;
    } catch (error) {
      console.error('Error getting permission conditions:', error);
      return [];
    }
  }
}

// Export singleton instance
export const conditionalPermissionService = new ConditionalPermissionService();
