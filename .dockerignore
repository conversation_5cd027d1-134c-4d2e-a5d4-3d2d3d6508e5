# Dependency directories
node_modules/
.pnpm-store/

# Build outputs
dist/
build/
out/
.next/

# Local environment files
# .env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Git related
.git/
.gitignore
.github/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Development assets
__tests__/
test/
tests/
coverage/
.coverage/
.nyc_output/

# Project specific
data/
!schema.sql
attached_assets/
replit_agent/