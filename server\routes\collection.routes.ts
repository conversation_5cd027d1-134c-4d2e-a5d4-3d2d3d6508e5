import { Express, Response } from 'express';
import { storage } from '../storage/index';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { requirePrefixSettings } from '../middleware/prefix-settings';
import { insertCollectionSchema, companyPrefixSettings, companies } from '@shared/schema';
import { ZodError } from 'zod';
import { db } from '../db';
import { eq } from 'drizzle-orm';

// Helper to get company name from company_id (fallback for prefix generation)
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);
    let prefix = '';

    if (words.length === 0) {
      prefix = 'COMP';
    } else if (words.length === 1) {
      // Single word: use first 2-3 characters
      const word = words[0].toUpperCase();
      prefix = word.length >= 3 ? word.substring(0, 3) : word;
    } else if (words.length === 2) {
      // Two words: use first 2 chars of each
      prefix = words[0].substring(0, 2).toUpperCase() + words[1].substring(0, 2).toUpperCase();
    } else {
      // Multiple words: use first char of first 3-4 words
      prefix = words.slice(0, Math.min(4, words.length))
        .map(word => word.charAt(0).toUpperCase())
        .join('');
    }

    // Ensure prefix is at least 2 characters and at most 4
    if (prefix.length < 2) {
      prefix = prefix.padEnd(2, 'X');
    } else if (prefix.length > 4) {
      prefix = prefix.substring(0, 4);
    }

    console.log(`Generated company prefix: ${prefix} for company ${companyId}`);
    return prefix;
  } catch (error) {
    console.error(`Error getting company name for company ${companyId}:`, error);
    return `COMP`;
  }
}

// Helper to get prefix from company_prefix_settings table
async function getPrefixFromSettings(companyId: number, entityType: 'loan' | 'collection' | 'customer' | 'partner' | 'agent'): Promise<{ prefix: string, startNumber: number }> {
  try {
    // Get company prefix settings
    const settings = await db.query.companyPrefixSettings.findFirst({
      where: eq(companyPrefixSettings.company_id, companyId),
    });

    if (!settings) {
      console.log(`No prefix settings found for company ${companyId}, falling back to company name`);
      // Fall back to company name-based prefix if no settings found
      const prefix = await getCompanyName(companyId);
      return { prefix, startNumber: 1 };
    }

    // Extract the appropriate prefix and start number based on entity type
    let prefix: string;
    let startNumber: number;

    switch (entityType) {
      case 'loan':
        prefix = settings.loan_prefix;
        startNumber = settings.loan_start_number;
        break;
      case 'collection':
        prefix = settings.collection_prefix;
        startNumber = settings.collection_start_number;
        break;
      case 'customer':
        prefix = settings.customer_prefix;
        startNumber = settings.customer_start_number;
        break;
      case 'partner':
        prefix = settings.partner_prefix;
        startNumber = settings.partner_start_number;
        break;
      case 'agent':
        prefix = settings.agent_prefix;
        startNumber = settings.agent_start_number;
        break;
      default:
        throw new Error(`Unknown entity type: ${entityType}`);
    }

    console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for ${entityType}`);
    return { prefix, startNumber };
  } catch (error) {
    console.error(`Error fetching prefix settings for company ${companyId}:`, error);
    // Fall back to company name-based prefix if error occurs
    const prefix = await getCompanyName(companyId);
    return { prefix, startNumber: 1 };
  }
}

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

export function registerCollectionRoutes(app: Express): void {
  // Get all collections for a company with pagination and search
  app.get('/api/companies/:companyId/collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log("Collections API called with query params:", req.query);
      }

      const companyId = parseInt(req.params.companyId);

      // Parse query parameters
      const status = req.query.status as string | undefined;
      const agentId = req.query.agent_id ? parseInt(req.query.agent_id as string) : undefined;
      const startDate = req.query.start_date as string | undefined;
      const endDate = req.query.end_date as string | undefined;
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const searchTerm = req.query.search as string | undefined;

      // Create date range object if both start and end dates are provided
      const dateRange = (startDate && endDate) ? { startDate, endDate } : undefined;

      // Check if branch_id filter is provided
      const branchId = req.query.branch_id ? parseInt(req.query.branch_id as string) : undefined;

      // Use the new paginated method if no branch_id is specified
      if (!branchId) {
        console.log(`Fetching paginated collections for company ${companyId} with params:`, {
          page,
          limit,
          status,
          searchTerm,
          agentId,
          dateRange
        });

        const result = await storage.getPaginatedCollections(companyId, {
          page,
          limit,
          status,
          searchTerm,
          agentId,
          dateRange
        });

        console.log(`Found ${result.collections.length} collections out of ${result.totalCount} total`);

        // Set cache control headers to prevent caching
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');

        return res.json({
          collections: result.collections,
          pagination: {
            page,
            limit,
            totalCount: result.totalCount,
            totalPages: Math.ceil(result.totalCount / limit)
          }
        });
      } else {
        // For branch-specific collections, we still use the old method
        // This could be updated in the future to support pagination for branch collections as well
        let collections = await storage.getCollectionsByBranch(branchId, status, agentId, dateRange);
        // Filter by company_id as well for security
        collections = collections.filter(collection => collection.company_id === companyId);

        // Set cache control headers to prevent caching
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');

        return res.json({
          collections,
          pagination: {
            page: 1,
            limit: collections.length,
            totalCount: collections.length,
            totalPages: 1
          }
        });
      }
    } catch (error) {
      console.error('Error fetching collections:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get collections for a loan
  app.get('/api/loans/:loanId/collections', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.loanId);

      // Get the loan to check company access
      const loan = await storage.getLoan(loanId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }

      const collections = await storage.getCollectionsByLoan(loanId, loan.company_id);
      return res.json(collections);
    } catch (error) {
      console.error(`Error fetching collections for loan ${req.params.loanId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get collections for an agent
  app.get('/api/agents/:agentId/collections', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const agentId = parseInt(req.params.agentId);

      // Parse query parameters
      const status = req.query.status as string | undefined;
      const startDate = req.query.start_date as string | undefined;
      const endDate = req.query.end_date as string | undefined;

      // Create date range object if both start and end dates are provided
      const dateRange = (startDate && endDate) ? { startDate, endDate } : undefined;

      // For now, we'll skip agent validation since getAgent is not implemented
      // TODO: Implement proper agent validation when agent storage is available
      const agent = { company_id: companyId };

      // Check if user has access to this agent's company
      if (req.user!.role !== 'saas_admin' && agent.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === agent.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this agent' });
        }
      }

      const collections = await storage.getCollectionsByAgent(agentId, agent.company_id, status, dateRange);
      return res.json(collections);
    } catch (error) {
      console.error(`Error fetching collections for agent ${req.params.agentId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get collection by ID
  app.get('/api/collections/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const collectionId = parseInt(req.params.id);

      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Check if user has access to this collection's company
      if (req.user!.role !== 'saas_admin' && collection.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === collection.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this collection' });
        }
      }

      return res.json(collection);
    } catch (error) {
      console.error(`Error fetching collection ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Check if a collection can be completed
  app.get('/api/collections/:id/can-complete', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const collectionId = parseInt(req.params.id);

      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Check if user has access to this collection's company
      if (req.user!.role !== 'saas_admin' && collection.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === collection.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this collection' });
        }
      }

      const completionStatus = await storage.canCompleteCollection(collectionId);
      return res.json(completionStatus);
    } catch (error) {
      console.error(`Error checking completion status for collection ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
  // Create collection
  app.post('/api/collections', authMiddleware, requirePrefixSettings, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);

      // Validate input
      const result = insertCollectionSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: formatZodError(result.error)
        });
      }

      // Check if user has access to the company
      const collectionCompanyId = result.data.company_id;
      if (req.user!.role !== 'saas_admin' && collectionCompanyId !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === collectionCompanyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Generate company-specific collection reference code
      const { prefix, startNumber } = await getPrefixFromSettings(collectionCompanyId, 'collection');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for collection`);

      // Get the highest existing collection reference code for this company
      const highestSerial = await storage.getHighestCollectionSerial(collectionCompanyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const collectionReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated collection reference code: ${collectionReferenceCode} for company ${collectionCompanyId}`);

      // Add the reference code to the collection data
      const collectionDataWithReferenceCode = {
        ...result.data,
        company_collection_string: collectionReferenceCode
      };

      const collection = await storage.createCollection(collectionDataWithReferenceCode);
      return res.status(201).json(collection);
    } catch (error) {
      console.error('Error creating collection:', error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Update collection
  app.put('/api/collections/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const collectionId = parseInt(req.params.id);

      // Get the collection to check company access
      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Check if user has access to this collection's company
      if (req.user!.role !== 'saas_admin' && collection.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === collection.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this collection' });
        }
      }

      // Validate input
      const result = insertCollectionSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: formatZodError(result.error)
        });
      }

      // Don't allow changing company_id, loan_id, or customer_id
      delete result.data.company_id;
      delete result.data.loan_id;
      delete result.data.customer_id;

      const updatedCollection = await storage.updateCollection(collectionId, collection.company_id, result.data);
      return res.json(updatedCollection);
    } catch (error) {
      console.error(`Error updating collection ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Update collection status
  app.patch('/api/collections/:id/status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const collectionId = parseInt(req.params.id);

      // Get the collection to check company access
      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Check if user has access to this collection's company
      if (req.user!.role !== 'saas_admin' && collection.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === collection.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this collection' });
        }
      }

      // Validate status
      const { status } = req.body;
      if (!status || typeof status !== 'string') {
        return res.status(400).json({ message: 'Status is required' });
      }

      // Check if status is valid
      const validStatuses = ['pending', 'in_progress', 'completed', 'failed', 'cancelled'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          message: 'Invalid status',
          validStatuses
        });
      }

      // If trying to mark as completed, check sequential completion restrictions
      if (status === 'completed') {
        const { canComplete, reason } = await storage.canCompleteCollection(collectionId);
        if (!canComplete) {
          return res.status(400).json({
            message: 'Collection completion restricted',
            reason: reason || 'Cannot complete this collection at this time'
          });
        }
      }

      const updatedCollection = await storage.updateCollectionStatus(collectionId, status);
      return res.json(updatedCollection);
    } catch (error) {
      console.error(`Error updating status for collection ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
  // Delete collection
  app.delete('/api/collections/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const collectionId = parseInt(req.params.id);

      // Get the collection to check company access
      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Check if user has access to this collection's company
      if (req.user!.role !== 'saas_admin' && collection.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === collection.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this collection' });
        }
      }

      // Use companyId from query parameter if provided, otherwise use from user context
      const companyIdToUse = req.query.companyId
        ? parseInt(req.query.companyId as string)
        : companyId;

      const success = await storage.deleteCollection(collectionId, companyIdToUse);
      if (!success) {
        return res.status(404).json({ message: 'Collection not found or could not be deleted' });
      }
      return res.json({ message: 'Collection deleted successfully' });
    } catch (error) {
      console.error(`Error deleting collection ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Mark multiple collections as completed
  app.post('/api/collections/mark-completed', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);

      // Validate input
      const { collectionIds } = req.body;
      if (!collectionIds || !Array.isArray(collectionIds) || collectionIds.length === 0) {
        return res.status(400).json({ message: 'Collection IDs are required' });
      }

      // Check if user has access to all collections
      for (const id of collectionIds) {
        const collection = await storage.getCollection(id);

        if (!collection) {
          return res.status(404).json({ message: `Collection with ID ${id} not found` });
        }

        if (req.user!.role !== 'saas_admin' && collection.company_id !== companyId) {
          const userCompanies = await storage.getUserCompanies(userId);
          const hasAccess = userCompanies.some(uc => uc.company_id === collection.company_id);

          if (!hasAccess) {
            return res.status(403).json({ message: `Access denied to collection with ID ${id}` });
          }
        }
      }

      await storage.markCollectionsAsCompleted(collectionIds);
      return res.json({ message: 'Collections marked as completed successfully' });
    } catch (error) {
      console.error('Error marking collections as completed:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
