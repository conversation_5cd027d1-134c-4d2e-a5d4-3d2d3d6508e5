import React from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { PermissionDashboard } from '@/components/permissions';
import { useAuth } from '@/lib/auth';

export default function PermissionsPage() {
  const [location, navigate] = useLocation();
  const { user } = useAuth();

  return (
    <div className="container mx-auto py-6">
      {/* Header with Back Button */}
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate('/user-management')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Permissions Management</h1>
          <p className="text-muted-foreground">
            Manage role permissions and access controls
          </p>
        </div>
      </div>

      <PermissionDashboard companyId={user?.company_id} />
    </div>
  );
}
