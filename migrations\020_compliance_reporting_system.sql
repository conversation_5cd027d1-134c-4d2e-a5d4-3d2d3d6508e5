-- Migration: Compliance Reporting System
-- Description: Create comprehensive compliance reporting and certification tables
-- Date: 2025-01-27

-- Create compliance framework enum
CREATE TYPE compliance_framework AS ENUM (
  'sox', 'gdpr', 'pci_dss', 'hipaa', 'iso27001', 'nist', 'coso', 'cobit', 'custom'
);

-- Create compliance status enum
CREATE TYPE compliance_status AS ENUM (
  'compliant', 'non_compliant', 'partially_compliant', 'under_review', 'not_assessed'
);

-- Create certification status enum
CREATE TYPE certification_status AS ENUM (
  'pending', 'in_progress', 'approved', 'rejected', 'expired', 'cancelled'
);

-- Create risk level enum
CREATE TYPE risk_level AS ENUM (
  'very_low', 'low', 'medium', 'high', 'very_high', 'critical'
);

-- Compliance frameworks table
CREATE TABLE IF NOT EXISTS "compliance_frameworks" (
  "id" SERIAL PRIMARY KEY,
  "framework_code" varchar(50) NOT NULL UNIQUE,
  "framework_name" varchar(255) NOT NULL,
  "framework_type" compliance_framework NOT NULL,
  "description" text,
  "version" varchar(50),
  "effective_date" date,
  "is_active" boolean DEFAULT true,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Compliance requirements table
CREATE TABLE IF NOT EXISTS "compliance_requirements" (
  "id" SERIAL PRIMARY KEY,
  "framework_id" integer REFERENCES "compliance_frameworks"("id") ON DELETE CASCADE,
  "requirement_code" varchar(100) NOT NULL,
  "requirement_name" varchar(255) NOT NULL,
  "description" text,
  "category" varchar(100),
  "subcategory" varchar(100),
  "control_type" varchar(50), -- 'preventive', 'detective', 'corrective'
  "risk_level" risk_level DEFAULT 'medium',
  "frequency" varchar(50), -- 'daily', 'weekly', 'monthly', 'quarterly', 'annually'
  "automated_check" boolean DEFAULT false,
  "check_query" text, -- SQL query for automated checks
  "remediation_guidance" text,
  "is_active" boolean DEFAULT true,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE("framework_id", "requirement_code")
);

-- Compliance assessments table
CREATE TABLE IF NOT EXISTS "compliance_assessments" (
  "id" SERIAL PRIMARY KEY,
  "assessment_id" varchar(255) NOT NULL UNIQUE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  "framework_id" integer REFERENCES "compliance_frameworks"("id") ON DELETE CASCADE,
  "requirement_id" integer REFERENCES "compliance_requirements"("id") ON DELETE CASCADE,
  "assessed_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Assessment details
  "assessment_date" date NOT NULL,
  "assessment_period_start" date,
  "assessment_period_end" date,
  "status" compliance_status NOT NULL,
  "score" integer, -- 0-100 compliance score
  "risk_rating" risk_level,
  
  -- Assessment results
  "findings" text,
  "evidence" jsonb DEFAULT '[]', -- Array of evidence documents/links
  "gaps_identified" jsonb DEFAULT '[]', -- Array of compliance gaps
  "remediation_actions" jsonb DEFAULT '[]', -- Array of required actions
  "remediation_deadline" date,
  "remediation_owner" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Review and approval
  "reviewed_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "reviewed_at" timestamp,
  "approved_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "approved_at" timestamp,
  
  -- Additional context
  "metadata" jsonb DEFAULT '{}',
  "attachments" jsonb DEFAULT '[]', -- Array of attachment references
  "next_assessment_due" date,
  
  -- Audit trail
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Access certifications table for access review workflows
CREATE TABLE IF NOT EXISTS "access_certifications" (
  "id" SERIAL PRIMARY KEY,
  "certification_id" varchar(255) NOT NULL UNIQUE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  "initiated_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Certification scope
  "certification_type" varchar(50) NOT NULL, -- 'user_access', 'role_access', 'permission_access', 'data_access'
  "scope_description" text,
  "target_user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "target_role_id" integer REFERENCES "custom_roles"("id") ON DELETE SET NULL,
  "target_permissions" jsonb DEFAULT '[]', -- Array of permission codes
  "target_resources" jsonb DEFAULT '[]', -- Array of resource identifiers
  
  -- Certification workflow
  "status" certification_status NOT NULL DEFAULT 'pending',
  "due_date" date NOT NULL,
  "reminder_sent" boolean DEFAULT false,
  "escalation_level" integer DEFAULT 0,
  
  -- Reviewer information
  "primary_reviewer" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "secondary_reviewer" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "manager_reviewer" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Review results
  "review_started_at" timestamp,
  "review_completed_at" timestamp,
  "certification_decision" varchar(50), -- 'approve', 'revoke', 'modify', 'escalate'
  "reviewer_comments" text,
  "justification" text,
  "risk_assessment" risk_level,
  
  -- Actions taken
  "actions_required" jsonb DEFAULT '[]', -- Array of required actions
  "actions_completed" jsonb DEFAULT '[]', -- Array of completed actions
  "permissions_revoked" jsonb DEFAULT '[]', -- Array of revoked permissions
  "permissions_modified" jsonb DEFAULT '[]', -- Array of modified permissions
  
  -- Compliance context
  "compliance_framework" varchar(50),
  "regulatory_requirement" varchar(100),
  "business_justification" text,
  
  -- Additional context
  "metadata" jsonb DEFAULT '{}',
  "evidence_provided" jsonb DEFAULT '[]', -- Array of evidence documents
  
  -- Audit trail
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Compliance violations table
CREATE TABLE IF NOT EXISTS "compliance_violations" (
  "id" SERIAL PRIMARY KEY,
  "violation_id" varchar(255) NOT NULL UNIQUE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  "framework_id" integer REFERENCES "compliance_frameworks"("id") ON DELETE CASCADE,
  "requirement_id" integer REFERENCES "compliance_requirements"("id") ON DELETE CASCADE,
  "detected_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Violation details
  "violation_type" varchar(100) NOT NULL,
  "severity" risk_level NOT NULL,
  "detected_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "description" text NOT NULL,
  "affected_systems" jsonb DEFAULT '[]', -- Array of affected systems/resources
  "affected_users" jsonb DEFAULT '[]', -- Array of affected user IDs
  "affected_data" jsonb DEFAULT '[]', -- Array of affected data types
  
  -- Detection context
  "detection_method" varchar(50), -- 'automated', 'manual', 'audit', 'incident'
  "detection_source" varchar(100), -- Source system or process
  "related_audit_logs" jsonb DEFAULT '[]', -- Array of related audit log IDs
  "related_security_events" jsonb DEFAULT '[]', -- Array of related security event IDs
  
  -- Impact assessment
  "business_impact" varchar(50), -- 'low', 'medium', 'high', 'critical'
  "financial_impact" numeric(15,2),
  "regulatory_impact" text,
  "reputation_impact" text,
  
  -- Response and remediation
  "status" varchar(50) DEFAULT 'open', -- 'open', 'investigating', 'remediating', 'resolved', 'closed'
  "assigned_to" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "response_plan" text,
  "remediation_actions" jsonb DEFAULT '[]', -- Array of remediation actions
  "remediation_deadline" date,
  "resolution_date" date,
  "resolution_summary" text,
  
  -- Reporting and notification
  "reported_to_regulator" boolean DEFAULT false,
  "regulator_reference" varchar(100),
  "reported_at" timestamp,
  "notification_sent" boolean DEFAULT false,
  "stakeholders_notified" jsonb DEFAULT '[]', -- Array of notified stakeholders
  
  -- Additional context
  "metadata" jsonb DEFAULT '{}',
  "attachments" jsonb DEFAULT '[]', -- Array of attachment references
  "lessons_learned" text,
  
  -- Audit trail
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_compliance_frameworks_type ON compliance_frameworks(framework_type);
CREATE INDEX idx_compliance_frameworks_active ON compliance_frameworks(is_active) WHERE is_active = true;

CREATE INDEX idx_compliance_requirements_framework ON compliance_requirements(framework_id);
CREATE INDEX idx_compliance_requirements_category ON compliance_requirements(category);
CREATE INDEX idx_compliance_requirements_risk ON compliance_requirements(risk_level);
CREATE INDEX idx_compliance_requirements_automated ON compliance_requirements(automated_check) WHERE automated_check = true;

CREATE INDEX idx_compliance_assessments_company ON compliance_assessments(company_id);
CREATE INDEX idx_compliance_assessments_framework ON compliance_assessments(framework_id);
CREATE INDEX idx_compliance_assessments_requirement ON compliance_assessments(requirement_id);
CREATE INDEX idx_compliance_assessments_date ON compliance_assessments(assessment_date);
CREATE INDEX idx_compliance_assessments_status ON compliance_assessments(status);
CREATE INDEX idx_compliance_assessments_due ON compliance_assessments(next_assessment_due);

CREATE INDEX idx_access_certifications_company ON access_certifications(company_id);
CREATE INDEX idx_access_certifications_status ON access_certifications(status);
CREATE INDEX idx_access_certifications_due ON access_certifications(due_date);
CREATE INDEX idx_access_certifications_reviewer ON access_certifications(primary_reviewer);
CREATE INDEX idx_access_certifications_target_user ON access_certifications(target_user_id);

CREATE INDEX idx_compliance_violations_company ON compliance_violations(company_id);
CREATE INDEX idx_compliance_violations_framework ON compliance_violations(framework_id);
CREATE INDEX idx_compliance_violations_severity ON compliance_violations(severity);
CREATE INDEX idx_compliance_violations_status ON compliance_violations(status);
CREATE INDEX idx_compliance_violations_detected ON compliance_violations(detected_at);

-- Add comments for documentation
COMMENT ON TABLE compliance_frameworks IS 'Regulatory and compliance frameworks (SOX, GDPR, etc.)';
COMMENT ON TABLE compliance_requirements IS 'Specific requirements within each compliance framework';
COMMENT ON TABLE compliance_assessments IS 'Periodic compliance assessments and their results';
COMMENT ON TABLE access_certifications IS 'Access review and certification workflows';
COMMENT ON TABLE compliance_violations IS 'Detected compliance violations and their remediation';

COMMENT ON COLUMN compliance_requirements.automated_check IS 'Whether this requirement can be checked automatically';
COMMENT ON COLUMN compliance_requirements.check_query IS 'SQL query for automated compliance checking';
COMMENT ON COLUMN compliance_assessments.score IS 'Compliance score from 0-100';
COMMENT ON COLUMN access_certifications.escalation_level IS 'Number of escalations for overdue certifications';
COMMENT ON COLUMN compliance_violations.detection_method IS 'How the violation was detected';
COMMENT ON COLUMN compliance_violations.business_impact IS 'Assessment of business impact level';
