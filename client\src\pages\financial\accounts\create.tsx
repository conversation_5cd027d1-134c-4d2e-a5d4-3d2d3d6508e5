import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useContextData } from '@/lib/useContextData';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// UI Components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Spinner } from '@/components/ui/spinner';
import { Textarea } from '@/components/ui/textarea';

// Icons
import { ArrowLeft, Save } from 'lucide-react';

// Schema for account creation/editing
const accountSchema = z.object({
  account_code: z.string().min(1, "Account code is required"),
  account_name: z.string().min(1, "Account name is required"),
  account_type: z.enum(['asset', 'liability', 'equity', 'income', 'expense'], {
    required_error: "Account type is required",
  }),
  parent_account_id: z.number().nullable(),
  is_active: z.boolean().default(true),
  description: z.string().optional(),
});

type AccountFormValues = z.infer<typeof accountSchema>;

// Types
interface Account {
  id: number;
  company_id: number;
  account_code: string;
  account_name: string;
  account_type: 'asset' | 'liability' | 'equity' | 'income' | 'expense';
  parent_account_id: number | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const CreateAccountPage = () => {
  const { companyId } = useContextData();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();

  // Fetch all accounts for parent selection
  const {
    data: accounts = [],
    isLoading: isLoadingAccounts
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'accounts'],
    queryFn: async () => {
      if (!companyId) return [];
      const response = await apiRequest('GET', `/api/companies/${companyId}/accounts`);
      return await response.json();
    },
    enabled: !!companyId
  });

  // Form setup
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      account_code: '',
      account_name: '',
      account_type: 'asset',
      parent_account_id: null,
      is_active: true,
      description: '',
    },
  });

  // Create account mutation
  const createMutation = useMutation({
    mutationFn: async (data: AccountFormValues) => {
      const response = await apiRequest('POST', `/api/companies/${companyId}/accounts`, {
        ...data,
        company_id: companyId,
      });
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Account created",
        description: "The account has been created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'accounts'] });
      navigate('/financial/accounts');
    },
    onError: (error: any) => {
      // Use the error message from the API response
      const errorMessage = error.message || "Failed to create account. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Form submission handler
  const onSubmit = (data: AccountFormValues) => {
    createMutation.mutate(data);
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex items-center mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/financial/accounts')}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
          </div>
          <CardTitle className="text-2xl font-bold">Create New Account</CardTitle>
          <CardDescription>
            Add a new account to your chart of accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Account Code */}
                <FormField
                  control={form.control}
                  name="account_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Code</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., 1000" {...field} />
                      </FormControl>
                      <FormDescription>
                        Unique identifier for this account
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Account Name */}
                <FormField
                  control={form.control}
                  name="account_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Cash" {...field} />
                      </FormControl>
                      <FormDescription>
                        Descriptive name for the account
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Account Type */}
                <FormField
                  control={form.control}
                  name="account_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="asset">Asset</SelectItem>
                          <SelectItem value="liability">Liability</SelectItem>
                          <SelectItem value="equity">Equity</SelectItem>
                          <SelectItem value="income">Income</SelectItem>
                          <SelectItem value="expense">Expense</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Determines how this account affects financial statements
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Parent Account */}
                <FormField
                  control={form.control}
                  name="parent_account_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Parent Account (Optional)</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(value === "null" ? null : parseInt(value))}
                        value={field.value?.toString() || ""}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="None (Top-level account)" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="null">None (Top-level account)</SelectItem>
                          {accounts.map((account: Account) => (
                            <SelectItem key={account.id} value={account.id.toString()}>
                              {account.account_code} - {account.account_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Make this a sub-account of another account
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter additional details about this account"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Active Status */}
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Account</FormLabel>
                      <FormDescription>
                        Inactive accounts won't appear in transaction forms
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <Button
            variant="outline"
            onClick={() => navigate('/financial/accounts')}
          >
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={createMutation.isPending}
          >
            {createMutation.isPending ? (
              <>
                <Spinner className="mr-2 h-4 w-4" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Create Account
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CreateAccountPage;