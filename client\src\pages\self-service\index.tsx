import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  Plus, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Shield,
  User,
  FileText,
  Activity
} from "lucide-react";
import { useAuth } from "@/lib/auth";
import { useLocation } from "wouter";

interface AccessStatusSummary {
  totalPermissions: number;
  activePermissions: number;
  temporaryPermissions: number;
  pendingRequests: number;
  recentChanges: number;
  riskScore: number;
  complianceStatus: string;
  lastAccessReview?: Date;
  nextAccessReview?: Date;
}

interface PermissionRequest {
  id: number;
  request_id: string;
  title: string;
  request_type: string;
  status: string;
  priority: string;
  submitted_at: string;
  business_justification: string;
  requested_permissions?: string[];
}

export default function SelfServicePortal() {
  const { getCurrentUser } = useAuth();
  const [, navigate] = useLocation();
  const user = getCurrentUser();
  const [activeTab, setActiveTab] = useState("dashboard");

  // Fetch access status summary
  const { data: accessStatus, isLoading: statusLoading } = useQuery<AccessStatusSummary>({
    queryKey: ['/api/self-service/access-status'],
    enabled: !!user?.id,
  });

  // Fetch recent requests
  const { data: requestsData, isLoading: requestsLoading } = useQuery<{
    requests: PermissionRequest[];
    totalCount: number;
  }>({
    queryKey: ['/api/self-service/requests?limit=10'],
    enabled: !!user?.id,
  });

  // Fetch current permissions
  const { data: currentPermissions, isLoading: permissionsLoading } = useQuery({
    queryKey: ['/api/self-service/current-permissions'],
    enabled: !!user?.id,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskScoreColor = (score: number) => {
    if (score >= 70) return 'text-red-600';
    if (score >= 50) return 'text-orange-600';
    if (score >= 30) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Self-Service Portal</h1>
        <Button 
          onClick={() => navigate('/self-service/request')}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          New Request
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="requests" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            My Requests
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            My Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          {/* Access Status Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statusLoading ? '...' : accessStatus?.totalPermissions || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Active: {accessStatus?.activePermissions || 0}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statusLoading ? '...' : accessStatus?.pendingRequests || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Awaiting approval
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${getRiskScoreColor(accessStatus?.riskScore || 0)}`}>
                  {statusLoading ? '...' : accessStatus?.riskScore || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {accessStatus?.complianceStatus || 'Unknown'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recent Changes</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statusLoading ? '...' : accessStatus?.recentChanges || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Last 30 days
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Requests */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Requests</CardTitle>
            </CardHeader>
            <CardContent>
              {requestsLoading ? (
                <div className="text-center py-4">Loading...</div>
              ) : requestsData?.requests?.length ? (
                <div className="space-y-4">
                  {requestsData.requests.slice(0, 5).map((request) => (
                    <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{request.title}</h4>
                          <Badge className={getStatusColor(request.status)}>
                            {request.status}
                          </Badge>
                          <Badge className={getPriorityColor(request.priority)}>
                            {request.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {request.business_justification}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Submitted: {new Date(request.submitted_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => navigate(`/self-service/requests/${request.id}`)}
                      >
                        View
                      </Button>
                    </div>
                  ))}
                  {requestsData.requests.length > 5 && (
                    <div className="text-center pt-4">
                      <Button 
                        variant="outline"
                        onClick={() => setActiveTab('requests')}
                      >
                        View All Requests
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No requests found</p>
                  <Button 
                    className="mt-4"
                    onClick={() => navigate('/self-service/request')}
                  >
                    Submit Your First Request
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requests" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>My Permission Requests</CardTitle>
            </CardHeader>
            <CardContent>
              {requestsLoading ? (
                <div className="text-center py-4">Loading...</div>
              ) : requestsData?.requests?.length ? (
                <div className="space-y-4">
                  {requestsData.requests.map((request) => (
                    <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{request.title}</h4>
                          <Badge className={getStatusColor(request.status)}>
                            {request.status}
                          </Badge>
                          <Badge className={getPriorityColor(request.priority)}>
                            {request.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Type: {request.request_type.replace('_', ' ')}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {request.business_justification}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Submitted: {new Date(request.submitted_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => navigate(`/self-service/requests/${request.id}`)}
                        >
                          View Details
                        </Button>
                        {request.status === 'pending' && (
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => {
                              // TODO: Implement cancel request
                              console.log('Cancel request:', request.id);
                            }}
                          >
                            Cancel
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No requests found</p>
                  <Button 
                    className="mt-4"
                    onClick={() => navigate('/self-service/request')}
                  >
                    Submit Your First Request
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Current Permissions</CardTitle>
            </CardHeader>
            <CardContent>
              {permissionsLoading ? (
                <div className="text-center py-4">Loading...</div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Role</h4>
                      <Badge variant="outline" className="text-sm">
                        {currentPermissions?.role || 'No role assigned'}
                      </Badge>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Direct Permissions</h4>
                      <div className="flex flex-wrap gap-2">
                        {currentPermissions?.permissions?.length ? (
                          currentPermissions.permissions.map((permission: string) => (
                            <Badge key={permission} variant="secondary" className="text-xs">
                              {permission}
                            </Badge>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground">No direct permissions</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-2">Temporary Permissions</h4>
                    <p className="text-sm text-muted-foreground">
                      {accessStatus?.temporaryPermissions || 0} temporary permissions active
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
