Create a comprehensive Collections Management module for the TrackFina Loan Management System, focusing on daily and weekly loan tracking with an intuitive dashboard interface. The current user is "VetrivelDevOps" and the current date is 2025-05-05.

1. Collections Dashboard View:
   - Main dashboard with filterable time periods: Today, This Week, This Month, Custom Date Range
   - Status summary cards showing:
     * Total collections due today (with amount)
     * Total collections due this week (with amount)
     * Total overdue collections (with aging breakdown: 1-7 days, 8-30 days, 30+ days)
     * Collection efficiency percentage (collected vs. due)
   - Interactive heatmap calendar showing collection density by day
   - Daily collection tracker showing hour-by-hour collection progress
   - Collection performance trends with day-over-day and week-over-week comparisons

2. EMI Collection Management:
   - Daily collections view with:
     * Chronological list of today's collections
     * Time-based grouping (morning, afternoon, evening collections)
     * Status indicators (pending, collected, missed)
     * Quick action buttons for each collection (mark as collected, reschedule, add notes)
   - Weekly collections view with:
     * Day-wise breakdown of collections
     * Visual indicators for high-volume collection days
     * Special highlighting for collection days approaching deadlines
   - Collection task prioritization:
     * Urgent (today's collections)
     * Important (overdue collections)
     * Upcoming (next 3 days)
     * Scheduled (future dates)

3. Collection Processing Interface:
   - Quick collection entry form:
     * Customer selection (autocomplete from existing data)
     * Loan selection (for customers with multiple loans)
     * Amount collected input
     * Collection date/time with default to current
     * Collection method (cash, bank transfer, UPI, etc.)
     * Receipt number and collection proof upload
   - Partial payment handling:
     * Record partial amounts
     * Calculate remaining balance
     * Schedule follow-up for remaining amount
     * Apply penalties if applicable
   - Bulk collection processing for multiple EMIs/customers

4. Loan Configuration & Calculation:
   - Loan setup interface with:
     * Principal amount input (with Rs. currency format)
     * Loan duration selection with toggles for:
       > Daily payments (specify number of days)
       > Weekly payments (specify number of weeks)
       > Monthly payments (specify number of months)
     * Commission structure with radio selection:
       > Percentage-based (e.g., 10% of principal)
       > Fixed amount (e.g., Rs. 10,000)
     * EMI scheduling options:
       > For daily: Select time window (morning/afternoon/evening)
       > For weekly: Select day of week (Monday through Sunday)
       > For monthly: Select date of month (1-31)
     * Start date selection with calendar picker
   - Real-time calculation showing:
     * Principal amount
     * Commission amount
     * Net disbursement amount (principal minus commission)
     * EMI amount calculation
     * Total repayment amount
     * Interest amount if applicable

5. Amortization Schedule Generator:
   - Visual timeline of all payments
   - Detailed breakdown for each payment:
     * Payment number
     * Due date
     * Principal component
     * Interest component (if applicable)
     * Running balance
     * Status (pending, paid, overdue)
   - Schedule adjustment tools:
     * Holiday/weekend adjustment options
     * Special date handling (skip or adjust dates)
     * Custom schedule modifications
   - Export options for schedule (PDF, CSV, Excel)

6. Collection Analytics & Reporting:
   - Daily collection summary report
   - Weekly collection efficiency chart
   - EMI tracking progress bar for each loan
   - Collection performance by time of day (to optimize collection timing)
   - Trends analysis showing collection patterns
   - Geographical heatmap of collections (if location data available)

7. Collection Agent Tools:
   - Daily collection route optimization
   - Mobile-first interface for field collections
   - Offline capability with sync when online
   - Quick receipt generation with sharing options
   - Collection territory management with maps integration

8. Technical Requirements:
   - Implement using React with TypeScript
   - Use shadcn/ui components for consistent design
   - Implement responsive layouts with mobile-first approach
   - Add skeleton loaders for data loading states
   - Include proper form validations for all inputs
   - Use React Context API for state management
   - Implement optimistic UI updates for better user experience

9. Mock Data Setup:
   - Generate sample loans with various:
     * Principal amounts (Rs. 10,000 to Rs. 500,000)
     * Duration periods (7 days to 52 weeks)
     * Commission structures (5%-15% or Rs. 1,000 to Rs. 50,000)
     * Payment frequencies (daily, weekly, monthly)
     * Start dates (past and future)
   - Create realistic collection scenarios:
     * On-time payments
     * Delayed payments
     * Partial payments
     * Missed payments
     * Rescheduled payments

10. User Experience Considerations:
    - Visual distinction between collection statuses using color coding
    - Intuitive navigation between dashboard, collections list, and loan details
    - Quick action shortcuts for common tasks
    - Notification system for upcoming and overdue collections
    - Progressive disclosure of complex information
    - Tooltips for explaining calculations and terms

Note: Focus implementation on Collections Management functionality since Customer Search & Management is already implemented. Ensure all interfaces prioritize daily and weekly loan tracking as specified.