import React, { useState, useEffect } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/lib/auth";
import { useContextData } from "@/lib/useContextData";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Spinner } from "@/components/ui/spinner";
import { Switch } from "@/components/ui/switch";
import { format, addDays, addMonths, addWeeks } from "date-fns";

// Define schema for the loan form
const loanFormSchema = z.object({
  customer_id: z.union([
    z.number({
      required_error: "Customer is required",
    }),
    z.string().transform(val => parseInt(val, 10))
  ]),
  amount: z.union([
    z.number({
      required_error: "Loan amount is required",
    }).min(1, {
      message: "Amount must be greater than 0",
    }),
    z.string().transform(val => parseFloat(val))
  ]),
  interest_rate: z.union([
    z.number({
      required_error: "Interest rate is required",
    }).min(0, {
      message: "Interest rate must be equal to or greater than 0",
    }),
    z.string().transform(val => parseFloat(val))
  ]),
  interest_type: z.enum(["flat", "reducing", "upfront"], {
    required_error: "Interest type is required",
  }),
  loan_type: z.enum(["personal", "business", "education", "mortgage", "vehicle", "other"], {
    required_error: "Loan type is required",
  }),
  term: z.union([
    z.number({
      required_error: "Loan term is required",
    }).min(1, {
      message: "Term must be at least 1",
    }),
    z.string().transform(val => parseInt(val, 10))
  ]),
  terms_frequency: z.enum(["daily", "weekly", "biweekly", "monthly"], {
    required_error: "Term frequency is required",
  }),
  is_upfront_interest: z.boolean().default(false),
  start_date: z.string({
    required_error: "Start date is required",
  }),
  end_date: z.string({
    required_error: "End date is required",
  }),
  payment_frequency: z.enum(["daily", "weekly", "biweekly", "monthly"], {
    required_error: "Payment frequency is required",
  }),
  notes: z.string().optional(),
});

type LoanFormInput = z.infer<typeof loanFormSchema>;

interface SimpleStaticLoanFormProps {
  customerId?: number;
  initialData?: Partial<LoanFormInput> & { id?: number; company_id?: number };
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
}

export default function SimpleStaticLoanForm({
  customerId,
  initialData,
  onSuccess,
  onCancel
}: SimpleStaticLoanFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCalculatingEndDate, setIsCalculatingEndDate] = useState(false);
  const [isEditMode] = useState(!!initialData);

  // Prepare initial values, ensuring proper type conversions
  const getInitialValues = () => {
    // Convert string number values to actual numbers
    let amount = initialData?.amount || 0;
    if (typeof amount === 'string') {
      amount = parseFloat(amount);
    }

    let interestRate = initialData?.interest_rate || 0;
    if (typeof interestRate === 'string') {
      interestRate = parseFloat(interestRate);
    }

    let term = initialData?.term || 1;
    if (typeof term === 'string') {
      term = parseInt(term, 10);
    }

    return {
      customer_id: customerId || initialData?.customer_id || 0,
      amount: amount,
      interest_rate: interestRate,
      interest_type: initialData?.interest_type || "flat",
      loan_type: initialData?.loan_type || "personal",
      term: term,
      terms_frequency: initialData?.terms_frequency || "monthly",
      is_upfront_interest: initialData?.is_upfront_interest || false,
      start_date: initialData?.start_date || format(new Date(), "yyyy-MM-dd"),
      end_date: initialData?.end_date || "",
      payment_frequency: initialData?.payment_frequency || "monthly",
      notes: initialData?.notes || "",
    };
  };

  // Initialize the form with initialData if provided
  const form = useForm<LoanFormInput>({
    resolver: zodResolver(loanFormSchema),
    defaultValues: getInitialValues(),
  });

  // Debug output and auto-fix validation issues
  useEffect(() => {
    console.log("Initial form data:", initialData);
    console.log("Form defaults:", form.formState.defaultValues);
    console.log("Missing payment_frequency?", initialData && !initialData.payment_frequency);

    // Add more extensive validation checking
    setTimeout(() => {
      console.log("Form state:", form.formState);
      console.log("Is form dirty?", form.formState.isDirty);
      console.log("Is form valid?", form.formState.isValid);
      console.log("Form errors:", form.formState.errors);

      // Fix the form values with the correct types
      const values = form.getValues();
      form.setValue('amount', parseFloat(values.amount as any));
      form.setValue('interest_rate', parseFloat(values.interest_rate as any));
      if (typeof values.term === 'string') {
        form.setValue('term', parseInt(values.term, 10));
      }

      // Force form validation again after fixing values
      form.trigger().then(isValid => {
        console.log("After fixing - Triggered validation result:", isValid);
        console.log("After fixing - Errors after trigger:", form.formState.errors);
        console.log("After fixing - Updated values:", form.getValues());
      });
    }, 1000);
  }, []);

  // Watch form values for automatic end date calculation
  const startDate = form.watch("start_date");
  const term = form.watch("term");
  const termsFrequency = form.watch("terms_frequency");

  // Calculate end date based on start date and term
  useEffect(() => {
    if (startDate && term && termsFrequency) {
      setIsCalculatingEndDate(true);
      try {
        const dateObj = new Date(startDate);
        let endDate: Date;

        switch (termsFrequency) {
          case "daily":
            endDate = addDays(dateObj, term);
            break;
          case "weekly":
            endDate = addWeeks(dateObj, term);
            break;
          case "biweekly":
            endDate = addWeeks(dateObj, term * 2);
            break;
          case "monthly":
            endDate = addMonths(dateObj, term);
            break;
          default:
            endDate = addMonths(dateObj, term);
        }

        form.setValue("end_date", format(endDate, "yyyy-MM-dd"));
      } catch (error) {
        console.error("Error calculating end date:", error);
      } finally {
        setIsCalculatingEndDate(false);
      }
    }
  }, [startDate, term, termsFrequency, form]);

  // Get company ID from context data
  const { companyId: contextCompanyId } = useContextData();
  const companyId = initialData?.company_id || contextCompanyId;

  // Create or update loan mutation
  const loanMutation = useMutation({
    mutationFn: async (data: LoanFormInput) => {
      // Ensure all numeric values are properly converted
      const amount = typeof data.amount === 'string' ? parseFloat(data.amount) : data.amount;
      const interestRate = typeof data.interest_rate === 'string' ? parseFloat(data.interest_rate) : data.interest_rate;
      const term = typeof data.term === 'string' ? parseInt(data.term, 10) : data.term;

      // Calculate the disbursed amount based on upfront interest
      const interestAmount = amount * (interestRate / 100);
      const disbursedAmount = data.is_upfront_interest
        ? amount - interestAmount
        : amount;
      const totalRepayable = data.is_upfront_interest
        ? amount
        : amount + interestAmount;
      const installmentAmount = totalRepayable / term;

      // Format dates consistently
      const startDate = data.start_date ?
        (typeof data.start_date === 'string' ? data.start_date : new Date(data.start_date).toISOString().split('T')[0]) :
        null;
      const endDate = data.end_date ?
        (typeof data.end_date === 'string' ? data.end_date : new Date(data.end_date).toISOString().split('T')[0]) :
        null;

      // Add calculated fields to the loan data with proper string conversions
      const loanData = {
        ...data,
        amount: amount.toString(),
        interest_rate: interestRate.toString(),
        term: term,
        company_id: companyId, // Add company_id explicitly to ensure authorization
        start_date: startDate,
        end_date: endDate,
        disbursed_amount: disbursedAmount.toString(),
        total_repayable: totalRepayable.toString(),
        installment_amount: installmentAmount.toString(),
      };

      console.log("Updating loan data:", loanData);

      // Use the correct HTTP method and endpoint based on edit/create mode
      const method = isEditMode && initialData?.id ? 'PATCH' : 'POST';
      const endpoint = isEditMode && initialData?.id ? `/api/loans/${initialData.id}` : '/api/loans';

      const response = await apiRequest(method, endpoint, loanData);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${isEditMode ? 'update' : 'create'} loan`);
      }

      return response.json();
    },
    onSuccess: (data) => {
      // Log success for debugging
      console.log("Loan successfully saved:", data);

      // Invalidate specific loan detail and parent collections
      if (initialData?.id) {
        // Invalidate loan detail page cache
        queryClient.invalidateQueries({ queryKey: [`/api/loans/${initialData.id}`] });

        // Force fetching the updated loan data immediately
        queryClient.fetchQuery({ queryKey: [`/api/loans/${initialData.id}`] });

        // Invalidate payment schedules for this loan
        queryClient.invalidateQueries({ queryKey: [`/api/loans/${initialData.id}/payment-schedules`] });

        // Invalidate collections for this loan
        queryClient.invalidateQueries({ queryKey: [`/api/loans/${initialData.id}/collections`] });
      }

      // Invalidate company loans list
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });

      // Invalidate customer loans if customerId is provided
      if (customerId) {
        queryClient.invalidateQueries({ queryKey: [`/api/customers/${customerId}/loans`] });
      }

      // Invalidate general loans query
      queryClient.invalidateQueries({ queryKey: [`/api/loans`] });

      // Show success message
      toast({
        title: "Success",
        description: `Loan ${isEditMode ? 'updated' : 'created'} successfully`,
      });

      // Call the onSuccess callback if provided
      if (onSuccess) {
        onSuccess(data);
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create loan",
        variant: "destructive",
      });
    },
  });

  // Function to handle form submission
  const onSubmit = (data: LoanFormInput) => {
    // Get a copy of the data to submit and ensure terms_frequency matches payment_frequency
    const submissionData = {
      ...data,
      terms_frequency: data.payment_frequency
    };

    // Fix any potential validation issues with the data
    // Convert form values from strings to appropriate types
    if (typeof submissionData.amount === 'string') {
      submissionData.amount = parseFloat(submissionData.amount);
    }

    if (typeof submissionData.interest_rate === 'string') {
      submissionData.interest_rate = parseFloat(submissionData.interest_rate);
    }

    if (typeof submissionData.term === 'string') {
      submissionData.term = parseInt(submissionData.term, 10);
    }

    // Log the data being submitted
    console.log(`${isEditMode ? 'Updating' : 'Creating'} loan data:`, submissionData);

    loanMutation.mutate(submissionData);
  };

  // Function to handle form cancellation
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{isEditMode ? 'Edit Loan' : 'Create New Loan'}</CardTitle>
        <CardDescription>
          {isEditMode
            ? 'Edit the existing loan details'
            : 'Create a new loan for the selected customer'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {!customerId && (
              <FormField
                control={form.control}
                name="customer_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer ID</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter customer ID"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Loan Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter loan amount"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="interest_rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Interest Rate (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter interest rate"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="interest_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Interest Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select interest type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="flat">Flat</SelectItem>
                        <SelectItem value="reducing">Reducing</SelectItem>
                        <SelectItem value="upfront">Upfront</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="loan_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Loan Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select loan type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="personal">Personal</SelectItem>
                        <SelectItem value="business">Business</SelectItem>
                        <SelectItem value="education">Education</SelectItem>
                        <SelectItem value="mortgage">Mortgage</SelectItem>
                        <SelectItem value="vehicle">Vehicle</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="term"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Term</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter term"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="terms_frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Term Frequency</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select term frequency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="biweekly">Biweekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_upfront_interest"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between space-x-2 space-y-0 rounded-md border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Upfront Interest</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Interest deducted from principal
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date {isCalculatingEndDate && <Spinner className="inline w-4 h-4 ml-2" />}</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} readOnly />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="payment_frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Frequency</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Also update terms_frequency to match payment_frequency
                        // Set terms_frequency to match payment_frequency directly
                        // Since we've updated the terms_frequency enum to match payment_frequency
                        form.setValue("terms_frequency", value);
                      }}
                      defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment frequency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="biweekly">Bi-weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter additional details about this loan"
                      className="resize-none h-20"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              <Button variant="outline" type="button" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loanMutation.isPending}
                onClick={() => {
                  console.log("Form validation errors:", form.formState.errors);
                  console.log("Form is valid:", form.formState.isValid);
                  console.log("Submitting form data:", form.getValues());
                }}
              >
                {loanMutation.isPending ? <Spinner className="mr-2" /> : null}
                {isEditMode ? 'Update Loan' : 'Create Loan'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}