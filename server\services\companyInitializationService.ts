import { db } from '../db';
import { customRoles, rolePermissions, userRoles, users } from '../../shared/schema';
import { eq, and, isNull } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';

/**
 * Service for initializing new companies with proper system roles and default configurations
 */
export class CompanyInitializationService {
  
  /**
   * Initialize a new company with system roles and default configurations
   * @param companyId - The ID of the newly created company
   * @param ownerUserId - The ID of the user who will be the company owner
   */
  async initializeCompany(companyId: number, ownerUserId?: number): Promise<void> {
    try {
      console.log(`🏗️ [COMPANY_INIT] Initializing company ${companyId}`);

      // 1. Verify system roles exist
      await this.ensureSystemRolesExist();

      // 2. Create default company-specific roles
      await this.createDefaultCompanyRoles(companyId);

      // 3. Assign owner role to the creating user if provided
      if (ownerUserId) {
        await this.assignOwnerRole(companyId, ownerUserId);
      }

      console.log(`✅ [COMPANY_INIT] Successfully initialized company ${companyId}`);
    } catch (error) {
      errorLogger.logError(
        `Failed to initialize company ${companyId}`,
        error as Error,
        'company-initialization'
      );
      throw error;
    }
  }

  /**
   * Ensure all required system roles exist in the database
   * Only the 4 essential system roles are maintained
   */
  private async ensureSystemRolesExist(): Promise<void> {
    const requiredSystemRoles = [
      {
        name: 'System Administrator',
        description: 'Full system access with all permissions',
        permissions: 'ALL'
      },
      {
        name: 'Owner',
        description: 'Company owner with full administrative access and control',
        permissions: 'ALL'
      },
      {
        name: 'Employee',
        description: 'Standard employee access',
        permissions: ['customer_view', 'loan_view', 'transaction_view']
      },
      {
        name: 'Collection Agent',
        description: 'Collection and recovery specialist',
        permissions: ['collection_create', 'collection_edit', 'collection_view', 'customer_view', 'loan_view']
      }
    ];

    for (const roleData of requiredSystemRoles) {
      await this.ensureSystemRoleExists(roleData);
    }
  }

  /**
   * Ensure a specific system role exists
   */
  private async ensureSystemRoleExists(roleData: {
    name: string;
    description: string;
    permissions: string[] | 'ALL';
  }): Promise<void> {
    // Check if role already exists
    const [existingRole] = await db
      .select()
      .from(customRoles)
      .where(
        and(
          eq(customRoles.name, roleData.name),
          eq(customRoles.is_system, true),
          isNull(customRoles.company_id)
        )
      );

    if (existingRole) {
      // Role exists, check if it has permissions
      const [permissionCount] = await db
        .select({ count: db.$count() })
        .from(rolePermissions)
        .where(eq(rolePermissions.role_id, existingRole.id));

      if (permissionCount.count === 0) {
        // Role exists but has no permissions, assign them
        await this.assignRolePermissions(existingRole.id, roleData.permissions);
        console.log(`🔧 [SYSTEM_ROLE] Fixed permissions for "${roleData.name}"`);
      }
      return;
    }

    // Create the system role
    const [newRole] = await db
      .insert(customRoles)
      .values({
        name: roleData.name,
        description: roleData.description,
        company_id: null,
        is_system: true,
      })
      .returning();

    // Assign permissions
    await this.assignRolePermissions(newRole.id, roleData.permissions);
    
    console.log(`✅ [SYSTEM_ROLE] Created "${roleData.name}" with permissions`);
  }

  /**
   * Assign permissions to a role
   */
  private async assignRolePermissions(roleId: number, permissions: string[] | 'ALL'): Promise<void> {
    if (permissions === 'ALL') {
      // Assign all permissions
      const allPermissions = await db.query.permissions.findMany();
      for (const permission of allPermissions) {
        await db
          .insert(rolePermissions)
          .values({
            role_id: roleId,
            permission_id: permission.id,
          })
          .onConflictDoNothing();
      }
    } else {
      // Assign specific permissions
      const permissionRecords = await db.query.permissions.findMany({
        where: (permissions_table, { inArray }) => 
          inArray(permissions_table.code, permissions)
      });

      for (const permission of permissionRecords) {
        await db
          .insert(rolePermissions)
          .values({
            role_id: roleId,
            permission_id: permission.id,
          })
          .onConflictDoNothing();
      }
    }
  }

  /**
   * Create default company-specific roles
   */
  private async createDefaultCompanyRoles(companyId: number): Promise<void> {
    const defaultRoles = [
      {
        name: 'Admin',
        description: 'Company administrator with full company access',
        permissions: ['user_create', 'user_edit', 'user_view', 'role_create', 'role_edit', 'role_view']
      }
    ];

    for (const roleData of defaultRoles) {
      // Check if role already exists for this company
      const [existingRole] = await db
        .select()
        .from(customRoles)
        .where(
          and(
            eq(customRoles.name, roleData.name),
            eq(customRoles.company_id, companyId),
            eq(customRoles.is_system, false)
          )
        );

      if (existingRole) {
        console.log(`✅ [COMPANY_ROLE] "${roleData.name}" already exists for company ${companyId}`);
        continue;
      }

      // Create the company role
      const [newRole] = await db
        .insert(customRoles)
        .values({
          name: roleData.name,
          description: roleData.description,
          company_id: companyId,
          is_system: false,
        })
        .returning();

      // Assign permissions
      await this.assignRolePermissions(newRole.id, roleData.permissions);
      
      console.log(`✅ [COMPANY_ROLE] Created "${roleData.name}" for company ${companyId}`);
    }
  }

  /**
   * Assign the Owner system role to a user
   */
  private async assignOwnerRole(companyId: number, userId: number): Promise<void> {
    // Get the Owner system role
    const [ownerRole] = await db
      .select()
      .from(customRoles)
      .where(
        and(
          eq(customRoles.name, 'Owner'),
          eq(customRoles.is_system, true),
          isNull(customRoles.company_id)
        )
      );

    if (!ownerRole) {
      throw new Error('Owner system role not found');
    }

    // Check if user already has the Owner role
    const [existingAssignment] = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.user_id, userId),
          eq(userRoles.role_id, ownerRole.id)
        )
      );

    if (existingAssignment) {
      console.log(`✅ [OWNER_ROLE] User ${userId} already has Owner role`);
      return;
    }

    // Assign the Owner role
    await db
      .insert(userRoles)
      .values({
        user_id: userId,
        role_id: ownerRole.id,
      });

    console.log(`✅ [OWNER_ROLE] Assigned Owner role to user ${userId} for company ${companyId}`);
  }

  /**
   * Get system roles that should be available to all companies
   */
  async getSystemRoles(): Promise<any[]> {
    return await db
      .select()
      .from(customRoles)
      .where(
        and(
          eq(customRoles.is_system, true),
          isNull(customRoles.company_id)
        )
      );
  }
}

export const companyInitializationService = new CompanyInitializationService();
