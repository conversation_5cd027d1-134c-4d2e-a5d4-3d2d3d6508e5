/**
 * Date utility functions for loan calculations
 */

/**
 * Gets today's date in YYYY-MM-DD format (for HTML date inputs)
 */
export function getTodayDate(): string {
  const today = new Date();
  return today.toISOString().split('T')[0];
}

/**
 * Calculates the end date based on start date and term in months
 * @param startDate The start date (string in YYYY-MM-DD format or Date object)
 * @param termMonths The loan term in months
 * @returns The calculated end date in YYYY-MM-DD format
 */
export function calculateEndDate(startDate: string | Date, termMonths: number): string {
  // If no valid inputs, return empty string
  if (!startDate || !termMonths) {
    return '';
  }
  
  // Convert string date to Date object if needed
  const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
  
  // Create a new date by adding the months
  const endDate = new Date(startDateObj);
  endDate.setMonth(endDate.getMonth() + termMonths);
  
  // Format the date as YYYY-MM-DD
  return endDate.toISOString().split('T')[0];
}