import React, { useState, useEffect } from 'react';
import { useRoute } from 'wouter';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Clock, User, FileText, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/lib/auth';

interface PermissionRequest {
  id: string;
  requester_id: number;
  requested_permissions: string[];
  justification: string;
  status: 'pending' | 'approved' | 'rejected' | 'implemented';
  duration_hours?: number;
  created_at: string;
  reviewed_at?: string;
  reviewer_id?: number;
  reviewer_comments?: string;
}

export default function RequestDetailPage() {
  const [match, params] = useRoute('/self-service/requests/:id');
  const { user } = useAuth();
  const [request, setRequest] = useState<PermissionRequest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const requestId = params?.id;

  useEffect(() => {
    if (requestId) {
      fetchRequestDetails();
    }
  }, [requestId]);

  const fetchRequestDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/self-service/requests/${requestId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch request details');
      }

      const data = await response.json();
      setRequest(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'approved':
      case 'implemented':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
      case 'implemented':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading request details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Error Loading Request</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={fetchRequestDetails}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!request) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Request Not Found</h3>
              <p className="text-muted-foreground">The requested permission request could not be found.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => window.history.back()}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Requests
        </Button>
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Permission Request</h1>
            <p className="text-muted-foreground">Request ID: {request.id}</p>
          </div>
          <Badge className={`${getStatusColor(request.status)} flex items-center gap-1`}>
            {getStatusIcon(request.status)}
            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
          </Badge>
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Request Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Submitted</label>
                <p className="text-sm">{new Date(request.created_at).toLocaleString()}</p>
              </div>
              {request.duration_hours && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Duration</label>
                  <p className="text-sm">{request.duration_hours} hours</p>
                </div>
              )}
            </div>

            <Separator />

            <div>
              <label className="text-sm font-medium text-muted-foreground">Requested Permissions</label>
              <div className="mt-2 flex flex-wrap gap-2">
                {request.requested_permissions.map((permission, index) => (
                  <Badge key={index} variant="outline">
                    {permission}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-muted-foreground">Justification</label>
              <p className="mt-1 text-sm bg-muted p-3 rounded-md">{request.justification}</p>
            </div>
          </CardContent>
        </Card>

        {(request.reviewed_at || request.reviewer_comments) && (
          <Card>
            <CardHeader>
              <CardTitle>Review Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {request.reviewed_at && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Reviewed</label>
                  <p className="text-sm">{new Date(request.reviewed_at).toLocaleString()}</p>
                </div>
              )}

              {request.reviewer_comments && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Reviewer Comments</label>
                  <p className="mt-1 text-sm bg-muted p-3 rounded-md">{request.reviewer_comments}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
