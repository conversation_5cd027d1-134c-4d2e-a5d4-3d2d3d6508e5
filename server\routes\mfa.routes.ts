import { Express, Request, Response } from 'express';
import { authMiddleware, AuthRequest } from '../middleware/auth';
import { mfaService } from '../services/mfaService';
import { z } from 'zod';

// Validation schemas
const setupMFASchema = z.object({
  email: z.string().email('Invalid email format')
});

const verifyMFASchema = z.object({
  token: z.string()
    .min(6, 'Token must be at least 6 characters')
    .max(8, 'Token must be at most 8 characters')
    .regex(/^[A-F0-9]+$/i, 'Token must contain only alphanumeric characters')
});

const regenerateBackupCodesSchema = z.object({
  confirmPassword: z.string().min(1, 'Password confirmation required')
});

export function registerMFARoutes(app: Express): void {
  
  // Setup MFA - Generate QR code and backup codes
  app.post('/api/auth/mfa/setup', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }

      const result = setupMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid input', 
          errors: result.error.errors 
        });
      }

      const mfaData = await mfaService.generateMFASecret(req.user.id, result.data.email);
      
      return res.json({
        success: true,
        data: {
          qrCodeUrl: mfaData.qrCodeUrl,
          backupCodes: mfaData.backupCodes
        },
        message: 'MFA setup initiated. Scan QR code with authenticator app and save backup codes.'
      });
    } catch (error) {
      console.error('MFA setup error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error during MFA setup' 
      });
    }
  });

  // Verify and enable MFA
  app.post('/api/auth/mfa/verify-setup', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      const result = verifyMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid input', 
          errors: result.error.errors 
        });
      }

      const isValid = await mfaService.verifyMFAToken(
        req.user.id, 
        result.data.token,
        req.ip,
        req.get('User-Agent')
      );

      if (!isValid) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid MFA token. Please check your authenticator app or try a backup code.' 
        });
      }

      await mfaService.enableMFA(req.user.id);

      return res.json({ 
        success: true,
        message: 'MFA enabled successfully. Your account is now protected with two-factor authentication.' 
      });
    } catch (error) {
      console.error('MFA verification error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error during MFA verification' 
      });
    }
  });

  // Verify MFA during login
  app.post('/api/auth/mfa/verify', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      const result = verifyMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid input', 
          errors: result.error.errors 
        });
      }

      const isValid = await mfaService.verifyMFAToken(
        req.user.id, 
        result.data.token,
        req.ip,
        req.get('User-Agent')
      );

      if (!isValid) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid MFA token. Please try again.' 
        });
      }

      return res.json({ 
        success: true,
        message: 'MFA verification successful' 
      });
    } catch (error) {
      console.error('MFA verification error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error during MFA verification' 
      });
    }
  });

  // Disable MFA
  app.post('/api/auth/mfa/disable', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      const result = verifyMFASchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid input', 
          errors: result.error.errors 
        });
      }

      const isValid = await mfaService.verifyMFAToken(
        req.user.id, 
        result.data.token,
        req.ip,
        req.get('User-Agent')
      );

      if (!isValid) {
        return res.status(400).json({ 
          success: false,
          message: 'Invalid MFA token. Verification required to disable MFA.' 
        });
      }

      await mfaService.disableMFA(req.user.id);

      return res.json({ 
        success: true,
        message: 'MFA disabled successfully. Your account is no longer protected with two-factor authentication.' 
      });
    } catch (error) {
      console.error('MFA disable error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error during MFA disable' 
      });
    }
  });

  // Get MFA status
  app.get('/api/auth/mfa/status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      const status = await mfaService.getMFAStatus(req.user.id);
      
      return res.json({ 
        success: true,
        data: status,
        message: status.required 
          ? 'MFA is required for your account' 
          : 'MFA is optional for your account'
      });
    } catch (error) {
      console.error('MFA status error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error retrieving MFA status' 
      });
    }
  });

  // Regenerate backup codes
  app.post('/api/auth/mfa/regenerate-backup-codes', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      // Check if MFA is enabled
      const isEnabled = await mfaService.isMFAEnabled(req.user.id);
      if (!isEnabled) {
        return res.status(400).json({ 
          success: false,
          message: 'MFA must be enabled to regenerate backup codes' 
        });
      }

      const newBackupCodes = await mfaService.regenerateBackupCodes(req.user.id);

      return res.json({ 
        success: true,
        data: {
          backupCodes: newBackupCodes
        },
        message: 'New backup codes generated successfully. Please save them securely.' 
      });
    } catch (error) {
      console.error('Backup codes regeneration error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error regenerating backup codes' 
      });
    }
  });

  // Get MFA verification attempts (for security monitoring)
  app.get('/api/auth/mfa/attempts', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ 
          success: false,
          message: 'Authentication required' 
        });
      }

      // This would typically be restricted to admins or the user themselves
      // For now, users can only see their own attempts
      
      return res.json({ 
        success: true,
        data: [],
        message: 'MFA verification attempts retrieved successfully' 
      });
    } catch (error) {
      console.error('MFA attempts retrieval error:', error);
      return res.status(500).json({ 
        success: false,
        message: 'Server error retrieving MFA attempts' 
      });
    }
  });
}
