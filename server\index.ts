// Load environment variables from .env file
import dotenv from 'dotenv';
import path from 'path';
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
console.log('Current working directory:', process.cwd());
console.log('DATABASE_URL from env:', process.env.DATABASE_URL);

import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes/index";
import { setupVite, serveStatic, log } from "./vite";
import errorLogger from './utils/errorLogger';
import cookieParser from "cookie-parser";
import { noCacheMiddleware } from "./middleware/no-cache";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

// Disable ALL caching for financial data accuracy - apply to all routes
app.use(noCacheMiddleware);

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  console.log('About to register routes...');
  try {
    // Register API routes first
    const server = await registerRoutes(app);
    console.log('Routes registered successfully');

    // Global error handler to catch and handle all exceptions
    app.use((err: any, req: Request, res: Response, _next: NextFunction) => {
      // Log error with detailed information
      errorLogger.logError(
        `HTTP ${req.method} ${req.path} failed`,
        'express-global-error-handler',
        {
          error: err,
          body: req.body,
          query: req.query,
          params: req.params
        }
      );

      // Format standardized error response
      const errorResponse = errorLogger.formatErrorResponse(err);

      // Add request path for debugging
      errorResponse.path = req.path;

      // Ensure we have a valid status code
      const statusCode = errorResponse.status || 500;

      // Return structured error response to client
      res.status(statusCode).json({
        success: false,
        message: errorResponse.message || 'Internal server error',
        code: errorResponse.code || 'INTERNAL_ERROR',
        path: req.path
      });
    });

    // Add a catch-all route for API requests that weren't handled
    app.use('/api/*', (req, res) => {
      res.status(404).json({
        message: `API endpoint not found: ${req.originalUrl}`,
        code: 'API_ENDPOINT_NOT_FOUND'
      });
    });

    // importantly only setup vite in development and after
    // setting up all the other routes so the catch-all route
    // doesn't interfere with the other routes
    if (app.get("env") === "development") {
      await setupVite(app, server);
    } else {
      serveStatic(app);
    }
    // Use PORT from environment variables or default to 5000
    // this serves both the API and the client
    const port = process.env.PORT || 5000;
    server.listen({
      port: Number(port),
      host: "0.0.0.0",
      reusePort: true,
    }, () => {
      log(`serving on port ${port}`);
    });
  } catch (error) {
    console.error('Failed to register routes:', error);
    throw error;
  }
})();
