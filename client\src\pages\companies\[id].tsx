import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, ChevronLeft, Building2, Users, User, DollarSign, FileText, Clock, Plus } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogDes<PERSON>, Di<PERSON>Footer, Di<PERSON>Header, Di<PERSON>Title } from "@/components/ui/dialog";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { apiRequest } from "@/lib/queryClient";

// Company interface
interface Company {
  id: number;
  name: string;
  email?: string;
  contact_email?: string;
  phone?: string;
  contact_phone?: string;
  address?: string;
  website?: string;
  logo?: string;
  active: boolean;
  subscription_status?: string;
  created_at: string;
  updated_at: string;
}

// User interface
interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  company_id?: number;
}

// Customer interface
interface Customer {
  id: number;
  company_id: number;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// Collection interface
interface Collection {
  id: number;
  company_id: number;
  agent_id?: number;
  customer_id: number;
  amount: number;
  status: string;
  date: string;
  created_at: string;
  updated_at: string;
}

export default function CompanyDetail() {
  const params = useParams();
  const companyId = parseInt(params.id);
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();
  const queryClient = useQueryClient();
  const [isUserDialogOpen, setIsUserDialogOpen] = useState(false);
  const [userForm, setUserForm] = useState({
    username: "",
    email: "",
    full_name: "",
    password: "",
    confirmPassword: "",
    role: "employee",
    company_id: companyId
  });
  
  // Handle user form input changes
  const handleUserFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserForm(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle role selection
  const handleRoleChange = (value: string) => {
    setUserForm(prev => ({ ...prev, role: value }));
  };
  
  // Reset user form
  const resetUserForm = () => {
    setUserForm({
      username: "",
      email: "",
      full_name: "",
      password: "",
      confirmPassword: "",
      role: "employee",
      company_id: companyId
    });
  };
  
  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: (userData: typeof userForm) => {
      return apiRequest('POST', '/api/auth/register', userData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/users`] });
      toast({
        title: "User created",
        description: "The user has been added to the company successfully.",
      });
      setIsUserDialogOpen(false);
      resetUserForm();
    },
    onError: (error: any) => {
      toast({
        title: "Error creating user",
        description: error.message || "There was an error creating the user.",
        variant: "destructive",
      });
    }
  });
  
  // Fetch company from API
  const {
    data: company,
    isLoading: isLoadingCompany,
    isError: isErrorCompany,
    error: errorCompany
  } = useQuery<Company>({
    queryKey: [`/api/companies/${companyId}`],
    enabled: isAuthenticated() && !isNaN(companyId)
  });
  
  // Fetch company users from API
  const {
    data: users = [],
    isLoading: isLoadingUsers,
    isError: isErrorUsers,
  } = useQuery<User[]>({
    queryKey: [`/api/companies/${companyId}/users`],
    enabled: isAuthenticated() && !isNaN(companyId) && !!company
  });
  
  // Fetch company customers from API
  const {
    data: customers = [],
    isLoading: isLoadingCustomers,
    isError: isErrorCustomers,
  } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${companyId}/customers`],
    enabled: isAuthenticated() && !isNaN(companyId) && !!company
  });
  
  // Fetch company collections from API
  const {
    data: collections = [],
    isLoading: isLoadingCollections,
    isError: isErrorCollections,
  } = useQuery<Collection[]>({
    queryKey: [`/api/companies/${companyId}/collections`],
    enabled: isAuthenticated() && !isNaN(companyId) && !!company
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };
  
  // Get collection status class
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Check if any data is loading
  const isLoading = isLoadingCompany || isLoadingUsers || isLoadingCustomers || isLoadingCollections;

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-20">
        <Loader2 className="h-10 w-10 text-primary animate-spin" />
        <p className="mt-4 text-lg text-gray-600">Loading company details...</p>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Link href="/companies">
            <Button variant="outline" size="sm" className="gap-1">
              <ChevronLeft className="h-4 w-4" />
              Back to Companies
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className="pt-6 text-center py-10">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Company Not Found</h2>
            <p className="text-gray-500">The company you're looking for doesn't exist or has been removed.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Submit user form
  const handleSubmitUser = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Check if passwords match
    if (userForm.password !== userForm.confirmPassword) {
      toast({
        title: "Password error",
        description: "Passwords do not match.",
        variant: "destructive",
      });
      return;
    }
    
    // Proceed with submission if passwords match
    createUserMutation.mutate(userForm);
  };

  return (
    <div className="space-y-6">
      {/* Add User Dialog */}
      <Dialog open={isUserDialogOpen} onOpenChange={setIsUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user for {company.name}. This user will have access to this company's data.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitUser}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  name="full_name"
                  value={userForm.full_name}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  value={userForm.username}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={userForm.email}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={userForm.password}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={userForm.confirmPassword}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Select value={userForm.role} onValueChange={handleRoleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="owner">Owner</SelectItem>
                      <SelectItem value="employee">Company Employee</SelectItem>
                      <SelectItem value="agent">Agent</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  resetUserForm();
                  setIsUserDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createUserMutation.isPending}>
                {createUserMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create User"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/companies">
            <Button variant="outline" size="sm" className="gap-1">
              <ChevronLeft className="h-4 w-4" />
              Back to Companies
            </Button>
          </Link>
          
          <h1 className="text-2xl font-bold">{company.name}</h1>
          
          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
            (company.subscription_status || 'pending') === 'active' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {company.subscription_status || 'pending'}
          </span>
        </div>
        
        <div>
          <Button 
            className="gap-1"
            onClick={() => {
              // This is where we would open an edit company dialog
              toast({
                title: "Edit Company",
                description: "This feature will be implemented soon.",
              });
            }}
          >
            <Building2 className="h-4 w-4" />
            Edit Company
          </Button>
        </div>
      </div>
      
      {/* Company Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-6 w-6 mr-2 text-blue-600" />
              <span className="text-2xl font-bold">{users.length}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <User className="h-6 w-6 mr-2 text-green-600" />
              <span className="text-2xl font-bold">{customers.length}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Collections</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <DollarSign className="h-6 w-6 mr-2 text-purple-600" />
              <span className="text-2xl font-bold">{collections.length}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Created</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-6 w-6 mr-2 text-gray-600" />
              <span className="text-sm font-medium">{formatDate(company.created_at)}</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Company Details */}
      <Card>
        <CardHeader>
          <CardTitle>Company Details</CardTitle>
          <CardDescription>Basic information about the company</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Company Name</Label>
                <Input value={company.name} readOnly />
              </div>
              
              <div className="space-y-2">
                <Label>Email Address</Label>
                <Input value={company.email || ""} readOnly />
              </div>
              
              <div className="space-y-2">
                <Label>Phone Number</Label>
                <Input value={company.phone || ""} readOnly />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Address</Label>
                <Input value={company.address || ""} readOnly />
              </div>
              
              <div className="space-y-2">
                <Label>Subscription Status</Label>
                <Input 
                  value={company.subscription_status || 'pending'} 
                  className="capitalize" 
                  readOnly 
                />
              </div>
              
              <div className="space-y-2">
                <Label>Last Updated</Label>
                <Input value={formatDate(company.updated_at)} readOnly />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Tabbed content: Users, Customers, Collections */}
      <Card>
        <CardHeader>
          <CardTitle>Company Data</CardTitle>
          <CardDescription>Manage users, customers, and collections</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="users" className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>Users</span>
              </TabsTrigger>
              <TabsTrigger value="customers" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>Customers</span>
              </TabsTrigger>
              <TabsTrigger value="collections" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                <span>Collections</span>
              </TabsTrigger>
              <TabsTrigger value="reports" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>Reports</span>
              </TabsTrigger>
            </TabsList>
            
            {/* Users tab */}
            <TabsContent value="users">
              <div className="flex justify-between mb-4">
                <h3 className="text-lg font-medium">Users</h3>
                <Button 
                  size="sm" 
                  className="gap-1" 
                  onClick={() => setIsUserDialogOpen(true)}
                >
                  <Plus className="h-4 w-4" />
                  Add User
                </Button>
              </div>
              
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  
                  <TableBody>
                    {users.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center h-24 text-gray-500">
                          No users found for this company.
                        </TableCell>
                      </TableRow>
                    ) : (
                      users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">{user.id}</TableCell>
                          <TableCell>{user.full_name}</TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell className="capitalize">{user.role.replace('_', ' ')}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="sm">
                                Edit
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            
            {/* Customers tab */}
            <TabsContent value="customers">
              <div className="flex justify-between mb-4">
                <h3 className="text-lg font-medium">Customers</h3>
                <Button size="sm" className="gap-1">
                  <Plus className="h-4 w-4" />
                  Add Customer
                </Button>
              </div>
              
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  
                  <TableBody>
                    {customers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center h-24 text-gray-500">
                          No customers found for this company.
                        </TableCell>
                      </TableRow>
                    ) : (
                      customers.map((customer) => (
                        <TableRow key={customer.id}>
                          <TableCell className="font-medium">{customer.id}</TableCell>
                          <TableCell>{customer.name}</TableCell>
                          <TableCell>{customer.email}</TableCell>
                          <TableCell>{customer.phone}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="sm">
                                Edit
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            
            {/* Collections tab */}
            <TabsContent value="collections">
              <div className="flex justify-between mb-4">
                <h3 className="text-lg font-medium">Collections</h3>
                <Button size="sm" className="gap-1">
                  <Plus className="h-4 w-4" />
                  Add Collection
                </Button>
              </div>
              
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  
                  <TableBody>
                    {collections.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center h-24 text-gray-500">
                          No collections found for this company.
                        </TableCell>
                      </TableRow>
                    ) : (
                      collections.map((collection) => (
                        <TableRow key={collection.id}>
                          <TableCell className="font-medium">{collection.company_collection_string || `#${collection.id}`}</TableCell>
                          <TableCell>
                            {customers.find(c => c.id === collection.customer_id)?.name || 'Unknown Customer'}
                          </TableCell>
                          <TableCell>${collection.amount.toFixed(2)}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(collection.status)}`}>
                              {collection.status}
                            </span>
                          </TableCell>
                          <TableCell>{formatDate(collection.date)}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button variant="ghost" size="sm">
                                View
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            
            {/* Reports tab */}
            <TabsContent value="reports">
              <div className="py-8 text-center">
                <FileText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Reports Coming Soon</h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  We're working on comprehensive reporting features for companies.
                  Check back soon for detailed analytics and insights.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}