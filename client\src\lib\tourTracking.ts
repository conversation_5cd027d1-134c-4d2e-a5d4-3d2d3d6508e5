/**
 * Tour tracking utilities for managing user onboarding tours
 * Uses localStorage to persist tour completion status per user
 */

export interface TourStatus {
  userId: number;
  tourId: string;
  completed: boolean;
  completedAt?: string;
  skipped?: boolean;
}

const TOUR_STORAGE_KEY = 'user_tours_completed';

/**
 * Get all completed tours for the current user
 */
export function getCompletedTours(userId: number): string[] {
  try {
    const stored = localStorage.getItem(TOUR_STORAGE_KEY);
    if (!stored) return [];
    
    const allTours: TourStatus[] = JSON.parse(stored);
    return allTours
      .filter(tour => tour.userId === userId && (tour.completed || tour.skipped))
      .map(tour => tour.tourId);
  } catch (error) {
    console.error('Error reading tour completion status:', error);
    return [];
  }
}

/**
 * Check if a specific tour has been completed by the user
 */
export function isTourCompleted(userId: number, tourId: string): boolean {
  const completedTours = getCompletedTours(userId);
  return completedTours.includes(tourId);
}

/**
 * Mark a tour as completed for the current user
 */
export function markTourCompleted(userId: number, tourId: string, skipped: boolean = false): void {
  try {
    const stored = localStorage.getItem(TOUR_STORAGE_KEY);
    const allTours: TourStatus[] = stored ? JSON.parse(stored) : [];
    
    // Remove any existing entry for this user/tour combination
    const filteredTours = allTours.filter(
      tour => !(tour.userId === userId && tour.tourId === tourId)
    );
    
    // Add the new completion status
    const newTourStatus: TourStatus = {
      userId,
      tourId,
      completed: !skipped,
      skipped,
      completedAt: new Date().toISOString()
    };
    
    filteredTours.push(newTourStatus);
    
    localStorage.setItem(TOUR_STORAGE_KEY, JSON.stringify(filteredTours));
    
    if (import.meta.env.DEV) {
      console.log(`Tour ${tourId} marked as ${skipped ? 'skipped' : 'completed'} for user ${userId}`);
    }
  } catch (error) {
    console.error('Error saving tour completion status:', error);
  }
}

/**
 * Reset all tour completion status for a user (useful for testing)
 */
export function resetUserTours(userId: number): void {
  try {
    const stored = localStorage.getItem(TOUR_STORAGE_KEY);
    if (!stored) return;
    
    const allTours: TourStatus[] = JSON.parse(stored);
    const filteredTours = allTours.filter(tour => tour.userId !== userId);
    
    localStorage.setItem(TOUR_STORAGE_KEY, JSON.stringify(filteredTours));
    
    if (import.meta.env.DEV) {
      console.log(`Reset all tours for user ${userId}`);
    }
  } catch (error) {
    console.error('Error resetting tour status:', error);
  }
}

/**
 * Get tour completion statistics (useful for analytics)
 */
export function getTourStats(userId: number): { completed: number; skipped: number; total: number } {
  try {
    const stored = localStorage.getItem(TOUR_STORAGE_KEY);
    if (!stored) return { completed: 0, skipped: 0, total: 0 };
    
    const allTours: TourStatus[] = JSON.parse(stored);
    const userTours = allTours.filter(tour => tour.userId === userId);
    
    const completed = userTours.filter(tour => tour.completed).length;
    const skipped = userTours.filter(tour => tour.skipped).length;
    const total = userTours.length;
    
    return { completed, skipped, total };
  } catch (error) {
    console.error('Error getting tour stats:', error);
    return { completed: 0, skipped: 0, total: 0 };
  }
}
