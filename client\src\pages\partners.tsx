import { useState } from "react";
import { useAuth } from "@/lib/auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Helmet } from "react-helmet";
import { Plus, FileEdit, Trash2, MoreHorizontal, Briefcase, Users, DollarSign, Loader2 } from "lucide-react";
import { apiRequest, queryClient } from "@/lib/queryClient";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { format } from "date-fns";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

// Define country code constant
const COUNTRY_CODE = '+91';

// Partner schema
const partnerSchema = z.object({
  company_id: z.number(),
  name: z.string().min(1, { message: "Partner name is required" }).min(2, { message: "Name must be at least 2 characters" }),
  type: z.enum(["investor", "strategic", "technology", "distribution"]),
  email: z.string()
    .min(1, { message: "Email is required" })
    .refine(
      (value) => {
        // Email regex pattern for validation
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        return emailPattern.test(value);
      },
      { message: "Please enter a valid email address (e.g., <EMAIL>)" }
    ),
  phone: z.string()
    .min(1, { message: "Phone number is required" })
    .refine(
      (value) => {
        // Accept only format with country code followed by exactly 10 digits
        const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
        const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
        return pattern.test(value);
      },
      { message: `Phone number must be exactly 10 digits with ${COUNTRY_CODE} country code` }
    ),
  investment_amount: z.string()
    .refine(
      (value) => {
        // If empty, it's valid (since investment_amount is optional)
        if (!value) return true;

        // Check if it's a valid number with at most 2 decimal places
        return /^\d+(\.\d{1,2})?$/.test(value);
      },
      { message: "Please enter a valid amount (numbers only with up to 2 decimal places)" }
    )
    .optional()
    .or(z.literal("")),
  partnership_start_date: z.string().min(1, { message: "Start date is required" }),
  partnership_end_date: z.string().min(1, { message: "End date is required" }),
  notes: z.string().optional().or(z.literal(""))
});

type Partner = z.infer<typeof partnerSchema>;

export default function PartnersPage() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const user = getCurrentUser();
  const [isAddSheetOpen, setIsAddSheetOpen] = useState(false);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentPartner, setCurrentPartner] = useState<EditPartner | null>(null);
  const [activeTab, setActiveTab] = useState("all");

  // Create form
  const createForm = useForm<Partner>({
    resolver: zodResolver(partnerSchema),
    defaultValues: {
      company_id: user?.company_id,
      name: "",
      type: "investor",
      email: "",
      phone: "",
      investment_amount: "",
      partnership_start_date: "",
      partnership_end_date: "",
      notes: ""
    }
  });

  // Define extended schema for edit form
  const editPartnerSchema = partnerSchema.extend({
    id: z.number()
  });

  type EditPartner = z.infer<typeof editPartnerSchema>;

  // Edit form
  const editForm = useForm<EditPartner>({
    resolver: zodResolver(editPartnerSchema),
    defaultValues: {
      id: 0,
      company_id: user?.company_id,
      name: "",
      type: "investor",
      email: "",
      phone: "",
      investment_amount: "",
      partnership_start_date: "",
      partnership_end_date: "",
      notes: ""
    }
  });

  // Fetch partners
  const { data: partners = [], isLoading } = useQuery({
    queryKey: ['/api/companies', user?.company_id, 'partners'],
    queryFn: async () => {
      if (!user?.company_id) return [];
      const response = await apiRequest("GET", `/api/companies/${user.company_id}/partners`);
      return await response.json();
    },
    enabled: !!user?.company_id
  });

  // Create partner mutation
  const createPartnerMutation = useMutation({
    mutationFn: async (data: Partner) => {
      const response = await apiRequest("POST", "/api/partners", data);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'partners'] });
      setIsAddSheetOpen(false);
      createForm.reset();
      toast({
        title: "Success",
        description: "Partner created successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error creating partner:", error);
      // Use the error message from the API response
      const errorMessage = error.message || "Failed to create partner. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Update partner mutation
  const updatePartnerMutation = useMutation({
    mutationFn: async (data: EditPartner) => {
      const { id, ...partnerData } = data;
      const response = await apiRequest("PATCH", `/api/partners/${id}`, partnerData);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'partners'] });
      setIsEditSheetOpen(false);
      setCurrentPartner(null);
      toast({
        title: "Success",
        description: "Partner updated successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error updating partner:", error);
      // Use the error message from the API response
      const errorMessage = error.message || "Failed to update partner. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Delete partner mutation
  const deletePartnerMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/partners/${id}?companyId=${user?.company_id}`);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'partners'] });
      setIsDeleteDialogOpen(false);
      setCurrentPartner(null);
      toast({
        title: "Success",
        description: "Partner deleted successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error deleting partner:", error);
      // Use the error message from the API response
      const errorMessage = error.message || "Failed to delete partner. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  // Handle create partner
  const handleCreatePartner = (data: Partner) => {
    // Check for mandatory fields
    let hasError = false;

    // Check name field
    if (!data.name || data.name.trim() === '') {
      createForm.setError("name", {
        type: "manual",
        message: "Partner name is required"
      });
      hasError = true;
    }

    // Validate start date field (required)
    if (!data.partnership_start_date || data.partnership_start_date.trim() === '') {
      createForm.setError("partnership_start_date", {
        type: "manual",
        message: "Start date is required"
      });
      hasError = true;
    }

    // Validate end date field (required)
    if (!data.partnership_end_date || data.partnership_end_date.trim() === '') {
      createForm.setError("partnership_end_date", {
        type: "manual",
        message: "End date is required"
      });
      hasError = true;
    }
    // Validate dates if both are provided
    else if (data.partnership_start_date) {
      const startDate = new Date(data.partnership_start_date);
      const endDate = new Date(data.partnership_end_date);

      if (endDate < startDate) {
        createForm.setError("partnership_end_date", {
          type: "manual",
          message: "End date must be after or equal to start date"
        });
        hasError = true;
      }
    }

    // Validate email field (required)
    if (!data.email || data.email.trim() === '') {
      createForm.setError("email", {
        type: "manual",
        message: "Email is required"
      });
      hasError = true;
    } else {
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      if (!emailPattern.test(data.email)) {
        createForm.setError("email", {
          type: "manual",
          message: "Please enter a valid email address"
        });
        hasError = true;
      }
    }

    // Validate phone field (required)
    if (!data.phone || data.phone.trim() === '') {
      createForm.setError("phone", {
        type: "manual",
        message: "Phone number is required"
      });
      hasError = true;
    } else {
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      if (!pattern.test(data.phone)) {
        createForm.setError("phone", {
          type: "manual",
          message: "Phone number must be exactly 10 digits with country code"
        });
        hasError = true;
      }
    }

    // If there are validation errors, don't submit
    if (hasError) {
      return;
    }

    // Prepare data for submission
    const formattedData = {
      ...data,
      company_id: user?.company_id as number,
    };

    createPartnerMutation.mutate(formattedData);
  };

  // Handle edit partner
  const handleEditPartner = (data: EditPartner) => {
    // Check for mandatory fields
    let hasError = false;

    // Check name field
    if (!data.name || data.name.trim() === '') {
      editForm.setError("name", {
        type: "manual",
        message: "Partner name is required"
      });
      hasError = true;
    }

    // Validate start date field (required)
    if (!data.partnership_start_date || data.partnership_start_date.trim() === '') {
      editForm.setError("partnership_start_date", {
        type: "manual",
        message: "Start date is required"
      });
      hasError = true;
    }

    // Validate end date field (required)
    if (!data.partnership_end_date || data.partnership_end_date.trim() === '') {
      editForm.setError("partnership_end_date", {
        type: "manual",
        message: "End date is required"
      });
      hasError = true;
    }
    // Validate dates if both are provided
    else if (data.partnership_start_date) {
      const startDate = new Date(data.partnership_start_date);
      const endDate = new Date(data.partnership_end_date);

      if (endDate < startDate) {
        editForm.setError("partnership_end_date", {
          type: "manual",
          message: "End date must be after or equal to start date"
        });
        hasError = true;
      }
    }

    // Validate email field (required)
    if (!data.email || data.email.trim() === '') {
      editForm.setError("email", {
        type: "manual",
        message: "Email is required"
      });
      hasError = true;
    } else {
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      if (!emailPattern.test(data.email)) {
        editForm.setError("email", {
          type: "manual",
          message: "Please enter a valid email address"
        });
        hasError = true;
      }
    }

    // Validate phone field (required)
    if (!data.phone || data.phone.trim() === '') {
      editForm.setError("phone", {
        type: "manual",
        message: "Phone number is required"
      });
      hasError = true;
    } else {
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      if (!pattern.test(data.phone)) {
        editForm.setError("phone", {
          type: "manual",
          message: "Phone number must be exactly 10 digits with country code"
        });
        hasError = true;
      }
    }

    // If there are validation errors, don't submit
    if (hasError) {
      return;
    }

    // Prepare data for submission
    const formattedData = {
      ...data,
    };

    updatePartnerMutation.mutate(formattedData);
  };

  // Handle delete partner
  const handleDeletePartner = () => {
    if (currentPartner) {
      deletePartnerMutation.mutate(currentPartner.id);
    }
  };

  // Open edit dialog
  const openEditDialog = (partner: any) => {
    console.log("Opening edit dialog with partner data:", partner);
    setCurrentPartner(partner);

    // Format phone number to ensure it has country code
    let phoneNumber = partner.phone || "";
    if (phoneNumber && !phoneNumber.startsWith(COUNTRY_CODE)) {
      phoneNumber = `${COUNTRY_CODE}${phoneNumber}`;
    }

    // Clear any previous errors
    editForm.clearErrors();

    // Reset form with partner data
    editForm.reset({
      id: partner.id,
      company_id: partner.company_id,
      name: partner.name,
      type: partner.type,
      email: partner.email || "",
      phone: phoneNumber,
      investment_amount: partner.investment_amount?.toString() || "",
      partnership_start_date: partner.partnership_start_date ? partner.partnership_start_date.split('T')[0] : "",
      partnership_end_date: partner.partnership_end_date ? partner.partnership_end_date.split('T')[0] : "",
      notes: partner.notes || ""
    });

    // Validate fields after setting values
    const formValues = editForm.getValues();

    // Validate name field
    if (!formValues.name || formValues.name.trim() === '') {
      editForm.setError("name", {
        type: "manual",
        message: "Partner name is required"
      });
    } else if (formValues.name.length < 2) {
      editForm.setError("name", {
        type: "manual",
        message: "Name must be at least 2 characters"
      });
    }

    // Validate email field (required)
    if (!formValues.email || formValues.email.trim() === '') {
      editForm.setError("email", {
        type: "manual",
        message: "Email is required"
      });
    } else {
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      if (!emailPattern.test(formValues.email)) {
        editForm.setError("email", {
          type: "manual",
          message: "Please enter a valid email address"
        });
      }
    }

    // Validate phone number field (required)
    if (!formValues.phone || formValues.phone.trim() === '') {
      editForm.setError("phone", {
        type: "manual",
        message: "Phone number is required"
      });
    } else {
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      if (!pattern.test(formValues.phone)) {
        editForm.setError("phone", {
          type: "manual",
          message: "Phone number must be exactly 10 digits with country code"
        });
      }
    }

    // Validate investment amount field
    if (formValues.investment_amount) {
      if (!/^\d+(\.\d{1,2})?$/.test(formValues.investment_amount)) {
        editForm.setError("investment_amount", {
          type: "manual",
          message: "Please enter a valid amount (numbers only with up to 2 decimal places)"
        });
      }
    }

    // Validate start date field (required)
    if (!formValues.partnership_start_date || formValues.partnership_start_date.trim() === '') {
      editForm.setError("partnership_start_date", {
        type: "manual",
        message: "Start date is required"
      });
    }

    // Validate end date field (required)
    if (!formValues.partnership_end_date || formValues.partnership_end_date.trim() === '') {
      editForm.setError("partnership_end_date", {
        type: "manual",
        message: "End date is required"
      });
    }
    // Validate dates if both are provided
    else if (formValues.partnership_start_date) {
      const startDate = new Date(formValues.partnership_start_date);
      const endDate = new Date(formValues.partnership_end_date);

      if (endDate < startDate) {
        editForm.setError("partnership_end_date", {
          type: "manual",
          message: "End date must be after or equal to start date"
        });
      }
    }

    // Open the edit sheet
    setIsEditSheetOpen(true);
  };

  // Open delete dialog
  const openDeleteDialog = (partner: any) => {
    setCurrentPartner(partner);
    setIsDeleteDialogOpen(true);
  };

  // Filter partners by type and sort in descending order by ID
  const filteredPartners = (activeTab === "all"
    ? [...partners]
    : [...partners].filter((partner: any) => partner.type === activeTab))
    .sort((a: any, b: any) => {
      // Handle cases where id might be undefined
      if (a.id === undefined && b.id === undefined) return 0;
      if (a.id === undefined) return 1;
      if (b.id === undefined) return -1;
      return b.id - a.id;
    });

  // Format currency
  const formatCurrency = (amount: string | number | null | undefined) => {
    if (!amount) return "₹0";
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(Number(amount));
  };

  return (
    <>
      <Helmet>
        <title>Partners Management | TrackFina</title>
      </Helmet>

      <div className="container">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Partners Management</h1>
            <p className="text-muted-foreground mt-1">
              Manage your investors, strategic partners, and other relationships
            </p>
          </div>
          <Button
            className="mt-4 md:mt-0"
            onClick={() => {
              // Reset form with default values
              createForm.reset({
                company_id: user?.company_id,
                name: "",
                type: "investor",
                email: "",
                phone: "",
                investment_amount: "",
                partnership_start_date: "",
                partnership_end_date: "",
                notes: ""
              });

              // Clear any previous errors
              createForm.clearErrors();

              // Open the add partner sheet
              setIsAddSheetOpen(true);
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Partner
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Partners</TabsTrigger>
            <TabsTrigger value="investor">Investors</TabsTrigger>
            <TabsTrigger value="strategic">Strategic</TabsTrigger>
            <TabsTrigger value="technology">Technology</TabsTrigger>
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-0">
            <Card>
              <CardContent className="p-0">
                {isLoading ? (
                  <div className="flex justify-center items-center p-8">
                    <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
                  </div>
                ) : filteredPartners.length === 0 ? (
                  <div className="text-center p-8">
                    <Briefcase className="mx-auto h-12 w-12 text-muted-foreground/50" />
                    <h3 className="mt-4 text-lg font-semibold">No partners found</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      You don't have any {activeTab !== "all" ? activeTab : ""} partners yet. Click "Add Partner" to create one.
                    </p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Investment</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPartners.map((partner: any) => (
                        <TableRow key={partner.id}>
                          <TableCell className="font-medium">
                            {partner.name}
                            <div className="text-xs font-medium text-primary">
                              {partner.partner_reference_code || `PTR-${partner.id}`}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                              partner.type === "investor" ? "bg-green-100 text-green-800" :
                              partner.type === "strategic" ? "bg-blue-100 text-blue-800" :
                              partner.type === "technology" ? "bg-purple-100 text-purple-800" :
                              "bg-orange-100 text-orange-800"
                            }`}>
                              {partner.type.charAt(0).toUpperCase() + partner.type.slice(1)}
                            </span>
                          </TableCell>
                          <TableCell>
                            {partner.email && <div className="text-sm">{partner.email}</div>}
                            {partner.phone && <div className="text-sm text-muted-foreground">{partner.phone}</div>}
                          </TableCell>
                          <TableCell>
                            {partner.investment_amount ? formatCurrency(partner.investment_amount) : "-"}
                          </TableCell>
                          <TableCell>
                            {partner.partnership_start_date
                              ? format(new Date(partner.partnership_start_date), 'MMM dd, yyyy')
                              : "-"}
                          </TableCell>
                          <TableCell>
                            {partner.partnership_end_date
                              ? format(new Date(partner.partnership_end_date), 'MMM dd, yyyy')
                              : "-"}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => openEditDialog(partner)}>
                                  <FileEdit className="mr-2 h-4 w-4" />
                                  Edit Partner
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onClick={() => openDeleteDialog(partner)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Partner
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Add Partner Sheet */}
      <Sheet open={isAddSheetOpen} onOpenChange={setIsAddSheetOpen}>
        <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
          <SheetHeader className="mb-5">
            <SheetTitle>Add New Partner</SheetTitle>
            <SheetDescription>
              Add a new partner or investor to your organization. Fill in the details below.
            </SheetDescription>
          </SheetHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(handleCreatePartner)} className="space-y-4">
              <FormField
                control={createForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Partner Name
                      <span className="text-destructive ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="E.g., Venture Capital Partners"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value);

                          // Validate name field
                          if (!value || value.trim() === '') {
                            createForm.setError("name", {
                              type: "manual",
                              message: "Partner name is required"
                            });
                          } else if (value.length < 2) {
                            createForm.setError("name", {
                              type: "manual",
                              message: "Name must be at least 2 characters"
                            });
                          } else {
                            createForm.clearErrors("name");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Partner Type
                      <span className="text-destructive ml-1">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select partner type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="investor">Investor</SelectItem>
                        <SelectItem value="strategic">Strategic Partner</SelectItem>
                        <SelectItem value="technology">Technology Partner</SelectItem>
                        <SelectItem value="distribution">Distribution Partner</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The type of relationship you have with this partner
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Email
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="E.g., <EMAIL>"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);

                            // Email validation (required)
                            if (!value || value.trim() === '') {
                              createForm.setError("email", {
                                type: "manual",
                                message: "Email is required"
                              });
                            } else {
                              const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                              if (!emailPattern.test(value)) {
                                createForm.setError("email", {
                                  type: "manual",
                                  message: "Please enter a valid email address"
                                });
                              } else {
                                createForm.clearErrors("email");
                              }
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={createForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Phone Number
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex w-full" style={{ display: "flex" }}>
                          <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground" style={{ flexShrink: 0, width: "50px" }}>
                            {COUNTRY_CODE}
                          </span>
                          <div style={{ flex: 1, width: "100%" }}>
                            <Input
                              className="rounded-l-none w-full"
                              placeholder="E.g., 9876543210"
                              {...field}
                              value={field.value?.replace(new RegExp(`^${COUNTRY_CODE.replace('+', '\\+')}`), '')}
                              onChange={(e) => {
                                // Remove non-digit characters
                                const digitsOnly = e.target.value.replace(/\D/g, '');

                                // Trim to 10 digits max
                                const trimmed = digitsOnly.substring(0, 10);

                                // Update form value with country code prefix
                                field.onChange(`${COUNTRY_CODE}${trimmed}`);

                                // Validate phone number (required)
                                if (!trimmed || trimmed.length === 0) {
                                  createForm.setError("phone", {
                                    type: "manual",
                                    message: "Phone number is required"
                                  });
                                }
                                // Show validation message if not exactly 10 digits
                                else if (digitsOnly.length < 10) {
                                  createForm.setError("phone", {
                                    type: "manual",
                                    message: `Phone number must be exactly 10 digits (currently ${digitsOnly.length})`
                                  });
                                } else if (digitsOnly.length === 10) {
                                  // Clear error when exactly 10 digits
                                  createForm.clearErrors("phone");
                                }
                              }}
                            />
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={createForm.control}
                name="investment_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Investment Amount</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="E.g., 1000000"
                        {...field}
                        onChange={(e) => {
                          // Allow only numbers and decimal point
                          const value = e.target.value.replace(/[^0-9.]/g, '');

                          // Ensure only one decimal point
                          const parts = value.split('.');
                          if (parts.length > 2) {
                            return; // Don't update if multiple decimal points
                          }

                          // Ensure at most 2 decimal places
                          if (parts.length === 2 && parts[1].length > 2) {
                            return; // Don't update if more than 2 decimal places
                          }

                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Investment or contract value in Rupees (numeric value only, up to 2 decimal places)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="partnership_start_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Start Date
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);

                            // Validate start date field
                            if (!value || value.trim() === '') {
                              createForm.setError("partnership_start_date", {
                                type: "manual",
                                message: "Start date is required"
                              });
                            } else {
                              // Check if end date exists and validate the date range
                              const endDateValue = createForm.getValues("partnership_end_date");
                              if (endDateValue && new Date(value) > new Date(endDateValue)) {
                                createForm.setError("partnership_start_date", {
                                  type: "manual",
                                  message: "Start date must be before or equal to end date"
                                });
                              } else {
                                createForm.clearErrors("partnership_start_date");
                                // Also clear end date error if it was related to date comparison
                                if (endDateValue) {
                                  createForm.clearErrors("partnership_end_date");
                                }
                              }
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={createForm.control}
                  name="partnership_end_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        End Date
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);

                            // Validate end date field (required)
                            if (!value || value.trim() === '') {
                              createForm.setError("partnership_end_date", {
                                type: "manual",
                                message: "End date is required"
                              });
                            } else {
                              const startDateValue = createForm.getValues("partnership_start_date");
                              if (startDateValue && new Date(value) < new Date(startDateValue)) {
                                createForm.setError("partnership_end_date", {
                                  type: "manual",
                                  message: "End date must be after or equal to start date"
                                });
                              } else {
                                createForm.clearErrors("partnership_end_date");
                              }
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={createForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this partnership"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsAddSheetOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={createPartnerMutation.isPending}>
                  {createPartnerMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Create Partner
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Edit Partner Sheet */}
      <Sheet open={isEditSheetOpen} onOpenChange={setIsEditSheetOpen}>
        <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
          <SheetHeader className="mb-5">
            <SheetTitle>Edit Partner</SheetTitle>
            <SheetDescription>
              Update the details of your partner or investor.
            </SheetDescription>
          </SheetHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditPartner)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Partner Name
                      <span className="text-destructive ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="E.g., Venture Capital Partners"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(value);

                          // Validate name field
                          if (!value || value.trim() === '') {
                            editForm.setError("name", {
                              type: "manual",
                              message: "Partner name is required"
                            });
                          } else if (value.length < 2) {
                            editForm.setError("name", {
                              type: "manual",
                              message: "Name must be at least 2 characters"
                            });
                          } else {
                            editForm.clearErrors("name");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Partner Type
                      <span className="text-destructive ml-1">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select partner type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="investor">Investor</SelectItem>
                        <SelectItem value="strategic">Strategic Partner</SelectItem>
                        <SelectItem value="technology">Technology Partner</SelectItem>
                        <SelectItem value="distribution">Distribution Partner</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The type of relationship you have with this partner
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Email
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="E.g., <EMAIL>"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);

                            // Email validation (required)
                            if (!value || value.trim() === '') {
                              editForm.setError("email", {
                                type: "manual",
                                message: "Email is required"
                              });
                            } else {
                              const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                              if (!emailPattern.test(value)) {
                                editForm.setError("email", {
                                  type: "manual",
                                  message: "Please enter a valid email address"
                                });
                              } else {
                                editForm.clearErrors("email");
                              }
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Phone Number
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex w-full" style={{ display: "flex" }}>
                          <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground" style={{ flexShrink: 0, width: "50px" }}>
                            {COUNTRY_CODE}
                          </span>
                          <div style={{ flex: 1, width: "100%" }}>
                            <Input
                              className="rounded-l-none w-full"
                              placeholder="E.g., 9876543210"
                              {...field}
                              value={field.value?.replace(new RegExp(`^${COUNTRY_CODE.replace('+', '\\+')}`), '')}
                              onChange={(e) => {
                                // Remove non-digit characters
                                const digitsOnly = e.target.value.replace(/\D/g, '');

                                // Trim to 10 digits max
                                const trimmed = digitsOnly.substring(0, 10);

                                // Update form value with country code prefix
                                field.onChange(`${COUNTRY_CODE}${trimmed}`);

                                // Validate phone number (required)
                                if (!trimmed || trimmed.length === 0) {
                                  editForm.setError("phone", {
                                    type: "manual",
                                    message: "Phone number is required"
                                  });
                                }
                                // Show validation message if not exactly 10 digits
                                else if (digitsOnly.length < 10) {
                                  editForm.setError("phone", {
                                    type: "manual",
                                    message: `Phone number must be exactly 10 digits (currently ${digitsOnly.length})`
                                  });
                                } else if (digitsOnly.length === 10) {
                                  // Clear error when exactly 10 digits
                                  editForm.clearErrors("phone");
                                }
                              }}
                            />
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="investment_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Investment Amount</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="E.g., 1000000"
                        {...field}
                        onChange={(e) => {
                          // Allow only numbers and decimal point
                          const value = e.target.value.replace(/[^0-9.]/g, '');

                          // Ensure only one decimal point
                          const parts = value.split('.');
                          if (parts.length > 2) {
                            return; // Don't update if multiple decimal points
                          }

                          // Ensure at most 2 decimal places
                          if (parts.length === 2 && parts[1].length > 2) {
                            return; // Don't update if more than 2 decimal places
                          }

                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Investment or contract value in Rupees (numeric value only, up to 2 decimal places)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="partnership_start_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Start Date
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);

                            // Validate start date field (required)
                            if (!value || value.trim() === '') {
                              editForm.setError("partnership_start_date", {
                                type: "manual",
                                message: "Start date is required"
                              });
                            } else {
                              // Check if end date exists and validate the date range
                              const endDateValue = editForm.getValues("partnership_end_date");
                              if (endDateValue && new Date(value) > new Date(endDateValue)) {
                                editForm.setError("partnership_start_date", {
                                  type: "manual",
                                  message: "Start date must be before or equal to end date"
                                });
                              } else {
                                editForm.clearErrors("partnership_start_date");
                                // Also clear end date error if it was related to date comparison
                                if (endDateValue) {
                                  editForm.clearErrors("partnership_end_date");
                                }
                              }
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="partnership_end_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        End Date
                        <span className="text-destructive ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);

                            // Validate end date field (required)
                            if (!value || value.trim() === '') {
                              editForm.setError("partnership_end_date", {
                                type: "manual",
                                message: "End date is required"
                              });
                            } else {
                              const startDateValue = editForm.getValues("partnership_start_date");
                              if (startDateValue && new Date(value) < new Date(startDateValue)) {
                                editForm.setError("partnership_end_date", {
                                  type: "manual",
                                  message: "End date must be after or equal to start date"
                                });
                              } else {
                                editForm.clearErrors("partnership_end_date");
                              }
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this partnership"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsEditSheetOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={updatePartnerMutation.isPending}>
                  {updatePartnerMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Update Partner
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Delete Partner Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Partner</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this partner? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">
              You are about to delete: <strong>{currentPartner?.name}</strong>
            </p>
          </div>
          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeletePartner}
              disabled={deletePartnerMutation.isPending}
            >
              {deletePartnerMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Delete Partner
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}