import { useState } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/lib/auth';
import { useCompany } from '@/lib/companies';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Separator } from '@/components/ui/separator';
import { Users, Plus, Edit, Trash2, Calendar, Clock, MapPin, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define the validation schema for groups
const groupFormSchema = z.object({
  company_id: z.number(),
  branch_id: z.number().optional().nullable(),
  name: z.string().min(1, 'Group name is required'),
  description: z.string().optional().or(z.literal('')),
  leader_name: z.string().optional().or(z.literal('')),
  meeting_day: z.string().optional().or(z.literal('')),
  meeting_time: z.string().optional().or(z.literal('')),
  location: z.string().optional().or(z.literal('')),
  status: z.string().default('active')
});

type GroupFormData = z.infer<typeof groupFormSchema>;

export default function GroupsPage() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const { currentCompany } = useCompany();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<any>(null);

  // Fetch branches for dropdown
  const {
    data: branches = [],
    isLoading: isBranchesLoading,
    isError: isBranchesError,
    error: branchesError,
  } = useQuery({
    queryKey: ['/api/companies', currentCompany?.id, 'branches'],
    queryFn: async () => {
      if (!currentCompany?.id) {
        console.log("No current company ID available for branches");
        return [];
      }
      
      try {
        const res = await apiRequest('GET', `/api/companies/${currentCompany.id}/branches`);
        if (!res.ok) {
          // Return empty array instead of throwing error - graceful fallback
          return [];
        }

        const data = await res.json();
        return Array.isArray(data) ? data : [];
      } catch (error) {
        // Return empty array instead of throwing error - graceful fallback
        return [];
      }
    },
    enabled: !!currentCompany?.id,
    retry: 1,
  });

  // Fetch groups
  const {
    data: groups = [],
    isLoading: isGroupsLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['/api/companies', currentCompany?.id, 'groups'],
    queryFn: async () => {
      if (!currentCompany?.id) {
        console.log("No current company ID available for groups");
        return [];
      }
      
      console.log(`Fetching groups for company ID: ${currentCompany.id}`);
      
      try {
        const res = await apiRequest('GET', `/api/companies/${currentCompany.id}/groups`);
        if (!res.ok) {
          console.error(`Failed to fetch groups: ${res.status}`);
          // Return empty array instead of throwing error - graceful fallback
          return [];
        }
        
        const data = await res.json();
        console.log(`Successfully fetched ${Array.isArray(data) ? data.length : 0} groups`);
        return Array.isArray(data) ? data : [];
      } catch (error) {
        console.error('Failed to fetch groups:', error);
        // Return empty array instead of throwing error - graceful fallback
        return [];
      }
    },
    enabled: !!currentCompany?.id,
    retry: 1,
  });

  // Create group form
  const createForm = useForm<GroupFormData>({
    resolver: zodResolver(groupFormSchema),
    defaultValues: {
      company_id: currentCompany?.id,
      branch_id: null,
      name: '',
      description: '',
      leader_name: '',
      meeting_day: '',
      meeting_time: '',
      location: '',
      status: 'active'
    },
  });

  // Edit group form
  const editForm = useForm<GroupFormData>({
    resolver: zodResolver(groupFormSchema),
    defaultValues: {
      company_id: currentCompany?.id,
      branch_id: null,
      name: '',
      description: '',
      leader_name: '',
      meeting_day: '',
      meeting_time: '',
      location: '',
      status: 'active'
    },
  });

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: async (data: GroupFormData) => {
      const res = await apiRequest('POST', '/api/groups', data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.id, 'groups'] });
      setIsCreateDialogOpen(false);
      createForm.reset();
      toast({
        title: 'Group created',
        description: 'Group has been created successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create group. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: async (data: GroupFormData) => {
      const res = await apiRequest('PATCH', `/api/groups/${selectedGroup.id}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.id, 'groups'] });
      setIsEditDialogOpen(false);
      editForm.reset();
      toast({
        title: 'Group updated',
        description: 'Group has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update group. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Delete group mutation
  const deleteGroupMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest('DELETE', `/api/groups/${id}`);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to delete group');
      }
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.id, 'groups'] });
      setIsDeleteDialogOpen(false);
      setSelectedGroup(null);
      toast({
        title: 'Group deleted',
        description: 'Group has been deleted successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete group. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Handle form submission - create
  const handleCreateFormSubmit = createForm.handleSubmit((data) => {
    createGroupMutation.mutate({
      ...data,
      company_id: currentCompany?.id as number
    });
  });

  // Handle form submission - edit
  const handleEditFormSubmit = editForm.handleSubmit((data) => {
    updateGroupMutation.mutate({
      ...data,
      company_id: currentCompany?.id as number
    });
  });

  // Handle edit group
  const handleEditGroup = (group: any) => {
    setSelectedGroup(group);
    editForm.reset({
      company_id: currentCompany?.id,
      branch_id: group.branch_id,
      name: group.name,
      description: group.description || '',
      leader_name: group.leader_name || '',
      meeting_day: group.meeting_day || '',
      meeting_time: group.meeting_time || '',
      location: group.location || '',
      status: group.status
    });
    setIsEditDialogOpen(true);
  };

  // Handle delete group
  const handleDeleteGroup = (group: any) => {
    setSelectedGroup(group);
    setIsDeleteDialogOpen(true);
  };

  // Get branch name by ID
  const getBranchName = (branchId: number | null) => {
    if (!branchId || !branches) return 'None';
    const branch = branches.find((b: any) => b.id === branchId);
    return branch ? branch.name : 'Unknown';
  };

  const isLoading = isGroupsLoading || isBranchesLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  // We've modified our error handling to return empty arrays instead of throwing errors,
  // so we won't get here unless there's a truly serious error
  if (isError || isBranchesError) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Groups</h1>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Add Group
          </Button>
        </div>
        
        <Card className="bg-red-50 border-red-200">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Data</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">
              {(error || branchesError) instanceof Error 
                ? (error || branchesError)?.message 
                : "Failed to load groups data. Please try again later."}
            </p>
            <div className="mt-4">
              <Button 
                variant="outline" 
                onClick={() => {
                  queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.id, 'groups'] });
                  queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.id, 'branches'] });
                }}
              >
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Groups</h1>
          <p className="text-gray-500">Manage your organization's groups</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Group
        </Button>
      </div>

      {groups?.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Users className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium">No groups yet</h3>
            <p className="text-gray-500 text-center mb-4">
              Create groups to organize your teams
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add First Group
            </Button>
          </CardContent>
        </Card>
      ) : isMobile ? (
        <div className="grid grid-cols-1 gap-4">
          {groups.map((group: any) => (
            <Card key={group.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{group.name}</CardTitle>
                  <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                    {group.status === 'active' ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                {group.branch_id && (
                  <div className="text-sm text-gray-500">
                    Branch: {getBranchName(group.branch_id)}
                  </div>
                )}
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-2 text-sm">
                  {group.description && (
                    <div className="text-gray-600 mb-2">{group.description}</div>
                  )}
                  {group.leader_name && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span>{group.leader_name}</span>
                    </div>
                  )}
                  {group.meeting_day && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>{group.meeting_day}</span>
                      {group.meeting_time && (
                        <><Clock className="h-4 w-4 ml-2 text-gray-500" /> {group.meeting_time}</>
                      )}
                    </div>
                  )}
                  {group.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{group.location}</span>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-end pt-2">
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditGroup(group)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteGroup(group)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-md shadow">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Branch</TableHead>
                <TableHead>Leader</TableHead>
                <TableHead>Meeting Schedule</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groups.map((group: any) => (
                <TableRow key={group.id}>
                  <TableCell className="font-medium">
                    <div>
                      {group.name}
                      {group.description && (
                        <div className="text-xs text-gray-500 mt-1">{group.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getBranchName(group.branch_id)}</TableCell>
                  <TableCell>{group.leader_name || '-'}</TableCell>
                  <TableCell>
                    {group.meeting_day ? (
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-gray-500 mr-1" />
                        {group.meeting_day}
                        {group.meeting_time && (
                          <span className="ml-2">
                            <Clock className="h-3 w-3 text-gray-500 inline mr-1" />
                            {group.meeting_time}
                          </span>
                        )}
                      </div>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                      {group.status === 'active' ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditGroup(group)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteGroup(group)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Create Group Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create Group</DialogTitle>
            <DialogDescription>
              Add a new group to your organization.
            </DialogDescription>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={handleCreateFormSubmit} className="space-y-6">
              {/* Group Name */}
              <FormField
                control={createForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter group name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Branch */}
              <FormField
                control={createForm.control}
                name="branch_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Branch</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value !== null ? field.value.toString() : "no_branch"}
                        onValueChange={(value) => {
                          if (value === "no_branch") {
                            field.onChange(null);
                          } else {
                            field.onChange(parseInt(value, 10));
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a branch (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="no_branch">No Branch</SelectItem>
                          {branches && branches.map((branch: any) => (
                            <SelectItem key={branch.id} value={branch.id.toString()}>
                              {branch.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Leader */}
              <FormField
                control={createForm.control}
                name="leader_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Leader</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter group leader name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Meeting Day */}
                <FormField
                  control={createForm.control}
                  name="meeting_day"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Day</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value || "none"}
                          onValueChange={(value) => {
                            if (value === "none") {
                              field.onChange("");
                            } else {
                              field.onChange(value);
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select day" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="Monday">Monday</SelectItem>
                            <SelectItem value="Tuesday">Tuesday</SelectItem>
                            <SelectItem value="Wednesday">Wednesday</SelectItem>
                            <SelectItem value="Thursday">Thursday</SelectItem>
                            <SelectItem value="Friday">Friday</SelectItem>
                            <SelectItem value="Saturday">Saturday</SelectItem>
                            <SelectItem value="Sunday">Sunday</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Meeting Time */}
                <FormField
                  control={createForm.control}
                  name="meeting_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Location */}
              <FormField
                control={createForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meeting Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter meeting location" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={createForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a description of the group"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createGroupMutation.isPending}
                >
                  {createGroupMutation.isPending ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground"></div>
                      Creating...
                    </>
                  ) : (
                    'Create Group'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Group Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Group</DialogTitle>
            <DialogDescription>
              Update the group details.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={handleEditFormSubmit} className="space-y-6">
              {/* Group Name */}
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter group name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Branch */}
              <FormField
                control={editForm.control}
                name="branch_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Branch</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value !== null ? field.value.toString() : "no_branch"}
                        onValueChange={(value) => {
                          if (value === "no_branch") {
                            field.onChange(null);
                          } else {
                            field.onChange(parseInt(value, 10));
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a branch (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="no_branch">No Branch</SelectItem>
                          {branches && branches.map((branch: any) => (
                            <SelectItem key={branch.id} value={branch.id.toString()}>
                              {branch.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Leader */}
              <FormField
                control={editForm.control}
                name="leader_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Leader</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter group leader name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Meeting Day */}
                <FormField
                  control={editForm.control}
                  name="meeting_day"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Day</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value || "none"}
                          onValueChange={(value) => {
                            if (value === "none") {
                              field.onChange("");
                            } else {
                              field.onChange(value);
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select day" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="Monday">Monday</SelectItem>
                            <SelectItem value="Tuesday">Tuesday</SelectItem>
                            <SelectItem value="Wednesday">Wednesday</SelectItem>
                            <SelectItem value="Thursday">Thursday</SelectItem>
                            <SelectItem value="Friday">Friday</SelectItem>
                            <SelectItem value="Saturday">Saturday</SelectItem>
                            <SelectItem value="Sunday">Sunday</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Meeting Time */}
                <FormField
                  control={editForm.control}
                  name="meeting_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Location */}
              <FormField
                control={editForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meeting Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter meeting location" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a description of the group"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateGroupMutation.isPending}
                >
                  {updateGroupMutation.isPending ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground"></div>
                      Updating...
                    </>
                  ) : (
                    'Update Group'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Group Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Delete Group</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this group? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 border rounded-md bg-muted/20 mb-4">
            <div className="font-medium">{selectedGroup?.name}</div>
            {selectedGroup?.description && (
              <div className="text-sm text-gray-500 mt-1">{selectedGroup.description}</div>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => deleteGroupMutation.mutate(selectedGroup.id)}
              disabled={deleteGroupMutation.isPending}
            >
              {deleteGroupMutation.isPending ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground"></div>
                  Deleting...
                </>
              ) : (
                'Delete Group'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
