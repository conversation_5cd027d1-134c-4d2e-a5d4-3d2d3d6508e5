#!/usr/bin/env node
/**
 * Debug Script: Check Partners Table
 * Purpose: Check if the partners table exists and display its structure and data
 * Usage: node scripts/debug/check-partners-table.js
 */

import { runMigration, logMigrationStats } from '../utils/migration-runner.js';
import { tableExists, getTableColumns, countTableRows } from '../utils/database-connection.js';

await runMigration('Check Partners Table', async (pool, { dryRun }) => {
  console.log('Checking if partners table exists...');
  
  // Check if the table exists
  const exists = await tableExists('partners');
  console.log('Partners table exists:', exists);
  
  if (exists) {
    // Get the columns of the table
    const columns = await getTableColumns('partners');
    
    console.log('Partners table columns:');
    columns.forEach(column => {
      console.log(`- ${column.column_name} (${column.data_type})`);
    });
    
    // Count the number of rows in the table
    const rowCount = await countTableRows('partners');
    console.log('Number of rows in partners table:', rowCount);
    
    // Get a sample row if any exist
    if (rowCount > 0) {
      const sampleResult = await pool.query('SELECT * FROM partners LIMIT 1');
      console.log('Sample row from partners table:', sampleResult.rows[0]);
    }

    logMigrationStats({
      'Table exists': 'Yes',
      'Column count': columns.length,
      'Row count': rowCount,
      'Has sample data': rowCount > 0 ? 'Yes' : 'No'
    });
  } else {
    console.log('Partners table does not exist.');
    
    if (dryRun) {
      console.log('Would create partners table with the following structure:');
      console.log(`
        CREATE TABLE IF NOT EXISTS partners (
          id SERIAL PRIMARY KEY,
          company_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          email TEXT,
          phone TEXT,
          address TEXT,
          website TEXT,
          contact_person TEXT,
          investment_amount NUMERIC(10, 2),
          partnership_start_date TIMESTAMP,
          partnership_end_date TIMESTAMP,
          agreement_details TEXT,
          notes TEXT,
          active BOOLEAN DEFAULT TRUE NOT NULL,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP DEFAULT NOW() NOT NULL
        )
      `);
      return;
    }
    
    console.log('Creating partners table...');
    
    // Create the partners table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS partners (
        id SERIAL PRIMARY KEY,
        company_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        website TEXT,
        contact_person TEXT,
        investment_amount NUMERIC(10, 2),
        partnership_start_date TIMESTAMP,
        partnership_end_date TIMESTAMP,
        agreement_details TEXT,
        notes TEXT,
        active BOOLEAN DEFAULT TRUE NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL
      )
    `);
    
    console.log('Partners table created successfully');
    
    logMigrationStats({
      'Table exists': 'Created',
      'Action': 'Table creation',
      'Status': 'Success'
    });
  }
});
