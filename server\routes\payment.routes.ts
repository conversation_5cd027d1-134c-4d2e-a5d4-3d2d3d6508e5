import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { insertPaymentSchema } from '@shared/schema';
import { ZodError } from 'zod';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

export function registerPaymentRoutes(app: Express): void {
  // Get all payments for a company
  app.get('/api/companies/:companyId/payments', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      // Parse query parameters for date range
      const startDate = req.query.start_date as string | undefined;
      const endDate = req.query.end_date as string | undefined;
      
      // Create date range object if both start and end dates are provided
      const dateRange = (startDate && endDate) ? { startDate, endDate } : undefined;
      
      const payments = await storage.getPaymentsByCompany(companyId, dateRange);
      return res.json(payments);
    } catch (error) {
      console.error('Error fetching payments:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get payments for a loan
  app.get('/api/loans/:loanId/payments', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.loanId);
      
      // Get the loan to check company access
      const loan = await storage.getLoan(loanId);
      
      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }
      
      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }
      
      const payments = await storage.getPaymentsByLoan(loanId, loan.company_id);
      return res.json(payments);
    } catch (error) {
      console.error(`Error fetching payments for loan ${req.params.loanId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get payments for a customer
  app.get('/api/customers/:customerId/payments', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const customerId = parseInt(req.params.customerId);
      
      // Get the customer to check company access
      const customer = await storage.getCustomer(customerId);
      
      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }
      
      // Check if user has access to this customer's company
      if (req.user!.role !== 'saas_admin' && customer.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === customer.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this customer' });
        }
      }
      
      const payments = await storage.getPaymentsByCustomer(customerId, customer.company_id);
      return res.json(payments);
    } catch (error) {
      console.error(`Error fetching payments for customer ${req.params.customerId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get payments for a collection
  app.get('/api/collections/:collectionId/payments', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const collectionId = parseInt(req.params.collectionId);
      
      // Get the collection to check company access
      const collection = await storage.getCollection(collectionId);
      
      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }
      
      // Check if user has access to this collection's company
      if (req.user!.role !== 'saas_admin' && collection.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === collection.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this collection' });
        }
      }
      
      const payments = await storage.getPaymentsByCollection(collectionId, collection.company_id);
      return res.json(payments);
    } catch (error) {
      console.error(`Error fetching payments for collection ${req.params.collectionId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get payment by ID
  app.get('/api/payments/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const paymentId = parseInt(req.params.id);
      
      const payment = await storage.getPayment(paymentId);
      
      if (!payment) {
        return res.status(404).json({ message: 'Payment not found' });
      }
      
      // Check if user has access to this payment's company
      if (req.user!.role !== 'saas_admin' && payment.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === payment.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this payment' });
        }
      }
      
      return res.json(payment);
    } catch (error) {
      console.error(`Error fetching payment ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create payment
  app.post('/api/payments', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      
      // Validate input
      const result = insertPaymentSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ 
          message: 'Invalid input', 
          errors: formatZodError(result.error) 
        });
      }
      
      // Check if user has access to the company
      const paymentCompanyId = result.data.company_id;
      if (req.user!.role !== 'saas_admin' && paymentCompanyId !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === paymentCompanyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }
      
      const payment = await storage.createPayment(result.data);
      return res.status(201).json(payment);
    } catch (error) {
      console.error('Error creating payment:', error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Update payment
  app.put('/api/payments/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const paymentId = parseInt(req.params.id);
      
      // Get the payment to check company access
      const payment = await storage.getPayment(paymentId);
      
      if (!payment) {
        return res.status(404).json({ message: 'Payment not found' });
      }
      
      // Check if user has access to this payment's company
      if (req.user!.role !== 'saas_admin' && payment.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === payment.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this payment' });
        }
      }
      
      // Validate input
      const result = insertPaymentSchema.partial().safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ 
          message: 'Invalid input', 
          errors: formatZodError(result.error) 
        });
      }
      
      const updatedPayment = await storage.updatePayment(paymentId, payment.company_id, result.data);
      return res.json(updatedPayment);
    } catch (error) {
      console.error(`Error updating payment ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Delete payment
  app.delete('/api/payments/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const paymentId = parseInt(req.params.id);
      
      // Get the payment to check company access
      const payment = await storage.getPayment(paymentId);
      
      if (!payment) {
        return res.status(404).json({ message: 'Payment not found' });
      }
      
      // Check if user has access to this payment's company
      if (req.user!.role !== 'saas_admin' && payment.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === payment.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this payment' });
        }
      }
      
      const success = await storage.deletePayment(paymentId, payment.company_id);
      
      if (!success) {
        return res.status(500).json({ message: 'Failed to delete payment' });
      }
      
      return res.json({ message: 'Payment deleted successfully' });
    } catch (error) {
      console.error(`Error deleting payment ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Get payment receipt
  app.get('/api/payments/:id/receipt', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const paymentId = parseInt(req.params.id);
      
      // Get the payment to check company access
      const payment = await storage.getPayment(paymentId);
      
      if (!payment) {
        return res.status(404).json({ message: 'Payment not found' });
      }
      
      // Check if user has access to this payment's company
      if (req.user!.role !== 'saas_admin' && payment.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === payment.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this payment' });
        }
      }
      
      const receiptData = await storage.getPaymentReceipt(paymentId, payment.company_id);
      
      if (!receiptData) {
        return res.status(500).json({ message: 'Failed to generate receipt' });
      }
      
      return res.json(receiptData);
    } catch (error) {
      console.error(`Error generating receipt for payment ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
