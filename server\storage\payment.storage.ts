import { db } from '../db';
import { eq, and, desc, sql } from 'drizzle-orm';
import { payments, collections, loans, customers } from '../../shared/schema';
import { Payment, InsertPayment, Collection } from '../../shared/schema';
import errorLogger from '../utils/errorLogger';
import { IPaymentStorage } from './interfaces';

export class PaymentStorage implements IPaymentStorage {
  async getPayment(id: number): Promise<Payment | undefined> {
    try {
      const [payment] = await db.select()
        .from(payments)
        .where(eq(payments.id, id));
      return payment;
    } catch (error) {
      errorLogger.logError(`Error fetching payment id=${id}`, 'payment-fetch', error as Error);
      return undefined;
    }
  }

  async getPaymentsByCompany(
    companyId: number,
    dateRange?: { startDate: string, endDate: string }
  ): Promise<Payment[]> {
    try {
      // Build the where conditions
      let whereConditions = eq(payments.company_id, companyId);

      // Apply date range filter if provided
      if (dateRange) {
        whereConditions = and(
          whereConditions,
          sql`DATE(${payments.payment_date}) >= ${dateRange.startDate}`,
          sql`DATE(${payments.payment_date}) <= ${dateRange.endDate}`
        );
      }

      const companyPayments = await db.select()
        .from(payments)
        .where(whereConditions)
        .orderBy(desc(payments.payment_date));

      return companyPayments;
    } catch (error) {
      errorLogger.logError(`Error fetching payments for company id=${companyId}`, 'payment-fetch', error as Error);
      return [];
    }
  }

  async getPaymentsByLoan(loanId: number, companyId: number): Promise<Payment[]> {
    try {
      // First check if the loan exists and belongs to the company
      const [loan] = await db.select()
        .from(loans)
        .where(
          and(
            eq(loans.id, loanId),
            eq(loans.company_id, companyId)
          )
        );

      if (!loan) {
        return [];
      }

      // Get payments through collections since payments only have collection_id
      const loanPayments = await db.select({
        payment: payments
      })
        .from(payments)
        .innerJoin(collections, eq(payments.collection_id, collections.id))
        .where(eq(collections.loan_id, loanId))
        .orderBy(desc(payments.payment_date));

      return loanPayments.map(row => row.payment);
    } catch (error) {
      errorLogger.logError(`Error fetching payments for loan id=${loanId}`, 'payment-fetch', error as Error);
      return [];
    }
  }

  async getPaymentsByCustomer(customerId: number, companyId: number): Promise<Payment[]> {
    try {
      // First check if the customer exists and belongs to the company
      const [customer] = await db.select()
        .from(customers)
        .where(
          and(
            eq(customers.id, customerId),
            eq(customers.company_id, companyId)
          )
        );

      if (!customer) {
        return [];
      }

      // Get payments through collections since payments only have collection_id
      const customerPayments = await db.select({
        payment: payments
      })
        .from(payments)
        .innerJoin(collections, eq(payments.collection_id, collections.id))
        .where(eq(collections.customer_id, customerId))
        .orderBy(desc(payments.payment_date));

      return customerPayments.map(row => row.payment);
    } catch (error) {
      errorLogger.logError(`Error fetching payments for customer id=${customerId}`, 'payment-fetch', error as Error);
      return [];
    }
  }

  async getPaymentsByCollection(collectionId: number, companyId: number): Promise<Payment[]> {
    try {
      // First check if the collection exists and belongs to the company
      const [collection] = await db.select()
        .from(collections)
        .where(
          and(
            eq(collections.id, collectionId),
            eq(collections.company_id, companyId)
          )
        );

      if (!collection) {
        return [];
      }

      const collectionPayments = await db.select()
        .from(payments)
        .where(eq(payments.collection_id, collectionId))
        .orderBy(desc(payments.payment_date));

      return collectionPayments;
    } catch (error) {
      errorLogger.logError(`Error fetching payments for collection id=${collectionId}`, 'payment-fetch', error as Error);
      return [];
    }
  }

  async createPayment(paymentData: InsertPayment): Promise<Payment> {
    try {
      // Start a transaction
      return await db.transaction(async (tx) => {
        // Validate that the collection exists and belongs to the company
        const [collection] = await tx.select()
          .from(collections)
          .where(
            and(
              eq(collections.id, paymentData.collection_id),
              eq(collections.company_id, paymentData.company_id)
            )
          );

        if (!collection) {
          throw new Error(`Collection with id=${paymentData.collection_id} not found for company id=${paymentData.company_id}`);
        }

        // Update the collection status to completed
        await tx.update(collections)
          .set({ status: 'completed' })
          .where(eq(collections.id, paymentData.collection_id));

        // Create the payment
        const [payment] = await tx.insert(payments)
          .values(paymentData)
          .returning();

        return payment;
      });
    } catch (error) {
      errorLogger.logError(`Error creating payment`, 'payment-create', error as Error);
      throw error;
    }
  }

  async updatePayment(id: number, companyId: number, paymentData: Partial<InsertPayment>): Promise<Payment> {
    try {
      // First check if the payment exists and belongs to the company
      const [existingPayment] = await db.select()
        .from(payments)
        .where(
          and(
            eq(payments.id, id),
            eq(payments.company_id, companyId)
          )
        );

      if (!existingPayment) {
        throw new Error(`Payment with id=${id} not found for company id=${companyId}`);
      }

      // Don't allow changing company_id or collection_id
      delete paymentData.company_id;

      // If collection_id is being updated, validate that the collection exists and belongs to the company
      if (paymentData.collection_id && paymentData.collection_id !== existingPayment.collection_id) {
        const [collection] = await db.select()
          .from(collections)
          .where(
            and(
              eq(collections.id, paymentData.collection_id),
              eq(collections.company_id, companyId)
            )
          );

        if (!collection) {
          throw new Error(`Collection with id=${paymentData.collection_id} not found for company id=${companyId}`);
        }
      }

      const [updatedPayment] = await db.update(payments)
        .set(paymentData)
        .where(eq(payments.id, id))
        .returning();

      return updatedPayment;
    } catch (error) {
      errorLogger.logError(`Error updating payment id=${id}`, 'payment-update', error as Error);
      throw error;
    }
  }

  async deletePayment(id: number, companyId: number): Promise<boolean> {
    try {
      // First check if the payment exists and belongs to the company
      const [existingPayment] = await db.select()
        .from(payments)
        .where(
          and(
            eq(payments.id, id),
            eq(payments.company_id, companyId)
          )
        );

      if (!existingPayment) {
        return false;
      }

      // Start a transaction
      await db.transaction(async (tx) => {
        // If the payment is associated with a collection, update the collection status back to pending
        if (existingPayment.collection_id) {
          await tx.update(collections)
            .set({
              status: 'pending'
            })
            .where(eq(collections.id, existingPayment.collection_id));
        }

        // Delete the payment
        await tx.delete(payments)
          .where(eq(payments.id, id));
      });

      return true;
    } catch (error) {
      errorLogger.logError(`Error deleting payment id=${id}`, 'payment-delete', error as Error);
      return false;
    }
  }

  async getPaymentReceipt(id: number, companyId: number): Promise<any> {
    try {
      // Get the payment with related data
      const [payment] = await db.select()
        .from(payments)
        .where(
          and(
            eq(payments.id, id),
            eq(payments.company_id, companyId)
          )
        );

      if (!payment) {
        return null;
      }

      // Get the collection details first, then loan and customer through collection
      let loan = null;
      let customer = null;
      if (payment.collection_id) {
        const [collection] = await db.select()
          .from(collections)
          .where(eq(collections.id, payment.collection_id));

        if (collection) {
          // Get loan details
          const [loanData] = await db.select()
            .from(loans)
            .where(eq(loans.id, collection.loan_id));
          loan = loanData;

          // Get customer details
          const [customerData] = await db.select()
            .from(customers)
            .where(eq(customers.id, collection.customer_id));
          customer = customerData;
        }
      }

      // Get the collection details if available
      let collection: Collection | null = null;
      if (payment.collection_id) {
        const [collectionData] = await db.select()
          .from(collections)
          .where(eq(collections.id, payment.collection_id));
        collection = collectionData;
      }

      // Construct the receipt data
      const receiptData = {
        payment,
        loan,
        customer,
        collection,
        receipt_date: new Date().toISOString(),
        receipt_number: `REC-${payment.id}-${Date.now()}`
      };

      return receiptData;
    } catch (error) {
      errorLogger.logError(`Error generating receipt for payment id=${id}`, 'payment-receipt', error as Error);
      return null;
    }
  }
}
