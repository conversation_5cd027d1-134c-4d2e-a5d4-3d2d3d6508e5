import { Router } from 'express';
import { auditService } from '../services/auditService';
import { enhancedAuthMiddleware, type EnhancedAuthRequest } from '../middleware/enhancedAuth';
import { requirePermission } from '../middleware/permission';
import { auditAdminOperation } from '../middleware/auditMiddleware';
import { z } from 'zod';

const router = Router();

// Apply enhanced auth middleware to all routes
router.use(enhancedAuthMiddleware);

// Validation schemas
const auditFiltersSchema = z.object({
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  userId: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  permissionCode: z.string().optional(),
  result: z.string().optional(),
  entityType: z.string().optional(),
  entityId: z.string().optional(),
  tableName: z.string().optional(),
  changeType: z.string().optional(),
  changedBy: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  targetUserId: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  targetRoleId: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
});

// Get permission audit logs
router.get('/permission-logs', 
  requirePermission('audit_view'),
  auditAdminOperation('audit_view', 'view_permission_logs'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const filters = auditFiltersSchema.parse(req.query);
      
      const result = await auditService.getPermissionAuditLogs({
        companyId: req.user!.company_id,
        ...filters,
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching permission audit logs:', error);
      res.status(500).json({ message: 'Failed to fetch permission audit logs' });
    }
  }
);

// Get data access audit logs
router.get('/data-access-logs',
  requirePermission('audit_view'),
  auditAdminOperation('audit_view', 'view_data_access_logs'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const filters = auditFiltersSchema.parse(req.query);
      
      const result = await auditService.getDataAccessAuditLogs({
        companyId: req.user!.company_id,
        ...filters,
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching data access audit logs:', error);
      res.status(500).json({ message: 'Failed to fetch data access audit logs' });
    }
  }
);

// Get permission change logs
router.get('/permission-changes',
  requirePermission('audit_view'),
  auditAdminOperation('audit_view', 'view_permission_changes'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const filters = auditFiltersSchema.parse(req.query);
      
      const result = await auditService.getPermissionChangeLogs({
        companyId: req.user!.company_id,
        ...filters,
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching permission change logs:', error);
      res.status(500).json({ message: 'Failed to fetch permission change logs' });
    }
  }
);

// Get audit statistics
router.get('/statistics',
  requirePermission('audit_view'),
  auditAdminOperation('audit_view', 'view_audit_statistics'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { startDate, endDate } = auditFiltersSchema.parse(req.query);
      
      const statistics = await auditService.getAuditStatistics(
        req.user!.company_id,
        startDate,
        endDate
      );

      res.json(statistics);
    } catch (error) {
      console.error('Error fetching audit statistics:', error);
      res.status(500).json({ message: 'Failed to fetch audit statistics' });
    }
  }
);

// Get sensitive operations audit
router.get('/sensitive-operations',
  requirePermission('audit_view'),
  auditAdminOperation('audit_view', 'view_sensitive_operations'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { startDate, endDate } = auditFiltersSchema.parse(req.query);
      
      const sensitiveOps = await auditService.getSensitiveOperationsAudit(
        req.user!.company_id,
        startDate,
        endDate
      );

      res.json(sensitiveOps);
    } catch (error) {
      console.error('Error fetching sensitive operations audit:', error);
      res.status(500).json({ message: 'Failed to fetch sensitive operations audit' });
    }
  }
);

// Get compliance audit report
router.get('/compliance-report',
  requirePermission('audit_view'),
  auditAdminOperation('audit_view', 'view_compliance_report'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { startDate, endDate } = auditFiltersSchema.parse(req.query);
      
      if (!startDate || !endDate) {
        return res.status(400).json({ 
          message: 'Start date and end date are required for compliance report' 
        });
      }
      
      const complianceReport = await auditService.getComplianceAuditReport(
        req.user!.company_id,
        startDate,
        endDate
      );

      res.json(complianceReport);
    } catch (error) {
      console.error('Error fetching compliance audit report:', error);
      res.status(500).json({ message: 'Failed to fetch compliance audit report' });
    }
  }
);

// Get user-specific audit logs (users can view their own logs)
router.get('/user/:userId/logs',
  requirePermission('audit_view_own'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const userId = parseInt(req.params.userId);
      
      // Users can only view their own logs unless they have admin permission
      if (userId !== req.user!.id && !req.user!.permissions.includes('audit_view')) {
        return res.status(403).json({ message: 'You can only view your own audit logs' });
      }

      const filters = auditFiltersSchema.parse(req.query);
      
      const [permissionLogs, dataAccessLogs, changeLogs] = await Promise.all([
        auditService.getPermissionAuditLogs({
          companyId: req.user!.company_id,
          userId,
          ...filters,
        }),
        auditService.getDataAccessAuditLogs({
          companyId: req.user!.company_id,
          userId,
          ...filters,
        }),
        auditService.getPermissionChangeLogs({
          companyId: req.user!.company_id,
          targetUserId: userId,
          ...filters,
        }),
      ]);

      res.json({
        permissionLogs,
        dataAccessLogs,
        changeLogs,
      });
    } catch (error) {
      console.error('Error fetching user audit logs:', error);
      res.status(500).json({ message: 'Failed to fetch user audit logs' });
    }
  }
);

// Get audit logs for a specific resource
router.get('/resource/:resourceType/:resourceId',
  requirePermission('audit_view'),
  auditAdminOperation('audit_view', 'view_resource_audit'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { resourceType, resourceId } = req.params;
      const filters = auditFiltersSchema.parse(req.query);
      
      const [permissionLogs, dataAccessLogs] = await Promise.all([
        auditService.getPermissionAuditLogs({
          companyId: req.user!.company_id,
          ...filters,
        }),
        auditService.getDataAccessAuditLogs({
          companyId: req.user!.company_id,
          entityId: resourceId,
          ...filters,
        }),
      ]);

      // Filter permission logs by resource
      const filteredPermissionLogs = {
        ...permissionLogs,
        logs: permissionLogs.logs.filter(log => 
          log.resource_type === resourceType && log.resource_id === resourceId
        ),
      };

      res.json({
        permissionLogs: filteredPermissionLogs,
        dataAccessLogs,
      });
    } catch (error) {
      console.error('Error fetching resource audit logs:', error);
      res.status(500).json({ message: 'Failed to fetch resource audit logs' });
    }
  }
);

// Export audit data (for compliance purposes)
router.get('/export',
  requirePermission('audit_export'),
  auditAdminOperation('audit_export', 'export_audit_data'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const filters = auditFiltersSchema.parse(req.query);
      
      if (!filters.startDate || !filters.endDate) {
        return res.status(400).json({ 
          message: 'Start date and end date are required for audit export' 
        });
      }

      const [permissionLogs, dataAccessLogs, changeLogs] = await Promise.all([
        auditService.getPermissionAuditLogs({
          companyId: req.user!.company_id,
          ...filters,
          limit: 10000, // Large limit for export
        }),
        auditService.getDataAccessAuditLogs({
          companyId: req.user!.company_id,
          ...filters,
          limit: 10000,
        }),
        auditService.getPermissionChangeLogs({
          companyId: req.user!.company_id,
          ...filters,
          limit: 10000,
        }),
      ]);

      const exportData = {
        exportedAt: new Date().toISOString(),
        exportedBy: req.user!.id,
        companyId: req.user!.company_id,
        dateRange: {
          startDate: filters.startDate,
          endDate: filters.endDate,
        },
        data: {
          permissionLogs,
          dataAccessLogs,
          changeLogs,
        },
      };

      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="audit-export-${Date.now()}.json"`);
      res.json(exportData);
    } catch (error) {
      console.error('Error exporting audit data:', error);
      res.status(500).json({ message: 'Failed to export audit data' });
    }
  }
);

export default router;
