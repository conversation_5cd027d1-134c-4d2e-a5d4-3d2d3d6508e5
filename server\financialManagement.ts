import { db } from './db';
import {
  accounts, transactions, accountBalances, accountingPeriods,
  shareholders, shareholdings, investmentTransactions,
  balanceSheets, balanceSheetItems, fixedAssets, depreciationSchedules,
  loans, collections, payments, expenses, fines, companies,
  type Account, type InsertAccount, type Transaction, type InsertTransaction,
  type AccountBalance, type InsertAccountBalance, type AccountingPeriod, type InsertAccountingPeriod,
  type Shareholder, type InsertShareholder, type Shareholding, type InsertShareholding,
  type InvestmentTransaction, type InsertInvestmentTransaction,
  type BalanceSheet, type InsertBalanceSheet, type BalanceSheetItem, type InsertBalanceSheetItem,
  type FixedAsset, type InsertFixedAsset, type DepreciationSchedule, type InsertDepreciationSchedule
} from '@shared/schema';
import { eq, and, desc, sql, lte, gte, lt, gt, or, like, inArray, count } from 'drizzle-orm';
import errorLogger from './utils/errorLogger';
import { DEFAULT_SYSTEM_ACCOUNTS, SYSTEM_ACCOUNT_CODES } from './config/systemAccounts';
import { TransactionStorage } from './storage/financial/transaction.storage';

// This file implements the financial management functions needed for the IStorage interface
// These are exported as named functions that can be used by the MemStorage class

// Helper to get company name from company_id for generating prefixes
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);

    let prefix = '';
    if (words.length === 0) {
      prefix = `C${companyId}`;
    } else if (words.length === 1) {
      // If only one word, use first and last letter of that word
      const word = words[0];
      prefix = word.length > 1
        ? (word.charAt(0) + word.charAt(word.length - 1)).toUpperCase()
        : word.toUpperCase() + companyId;
    } else {
      // If multiple words, use first letter of first word and first letter of last word
      // This handles cases like "GOVINDARAJI S" correctly
      prefix = (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }

    console.log(`Generated prefix: "${prefix}" for company name: "${fullName}"`);
    return prefix;
  } catch (error) {
    console.error(`Error fetching company name for ID ${companyId}:`, error);
    // Return a fallback value in case of error
    return `C${companyId}`;
  }
}

// Financial Transaction Validation
/**
 * Validates financial transaction data to ensure it adheres to double-entry accounting principles
 * @param entries Record of debit and credit entries to validate
 * @returns { valid: boolean, message?: string, totalDebits: number, totalCredits: number }
 */
export function validateTransaction(entries: Record<string, { account_id: number, amount: number }>): {
  valid: boolean,
  message?: string,
  totalDebits: number,
  totalCredits: number
} {
  // Extract debit and credit entries
  const debitEntries: { account_id: number, amount: number }[] = [];
  const creditEntries: { account_id: number, amount: number }[] = [];

  // Categorize the entries based on key names
  Object.entries(entries).forEach(([key, entry]) => {
    if (key === 'debit' || key.startsWith('debit')) {
      debitEntries.push(entry);
    } else if (key === 'credit' || key.startsWith('credit')) {
      creditEntries.push(entry);
    }
  });

  // Calculate total debits and credits
  const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
  const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);

  // Check if entries exist
  if (debitEntries.length === 0 || creditEntries.length === 0) {
    return {
      valid: false,
      message: "Transaction must have at least one debit and one credit entry",
      totalDebits,
      totalCredits
    };
  }

  // Check for negative values
  const hasNegativeDebit = debitEntries.some(entry => entry.amount < 0);
  const hasNegativeCredit = creditEntries.some(entry => entry.amount < 0);

  if (hasNegativeDebit || hasNegativeCredit) {
    return {
      valid: false,
      message: "Transaction entries cannot have negative amounts",
      totalDebits,
      totalCredits
    };
  }

  // Check if totals match (accounting equation: debits = credits)
  const difference = Math.abs(totalDebits - totalCredits);
  if (difference > 0.01) { // Allow for small rounding differences
    return {
      valid: false,
      message: `Transaction is unbalanced: Total debits (${totalDebits.toFixed(2)}) do not equal total credits (${totalCredits.toFixed(2)})`,
      totalDebits,
      totalCredits
    };
  }

  return {
    valid: true,
    totalDebits,
    totalCredits
  };
}

// Account Management
export async function getAccountById(id: number, companyId: number): Promise<Account | undefined> {
  try {
    const [account] = await db.select()
      .from(accounts)
      .where(and(
        eq(accounts.id, id),
        eq(accounts.company_id, companyId)
      ));
    return account;
  } catch (error) {
    errorLogger.logError(`Failed to get account ${id}`, 'account-fetch', error as Error);
    return undefined;
  }
}

export async function getAccountsByCompany(companyId: number): Promise<Account[]> {
  try {
    return await db.select()
      .from(accounts)
      .where(eq(accounts.company_id, companyId))
      .orderBy(accounts.account_name);
  } catch (error) {
    errorLogger.logError(`Failed to get accounts for company ${companyId}`, 'accounts-fetch', error as Error);
    return [];
  }
}

export async function getAccountHierarchy(companyId: number): Promise<Account[]> {
  try {
    // In a real implementation, this would return accounts in a hierarchical structure
    return await getAccountsByCompany(companyId);
  } catch (error) {
    errorLogger.logError(`Failed to get account hierarchy for company ${companyId}`, 'account-hierarchy-fetch', error as Error);
    return [];
  }
}

export async function createAccount(account: InsertAccount): Promise<Account> {
  try {
    const [newAccount] = await db
      .insert(accounts)
      .values(account)
      .returning();
    return newAccount;
  } catch (error) {
    errorLogger.logError('Failed to create account', 'account-creation', error as Error);
    throw error;
  }
}

export async function updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account | undefined> {
  try {
    // Check if it's a system account before allowing updates to core properties
    const [existingAccount] = await db.select()
      .from(accounts)
      .where(and(
        eq(accounts.id, id),
        eq(accounts.company_id, companyId)
      ));

    if (!existingAccount) {
      return undefined; // Account not found
    }

    // For system accounts, prevent changes to critical properties
    if (existingAccount.is_system) {
      // Create a safe update object that won't modify protected properties
      const safeUpdate: Partial<InsertAccount> = {};

      // Only allow specific properties to be updated for system accounts
      if (account.description !== undefined) safeUpdate.description = account.description;
      if (account.is_active !== undefined) safeUpdate.is_active = account.is_active;

      // Updated timestamp always gets updated
      safeUpdate.updated_at = new Date();

      // Update with restricted fields
      const [updatedAccount] = await db
        .update(accounts)
        .set(safeUpdate)
        .where(and(
          eq(accounts.id, id),
          eq(accounts.company_id, companyId)
        ))
        .returning();

      return updatedAccount;
    } else {
      // For non-system accounts, allow full updates
      const [updatedAccount] = await db
        .update(accounts)
        .set({ ...account, updated_at: new Date() })
        .where(and(
          eq(accounts.id, id),
          eq(accounts.company_id, companyId)
        ))
        .returning();

      return updatedAccount;
    }
  } catch (error) {
    errorLogger.logError(`Failed to update account ${id}`, 'account-update', error as Error);
    return undefined;
  }
}

/**
 * Initializes the default system accounts for a new company
 * @param companyId The ID of the company to initialize accounts for
 * @param userId Optional user ID who is initializing the accounts
 * @returns Promise<void>
 */
export async function initializeSystemAccounts(companyId: number, userId?: number): Promise<void> {
  try {
    // Check if any accounts already exist for this company
    const existingAccounts = await getAccountsByCompany(companyId);
    if (existingAccounts.length > 0) {
      // Company already has accounts, skip initialization
      errorLogger.logInfo(`Company ${companyId} already has accounts, skipping initialization`, 'system-accounts-init');
      return;
    }

    // Create default system accounts
    const createdAccounts: Account[] = [];
    for (const accountData of DEFAULT_SYSTEM_ACCOUNTS) {
      try {
        const account = await createAccount({
          ...accountData,
          company_id: companyId,
          description: accountData.description || `System ${accountData.account_type} account`,
        });
        createdAccounts.push(account);
      } catch (err) {
        console.error(`Error creating account ${accountData.account_code} - ${accountData.account_name}:`, err);
        errorLogger.logError(`Failed to create account ${accountData.account_code}`, 'system-accounts-init', err as Error);
        // Continue with the next account instead of failing the whole process
      }
    }

    // Create an initial accounting period
    const currentDate = new Date();
    const fiscalYearStart = new Date(currentDate.getFullYear(), 0, 1); // Jan 1st of current year
    const fiscalYearEnd = new Date(currentDate.getFullYear(), 11, 31); // Dec 31st of current year

    try {
      await createAccountingPeriod({
        company_id: companyId,
        period_name: `Fiscal Year ${currentDate.getFullYear()}`,
        start_date: fiscalYearStart,
        end_date: fiscalYearEnd,
        status: 'active',
        closed_by: null,
        closed_at: null
      });

      // Log successful creation of accounts
      console.log(`Created ${createdAccounts.length} system accounts for company ${companyId}`);

      // Log the critical accounts to verify they were created
      const cashAccount = createdAccounts.find(a => a.account_code === SYSTEM_ACCOUNT_CODES.CASH);
      const bankAccount = createdAccounts.find(a => a.account_code === SYSTEM_ACCOUNT_CODES.BANK);
      const loanReceivableAccount = createdAccounts.find(a => a.account_code === SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE);

      console.log("Critical accounts created:");
      console.log(`  Cash: ${cashAccount ? `ID ${cashAccount.id}` : 'Not created'}`);
      console.log(`  Bank: ${bankAccount ? `ID ${bankAccount.id}` : 'Not created'}`);
      console.log(`  Loan Receivable: ${loanReceivableAccount ? `ID ${loanReceivableAccount.id}` : 'Not created'}`);
    } catch (periodError) {
      console.error("Error creating accounting period:", periodError);
      // We'll still continue since the accounts were created successfully
    }

    errorLogger.logInfo(`Initialized ${createdAccounts.length} system accounts for company ${companyId}`, 'system-accounts-init');
  } catch (error) {
    errorLogger.logError(`Failed to initialize system accounts for company ${companyId}`, 'system-accounts-init', error as Error);
    throw error;
  }
}

/**
 * Finds an account by its code for a specific company
 * Enhanced to be more flexible in finding accounts by name if code is not found
 *
 * @param companyId The ID of the company
 * @param accountCode The account code to search for
 * @param fallbackName Optional fallback name to search for if code is not found
 * @returns Promise<Account | undefined>
 */
export async function getAccountByCode(companyId: number, accountCode: string, fallbackName?: string): Promise<Account | undefined> {
  try {
    console.log(`Looking for account with code '${accountCode}' for company ${companyId} (fallback: ${fallbackName || 'none'})`);

    // First try to find by exact code match
    const [accountByCode] = await db.select()
      .from(accounts)
      .where(and(
        eq(accounts.company_id, companyId),
        eq(accounts.account_code, accountCode)
      ));

    if (accountByCode) {
      console.log(`Found account by code: ${accountByCode.account_name} (ID: ${accountByCode.id})`);
      return accountByCode;
    }

    console.log(`No account found with code '${accountCode}', trying fallback methods`);

    // If no account found by code, try to find by name pattern
    // This helps with variations in naming (Loan Receivable vs Loans Receivable)
    if (fallbackName) {
      const accountsByName = await db.select()
        .from(accounts)
        .where(and(
          eq(accounts.company_id, companyId),
          like(accounts.account_name, `%${fallbackName}%`)
        ));

      if (accountsByName && accountsByName.length > 0) {
        // Return the first match
        return accountsByName[0];
      }
    }

    // If still no account found, look for accounts with similar purposes
    // based on account types
    if (accountCode === SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE) {
      // Try to find any receivable account
      const [receivableAccount] = await db.select()
        .from(accounts)
        .where(and(
          eq(accounts.company_id, companyId),
          eq(accounts.account_type, 'asset'),
          or(
            like(accounts.account_name, '%Receivable%'),
            like(accounts.account_name, '%Loan%')
          )
        ));

      if (receivableAccount) {
        return receivableAccount;
      }
    } else if (accountCode === SYSTEM_ACCOUNT_CODES.CASH || accountCode === SYSTEM_ACCOUNT_CODES.BANK) {
      // Try to find any cash or bank account
      const [cashOrBankAccount] = await db.select()
        .from(accounts)
        .where(and(
          eq(accounts.company_id, companyId),
          eq(accounts.account_type, 'asset'),
          or(
            like(accounts.account_name, '%Cash%'),
            like(accounts.account_name, '%Bank%')
          )
        ));

      if (cashOrBankAccount) {
        return cashOrBankAccount;
      }
    }

    // Nothing found
    return undefined;
  } catch (error) {
    errorLogger.logError(`Failed to get account with code ${accountCode}`, 'account-code-fetch', error as Error);
    return undefined;
  }
}

export async function deleteAccount(id: number, companyId: number): Promise<boolean> {
  try {
    // First check if it's a system account
    const [account] = await db.select()
      .from(accounts)
      .where(and(
        eq(accounts.id, id),
        eq(accounts.company_id, companyId)
      ));

    if (account && account.is_system) {
      return false; // Cannot delete system accounts
    }

    // Check if account has transactions
    const [transactionCount] = await db.select({ count: db.fn.count() })
      .from(transactions)
      .where(and(
        eq(transactions.account_id, id),
        eq(transactions.company_id, companyId)
      ));

    if (transactionCount && Number(transactionCount.count) > 0) {
      return false; // Cannot delete account with transactions
    }

    // Check for child accounts
    const [childCount] = await db.select({ count: db.fn.count() })
      .from(accounts)
      .where(and(
        eq(accounts.parent_account_id, id),
        eq(accounts.company_id, companyId)
      ));

    if (childCount && Number(childCount.count) > 0) {
      return false; // Cannot delete account with child accounts
    }

    // Now safe to delete
    await db
      .delete(accounts)
      .where(and(
        eq(accounts.id, id),
        eq(accounts.company_id, companyId)
      ));

    return true;
  } catch (error) {
    errorLogger.logError(`Failed to delete account ${id}`, 'account-deletion', error as Error);
    return false;
  }
}

// Transaction Management
/**
 * Create a journal entry for a financial transaction
 * This enforces double-entry accounting principles by requiring
 * equal and opposite account entries that balance to zero
 *
 * @param journalEntry Object containing transaction data and entries
 * @returns Promise<{debitTransaction: Transaction, creditTransaction: Transaction}>
 */
export async function createJournalEntry(journalEntry: {
  company_id: number;
  branch_id?: number | null;
  transaction_date: Date;
  description: string;
  reference_type: 'loan' | 'collection' | 'expense' | 'investment' | 'withdrawal' | 'transfer' | 'adjustment';
  reference_id: number;
  created_by?: number;
  entries: Record<string, {
    account_id: number;
    amount: number;
  }>;
}): Promise<Transaction[]> {
  try {
    console.log("==== START JOURNAL ENTRY CREATION ====");
    console.log("Full journal entry data:", JSON.stringify(journalEntry, null, 2));

    // Validate required fields
    if (!journalEntry.company_id) {
      console.error("ERROR: Journal entry missing company_id");
      throw new Error("Journal entry must have a company_id");
    }

    if (!journalEntry.transaction_date) {
      console.log("No transaction date provided, using current date");
      journalEntry.transaction_date = new Date();
    }

    if (!journalEntry.description) {
      console.error("ERROR: Journal entry missing description");
      throw new Error("Journal entry must have a description");
    }

    if (!journalEntry.reference_id) {
      console.error("ERROR: Journal entry missing reference_id");
      throw new Error("Journal entry must have a reference_id");
    }

    if (!journalEntry.entries || Object.keys(journalEntry.entries).length === 0) {
      console.error("ERROR: Journal entry has no entries");
      throw new Error("Journal entry must have at least one entry");
    }

    console.log("Creating journal entry with data:", {
      company_id: journalEntry.company_id,
      description: journalEntry.description,
      reference_type: journalEntry.reference_type,
      reference_id: journalEntry.reference_id,
      transaction_date: journalEntry.transaction_date,
      entriesCount: Object.keys(journalEntry.entries).length
    });

    // Generate company-specific transaction reference codes
    const companyPrefix = await getCompanyName(journalEntry.company_id);
    console.log(`Generated company prefix for transaction reference codes: ${companyPrefix}`);

    // Get the transaction storage instance for reference code generation
    const transactionStorage = new TransactionStorage();

    // Validate the transaction entries
    const validationResult = validateTransaction(journalEntry.entries);

    if (!validationResult.valid) {
      throw new Error(validationResult.message || "Invalid journal entry");
    }

    // Extract all debit and credit entries
    const debitEntries: { account_id: number, amount: number }[] = [];
    const creditEntries: { account_id: number, amount: number }[] = [];

    // Process entries based on key names
    Object.entries(journalEntry.entries).forEach(([key, entry]) => {
      if (!entry || !entry.account_id || entry.amount === undefined) {
        throw new Error(`Invalid entry for key ${key}: account_id and amount are required`);
      }

      console.log(`Processing journal entry ${key}:`, entry);

      if (key === 'debit' || key.startsWith('debit')) {
        debitEntries.push(entry);
      } else if (key === 'credit' || key.startsWith('credit')) {
        creditEntries.push(entry);
      } else {
        console.warn(`Unrecognized journal entry key: ${key}`);
      }
    });

    console.log(`Processing ${debitEntries.length} debit entries and ${creditEntries.length} credit entries`);

    const transactionResults: Transaction[] = [];

    // Create and insert debit transactions
    for (const entry of debitEntries) {
      // Validate entry data
      if (!entry.account_id) {
        throw new Error("Debit entry must have an account_id");
      }

      if (!entry.amount) {
        throw new Error("Debit entry must have an amount");
      }

      // Generate transaction reference code for this debit transaction
      const highestSerial = await transactionStorage.getHighestTransactionSerial(
        journalEntry.company_id,
        `${companyPrefix}-T-`
      );
      const nextSerial = highestSerial + 1;
      const serialString = nextSerial.toString().padStart(3, '0');
      const transactionReferenceCode = `${companyPrefix}-T-${serialString}`;

      console.log(`Generated transaction reference code: ${transactionReferenceCode} for debit transaction`);

      const debitTransaction: InsertTransaction = {
        company_id: journalEntry.company_id,
        branch_id: journalEntry.branch_id,
        account_id: entry.account_id,
        transaction_date: journalEntry.transaction_date,
        transaction_type: 'debit',
        amount: String(entry.amount),
        reference_type: journalEntry.reference_type, // Using proper type instead of 'as any'
        reference_id: journalEntry.reference_id,
        description: journalEntry.description,
        transaction_reference_code: transactionReferenceCode,
        created_by: journalEntry.created_by,
      };

      console.log("Creating debit transaction:", {
        company_id: debitTransaction.company_id,
        account_id: debitTransaction.account_id,
        amount: debitTransaction.amount,
        transaction_type: debitTransaction.transaction_type
      });

      try {
        // Import 'transactions' refers to the database table, not the local array
        const [debit] = await db
          .insert(transactions)
          .values(debitTransaction)
          .returning();

        transactionResults.push(debit);
      } catch (insertError) {
        console.error("Error inserting debit transaction:", insertError);
        throw new Error(`Failed to insert debit transaction: ${(insertError as Error).message}`);
      }
    }

    // Create and insert credit transactions
    for (const entry of creditEntries) {
      // Validate entry data
      if (!entry.account_id) {
        throw new Error("Credit entry must have an account_id");
      }

      if (!entry.amount) {
        throw new Error("Credit entry must have an amount");
      }

      // Generate transaction reference code for this credit transaction
      const highestSerial = await transactionStorage.getHighestTransactionSerial(
        journalEntry.company_id,
        `${companyPrefix}-T-`
      );
      const nextSerial = highestSerial + 1;
      const serialString = nextSerial.toString().padStart(3, '0');
      const transactionReferenceCode = `${companyPrefix}-T-${serialString}`;

      console.log(`Generated transaction reference code: ${transactionReferenceCode} for credit transaction`);

      const creditTransaction: InsertTransaction = {
        company_id: journalEntry.company_id,
        branch_id: journalEntry.branch_id,
        account_id: entry.account_id,
        transaction_date: journalEntry.transaction_date,
        transaction_type: 'credit',
        amount: String(entry.amount),
        reference_type: journalEntry.reference_type, // Using proper type instead of 'as any'
        reference_id: journalEntry.reference_id,
        description: journalEntry.description,
        transaction_reference_code: transactionReferenceCode,
        created_by: journalEntry.created_by,
      };

      console.log("Creating credit transaction:", {
        company_id: creditTransaction.company_id,
        account_id: creditTransaction.account_id,
        amount: creditTransaction.amount,
        transaction_type: creditTransaction.transaction_type
      });

      try {
        // Import 'transactions' refers to the database table, not the local array
        const [credit] = await db
          .insert(transactions)
          .values(creditTransaction)
          .returning();

        transactionResults.push(credit);
      } catch (insertError) {
        console.error("Error inserting credit transaction:", insertError);
        throw new Error(`Failed to insert credit transaction: ${(insertError as Error).message}`);
      }
    }

    console.log(`Journal entry creation successful, created ${transactionResults.length} transaction records`);
    console.log("Transaction IDs created:", transactionResults.map(t => t.id).join(', '));
    console.log("==== END JOURNAL ENTRY CREATION ====");
    return transactionResults;
  } catch (error) {
    console.error("==== JOURNAL ENTRY CREATION FAILED ====");
    console.error("Failed to create journal entry:", (error as Error).message);
    console.error("Error stack:", (error as Error).stack);
    errorLogger.logError('Failed to create journal entry', 'journal-entry-creation', error as Error);
    throw error;
  }
}

export async function createTransaction(transaction: InsertTransaction): Promise<Transaction> {
  try {
    // Generate transaction reference code if not provided
    if (!transaction.transaction_reference_code && transaction.company_id) {
      const companyPrefix = await getCompanyName(transaction.company_id);
      console.log(`Generated company prefix for transaction reference code: ${companyPrefix}`);

      const transactionStorage = new TransactionStorage();
      const highestSerial = await transactionStorage.getHighestTransactionSerial(
        transaction.company_id,
        `${companyPrefix}-T-`
      );
      const nextSerial = highestSerial + 1;
      const serialString = nextSerial.toString().padStart(3, '0');
      const transactionReferenceCode = `${companyPrefix}-T-${serialString}`;

      console.log(`Generated transaction reference code: ${transactionReferenceCode} for individual transaction`);

      // Add the reference code to the transaction
      transaction.transaction_reference_code = transactionReferenceCode;
    }

    const [newTransaction] = await db
      .insert(transactions)
      .values(transaction)
      .returning();
    return newTransaction;
  } catch (error) {
    errorLogger.logError('Failed to create transaction', 'transaction-creation', error as Error);
    throw error;
  }
}

export async function getTransaction(id: number, companyId: number): Promise<Transaction | undefined> {
  try {
    const [transaction] = await db.select()
      .from(transactions)
      .where(and(
        eq(transactions.id, id),
        eq(transactions.company_id, companyId)
      ));

    if (!transaction) {
      return undefined;
    }

    // If the transaction references a collection, fetch the collection reference code
    if (transaction.reference_type === 'collection' && transaction.reference_id) {
      try {
        const [collection] = await db.select({
          company_collection_string: collections.company_collection_string
        })
          .from(collections)
          .where(and(
            eq(collections.id, transaction.reference_id),
            eq(collections.company_id, companyId)
          ));

        if (collection && collection.company_collection_string) {
          // Add the collection reference code to the transaction object
          (transaction as any).collection_reference = collection.company_collection_string;
        }
      } catch (collectionError) {
        errorLogger.logError(`Failed to fetch collection reference for transaction ${id}`, 'collection-reference-fetch', collectionError as Error);
        // Don't fail the whole request if collection fetch fails
      }
    }

    // If the transaction references a loan, fetch the loan reference code
    if (transaction.reference_type === 'loan' && transaction.reference_id) {
      try {
        const [loan] = await db.select({
          loan_reference_code: loans.loan_reference_code
        })
          .from(loans)
          .where(and(
            eq(loans.id, transaction.reference_id),
            eq(loans.company_id, companyId)
          ));

        if (loan && loan.loan_reference_code) {
          // Add the loan reference code to the transaction object
          (transaction as any).loan_reference = loan.loan_reference_code;
        }
      } catch (loanError) {
        errorLogger.logError(`Failed to fetch loan reference for transaction ${id}`, 'loan-reference-fetch', loanError as Error);
        // Don't fail the whole request if loan fetch fails
      }
    }

    return transaction;
  } catch (error) {
    errorLogger.logError(`Failed to get transaction ${id}`, 'transaction-fetch', error as Error);
    return undefined;
  }
}

export async function getTransactionsByCompany(
  companyId: number,
  options?: {
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    accountType?: string;
    transactionType?: string;
    referenceType?: string;
    searchTerm?: string;
  }
): Promise<{ transactions: Transaction[], totalCount: number }> {
  try {
    const {
      startDate,
      endDate,
      page = 1,
      limit = 10,
      accountType,
      transactionType,
      referenceType,
      searchTerm
    } = options || {};

    // Calculate offset based on page and limit
    const offset = (page - 1) * limit;

    // Build the WHERE conditions
    const whereConditions = [eq(transactions.company_id, companyId)];

    // Add date filters if provided
    if (startDate) {
      whereConditions.push(gte(transactions.transaction_date, new Date(startDate)));
    }

    if (endDate) {
      whereConditions.push(lte(transactions.transaction_date, new Date(endDate)));
    }

    // Add transaction type filter if provided
    if (transactionType && transactionType !== 'all_transaction_types') {
      whereConditions.push(eq(transactions.transaction_type, transactionType));
    }

    // Add reference type filter if provided
    if (referenceType && referenceType !== 'all_reference_types') {
      whereConditions.push(eq(transactions.reference_type, referenceType));
    }

    // Add account type filter conditions - we need to join with accounts table
    let joinedAccountConditions = [];
    if (accountType && accountType !== 'all_account_types') {
      joinedAccountConditions.push(eq(accounts.account_type, accountType));
    }

    // Add search term condition - perform on database level for proper pagination
    let searchConditions = [];
    if (searchTerm && searchTerm.trim() !== '') {
      const lowerSearchTerm = `%${searchTerm.toLowerCase()}%`;
      searchConditions = [
        sql`LOWER(${transactions.description}) LIKE ${lowerSearchTerm}`,
        sql`LOWER(${accounts.account_name}) LIKE ${lowerSearchTerm}`,
        sql`LOWER(${accounts.account_code}) LIKE ${lowerSearchTerm}`,
        sql`LOWER(${transactions.reference_type}) LIKE ${lowerSearchTerm}`
      ];
    }

    // Combine all conditions
    let allConditions = [...whereConditions];
    if (joinedAccountConditions.length > 0) {
      allConditions = [...allConditions, ...joinedAccountConditions];
    }

    // Build the count query with all necessary conditions
    let countQuery = db
      .select({ count: count() })
      .from(transactions)
      .leftJoin(accounts, eq(transactions.account_id, accounts.id))
      .where(and(...allConditions));

    // Add search conditions to count query if they exist
    if (searchConditions.length > 0) {
      countQuery = countQuery.where(sql`(${searchConditions.join(' OR ')})`);
    }

    // Execute count query to get total records matching filters
    const [countResult] = await countQuery;
    const totalCount = Number(countResult?.count || 0);

    // Build main query with all necessary conditions
    let query = db
      .select({
        transaction: transactions,
        account: {
          id: accounts.id,
          account_code: accounts.account_code,
          account_name: accounts.account_name,
          account_type: accounts.account_type
        }
      })
      .from(transactions)
      .leftJoin(accounts, eq(transactions.account_id, accounts.id))
      .where(and(...allConditions))
      .orderBy(desc(transactions.transaction_date))
      .limit(limit)
      .offset(offset);

    // Add search conditions to main query if they exist
    if (searchConditions.length > 0) {
      query = query.where(sql`(${searchConditions.join(' OR ')})`);
    }

    // Execute the query
    const results = await query;

    // Transform the results to the expected format
    const transformedTransactions = results.map(row => ({
      ...row.transaction,
      account: row.account.id ? row.account : undefined
    }));

    return {
      transactions: transformedTransactions,
      totalCount
    };
  } catch (error) {
    errorLogger.logError(`Failed to get transactions for company ${companyId}`, 'transactions-fetch', error as Error);
    return { transactions: [], totalCount: 0 };
  }
}

export async function getTransactionsByAccount(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
  try {
    // Use raw SQL query to avoid Drizzle ORM issues
    const result = await db.execute(sql`
      SELECT
        t.*,
        l.loan_reference_code as loan_reference,
        c.company_collection_string as collection_reference
      FROM transactions t
      LEFT JOIN loans l ON (t.reference_type = 'loan' AND t.reference_id = l.id AND l.company_id = ${companyId})
      LEFT JOIN collections c ON (t.reference_type = 'collection' AND t.reference_id = c.id AND c.company_id = ${companyId})
      WHERE t.account_id = ${accountId} AND t.company_id = ${companyId}
      ${startDate ? sql`AND t.transaction_date >= ${startDate}` : sql``}
      ${endDate ? sql`AND t.transaction_date <= ${endDate}` : sql``}
      ORDER BY t.transaction_date DESC
    `);
    return result.rows as any[];
  } catch (error) {
    errorLogger.logError(`Failed to get transactions for account ${accountId}`, 'account-transactions-fetch', error as Error);
    return [];
  }
}

export async function updateTransaction(id: number, companyId: number, transaction: Partial<InsertTransaction>): Promise<Transaction | undefined> {
  try {
    const [updatedTransaction] = await db
      .update(transactions)
      .set({ ...transaction, updated_at: new Date() })
      .where(and(
        eq(transactions.id, id),
        eq(transactions.company_id, companyId)
      ))
      .returning();
    return updatedTransaction;
  } catch (error) {
    errorLogger.logError(`Failed to update transaction ${id}`, 'transaction-update', error as Error);
    return undefined;
  }
}

export async function deleteTransaction(id: number, companyId: number): Promise<boolean> {
  try {
    // Delete the transaction
    const result = await db
      .delete(transactions)
      .where(and(
        eq(transactions.id, id),
        eq(transactions.company_id, companyId)
      ))
      .returning({ id: transactions.id });

    // Check if any row was deleted
    return result.length > 0;
  } catch (error) {
    errorLogger.logError(`Failed to delete transaction ${id}`, 'transaction-deletion', error as Error);
    return false;
  }
}

// Account Balance Management
export async function createAccountBalance(balance: InsertAccountBalance): Promise<AccountBalance> {
  try {
    const [newBalance] = await db
      .insert(accountBalances)
      .values(balance)
      .returning();
    return newBalance;
  } catch (error) {
    errorLogger.logError('Failed to create account balance', 'account-balance-creation', error as Error);
    throw error;
  }
}

export async function getAccountBalances(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<AccountBalance[]> {
  try {
    let query = db.select()
      .from(accountBalances)
      .where(and(
        eq(accountBalances.account_id, accountId),
        eq(accountBalances.company_id, companyId)
      ))
      .orderBy(desc(accountBalances.balance_date));

    // Add date filters if provided
    if (startDate) {
      query = query.where(accountBalances.balance_date >= new Date(startDate));
    }

    if (endDate) {
      query = query.where(accountBalances.balance_date <= new Date(endDate));
    }

    return await query;
  } catch (error) {
    errorLogger.logError(`Failed to get balances for account ${accountId}`, 'account-balances-fetch', error as Error);
    return [];
  }
}

export async function getLatestAccountBalance(accountId: number, companyId: number): Promise<AccountBalance | undefined> {
  try {
    const [balance] = await db.select()
      .from(accountBalances)
      .where(and(
        eq(accountBalances.account_id, accountId),
        eq(accountBalances.company_id, companyId)
      ))
      .orderBy(desc(accountBalances.balance_date))
      .limit(1);

    return balance;
  } catch (error) {
    errorLogger.logError(`Failed to get latest balance for account ${accountId}`, 'account-latest-balance-fetch', error as Error);
    return undefined;
  }
}

export async function reconcileAccountBalance(id: number, userId: number, companyId: number): Promise<AccountBalance | undefined> {
  try {
    const [reconciledBalance] = await db
      .update(accountBalances)
      .set({
        reconciled: true,
        reconciled_by: userId,
        reconciled_at: new Date(),
        updated_at: new Date()
      })
      .where(and(
        eq(accountBalances.id, id),
        eq(accountBalances.company_id, companyId)
      ))
      .returning();

    return reconciledBalance;
  } catch (error) {
    errorLogger.logError(`Failed to reconcile balance ${id}`, 'account-balance-reconciliation', error as Error);
    return undefined;
  }
}

// Accounting Period Management
export async function createAccountingPeriod(period: InsertAccountingPeriod): Promise<AccountingPeriod> {
  try {
    const [newPeriod] = await db
      .insert(accountingPeriods)
      .values(period)
      .returning();
    return newPeriod;
  } catch (error) {
    errorLogger.logError('Failed to create accounting period', 'accounting-period-creation', error as Error);
    throw error;
  }
}

export async function getAccountingPeriods(companyId: number): Promise<AccountingPeriod[]> {
  try {
    return await db.select()
      .from(accountingPeriods)
      .where(eq(accountingPeriods.company_id, companyId))
      .orderBy(desc(accountingPeriods.end_date));
  } catch (error) {
    errorLogger.logError(`Failed to get accounting periods for company ${companyId}`, 'accounting-periods-fetch', error as Error);
    return [];
  }
}

export async function getCurrentAccountingPeriod(companyId: number): Promise<AccountingPeriod | undefined> {
  try {
    const today = new Date();

    const [period] = await db.select()
      .from(accountingPeriods)
      .where(and(
        eq(accountingPeriods.company_id, companyId),
        eq(accountingPeriods.status, 'active'),
        accountingPeriods.start_date <= today,
        accountingPeriods.end_date >= today
      ))
      .limit(1);

    return period;
  } catch (error) {
    errorLogger.logError(`Failed to get current accounting period for company ${companyId}`, 'current-accounting-period-fetch', error as Error);
    return undefined;
  }
}

export async function closeAccountingPeriod(id: number, userId: number, companyId: number): Promise<AccountingPeriod | undefined> {
  try {
    const [closedPeriod] = await db
      .update(accountingPeriods)
      .set({
        status: 'closed',
        closed_by: userId,
        closed_at: new Date(),
        updated_at: new Date()
      })
      .where(and(
        eq(accountingPeriods.id, id),
        eq(accountingPeriods.company_id, companyId),
        eq(accountingPeriods.status, 'active')
      ))
      .returning();

    return closedPeriod;
  } catch (error) {
    errorLogger.logError(`Failed to close accounting period ${id}`, 'accounting-period-closing', error as Error);
    return undefined;
  }
}

// Shareholder Management
export async function createShareholder(shareholder: InsertShareholder): Promise<Shareholder> {
  try {
    const [newShareholder] = await db
      .insert(shareholders)
      .values(shareholder)
      .returning();
    return newShareholder;
  } catch (error) {
    errorLogger.logError('Failed to create shareholder', 'shareholder-creation', error as Error);
    throw error;
  }
}

export async function getShareholder(id: number, companyId: number): Promise<Shareholder | undefined> {
  try {
    const [shareholder] = await db.select()
      .from(shareholders)
      .where(and(
        eq(shareholders.id, id),
        eq(shareholders.company_id, companyId)
      ));
    return shareholder;
  } catch (error) {
    errorLogger.logError(`Failed to get shareholder ${id}`, 'shareholder-fetch', error as Error);
    return undefined;
  }
}

export async function getShareholdersByCompany(companyId: number): Promise<Shareholder[]> {
  try {
    return await db.select()
      .from(shareholders)
      .where(eq(shareholders.company_id, companyId));
  } catch (error) {
    errorLogger.logError(`Failed to get shareholders for company ${companyId}`, 'shareholders-fetch', error as Error);
    return [];
  }
}

export async function updateShareholder(id: number, companyId: number, shareholder: Partial<InsertShareholder>): Promise<Shareholder | undefined> {
  try {
    const [updatedShareholder] = await db
      .update(shareholders)
      .set({ ...shareholder, updated_at: new Date() })
      .where(and(
        eq(shareholders.id, id),
        eq(shareholders.company_id, companyId)
      ))
      .returning();
    return updatedShareholder;
  } catch (error) {
    errorLogger.logError(`Failed to update shareholder ${id}`, 'shareholder-update', error as Error);
    return undefined;
  }
}

export async function createShareholding(shareholding: InsertShareholding): Promise<Shareholding> {
  try {
    const [newShareholding] = await db
      .insert(shareholdings)
      .values(shareholding)
      .returning();
    return newShareholding;
  } catch (error) {
    errorLogger.logError('Failed to create shareholding', 'shareholding-creation', error as Error);
    throw error;
  }
}

// Investment Management
export async function createInvestmentTransaction(transaction: InsertInvestmentTransaction): Promise<InvestmentTransaction> {
  try {
    const [newTransaction] = await db
      .insert(investmentTransactions)
      .values(transaction)
      .returning();
    return newTransaction;
  } catch (error) {
    errorLogger.logError('Failed to create investment transaction', 'investment-transaction-creation', error as Error);
    throw error;
  }
}

export async function getInvestmentTransactions(shareholderId: number): Promise<InvestmentTransaction[]> {
  try {
    return await db.select()
      .from(investmentTransactions)
      .where(eq(investmentTransactions.shareholder_id, shareholderId))
      .orderBy(desc(investmentTransactions.transaction_date));
  } catch (error) {
    errorLogger.logError(`Failed to get investment transactions for shareholder ${shareholderId}`, 'investment-transactions-fetch', error as Error);
    return [];
  }
}

// Fixed Asset Management
export async function createFixedAsset(asset: InsertFixedAsset): Promise<FixedAsset> {
  try {
    const [newAsset] = await db
      .insert(fixedAssets)
      .values(asset)
      .returning();
    return newAsset;
  } catch (error) {
    errorLogger.logError('Failed to create fixed asset', 'fixed-asset-creation', error as Error);
    throw error;
  }
}

export async function getFixedAssetsByCompany(companyId: number): Promise<FixedAsset[]> {
  try {
    return await db.select()
      .from(fixedAssets)
      .where(eq(fixedAssets.company_id, companyId));
  } catch (error) {
    errorLogger.logError(`Failed to get fixed assets for company ${companyId}`, 'fixed-assets-fetch', error as Error);
    return [];
  }
}

export async function updateFixedAsset(id: number, companyId: number, asset: Partial<InsertFixedAsset>): Promise<FixedAsset | undefined> {
  try {
    const [updatedAsset] = await db
      .update(fixedAssets)
      .set({ ...asset, updated_at: new Date() })
      .where(and(
        eq(fixedAssets.id, id),
        eq(fixedAssets.company_id, companyId)
      ))
      .returning();
    return updatedAsset;
  } catch (error) {
    errorLogger.logError(`Failed to update fixed asset ${id}`, 'fixed-asset-update', error as Error);
    return undefined;
  }
}

export async function recordDepreciation(assetId: number, amount: number, companyId: number): Promise<FixedAsset | undefined> {
  try {
    // Get the asset first
    const [asset] = await db.select()
      .from(fixedAssets)
      .where(and(
        eq(fixedAssets.id, assetId),
        eq(fixedAssets.company_id, companyId)
      ));

    if (!asset || asset.status !== 'active') {
      return undefined;
    }

    // Calculate depreciation
    const currentAccumulatedDepreciation = Number(asset.accumulated_depreciation || 0);
    const newAccumulatedDepreciation = currentAccumulatedDepreciation + Number(amount);

    // Update the asset
    const [updatedAsset] = await db
      .update(fixedAssets)
      .set({
        accumulated_depreciation: newAccumulatedDepreciation.toString(),
        updated_at: new Date()
      })
      .where(and(
        eq(fixedAssets.id, assetId),
        eq(fixedAssets.company_id, companyId)
      ))
      .returning();

    return updatedAsset;
  } catch (error) {
    errorLogger.logError(`Failed to record depreciation for asset ${assetId}`, 'depreciation-recording', error as Error);
    return undefined;
  }
}

export async function disposeFixedAsset(id: number, disposalAmount: number, disposalDate: Date, companyId: number): Promise<FixedAsset | undefined> {
  try {
    // Get the asset first
    const [asset] = await db.select()
      .from(fixedAssets)
      .where(and(
        eq(fixedAssets.id, id),
        eq(fixedAssets.company_id, companyId)
      ));

    if (!asset || asset.status !== 'active') {
      return undefined;
    }

    // Calculate book value and gain/loss
    const acquisitionCost = Number(asset.acquisition_cost || 0);
    const accumulatedDepreciation = Number(asset.accumulated_depreciation || 0);
    const bookValue = acquisitionCost - accumulatedDepreciation;
    const gainOrLoss = disposalAmount - bookValue;

    // Update the asset
    const [disposedAsset] = await db
      .update(fixedAssets)
      .set({
        status: 'disposed',
        disposal_date: disposalDate,
        disposal_amount: disposalAmount.toString(),
        gain_loss_on_disposal: gainOrLoss.toString(),
        updated_at: new Date()
      })
      .where(and(
        eq(fixedAssets.id, id),
        eq(fixedAssets.company_id, companyId)
      ))
      .returning();

    return disposedAsset;
  } catch (error) {
    errorLogger.logError(`Failed to dispose fixed asset ${id}`, 'fixed-asset-disposal', error as Error);
    return undefined;
  }
}

// Financial Reports
export async function getAccountStatement(companyId: number, accountId: number, startDate: string, endDate: string): Promise<{
  account: Account | undefined;
  openingBalance: number;
  closingBalance: number;
  transactions: Transaction[];
  formattedTransactions: {
    date: string;
    description: string;
    reference: string;
    debit: number;
    credit: number;
    balance: number;
  }[];
  summary: {
    totalDebits: number;
    totalCredits: number;
    netChange: number;
  };
}> {
  try {
    // Get the account details
    const account = await getAccountById(accountId, companyId);
    if (!account) {
      throw new Error(`Account ${accountId} not found for company ${companyId}`);
    }

    // Convert date strings to Date objects
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Format dates as YYYY-MM-DD for SQL query
    const formattedStartDate = startDateObj.toISOString().split('T')[0];
    const formattedEndDate = endDateObj.toISOString().split('T')[0];

    // Get all transactions before the start date to calculate opening balance
    const previousTransactions = await db.select()
      .from(transactions)
      .where(and(
        eq(transactions.company_id, companyId),
        eq(transactions.account_id, accountId),
        lt(transactions.transaction_date, new Date(formattedStartDate))
      ));

    // Calculate opening balance based on previous transactions
    let openingBalance = 0;
    for (const txn of previousTransactions) {
      if (txn.transaction_type === 'debit') {
        // For asset and expense accounts, debits increase balance
        if (account.account_type === 'asset' || account.account_type === 'expense') {
          openingBalance += Number(txn.amount);
        } else {
          // For liability, equity, and income accounts, debits decrease balance
          openingBalance -= Number(txn.amount);
        }
      } else if (txn.transaction_type === 'credit') {
        // For asset and expense accounts, credits decrease balance
        if (account.account_type === 'asset' || account.account_type === 'expense') {
          openingBalance -= Number(txn.amount);
        } else {
          // For liability, equity, and income accounts, credits increase balance
          openingBalance += Number(txn.amount);
        }
      }
    }

    // Get transactions for the requested period
    const periodTransactions = await getTransactionsByAccount(accountId, companyId, startDate, endDate);

    // Sort transactions by date
    periodTransactions.sort((a, b) =>
      new Date(a.transaction_date).getTime() - new Date(b.transaction_date).getTime()
    );

    // Calculate running balance and format transactions for display
    let runningBalance = openingBalance;
    let totalDebits = 0;
    let totalCredits = 0;

    const formattedTransactions = periodTransactions.map(txn => {
      const amount = Number(txn.amount);
      const isDebit = txn.transaction_type === 'debit';

      // Update totals
      if (isDebit) {
        totalDebits += amount;
      } else {
        totalCredits += amount;
      }

      // Update running balance
      if (isDebit) {
        // For asset and expense accounts, debits increase balance
        if (account.account_type === 'asset' || account.account_type === 'expense') {
          runningBalance += amount;
        } else {
          // For liability, equity, and income accounts, debits decrease balance
          runningBalance -= amount;
        }
      } else {
        // For asset and expense accounts, credits decrease balance
        if (account.account_type === 'asset' || account.account_type === 'expense') {
          runningBalance -= amount;
        } else {
          // For liability, equity, and income accounts, credits increase balance
          runningBalance += amount;
        }
      }

      // Return formatted transaction with running balance
      return {
        date: new Date(txn.transaction_date).toLocaleDateString(),
        description: txn.description || '',
        reference: `${txn.reference_type}/${txn.reference_id}`,
        debit: isDebit ? amount : 0,
        credit: !isDebit ? amount : 0,
        balance: runningBalance
      };
    });

    // Calculate net change
    const netChange = (account.account_type === 'asset' || account.account_type === 'expense')
      ? totalDebits - totalCredits
      : totalCredits - totalDebits;

    return {
      account,
      openingBalance,
      closingBalance: runningBalance,
      transactions: periodTransactions,
      formattedTransactions,
      summary: {
        totalDebits,
        totalCredits,
        netChange
      }
    };
  } catch (error) {
    errorLogger.logError(`Failed to generate account statement for account ${accountId}`, 'account-statement', error as Error);
    return {
      account: undefined,
      openingBalance: 0,
      closingBalance: 0,
      transactions: [],
      formattedTransactions: [],
      summary: {
        totalDebits: 0,
        totalCredits: 0,
        netChange: 0
      }
    };
  }
}

export async function getBalanceSheetReport(companyId: number, asOfDate: string): Promise<{
  companyId: number;
  asOfDate: string;
  reportDate: string;
  assets: {
    id: number;
    code: string;
    name: string;
    balance: number;
  }[];
  liabilities: {
    id: number;
    code: string;
    name: string;
    balance: number;
  }[];
  equity: {
    id: number;
    code: string;
    name: string;
    balance: number;
  }[];
  totalAssets: number;
  totalLiabilities: number;
  totalEquity: number;
  balanced: boolean;
}> {
  try {
    // Use the trial balance report to get all account balances
    const trialBalance = await getTrialBalanceReport(companyId, asOfDate);

    // Filter accounts by type
    const assets = trialBalance.accounts
      .filter(account => account.type === 'asset')
      .map(account => ({
        id: account.id,
        code: account.code,
        name: account.name,
        balance: account.balance
      }))
      .sort((a, b) => a.code.localeCompare(b.code));

    const liabilities = trialBalance.accounts
      .filter(account => account.type === 'liability')
      .map(account => ({
        id: account.id,
        code: account.code,
        name: account.name,
        balance: account.balance
      }))
      .sort((a, b) => a.code.localeCompare(b.code));

    const equity = trialBalance.accounts
      .filter(account => account.type === 'equity' || account.type === 'income' || account.type === 'expense')
      .map(account => {
        // Expenses should have their sign flipped for the balance sheet
        const balance = account.type === 'expense' ? -account.balance : account.balance;

        return {
          id: account.id,
          code: account.code,
          name: account.name,
          balance
        };
      })
      .sort((a, b) => a.code.localeCompare(b.code));

    // Calculate totals
    const totalAssets = assets.reduce((sum, asset) => sum + asset.balance, 0);
    const totalLiabilities = liabilities.reduce((sum, liability) => sum + liability.balance, 0);
    const totalEquity = equity.reduce((sum, equityAccount) => sum + equityAccount.balance, 0);

    // Format the report date
    const reportDate = new Date().toLocaleDateString();

    return {
      companyId,
      asOfDate,
      reportDate,
      assets,
      liabilities,
      equity,
      totalAssets,
      totalLiabilities,
      totalEquity,
      balanced: Math.abs(totalAssets - (totalLiabilities + totalEquity)) < 0.01 // Allow for small rounding differences
    };
  } catch (error) {
    errorLogger.logError(`Failed to generate balance sheet report for company ${companyId}`, 'balance-sheet-report', error as Error);

    // Return empty report on error
    return {
      companyId,
      asOfDate,
      reportDate: new Date().toLocaleDateString(),
      assets: [],
      liabilities: [],
      equity: [],
      totalAssets: 0,
      totalLiabilities: 0,
      totalEquity: 0,
      balanced: true
    };
  }
}

export async function getCashFlowReport(companyId: number, startDate: string, endDate: string): Promise<{
  companyId: number;
  startDate: string;
  endDate: string;
  reportDate: string;
  operatingActivities: {
    description: string;
    amount: number;
    category: string;
  }[];
  investingActivities: {
    description: string;
    amount: number;
    category: string;
  }[];
  financingActivities: {
    description: string;
    amount: number;
    category: string;
  }[];
  operatingTotal: number;
  investingTotal: number;
  financingTotal: number;
  netCashFlow: number;
  openingCashBalance: number;
  closingCashBalance: number;
}> {
  try {
    // Get cash accounts
    const cashAccounts = await db.select()
      .from(accounts)
      .where(and(
        eq(accounts.company_id, companyId),
        eq(accounts.account_type, 'asset'),
        sql`${accounts.account_code} LIKE '101%'` // Cash and bank accounts typically start with 101 in CoA
      ));

    if (!cashAccounts || cashAccounts.length === 0) {
      throw new Error("No cash accounts found for cash flow calculation");
    }

    // Convert date strings to Date objects
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Format dates for SQL
    const formattedStartDate = startDateObj.toISOString().split('T')[0];
    const formattedEndDate = endDateObj.toISOString().split('T')[0];

    // Get opening balances for cash accounts (as of start date)
    let openingCashBalance = 0;

    // For each cash account, get transactions before start date to calculate opening balance
    for (const cashAccount of cashAccounts) {
      const previousTransactions = await db.select()
        .from(transactions)
        .where(and(
          eq(transactions.company_id, companyId),
          eq(transactions.account_id, cashAccount.id),
          lt(transactions.transaction_date, new Date(formattedStartDate))
        ));

      // Calculate balance
      for (const txn of previousTransactions) {
        if (txn.transaction_type === 'debit') {
          openingCashBalance += Number(txn.amount);
        } else if (txn.transaction_type === 'credit') {
          openingCashBalance -= Number(txn.amount);
        }
      }
    }

    // Get transactions for all accounts in the period
    const periodTransactions = await db.select({
      id: transactions.id,
      account_id: transactions.account_id,
      transaction_type: transactions.transaction_type,
      amount: transactions.amount,
      reference_type: transactions.reference_type,
      description: transactions.description,
      transaction_date: transactions.transaction_date
    })
      .from(transactions)
      .where(and(
        eq(transactions.company_id, companyId),
        gte(transactions.transaction_date, new Date(formattedStartDate)),
        lte(transactions.transaction_date, new Date(formattedEndDate))
      ))
      .orderBy(transactions.transaction_date);

    // Get all account details to determine categories
    const accountsMap = new Map<number, Account>();
    const allAccounts = await getAccountsByCompany(companyId);
    allAccounts.forEach(account => {
      accountsMap.set(account.id, account);
    });

    // Prepare cash flow categories
    const operatingActivities: {description: string; amount: number; category: string}[] = [];
    const investingActivities: {description: string; amount: number; category: string}[] = [];
    const financingActivities: {description: string; amount: number; category: string}[] = [];

    // Calculate impact on cash for each transaction
    let closingCashBalance = openingCashBalance;
    const cashAccountIds = cashAccounts.map(a => a.id);

    for (const txn of periodTransactions) {
      const account = accountsMap.get(txn.account_id);
      if (!account) continue;

      // Skip transactions between cash accounts (transfers)
      if (cashAccountIds.includes(txn.account_id) && txn.reference_type === 'transfer') {
        continue;
      }

      // For cash accounts, track the impact on cash balance
      if (cashAccountIds.includes(txn.account_id)) {
        if (txn.transaction_type === 'debit') {
          closingCashBalance += Number(txn.amount);
        } else if (txn.transaction_type === 'credit') {
          closingCashBalance -= Number(txn.amount);
        }
      }

      // Only process non-cash account entries that represent cash flows
      if (!cashAccountIds.includes(txn.account_id)) {
        // Categorize based on reference type and account type
        const amount = Number(txn.amount);
        const isCashInflow = txn.transaction_type === 'credit'; // Credit to non-cash usually means cash inflow
        const adjustedAmount = isCashInflow ? amount : -amount;

        // Determine the category based on account type and reference
        if (account.account_type === 'asset' && txn.reference_type.includes('loan')) {
          // Loan activities are financing
          financingActivities.push({
            description: txn.description || account.account_name,
            amount: adjustedAmount,
            category: 'loans'
          });
        } else if (account.account_type === 'asset' &&
                  (txn.reference_type.includes('purchase') || txn.reference_type.includes('disposal'))) {
          // Asset purchases/sales are investing activities
          investingActivities.push({
            description: txn.description || account.account_name,
            amount: adjustedAmount,
            category: 'fixed_assets'
          });
        } else if (account.account_type === 'liability' && !txn.reference_type.includes('loan')) {
          // Regular liability activities are operating
          operatingActivities.push({
            description: txn.description || account.account_name,
            amount: adjustedAmount,
            category: 'liabilities'
          });
        } else if (account.account_type === 'liability' && txn.reference_type.includes('loan')) {
          // Loan-related liability activities are financing
          financingActivities.push({
            description: txn.description || account.account_name,
            amount: adjustedAmount,
            category: 'loans'
          });
        } else if (account.account_type === 'income') {
          // Income is operating
          operatingActivities.push({
            description: txn.description || account.account_name,
            amount: adjustedAmount,
            category: 'income'
          });
        } else if (account.account_type === 'expense') {
          // Expenses are operating
          operatingActivities.push({
            description: txn.description || account.account_name,
            amount: adjustedAmount,
            category: 'expenses'
          });
        } else if (account.account_type === 'equity') {
          // Most equity activities are financing
          financingActivities.push({
            description: txn.description || account.account_name,
            amount: adjustedAmount,
            category: 'equity'
          });
        }
      }
    }

    // Calculate totals
    const operatingTotal = operatingActivities.reduce((sum, item) => sum + item.amount, 0);
    const investingTotal = investingActivities.reduce((sum, item) => sum + item.amount, 0);
    const financingTotal = financingActivities.reduce((sum, item) => sum + item.amount, 0);
    const netCashFlow = operatingTotal + investingTotal + financingTotal;

    // Group similar activities
    const groupActivities = (activities: {description: string; amount: number; category: string}[]) => {
      const groupedMap = new Map<string, {description: string; amount: number; category: string}>();

      activities.forEach(activity => {
        const key = `${activity.category}:${activity.description}`;
        if (groupedMap.has(key)) {
          const existing = groupedMap.get(key)!;
          existing.amount += activity.amount;
        } else {
          groupedMap.set(key, { ...activity });
        }
      });

      return Array.from(groupedMap.values());
    };

    return {
      companyId,
      startDate,
      endDate,
      reportDate: new Date().toLocaleDateString(),
      operatingActivities: groupActivities(operatingActivities),
      investingActivities: groupActivities(investingActivities),
      financingActivities: groupActivities(financingActivities),
      operatingTotal,
      investingTotal,
      financingTotal,
      netCashFlow,
      openingCashBalance,
      closingCashBalance
    };
  } catch (error) {
    errorLogger.logError(`Failed to generate cash flow report for company ${companyId}`, 'cash-flow-report', error as Error);

    // Return empty report on error
    return {
      companyId,
      startDate,
      endDate,
      reportDate: new Date().toLocaleDateString(),
      operatingActivities: [],
      investingActivities: [],
      financingActivities: [],
      operatingTotal: 0,
      investingTotal: 0,
      financingTotal: 0,
      netCashFlow: 0,
      openingCashBalance: 0,
      closingCashBalance: 0
    };
  }
}

export async function getShareholderReport(companyId: number, shareholderId: number): Promise<{
  shareholder: Shareholder | undefined;
  investments: InvestmentTransaction[];
  shareholdings: {
    id: number;
    shares: number;
    initialInvestment: number;
    currentValue: number;
    acquisitionDate: string;
    percentageOwned: number;
  }[];
  totalInvested: number;
  totalShares: number;
  currentValue: number;
  returnOnInvestment: number;
  dividendHistory: {
    date: string;
    amount: number;
    description: string;
  }[];
  totalDividends: number;
}> {
  try {
    // Get the shareholder details
    const shareholder = await getShareholder(shareholderId, companyId);
    if (!shareholder) {
      throw new Error(`Shareholder ${shareholderId} not found for company ${companyId}`);
    }

    // Get all investment transactions for this shareholder
    const investments = await getInvestmentTransactions(shareholderId);

    // Get shareholding details
    const shareholdingRecords = await db.select()
      .from(shareholdings)
      .where(and(
        eq(shareholdings.shareholder_id, shareholderId),
        eq(shareholdings.company_id, companyId)
      ));

    // Calculate total investment and shares
    let totalInvested = 0;
    let totalShares = 0;

    for (const investment of investments) {
      if (investment.transaction_type === 'investment') {
        totalInvested += Number(investment.amount);
      }
    }

    // Find dividend transactions
    const dividendTransactions = investments.filter(t => t.transaction_type === 'dividend');
    const totalDividends = dividendTransactions.reduce((sum, t) => sum + Number(t.amount), 0);

    // Format dividend history
    const dividendHistory = dividendTransactions.map(t => ({
      date: new Date(t.transaction_date).toLocaleDateString(),
      amount: Number(t.amount),
      description: t.description || 'Dividend payment'
    }));

    // Process shareholdings
    const formattedShareholdings = shareholdingRecords.map(holding => {
      // Ensure proper type handling
      const shares = Number(holding.shares || 0);
      totalShares += shares;

      // Find associated investment for initial value
      const initialInvestment = investments.find(i =>
        i.shareholder_id === shareholderId && i.shareholding_id === holding.id
      );

      const initialValue = initialInvestment ? Number(initialInvestment.amount) : 0;

      return {
        id: holding.id,
        shares: shares,
        initialInvestment: initialValue,
        currentValue: Number(holding.current_value || initialValue), // Use current value if available
        acquisitionDate: new Date(holding.acquisition_date).toLocaleDateString(),
        percentageOwned: Number(holding.percentage || 0)
      };
    });

    // Calculate current value and ROI
    const currentValue = formattedShareholdings.reduce((sum, h) => sum + h.currentValue, 0);
    const totalReturn = (currentValue - totalInvested) + totalDividends;
    const returnOnInvestment = totalInvested > 0 ? (totalReturn / totalInvested) * 100 : 0;

    return {
      shareholder,
      investments,
      shareholdings: formattedShareholdings,
      totalInvested,
      totalShares,
      currentValue,
      returnOnInvestment,
      dividendHistory,
      totalDividends
    };
  } catch (error) {
    errorLogger.logError(`Failed to generate shareholder report for shareholder ${shareholderId}`, 'shareholder-report', error as Error);

    // Return empty report on error
    return {
      shareholder: undefined,
      investments: [],
      shareholdings: [],
      totalInvested: 0,
      totalShares: 0,
      currentValue: 0,
      returnOnInvestment: 0,
      dividendHistory: [],
      totalDividends: 0
    };
  }
}

/**
 * Generates a trial balance report for a company
 * Aggregates all transactions and returns account balances
 * @param companyId The company ID to generate the report for
 * @param asOfDate Optional date to generate the report as of (defaults to current date)
 * @returns Trial balance with account names, debit and credit totals
 */
export async function getTrialBalanceReport(companyId: number, asOfDate?: string): Promise<{
  accounts: {
    id: number;
    code: string;
    name: string;
    type: string;
    debit_total: number;
    credit_total: number;
    balance: number;
  }[];
  totals: {
    debit_total: number;
    credit_total: number;
    difference: number;
  };
}> {
  try {
    // Get all accounts for the company
    const companyAccounts = await getAccountsByCompany(companyId);

    if (!companyAccounts || companyAccounts.length === 0) {
      return {
        accounts: [],
        totals: { debit_total: 0, credit_total: 0, difference: 0 }
      };
    }

    // Prepare the date filter
    const dateFilter = asOfDate ? new Date(asOfDate) : new Date();

    // Collect all transactions up to the given date
    const allTransactions = await db.select()
      .from(transactions)
      .where(and(
        eq(transactions.company_id, companyId),
        lte(transactions.transaction_date, dateFilter)
      ));

    // Group transactions by account and calculate totals
    const accountTransactions = new Map<number, { debit_total: number, credit_total: number }>();

    // Initialize all accounts with zero balances
    companyAccounts.forEach(account => {
      accountTransactions.set(account.id, { debit_total: 0, credit_total: 0 });
    });

    // Calculate totals for each account
    allTransactions.forEach(transaction => {
      const accountRecord = accountTransactions.get(transaction.account_id);
      if (accountRecord) {
        if (transaction.transaction_type === 'debit') {
          accountRecord.debit_total += Number(transaction.amount);
        } else if (transaction.transaction_type === 'credit') {
          accountRecord.credit_total += Number(transaction.amount);
        }
      }
    });

    // Prepare the result array
    let totalDebit = 0;
    let totalCredit = 0;

    const trialBalanceAccounts = companyAccounts.map(account => {
      const transactions = accountTransactions.get(account.id) || { debit_total: 0, credit_total: 0 };

      // Calculate the account balance based on account type
      const debitBalance = transactions.debit_total - transactions.credit_total;
      const creditBalance = transactions.credit_total - transactions.debit_total;

      let balance = 0;

      // Asset and expense accounts normally have debit balances
      if (account.account_type === 'asset' || account.account_type === 'expense') {
        balance = debitBalance;
      }
      // Liability, equity, and income accounts normally have credit balances
      else if (account.account_type === 'liability' || account.account_type === 'equity' || account.account_type === 'income') {
        balance = creditBalance;
      }

      totalDebit += transactions.debit_total;
      totalCredit += transactions.credit_total;

      return {
        id: account.id,
        code: account.account_code,
        name: account.account_name,
        type: account.account_type,
        debit_total: transactions.debit_total,
        credit_total: transactions.credit_total,
        balance
      };
    }).filter(account => account.debit_total > 0 || account.credit_total > 0);

    return {
      accounts: trialBalanceAccounts,
      totals: {
        debit_total: totalDebit,
        credit_total: totalCredit,
        difference: Math.abs(totalDebit - totalCredit)
      }
    };
  } catch (error) {
    errorLogger.logError(`Failed to generate trial balance report for company ${companyId}`, 'trial-balance-report', error as Error);
    throw error;
  }
}

/**
 * Utility function to delete all test data for a specific company
 * This removes all loans, collections, payments, payment schedules, and associated transactions
 * to allow for clean end-to-end testing
 *
 * @param companyId The company ID for which to delete test data
 * @returns Promise<{ deleted: boolean, counts: Record<string, number> }>
 */
export async function deleteAllTestData(companyId: number): Promise<{
  deleted: boolean;
  counts: Record<string, number>;
}> {
  try {
    console.log(`Attempting to delete all test data for company ${companyId}...`);
    const counts: Record<string, number> = {};

    // We need to delete data in the correct order to respect foreign key constraints

    // 1. First handle transactions linked to collections and loans
    // Get all loan IDs for this company to delete their linked transactions
    const companyLoans = await db.select({
        id: loans.id
      })
      .from(loans)
      .where(eq(loans.company_id, companyId));

    const loanIds = companyLoans.map(loan => loan.id);

    // Delete payment records first (they reference collections)
    try {
      const deletedPayments = await db.delete(payments)
        .where(eq(payments.company_id, companyId))
        .returning();
      counts.payments = deletedPayments.length;
      console.log(`Deleted ${deletedPayments.length} payment records`);
    } catch (error) {
      console.error('Error deleting payments:', error);
      counts.payments = 0;
    }

    // Delete fines as they reference loans and payments
    try {
      const deletedFines = await db.delete(fines)
        .where(eq(fines.company_id, companyId))
        .returning();
      counts.fines = deletedFines.length;
      console.log(`Deleted ${deletedFines.length} fine records`);
    } catch (error) {
      console.error('Error deleting fines:', error);
      counts.fines = 0;
    }

    // Delete payment schedules
    try {
      const deletedSchedules = await db.delete(paymentSchedules)
        .where(eq(paymentSchedules.company_id, companyId))
        .returning();
      counts.paymentSchedules = deletedSchedules.length;
      console.log(`Deleted ${deletedSchedules.length} payment schedule records`);
    } catch (error) {
      console.error('Error deleting payment schedules:', error);
      counts.paymentSchedules = 0;
    }

    // Delete collections as they reference loans
    try {
      const deletedCollections = await db.delete(collections)
        .where(eq(collections.company_id, companyId))
        .returning();
      counts.collections = deletedCollections.length;
      console.log(`Deleted ${deletedCollections.length} collection records`);
    } catch (error) {
      console.error('Error deleting collections:', error);
      counts.collections = 0;
    }

    // Delete transactions that reference loans or collections
    // Get all transactions that have reference_type 'loan' or 'collection'
    try {
      const deletedTransactions = await db.delete(transactions)
        .where(and(
          eq(transactions.company_id, companyId),
          or(
            eq(transactions.reference_type, 'loan'),
            eq(transactions.reference_type, 'collection')
          )
        ))
        .returning();
      counts.transactions = deletedTransactions.length;
      console.log(`Deleted ${deletedTransactions.length} financial transactions`);
    } catch (error) {
      console.error('Error deleting transactions:', error);
      counts.transactions = 0;
    }

    // Finally delete loans
    try {
      const deletedLoans = await db.delete(loans)
        .where(eq(loans.company_id, companyId))
        .returning();
      counts.loans = deletedLoans.length;
      console.log(`Deleted ${deletedLoans.length} loan records`);
    } catch (error) {
      console.error('Error deleting loans:', error);
      counts.loans = 0;
    }

    // Delete any expense records and their transactions
    try {
      const deletedExpenseTransactions = await db.delete(transactions)
        .where(and(
          eq(transactions.company_id, companyId),
          eq(transactions.reference_type, 'expense')
        ))
        .returning();
      counts.expenseTransactions = deletedExpenseTransactions.length;
      console.log(`Deleted ${deletedExpenseTransactions.length} expense transactions`);
    } catch (error) {
      console.error('Error deleting expense transactions:', error);
      counts.expenseTransactions = 0;
    }

    try {
      const deletedExpenses = await db.delete(expenses)
        .where(eq(expenses.company_id, companyId))
        .returning();
      counts.expenses = deletedExpenses.length;
      console.log(`Deleted ${deletedExpenses.length} expense records`);
    } catch (error) {
      console.error('Error deleting expenses:', error);
      counts.expenses = 0;
    }

    // Return summary of deleted records
    return {
      deleted: true,
      counts
    };
  } catch (error) {
    errorLogger.logError(`Failed to delete test data for company ${companyId}`, 'delete-test-data', error as Error);
    console.error('Error deleting test data:', error);
    return {
      deleted: false,
      counts: {}
    };
  }
}

/**
 * Creates missing journal entries for completed collections
 * This is a fix for older collections that may not have financial transactions
 *
 * @param companyId The company ID to create missing entries for
 * @returns Promise<{fixed: number, errors: number, details: string[]}>
 */
export async function fixMissingCollectionTransactions(companyId: number): Promise<{
  fixed: number;
  errors: number;
  details: string[];
}> {
  const details: string[] = [];
  let fixed = 0;
  let errors = 0;

  try {
    console.log(`Finding completed collections without transactions for company ${companyId}...`);

    // Find all completed collections
    const completedCollections = await db
      .select()
      .from(collections)
      .where(
        and(
          eq(collections.company_id, companyId),
          eq(collections.status, 'completed')
        )
      );

    console.log(`Found ${completedCollections.length} completed collections`);
    details.push(`Found ${completedCollections.length} completed collections`);

    if (completedCollections.length === 0) {
      return { fixed: 0, errors: 0, details };
    }

    // For each collection, check if it has journal entries
    for (const collection of completedCollections) {
      try {
        // First get payments for this collection
        const paymentsForCollection = await db
          .select()
          .from(payments)
          .where(eq(payments.collection_id, collection.id));

        // Prepare payment IDs to check
        const paymentIds = paymentsForCollection.map(payment => payment.id);

        // Check if this collection or its payments already have transactions
        const existingTransactions = await db
          .select()
          .from(transactions)
          .where(
            and(
              eq(transactions.company_id, companyId),
              eq(transactions.reference_type, 'collection'),
              paymentIds.length > 0
                ? or(
                    eq(transactions.reference_id, collection.id),
                    inArray(transactions.reference_id, paymentIds)
                  )
                : eq(transactions.reference_id, collection.id)
            )
          );

        if (existingTransactions.length > 0) {
          console.log(`Collection #${collection.id} already has ${existingTransactions.length} transactions, skipping`);
          details.push(`Collection #${collection.id} already has transactions, skipping`);
          continue;
        }

        // Get the associated loan
        const loan = await db
          .select()
          .from(loans)
          .where(
            and(
              eq(loans.id, collection.loan_id),
              eq(loans.company_id, companyId)
            )
          )
          .limit(1);

        if (loan.length === 0) {
          console.error(`Loan #${collection.loan_id} not found for collection #${collection.id}`);
          details.push(`Error: Loan #${collection.loan_id} not found for collection #${collection.id}`);
          errors++;
          continue;
        }

        // Get the payment for this collection
        const payment = await db
          .select()
          .from(payments)
          .where(eq(payments.collection_id, collection.id))
          .limit(1);

        if (payment.length === 0) {
          console.log(`No payment found for collection #${collection.id}, skipping`);
          details.push(`No payment found for collection #${collection.id}, skipping`);
          continue;
        }

        // Get the required accounts
        const cashAccount = await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.CASH, "Cash");
        const cashOrBankAccount = cashAccount || await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.BANK, "Bank");
        const loanReceivableAccount = await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE, "Loan Receivable");
        const interestIncomeAccount = await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.INTEREST_INCOME, "Interest Income");

        if (!cashOrBankAccount || !loanReceivableAccount || !interestIncomeAccount) {
          console.error(`Required accounts not found for company ${companyId}`);
          details.push(`Error: Required accounts not found for company ${companyId}`);
          errors++;
          continue;
        }

        // Calculate the amounts
        const principalAmount = parseFloat(collection.amount);
        const interestAmount = collection.fine_amount ? parseFloat(collection.fine_amount) : 0;
        const totalAmount = principalAmount + interestAmount;

        if (isNaN(principalAmount) || isNaN(totalAmount)) {
          console.error(`Invalid amount values: principal=${collection.amount}, fine=${collection.fine_amount}`);
          details.push(`Error: Invalid amount values for collection #${collection.id}`);
          errors++;
          continue;
        }

        // Set up the journal entries
        const entries: Record<string, {account_id: number, amount: number}> = {
          // Cash increases (debit)
          debit: {
            account_id: cashOrBankAccount.id,
            amount: totalAmount
          }
        };

        // Principal amount to reduce loan receivable (credit)
        if (principalAmount > 0) {
          entries.creditPrincipal = {
            account_id: loanReceivableAccount.id,
            amount: principalAmount
          };
        }

        // Fine/interest amount to interest income (credit)
        if (interestAmount > 0) {
          entries.creditInterest = {
            account_id: interestIncomeAccount.id,
            amount: interestAmount
          };
        }

        // Create the journal entry
        const journalEntryData = {
          company_id: companyId,
          branch_id: collection.branch_id || null,
          transaction_date: collection.collection_date || new Date(),
          description: `Collection payment processed for loan ${loan[0].id}`,
          reference_type: 'collection' as const,
          reference_id: payment[0].id || collection.id,
          created_by: undefined,
          entries
        };

        // Create the journal entry
        const journalTransactions = await createJournalEntry(journalEntryData);
        console.log(`Created journal entry for collection #${collection.id} with ${journalTransactions.length} transactions`);
        details.push(`Fixed: Created journal entry for collection #${collection.id} with ${journalTransactions.length} transactions`);
        fixed++;
      } catch (error) {
        console.error(`Error fixing collection #${collection.id}:`, error);
        details.push(`Error fixing collection #${collection.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        errors++;
      }
    }

    return { fixed, errors, details };
  } catch (error) {
    console.error('Error fixing missing collection transactions:', error);
    details.push(`Global error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { fixed, errors: errors + 1, details };
  }
}