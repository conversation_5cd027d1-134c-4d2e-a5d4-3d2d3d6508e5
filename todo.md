# Chart of Accounts (CoA) Implementation Todo List

## Completed Items
- [x] Designed the base Chart of Accounts structure with appropriate account types (Asset, Liability, Equity, Income, Expense)
- [x] Added system-defined accounts in systemAccounts.ts configuration that are protected from deletion/renaming
- [x] Implemented createAccount, updateAccount, and getAccount functions
- [x] Enhanced the createJournalEntry function to support multiple debit and credit entries
- [x] Implemented double-entry accounting for collection transactions (decreasing Loan Receivable, increasing Cash)
- [x] Added trial balance report function in financialManagement.ts to verify balanced accounting
- [x] Added API endpoint for trial balance report
- [x] Created test page for trial balance verification
- [x] Fixed trial balance report calculation to use transaction_type instead of type field
- [x] Fixed routing issue for trial balance page in DynamicImport component
- [x] Clean up existing test data that doesn't follow double-entry accounting principles
- [x] Added missing createLoan method to DatabaseStorage class

## In Progress
- [x] Fix loan creation to properly generate journal entries (debit Loan Receivable, credit Cash/Bank)
- [ ] Implement loan collection journal entries (debit Cash/Bank, credit Loan Receivable + Interest Income)
- [ ] Enhance financial transaction creation to enforce double-entry accounting validation
- [ ] Add UI feedback for unbalanced transactions
- [ ] Integrate trial balance data with balance sheet report generation
- [ ] Implement account statement report showing all transactions for a specific account

## Upcoming Work
- [ ] Add account closures at period-end to transfer temporary account balances to permanent accounts
- [ ] Implement support for multi-currency transactions
- [ ] Create audit trails for all account changes
- [ ] Add more detailed transaction descriptions for accounting entries
- [ ] Support attachments for financial transactions (receipts, invoices)
- [ ] Implement budget vs. actual reporting based on account structure

## Issues and Technical Debt
- [ ] Replace transaction amount fields with Decimal type or equivalent to avoid floating-point precision issues
- [ ] Consider implementing database-level constraints for balanced transactions
- [ ] Investigate performance issues with large transaction volumes in financial reports
- [ ] Review and fix LSP typing issues throughout the financial module

## Notes
- Need to ensure all financial operations (collections, loans, expenses) generate the appropriate journal entries
- For loan disbursements: Debit Loan Receivable (1100), Credit Cash/Bank (1000/1010)
- For loan repayments: Debit Cash/Bank (1000/1010), Credit Loan Receivable (1100) + Interest Income (4000)
- Consider creating separate transaction categories for better financial reporting