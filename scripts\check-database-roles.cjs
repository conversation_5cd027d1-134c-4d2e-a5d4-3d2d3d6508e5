// Script to check current database state for role migration
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkDatabaseRoles() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Checking current database state for role migration...\n');

    // 1. Check current enum values
    console.log('1. Current user_role enum values:');
    try {
      const enumResult = await client.query(`
        SELECT enumlabel 
        FROM pg_enum 
        WHERE enumtypid = (
          SELECT oid 
          FROM pg_type 
          WHERE typname = 'user_role'
        )
        ORDER BY enumsortorder;
      `);
      
      console.log('   Available roles:', enumResult.rows.map(r => r.enumlabel).join(', '));
      
      const hasCompanyAdmin = enumResult.rows.some(r => r.enumlabel === 'company_admin');
      const hasOwner = enumResult.rows.some(r => r.enumlabel === 'owner');
      
      console.log(`   - Has 'company_admin': ${hasCompanyAdmin}`);
      console.log(`   - Has 'owner': ${hasOwner}`);
      
    } catch (error) {
      console.log('   Error checking enum:', error.message);
    }

    // 2. Check users table for company_admin roles
    console.log('\n2. Users with company_admin role:');
    try {
      const usersResult = await client.query(`
        SELECT id, email, full_name, role, company_id
        FROM users
        WHERE role = 'company_admin'
        ORDER BY id;
      `);
      
      if (usersResult.rows.length === 0) {
        console.log('   No users found with company_admin role');
      } else {
        console.log(`   Found ${usersResult.rows.length} users with company_admin role:`);
        usersResult.rows.forEach(user => {
          console.log(`   - ID: ${user.id}, Email: ${user.email}, Company: ${user.company_id}`);
        });
      }
    } catch (error) {
      console.log('   Error checking users:', error.message);
    }

    // 3. Check user_companies table for company_admin roles
    console.log('\n3. User-company associations with company_admin role:');
    try {
      const userCompaniesResult = await client.query(`
        SELECT uc.user_id, uc.company_id, uc.role, u.email
        FROM user_companies uc
        JOIN users u ON uc.user_id = u.id
        WHERE uc.role = 'company_admin'
        ORDER BY uc.user_id;
      `);
      
      if (userCompaniesResult.rows.length === 0) {
        console.log('   No user-company associations found with company_admin role');
      } else {
        console.log(`   Found ${userCompaniesResult.rows.length} associations with company_admin role:`);
        userCompaniesResult.rows.forEach(assoc => {
          console.log(`   - User: ${assoc.email} (ID: ${assoc.user_id}), Company: ${assoc.company_id}`);
        });
      }
    } catch (error) {
      console.log('   Error checking user_companies:', error.message);
    }

    // 4. Check users with owner role
    console.log('\n4. Users with owner role:');
    try {
      const ownersResult = await client.query(`
        SELECT id, email, full_name, role, company_id
        FROM users
        WHERE role = 'owner'
        ORDER BY id;
      `);
      
      if (ownersResult.rows.length === 0) {
        console.log('   No users found with owner role');
      } else {
        console.log(`   Found ${ownersResult.rows.length} users with owner role:`);
        ownersResult.rows.forEach(user => {
          console.log(`   - ID: ${user.id}, Email: ${user.email}, Company: ${user.company_id}`);
        });
      }
    } catch (error) {
      console.log('   Error checking owners:', error.message);
    }

    // 5. Check custom_roles table for Company Administrator
    console.log('\n5. Custom roles with "Company Administrator" name:');
    try {
      const customRolesResult = await client.query(`
        SELECT id, name, description, company_id, is_system
        FROM custom_roles
        WHERE name ILIKE '%Company Administrator%' OR name ILIKE '%Company Admin%'
        ORDER BY id;
      `);
      
      if (customRolesResult.rows.length === 0) {
        console.log('   No custom roles found with "Company Administrator" name');
      } else {
        console.log(`   Found ${customRolesResult.rows.length} custom roles:`);
        customRolesResult.rows.forEach(role => {
          console.log(`   - ID: ${role.id}, Name: "${role.name}", Company: ${role.company_id}, System: ${role.is_system}`);
        });
      }
    } catch (error) {
      console.log('   Error checking custom roles:', error.message);
    }

    console.log('\n📋 Summary:');
    console.log('   - If you see company_admin roles above, you need to run the migration');
    console.log('   - The migration file is: migrations/021_rename_company_admin_to_owner.sql');
    console.log('   - This will update the enum and all existing records');
    
  } catch (error) {
    console.error('❌ Error checking database state:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the check
checkDatabaseRoles().catch(console.error);
