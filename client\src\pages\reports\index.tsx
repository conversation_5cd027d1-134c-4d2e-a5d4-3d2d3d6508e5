import { useState, useEffect, useRef, use<PERSON>allback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  BarC<PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from "recharts";
import { 
  Download, 
  BarChart3, 
  <PERSON><PERSON><PERSON> as PieChartIcon, 
  <PERSON><PERSON>hart as LineChartIcon,
  FileText,
  Calendar
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { format, subDays, startOfMonth, endOf<PERSON>onth, subMonths } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/components/auth/auth-context";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { useToast } from "@/hooks/use-toast";
import logger from "@/lib/logger";
import { useContextData } from "@/lib/useContextData";

// Types for API responses
interface DailyCollectionItem {
  id: number;
  loan_id: number;
  customer_id: number;
  agent_id: number | null;
  amount: string;
  scheduled_date: string;
  collection_date: string | null;
  status: string;
  payment_method: string | null;
  customerName: string;
  agentName: string | null;
  receipt_id: string | null;
}

interface DailyGroupedData {
  date: string;
  collections: DailyCollectionItem[];
  totalAmount: number;
  completedAmount: number;
  pendingAmount: number;
}

interface DailyCollectionReport {
  startDate: string;
  endDate: string;
  totalCollected: number;
  totalPending: number;
  dailyData: DailyGroupedData[];
  rawData: DailyCollectionItem[];
}

interface AgentReport {
  agent: {
    id: number;
    full_name: string;
    user: {
      username: string;
      email: string;
    };
  };
  collections: any[];
  summary: {
    totalCollections: number;
    totalAmount: number;
    completedCollections: number;
    completedAmount: number;
    pendingCollections: number;
    pendingAmount: number;
    efficiencyRate: number;
    commission: number;
  };
  performanceTrend: {
    date: string;
    collections: number;
    amount: number;
    efficiency: number;
  }[];
}

interface ProfitLossReport {
  startDate: string;
  endDate: string;
  totalIncome: number;
  totalExpenses: number;
  netProfit: number;
  incomeBreakdown: {
    interestIncome: number;
    fineIncome: number;
    processingFeeIncome: number;
    otherIncome: number;
  };
  expenseBreakdown: {
    category: string;
    amount: number;
  }[];
  monthlyData: {
    month: string;
    income: number;
    expenses: number;
    profit: number;
  }[];
}

export default function Reports() {
  const { toast } = useToast();
  const { user } = useAuth();
  
  // Use our new centralized context hook for consistent company access
  const { 
    companyId, 
    companyName,
    userId
  } = useContextData();
  
  // Date range state - MOVED UP to fix initialization order
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date()
  });
  
  // Effect to validate date range whenever it changes
  useEffect(() => {
    if (dateRange?.from && dateRange?.to) {
      // Validate date range - ensure 'to' is not before 'from'
      if (dateRange.to < dateRange.from) {
        logger.warn('Invalid date range selected', {
          context: 'date-validation',
          data: {
            from: format(dateRange.from, 'yyyy-MM-dd'),
            to: format(dateRange.to, 'yyyy-MM-dd')
          }
        });

        toast({
          title: "Invalid date range",
          description: "End date cannot be before start date",
          variant: "destructive"
        });
      }
    }
  }, [dateRange, toast]);

  // Format date range for API calls
  const startDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : '';
  const endDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : '';
  
  const [reportType, setReportType] = useState("collections");
  const [selectedAgentId, setSelectedAgentId] = useState<number | undefined>(undefined);
  const [selectedBranchId, setSelectedBranchId] = useState<number | undefined>(undefined);

  // Fetch agents for the dropdown
  const { data: agents } = useQuery({
    queryKey: ['/api/companies', companyId, 'agents'],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/companies/${companyId}/agents`);
      return response.json();
    },
    enabled: !!companyId
  });

  // Fetch branches for the dropdown
  const { data: branchOptions } = useQuery({
    queryKey: ['/api/companies', companyId, 'branches'],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/companies/${companyId}/branches`);
      return response.json();
    },
    enabled: !!companyId
  });

  // Fetch daily collections data with enhanced logging
  const { 
    data: collectionsData, 
    isLoading: collectionsLoading, 
    error: collectionsError, 
    refetch: refetchCollections
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'reports/daily-collections', startDate, endDate, selectedAgentId, selectedBranchId],
    queryFn: () => {
      let url = `/api/companies/${companyId}/reports/daily-collections?startDate=${startDate}&endDate=${endDate}`;
      if (selectedAgentId) url += `&agentId=${selectedAgentId}`;
      if (selectedBranchId) url += `&branchId=${selectedBranchId}`;
      
      // Add timestamp and query ID for tracing requests
      // Generate request ID for error tracking if needed
      const requestId = Math.random().toString(36).substring(2, 10);
      
      return apiRequest('GET', url)
        .then(async response => {
          const data = await response.json();
          return data;
        })
        .catch(error => {
          logger.error('API REQUEST FAILED: daily-collections', error, {
            context: 'api-error',
            data: {
              requestId,
              endpoint: 'daily-collections',
              dateRange: {
                from: startDate,
                to: endDate
              }
            }
          });
          throw error;
        });
    },
    enabled: !!companyId && !!startDate && !!endDate
  });

  // Fetch agent report if an agent is selected
  const {
    data: agentReport,
    isLoading: agentReportLoading,
    error: agentReportError
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'reports/agent', selectedAgentId, startDate, endDate],
    queryFn: () => {
      let url = `/api/companies/${companyId}/reports/agent/${selectedAgentId}`;
      if (startDate && endDate) url += `?startDate=${startDate}&endDate=${endDate}`;
      
      return apiRequest('GET', url)
        .then(async response => {
          const data = await response.json();
          return data;
        })
        .catch(error => {
          logger.error('Error fetching agent report data', error, {
            context: 'api-error'
          });
          throw error;
        });
    },
    enabled: !!companyId && !!selectedAgentId
  });

  // Fetch profit/loss report
  const {
    data: profitLossReport,
    isLoading: profitLossLoading
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'reports/profit-loss', startDate, endDate, selectedBranchId],
    queryFn: async () => {
      let url = `/api/companies/${companyId}/reports/profit-loss?startDate=${startDate}&endDate=${endDate}`;
      if (selectedBranchId) url += `&branchId=${selectedBranchId}`;
      const response = await apiRequest('GET', url);
      return response.json();
    },
    enabled: !!companyId && !!startDate && !!endDate && reportType === 'profit-loss'
  });

  // Show error toast if any of the API calls fail
  useEffect(() => {
    if (collectionsError) {
      toast({
        title: "Error loading reports",
        description: "There was a problem loading the report data. Please try again.",
        variant: "destructive"
      });
    }
  }, [collectionsError]);

  // Transform collections data for visualization
  const transformedCollectionsData = collectionsData?.dailyData.map(day => ({
    date: format(new Date(day.date), 'dd MMM'),
    completed: day.completedAmount,
    pending: day.pendingAmount,
    total: day.totalAmount
  })) || [];

  // Process status distribution data
  const statusDistributionData = collectionsData ? [
    { name: "Completed", value: collectionsData.rawData.filter(c => c.status === 'completed').length, color: "#16a34a" },
    { name: "Pending", value: collectionsData.rawData.filter(c => c.status === 'pending').length, color: "#f59e0b" },
    { name: "Overdue", value: collectionsData.rawData.filter(c => c.status === 'overdue').length, color: "#dc2626" },
  ] : [];

  // Process agent data if available
  const agentPerformanceData = agentReport?.performanceTrend.map(day => ({
    date: format(new Date(day.date), 'dd MMM'),
    collections: day.collections,
    amount: day.amount,
    efficiency: day.efficiency
  })) || [];

  // Custom tooltip formatter for collections chart
  const CustomCollectionsTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded shadow-sm">
          <p className="text-sm font-medium text-gray-600">{label}</p>
          {payload.map((item: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: item.color }}>
              {item.name}: {formatCurrency(parseFloat(item.value), 'INR', 'en-IN')}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Function to export data as CSV
  const exportToCsv = () => {
    if (!collectionsData) return;
    
    const csvData = collectionsData.rawData.map(item => ({
      'Date': item.scheduled_date,
      'Collection Date': item.collection_date || '-',
      'Customer': item.customerName,
      'Agent': item.agentName || '-',
      'Amount': item.amount,
      'Status': item.status,
      'Payment Method': item.payment_method || '-',
      'Receipt ID': item.receipt_id || '-'
    }));
    
    // Create CSV content
    const headers = Object.keys(csvData[0]).join(',');
    const rows = csvData.map(row => 
      Object.values(row).map(value => 
        typeof value === 'string' && value.includes(',') ? `"${value}"` : value
      ).join(',')
    ).join('\n');
    const csvContent = `${headers}\n${rows}`;
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `collections_report_${startDate}_to_${endDate}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Create a stable reference to track setTimeout across renders
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Preset date range handlers with improved click handling
  const handlePresetChange = useCallback((preset: string, e?: React.MouseEvent) => {
    // If event object is provided, prevent any default browser behavior
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // Generate unique ID for tracking this specific button press
    const eventId = `${preset}-${Date.now()}`;
    
    // Clear any existing timeout to prevent race conditions
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    

    
    const today = new Date();
    // Set today to end of day to ensure we include all data for today
    today.setHours(23, 59, 59, 999); 
    
    let from: Date;
    let to = today;
    
    switch(preset) {
      case 'week':
        // For weekly view, start 7 days ago at start of day (00:00:00)
        from = subDays(today, 7);
        from.setHours(0, 0, 0, 0);
        break;
      case 'month':
        from = subDays(today, 30);
        from.setHours(0, 0, 0, 0);
        break;
      case 'quarter':
        from = subDays(today, 90);
        from.setHours(0, 0, 0, 0);
        break;
      case 'year':
        from = subDays(today, 365);
        from.setHours(0, 0, 0, 0);
        break;
      default:
        from = subDays(today, 30);
        from.setHours(0, 0, 0, 0);
    }
    
    // Update date range with new values
    setDateRange({ from, to });
    
    // Force a refresh of the data after changing date range
    // Store the timeout reference so we can clear it if needed
    timerRef.current = setTimeout(() => {
      // Check if companyId is valid before proceeding
      if (!companyId) {
        logger.error('Cannot fetch data: Company ID is undefined', {
          context: 'api-error',
          data: {
            eventId,
            user: user ? { id: user.id, username: user.username, email: user.email } : null,
            companyId: user?.company_id
          }
        });

        // Show error to user
        toast({
          title: "Error loading reports",
          description: "No company selected. Please select a company first.",
          variant: "destructive"
        });
        return;
      }

      // Explicitly call refetch to ensure the query runs again with new date range
      if (refetchCollections) {
        refetchCollections();
      } else {
        logger.warn('refetchCollections function is not available', {
          context: 'api-error',
          data: { eventId }
        });
      }

      // Clear the reference once done
      timerRef.current = null;
    }, 100);
  }, [setDateRange, refetchCollections, toast, user, companyId]); // Include all dependencies
  
  // Clean up timeout on component unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Reports</h1>
          <p className="mt-1 text-sm text-gray-500">
            Generate and view financial reports and analytics
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-col md:flex-row gap-4">
          <DatePickerWithRange 
            className="w-full md:w-auto"
            dateRange={dateRange}
            onUpdate={setDateRange}
          />
          <Button 
            onClick={exportToCsv} 
            disabled={!collectionsData} 
            className="flex items-center gap-1"
          >
            <Download size={16} />
            <span>Export CSV</span>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Select 
          value={reportType} 
          onValueChange={setReportType}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select Report Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="collections">Collections Report</SelectItem>
            <SelectItem value="status">Status Distribution</SelectItem>
            <SelectItem value="agents">Agent Performance</SelectItem>
            <SelectItem value="profit-loss">Profit & Loss</SelectItem>
          </SelectContent>
        </Select>
        
        <Select 
          value={selectedAgentId?.toString() || "all"} 
          onValueChange={(value) => {
            setSelectedAgentId(value === "all" ? undefined : parseInt(value));
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="All Agents" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Agents</SelectItem>
            {agents?.map((agent: any) => (
              <SelectItem key={agent.id} value={agent.id.toString()}>
                {agent.full_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select 
          value={selectedBranchId?.toString() || "all"} 
          onValueChange={(value) => {
            setSelectedBranchId(value === "all" ? undefined : parseInt(value));
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="All Branches" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Branches</SelectItem>
            {branchOptions?.map((branch: any) => (
              <SelectItem key={branch.id} value={branch.id.toString()}>
                {branch.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Report Content */}
      {reportType === 'collections' && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Daily Collections Report</CardTitle>
              <CardDescription>
                Overview of collections from {startDate} to {endDate}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => handlePresetChange('week', e)}
                id="week-preset-btn"
                data-testid="week-preset-btn"
              >
                Week
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => handlePresetChange('month', e)}
                id="month-preset-btn"
                data-testid="month-preset-btn"
              >
                Month
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => handlePresetChange('quarter', e)}
                id="quarter-preset-btn"
                data-testid="quarter-preset-btn"
              >
                Quarter
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {collectionsLoading ? (
              <div className="h-96 flex items-center justify-center">
                <Skeleton className="h-[350px] w-full" />
              </div>
            ) : collectionsData && transformedCollectionsData.length > 0 ? (
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart 
                    data={transformedCollectionsData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis dataKey="date" />
                    <YAxis 
                      tickFormatter={(value) => 
                        value >= 1000 
                          ? `₹${(value/1000).toFixed(1)}k` 
                          : `₹${value}`
                      }
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip content={<CustomCollectionsTooltip />} />
                    <Legend />
                    <Bar dataKey="completed" name="Completed" fill="#16a34a" />
                    <Bar dataKey="pending" name="Pending" fill="#f59e0b" />
                    <Bar dataKey="total" name="Total" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center">
                <p className="text-gray-500">No data available for the selected period.</p>
              </div>
            )}
            
            {/* Summary Statistics */}
            {collectionsData && (
              <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500">Total Collections</p>
                        <h3 className="text-2xl font-bold mt-1">
                          {formatCurrency(collectionsData.totalCollected + collectionsData.totalPending, 'INR', 'en-IN')}
                        </h3>
                      </div>
                      <div className="bg-blue-100 p-3 rounded-full">
                        <FileText className="text-blue-600" size={24} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500">Completed</p>
                        <h3 className="text-2xl font-bold mt-1 text-green-600">
                          {formatCurrency(collectionsData.totalCollected, 'INR', 'en-IN')}
                        </h3>
                      </div>
                      <div className="bg-green-100 p-3 rounded-full">
                        <FileText className="text-green-600" size={24} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500">Pending</p>
                        <h3 className="text-2xl font-bold mt-1 text-amber-600">
                          {formatCurrency(collectionsData.totalPending, 'INR', 'en-IN')}
                        </h3>
                      </div>
                      <div className="bg-amber-100 p-3 rounded-full">
                        <Calendar className="text-amber-600" size={24} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {reportType === 'status' && (
        <Card>
          <CardHeader>
            <CardTitle>Collection Status Distribution</CardTitle>
            <CardDescription>
              Breakdown of collections by their current status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {collectionsLoading ? (
              <div className="h-96 flex items-center justify-center">
                <Skeleton className="h-[350px] w-full" />
              </div>
            ) : statusDistributionData.length > 0 ? (
              <div className="h-96 flex flex-col md:flex-row items-center justify-between">
                <div className="w-full md:w-1/2 h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={statusDistributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                        outerRadius={100}
                        dataKey="value"
                      >
                        {statusDistributionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip 
                        formatter={(value) => [`${value} collections`, "Count"]}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="w-full md:w-1/2">
                  <h3 className="text-lg font-medium mb-4">Status Summary</h3>
                  <dl className="space-y-4">
                    {statusDistributionData.map((status, index) => (
                      <div key={index} className="flex justify-between">
                        <dt className="flex items-center">
                          <span 
                            className="w-3 h-3 rounded-full mr-2" 
                            style={{ backgroundColor: status.color }}
                          />
                          <span>{status.name}</span>
                        </dt>
                        <dd className="font-medium">{status.value} collections</dd>
                      </div>
                    ))}
                    <div className="pt-2 border-t">
                      <div className="flex justify-between font-semibold">
                        <dt>Total Collections</dt>
                        <dd>
                          {statusDistributionData.reduce((sum, item) => sum + item.value, 0)}
                        </dd>
                      </div>
                    </div>
                  </dl>
                </div>
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center">
                <p className="text-gray-500">No status data available for the selected period.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {reportType === 'agents' && (
        <Card>
          <CardHeader>
            <CardTitle>
              {selectedAgentId 
                ? `Agent Performance: ${agentReport?.agent.full_name || 'Loading...'}`
                : 'Agent Performance Report'
              }
            </CardTitle>
            <CardDescription>
              {selectedAgentId
                ? `Detailed performance metrics for selected agent`
                : 'Select an agent to view their detailed performance report'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {agentReportLoading || !selectedAgentId ? (
              <div className="h-96 flex items-center justify-center">
                {agentReportLoading ? (
                  <Skeleton className="h-[350px] w-full" />
                ) : (
                  <p className="text-gray-500">Select an agent to view their performance report.</p>
                )}
              </div>
            ) : agentReport ? (
              <div className="space-y-6">
                {/* Agent Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-500">Total Collections</p>
                        <h3 className="text-2xl font-bold mt-1">
                          {agentReport.summary.totalCollections}
                        </h3>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-500">Total Amount</p>
                        <h3 className="text-2xl font-bold mt-1">
                          {formatCurrency(agentReport.summary.totalAmount, 'INR', 'en-IN')}
                        </h3>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-500">Efficiency Rate</p>
                        <h3 className="text-2xl font-bold mt-1">
                          {`${agentReport.summary.efficiencyRate.toFixed(1)}%`}
                        </h3>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-500">Commission</p>
                        <h3 className="text-2xl font-bold mt-1">
                          {formatCurrency(agentReport.summary.commission, 'INR', 'en-IN')}
                        </h3>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                {/* Performance Trend Chart */}
                <div className="h-80">
                  {agentPerformanceData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={agentPerformanceData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                        <XAxis dataKey="date" />
                        <YAxis 
                          yAxisId="left"
                          orientation="left"
                          tickFormatter={(value) => 
                            value >= 1000 
                              ? `₹${(value/1000).toFixed(1)}k` 
                              : `₹${value}`
                          }
                        />
                        <YAxis 
                          yAxisId="right"
                          orientation="right"
                          tickFormatter={(value) => `${value}%`}
                        />
                        <Tooltip />
                        <Legend />
                        <Line 
                          yAxisId="left"
                          type="monotone" 
                          dataKey="amount" 
                          name="Collection Amount" 
                          stroke="#3b82f6" 
                          activeDot={{ r: 8 }} 
                        />
                        <Line 
                          yAxisId="right"
                          type="monotone" 
                          dataKey="efficiency" 
                          name="Efficiency Rate" 
                          stroke="#16a34a" 
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <p className="text-gray-500">No performance trend data available.</p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center">
                <p className="text-gray-500">Failed to load agent data. Please try again.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {reportType === 'profit-loss' && (
        <Card>
          <CardHeader>
            <CardTitle>Profit & Loss Statement</CardTitle>
            <CardDescription>
              Financial performance summary from {startDate} to {endDate}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {profitLossLoading ? (
              <div className="h-96 flex items-center justify-center">
                <Skeleton className="h-[350px] w-full" />
              </div>
            ) : profitLossReport ? (
              <div className="space-y-8">
                {/* P&L Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Total Income</p>
                          <h3 className="text-2xl font-bold mt-1 text-green-600">
                            {formatCurrency(profitLossReport.totalIncome, 'INR', 'en-IN')}
                          </h3>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Total Expenses</p>
                          <h3 className="text-2xl font-bold mt-1 text-red-600">
                            {formatCurrency(profitLossReport.totalExpenses, 'INR', 'en-IN')}
                          </h3>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Net Profit</p>
                          <h3 className={`text-2xl font-bold mt-1 ${profitLossReport.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatCurrency(profitLossReport.netProfit, 'INR', 'en-IN')}
                          </h3>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                
                {/* Monthly Trend Chart */}
                <div className="h-80">
                  {profitLossReport.monthlyData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={profitLossReport.monthlyData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                        <XAxis dataKey="month" />
                        <YAxis 
                          tickFormatter={(value) => 
                            value >= 1000 
                              ? `₹${(value/1000).toFixed(1)}k` 
                              : `₹${value}`
                          }
                        />
                        <Tooltip formatter={(value) => [formatCurrency(value as number, 'INR', 'en-IN')]} />
                        <Legend />
                        <Bar dataKey="income" name="Income" fill="#16a34a" />
                        <Bar dataKey="expenses" name="Expenses" fill="#dc2626" />
                        <Bar dataKey="profit" name="Profit" fill="#3b82f6" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <p className="text-gray-500">No monthly data available for the selected period.</p>
                    </div>
                  )}
                </div>
                
                {/* Breakdown Tables */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Income Breakdown */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">Income Breakdown</h3>
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="py-2 text-left">Category</th>
                          <th className="py-2 text-right">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="py-2">Interest Income</td>
                          <td className="py-2 text-right">
                            {formatCurrency(profitLossReport.incomeBreakdown.interestIncome, 'INR', 'en-IN')}
                          </td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2">Fine Income</td>
                          <td className="py-2 text-right">
                            {formatCurrency(profitLossReport.incomeBreakdown.fineIncome, 'INR', 'en-IN')}
                          </td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2">Processing Fee Income</td>
                          <td className="py-2 text-right">
                            {formatCurrency(profitLossReport.incomeBreakdown.processingFeeIncome, 'INR', 'en-IN')}
                          </td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-2">Other Income</td>
                          <td className="py-2 text-right">
                            {formatCurrency(profitLossReport.incomeBreakdown.otherIncome, 'INR', 'en-IN')}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-2 font-semibold">Total Income</td>
                          <td className="py-2 text-right font-semibold">
                            {formatCurrency(profitLossReport.totalIncome, 'INR', 'en-IN')}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  {/* Expense Breakdown */}
                  <div>
                    <h3 className="text-lg font-medium mb-4">Expense Breakdown</h3>
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="py-2 text-left">Category</th>
                          <th className="py-2 text-right">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        {profitLossReport.expenseBreakdown.map((expense, index) => (
                          <tr key={index} className="border-b">
                            <td className="py-2 capitalize">{expense.category.replace('_', ' ')}</td>
                            <td className="py-2 text-right">
                              {formatCurrency(expense.amount, 'INR', 'en-IN')}
                            </td>
                          </tr>
                        ))}
                        <tr>
                          <td className="py-2 font-semibold">Total Expenses</td>
                          <td className="py-2 text-right font-semibold">
                            {formatCurrency(profitLossReport.totalExpenses, 'INR', 'en-IN')}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center">
                <p className="text-gray-500">No profit and loss data available for the selected period.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
