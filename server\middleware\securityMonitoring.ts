import { Request, Response, NextFunction } from 'express';
import { accessMonitoringService } from '../services/accessMonitoringService';
import { type EnhancedAuthRequest } from './enhancedAuth';
import { type SecurityEventContext } from '@shared/schema';

// Rate limiting storage (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Security monitoring middleware that records all security events
 */
export function securityMonitoringMiddleware() {
  return async (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // Store original end function
    const originalEnd = res.end;
    
    // Override end function to log security events
    res.end = function(chunk?: any, encoding?: any) {
      const responseTime = Date.now() - startTime;
      
      // Create security event context
      const context: SecurityEventContext = {
        userId: req.user?.id,
        companyId: req.user?.company_id,
        sessionId: req.session?.sessionId,
        ipAddress: req.deviceInfo?.ipAddress || req.ip,
        userAgent: req.deviceInfo?.userAgent || req.get('User-Agent'),
        deviceFingerprint: req.deviceInfo?.fingerprint,
        deviceType: req.deviceInfo?.type,
        endpoint: req.path,
        method: req.method,
        statusCode: res.statusCode,
        responseTime,
        location: req.deviceInfo?.location,
        metadata: {
          query: req.query,
          body: req.method === 'POST' || req.method === 'PUT' ? req.body : undefined,
          referer: req.get('Referer'),
          contentLength: res.get('Content-Length')
        }
      };
      
      // Determine event type and record security event
      recordSecurityEventAsync(req, res, context);
      
      // Call original end function
      originalEnd.call(this, chunk, encoding);
    };
    
    next();
  };
}

/**
 * Rate limiting middleware with security monitoring
 */
export function rateLimitingMiddleware(options: {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}) {
  return async (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    const key = options.keyGenerator ? options.keyGenerator(req) : 
                req.deviceInfo?.ipAddress || req.ip || 'unknown';
    
    const now = Date.now();
    const windowStart = now - options.windowMs;
    
    // Clean up expired entries
    for (const [k, v] of rateLimitStore.entries()) {
      if (v.resetTime < now) {
        rateLimitStore.delete(k);
      }
    }
    
    // Get or create rate limit entry
    let entry = rateLimitStore.get(key);
    if (!entry || entry.resetTime < now) {
      entry = { count: 0, resetTime: now + options.windowMs };
      rateLimitStore.set(key, entry);
    }
    
    entry.count++;
    
    // Check if rate limit exceeded
    if (entry.count > options.maxRequests) {
      // Record rate limit violation
      await accessMonitoringService.recordSecurityEvent({
        eventType: 'rate_limit_exceeded',
        eventDescription: `Rate limit exceeded: ${entry.count} requests in ${options.windowMs}ms`,
        context: {
          userId: req.user?.id,
          companyId: req.user?.company_id,
          sessionId: req.session?.sessionId,
          ipAddress: req.deviceInfo?.ipAddress || req.ip,
          userAgent: req.deviceInfo?.userAgent,
          endpoint: req.path,
          method: req.method,
          metadata: {
            rateLimitKey: key,
            requestCount: entry.count,
            windowMs: options.windowMs,
            maxRequests: options.maxRequests
          }
        },
        severity: 'medium',
        eventSource: 'rate_limiter'
      });
      
      return res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded',
        retryAfter: Math.ceil((entry.resetTime - now) / 1000)
      });
    }
    
    // Add rate limit headers
    res.set({
      'X-RateLimit-Limit': options.maxRequests.toString(),
      'X-RateLimit-Remaining': Math.max(0, options.maxRequests - entry.count).toString(),
      'X-RateLimit-Reset': Math.ceil(entry.resetTime / 1000).toString()
    });
    
    next();
  };
}

/**
 * Brute force protection middleware
 */
export function bruteForceProtectionMiddleware(options: {
  maxAttempts: number;
  windowMs: number;
  blockDurationMs: number;
}) {
  const attemptStore = new Map<string, { attempts: number; lastAttempt: number; blockedUntil?: number }>();
  
  return async (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    const key = req.deviceInfo?.ipAddress || req.ip || 'unknown';
    const now = Date.now();
    
    // Clean up expired entries
    for (const [k, v] of attemptStore.entries()) {
      if (v.lastAttempt < now - options.windowMs) {
        attemptStore.delete(k);
      }
    }
    
    let entry = attemptStore.get(key);
    if (!entry) {
      entry = { attempts: 0, lastAttempt: now };
      attemptStore.set(key, entry);
    }
    
    // Check if currently blocked
    if (entry.blockedUntil && entry.blockedUntil > now) {
      await accessMonitoringService.recordSecurityEvent({
        eventType: 'brute_force_attempt',
        eventDescription: `Blocked brute force attempt from ${key}`,
        context: {
          userId: req.user?.id,
          ipAddress: key,
          userAgent: req.deviceInfo?.userAgent,
          endpoint: req.path,
          method: req.method,
          metadata: {
            attempts: entry.attempts,
            blockedUntil: entry.blockedUntil
          }
        },
        severity: 'high',
        eventSource: 'brute_force_protection'
      });
      
      return res.status(429).json({
        error: 'Blocked',
        message: 'Too many failed attempts. Please try again later.',
        blockedUntil: new Date(entry.blockedUntil).toISOString()
      });
    }
    
    // Store original end function to check response status
    const originalEnd = res.end;
    res.end = function(chunk?: any, encoding?: any) {
      // Check if this was a failed authentication attempt
      if (res.statusCode === 401 || res.statusCode === 403) {
        entry!.attempts++;
        entry!.lastAttempt = now;
        
        if (entry!.attempts >= options.maxAttempts) {
          entry!.blockedUntil = now + options.blockDurationMs;
          
          // Record brute force detection
          accessMonitoringService.recordSecurityEvent({
            eventType: 'brute_force_attempt',
            eventDescription: `Brute force attack detected from ${key}: ${entry!.attempts} failed attempts`,
            context: {
              userId: req.user?.id,
              ipAddress: key,
              userAgent: req.deviceInfo?.userAgent,
              endpoint: req.path,
              method: req.method,
              metadata: {
                attempts: entry!.attempts,
                windowMs: options.windowMs,
                blockedFor: options.blockDurationMs
              }
            },
            severity: 'high',
            eventSource: 'brute_force_protection'
          }).catch(console.error);
        }
      } else if (res.statusCode >= 200 && res.statusCode < 300) {
        // Successful request - reset attempts
        entry!.attempts = 0;
      }
      
      originalEnd.call(this, chunk, encoding);
    };
    
    next();
  };
}

/**
 * Suspicious activity detection middleware
 */
export function suspiciousActivityMiddleware() {
  return async (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    const suspiciousIndicators: string[] = [];
    
    // Check for suspicious patterns in request
    
    // 1. SQL injection patterns
    const sqlPatterns = /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b|['"]\s*;\s*|--|\|\|)/i;
    const queryString = JSON.stringify(req.query) + JSON.stringify(req.body || {});
    if (sqlPatterns.test(queryString)) {
      suspiciousIndicators.push('Potential SQL injection attempt');
    }
    
    // 2. XSS patterns
    const xssPatterns = /<script|javascript:|on\w+\s*=/i;
    if (xssPatterns.test(queryString)) {
      suspiciousIndicators.push('Potential XSS attempt');
    }
    
    // 3. Path traversal patterns
    const pathTraversalPatterns = /\.\.[\/\\]|%2e%2e[%2f%5c]/i;
    if (pathTraversalPatterns.test(req.url)) {
      suspiciousIndicators.push('Potential path traversal attempt');
    }
    
    // 4. Unusual user agent
    const userAgent = req.get('User-Agent') || '';
    if (!userAgent || userAgent.length < 10 || /bot|crawler|scanner|curl|wget/i.test(userAgent)) {
      suspiciousIndicators.push('Suspicious user agent');
    }
    
    // 5. Unusual request headers
    const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip', 'x-originating-ip'];
    for (const header of suspiciousHeaders) {
      if (req.get(header)) {
        suspiciousIndicators.push(`Suspicious header: ${header}`);
      }
    }
    
    // Record suspicious activity if detected
    if (suspiciousIndicators.length > 0) {
      await accessMonitoringService.recordSecurityEvent({
        eventType: 'suspicious_activity',
        eventDescription: `Suspicious activity detected: ${suspiciousIndicators.join(', ')}`,
        context: {
          userId: req.user?.id,
          companyId: req.user?.company_id,
          sessionId: req.session?.sessionId,
          ipAddress: req.deviceInfo?.ipAddress || req.ip,
          userAgent: req.deviceInfo?.userAgent,
          endpoint: req.path,
          method: req.method,
          metadata: {
            indicators: suspiciousIndicators,
            query: req.query,
            body: req.body,
            headers: req.headers
          }
        },
        severity: suspiciousIndicators.length > 2 ? 'high' : 'medium',
        eventSource: 'suspicious_activity_detector'
      });
    }
    
    next();
  };
}

/**
 * Record security event asynchronously to avoid blocking requests
 */
async function recordSecurityEventAsync(
  req: EnhancedAuthRequest,
  res: Response,
  context: SecurityEventContext
) {
  try {
    let eventType = 'data_access';
    let eventDescription = `${req.method} ${req.path}`;
    let severity: 'low' | 'medium' | 'high' | 'critical' = 'low';
    
    // Determine event type based on response status and endpoint
    if (res.statusCode === 401) {
      eventType = 'login_failure';
      eventDescription = 'Authentication failed';
      severity = 'medium';
    } else if (res.statusCode === 403) {
      eventType = 'permission_denied';
      eventDescription = 'Access denied';
      severity = 'medium';
    } else if (res.statusCode >= 200 && res.statusCode < 300) {
      if (req.path.includes('/login') || req.path.includes('/auth')) {
        eventType = 'login_success';
        eventDescription = 'User logged in successfully';
        severity = 'low';
      } else if (req.path.includes('/logout')) {
        eventType = 'logout';
        eventDescription = 'User logged out';
        severity = 'low';
      } else if (req.method === 'POST' || req.method === 'PUT' || req.method === 'DELETE') {
        eventType = 'data_access';
        eventDescription = `Data modification: ${req.method} ${req.path}`;
        severity = 'low';
      }
    } else if (res.statusCode >= 500) {
      eventType = 'suspicious_activity';
      eventDescription = `Server error: ${res.statusCode}`;
      severity = 'medium';
    }
    
    // Record the security event
    await accessMonitoringService.recordSecurityEvent({
      eventType,
      eventDescription,
      context,
      severity,
      eventSource: 'api',
      resourceAccessed: req.path,
      operationPerformed: req.method
    });
  } catch (error) {
    console.error('Failed to record security event:', error);
  }
}

/**
 * Admin-only middleware for security monitoring endpoints
 */
export function requireSecurityAdmin() {
  return (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    if (!['admin', 'super_admin', 'security_admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Security administrator access required',
        requiredRoles: ['admin', 'super_admin', 'security_admin']
      });
    }
    
    next();
  };
}
