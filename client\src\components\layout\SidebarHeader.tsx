import { X } from "lucide-react";
import { useLocation } from "wouter";

interface SidebarHeaderProps {
  closeSidebar: () => void;
}

const SidebarHeader = ({ closeSidebar }: SidebarHeaderProps) => {
  const [, navigate] = useLocation();

  return (
    <div className="flex items-center justify-between h-16 px-4 bg-blue-950 text-white">
      <div
        className="flex items-center cursor-pointer"
        onClick={() => {
          // Navigate to dashboard
          navigate("/dashboard");
          // Close sidebar on mobile
          closeSidebar();
        }}
      >
        <svg className="h-8 w-8" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
            stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        <span className="ml-2 text-xl font-bold">TrackFina</span>
      </div>
      <button
        className="text-white lg:hidden"
        onClick={closeSidebar}
      >
        <X className="h-6 w-6" />
      </button>
    </div>
  );
};

export default SidebarHeader;