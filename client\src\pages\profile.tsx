import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/lib/auth";
import { CalendarIcon, UserIcon, MailIcon, BadgeCheck, Building2, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

export default function Profile() {
  const { getCurrentUser, isAuthenticated } = useAuth();
  const user = getCurrentUser();

  const userInitials = user?.full_name
    ? user.full_name.split(" ").map((name) => name[0]).join("").substring(0, 2).toUpperCase()
    : "U";

  // Fetch user details including join date from API using React Query
  const {
    data: userData,
    isLoading,
    error
  } = useQuery({
    queryKey: [`/api/users/${user?.id}`],
    enabled: !!user?.id && isAuthenticated()
  });

  const formattedJoinDate = userData?.created_at
    ? new Date(userData.created_at).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "N/A";

  const renderRoleBadge = (role: string) => {
    switch (role) {
      case 'saas_admin':
        return <Badge className="bg-purple-600">SaaS Admin</Badge>;
      case 'owner':
        return <Badge className="bg-blue-600">Owner</Badge>;
      case 'employee':
        return <Badge className="bg-green-600">Employee</Badge>;
      case 'agent':
        return <Badge className="bg-amber-600">Agent</Badge>;
      case 'reseller':
        return <Badge className="bg-indigo-600">Reseller</Badge>;
      case 'partner':
        return <Badge className="bg-pink-600">Partner</Badge>;
      default:
        return <Badge>{role}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">My Profile</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Profile Card */}
        <Card className="md:col-span-1">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Avatar className="h-24 w-24">
                <AvatarFallback className="bg-blue-600 text-xl">
                  {userInitials}
                </AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-xl">{user?.full_name}</CardTitle>
            <div className="mt-2 flex justify-center">
              {user?.role && renderRoleBadge(user.role)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Email */}
              <div className="flex items-center gap-3">
                <MailIcon className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Email</p>
                  <p className="text-sm font-medium">{user?.email}</p>
                </div>
              </div>

              {/* Join Date */}
              <div className="flex items-center gap-3">
                <CalendarIcon className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Joined</p>
                  <p className="text-sm font-medium">{formattedJoinDate}</p>
                </div>
              </div>

              {/* Company */}
              {user?.company_id && (
                <div className="flex items-center gap-3">
                  <Building2 className="h-5 w-5 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Company</p>
                    <p className="text-sm font-medium">
                      {userData?.company?.name || `Company #${user.company_id}`}
                    </p>
                  </div>
                </div>
              )}

              {/* Verified Status */}
              <div className="flex items-center gap-3">
                <BadgeCheck className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <p className="text-sm font-medium text-green-600">Verified Account</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activity & Stats */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Activity Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              <p className="text-gray-500">Account activity and statistics will appear here soon.</p>

              {/* User activity metrics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">Collections</p>
                  {isLoading ? (
                    <div className="flex items-center justify-center h-8 mt-1">
                      <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
                    </div>
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">
                      {userData?.activity?.collections_count || 0}
                    </p>
                  )}
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">Customers</p>
                  {isLoading ? (
                    <div className="flex items-center justify-center h-8 mt-1">
                      <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
                    </div>
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">
                      {userData?.activity?.customers_count || 0}
                    </p>
                  )}
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-500">Commission</p>
                  {isLoading ? (
                    <div className="flex items-center justify-center h-8 mt-1">
                      <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
                    </div>
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">
                      ${(userData?.activity?.commission_amount || 0).toFixed(2)}
                    </p>
                  )}
                </div>
              </div>

              <div className="pt-4">
                <h3 className="text-lg font-medium mb-4">Recent Activity</h3>
                {isLoading ? (
                  <div className="flex items-center justify-center h-24">
                    <Loader2 className="h-8 w-8 text-gray-400 animate-spin" />
                  </div>
                ) : userData?.recent_activities?.length > 0 ? (
                  <div className="border rounded-md divide-y">
                    {userData.recent_activities.map((activity: any, index: number) => (
                      <div key={index} className="p-4">
                        <p className="font-medium">{activity.description}</p>
                        <p className="text-sm text-gray-500">{new Date(activity.timestamp).toLocaleString()}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="border rounded-md p-4 text-center text-gray-500">
                    No recent activity to display
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}