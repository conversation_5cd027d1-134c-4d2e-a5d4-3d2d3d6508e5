import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Pencil, Trash, Loader2, RefreshCcw } from "lucide-react";

export function CompanyManagement() {
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const userId = user?.id;
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<any>(null);
  const [isSettingPrimary, setIsSettingPrimary] = useState(false);
  const [companyForm, setCompanyForm] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    website: ''
  });

  // Fetch user companies
  const { data: companies, isLoading } = useQuery({
    queryKey: [`/api/users/${userId}/companies`],
    enabled: !!userId,
  });

  // Create company mutation
  const createCompany = useMutation({
    mutationFn: async (data: any) => {
      // First create the company
      const response = await apiRequest('POST', '/api/companies', data);
      const newCompany = await response.json();

      // Then associate it with the user
      await apiRequest('POST', '/api/user-companies', {
        user_id: userId,
        company_id: newCompany.id,
        is_primary: false
      });

      return newCompany;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/users/${userId}/companies`] });
      toast({
        title: "Company Created",
        description: "The company has been created successfully."
      });
      setIsAddModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create company.",
        variant: "destructive"
      });
    }
  });

  // Update company mutation
  const updateCompany = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('PATCH', `/api/companies/${selectedCompany.id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/users/${userId}/companies`] });
      toast({
        title: "Company Updated",
        description: "The company information has been updated successfully."
      });
      setIsEditModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update company.",
        variant: "destructive"
      });
    }
  });

  // Delete company mutation
  const deleteCompany = useMutation({
    mutationFn: async () => {
      try {
        const response = await apiRequest('DELETE', `/api/companies/${selectedCompany.id}`);
        const data = await response.json();

        // If there's an error message in the response
        if (!response.ok) {
          // Enhanced error handling with more details
          const errorDetails = {
            message: data.message || "Failed to delete company",
            status: response.status,
            hasCustomers: Boolean(data.customersCount),
            customersCount: data.customersCount || 0,
            originalInput: { id: selectedCompany.id }
          };

          // Create a detailed error object
          const customError = new Error(errorDetails.message);
          (customError as any).details = errorDetails;
          throw customError;
        }

        return data;
      } catch (error: any) {
        // If it's already a properly structured error from our API handler above, just rethrow
        if (error instanceof Error && (error as any).details) {
          throw error;
        }

        // Handle network errors or unexpected errors
        const newError = new Error(
          error.message || "Failed to connect to the server. Please check your connection."
        );
        (newError as any).details = {
          message: error.message || "Network error",
          originalInput: { id: selectedCompany.id }
        };
        throw newError;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/users/${userId}/companies`] });
      toast({
        title: "Company Deleted",
        description: "The company has been deleted successfully."
      });
      setIsDeleteModalOpen(false);
    },
    onError: (error: any) => {
      console.error("Error deleting company:", error);

      const errorDetails = (error as any).details || {};

      // Handle specific error cases
      if (errorDetails.hasCustomers) {
        toast({
          title: "Cannot Delete Company",
          description: `This company has ${errorDetails.customersCount} associated customers. Please delete all customers first before deleting this company.`,
          variant: "destructive",
          duration: 5000,
        });
      } else {
        toast({
          title: "Error",
          description: errorDetails.message || "Failed to delete company. It may have related data that needs to be removed first.",
          variant: "destructive"
        });
      }
    }
  });

  // Set primary company mutation
  const setPrimaryCompany = useMutation({
    mutationFn: async (userCompanyId: number) => {
      const response = await apiRequest('PATCH', `/api/user-companies/${userCompanyId}/primary`, {});
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/users/${userId}/companies`] });
      toast({
        title: "Primary Company Set",
        description: "Your primary company has been updated."
      });
      setIsSettingPrimary(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to set primary company.",
        variant: "destructive"
      });
      setIsSettingPrimary(false);
    }
  });

  const resetForm = () => {
    setCompanyForm({
      name: '',
      address: '',
      phone: '',
      email: '',
      website: ''
    });
    setSelectedCompany(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCompanyForm({
      ...companyForm,
      [name]: value
    });
  };

  const handleAddSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createCompany.mutate(companyForm);
  };

  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateCompany.mutate(companyForm);
  };

  const handleDeleteConfirm = () => {
    deleteCompany.mutate();
  };

  const handleSetPrimary = (userCompanyId: number) => {
    setIsSettingPrimary(true);
    setPrimaryCompany.mutate(userCompanyId);
  };

  const openEditModal = (company: any) => {
    setSelectedCompany(company);
    setCompanyForm({
      name: company.name || '',
      address: company.address || '',
      phone: company.phone || '',
      email: company.email || '',
      website: company.website || ''
    });
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (company: any) => {
    setSelectedCompany(company);
    setIsDeleteModalOpen(true);
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Company Management</CardTitle>
            <CardDescription>
              Manage your companies and switch between them
            </CardDescription>
          </div>
          <Button
            onClick={() => {
              resetForm();
              setIsAddModalOpen(true);
            }}
            className="flex items-center gap-1"
          >
            <Plus size={16} />
            Add Company
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-6">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : companies?.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {companies.map((company: any) => (
                  <TableRow key={company.id}>
                    <TableCell className="font-medium">{company.name}</TableCell>
                    <TableCell>{company.address || "—"}</TableCell>
                    <TableCell>
                      {company.phone && (
                        <div>{company.phone}</div>
                      )}
                      {company.email && (
                        <div className="text-sm text-gray-500">{company.email}</div>
                      )}
                      {company.website && (
                        <div className="text-sm text-gray-500">{company.website}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {company.is_primary ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Primary
                        </span>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={() => handleSetPrimary(company.user_company_id)}
                          disabled={isSettingPrimary}
                        >
                          {isSettingPrimary ? (
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                          ) : (
                            <RefreshCcw className="mr-1 h-3 w-3" />
                          )}
                          Set as Primary
                        </Button>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => openEditModal(company)}
                        >
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0 text-red-600 hover:bg-red-50"
                          onClick={() => openDeleteModal(company)}
                          disabled={company.is_primary}
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No companies have been created yet.</p>
              <Button
                variant="outline"
                onClick={() => setIsAddModalOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Create your first company
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Company Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Company</DialogTitle>
            <DialogDescription>
              Create a new company. Fill in the company details below.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="add-name">Company Name</Label>
                <Input
                  id="add-name"
                  name="name"
                  placeholder="Company name"
                  value={companyForm.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="add-address">Address</Label>
                <Textarea
                  id="add-address"
                  name="address"
                  placeholder="Full address"
                  value={companyForm.address}
                  onChange={handleInputChange}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="add-phone">Phone</Label>
                  <Input
                    id="add-phone"
                    name="phone"
                    placeholder="Phone number"
                    value={companyForm.phone}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="add-email">Email</Label>
                  <Input
                    id="add-email"
                    name="email"
                    type="email"
                    placeholder="Email address"
                    value={companyForm.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="add-website">Website</Label>
                <Input
                  id="add-website"
                  name="website"
                  placeholder="Website URL"
                  value={companyForm.website}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createCompany.isPending}>
                {createCompany.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Company"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Company Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Company</DialogTitle>
            <DialogDescription>
              Update the company information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Company Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  placeholder="Company name"
                  value={companyForm.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-address">Address</Label>
                <Textarea
                  id="edit-address"
                  name="address"
                  placeholder="Full address"
                  value={companyForm.address}
                  onChange={handleInputChange}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-phone">Phone</Label>
                  <Input
                    id="edit-phone"
                    name="phone"
                    placeholder="Phone number"
                    value={companyForm.phone}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-email">Email</Label>
                  <Input
                    id="edit-email"
                    name="email"
                    type="email"
                    placeholder="Email address"
                    value={companyForm.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-website">Website</Label>
                <Input
                  id="edit-website"
                  name="website"
                  placeholder="Website URL"
                  value={companyForm.website}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateCompany.isPending}>
                {updateCompany.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  "Update Company"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Company</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this company? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <p className="py-4">
            Company: <strong>{selectedCompany?.name}</strong>
          </p>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteCompany.isPending}
            >
              {deleteCompany.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Company"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}