import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/lib/auth';

interface SystemSettings {
  date_format: string;
  currency_symbol: string;
}

interface SettingsContextType {
  settings: SystemSettings;
  isLoading: boolean;
  error: Error | null;
}

const defaultSettings: SystemSettings = {
  date_format: 'dd-MM-yyyy',
  currency_symbol: '₹',
};

const SettingsContext = createContext<SettingsContextType>({
  settings: defaultSettings,
  isLoading: false,
  error: null,
});

export const useSettings = () => useContext(SettingsContext);

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider: React.FC<SettingsProviderProps> = ({ children }) => {
  const { getCurrentUser, isAuthenticated } = useAuth();
  const user = getCurrentUser();
  const companyId = user?.company_id;
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);

  // Fetch settings from API
  const {
    data: settingsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: [`/api/companies/${companyId}/settings`],
    enabled: !!companyId && isAuthenticated(),
  });

  // Update settings when data is loaded
  useEffect(() => {
    if (settingsData) {
      setSettings({
        date_format: settingsData.date_format || defaultSettings.date_format,
        currency_symbol: settingsData.currency_symbol || defaultSettings.currency_symbol,
      });
    }
  }, [settingsData]);

  return (
    <SettingsContext.Provider
      value={{
        settings,
        isLoading,
        error: error as Error | null,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};
