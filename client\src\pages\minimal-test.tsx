import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { DynamicFormBuilder } from "@/components/form-builder/DynamicFormBuilder";
import { useContextData } from "@/lib/useContextData";

export default function MinimalTest() {
  const [editingTemplate, setEditingTemplate] = useState(undefined);
  const { companyId } = useContextData();

  const handleSaveTemplate = (template) => {
    console.log("Saving template", template);
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <DynamicFormBuilder
          initialTemplate={editingTemplate}
          onSave={handleSaveTemplate}
          companyId={companyId}
        />
      </CardContent>
    </Card>
  );
}