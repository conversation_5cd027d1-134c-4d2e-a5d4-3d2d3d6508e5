import { Express, Response } from 'express';
import { authMiddleware, requireCompanyAccess, requireRole, AuthRequest } from '../middleware/auth';
import { requirePrefixSettings } from '../middleware/prefix-settings';
import { storage } from '../storage';
import { insertPartnerSchema, companyPrefixSettings, companies } from '../../shared/schema';
import { ZodError } from 'zod';
import errorLogger from '../utils/errorLogger';
import { db } from '../db';
import { eq } from 'drizzle-orm';

// Helper to get company name from company_id (fallback for prefix generation)
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);
    let prefix = '';

    if (words.length === 0) {
      prefix = 'COMP';
    } else if (words.length === 1) {
      // Single word: use first 2-3 characters
      const word = words[0].toUpperCase();
      prefix = word.length >= 3 ? word.substring(0, 3) : word;
    } else if (words.length === 2) {
      // Two words: use first 2 chars of each
      prefix = words[0].substring(0, 2).toUpperCase() + words[1].substring(0, 2).toUpperCase();
    } else {
      // Multiple words: use first char of first 3-4 words
      prefix = words.slice(0, Math.min(4, words.length))
        .map(word => word.charAt(0).toUpperCase())
        .join('');
    }

    // Ensure prefix is at least 2 characters and at most 4
    if (prefix.length < 2) {
      prefix = prefix.padEnd(2, 'X');
    } else if (prefix.length > 4) {
      prefix = prefix.substring(0, 4);
    }

    console.log(`Generated company prefix: ${prefix} for company ${companyId}`);
    return prefix;
  } catch (error) {
    console.error(`Error getting company name for company ${companyId}:`, error);
    return `COMP`;
  }
}

// Helper to get prefix from company_prefix_settings table
async function getPrefixFromSettings(companyId: number, entityType: 'loan' | 'collection' | 'customer' | 'partner' | 'agent'): Promise<{ prefix: string, startNumber: number }> {
  try {
    // Get company prefix settings
    const settings = await db.query.companyPrefixSettings.findFirst({
      where: eq(companyPrefixSettings.company_id, companyId),
    });

    if (!settings) {
      console.log(`No prefix settings found for company ${companyId}, falling back to company name`);
      // Fall back to company name-based prefix if no settings found
      const prefix = await getCompanyName(companyId);
      return { prefix, startNumber: 1 };
    }

    // Extract the appropriate prefix and start number based on entity type
    let prefix: string;
    let startNumber: number;

    switch (entityType) {
      case 'loan':
        prefix = settings.loan_prefix;
        startNumber = settings.loan_start_number;
        break;
      case 'collection':
        prefix = settings.collection_prefix;
        startNumber = settings.collection_start_number;
        break;
      case 'customer':
        prefix = settings.customer_prefix;
        startNumber = settings.customer_start_number;
        break;
      case 'partner':
        prefix = settings.partner_prefix;
        startNumber = settings.partner_start_number;
        break;
      case 'agent':
        prefix = settings.agent_prefix;
        startNumber = settings.agent_start_number;
        break;
      default:
        throw new Error(`Unknown entity type: ${entityType}`);
    }

    console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for ${entityType}`);
    return { prefix, startNumber };
  } catch (error) {
    console.error(`Error fetching prefix settings for company ${companyId}:`, error);
    // Fall back to company name-based prefix if error occurs
    const prefix = await getCompanyName(companyId);
    return { prefix, startNumber: 1 };
  }
}

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

export function registerPartnerRoutes(app: Express): void {
  // Get all partners for a company
  app.get('/api/companies/:companyId/partners', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      console.log(`Fetching partners for company ${companyId}`);
      
      errorLogger.logInfo(
        `Fetching partners for company ${companyId}`,
        'partners-route',
        { companyId, userId: req.user?.id }
      );

      const partners = await storage.getPartnersByCompany(companyId);
      console.log(`Found ${partners.length} partners for company ${companyId}`);
      
      return res.json(partners);
    } catch (error) {
      console.error('Error fetching partners:', error);
      errorLogger.logError(
        'Failed to fetch partners',
        'partners-route',
        { error, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch partners')
      );
    }
  });

  // Get a specific partner
  app.get('/api/companies/:companyId/partners/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const id = parseInt(req.params.id, 10);

      console.log(`Fetching partner ${id} for company ${companyId}`);
      const partner = await storage.getPartner(id);

      if (!partner || partner.company_id !== companyId) {
        console.log(`Partner ${id} not found or doesn't belong to company ${companyId}`);
        errorLogger.logWarning(
          `Partner not found`,
          'get-partner-route',
          { id, companyId, userId: req.user?.id }
        );
        return res.status(404).json({ message: 'Partner not found' });
      }

      return res.json(partner);
    } catch (error) {
      console.error('Error fetching partner:', error);
      errorLogger.logError(
        'Failed to fetch partner',
        'get-partner-route',
        { error, id: req.params.id, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch partner')
      );
    }
  });

  // Create a new partner
  app.post('/api/companies/:companyId/partners', authMiddleware, requireCompanyAccess, requirePrefixSettings, requireRole(['owner', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);

      console.log(`Creating partner for company ${companyId}:`, req.body);

      // Validate the request body
      const result = insertPartnerSchema.safeParse({
        ...req.body,
        company_id: companyId
      });

      if (!result.success) {
        console.log('Validation failed for partner:', formatZodError(result.error));
        return res.status(400).json({ message: 'Invalid partner data', errors: formatZodError(result.error) });
      }

      // Generate company-specific partner reference code
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'partner');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for partner`);

      // Get the highest existing partner reference code for this company
      const highestSerial = await storage.getHighestPartnerSerial(companyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const partnerReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated partner reference code: ${partnerReferenceCode} for company ${companyId}`);

      // Add the reference code to the partner data
      const partnerDataWithReferenceCode = {
        ...result.data,
        partner_reference_code: partnerReferenceCode
      };

      // Create the partner
      const partner = await storage.createPartner(partnerDataWithReferenceCode);
      console.log(`Created partner ${partner.id} for company ${companyId}`);

      errorLogger.logInfo(
        `Partner created successfully`,
        'create-partner-route',
        { partnerId: partner.id, companyId, userId: req.user?.id }
      );

      return res.status(201).json(partner);
    } catch (error) {
      console.error('Error creating partner:', error);
      errorLogger.logError(
        'Failed to create partner',
        'create-partner-route',
        { error, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to create partner')
      );
    }
  });

  // Update a partner
  app.put('/api/companies/:companyId/partners/:id', authMiddleware, requireCompanyAccess, requireRole(['owner', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const id = parseInt(req.params.id, 10);

      console.log(`Updating partner ${id} for company ${companyId}:`, req.body);

      // Make sure the partner exists and belongs to the company
      const existingPartner = await storage.getPartner(id);

      if (!existingPartner || existingPartner.company_id !== companyId) {
        console.log(`Partner ${id} not found or doesn't belong to company ${companyId}`);
        return res.status(404).json({ message: 'Partner not found' });
      }

      // Validate the request body
      const result = insertPartnerSchema.partial().safeParse({
        ...req.body,
        company_id: companyId
      });

      if (!result.success) {
        console.log('Validation failed for partner update:', formatZodError(result.error));
        return res.status(400).json({ message: 'Invalid partner data', errors: formatZodError(result.error) });
      }

      // Update the partner
      const updatedPartner = await storage.updatePartner(id, companyId, result.data);
      console.log(`Updated partner ${id} for company ${companyId}`);
      
      errorLogger.logInfo(
        `Partner updated successfully`,
        'update-partner-route',
        { partnerId: id, companyId, userId: req.user?.id }
      );
      
      return res.json(updatedPartner);
    } catch (error) {
      console.error('Error updating partner:', error);
      errorLogger.logError(
        'Failed to update partner',
        'update-partner-route',
        { error, id: req.params.id, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to update partner')
      );
    }
  });

  // Delete a partner
  app.delete('/api/companies/:companyId/partners/:id', authMiddleware, requireCompanyAccess, requireRole(['owner', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const id = parseInt(req.params.id, 10);

      console.log(`Deleting partner ${id} for company ${companyId}`);

      // Make sure the partner exists and belongs to the company
      const existingPartner = await storage.getPartner(id);

      if (!existingPartner || existingPartner.company_id !== companyId) {
        console.log(`Partner ${id} not found or doesn't belong to company ${companyId}`);
        return res.status(404).json({ message: 'Partner not found' });
      }

      // Delete the partner
      const result = await storage.deletePartner(id, companyId);

      if (!result.success) {
        console.log(`Failed to delete partner ${id}: ${result.error}`);
        return res.status(500).json({ message: result.error || 'Failed to delete partner' });
      }

      console.log(`Successfully deleted partner ${id} for company ${companyId}`);
      errorLogger.logInfo(
        `Partner deleted successfully`,
        'delete-partner-route',
        { partnerId: id, companyId, userId: req.user?.id }
      );
      
      return res.json({ message: 'Partner deleted successfully' });
    } catch (error) {
      console.error('Error deleting partner:', error);
      errorLogger.logError(
        'Failed to delete partner',
        'delete-partner-route',
        { error, id: req.params.id, companyId: req.params.companyId, userId: req.user?.id }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to delete partner')
      );
    }
  });
}
