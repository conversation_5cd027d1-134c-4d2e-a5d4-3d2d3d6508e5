import { db } from "../db";
import { payments, collections } from "@shared/schema";
import { eq, and, desc, sql } from "drizzle-orm";
import errorLogger from './errorLogger';
import type { Payment, InsertPayment, Collection } from "@shared/schema";

/**
 * PaymentProcessor - Handles payment generation, receipt creation and collection status updates
 */
export class PaymentProcessor {
  /**
   * Generate a unique receipt number for a payment
   * Format: RCPT-YYMMDD-COMPANYID-SEQUENTIAL
   */
  static async generateReceiptNumber(companyId: number): Promise<string> {
    try {
      // Get current date components for receipt number prefix
      const now = new Date();
      const year = now.getFullYear().toString().slice(-2); // Last 2 digits of year
      const month = (now.getMonth() + 1).toString().padStart(2, '0'); // Month (01-12)
      const day = now.getDate().toString().padStart(2, '0'); // Day (01-31)

      // Count existing receipts for this company today to generate a sequential number
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);

      const result = await db
        .select({ count: sql<number>`count(*)` })
        .from(payments)
        .where(and(
          eq(payments.company_id, companyId),
          sql`${payments.payment_date} >= ${todayStart}`,
          sql`${payments.payment_date} < ${todayEnd}`
        ));

      const count = result[0]?.count || 0;
      const sequentialNumber = (count + 1).toString().padStart(4, '0');

      // Format: RCPT-YYMMDD-COMPANYID-SEQUENTIAL
      const receiptNumber = `RCPT-${year}${month}${day}-${companyId}-${sequentialNumber}`;

      errorLogger.logInfo(
        `Generated receipt number: ${receiptNumber} for company=${companyId}`,
        'payment-processor'
      );

      return receiptNumber;
    } catch (error) {
      errorLogger.logError(
        `Failed to generate receipt number for company=${companyId}`,
        'payment-processor',
        error as Error
      );
      // Fallback to a simple format if there's an error
      return `RCPT-${Date.now()}-${companyId}`;
    }
  }

  /**
   * Create a payment entry for a completed collection
   * OPTIMIZED: Uses a transaction to ensure atomicity and reduce round trips
   */
  static async createPaymentFromCollection(
    collection: Collection,
    paymentMethod: string,
    userId: number | null,
    fineAmount: number | null = null,
    transactionReference: string | null = null,
    notes: string | null = null,
    receiptNumber?: string // Allow passing an existing receipt number
  ): Promise<Payment> {
    try {
      // Generate receipt number if not provided
      if (!receiptNumber) {
        receiptNumber = await this.generateReceiptNumber(collection.company_id);
      }

      // Use a transaction to ensure both operations succeed or fail together
      return await db.transaction(async (tx) => {
        // Create payment record
        const paymentData: InsertPayment = {
          company_id: collection.company_id,
          collection_id: collection.id,
          payment_schedule_id: null,
          amount: collection.amount.toString(),
          payment_date: new Date(),
          payment_method: paymentMethod as any,
          fine_amount: fineAmount ? fineAmount.toString() : '0',
          receipt_number: receiptNumber!,
          transaction_reference: transactionReference,
          notes: notes,
          recorded_by: userId
        };

        // Insert payment and update collection in a single transaction
        const [payment] = await tx
          .insert(payments)
          .values(paymentData)
          .returning();

        // Only update the collection if it doesn't already have this receipt number
        if (collection.receipt_id !== receiptNumber) {
          await tx
            .update(collections)
            .set({
              receipt_id: receiptNumber,
              updated_at: new Date()
            })
            .where(eq(collections.id, collection.id));
        }

        return payment;
      });
    } catch (error) {
      errorLogger.logError(
        `Failed to create payment for collection=${collection.id}`,
        'payment-processor',
        error as Error
      );
      throw error;
    }
  }

  /**
   * Get payments by collection ID
   */
  static async getPaymentsByCollection(collectionId: number, companyId: number): Promise<Payment[]> {
    try {
      return await db
        .select()
        .from(payments)
        .where(and(
          eq(payments.collection_id, collectionId),
          eq(payments.company_id, companyId)
        ))
        .orderBy(desc(payments.payment_date));
    } catch (error) {
      errorLogger.logError(
        `Failed to get payments for collection=${collectionId}`,
        'payment-processor',
        error as Error
      );
      return [];
    }
  }

  /**
   * Get a single payment by ID
   */
  static async getPayment(id: number, companyId: number): Promise<Payment | undefined> {
    try {
      const [payment] = await db
        .select()
        .from(payments)
        .where(and(
          eq(payments.id, id),
          eq(payments.company_id, companyId)
        ));

      return payment;
    } catch (error) {
      errorLogger.logError(
        `Failed to get payment id=${id}`,
        'payment-processor',
        error as Error
      );
      return undefined;
    }
  }
}