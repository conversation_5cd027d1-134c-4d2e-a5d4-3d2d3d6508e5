import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  AlertTriangle, 
  Activity, 
  Eye,
  Clock,
  Users,
  TrendingUp,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { formatDistanceToNow } from 'date-fns';

interface SecurityEvent {
  id: number;
  event_id: string;
  event_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  event_description: string;
  timestamp: string;
  user_id?: number;
  ip_address?: string;
  location_country?: string;
  risk_score: number;
  resolved: boolean;
}

interface SecurityAlert {
  id: number;
  alert_id: string;
  alert_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  acknowledged: boolean;
  created_at: string;
}

interface DashboardData {
  summary: {
    eventCounts: Record<string, number>;
    openAlerts: number;
    activeRules: number;
  };
  topEventTypes: Array<{ event_type: string; count: number }>;
  recentHighSeverityEvents: SecurityEvent[];
}

const severityColors = {
  low: 'bg-blue-100 text-blue-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  critical: 'bg-red-100 text-red-800'
};

const severityIcons = {
  low: CheckCircle,
  medium: AlertCircle,
  high: AlertTriangle,
  critical: XCircle
};

export function SecurityMonitoringDashboard() {
  const queryClient = useQueryClient();
  const [selectedTab, setSelectedTab] = useState('overview');

  // Fetch dashboard data
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery({
    queryKey: ['security', 'dashboard'],
    queryFn: () => apiRequest('/api/monitoring/dashboard'),
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Fetch security events
  const { data: eventsData, isLoading: eventsLoading } = useQuery({
    queryKey: ['security', 'events'],
    queryFn: () => apiRequest('/api/monitoring/events?limit=50'),
    refetchInterval: 15000 // Refresh every 15 seconds
  });

  // Fetch security alerts
  const { data: alertsData, isLoading: alertsLoading } = useQuery({
    queryKey: ['security', 'alerts'],
    queryFn: () => apiRequest('/api/monitoring/alerts?status=open'),
    refetchInterval: 15000
  });

  // Acknowledge alert mutation
  const acknowledgeAlertMutation = useMutation({
    mutationFn: (alertId: string) => 
      apiRequest(`/api/monitoring/alerts/${alertId}/acknowledge`, { method: 'PUT' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['security'] });
    }
  });

  // Resolve alert mutation
  const resolveAlertMutation = useMutation({
    mutationFn: ({ alertId, notes }: { alertId: string; notes: string }) => 
      apiRequest(`/api/monitoring/alerts/${alertId}/resolve`, { 
        method: 'PUT',
        body: { resolution_notes: notes }
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['security'] });
    }
  });

  const dashboard: DashboardData = dashboardData || {
    summary: { eventCounts: {}, openAlerts: 0, activeRules: 0 },
    topEventTypes: [],
    recentHighSeverityEvents: []
  };

  const events: SecurityEvent[] = eventsData?.events || [];
  const alerts: SecurityAlert[] = alertsData?.alerts || [];

  const handleAcknowledgeAlert = (alertId: string) => {
    acknowledgeAlertMutation.mutate(alertId);
  };

  const handleResolveAlert = (alertId: string) => {
    const notes = prompt('Enter resolution notes:');
    if (notes) {
      resolveAlertMutation.mutate({ alertId, notes });
    }
  };

  const getSeverityIcon = (severity: string) => {
    const IconComponent = severityIcons[severity as keyof typeof severityIcons] || AlertCircle;
    return <IconComponent className="h-4 w-4" />;
  };

  if (dashboardLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading security dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Security Monitoring</h1>
          <p className="text-muted-foreground">
            Real-time security monitoring and threat detection
          </p>
        </div>
        <Button variant="outline">
          <Settings className="h-4 w-4 mr-2" />
          Configure Rules
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">Active Rules</p>
                <p className="text-2xl font-bold">{dashboard.summary.activeRules}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Open Alerts</p>
                <p className="text-2xl font-bold">{dashboard.summary.openAlerts}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">Events (24h)</p>
                <p className="text-2xl font-bold">
                  {Object.values(dashboard.summary.eventCounts).reduce((a, b) => a + b, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm font-medium">Critical Events</p>
                <p className="text-2xl font-bold">
                  {dashboard.summary.eventCounts.critical || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="alerts">Active Alerts</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent High Severity Events */}
            <Card>
              <CardHeader>
                <CardTitle>Recent High Severity Events</CardTitle>
                <CardDescription>
                  Critical and high severity security events from the last 24 hours
                </CardDescription>
              </CardHeader>
              <CardContent>
                {dashboard.recentHighSeverityEvents.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No high severity events in the last 24 hours
                  </p>
                ) : (
                  <div className="space-y-3">
                    {dashboard.recentHighSeverityEvents.slice(0, 5).map((event) => (
                      <div key={event.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                        <div className="mt-1">
                          {getSeverityIcon(event.severity)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <Badge className={severityColors[event.severity]}>
                              {event.severity}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {formatDistanceToNow(new Date(event.timestamp), { addSuffix: true })}
                            </span>
                          </div>
                          <p className="text-sm font-medium">{event.event_description}</p>
                          <p className="text-xs text-muted-foreground">
                            Risk Score: {event.risk_score}/100
                            {event.ip_address && ` • IP: ${event.ip_address}`}
                            {event.location_country && ` • ${event.location_country}`}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Top Event Types */}
            <Card>
              <CardHeader>
                <CardTitle>Top Event Types (7 days)</CardTitle>
                <CardDescription>
                  Most frequent security event types
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {dashboard.topEventTypes.slice(0, 8).map((eventType, index) => (
                    <div key={eventType.event_type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-primary"></div>
                        <span className="text-sm font-medium capitalize">
                          {eventType.event_type.replace(/_/g, ' ')}
                        </span>
                      </div>
                      <Badge variant="secondary">{eventType.count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Events</CardTitle>
              <CardDescription>
                Recent security events and monitoring data
              </CardDescription>
            </CardHeader>
            <CardContent>
              {eventsLoading ? (
                <div className="text-center py-4">Loading events...</div>
              ) : events.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No security events found
                </p>
              ) : (
                <div className="space-y-3">
                  {events.map((event) => (
                    <div key={event.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <div className="mt-1">
                        {getSeverityIcon(event.severity)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <Badge className={severityColors[event.severity]}>
                            {event.severity}
                          </Badge>
                          <Badge variant="outline">
                            {event.event_type.replace(/_/g, ' ')}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {formatDistanceToNow(new Date(event.timestamp), { addSuffix: true })}
                          </span>
                          {event.resolved && (
                            <Badge variant="secondary">Resolved</Badge>
                          )}
                        </div>
                        <p className="text-sm font-medium">{event.event_description}</p>
                        <p className="text-xs text-muted-foreground">
                          Event ID: {event.event_id} • Risk Score: {event.risk_score}/100
                          {event.ip_address && ` • IP: ${event.ip_address}`}
                          {event.location_country && ` • ${event.location_country}`}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Security Alerts</CardTitle>
              <CardDescription>
                Security alerts requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              {alertsLoading ? (
                <div className="text-center py-4">Loading alerts...</div>
              ) : alerts.length === 0 ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    No active security alerts. All systems are operating normally.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  {alerts.map((alert) => (
                    <div key={alert.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          {getSeverityIcon(alert.severity)}
                          <Badge className={severityColors[alert.severity]}>
                            {alert.severity}
                          </Badge>
                          <Badge variant="outline">{alert.status}</Badge>
                          {alert.acknowledged && (
                            <Badge variant="secondary">Acknowledged</Badge>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          {!alert.acknowledged && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleAcknowledgeAlert(alert.alert_id)}
                              disabled={acknowledgeAlertMutation.isPending}
                            >
                              Acknowledge
                            </Button>
                          )}
                          <Button
                            size="sm"
                            onClick={() => handleResolveAlert(alert.alert_id)}
                            disabled={resolveAlertMutation.isPending}
                          >
                            Resolve
                          </Button>
                        </div>
                      </div>
                      <h4 className="font-medium mb-2">{alert.title}</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        {alert.description}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Created {formatDistanceToNow(new Date(alert.created_at), { addSuffix: true })} • 
                        Alert ID: {alert.alert_id}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Analytics</CardTitle>
              <CardDescription>
                Security trends and patterns analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Event Severity Distribution */}
                <div>
                  <h4 className="font-medium mb-3">Event Severity Distribution (24h)</h4>
                  <div className="space-y-2">
                    {Object.entries(dashboard.summary.eventCounts).map(([severity, count]) => (
                      <div key={severity} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getSeverityIcon(severity)}
                          <span className="capitalize">{severity}</span>
                        </div>
                        <Badge variant="secondary">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* System Health */}
                <div>
                  <h4 className="font-medium mb-3">System Health</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>Security Rules</span>
                      <Badge variant="secondary">{dashboard.summary.activeRules} active</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Open Alerts</span>
                      <Badge variant={dashboard.summary.openAlerts > 0 ? "destructive" : "secondary"}>
                        {dashboard.summary.openAlerts}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Monitoring Status</span>
                      <Badge variant="secondary">Active</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
