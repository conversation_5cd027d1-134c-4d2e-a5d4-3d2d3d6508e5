# User Management & Roles & Permissions System - Development Plan

## Project Overview

This document outlines the comprehensive development plan for implementing an advanced User Management, Roles & Permissions system for the TrackFina Loan SAAS platform. The system will provide granular access control, role-based permissions, and enterprise-grade security features.

## Current State Analysis

### Existing Infrastructure ✅
- **Database Schema**: Basic user, role, and permission tables are implemented
- **Authentication**: JWT-based authentication with middleware
- **Basic RBAC**: Role-based access control with permission checking
- **Frontend Components**: User management UI with role assignment
- **Permission Middleware**: Server-side permission validation

### Current Database Tables
- `users` - User accounts with basic role assignment
- `permissions` - System permissions with categories
- `custom_roles` - Company-specific roles
- `role_permissions` - Role-permission associations
- `user_roles` - User-role assignments
- `groups` - User groups for organizational structure
- `group_users` - Group membership
- `group_roles` - Group-role assignments

### Current Permission Categories
- user_management
- role_management
- group_management
- permission_management
- company_management
- customer_management
- loan_management
- financial_management
- report_management
- system_settings

## Implementation Roadmap

### Phase 1: Enhanced Permission System (Week 1-2)
**Status: 🟡 In Progress**

#### 1.1 Granular Loan Management Permissions
- [ ] **Loan Creation Permissions**
  - `loan_create_basic` - Create loans up to basic limit
  - `loan_create_advanced` - Create loans with advanced features
  - `loan_create_unlimited` - No loan amount restrictions

- [ ] **Loan Approval Workflow**
  - `loan_approve_tier1` - Approve loans up to $10,000
  - `loan_approve_tier2` - Approve loans up to $50,000
  - `loan_approve_tier3` - Approve loans up to $100,000
  - `loan_approve_unlimited` - No approval limits

- [ ] **Loan Modification Permissions**
  - `loan_modify_terms` - Modify loan terms and conditions
  - `loan_modify_interest` - Change interest rates
  - `loan_modify_schedule` - Modify payment schedules
  - `loan_modify_status` - Change loan status

#### 1.2 Customer Data Protection Permissions
- [ ] **Customer Access Levels**
  - `customer_view_basic` - View basic customer info
  - `customer_view_financial` - Access financial documents
  - `customer_view_sensitive` - Access SSN, credit scores
  - `customer_export_data` - Export customer data

- [ ] **Customer Communication**
  - `customer_contact_email` - Send emails to customers
  - `customer_contact_sms` - Send SMS notifications
  - `customer_contact_call` - Make phone calls
  - `customer_contact_automated` - Set up automated communications

#### 1.3 Financial Operations Permissions
- [ ] **Payment Processing**
  - `payment_process_manual` - Process manual payments
  - `payment_process_automated` - Set up automated payments
  - `payment_refund` - Issue refunds
  - `payment_void` - Void transactions

- [ ] **Financial Reporting**
  - `report_view_basic` - View basic reports
  - `report_view_detailed` - Access detailed financial reports
  - `report_export` - Export reports
  - `report_create_custom` - Create custom reports

### Phase 2: Advanced Role Management (Week 3-4)
**Status: 🔴 Not Started**

#### 2.1 Role Hierarchy System
- [ ] **Implement Role Inheritance**
  - Parent-child role relationships
  - Permission inheritance with override capability
  - Conflict resolution for multiple role assignments

- [ ] **Role Templates**
  - Industry-standard role templates
  - Quick role setup for new companies
  - Customizable role templates

#### 2.2 Dynamic Permission Assignment
- [ ] **Conditional Permissions**
  - Time-based permissions (working hours, specific dates)
  - Location-based access controls (IP restrictions, geo-fencing)
  - Amount-based permissions (loan limits, transaction limits)

- [ ] **Temporary Permission Elevation**
  - Emergency access permissions
  - Time-limited elevated access
  - Approval workflow for permission elevation

#### 2.3 Multi-Level Approval Workflows
- [ ] **Approval Chain Configuration**
  - Define approval hierarchies
  - Parallel and sequential approval processes
  - Escalation rules for delayed approvals

- [ ] **Approval Tracking**
  - Audit trail for all approvals
  - Notification system for pending approvals
  - Dashboard for approval managers

### Phase 3: Data Scope & Field-Level Security (Week 5-6)
**Status: 🔴 Not Started**

#### 3.1 Data Scope Permissions
- [ ] **Organizational Data Access**
  - Branch-based data visibility
  - Department-based access controls
  - Team-based data sharing

- [ ] **Hierarchical Data Access**
  - Manager access to subordinate data
  - Regional manager access patterns
  - Executive-level data visibility

#### 3.2 Field-Level Security
- [ ] **Sensitive Data Protection**
  - Field-level encryption for sensitive data
  - Role-based field visibility
  - Data masking for unauthorized users

- [ ] **Dynamic Field Permissions**
  - Context-aware field access
  - Read-only vs editable field controls
  - Conditional field visibility

### Phase 4: Advanced Security Features (Week 7-8)
**Status: 🔴 Not Started**

#### 4.1 Session & Access Management
- [ ] **Enhanced Session Control**
  - Concurrent session limits
  - Device-based access controls
  - Session timeout policies

- [ ] **Access Monitoring**
  - Real-time access monitoring
  - Suspicious activity detection
  - Automated security responses

#### 4.2 Compliance & Audit Features
- [ ] **Comprehensive Audit Trail**
  - Detailed permission usage logs
  - Data access tracking
  - Change history for all permissions

- [ ] **Compliance Reporting**
  - Regulatory compliance reports
  - Access certification workflows
  - Risk assessment tools

### Phase 5: User Experience Enhancements (Week 9-10)
**Status: 🔴 Not Started**

#### 5.1 Self-Service Portal
- [ ] **User Self-Service**
  - Permission request portal
  - Access status dashboard
  - Personal permission history

- [ ] **Manager Tools**
  - Team permission overview
  - Bulk permission management
  - Permission analytics

#### 5.2 Administrative Efficiency
- [ ] **Bulk Operations**
  - Mass user import/export
  - Bulk role assignments
  - Template-based user creation

- [ ] **Advanced Search & Filtering**
  - Complex permission queries
  - User access analytics
  - Permission usage reports

## Technical Implementation Details

### Database Schema Enhancements

#### New Tables Required
```sql
-- Permission conditions for advanced rules
CREATE TABLE permission_conditions (
  id SERIAL PRIMARY KEY,
  permission_id INTEGER REFERENCES permissions(id),
  condition_type VARCHAR(50), -- 'time', 'location', 'amount', 'approval'
  condition_config JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Role hierarchy for inheritance
CREATE TABLE role_hierarchy (
  id SERIAL PRIMARY KEY,
  parent_role_id INTEGER REFERENCES custom_roles(id),
  child_role_id INTEGER REFERENCES custom_roles(id),
  inheritance_type VARCHAR(20) DEFAULT 'inherit', -- 'inherit', 'override'
  created_at TIMESTAMP DEFAULT NOW()
);

-- Approval workflows
CREATE TABLE approval_workflows (
  id SERIAL PRIMARY KEY,
  company_id INTEGER REFERENCES companies(id),
  workflow_name VARCHAR(100),
  workflow_config JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Permission audit trail
CREATE TABLE permission_audit (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  permission_code VARCHAR(100),
  action VARCHAR(50), -- 'granted', 'revoked', 'used'
  context JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### API Endpoints to Implement

#### Permission Management APIs
- `GET /api/permissions/categories` - Get permission categories
- `POST /api/permissions/bulk-assign` - Bulk permission assignment
- `GET /api/permissions/user/:id/effective` - Get effective permissions for user
- `POST /api/permissions/request` - Request additional permissions

#### Role Management APIs
- `POST /api/roles/hierarchy` - Create role hierarchy
- `GET /api/roles/:id/inheritance` - Get role inheritance chain
- `POST /api/roles/templates` - Create role templates
- `GET /api/roles/templates` - List available templates

#### Audit & Compliance APIs
- `GET /api/audit/permissions` - Permission usage audit
- `GET /api/audit/access-log` - User access logs
- `POST /api/compliance/reports` - Generate compliance reports
- `GET /api/compliance/violations` - Get security violations

### Frontend Components to Develop

#### Enhanced Permission Management
- **PermissionMatrix Component** - Visual permission assignment grid
- **RoleHierarchyBuilder** - Drag-and-drop role hierarchy creator
- **ConditionalPermissionEditor** - Configure time/location-based permissions
- **BulkPermissionManager** - Mass permission operations

#### User Experience Components
- **PermissionRequestPortal** - Self-service permission requests
- **AccessDashboard** - Personal access overview
- **ApprovalWorkflowDesigner** - Visual workflow builder
- **SecurityAuditViewer** - Security event browser

## Security Considerations

### Data Protection
- **Encryption**: All sensitive permission data encrypted at rest
- **Access Logging**: Comprehensive audit trail for all permission operations
- **Session Security**: Enhanced session management with device tracking
- **API Security**: Rate limiting and request validation for all permission APIs

### Compliance Requirements
- **GDPR Compliance**: Data protection and user consent management
- **SOX Compliance**: Financial data access controls and audit trails
- **Industry Standards**: Adherence to financial services security standards

## Testing Strategy

### Unit Testing
- Permission calculation logic
- Role inheritance algorithms
- Conditional permission evaluation
- Audit trail generation

### Integration Testing
- End-to-end permission workflows
- Multi-user permission scenarios
- Cross-company data isolation
- Performance testing with large datasets

### Security Testing
- Permission bypass attempts
- Privilege escalation testing
- Session hijacking prevention
- SQL injection and XSS protection

## Performance Optimization

### Caching Strategy
- **Permission Cache**: Redis-based permission caching
- **Role Cache**: Cached role hierarchies and inheritance
- **Session Cache**: Optimized session data storage

### Database Optimization
- **Indexing**: Strategic indexes on permission lookup tables
- **Query Optimization**: Efficient permission checking queries
- **Connection Pooling**: Optimized database connections

## Deployment Plan

### Development Environment
- Local development with Docker containers
- Automated testing pipeline
- Code review and approval process

### Staging Environment
- Full feature testing
- Performance benchmarking
- Security penetration testing

### Production Deployment
- Blue-green deployment strategy
- Database migration scripts
- Rollback procedures
- Monitoring and alerting

## Success Metrics

### Functional Metrics
- **Permission Accuracy**: 99.9% correct permission enforcement
- **Response Time**: <100ms for permission checks
- **User Adoption**: 90% of users actively using new features
- **Admin Efficiency**: 50% reduction in permission management time

### Security Metrics
- **Zero Security Breaches**: No unauthorized access incidents
- **Audit Compliance**: 100% audit trail coverage
- **Access Reviews**: Monthly access certification completion
- **Incident Response**: <1 hour response time for security events

## Risk Mitigation

### Technical Risks
- **Performance Impact**: Comprehensive caching and optimization
- **Data Migration**: Careful migration planning with rollback options
- **Integration Issues**: Thorough testing of all integrations

### Business Risks
- **User Training**: Comprehensive training program for new features
- **Change Management**: Gradual rollout with user feedback incorporation
- **Compliance**: Regular compliance audits and updates

## Detailed Implementation Specifications

### Phase 1 Implementation Details

#### 1.1 Enhanced Permission System Implementation

**Database Migration Script (001_enhanced_permissions.sql)**
```sql
-- Add new loan management permissions
INSERT INTO permissions (code, name, description, category) VALUES
('loan_create_basic', 'Create Basic Loans', 'Create loans up to $10,000', 'loan_management'),
('loan_create_advanced', 'Create Advanced Loans', 'Create loans with advanced features', 'loan_management'),
('loan_create_unlimited', 'Create Unlimited Loans', 'No loan amount restrictions', 'loan_management'),
('loan_approve_tier1', 'Approve Tier 1 Loans', 'Approve loans up to $10,000', 'loan_management'),
('loan_approve_tier2', 'Approve Tier 2 Loans', 'Approve loans up to $50,000', 'loan_management'),
('loan_approve_tier3', 'Approve Tier 3 Loans', 'Approve loans up to $100,000', 'loan_management'),
('loan_approve_unlimited', 'Approve Unlimited Loans', 'No approval limits', 'loan_management');

-- Add customer data protection permissions
INSERT INTO permissions (code, name, description, category) VALUES
('customer_view_basic', 'View Basic Customer Info', 'View basic customer information', 'customer_management'),
('customer_view_financial', 'View Financial Documents', 'Access customer financial documents', 'customer_management'),
('customer_view_sensitive', 'View Sensitive Data', 'Access SSN, credit scores', 'customer_management'),
('customer_export_data', 'Export Customer Data', 'Export customer data', 'customer_management');
```

**Backend Service Enhancement (server/services/permissionService.ts)**
```typescript
export class EnhancedPermissionService {
  async checkLoanCreationPermission(userId: number, loanAmount: number): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);

    if (userPermissions.includes('loan_create_unlimited')) return true;
    if (loanAmount <= 10000 && userPermissions.includes('loan_create_basic')) return true;
    if (userPermissions.includes('loan_create_advanced')) return true;

    return false;
  }

  async checkLoanApprovalPermission(userId: number, loanAmount: number): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);

    if (userPermissions.includes('loan_approve_unlimited')) return true;
    if (loanAmount <= 10000 && userPermissions.includes('loan_approve_tier1')) return true;
    if (loanAmount <= 50000 && userPermissions.includes('loan_approve_tier2')) return true;
    if (loanAmount <= 100000 && userPermissions.includes('loan_approve_tier3')) return true;

    return false;
  }

  async checkCustomerDataAccess(userId: number, dataType: string): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);

    switch (dataType) {
      case 'basic':
        return userPermissions.includes('customer_view_basic');
      case 'financial':
        return userPermissions.includes('customer_view_financial');
      case 'sensitive':
        return userPermissions.includes('customer_view_sensitive');
      default:
        return false;
    }
  }
}
```

**Frontend Permission Component (client/src/components/permissions/PermissionMatrix.tsx)**
```typescript
interface PermissionMatrixProps {
  roles: Role[];
  permissions: Permission[];
  onPermissionChange: (roleId: number, permissionId: number, granted: boolean) => void;
}

export function PermissionMatrix({ roles, permissions, onPermissionChange }: PermissionMatrixProps) {
  const groupedPermissions = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) acc[permission.category] = [];
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  return (
    <div className="permission-matrix">
      <table className="w-full border-collapse">
        <thead>
          <tr>
            <th className="border p-2">Permission</th>
            {roles.map(role => (
              <th key={role.id} className="border p-2">{role.name}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Object.entries(groupedPermissions).map(([category, perms]) => (
            <React.Fragment key={category}>
              <tr className="bg-gray-100">
                <td colSpan={roles.length + 1} className="border p-2 font-bold">
                  {category.replace('_', ' ').toUpperCase()}
                </td>
              </tr>
              {perms.map(permission => (
                <tr key={permission.id}>
                  <td className="border p-2">{permission.name}</td>
                  {roles.map(role => (
                    <td key={role.id} className="border p-2 text-center">
                      <Checkbox
                        checked={role.permissions?.includes(permission.id) || false}
                        onCheckedChange={(checked) =>
                          onPermissionChange(role.id, permission.id, !!checked)
                        }
                      />
                    </td>
                  ))}
                </tr>
              ))}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
}
```

#### 1.2 Middleware Enhancement

**Enhanced Permission Middleware (server/middleware/enhancedPermission.ts)**
```typescript
export function requireLoanPermission(action: 'create' | 'approve', amountField?: string) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const amount = amountField ? req.body[amountField] : 0;
      const permissionService = new EnhancedPermissionService();

      let hasPermission = false;

      if (action === 'create') {
        hasPermission = await permissionService.checkLoanCreationPermission(req.user.id, amount);
      } else if (action === 'approve') {
        hasPermission = await permissionService.checkLoanApprovalPermission(req.user.id, amount);
      }

      if (!hasPermission) {
        return res.status(403).json({
          message: `Insufficient permissions for ${action} operation`,
          required_amount_limit: amount,
          action: action
        });
      }

      next();
    } catch (error) {
      console.error('Enhanced permission middleware error:', error);
      return res.status(500).json({ message: 'Permission check failed' });
    }
  };
}

export function requireCustomerDataAccess(dataType: 'basic' | 'financial' | 'sensitive') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const permissionService = new EnhancedPermissionService();
      const hasAccess = await permissionService.checkCustomerDataAccess(req.user.id, dataType);

      if (!hasAccess) {
        return res.status(403).json({
          message: `Access denied to ${dataType} customer data`,
          required_permission: `customer_view_${dataType}`
        });
      }

      next();
    } catch (error) {
      console.error('Customer data access middleware error:', error);
      return res.status(500).json({ message: 'Access check failed' });
    }
  };
}
```

### Phase 2 Implementation Details

#### 2.1 Role Hierarchy System

**Database Schema for Role Hierarchy**
```sql
-- Role hierarchy table
CREATE TABLE role_hierarchy (
  id SERIAL PRIMARY KEY,
  parent_role_id INTEGER REFERENCES custom_roles(id) ON DELETE CASCADE,
  child_role_id INTEGER REFERENCES custom_roles(id) ON DELETE CASCADE,
  inheritance_type VARCHAR(20) DEFAULT 'inherit', -- 'inherit', 'override', 'deny'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(parent_role_id, child_role_id)
);

-- Role templates for quick setup
CREATE TABLE role_templates (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_config JSONB NOT NULL,
  industry VARCHAR(50),
  is_system BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);
```

**Role Hierarchy Service (server/services/roleHierarchyService.ts)**
```typescript
export class RoleHierarchyService {
  async createRoleHierarchy(parentRoleId: number, childRoleId: number, inheritanceType: string = 'inherit') {
    // Check for circular dependencies
    if (await this.wouldCreateCircularDependency(parentRoleId, childRoleId)) {
      throw new Error('Cannot create circular role hierarchy');
    }

    return await db.insert(roleHierarchy).values({
      parent_role_id: parentRoleId,
      child_role_id: childRoleId,
      inheritance_type: inheritanceType
    });
  }

  async getEffectivePermissions(roleId: number): Promise<string[]> {
    const permissions = new Set<string>();
    const visited = new Set<number>();

    await this.collectPermissions(roleId, permissions, visited);
    return Array.from(permissions);
  }

  private async collectPermissions(roleId: number, permissions: Set<string>, visited: Set<number>) {
    if (visited.has(roleId)) return;
    visited.add(roleId);

    // Get direct permissions
    const directPermissions = await this.getDirectRolePermissions(roleId);
    directPermissions.forEach(p => permissions.add(p));

    // Get inherited permissions
    const parentRoles = await this.getParentRoles(roleId);
    for (const parent of parentRoles) {
      if (parent.inheritance_type === 'inherit') {
        await this.collectPermissions(parent.parent_role_id, permissions, visited);
      }
    }
  }

  private async wouldCreateCircularDependency(parentId: number, childId: number): Promise<boolean> {
    const descendants = await this.getAllDescendants(childId);
    return descendants.includes(parentId);
  }
}
```

#### 2.2 Conditional Permissions

**Conditional Permission Schema**
```sql
CREATE TABLE permission_conditions (
  id SERIAL PRIMARY KEY,
  permission_id INTEGER REFERENCES permissions(id) ON DELETE CASCADE,
  condition_type VARCHAR(50) NOT NULL, -- 'time', 'location', 'amount', 'approval'
  condition_config JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Examples of condition_config:
-- Time-based: {"start_time": "09:00", "end_time": "17:00", "days": ["monday", "tuesday"]}
-- Location-based: {"allowed_ips": ["***********/24"], "allowed_countries": ["US", "CA"]}
-- Amount-based: {"min_amount": 0, "max_amount": 10000}
-- Approval-based: {"requires_approval": true, "approver_roles": ["manager", "director"]}
```

**Conditional Permission Service (server/services/conditionalPermissionService.ts)**
```typescript
export class ConditionalPermissionService {
  async checkConditionalPermission(
    userId: number,
    permissionCode: string,
    context: PermissionContext
  ): Promise<boolean> {
    const conditions = await this.getPermissionConditions(permissionCode);

    for (const condition of conditions) {
      if (!await this.evaluateCondition(condition, context)) {
        return false;
      }
    }

    return true;
  }

  private async evaluateCondition(condition: PermissionCondition, context: PermissionContext): Promise<boolean> {
    switch (condition.condition_type) {
      case 'time':
        return this.evaluateTimeCondition(condition.condition_config, context.timestamp);
      case 'location':
        return this.evaluateLocationCondition(condition.condition_config, context.ip_address);
      case 'amount':
        return this.evaluateAmountCondition(condition.condition_config, context.amount);
      case 'approval':
        return this.evaluateApprovalCondition(condition.condition_config, context);
      default:
        return true;
    }
  }

  private evaluateTimeCondition(config: any, timestamp: Date): boolean {
    const currentTime = timestamp.toTimeString().slice(0, 5);
    const currentDay = timestamp.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

    if (config.start_time && currentTime < config.start_time) return false;
    if (config.end_time && currentTime > config.end_time) return false;
    if (config.days && !config.days.includes(currentDay)) return false;

    return true;
  }

  private evaluateLocationCondition(config: any, ipAddress: string): boolean {
    if (config.allowed_ips) {
      return config.allowed_ips.some((allowedIp: string) =>
        this.isIpInRange(ipAddress, allowedIp)
      );
    }

    // Additional location checks (country, etc.) can be added here
    return true;
  }

  private evaluateAmountCondition(config: any, amount?: number): boolean {
    if (amount === undefined) return true;

    if (config.min_amount && amount < config.min_amount) return false;
    if (config.max_amount && amount > config.max_amount) return false;

    return true;
  }
}
```

### Phase 3 Implementation Details

#### 3.1 Data Scope Permissions

**Data Scope Schema**
```sql
CREATE TABLE data_scope_rules (
  id SERIAL PRIMARY KEY,
  role_id INTEGER REFERENCES custom_roles(id) ON DELETE CASCADE,
  scope_type VARCHAR(50) NOT NULL, -- 'branch', 'department', 'team', 'hierarchy'
  scope_config JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Add scope columns to existing tables
ALTER TABLE users ADD COLUMN branch_id INTEGER REFERENCES branches(id);
ALTER TABLE users ADD COLUMN department_id INTEGER;
ALTER TABLE users ADD COLUMN manager_id INTEGER REFERENCES users(id);
```

**Data Scope Service (server/services/dataScopeService.ts)**
```typescript
export class DataScopeService {
  async getAccessibleCustomers(userId: number): Promise<number[]> {
    const user = await this.getUser(userId);
    const userRoles = await this.getUserRoles(userId);
    const scopeRules = await this.getScopeRules(userRoles);

    let accessibleCustomers: number[] = [];

    for (const rule of scopeRules) {
      switch (rule.scope_type) {
        case 'branch':
          const branchCustomers = await this.getCustomersByBranch(user.branch_id);
          accessibleCustomers.push(...branchCustomers);
          break;
        case 'department':
          const deptCustomers = await this.getCustomersByDepartment(user.department_id);
          accessibleCustomers.push(...deptCustomers);
          break;
        case 'hierarchy':
          const hierarchyCustomers = await this.getCustomersByHierarchy(userId);
          accessibleCustomers.push(...hierarchyCustomers);
          break;
      }
    }

    return [...new Set(accessibleCustomers)]; // Remove duplicates
  }

  async filterQueryByScope(userId: number, baseQuery: any, entityType: string) {
    const accessibleIds = await this.getAccessibleEntities(userId, entityType);

    if (accessibleIds.length === 0) {
      return baseQuery.where(sql`false`); // No access
    }

    return baseQuery.where(inArray(sql`id`, accessibleIds));
  }
}
```

#### 3.2 Field-Level Security

**Field Security Schema**
```sql
CREATE TABLE field_security_rules (
  id SERIAL PRIMARY KEY,
  role_id INTEGER REFERENCES custom_roles(id) ON DELETE CASCADE,
  table_name VARCHAR(100) NOT NULL,
  field_name VARCHAR(100) NOT NULL,
  access_type VARCHAR(20) NOT NULL, -- 'read', 'write', 'none', 'masked'
  condition_config JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);
```

**Field Security Service (server/services/fieldSecurityService.ts)**
```typescript
export class FieldSecurityService {
  async filterFieldsByPermissions(userId: number, data: any, tableName: string): Promise<any> {
    const userRoles = await this.getUserRoles(userId);
    const fieldRules = await this.getFieldSecurityRules(userRoles, tableName);

    const filteredData = { ...data };

    for (const [fieldName, value] of Object.entries(data)) {
      const rule = fieldRules.find(r => r.field_name === fieldName);

      if (!rule || rule.access_type === 'none') {
        delete filteredData[fieldName];
      } else if (rule.access_type === 'masked') {
        filteredData[fieldName] = this.maskFieldValue(value, fieldName);
      }
    }

    return filteredData;
  }

  private maskFieldValue(value: any, fieldName: string): any {
    if (typeof value !== 'string') return value;

    switch (fieldName) {
      case 'ssn':
        return value.replace(/\d(?=\d{4})/g, '*');
      case 'email':
        const [local, domain] = value.split('@');
        return `${local.slice(0, 2)}***@${domain}`;
      case 'phone':
        return value.replace(/\d(?=\d{4})/g, '*');
      default:
        return value.slice(0, 2) + '*'.repeat(value.length - 2);
    }
  }
}
```

## Conclusion

This comprehensive user management and permissions system will provide TrackFina with enterprise-grade security, compliance capabilities, and user experience. The phased implementation approach ensures minimal disruption while delivering incremental value throughout the development process.

The system will support the complex requirements outlined in the user stories while maintaining high performance, security, and usability standards required for a financial services SAAS platform.

### Next Steps
1. **Review and Approve Plan** - Stakeholder review of implementation plan
2. **Environment Setup** - Prepare development and testing environments
3. **Phase 1 Implementation** - Begin with enhanced permission system
4. **Continuous Testing** - Implement comprehensive testing throughout development
5. **User Training** - Prepare training materials and documentation
6. **Gradual Rollout** - Phased deployment with user feedback incorporation
