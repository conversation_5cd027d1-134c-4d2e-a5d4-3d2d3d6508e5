import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ConditionalPermissionService, PermissionEvaluationContext } from '../conditionalPermissionService';

// Mock the database
vi.mock('../../db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
  }
}));

// Mock the schema
vi.mock('@shared/schema', () => ({
  permissions: { id: 'id', code: 'code' },
  permissionConditions: {
    permission_id: 'permission_id',
    is_active: 'is_active',
    priority: 'priority',
    condition_type: 'condition_type',
    configuration: 'configuration'
  }
}));

describe('ConditionalPermissionService', () => {
  let service: ConditionalPermissionService;
  let mockContext: PermissionEvaluationContext;

  beforeEach(() => {
    service = new ConditionalPermissionService();
    mockContext = {
      userId: 1,
      permissionCode: 'loan_create_basic',
      timestamp: new Date('2024-01-15T10:30:00Z'),
      amount: 5000,
      ipAddress: '*************',
      location: { country: 'US' },
      deviceInfo: { type: 'desktop', registered: true },
      sessionInfo: {
        sessionAge: 1800,
        mfaVerified: true,
        freshAuth: true,
        lastActivity: new Date('2024-01-15T10:25:00Z')
      }
    };
  });

  describe('evaluateTimeCondition', () => {
    it('should pass when current time is within allowed hours', () => {
      const config = {
        start_time: '09:00',
        end_time: '17:00',
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
      };

      const result = (service as any).evaluateTimeCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should fail when current time is outside allowed hours', () => {
      const config = {
        start_time: '09:00',
        end_time: '17:00'
      };

      const lateContext = {
        ...mockContext,
        timestamp: new Date('2024-01-15T18:30:00Z') // 6:30 PM
      };

      const result = (service as any).evaluateTimeCondition(config, lateContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Access only allowed between');
    });

    it('should fail when current day is not allowed', () => {
      const config = {
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
      };

      const weekendContext = {
        ...mockContext,
        timestamp: new Date('2024-01-13T10:30:00Z') // Saturday
      };

      const result = (service as any).evaluateTimeCondition(config, weekendContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Access only allowed on');
    });
  });

  describe('evaluateLocationCondition', () => {
    it('should pass when IP is in allowed range', () => {
      const config = {
        allowed_ip_ranges: ['***********/24', '10.0.0.0/8']
      };

      const result = (service as any).evaluateLocationCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should fail when IP is not in allowed range', () => {
      const config = {
        allowed_ip_ranges: ['10.0.0.0/8']
      };

      const result = (service as any).evaluateLocationCondition(config, mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Access not allowed from this IP address');
    });

    it('should pass when country is allowed', () => {
      const config = {
        allowed_countries: ['US', 'CA']
      };

      const result = (service as any).evaluateLocationCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should fail when country is blocked', () => {
      const config = {
        blocked_countries: ['US', 'CN']
      };

      const result = (service as any).evaluateLocationCondition(config, mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Access blocked from country');
    });
  });

  describe('evaluateAmountCondition', () => {
    it('should pass when amount is within limits', () => {
      const config = {
        min_amount: 1000,
        max_amount: 10000
      };

      const result = (service as any).evaluateAmountCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should fail when amount is below minimum', () => {
      const config = {
        min_amount: 10000
      };

      const result = (service as any).evaluateAmountCondition(config, mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Amount must be at least');
    });

    it('should fail when amount exceeds maximum', () => {
      const config = {
        max_amount: 1000
      };

      const result = (service as any).evaluateAmountCondition(config, mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Amount cannot exceed');
    });

    it('should pass when no amount is provided', () => {
      const config = {
        min_amount: 1000,
        max_amount: 10000
      };

      const noAmountContext = { ...mockContext };
      delete noAmountContext.amount;

      const result = (service as any).evaluateAmountCondition(config, noAmountContext);
      expect(result.passed).toBe(true);
    });
  });

  describe('evaluateApprovalCondition', () => {
    it('should pass when approval is not required', () => {
      const config = {
        requires_approval: false
      };

      const result = (service as any).evaluateApprovalCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should pass when amount is below approval threshold', () => {
      const config = {
        requires_approval: true,
        approval_threshold: 10000
      };

      const result = (service as any).evaluateApprovalCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should pass when amount is below auto-approve threshold', () => {
      const config = {
        requires_approval: true,
        auto_approve_below: 10000
      };

      const result = (service as any).evaluateApprovalCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should require approval when amount exceeds threshold', () => {
      const config = {
        requires_approval: true,
        approval_threshold: 1000,
        approver_roles: ['manager', 'director']
      };

      const result = (service as any).evaluateApprovalCondition(config, mockContext);
      expect(result.passed).toBe(false);
      expect(result.requiresApproval).toBe(true);
      expect(result.approverRoles).toEqual(['manager', 'director']);
    });

    it('should pass when already approved', () => {
      const config = {
        requires_approval: true,
        approval_threshold: 1000
      };

      const approvedContext = {
        ...mockContext,
        approvalInfo: { approvalStatus: 'approved' as const }
      };

      const result = (service as any).evaluateApprovalCondition(config, approvedContext);
      expect(result.passed).toBe(true);
    });
  });

  describe('evaluateDeviceCondition', () => {
    it('should pass when device type is allowed', () => {
      const config = {
        allowed_device_types: ['desktop', 'laptop']
      };

      const result = (service as any).evaluateDeviceCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should fail when device type is blocked', () => {
      const config = {
        blocked_device_types: ['desktop', 'mobile']
      };

      const result = (service as any).evaluateDeviceCondition(config, mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Access blocked from desktop devices');
    });

    it('should fail when device is not registered and registration is required', () => {
      const config = {
        require_registered_device: true
      };

      const unregisteredContext = {
        ...mockContext,
        deviceInfo: { type: 'desktop' as const, registered: false }
      };

      const result = (service as any).evaluateDeviceCondition(config, unregisteredContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Device must be registered');
    });
  });

  describe('evaluateSessionCondition', () => {
    it('should pass when session meets all requirements', () => {
      const config = {
        max_session_age: 3600,
        require_mfa: true,
        require_fresh_auth: true,
        max_idle_time: 1800
      };

      const result = (service as any).evaluateSessionCondition(config, mockContext);
      expect(result.passed).toBe(true);
    });

    it('should fail when session is too old', () => {
      const config = {
        max_session_age: 1000
      };

      const result = (service as any).evaluateSessionCondition(config, mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Session too old');
    });

    it('should fail when MFA is required but not verified', () => {
      const config = {
        require_mfa: true
      };

      const noMfaContext = {
        ...mockContext,
        sessionInfo: { ...mockContext.sessionInfo!, mfaVerified: false }
      };

      const result = (service as any).evaluateSessionCondition(config, noMfaContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Multi-factor authentication required');
    });

    it('should fail when fresh auth is required but not provided', () => {
      const config = {
        require_fresh_auth: true
      };

      const noFreshAuthContext = {
        ...mockContext,
        sessionInfo: { ...mockContext.sessionInfo!, freshAuth: false }
      };

      const result = (service as any).evaluateSessionCondition(config, noFreshAuthContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Fresh authentication required');
    });
  });

  describe('isIPInRanges', () => {
    it('should return true for exact IP match', () => {
      const result = (service as any).isIPInRanges('*************', ['*************', '********']);
      expect(result).toBe(true);
    });

    it('should return true for CIDR range match', () => {
      const result = (service as any).isIPInRanges('*************', ['***********/24']);
      expect(result).toBe(true);
    });

    it('should return false for no match', () => {
      const result = (service as any).isIPInRanges('************', ['***********/24', '10.0.0.0/8']);
      expect(result).toBe(false);
    });

    it('should handle invalid IP addresses gracefully', () => {
      const result = (service as any).isIPInRanges('invalid-ip', ['***********/24']);
      expect(result).toBe(false);
    });

    it('should handle invalid CIDR ranges gracefully', () => {
      const result = (service as any).isIPInRanges('*************', ['invalid-cidr']);
      expect(result).toBe(false);
    });
  });

  describe('evaluatePermissionConditions', () => {
    beforeEach(() => {
      const mockDb = vi.mocked(require('../../db').db);
      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.orderBy.mockReturnThis();
    });

    it('should return passed: true when permission exists and no conditions', async () => {
      const mockDb = vi.mocked(require('../../db').db);

      // Mock permission exists
      mockDb.select.mockResolvedValueOnce([{ id: 1, code: 'loan_create_basic' }]);
      // Mock no conditions
      mockDb.select.mockResolvedValueOnce([]);

      const result = await service.evaluatePermissionConditions(mockContext);
      expect(result.passed).toBe(true);
    });

    it('should return passed: false when permission does not exist', async () => {
      const mockDb = vi.mocked(require('../../db').db);

      // Mock permission does not exist
      mockDb.select.mockResolvedValueOnce([]);

      const result = await service.evaluatePermissionConditions(mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Permission not found');
    });

    it('should evaluate multiple conditions and pass when all pass', async () => {
      const mockDb = vi.mocked(require('../../db').db);

      // Mock permission exists
      mockDb.select.mockResolvedValueOnce([{ id: 1, code: 'loan_create_basic' }]);
      // Mock conditions
      mockDb.select.mockResolvedValueOnce([
        {
          id: 1,
          condition_type: 'time',
          configuration: { start_time: '09:00', end_time: '17:00' },
          priority: 1
        },
        {
          id: 2,
          condition_type: 'amount',
          configuration: { max_amount: 10000 },
          priority: 2
        }
      ]);

      const result = await service.evaluatePermissionConditions(mockContext);
      expect(result.passed).toBe(true);
    });

    it('should fail when any condition fails', async () => {
      const mockDb = vi.mocked(require('../../db').db);

      // Mock permission exists
      mockDb.select.mockResolvedValueOnce([{ id: 1, code: 'loan_create_basic' }]);
      // Mock conditions with one that will fail
      mockDb.select.mockResolvedValueOnce([
        {
          id: 1,
          condition_type: 'amount',
          configuration: { max_amount: 1000 }, // This will fail for amount 5000
          priority: 1
        }
      ]);

      const result = await service.evaluatePermissionConditions(mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Amount cannot exceed');
    });

    it('should handle approval requirements correctly', async () => {
      const mockDb = vi.mocked(require('../../db').db);

      // Mock permission exists
      mockDb.select.mockResolvedValueOnce([{ id: 1, code: 'loan_create_basic' }]);
      // Mock approval condition
      mockDb.select.mockResolvedValueOnce([
        {
          id: 1,
          condition_type: 'approval',
          configuration: {
            requires_approval: true,
            approval_threshold: 1000,
            approver_roles: ['manager', 'director']
          },
          priority: 1
        }
      ]);

      const result = await service.evaluatePermissionConditions(mockContext);
      expect(result.passed).toBe(false);
      expect(result.requiresApproval).toBe(true);
      expect(result.approverRoles).toEqual(['manager', 'director']);
    });

    it('should handle database errors gracefully', async () => {
      const mockDb = vi.mocked(require('../../db').db);

      // Mock database error
      mockDb.select.mockRejectedValueOnce(new Error('Database connection failed'));

      const result = await service.evaluatePermissionConditions(mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toBe('Evaluation error');
    });
  });

  describe('evaluateCondition', () => {
    it('should handle unknown condition types', async () => {
      const condition = {
        id: 1,
        condition_type: 'unknown_type',
        configuration: {},
        priority: 1
      };

      const result = await (service as any).evaluateCondition(condition, mockContext);
      expect(result.passed).toBe(false);
      expect(result.reason).toContain('Unknown condition type');
    });

    it('should handle malformed configuration gracefully', async () => {
      const condition = {
        id: 1,
        condition_type: 'time',
        configuration: null, // Invalid configuration
        priority: 1
      };

      const result = await (service as any).evaluateCondition(condition, mockContext);
      expect(result.passed).toBe(false);
    });
  });
});
