/**
 * Environment Variable Loader Utility
 * 
 * Provides a standardized way to load environment variables from .env file
 * Used by migration scripts and utilities to ensure consistent environment setup
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

/**
 * Load environment variables from .env file
 * Logs the loading process for debugging purposes
 */
export function loadEnvironment() {
  const envPath = path.resolve('.env');
  console.log('Checking for .env file at:', envPath);
  console.log('File exists:', fs.existsSync(envPath));

  if (fs.existsSync(envPath)) {
    const envConfig = dotenv.parse(fs.readFileSync(envPath));
    for (const k in envConfig) {
      process.env[k] = envConfig[k];
    }
    console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
  } else {
    console.warn('No .env file found. Make sure environment variables are set.');
  }
}

/**
 * Validate that required environment variables are present
 * @param {string[]} requiredVars - Array of required environment variable names
 * @throws {Error} If any required variables are missing
 */
export function validateEnvironment(requiredVars = ['DATABASE_URL']) {
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Load and validate environment in one call
 * @param {string[]} requiredVars - Array of required environment variable names
 */
export function setupEnvironment(requiredVars = ['DATABASE_URL']) {
  loadEnvironment();
  validateEnvironment(requiredVars);
}
