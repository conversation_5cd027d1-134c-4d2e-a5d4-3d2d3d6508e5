# Role Hierarchy Database Schema - Task 2.1.1

## Overview
Successfully completed Task 2.1.1: "Create role hierarchy database schema" from Phase 2 of the User Management & Permissions System. This task established the foundational database structure for advanced role management with inheritance and templates.

## What Was Accomplished

### 1. Database Schema Design

#### New Enum Type
- **`inheritanceTypeEnum`**: Defines how child roles inherit from parent roles
  - `'inherit'` - Child role adds parent permissions to its own
  - `'override'` - Child role replaces parent permissions with its own
  - `'deny'` - Child role explicitly removes parent permissions

#### New Tables Created

##### Role Hierarchy Table (`role_hierarchy`)
```sql
CREATE TABLE role_hierarchy (
  id SERIAL PRIMARY KEY,
  parent_role_id INTEGER NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
  child_role_id INTEGER NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
  inheritance_type inheritance_type DEFAULT 'inherit' NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT unique_parent_child UNIQUE (parent_role_id, child_role_id),
  CONSTRAINT check_not_self_referencing CHECK (parent_role_id != child_role_id)
);
```

**Features:**
- Parent-child role relationships
- Configurable inheritance behavior
- Prevents duplicate relationships
- Prevents self-referencing roles
- Automatic timestamps with update triggers

##### Role Templates Table (`role_templates`)
```sql
CREATE TABLE role_templates (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  template_config JSONB NOT NULL,
  industry TEXT,
  is_system BOOLEAN DEFAULT FALSE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  -- Constraints
  CONSTRAINT unique_template_name UNIQUE (name)
);
```

**Features:**
- Predefined role configurations
- Industry-specific templates
- JSON configuration for permissions and conditions
- System vs. custom template distinction
- Unique template names

### 2. Database Constraints and Validation

#### Integrity Constraints
- **Unique parent-child relationships**: Prevents duplicate hierarchy entries
- **Self-reference prevention**: Roles cannot be parents of themselves
- **Unique template names**: Ensures template name uniqueness
- **Foreign key constraints**: Maintains referential integrity

#### Performance Indexes
```sql
CREATE INDEX idx_role_hierarchy_parent ON role_hierarchy(parent_role_id);
CREATE INDEX idx_role_hierarchy_child ON role_hierarchy(child_role_id);
CREATE INDEX idx_role_hierarchy_inheritance_type ON role_hierarchy(inheritance_type);
CREATE INDEX idx_role_templates_industry ON role_templates(industry);
CREATE INDEX idx_role_templates_is_system ON role_templates(is_system);
```

### 3. Drizzle ORM Integration

#### Schema Definitions
- Added `roleHierarchy` table definition with proper constraints
- Added `roleTemplates` table definition with JSON configuration
- Integrated with existing schema structure

#### Relations
```typescript
// Role hierarchy relations
export const roleHierarchyRelations = relations(roleHierarchy, ({ one }) => ({
  parentRole: one(customRoles, {
    fields: [roleHierarchy.parent_role_id],
    references: [customRoles.id],
    relationName: 'parentRole',
  }),
  childRole: one(customRoles, {
    fields: [roleHierarchy.child_role_id],
    references: [customRoles.id],
    relationName: 'childRole',
  }),
}));

// Updated custom roles to include hierarchy relations
parentRoles: many(roleHierarchy, { relationName: 'childRole' }),
childRoles: many(roleHierarchy, { relationName: 'parentRole' }),
```

### 4. TypeScript Types and Schemas

#### Zod Validation Schemas
```typescript
export const insertRoleHierarchySchema = createInsertSchema(roleHierarchy)
  .omit({ id: true, created_at: true, updated_at: true });

export const insertRoleTemplateSchema = createInsertSchema(roleTemplates)
  .omit({ id: true, created_at: true, updated_at: true });
```

#### TypeScript Types
```typescript
export type RoleHierarchy = typeof roleHierarchy.$inferSelect;
export type InsertRoleHierarchy = z.infer<typeof insertRoleHierarchySchema>;

export type RoleTemplate = typeof roleTemplates.$inferSelect;
export type InsertRoleTemplate = z.infer<typeof insertRoleTemplateSchema>;
```

### 5. Migration Script

#### Migration File: `migrations/011_role_hierarchy.sql`
- Creates enum types and tables
- Adds proper indexes for performance
- Includes comprehensive documentation
- Sets up update triggers for timestamps

#### Predefined Role Templates
Created 6 industry-standard role templates:

1. **Loan Officer**
   - Basic loan creation and customer management
   - Approval limit: $50,000
   - Business hours restrictions

2. **Senior Loan Officer**
   - Higher approval limits: $200,000
   - Team oversight capabilities
   - Extended permissions

3. **Collection Agent**
   - Specialized collection activities
   - Customer communication focus
   - Time-restricted operations

4. **Branch Manager**
   - Complete branch operations
   - High approval limits: $500,000
   - Full staff management

5. **Auditor**
   - Read-only access for compliance
   - Comprehensive audit capabilities
   - Report generation access

6. **Customer Service Representative**
   - Customer-facing operations
   - Limited system access
   - Session-based restrictions

### 6. Template Configuration Structure

Each template includes:
```json
{
  "permissions": ["permission_code1", "permission_code2"],
  "conditions": {
    "permission_code": {
      "type": "amount|time|location|session",
      "config": { /* condition-specific configuration */ }
    }
  },
  "description": "Human-readable description"
}
```

## Technical Benefits

### 1. Scalable Role Management
- Hierarchical role structure reduces duplication
- Template system enables rapid role deployment
- Industry-specific configurations

### 2. Flexible Inheritance
- Multiple inheritance types support various business models
- Override capabilities for specialized roles
- Deny permissions for security restrictions

### 3. Data Integrity
- Comprehensive constraints prevent invalid configurations
- Circular dependency prevention at database level
- Referential integrity maintenance

### 4. Performance Optimization
- Strategic indexing for common query patterns
- Efficient parent-child lookups
- Template filtering by industry/type

### 5. Developer Experience
- Full TypeScript support with type safety
- Zod validation for runtime safety
- Drizzle ORM integration for type-safe queries

## Files Created/Modified

### New Files:
- `migrations/011_role_hierarchy.sql` - Database migration script

### Modified Files:
- `shared/schema.ts` - Added new tables, relations, types, and schemas
- `docs/task-list.md` - Updated task completion status

## Database Schema Changes Summary

### Tables Added:
1. `role_hierarchy` - Parent-child role relationships
2. `role_templates` - Predefined role configurations

### Enums Added:
1. `inheritance_type` - Role inheritance behavior types

### Indexes Added:
5 strategic indexes for performance optimization

### Constraints Added:
- Unique constraints for data integrity
- Check constraints for business rules
- Foreign key constraints for referential integrity

## Next Steps

With the database schema complete, the next task in sequence is:
- **Task 2.1.2**: Create RoleHierarchyService
  - Implement role inheritance logic
  - Add circular dependency prevention
  - Add effective permission calculation

This schema provides the foundation for implementing the business logic layer that will handle role inheritance, permission calculation, and template management.

## Conclusion

Task 2.1.1 has been successfully completed with a comprehensive database schema that supports:
- ✅ Hierarchical role relationships
- ✅ Flexible inheritance types
- ✅ Role templates for rapid deployment
- ✅ Data integrity and performance optimization
- ✅ Full TypeScript and ORM integration

The schema is designed to be scalable, maintainable, and supports the advanced role management features planned for Phase 2 of the project.
