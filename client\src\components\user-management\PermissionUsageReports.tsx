import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { 
  Shield, AlertTriangle, TrendingUp, TrendingDown, Download, 
  Search, Filter, Eye, Users, Activity, Clock
} from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { apiRequest } from '@/lib/api';
import { exportToCsv, generateFilename } from '@/lib/exportUtils';

interface PermissionUsageReport {
  permission_code: string;
  permission_name: string;
  category: string;
  total_users: number;
  usage_count_30d: number;
  last_used: Date;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  is_sensitive: boolean;
}

export default function PermissionUsageReports() {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [riskFilter, setRiskFilter] = useState<string>('');

  const { data: permissionReports, isLoading, refetch } = useQuery({
    queryKey: ['/api/advanced-search/permission-usage-report'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/advanced-search/permission-usage-report');
      return response.json() as Promise<PermissionUsageReport[]>;
    },
  });

  const getRiskBadgeVariant = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <AlertTriangle className="h-3 w-3" />;
      case 'medium':
        return <Eye className="h-3 w-3" />;
      default:
        return <Shield className="h-3 w-3" />;
    }
  };

  const columns: ColumnDef<PermissionUsageReport>[] = [
    {
      accessorKey: 'permission_name',
      header: 'Permission',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.permission_name}</div>
          <div className="text-xs text-muted-foreground">{row.original.permission_code}</div>
        </div>
      ),
    },
    {
      accessorKey: 'category',
      header: 'Category',
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.original.category}
        </Badge>
      ),
    },
    {
      accessorKey: 'total_users',
      header: 'Total Users',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.original.total_users}</span>
        </div>
      ),
    },
    {
      accessorKey: 'usage_count_30d',
      header: '30-Day Usage',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Activity className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.original.usage_count_30d}</span>
          {row.original.usage_count_30d === 0 && (
            <Badge variant="outline" className="text-xs">
              Unused
            </Badge>
          )}
        </div>
      ),
    },
    {
      accessorKey: 'last_used',
      header: 'Last Used',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {row.original.last_used 
              ? new Date(row.original.last_used).toLocaleDateString()
              : 'Never'
            }
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'risk_level',
      header: 'Risk Level',
      cell: ({ row }) => (
        <Badge 
          variant={getRiskBadgeVariant(row.original.risk_level)}
          className="flex items-center gap-1 w-fit"
        >
          {getRiskIcon(row.original.risk_level)}
          <span className="capitalize">{row.original.risk_level}</span>
        </Badge>
      ),
    },
    {
      accessorKey: 'is_sensitive',
      header: 'Sensitive',
      cell: ({ row }) => (
        row.original.is_sensitive ? (
          <Badge variant="destructive" className="text-xs">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Sensitive
          </Badge>
        ) : (
          <Badge variant="secondary" className="text-xs">
            Standard
          </Badge>
        )
      ),
    },
  ];

  const filteredData = React.useMemo(() => {
    if (!permissionReports) return [];
    
    return permissionReports.filter(report => {
      const matchesSearch = !searchQuery || 
        report.permission_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.permission_code.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesCategory = !categoryFilter || report.category === categoryFilter;
      const matchesRisk = !riskFilter || report.risk_level === riskFilter;
      
      return matchesSearch && matchesCategory && matchesRisk;
    });
  }, [permissionReports, searchQuery, categoryFilter, riskFilter]);

  const handleExport = (data: PermissionUsageReport[]) => {
    const exportColumns = [
      { key: 'permission_code' as const, header: 'Permission Code' },
      { key: 'permission_name' as const, header: 'Permission Name' },
      { key: 'category' as const, header: 'Category' },
      { key: 'total_users' as const, header: 'Total Users' },
      { key: 'usage_count_30d' as const, header: '30-Day Usage Count' },
      { key: 'last_used' as const, header: 'Last Used', formatter: (date: Date) => date ? new Date(date).toLocaleDateString() : 'Never' },
      { key: 'risk_level' as const, header: 'Risk Level' },
      { key: 'is_sensitive' as const, header: 'Is Sensitive', formatter: (value: boolean) => value ? 'Yes' : 'No' },
    ];

    const filename = generateFilename('permission_usage_report');
    exportToCsv(data, exportColumns, filename);
  };

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (!permissionReports) return null;

    const totalPermissions = permissionReports.length;
    const unusedPermissions = permissionReports.filter(p => p.usage_count_30d === 0).length;
    const sensitivePermissions = permissionReports.filter(p => p.is_sensitive).length;
    const highRiskPermissions = permissionReports.filter(p => p.risk_level === 'high' || p.risk_level === 'critical').length;
    const averageUsage = Math.round(permissionReports.reduce((sum, p) => sum + p.usage_count_30d, 0) / totalPermissions);

    return {
      totalPermissions,
      unusedPermissions,
      sensitivePermissions,
      highRiskPermissions,
      averageUsage,
      unusedPercentage: Math.round((unusedPermissions / totalPermissions) * 100),
      sensitivePercentage: Math.round((sensitivePermissions / totalPermissions) * 100),
      highRiskPercentage: Math.round((highRiskPermissions / totalPermissions) * 100),
    };
  }, [permissionReports]);

  const categories = React.useMemo(() => {
    if (!permissionReports) return [];
    return [...new Set(permissionReports.map(p => p.category))];
  }, [permissionReports]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse h-64 bg-gray-200 rounded"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      {summaryStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Permissions</p>
                  <p className="text-2xl font-bold">{summaryStats.totalPermissions}</p>
                </div>
                <Shield className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Unused (30d)</p>
                  <p className="text-2xl font-bold text-orange-600">{summaryStats.unusedPermissions}</p>
                  <p className="text-xs text-muted-foreground">
                    {summaryStats.unusedPercentage}% of total
                  </p>
                </div>
                <TrendingDown className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Sensitive</p>
                  <p className="text-2xl font-bold text-red-600">{summaryStats.sensitivePermissions}</p>
                  <p className="text-xs text-muted-foreground">
                    {summaryStats.sensitivePercentage}% of total
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Usage (30d)</p>
                  <p className="text-2xl font-bold text-green-600">{summaryStats.averageUsage}</p>
                  <p className="text-xs text-muted-foreground">
                    per permission
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permission Usage Report
          </CardTitle>
          <CardDescription>
            Detailed analysis of permission usage across your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search permissions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={riskFilter} onValueChange={setRiskFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Risk Levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Risk Levels</SelectItem>
                <SelectItem value="low">Low Risk</SelectItem>
                <SelectItem value="medium">Medium Risk</SelectItem>
                <SelectItem value="high">High Risk</SelectItem>
                <SelectItem value="critical">Critical Risk</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={() => refetch()}>
              <Activity className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          <AdvancedDataTable
            columns={columns}
            data={filteredData}
            enableGlobalFilter={false}
            enableColumnFilters={true}
            enableColumnVisibility={true}
            enableExport={true}
            onExport={handleExport}
            filterOptions={{
              category: categories.map(cat => ({ value: cat, label: cat })),
              risk_level: [
                { value: 'low', label: 'Low Risk' },
                { value: 'medium', label: 'Medium Risk' },
                { value: 'high', label: 'High Risk' },
                { value: 'critical', label: 'Critical Risk' },
              ],
            }}
          />
        </CardContent>
      </Card>

      {/* Insights */}
      {summaryStats && (
        <Card>
          <CardHeader>
            <CardTitle>Key Insights</CardTitle>
            <CardDescription>
              Important observations from permission usage data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {summaryStats.unusedPercentage > 20 && (
                <div className="flex items-start gap-3 p-3 border rounded-lg border-orange-200 bg-orange-50">
                  <TrendingDown className="h-5 w-5 text-orange-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-orange-800">High Unused Permissions</p>
                    <p className="text-xs text-orange-600">
                      {summaryStats.unusedPercentage}% of permissions haven't been used in 30 days. Consider reviewing permission assignments.
                    </p>
                  </div>
                </div>
              )}

              {summaryStats.sensitivePercentage > 15 && (
                <div className="flex items-start gap-3 p-3 border rounded-lg border-red-200 bg-red-50">
                  <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-red-800">High Sensitive Permissions</p>
                    <p className="text-xs text-red-600">
                      {summaryStats.sensitivePercentage}% of permissions are marked as sensitive. Ensure proper access controls.
                    </p>
                  </div>
                </div>
              )}

              <div className="flex items-start gap-3 p-3 border rounded-lg border-blue-200 bg-blue-50">
                <Activity className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-blue-800">Usage Pattern</p>
                  <p className="text-xs text-blue-600">
                    Average of {summaryStats.averageUsage} uses per permission in the last 30 days.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
