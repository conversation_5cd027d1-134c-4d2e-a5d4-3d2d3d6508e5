import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes"; // This now points to our new modular routes
import { setupVite, serveStatic, log } from "./vite";
import errorLogger from './utils/errorLogger';
import cookieParser from "cookie-parser";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  // Global error handler to catch and handle all exceptions
  app.use((err: any, req: Request, res: Response, _next: NextFunction) => {
    // Log error with detailed information
    errorLogger.logError(
      `HTTP ${req.method} ${req.path} failed`, 
      'express-global-error-handler',
      {
        error: err,
        body: req.body,
        query: req.query,
        params: req.params
      }
    );
    
    // Format standardized error response
    const errorResponse = errorLogger.formatErrorResponse(err);
    
    // Add request path for debugging
    errorResponse.path = req.path;
    
    // Return structured error response to client
    res.status(errorResponse.status).json(errorResponse);
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();
