import { describe, it, expect, beforeEach, vi, beforeAll, afterAll } from 'vitest';
import { AccessMonitoringService } from '../accessMonitoringService';
import { db } from '../../db';
import { securityEvents, securityRules, userBehaviorBaselines } from '@shared/schema';

// Mock the database
vi.mock('../../db', () => ({
  db: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn()
  }
}));

// Mock the enhanced session service
vi.mock('../enhancedSessionService', () => ({
  enhancedSessionService: {
    terminateSession: vi.fn()
  }
}));

describe('AccessMonitoringService', () => {
  let service: AccessMonitoringService;
  let mockDb: any;

  beforeEach(() => {
    service = new AccessMonitoringService();
    mockDb = db as any;
    vi.clearAllMocks();
  });

  describe('recordSecurityEvent', () => {
    it('should record a security event with anomaly detection', async () => {
      // Mock user behavior baseline lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([{
          id: 1,
          user_id: 1,
          typical_locations: ['US'],
          typical_login_hours: { '9': 0.8, '10': 0.9 },
          known_devices: ['device123']
        }])
      });

      // Mock security rules lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([])
      });

      // Mock event creation
      const mockEvent = {
        id: 1,
        event_id: 'evt_test',
        event_type: 'login_success',
        severity: 'low',
        risk_score: 25,
        event_description: 'User login from unusual location',
        user_id: 1,
        company_id: 1,
        ip_address: '***********',
        location_country: 'CA', // Different from baseline
        is_geo_anomaly: true,
        timestamp: new Date()
      };

      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([mockEvent])
      });

      const result = await service.recordSecurityEvent({
        eventType: 'login_success',
        eventDescription: 'User login from unusual location',
        context: {
          userId: 1,
          companyId: 1,
          ipAddress: '***********',
          location: { country: 'CA' },
          deviceFingerprint: 'device456' // Different from baseline
        }
      });

      expect(result).toBeDefined();
      expect(result.event_type).toBe('login_success');
      expect(result.is_geo_anomaly).toBe(true);
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it('should calculate risk score based on event type and anomalies', async () => {
      // Mock no baseline (new user)
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      });

      // Mock security rules lookup
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([])
      });

      // Mock event creation
      const mockEvent = {
        id: 1,
        event_id: 'evt_test',
        event_type: 'brute_force_attempt',
        severity: 'high',
        risk_score: 80,
        event_description: 'Multiple failed login attempts',
        user_id: 1
      };

      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([mockEvent])
      });

      const result = await service.recordSecurityEvent({
        eventType: 'brute_force_attempt',
        eventDescription: 'Multiple failed login attempts',
        context: {
          userId: 1,
          ipAddress: '***********'
        }
      });

      expect(result.risk_score).toBeGreaterThan(70); // High risk for brute force
      expect(result.severity).toBe('high');
    });
  });

  describe('detectAnomalies', () => {
    it('should detect geographic anomalies', async () => {
      // Mock user behavior baseline
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([{
          id: 1,
          user_id: 1,
          typical_locations: ['US', 'CA'],
          typical_login_hours: { '9': 0.8 },
          known_devices: ['device123']
        }])
      });

      const result = await service.detectAnomalies({
        userId: 1,
        location: { country: 'RU' }, // Unusual location
        deviceFingerprint: 'device123'
      });

      expect(result.isAnomaly).toBe(true);
      expect(result.anomalyType).toBe('geo');
      expect(result.indicators).toContain('Unusual location: RU');
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    it('should detect device anomalies', async () => {
      // Mock user behavior baseline
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([{
          id: 1,
          user_id: 1,
          typical_locations: ['US'],
          typical_login_hours: { '9': 0.8 },
          known_devices: ['device123', 'device456']
        }])
      });

      const result = await service.detectAnomalies({
        userId: 1,
        location: { country: 'US' },
        deviceFingerprint: 'unknown_device' // Unknown device
      });

      expect(result.isAnomaly).toBe(true);
      expect(result.anomalyType).toBe('device');
      expect(result.indicators).toContain('Unknown device');
    });

    it('should not detect anomalies for normal behavior', async () => {
      // Mock user behavior baseline
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([{
          id: 1,
          user_id: 1,
          typical_locations: ['US'],
          typical_login_hours: { '9': 0.8 },
          known_devices: ['device123']
        }])
      });

      const result = await service.detectAnomalies({
        userId: 1,
        location: { country: 'US' }, // Normal location
        deviceFingerprint: 'device123' // Known device
      });

      expect(result.isAnomaly).toBe(false);
      expect(result.indicators).toHaveLength(0);
    });
  });

  describe('evaluateSecurityRules', () => {
    it('should trigger security rules and create alerts', async () => {
      const mockEvent = {
        id: 1,
        event_id: 'evt_test',
        event_type: 'login_failure',
        severity: 'medium',
        risk_score: 50,
        user_id: 1,
        company_id: 1,
        ip_address: '***********'
      };

      // Mock security rules lookup
      const mockRule = {
        id: 1,
        rule_name: 'Brute Force Detection',
        event_types: ['login_failure'],
        conditions: { ip_address: { exists: true } },
        threshold_config: { max_attempts: 3, time_window: 300 },
        severity: 'high',
        auto_response_actions: ['block_ip'],
        is_active: true,
        priority: 1
      };

      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([mockRule])
      });

      // Mock recent events lookup (for threshold checking)
      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([
          { id: 1, event_type: 'login_failure' },
          { id: 2, event_type: 'login_failure' },
          { id: 3, event_type: 'login_failure' }
        ])
      });

      // Mock rule trigger count update
      mockDb.update.mockReturnValueOnce({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(undefined)
      });

      // Mock alert creation
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{
          id: 1,
          alert_id: 'alert_test',
          title: 'Security Alert: Brute Force Detection'
        }])
      });

      // Mock response creation
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{
          id: 1,
          response_id: 'resp_test',
          response_action: 'block_ip'
        }])
      });

      // Mock response execution update
      mockDb.update.mockReturnValueOnce({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(undefined)
      });

      await service.evaluateSecurityRules(mockEvent as any);

      expect(mockDb.update).toHaveBeenCalled(); // Rule trigger count update
      expect(mockDb.insert).toHaveBeenCalledTimes(2); // Alert + Response creation
    });

    it('should not trigger rules when conditions are not met', async () => {
      const mockEvent = {
        id: 1,
        event_type: 'data_access', // Different event type
        severity: 'low',
        user_id: 1
      };

      // Mock security rules lookup
      const mockRule = {
        id: 1,
        rule_name: 'Login Failure Detection',
        event_types: ['login_failure'], // Rule only applies to login failures
        conditions: {},
        threshold_config: {},
        severity: 'medium',
        auto_response_actions: [],
        is_active: true
      };

      mockDb.select.mockReturnValueOnce({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockResolvedValue([mockRule])
      });

      await service.evaluateSecurityRules(mockEvent as any);

      // Should not create alerts or responses since rule doesn't apply
      expect(mockDb.insert).not.toHaveBeenCalled();
    });
  });

  describe('calculateRiskScore', () => {
    it('should calculate higher risk scores for critical events', () => {
      const riskScore = service['calculateRiskScore'](
        'brute_force_attempt',
        { userId: 1 },
        { isAnomaly: false, anomalyType: 'behavior', confidence: 0, indicators: [], riskScore: 0 }
      );

      expect(riskScore).toBeGreaterThan(70);
    });

    it('should add anomaly risk to base score', () => {
      const riskScore = service['calculateRiskScore'](
        'login_success',
        { userId: 1 },
        { isAnomaly: true, anomalyType: 'geo', confidence: 0.8, indicators: [], riskScore: 30 }
      );

      expect(riskScore).toBeGreaterThan(30); // Should be higher due to anomaly
    });

    it('should cap risk score at 100', () => {
      const riskScore = service['calculateRiskScore'](
        'privilege_escalation',
        { userId: 1 },
        { isAnomaly: true, anomalyType: 'behavior', confidence: 1.0, indicators: [], riskScore: 50 }
      );

      expect(riskScore).toBeLessThanOrEqual(100);
    });
  });

  describe('determineSeverity', () => {
    it('should determine correct severity levels', () => {
      expect(service['determineSeverity'](95)).toBe('critical');
      expect(service['determineSeverity'](75)).toBe('high');
      expect(service['determineSeverity'](50)).toBe('medium');
      expect(service['determineSeverity'](20)).toBe('low');
    });
  });
});
