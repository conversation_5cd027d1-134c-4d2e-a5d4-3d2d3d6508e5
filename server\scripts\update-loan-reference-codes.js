// Script to update all existing loans to have an empty value in the loan_reference_code column
// This script uses the application's storage layer rather than direct SQL queries

import { db } from '../db.js';
import { loans } from '../../shared/schema.js';
import { LoanStorage } from '../storage/loan.storage.js';
import errorLogger from '../utils/errorLogger.js';

async function updateLoanReferenceCodes() {
  console.log('Starting update of loan_reference_code values...');

  try {
    // Initialize the loan storage
    const loanStorage = new LoanStorage();

    // Get all loans from the database
    const allLoans = await db.select().from(loans);
    console.log(`Found ${allLoans.length} loans in the database`);

    if (allLoans.length === 0) {
      console.log('No loans to update. Exiting.');
      return;
    }

    // Update each loan to have an empty value in the loan_reference_code column
    let updatedCount = 0;
    let errorCount = 0;

    for (const loan of allLoans) {
      try {
        // Only update loans that don't already have a reference code or have an empty reference code
        if (!loan.loan_reference_code || loan.loan_reference_code.trim() === '') {
          // Use the storage layer to update the loan
          await loanStorage.updateLoan(
            loan.id,
            loan.company_id,
            { loan_reference_code: '' }
          );

          updatedCount++;
          if (updatedCount % 10 === 0) {
            console.log(`Updated ${updatedCount} loans so far...`);
          }
        }
      } catch (error) {
        errorCount++;
        errorLogger.logError(
          `Error updating loan ID ${loan.id} with reference code`,
          'loan-reference-update',
          error
        );
      }
    }

    console.log(`Updated ${updatedCount} loans with empty loan_reference_code`);
    if (errorCount > 0) {
      console.log(`Encountered errors while updating ${errorCount} loans. Check the error logs for details.`);
    }
    console.log('Update completed successfully!');
  } catch (error) {
    errorLogger.logError(
      'Error in loan reference code update script',
      'loan-reference-update-script',
      error
    );
    console.error('Error updating loan reference codes:', error);
  } finally {
    process.exit(0);
  }
}

// Run the update
updateLoanReferenceCodes();
