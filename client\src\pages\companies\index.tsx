import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Plus, Pencil, Trash2, Eye, Search, Building, Building2, Users, DollarSign, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/lib/auth";
import { Link } from "wouter";

// Company type interface
interface Company {
  id: number;
  name: string;
  email?: string;
  contact_email?: string;
  phone?: string;
  contact_phone?: string;
  address?: string;
  website?: string;
  logo?: string;
  active: boolean;
  subscription_status?: string;
  created_at: string;
  updated_at: string;
}

export default function Companies() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);

  // Get auth functions
  const { isAuthenticated } = useAuth();
  
  // Fetch companies from API
  const {
    data: companies = [],
    isLoading,
    isError,
    error
  } = useQuery<Company[]>({
    queryKey: ['/api/companies'],
    enabled: isAuthenticated(),
    onSuccess: (data) => {
      console.log('Companies loaded:', data);
    },
    onError: (err) => {
      console.error('Error loading companies:', err);
    }
  });
  
  // Form state for create/edit
  const [companyForm, setCompanyForm] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    subscription_status: "active",
  });
  
  // Handle search with safeguards for potentially undefined fields
  const filteredCompanies = companies.filter(company => {
    const query = searchQuery.toLowerCase();
    return (
      company.name.toLowerCase().includes(query) ||
      (company.email?.toLowerCase().includes(query) || false) ||
      (company.contact_email?.toLowerCase().includes(query) || false)
    );
  });
  
  // Open create dialog with empty form
  const handleOpenCreateDialog = () => {
    setCompanyForm({
      name: "",
      email: "",
      phone: "",
      address: "",
      subscription_status: "active",
    });
    setIsCreateDialogOpen(true);
  };
  
  // Open edit dialog with company data
  const handleOpenEditDialog = (company: any) => {
    setCurrentCompany(company);
    setCompanyForm({
      name: company.name,
      email: company.email,
      phone: company.phone,
      address: company.address,
      subscription_status: company.subscription_status,
    });
    setIsEditDialogOpen(true);
  };
  
  // Open delete confirmation dialog
  const handleOpenDeleteDialog = (company: any) => {
    setCurrentCompany(company);
    setIsDeleteDialogOpen(true);
  };
  
  // Handle form input changes
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCompanyForm(prev => ({ ...prev, [name]: value }));
  };
  
  // Create company mutation
  const createCompanyMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/companies", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      setIsCreateDialogOpen(false);
      toast({
        title: "Company created",
        description: `${companyForm.name} has been created successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error creating company",
        description: error.message || "Failed to create company",
        variant: "destructive",
      });
    }
  });

  // Create new company
  const handleCreateCompany = () => {
    // Validation
    if (!companyForm.name || !companyForm.email) {
      toast({
        title: "Required fields missing",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    
    // Prepare the data for API
    const companyData = {
      name: companyForm.name,
      email: companyForm.email,
      phone: companyForm.phone,
      address: companyForm.address,
      active: true
    };
    
    // Call mutation to create company
    createCompanyMutation.mutate(companyData);
  };
  
  // Update company mutation
  const updateCompanyMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: any }) => {
      const response = await apiRequest("PATCH", `/api/companies/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      setIsEditDialogOpen(false);
      toast({
        title: "Company updated",
        description: `${companyForm.name} has been updated successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error updating company",
        description: error.message || "Failed to update company",
        variant: "destructive",
      });
    }
  });
  
  // Update existing company
  const handleUpdateCompany = () => {
    if (!currentCompany) return;
    
    // Validation
    if (!companyForm.name || !companyForm.email) {
      toast({
        title: "Required fields missing",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    
    // Prepare the data for API
    const companyData = {
      name: companyForm.name,
      email: companyForm.email,
      phone: companyForm.phone,
      address: companyForm.address,
      active: true
    };
    
    // Call mutation to update company
    updateCompanyMutation.mutate({ 
      id: currentCompany.id, 
      data: companyData 
    });
  };
  
  // Delete company mutation
  const deleteCompanyMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/companies/${id}`, null);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      setIsDeleteDialogOpen(false);
      toast({
        title: "Company deleted",
        description: `${currentCompany?.name} has been deleted successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting company",
        description: error.message || "Failed to delete company",
        variant: "destructive",
      });
    }
  });
  
  // Delete company
  const handleDeleteCompany = () => {
    if (!currentCompany) return;
    
    // Call mutation to delete company
    deleteCompanyMutation.mutate(currentCompany.id);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Companies</h1>
        
        <Button onClick={handleOpenCreateDialog} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Company
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Company Management</CardTitle>
          <CardDescription>
            View and manage all the companies in your SaaS platform.
          </CardDescription>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder="Search companies by name or email..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Company Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              
              <TableBody>
                {filteredCompanies.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center h-24 text-gray-500">
                      No companies found matching your search.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCompanies.map((company) => (
                    <TableRow key={company.id}>
                      <TableCell className="font-medium">{company.id}</TableCell>
                      <TableCell>{company.name}</TableCell>
                      <TableCell>{company.email}</TableCell>
                      <TableCell>{company.phone}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          company.subscription_status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {company.subscription_status}
                        </span>
                      </TableCell>
                      <TableCell>{formatDate(company.created_at)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Link to={`/companies/${company.id}`}>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="h-8 w-8 text-blue-600"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            className="h-8 w-8 text-amber-600"
                            onClick={() => handleOpenEditDialog(company)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            className="h-8 w-8 text-red-600"
                            onClick={() => handleOpenDeleteDialog(company)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between border-t p-4">
          <div className="text-sm text-gray-500">
            Showing {filteredCompanies.length} of {companies.length} companies
          </div>
        </CardFooter>
      </Card>

      {/* Company Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Companies</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Building2 className="h-6 w-6 mr-2 text-blue-600" />
              <span className="text-2xl font-bold">{companies.length}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Active Subscriptions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <DollarSign className="h-6 w-6 mr-2 text-green-600" />
              <span className="text-2xl font-bold">
                {companies.filter(c => c.subscription_status === 'active').length}
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-6 w-6 mr-2 text-purple-600" />
              <span className="text-2xl font-bold">0</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Create Company Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Company</DialogTitle>
            <DialogDescription>
              Add a new company to your SaaS platform.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Company Name <span className="text-red-500">*</span></Label>
              <Input
                id="name"
                name="name"
                value={companyForm.name}
                onChange={handleFormChange}
                placeholder="Enter company name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email Address <span className="text-red-500">*</span></Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={companyForm.email}
                onChange={handleFormChange}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                value={companyForm.phone}
                onChange={handleFormChange}
                placeholder="******-123-4567"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                value={companyForm.address}
                onChange={handleFormChange}
                placeholder="123 Business Street, City, State"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleCreateCompany}>
              Create Company
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Company Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Company</DialogTitle>
            <DialogDescription>
              Update the company information.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Company Name <span className="text-red-500">*</span></Label>
              <Input
                id="edit-name"
                name="name"
                value={companyForm.name}
                onChange={handleFormChange}
                placeholder="Enter company name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-email">Email Address <span className="text-red-500">*</span></Label>
              <Input
                id="edit-email"
                name="email"
                type="email"
                value={companyForm.email}
                onChange={handleFormChange}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-phone">Phone Number</Label>
              <Input
                id="edit-phone"
                name="phone"
                value={companyForm.phone}
                onChange={handleFormChange}
                placeholder="******-123-4567"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-address">Address</Label>
              <Input
                id="edit-address"
                name="address"
                value={companyForm.address}
                onChange={handleFormChange}
                placeholder="123 Business Street, City, State"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleUpdateCompany}>
              Update Company
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {currentCompany?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" variant="destructive" onClick={handleDeleteCompany}>
              Delete Company
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}