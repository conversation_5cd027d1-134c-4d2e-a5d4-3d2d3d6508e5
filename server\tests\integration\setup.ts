import { vi } from 'vitest';
import { Express } from 'express';
import request from 'supertest';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
process.env.SESSION_SECRET = 'test-session-secret';

// Global test setup
beforeEach(() => {
  // Clear all mocks before each test
  vi.clearAllMocks();
});

afterEach(() => {
  // Restore all mocks after each test
  vi.restoreAllMocks();
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Test user data for authentication
export const testUsers = {
  saasAdmin: {
    id: 1,
    username: '<EMAIL>',
    role: 'saas_admin',
    company_id: 1,
    permissions: ['*'] // All permissions
  },
  companyAdmin: {
    id: 2,
    username: '<EMAIL>',
    role: 'owner',
    company_id: 1,
    permissions: ['loan_create_unlimited', 'loan_approve_unlimited', 'customer_view_sensitive']
  },
  loanOfficer: {
    id: 3,
    username: '<EMAIL>',
    role: 'loan_officer',
    company_id: 1,
    permissions: ['loan_create_basic', 'loan_approve_tier1', 'customer_view_basic']
  },
  limitedUser: {
    id: 4,
    username: '<EMAIL>',
    role: 'user',
    company_id: 1,
    permissions: ['customer_view_basic']
  }
};

// Helper function to create authenticated request
export function authenticatedRequest(app: Express, user: typeof testUsers.saasAdmin) {
  const agent = request.agent(app);
  
  // Mock the authentication middleware
  vi.doMock('../middleware/auth', () => ({
    authMiddleware: (req: any, res: any, next: any) => {
      req.user = user;
      next();
    },
    requireRole: (roles: string[]) => (req: any, res: any, next: any) => {
      if (roles.includes(user.role)) {
        next();
      } else {
        res.status(403).json({ message: 'Insufficient role permissions' });
      }
    },
    requirePermission: (permission: string) => (req: any, res: any, next: any) => {
      if (user.permissions.includes('*') || user.permissions.includes(permission)) {
        next();
      } else {
        res.status(403).json({ message: 'Insufficient permissions' });
      }
    }
  }));
  
  return agent;
}

// Test data factories
export const testData = {
  loan: {
    customer_id: 1,
    amount: '10000',
    interest_rate: '10',
    term: 12,
    start_date: new Date().toISOString(),
    status: 'pending'
  },
  customer: {
    full_name: 'Test Customer',
    phone: '+911234567890',
    email: '<EMAIL>',
    address: 'Test Address',
    company_id: 1
  },
  permissionCondition: {
    permission_id: 1,
    condition_type: 'time',
    configuration: {
      start_time: '09:00',
      end_time: '17:00',
      days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
    },
    is_active: true,
    priority: 1
  }
};

// Database mocking helpers
export function mockDatabase() {
  return {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    leftJoin: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis()
  };
}
