#!/usr/bin/env node
/**
 * Migration Script: Company Prefix Settings Migration
 * Purpose: Create the company_prefix_settings table
 * Usage: node scripts/migrations/run-company-prefix-settings-migration.js
 */

import { runMigration, logMigrationStats } from '../utils/migration-runner.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

await runMigration('Company Prefix Settings Migration', async (pool, { dryRun }) => {
  // Read the migration SQL file
  const migrationPath = path.join(__dirname, '..', '..', 'migrations', '009_create_company_prefix_settings.sql');
  console.log(`Reading migration file from: ${migrationPath}`);
  
  if (!fs.existsSync(migrationPath)) {
    throw new Error(`Migration file not found: ${migrationPath}`);
  }
  
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  console.log('Migration SQL loaded successfully');
  
  if (dryRun) {
    console.log('Would execute the following migration:');
    console.log('--- Migration SQL ---');
    console.log(migrationSQL);
    console.log('--- End Migration SQL ---');
    
    // Check current state
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables WHERE table_name = 'company_prefix_settings'
      ) as table_exists;
    `);
    
    logMigrationStats({
      'Migration file': 'Found',
      'Current table exists': tableCheck.rows[0].table_exists ? 'Yes' : 'No',
      'Action': tableCheck.rows[0].table_exists ? 'No changes needed' : 'Would create table'
    });
    return;
  }
  
  // Execute the migration
  console.log('Executing migration...');
  await pool.query(migrationSQL);
  
  // Verify the migration
  const tableCheck = await pool.query(`
    SELECT EXISTS (
      SELECT 1 FROM information_schema.tables WHERE table_name = 'company_prefix_settings'
    ) as table_exists;
  `);
  
  if (tableCheck.rows[0].table_exists) {
    console.log('✅ Migration verified: company_prefix_settings table created successfully!');
    
    // Get table structure for verification
    const columns = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'company_prefix_settings'
      ORDER BY ordinal_position
    `);
    
    console.log('Table structure:');
    columns.rows.forEach(col => {
      console.log(`  - ${col.column_name} (${col.data_type})`);
    });
    
    logMigrationStats({
      'Table created': 'Yes',
      'Column count': columns.rows.length,
      'Migration status': 'Success'
    });
  } else {
    console.log('❌ Migration verification failed: company_prefix_settings table not found!');
    
    logMigrationStats({
      'Table created': 'No',
      'Migration status': 'Failed'
    });
    
    throw new Error('Migration verification failed');
  }
});
