import { db } from '../db';
import { eq, and, like, sql } from 'drizzle-orm';
import { customers, loans } from '@shared/schema';
import { Customer, InsertCustomer } from '@shared/schema';
import errorLogger from '../utils/errorLogger';
import { ICustomerStorage } from './interfaces';

export class CustomerStorage implements ICustomerStorage {
  async getCustomer(id: number): Promise<Customer | undefined> {
    try {
      const [customer] = await db.select()
        .from(customers)
        .where(eq(customers.id, id));
      return customer;
    } catch (error) {
      errorLogger.logError(`Error fetching customer id=${id}`, 'customer-fetch', error as Error);
      return undefined;
    }
  }

  async getCustomersByCompany(companyId: number): Promise<Customer[]> {
    try {
      const companyCustomers = await db.select()
        .from(customers)
        .where(eq(customers.company_id, companyId));
      return companyCustomers;
    } catch (error) {
      errorLogger.logError(`Error fetching customers for company id=${companyId}`, 'customer-fetch', error as Error);
      return [];
    }
  }

  async getCustomersByBranch(branchId: number): Promise<Customer[]> {
    try {
      const branchCustomers = await db.select()
        .from(customers)
        .where(eq(customers.branch_id, branchId));
      return branchCustomers;
    } catch (error) {
      errorLogger.logError(`Error fetching customers for branch id=${branchId}`, 'customer-fetch', error as Error);
      return [];
    }
  }

  async getCustomerByPhone(phone: string, companyId: number): Promise<Customer | undefined> {
    try {
      const [customer] = await db.select()
        .from(customers)
        .where(and(
          eq(customers.phone, phone),
          eq(customers.company_id, companyId)
        ));
      return customer;
    } catch (error) {
      errorLogger.logError(`Error fetching customer by phone=${phone} for company id=${companyId}`, 'customer-fetch', error as Error);
      return undefined;
    }
  }

  async createCustomer(customerData: InsertCustomer): Promise<Customer> {
    try {
      const [customer] = await db.insert(customers)
        .values(customerData)
        .returning();

      return customer;
    } catch (error) {
      errorLogger.logError(`Error creating customer`, 'customer-create', error as Error);
      throw error;
    }
  }

  async updateCustomer(id: number, companyId: number, customerData: Partial<InsertCustomer>): Promise<Customer | undefined> {
    try {
      // First check if the customer exists and belongs to the company
      const [existingCustomer] = await db.select()
        .from(customers)
        .where(eq(customers.id, id));

      if (!existingCustomer || existingCustomer.company_id !== companyId) {
        return undefined;
      }

      const [updatedCustomer] = await db.update(customers)
        .set(customerData)
        .where(eq(customers.id, id))
        .returning();

      return updatedCustomer;
    } catch (error) {
      errorLogger.logError(`Error updating customer id=${id}`, 'customer-update', error as Error);
      throw error;
    }
  }

  async deleteCustomer(id: number, companyId: number): Promise<{ success: boolean, error?: string, loansCount?: number }> {
    try {
      // First check if the customer exists and belongs to the company
      const [existingCustomer] = await db.select()
        .from(customers)
        .where(eq(customers.id, id));

      if (!existingCustomer || existingCustomer.company_id !== companyId) {
        return {
          success: false,
          error: `Customer with id=${id} not found for company id=${companyId}`
        };
      }

      // Check if the customer has any loans
      const customerLoans = await db.select()
        .from(loans)
        .where(eq(loans.customer_id, id));

      if (customerLoans.length > 0) {
        return {
          success: false,
          error: 'Cannot delete customer with associated loans. Please delete all loans first.',
          loansCount: customerLoans.length
        };
      }

      // Delete the customer
      await db.delete(customers)
        .where(eq(customers.id, id));

      return { success: true };
    } catch (error) {
      errorLogger.logError(`Error deleting customer id=${id}`, 'customer-delete', error as Error);
      return {
        success: false,
        error: `Error deleting customer: ${(error as Error).message}`
      };
    }
  }

  /**
   * Get the highest serial number for a given company and prefix (e.g., 'GS-')
   * Returns the highest serial as a number, or 0 if none found.
   * Only considers customers from the specific company.
   */
  async getHighestCustomerSerial(companyId: number, prefix: string): Promise<number> {
    try {
      // Find the max serial for this company and prefix
      // customer_reference_code is like 'GS-001', 'GS-002', ...
      const result = await db.select({ maxString: sql`MAX(${customers.customer_reference_code})` })
        .from(customers)
        .where(
          and(
            eq(customers.company_id, companyId),
            like(customers.customer_reference_code, `${prefix}%`)
          )
        );
      const maxString = result[0]?.maxString as string | undefined;
      if (!maxString) return 0;

      // Extract the serial part (e.g., 'GS-012' => 12)
      const match = maxString.match(/^(.*-)(\d{3})$/);
      if (match) {
        return parseInt(match[2], 10);
      }
      return 0;
    } catch (error) {
      errorLogger.logError(`Error getting highest customer serial for company ${companyId} and prefix ${prefix}`, 'customer-serial', error as Error);
      return 0;
    }
  }
}
