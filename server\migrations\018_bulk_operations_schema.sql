-- Migration: Add bulk operations tables
-- Description: Add user templates and bulk operation logs for bulk user management
-- Date: 2025-01-24

-- User templates for template-based user creation
CREATE TABLE IF NOT EXISTS "user_templates" (
  "id" SERIAL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "company_id" INTEGER NOT NULL REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Template configuration
  "template_config" JSONB NOT NULL DEFAULT '{}',
  "default_role" user_role NOT NULL,
  "default_permissions" JSONB DEFAULT '[]',
  "default_roles" JSONB DEFAULT '[]',
  
  -- Organizational defaults
  "default_branch_id" INTEGER REFERENCES "branches"("id") ON DELETE SET NULL,
  "default_department_id" INTEGER REFERENCES "departments"("id") ON DELETE SET NULL,
  
  -- Template metadata
  "is_active" BOOLEAN NOT NULL DEFAULT true,
  "usage_count" INTEGER NOT NULL DEFAULT 0,
  "created_by" INTEGER REFERENCES "users"("id") ON DELETE SET NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  UNIQUE("name", "company_id")
);

-- Bulk operation logs for tracking bulk operations
CREATE TABLE IF NOT EXISTS "bulk_operation_logs" (
  "id" SERIAL PRIMARY KEY,
  "operation_type" TEXT NOT NULL, -- 'user_import', 'user_export', 'bulk_role_assign', etc.
  "company_id" INTEGER NOT NULL REFERENCES "companies"("id") ON DELETE CASCADE,
  "initiated_by" INTEGER REFERENCES "users"("id") ON DELETE SET NULL,
  
  -- Operation details
  "total_records" INTEGER NOT NULL DEFAULT 0,
  "successful_records" INTEGER NOT NULL DEFAULT 0,
  "failed_records" INTEGER NOT NULL DEFAULT 0,
  
  -- Operation data
  "operation_data" JSONB, -- Input parameters, file info, etc.
  "results" JSONB, -- Success/failure details, error messages, etc.
  
  -- Status and timing
  "status" TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  "started_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "completed_at" TIMESTAMP,
  
  -- Metadata
  "file_name" TEXT, -- Original filename for imports
  "file_size" INTEGER, -- File size in bytes
  "processing_time_ms" INTEGER, -- Processing time in milliseconds
  
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_user_templates_company_id" ON "user_templates"("company_id");
CREATE INDEX IF NOT EXISTS "idx_user_templates_active" ON "user_templates"("is_active");
CREATE INDEX IF NOT EXISTS "idx_user_templates_created_by" ON "user_templates"("created_by");

CREATE INDEX IF NOT EXISTS "idx_bulk_operation_logs_company_id" ON "bulk_operation_logs"("company_id");
CREATE INDEX IF NOT EXISTS "idx_bulk_operation_logs_operation_type" ON "bulk_operation_logs"("operation_type");
CREATE INDEX IF NOT EXISTS "idx_bulk_operation_logs_status" ON "bulk_operation_logs"("status");
CREATE INDEX IF NOT EXISTS "idx_bulk_operation_logs_initiated_by" ON "bulk_operation_logs"("initiated_by");
CREATE INDEX IF NOT EXISTS "idx_bulk_operation_logs_started_at" ON "bulk_operation_logs"("started_at");

-- Insert sample user templates for common roles
-- Note: These templates will be created for each company during company setup
-- This is just a reference for the template structure
-- INSERT INTO "user_templates" ("name", "description", "company_id", "template_config", "default_role", "default_permissions", "default_roles", "is_active", "created_at") VALUES
-- (
--   'Employee Template',
--   'Standard employee template with basic permissions',
--   1, -- Company ID should be set dynamically during company setup
--   '{"defaultFields": {"phone": "+91", "address": ""}, "organizationalDefaults": {}, "accessDefaults": {"role": "employee", "permissions": ["customer_view", "loan_view"], "roles": []}}',
--   'employee',
--   '["customer_view", "loan_view"]',
--   '[]',
--   true,
--   CURRENT_TIMESTAMP
-- ),
-- (
--   'Agent Template',
--   'Collection agent template with collection permissions',
--   1, -- Company ID should be set dynamically during company setup
--   '{"defaultFields": {"phone": "+91", "address": ""}, "organizationalDefaults": {}, "accessDefaults": {"role": "agent", "permissions": ["customer_view", "loan_view", "collection_create", "collection_edit"], "roles": []}}',
--   'agent',
--   '["customer_view", "loan_view", "collection_create", "collection_edit"]',
--   '[]',
--   true,
--   CURRENT_TIMESTAMP
-- ),
-- (
--   'Manager Template',
--   'Manager template with supervisory permissions',
--   1, -- Company ID should be set dynamically during company setup
--   '{"defaultFields": {"phone": "+91", "address": ""}, "organizationalDefaults": {}, "accessDefaults": {"role": "employee", "permissions": ["customer_view", "customer_edit", "loan_view", "loan_edit", "collection_view", "collection_edit", "user_view"], "roles": []}}',
--   'employee',
--   '["customer_view", "customer_edit", "loan_view", "loan_edit", "collection_view", "collection_edit", "user_view"]',
--   '[]',
--   true,
--   CURRENT_TIMESTAMP
-- );

-- Add comments for documentation
COMMENT ON TABLE "user_templates" IS 'Templates for creating users with predefined settings and permissions';
COMMENT ON TABLE "bulk_operation_logs" IS 'Logs for tracking bulk operations like user imports, exports, and bulk role assignments';

COMMENT ON COLUMN "user_templates"."template_config" IS 'JSON configuration containing default field values and organizational settings';
COMMENT ON COLUMN "user_templates"."default_permissions" IS 'Array of permission codes to assign by default';
COMMENT ON COLUMN "user_templates"."default_roles" IS 'Array of role IDs to assign by default';
COMMENT ON COLUMN "user_templates"."usage_count" IS 'Number of times this template has been used to create users';

COMMENT ON COLUMN "bulk_operation_logs"."operation_data" IS 'JSON containing input parameters, file information, and operation configuration';
COMMENT ON COLUMN "bulk_operation_logs"."results" IS 'JSON containing detailed results, success/failure records, and error messages';
COMMENT ON COLUMN "bulk_operation_logs"."processing_time_ms" IS 'Time taken to process the operation in milliseconds';
