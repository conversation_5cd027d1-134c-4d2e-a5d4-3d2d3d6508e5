# TrackFina Migration Guides

This document contains migration guides for major system changes and database updates.

## Username Field Removal Migration

### Overview
This migration removes the redundant `username` field from the users table and all related code. The system uses email-based authentication, making the username field unnecessary.

### Rationale
- **Email is the primary identifier**: The system uses email for login, not username
- **<PERSON>ail is already unique**: Email serves as the unique identifier
- **Redundant data**: Username was only used for display purposes
- **Simplifies user management**: Reduces complexity in forms and validation

### Database Changes

#### Migration File: `migrations/011_remove_username_field.sql`
```sql
-- Remove the unique constraint on username (if it exists)
ALTER TABLE "users" DROP CONSTRAINT IF EXISTS "users_username_key";

-- Drop the username column
ALTER TABLE "users" DROP COLUMN IF EXISTS "username";

-- Update table comment
COMMENT ON TABLE "users" IS 'Users table - uses email as primary identifier for authentication, username field removed as redundant';
```

#### Schema Updates
- **File**: `shared/schema.ts`
  - Removed `username` field from users table definition
  - Updated user schema to exclude username

- **File**: `schema.sql`
  - Removed `username` column from users table
  - Updated default admin user creation to use email instead of username

### Backend Changes

#### API Endpoints
- **File**: `server/routes/auth.routes.ts`
  - Updated registration validation to check email instead of username
  - Removed username from API responses

- **File**: `server/routes.ts`
  - Updated registration endpoint to use email validation
  - Removed username references

- **File**: `server/routes/user.routes.ts`
  - Removed username from user profile responses

#### Storage Layer
- **File**: `server/storage.ts`
  - Removed `getUserByUsername()` method

- **File**: `server/storage/user.storage.ts`
  - Removed `getUserByUsername()` method

### Frontend Changes

#### Authentication
- **File**: `client/src/lib/auth.ts`
  - Updated `UserData` interface to remove username
  - Updated `RegisterData` interface to remove username

- **File**: `client/src/components/auth/auth-context.tsx`
  - Updated `User` interface to remove username

#### Registration Forms
- **File**: `client/src/pages/register.tsx`
  - Removed username field from registration form
  - Updated form validation schema
  - Updated form default values

#### User Management
- **File**: `client/src/pages/user-management/index.tsx`
  - Removed username field from user creation form
  - Updated user form state and validation
  - Removed username column from user table display
  - Updated User interface definition

- **File**: `client/src/pages/user-management/groups/[id].tsx`
  - Updated User interface to remove username
  - Removed username column from group members table

#### Profile Page
- **File**: `client/src/pages/profile.tsx`
  - Removed username display from profile page
  - Removed unused AtSignIcon import

### Migration Steps

#### 1. Database Migration
```bash
# Run the migration script
psql -d your_database -f migrations/011_remove_username_field.sql
```

#### 2. Backend Deployment
- Deploy updated backend code
- Restart server to apply schema changes

#### 3. Frontend Deployment
- Deploy updated frontend code
- Clear browser cache if needed

### Testing Checklist

#### Authentication
- [ ] User registration works without username
- [ ] User login works with email
- [ ] Password reset works with email
- [ ] JWT tokens are generated correctly

#### User Management
- [ ] User creation form works without username
- [ ] User list displays correctly without username column
- [ ] User profile displays correctly without username
- [ ] Group management works without username references

#### API Endpoints
- [ ] POST /api/auth/register works without username
- [ ] GET /api/users returns data without username
- [ ] User-related API responses exclude username

### Rollback Plan

If rollback is needed:

1. **Add username column back**:
```sql
ALTER TABLE "users" ADD COLUMN "username" varchar(100);
UPDATE "users" SET "username" = SPLIT_PART("email", '@', 1);
ALTER TABLE "users" ADD CONSTRAINT "users_username_key" UNIQUE ("username");
```

2. **Revert code changes** using git
3. **Update default admin user** to include username

### Post-Migration Notes

- All existing users will continue to work with email-based authentication
- No data loss occurs as email remains the primary identifier
- User experience is simplified with one less field to manage
- System performance may improve slightly due to reduced data storage and processing

### Impact Assessment

#### Positive Impacts
- Simplified user registration and management
- Reduced form complexity
- Cleaner database schema
- Better alignment with email-based authentication

#### No Negative Impacts
- No functionality is lost
- All authentication continues to work
- No user data is lost
- No breaking changes for existing users

## Reference Code Migration

### Overview
Migration guide for implementing reference codes across all entities to replace internal ID exposure.

### Implementation Steps

1. **Add reference_code columns** to all entity tables
2. **Generate reference codes** for existing records
3. **Update API endpoints** to use reference codes
4. **Update frontend** to display reference codes
5. **Update search and filtering** to use reference codes

### Database Schema Changes
```sql
-- Add reference_code column to each entity table
ALTER TABLE loans ADD COLUMN reference_code VARCHAR(50) UNIQUE;
ALTER TABLE customers ADD COLUMN reference_code VARCHAR(50) UNIQUE;
ALTER TABLE agents ADD COLUMN reference_code VARCHAR(50) UNIQUE;
-- ... repeat for other entities

-- Create indexes for performance
CREATE INDEX idx_loans_reference_code ON loans(reference_code);
CREATE INDEX idx_customers_reference_code ON customers(reference_code);
-- ... repeat for other entities
```

### Data Migration Script
```typescript
const migrateToReferenceCodes = async () => {
  const companies = await getAllCompanies();
  
  for (const company of companies) {
    // Generate reference codes for existing records
    await generateReferenceCodesForCompany(company.id, company.prefix);
  }
};
```

This migration guide ensures smooth transitions for major system changes while maintaining data integrity and system functionality.
