import logger from './logger';

/**
 * Static helpers for getting context data from localStorage when hooks are not available
 * These should only be used in special cases where the useContextData hook cannot be used
 */

export interface ContextData {
  companyId: number | null;
  companyName: string | null;
  branchId: number | null;
  userId: number | null;
  username: string | null;
}

/**
 * Get current context data from localStorage
 * @returns Object containing current context data
 */
export function getContextData(): ContextData {
  try {
    // Get company data
    const contextData: ContextData = {
      companyId: null,
      companyName: null,
      branchId: null,
      userId: null,
      username: null
    };
    
    // Try to get user data first
    const userData = localStorage.getItem('user_data');
    if (userData) {
      try {
        const user = JSON.parse(userData);
        contextData.userId = user.id;
        contextData.username = user.username;
        contextData.companyId = user.company_id;
        contextData.companyName = user.company_name;
      } catch (e) {
        logger.error('Error parsing user_data from localStorage', e instanceof Error ? e : new Error('JSON parse error'), {
          context: 'context-helpers'
        });
      }
    }
    
    // Try to get branch data if it exists
    const selectedBranch = localStorage.getItem('selected_branch');
    if (selectedBranch) {
      try {
        const branch = JSON.parse(selectedBranch);
        contextData.branchId = branch.id;
      } catch (e) {
        logger.error('Error parsing selected_branch from localStorage', e instanceof Error ? e : new Error('JSON parse error'), {
          context: 'context-helpers'
        });
      }
    }
    
    return contextData;
  } catch (e) {
    logger.error('Error getting context data', e instanceof Error ? e : new Error('Unknown error'), {
      context: 'context-helpers'
    });
    
    return {
      companyId: null,
      companyName: null,
      branchId: null,
      userId: null,
      username: null
    };
  }
}

/**
 * Get just the company ID using the most reliable method available
 * @returns Company ID or null if not available
 */
export function getCompanyId(): number | null {
  const { companyId } = getContextData();
  return companyId;
}

/**
 * Validates if a company ID is available in the current context
 * @returns Boolean indicating if company context is available
 */
export function hasCompanyContext(): boolean {
  return getCompanyId() !== null;
}

/**
 * Create URL with company context for API requests
 * @param endpoint API endpoint path 
 * @param params Optional query parameters
 * @returns Fully formed URL with company context included
 */
export function createContextUrl(endpoint: string, params?: Record<string, string | number | boolean>): string {
  const companyId = getCompanyId();
  
  if (!companyId) {
    logger.warn('Creating URL without company context', {
      context: 'context-helpers',
      data: { endpoint }
    });
    return endpoint;
  }
  
  // Replace company ID placeholder if it exists
  const baseUrl = endpoint.includes(':companyId') 
    ? endpoint.replace(':companyId', companyId.toString()) 
    : endpoint;
    
  // Format as /api/companies/{companyId}/{path} if not already in that format
  let url = baseUrl;
  if (!url.includes(`/companies/${companyId}`) && !url.startsWith('/api/auth')) {
    if (url.startsWith('/api/')) {
      url = `/api/companies/${companyId}${url.substring(4)}`;
    } else if (!url.startsWith('/api/')) {
      url = `/api/companies/${companyId}/${url.startsWith('/') ? url.substring(1) : url}`;
    }
  }
  
  // Add query parameters if provided
  if (params && Object.keys(params).length > 0) {
    const queryString = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
      .join('&');
    
    url += url.includes('?') ? `&${queryString}` : `?${queryString}`;
  }
  
  return url;
}