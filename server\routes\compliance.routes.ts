import { Router } from 'express';
import { complianceService } from '../services/complianceService';
import { enhancedAuthMiddleware, type EnhancedAuthRequest } from '../middleware/enhancedAuth';
import { requirePermission } from '../middleware/permission';
import { auditAdminOperation } from '../middleware/auditMiddleware';
import { z } from 'zod';

const router = Router();

// Apply enhanced auth middleware to all routes
router.use(enhancedAuthMiddleware);

// Validation schemas
const complianceFiltersSchema = z.object({
  frameworkId: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  requirementId: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  status: z.string().optional(),
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
});

const createFrameworkSchema = z.object({
  framework_code: z.string().min(1),
  framework_name: z.string().min(1),
  framework_type: z.enum(['sox', 'gdpr', 'pci_dss', 'hipaa', 'iso27001', 'nist', 'coso', 'cobit', 'custom']),
  description: z.string().optional(),
  version: z.string().optional(),
  effective_date: z.string().optional().transform(val => val ? new Date(val) : undefined),
});

const createRequirementSchema = z.object({
  framework_id: z.number(),
  requirement_code: z.string().min(1),
  requirement_name: z.string().min(1),
  description: z.string().optional(),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  control_type: z.string().optional(),
  risk_level: z.enum(['very_low', 'low', 'medium', 'high', 'very_high', 'critical']).optional(),
  frequency: z.string().optional(),
  automated_check: z.boolean().optional(),
  check_query: z.string().optional(),
  remediation_guidance: z.string().optional(),
});

const createAssessmentSchema = z.object({
  framework_id: z.number(),
  requirement_id: z.number(),
  assessment_date: z.string().transform(val => new Date(val)),
  assessment_period_start: z.string().optional().transform(val => val ? new Date(val) : undefined),
  assessment_period_end: z.string().optional().transform(val => val ? new Date(val) : undefined),
  status: z.enum(['compliant', 'non_compliant', 'partially_compliant', 'under_review', 'not_assessed']),
  score: z.number().min(0).max(100).optional(),
  risk_rating: z.enum(['very_low', 'low', 'medium', 'high', 'very_high', 'critical']).optional(),
  findings: z.string().optional(),
  evidence: z.array(z.any()).optional(),
  gaps_identified: z.array(z.any()).optional(),
  remediation_actions: z.array(z.any()).optional(),
  remediation_deadline: z.string().optional().transform(val => val ? new Date(val) : undefined),
  remediation_owner: z.number().optional(),
});

// Get compliance frameworks
router.get('/frameworks',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_frameworks'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { frameworkType, isActive } = req.query;

      const frameworks = await complianceService.getFrameworks({
        frameworkType: frameworkType as string,
        isActive: isActive === 'true',
      });

      res.json(frameworks);
    } catch (error) {
      console.error('Error fetching compliance frameworks:', error);
      res.status(500).json({ message: 'Failed to fetch compliance frameworks' });
    }
  }
);

// Create compliance framework
router.post('/frameworks',
  requirePermission('compliance_manage'),
  auditAdminOperation('compliance_manage', 'create_framework'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const frameworkData = createFrameworkSchema.parse(req.body);

      const framework = await complianceService.createFramework(frameworkData);

      res.status(201).json(framework);
    } catch (error) {
      console.error('Error creating compliance framework:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid framework data', errors: error.errors });
      }
      res.status(500).json({ message: 'Failed to create compliance framework' });
    }
  }
);

// Get compliance requirements
router.get('/requirements',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_requirements'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { frameworkId, category, riskLevel, automatedCheck, isActive } = req.query;

      const requirements = await complianceService.getRequirements({
        frameworkId: frameworkId ? parseInt(frameworkId as string) : undefined,
        category: category as string,
        riskLevel: riskLevel as string,
        automatedCheck: automatedCheck === 'true',
        isActive: isActive === 'true',
      });

      res.json(requirements);
    } catch (error) {
      console.error('Error fetching compliance requirements:', error);
      res.status(500).json({ message: 'Failed to fetch compliance requirements' });
    }
  }
);

// Create compliance requirement
router.post('/requirements',
  requirePermission('compliance_manage'),
  auditAdminOperation('compliance_manage', 'create_requirement'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const requirementData = createRequirementSchema.parse(req.body);

      const requirement = await complianceService.createRequirement(requirementData);

      res.status(201).json(requirement);
    } catch (error) {
      console.error('Error creating compliance requirement:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid requirement data', errors: error.errors });
      }
      res.status(500).json({ message: 'Failed to create compliance requirement' });
    }
  }
);

// Get compliance assessments
router.get('/assessments',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_assessments'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const filters = complianceFiltersSchema.parse(req.query);

      const result = await complianceService.getAssessments({
        companyId: req.user!.company_id,
        ...filters,
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching compliance assessments:', error);
      res.status(500).json({ message: 'Failed to fetch compliance assessments' });
    }
  }
);

// Create compliance assessment
router.post('/assessments',
  requirePermission('compliance_assess'),
  auditAdminOperation('compliance_assess', 'create_assessment'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const assessmentData = createAssessmentSchema.parse(req.body);

      const assessment = await complianceService.createAssessment({
        ...assessmentData,
        company_id: req.user!.company_id,
        assessed_by: req.user!.id,
      });

      res.status(201).json(assessment);
    } catch (error) {
      console.error('Error creating compliance assessment:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid assessment data', errors: error.errors });
      }
      res.status(500).json({ message: 'Failed to create compliance assessment' });
    }
  }
);

// Update compliance assessment
router.put('/assessments/:id',
  requirePermission('compliance_assess'),
  auditAdminOperation('compliance_assess', 'update_assessment'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const assessmentId = parseInt(req.params.id);
      const updateData = req.body;

      const assessment = await complianceService.updateAssessment(assessmentId, updateData);

      res.json(assessment);
    } catch (error) {
      console.error('Error updating compliance assessment:', error);
      res.status(500).json({ message: 'Failed to update compliance assessment' });
    }
  }
);

// Get access certifications
router.get('/certifications',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_certifications'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { status, certificationType, targetUserId, primaryReviewer, dueBefore, limit, offset } = req.query;

      const result = await complianceService.getAccessCertifications({
        companyId: req.user!.company_id,
        status: status as string,
        certificationType: certificationType as string,
        targetUserId: targetUserId ? parseInt(targetUserId as string) : undefined,
        primaryReviewer: primaryReviewer ? parseInt(primaryReviewer as string) : undefined,
        dueBefore: dueBefore ? new Date(dueBefore as string) : undefined,
        limit: limit ? parseInt(limit as string) : 50,
        offset: offset ? parseInt(offset as string) : 0,
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching access certifications:', error);
      res.status(500).json({ message: 'Failed to fetch access certifications' });
    }
  }
);

// Create access certification
router.post('/certifications',
  requirePermission('compliance_certify'),
  auditAdminOperation('compliance_certify', 'create_certification'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const certificationData = req.body;

      const certification = await complianceService.createAccessCertification({
        ...certificationData,
        company_id: req.user!.company_id,
        initiated_by: req.user!.id,
      });

      res.status(201).json(certification);
    } catch (error) {
      console.error('Error creating access certification:', error);
      res.status(500).json({ message: 'Failed to create access certification' });
    }
  }
);

// Update access certification
router.put('/certifications/:id',
  requirePermission('compliance_certify'),
  auditAdminOperation('compliance_certify', 'update_certification'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const certificationId = parseInt(req.params.id);
      const updateData = req.body;

      const certification = await complianceService.updateAccessCertification(certificationId, updateData);

      res.json(certification);
    } catch (error) {
      console.error('Error updating access certification:', error);
      res.status(500).json({ message: 'Failed to update access certification' });
    }
  }
);

// Get compliance violations
router.get('/violations',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_violations'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const filters = complianceFiltersSchema.parse(req.query);

      const result = await complianceService.getViolations({
        companyId: req.user!.company_id,
        ...filters,
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching compliance violations:', error);
      res.status(500).json({ message: 'Failed to fetch compliance violations' });
    }
  }
);

// Create compliance violation
router.post('/violations',
  requirePermission('compliance_manage'),
  auditAdminOperation('compliance_manage', 'create_violation'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const violationData = req.body;

      const violation = await complianceService.createViolation({
        ...violationData,
        company_id: req.user!.company_id,
        detected_by: req.user!.id,
      });

      res.status(201).json(violation);
    } catch (error) {
      console.error('Error creating compliance violation:', error);
      res.status(500).json({ message: 'Failed to create compliance violation' });
    }
  }
);

// Update compliance violation
router.put('/violations/:id',
  requirePermission('compliance_manage'),
  auditAdminOperation('compliance_manage', 'update_violation'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const violationId = parseInt(req.params.id);
      const updateData = req.body;

      const violation = await complianceService.updateViolation(violationId, updateData);

      res.json(violation);
    } catch (error) {
      console.error('Error updating compliance violation:', error);
      res.status(500).json({ message: 'Failed to update compliance violation' });
    }
  }
);

// Get compliance dashboard
router.get('/dashboard',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_dashboard'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const dashboard = await complianceService.getComplianceDashboard(req.user!.company_id);

      res.json(dashboard);
    } catch (error) {
      console.error('Error fetching compliance dashboard:', error);
      res.status(500).json({ message: 'Failed to fetch compliance dashboard' });
    }
  }
);

// Generate regulatory compliance report
router.get('/reports/regulatory/:frameworkId',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'generate_regulatory_report'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const frameworkId = parseInt(req.params.frameworkId);
      const { startDate, endDate } = req.query;

      if (!startDate || !endDate) {
        return res.status(400).json({
          message: 'Start date and end date are required for regulatory report'
        });
      }

      const report = await complianceService.generateRegulatoryReport(
        req.user!.company_id,
        frameworkId,
        new Date(startDate as string),
        new Date(endDate as string)
      );

      res.json(report);
    } catch (error) {
      console.error('Error generating regulatory report:', error);
      res.status(500).json({ message: 'Failed to generate regulatory report' });
    }
  }
);

// Initiate bulk access certification
router.post('/certifications/bulk',
  requirePermission('compliance_certify'),
  auditAdminOperation('compliance_certify', 'bulk_certification'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { userIds, roleIds, certificationType, dueDate, primaryReviewer } = req.body;

      const certifications = [];

      // Create certifications for users
      if (userIds && Array.isArray(userIds)) {
        for (const userId of userIds) {
          const certification = await complianceService.createAccessCertification({
            company_id: req.user!.company_id,
            initiated_by: req.user!.id,
            certification_type: certificationType,
            target_user_id: userId,
            due_date: new Date(dueDate),
            primary_reviewer: primaryReviewer,
            scope_description: `Bulk access certification for user ${userId}`,
          });
          certifications.push(certification);
        }
      }

      // Create certifications for roles
      if (roleIds && Array.isArray(roleIds)) {
        for (const roleId of roleIds) {
          const certification = await complianceService.createAccessCertification({
            company_id: req.user!.company_id,
            initiated_by: req.user!.id,
            certification_type: certificationType,
            target_role_id: roleId,
            due_date: new Date(dueDate),
            primary_reviewer: primaryReviewer,
            scope_description: `Bulk access certification for role ${roleId}`,
          });
          certifications.push(certification);
        }
      }

      res.status(201).json({
        message: `Created ${certifications.length} access certifications`,
        certifications
      });
    } catch (error) {
      console.error('Error creating bulk access certifications:', error);
      res.status(500).json({ message: 'Failed to create bulk access certifications' });
    }
  }
);

// Get overdue certifications
router.get('/certifications/overdue',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_overdue_certifications'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const result = await complianceService.getAccessCertifications({
        companyId: req.user!.company_id,
        dueBefore: new Date(),
        status: 'pending',
        limit: 100,
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching overdue certifications:', error);
      res.status(500).json({ message: 'Failed to fetch overdue certifications' });
    }
  }
);

// Get compliance metrics
router.get('/metrics',
  requirePermission('compliance_view'),
  auditAdminOperation('compliance_view', 'view_metrics'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { startDate, endDate } = req.query;

      const dashboard = await complianceService.getComplianceDashboard(req.user!.company_id);

      // Additional metrics can be calculated here
      const metrics = {
        overallScore: dashboard.overallScore,
        frameworkCount: dashboard.frameworkCompliance.length,
        compliantFrameworks: dashboard.frameworkCompliance.filter(f => f.status === 'compliant').length,
        pendingCertifications: dashboard.pendingCertifications.length,
        activeViolations: dashboard.activeViolations.length,
        upcomingDeadlines: dashboard.upcomingDeadlines.length,
        riskDistribution: {
          low: dashboard.activeViolations.filter(v => v.severity === 'low').length,
          medium: dashboard.activeViolations.filter(v => v.severity === 'medium').length,
          high: dashboard.activeViolations.filter(v => v.severity === 'high').length,
          critical: dashboard.activeViolations.filter(v => v.severity === 'critical').length,
        },
      };

      res.json(metrics);
    } catch (error) {
      console.error('Error fetching compliance metrics:', error);
      res.status(500).json({ message: 'Failed to fetch compliance metrics' });
    }
  }
);

// Export compliance data
router.get('/export',
  requirePermission('compliance_export'),
  auditAdminOperation('compliance_export', 'export_compliance_data'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { frameworkId, startDate, endDate } = req.query;

      if (!startDate || !endDate) {
        return res.status(400).json({
          message: 'Start date and end date are required for compliance export'
        });
      }

      const [assessments, certifications, violations] = await Promise.all([
        complianceService.getAssessments({
          companyId: req.user!.company_id,
          frameworkId: frameworkId ? parseInt(frameworkId as string) : undefined,
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string),
          limit: 10000,
        }),
        complianceService.getAccessCertifications({
          companyId: req.user!.company_id,
          limit: 10000,
        }),
        complianceService.getViolations({
          companyId: req.user!.company_id,
          frameworkId: frameworkId ? parseInt(frameworkId as string) : undefined,
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string),
          limit: 10000,
        }),
      ]);

      const exportData = {
        exportedAt: new Date().toISOString(),
        exportedBy: req.user!.id,
        companyId: req.user!.company_id,
        dateRange: {
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string),
        },
        data: {
          assessments,
          certifications,
          violations,
        },
      };

      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="compliance-export-${Date.now()}.json"`);
      res.json(exportData);
    } catch (error) {
      console.error('Error exporting compliance data:', error);
      res.status(500).json({ message: 'Failed to export compliance data' });
    }
  }
);

export default router;
