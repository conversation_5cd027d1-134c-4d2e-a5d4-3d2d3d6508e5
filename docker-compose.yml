version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      - postgres
    environment:
      - DATABASE_URL=*****************************************************/trackfina
      - PORT=3000
      - NODE_ENV=production
      - SESSION_SECRET=your_session_secret_here
    volumes:
      - ./data:/app/data
      - ./.env:/app/.env
    restart: unless-stopped
    networks:
      - trackfina-network

  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=trackfina
      - POSTGRES_PASSWORD=trackfina_password
      - POSTGRES_DB=trackfina
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql
    restart: unless-stopped
    networks:
      - trackfina-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trackfina"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres-data:

networks:
  trackfina-network:
    driver: bridge