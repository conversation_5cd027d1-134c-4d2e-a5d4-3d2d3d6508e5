import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../utils/jwt';
import { storage } from '../storage';
import { hasPermission } from './permission';
import errorLogger from '../utils/errorLogger';

// Define the auth request interface with user data
export interface AuthRequest extends Request {
  user?: {
    id: number;
    role: string;
    company_id?: number;
    permissions?: string[];
  };
  cookies?: {
    [key: string]: string
  };
}

export async function authMiddleware(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    // Check authorization header first (Bearer token)
    const authHeader = req.headers.authorization;
    let token = null;
    let decoded = null;

    // First try with authorization header
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
      try {
        decoded = await verifyToken(token);
      } catch (tokenError) {
        console.error('Invalid token in Authorization header:', tokenError);
      }
    }

    // If no valid token from authorization header, try cookie
    // Using cookie-parser (req.cookies) instead of parsing manually
    if (!decoded && req.cookies && req.cookies.auth_token) {
      try {
        token = req.cookies.auth_token;
        decoded = await verifyToken(token);
      } catch (cookieTokenError) {
        errorLogger.logDebug('Invalid token in cookie', 'auth-middleware', cookieTokenError as Error);
      }
    }

    // If no valid token found, return unauthorized
    if (!decoded || typeof decoded !== 'object' || !decoded.userId) {
      errorLogger.logDebug('No valid token found in request', 'auth-middleware', {
        hasAuthHeader: !!authHeader,
        hasCookies: !!req.headers.cookie,
        path: req.path
      });
      return res.status(401).json({ message: 'Authentication required' });
    }

    const user = await storage.getUser(decoded.userId);

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    req.user = {
      id: user.id,
      role: user.role,
      company_id: user.company_id
    };

    next();
  } catch (error) {
    errorLogger.logError('Auth middleware error', 'auth-middleware', error as Error);
    return res.status(401).json({ message: 'Authentication failed' });
  }
}

export function requireRole(roles: string[]) {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    next();
  };
}

export function requireCompanyAccess(req: AuthRequest, res: Response, next: NextFunction) {
  const companyId = parseInt(req.params.companyId || req.body.company_id, 10);

  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  // SaaS admin has access to all companies
  if (req.user.role === 'saas_admin') {
    return next();
  }

  // For other users, we need to check if they have access to the requested company
  // If the user's current token has this company_id (was issued for this company), grant access
  if (req.user.company_id === companyId) {
    return next();
  }

  // Check if the user has access to the requested company through their associations
  storage.getUserCompanies(req.user.id)
    .then(userCompanies => {
      const hasCompanyAccess = userCompanies.some(uc =>
        uc.company_id === companyId ||
        (uc.company && uc.company.id === companyId)
      );

      if (hasCompanyAccess) {
        // User has access to this company through an association
        return next();
      } else {
        // User doesn't have access to this company
        console.log(`User ${req.user?.id} tried to access company ${companyId} but is not associated with it`);
        return res.status(403).json({ message: 'Access denied to this company' });
      }
    })
    .catch(error => {
      console.error('Error checking company access:', error);
      return res.status(500).json({ message: 'Error checking company access' });
    });
}

/**
 * Middleware to check if user has required permission
 * @param permissionCode Permission code to check
 * @returns Middleware function
 */
export function requirePermission(permissionCode: string) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // System admins have all permissions
      if (req.user.role === 'saas_admin') {
        return next();
      }

      // Check if user has permission
      const hasAccess = await hasPermission(req.user.id, permissionCode);

      if (!hasAccess) {
        return res.status(403).json({
          message: 'Access denied. You do not have the required permission.',
          required_permission: permissionCode
        });
      }

      next();
    } catch (error) {
      console.error('Error in permission middleware:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  };
}

/**
 * Middleware to check if user has any of the required permissions
 * @param permissionCodes Array of permission codes to check
 * @returns Middleware function
 */
export function requireAnyPermission(permissionCodes: string[]) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // System admins have all permissions
      if (req.user.role === 'saas_admin') {
        return next();
      }

      // Check if user has any of the required permissions
      for (const code of permissionCodes) {
        const hasAccess = await hasPermission(req.user.id, code);
        if (hasAccess) {
          return next();
        }
      }

      return res.status(403).json({
        message: 'Access denied. You do not have any of the required permissions.',
        required_permissions: permissionCodes
      });
    } catch (error) {
      console.error('Error in permission middleware:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  };
}
