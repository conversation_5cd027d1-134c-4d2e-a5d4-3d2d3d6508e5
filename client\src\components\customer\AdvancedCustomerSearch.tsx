import { useState } from "react";
import { 
  Card, 
  CardContent 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, Filter, MapPin, User, Phone, Mail, FileText, X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose
} from "@/components/ui/sheet";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export type SearchFilters = {
  query: string;
  searchBy: string;
  location: string;
  creditScoreRange: [number, number] | null;
  status: string;
  loanStatus: string;
};

type Props = {
  onSearch: (filters: SearchFilters) => void;
  isLoading?: boolean;
};

const DEFAULT_FILTERS: SearchFilters = {
  query: "",
  searchBy: "all",
  location: "",
  creditScoreRange: null,
  status: "all",
  loanStatus: "all"
};

export function AdvancedCustomerSearch({ onSearch, isLoading = false }: Props) {
  const [filters, setFilters] = useState<SearchFilters>(DEFAULT_FILTERS);
  const [expandedFilters, setExpandedFilters] = useState(false);
  
  // Helper function to capitalize status
  const capitalizeStatus = (status?: string) => {
    if (!status || status === 'all') return status;
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any, applySearch: boolean = false) => {
    const updatedFilters = { ...filters, [key]: value };
    setFilters(updatedFilters);
    
    // Immediately apply search if requested
    if (applySearch) {
      onSearch(updatedFilters);
    }
  };

  const handleSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    onSearch(filters);
  };

  const handleReset = () => {
    setFilters(DEFAULT_FILTERS);
    // Apply the reset filters immediately by calling onSearch
    onSearch(DEFAULT_FILTERS);
  };

  // Customize search input placeholder based on search type
  const getSearchPlaceholder = () => {
    switch (filters.searchBy) {
      case "id": return "Search by Customer ID...";
      case "name": return "Search by customer name...";
      case "email": return "Search by customer email...";
      case "phone": return "Search by phone number...";
      default: return "Search customers by name, ID, email or phone...";
    }
  };

  // Get search field icon based on search type
  const getSearchIcon = () => {
    switch (filters.searchBy) {
      case "id": return <FileText className="h-4 w-4 text-muted-foreground" />;
      case "name": return <User className="h-4 w-4 text-muted-foreground" />;
      case "email": return <Mail className="h-4 w-4 text-muted-foreground" />;
      case "phone": return <Phone className="h-4 w-4 text-muted-foreground" />;
      default: return <Search className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="flex flex-col lg:flex-row gap-3">
            {/* Main search field */}
            <div className="relative flex-1">
              <div className="absolute left-2.5 top-2.5">
                {getSearchIcon()}
              </div>
              <Input
                type="search"
                placeholder={getSearchPlaceholder()}
                className="pl-8"
                value={filters.query}
                onChange={(e) => handleFilterChange("query", e.target.value)}
              />
            </div>

            {/* Search type selector */}
            <div className="w-full lg:w-48">
              <Select 
                value={filters.searchBy}
                onValueChange={(value) => handleFilterChange("searchBy", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Search by..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Fields</SelectItem>
                  <SelectItem value="id">Customer ID</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="phone">Phone</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Advanced Filters Button */}
            <Sheet>
              <SheetTrigger asChild>
                <Button type="button" variant="outline" className="gap-1">
                  <Filter className="h-4 w-4" />
                  <span>Advanced Filters</span>
                </Button>
              </SheetTrigger>
              <SheetContent className="sm:max-w-md">
                <SheetHeader>
                  <SheetTitle>Advanced Filters</SheetTitle>
                  <SheetDescription>
                    Filter customers by various criteria to find exactly what you need.
                  </SheetDescription>
                </SheetHeader>
                <div className="mt-6 space-y-6">
                  <Accordion type="single" collapsible defaultValue="location">
                    {/* Location Filter */}
                    <AccordionItem value="location">
                      <AccordionTrigger>
                        <div className="flex gap-2 items-center">
                          <MapPin className="h-4 w-4" />
                          <span>Location</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2">
                          <Label htmlFor="location">Customer Location</Label>
                          <Input
                            id="location"
                            placeholder="City, region, or address"
                            value={filters.location}
                            onChange={(e) => handleFilterChange("location", e.target.value)}
                          />
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Credit Score Filter */}
                    <AccordionItem value="creditScore">
                      <AccordionTrigger>
                        <div className="flex gap-2 items-center">
                          <FileText className="h-4 w-4" />
                          <span>Credit Score</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="creditScoreMin">Minimum Credit Score</Label>
                            <Input
                              id="creditScoreMin"
                              type="number"
                              placeholder="Min Score"
                              value={filters.creditScoreRange?.[0] || ""}
                              onChange={(e) => {
                                const min = e.target.value ? parseInt(e.target.value) : 0;
                                const max = filters.creditScoreRange?.[1] || 900;
                                handleFilterChange("creditScoreRange", [min, max]);
                              }}
                            />
                          </div>
                          <div>
                            <Label htmlFor="creditScoreMax">Maximum Credit Score</Label>
                            <Input
                              id="creditScoreMax"
                              type="number"
                              placeholder="Max Score"
                              value={filters.creditScoreRange?.[1] || ""}
                              onChange={(e) => {
                                const max = e.target.value ? parseInt(e.target.value) : 900;
                                const min = filters.creditScoreRange?.[0] || 0;
                                handleFilterChange("creditScoreRange", [min, max]);
                              }}
                            />
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Status Filter */}
                    <AccordionItem value="status">
                      <AccordionTrigger>
                        <div className="flex gap-2 items-center">
                          <User className="h-4 w-4" />
                          <span>Customer Status</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2">
                          <Label htmlFor="status">Status</Label>
                          <Select 
                            value={filters.status}
                            onValueChange={(value) => handleFilterChange("status", value)}
                          >
                            <SelectTrigger id="status">
                              <SelectValue placeholder="Select Status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="inactive">Inactive</SelectItem>
                              <SelectItem value="verified">KYC Verified</SelectItem>
                              <SelectItem value="unverified">KYC Unverified</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Loan Status Filter */}
                    <AccordionItem value="loanStatus">
                      <AccordionTrigger>
                        <div className="flex gap-2 items-center">
                          <FileText className="h-4 w-4" />
                          <span>Loan Status</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-2">
                          <Label htmlFor="loanStatus">Loan Status</Label>
                          <Select 
                            value={filters.loanStatus}
                            onValueChange={(value) => handleFilterChange("loanStatus", value)}
                          >
                            <SelectTrigger id="loanStatus">
                              <SelectValue placeholder="Select Loan Status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              <SelectItem value="active">Active Loans</SelectItem>
                              <SelectItem value="completed">Completed Loans</SelectItem>
                              <SelectItem value="overdue">Overdue Loans</SelectItem>
                              <SelectItem value="pending">Pending Approval</SelectItem>
                              <SelectItem value="rejected">Rejected</SelectItem>
                              <SelectItem value="none">No Loans</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
                <SheetFooter className="mt-6 flex gap-2 sm:justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={handleReset}
                    className="gap-1"
                  >
                    <X className="h-4 w-4" />
                    Reset
                  </Button>
                  <SheetClose asChild>
                    <Button 
                      type="button" 
                      onClick={() => handleSearch()}
                      className="gap-1"
                    >
                      <Search className="h-4 w-4" />
                      Apply Filters
                    </Button>
                  </SheetClose>
                </SheetFooter>
              </SheetContent>
            </Sheet>

            <Button type="submit" disabled={isLoading} className="gap-1">
              {isLoading ? (
                <>Searching...</>
              ) : (
                <>
                  <Search className="h-4 w-4" />
                  <span>Search</span>
                </>
              )}
            </Button>
          </div>

          {/* Applied filters display */}
          {(filters.location || filters.creditScoreRange || filters.status !== 'all' || filters.loanStatus !== 'all') && (
            <div className="flex flex-wrap gap-2 pt-2">
              <div className="text-sm text-muted-foreground">Active filters:</div>
              
              {filters.location && (
                <div className="inline-flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-md">
                  <MapPin className="h-3 w-3" />
                  <span>Location: {filters.location}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => handleFilterChange("location", "", true)}
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove</span>
                  </Button>
                </div>
              )}
              
              {filters.creditScoreRange && (
                <div className="inline-flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-md">
                  <FileText className="h-3 w-3" />
                  <span>Credit: {filters.creditScoreRange[0]}-{filters.creditScoreRange[1]}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => handleFilterChange("creditScoreRange", null, true)}
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove</span>
                  </Button>
                </div>
              )}
              
              {filters.status !== 'all' && (
                <div className="inline-flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-md">
                  <User className="h-3 w-3" />
                  <span>Status: {capitalizeStatus(filters.status)}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => handleFilterChange("status", "all", true)}
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove</span>
                  </Button>
                </div>
              )}
              
              {filters.loanStatus !== 'all' && (
                <div className="inline-flex items-center gap-1 text-xs bg-muted px-2 py-1 rounded-md">
                  <FileText className="h-3 w-3" />
                  <span>Loan: {capitalizeStatus(filters.loanStatus)}</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => handleFilterChange("loanStatus", "all", true)}
                  >
                    <X className="h-3 w-3" />
                    <span className="sr-only">Remove</span>
                  </Button>
                </div>
              )}
              
              {(filters.location || filters.creditScoreRange || filters.status !== 'all' || filters.loanStatus !== 'all') && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs h-6"
                  onClick={handleReset}
                  type="button" 
                >
                  Clear all
                </Button>
              )}
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}