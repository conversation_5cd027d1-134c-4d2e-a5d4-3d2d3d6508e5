import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/api';

interface PermissionCondition {
  id?: number;
  permission_id: number;
  condition_type: 'time' | 'location' | 'amount' | 'approval' | 'device' | 'session';
  condition_config: any;
  is_active: boolean;
  priority: number;
  description: string;
  created_at?: string;
  updated_at?: string;
}

interface ConditionType {
  type: string;
  name: string;
  description: string;
  schema: Record<string, any>;
}

export function usePermissionConditions(permissionCode?: string) {
  const [conditions, setConditions] = useState<PermissionCondition[]>([]);
  const [conditionTypes, setConditionTypes] = useState<ConditionType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load conditions for a specific permission
  const loadConditions = async (code: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiRequest('GET', `/api/permissions/${code}/conditions`);

      if (!response.ok) {
        throw new Error(`Failed to load conditions: ${response.statusText}`);
      }

      const data = await response.json();
      setConditions(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load conditions';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load condition types and schemas
  const loadConditionTypes = async () => {
    try {
      const response = await fetch('/api/permission-conditions/types');

      if (!response.ok) {
        throw new Error(`Failed to load condition types: ${response.statusText}`);
      }

      const data = await response.json();
      setConditionTypes(data);
    } catch (err) {
      console.error('Error loading condition types:', err);
    }
  };

  // Save conditions for a permission
  const saveConditions = async (code: string, conditionsToSave: PermissionCondition[]) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/permissions/${code}/conditions/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ conditions: conditionsToSave }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to save conditions: ${response.statusText}`);
      }

      const data = await response.json();
      setConditions(data.conditions);

      toast({
        title: "Success",
        description: "Permission conditions saved successfully",
      });

      return data.conditions;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save conditions';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Create a new condition
  const createCondition = async (condition: Omit<PermissionCondition, 'id'>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/permission-conditions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(condition),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create condition: ${response.statusText}`);
      }

      const newCondition = await response.json();
      setConditions(prev => [...prev, newCondition]);

      toast({
        title: "Success",
        description: "Permission condition created successfully",
      });

      return newCondition;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create condition';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update a condition
  const updateCondition = async (id: number, updates: Partial<PermissionCondition>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/permission-conditions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to update condition: ${response.statusText}`);
      }

      const updatedCondition = await response.json();
      setConditions(prev =>
        prev.map(condition =>
          condition.id === id ? updatedCondition : condition
        )
      );

      toast({
        title: "Success",
        description: "Permission condition updated successfully",
      });

      return updatedCondition;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update condition';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a condition
  const deleteCondition = async (id: number) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/permission-conditions/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete condition: ${response.statusText}`);
      }

      setConditions(prev => prev.filter(condition => condition.id !== id));

      toast({
        title: "Success",
        description: "Permission condition deleted successfully",
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete condition';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get condition type info
  const getConditionType = (type: string) => {
    return conditionTypes.find(ct => ct.type === type);
  };

  // Validate condition configuration
  const validateCondition = (condition: PermissionCondition): string[] => {
    const errors: string[] = [];

    if (!condition.condition_type) {
      errors.push('Condition type is required');
    }

    if (!condition.description?.trim()) {
      errors.push('Description is required');
    }

    if (condition.priority < 0 || condition.priority > 100) {
      errors.push('Priority must be between 0 and 100');
    }

    if (!condition.condition_config || typeof condition.condition_config !== 'object') {
      errors.push('Condition configuration is required');
    }

    // Type-specific validation
    const conditionType = getConditionType(condition.condition_type);
    if (conditionType && condition.condition_config) {
      // Add specific validation based on condition type schema
      // This could be expanded with more detailed validation
    }

    return errors;
  };

  // Load initial data
  useEffect(() => {
    loadConditionTypes();

    if (permissionCode) {
      loadConditions(permissionCode);
    }
  }, [permissionCode]);

  return {
    conditions,
    conditionTypes,
    loading,
    error,
    loadConditions,
    saveConditions,
    createCondition,
    updateCondition,
    deleteCondition,
    getConditionType,
    validateCondition,
    setConditions,
  };
}
