chunk-RPCDYKBN.js?v=bde597cc:521 Warning: React does not recognize the `indicatorClassName` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `indicatorclassname` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
    at div
    at https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…unner/workspace/node_modules/.vite/deps/chunk-AXMZZQ2X.js?v=bde597cc:42:13
    at Provider (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…unner/workspace/node_modules/.vite/deps/chunk-OXZDJRWN.js?v=bde597cc:38:15)
    at https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…kspace/node_modules/.vite/deps/@radix-ui_react-progress.js?v=bde597cc:30:7
    at _c (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/progress.tsx:22:11)
    at td
    at _c13 (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/table.tsx:152:13)
    at tr
    at _c9 (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/table.tsx:102:12)
    at tbody
    at _c5 (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/table.tsx:55:12)
    at table
    at div
    at _c (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/table.tsx:20:11)
    at div
    at div
    at _c9 (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/card.tsx:114:12)
    at div
    at _c (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/card.tsx:20:11)
    at TopAgents (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/dashboard/TopAgents.tsx:38:3)
    at div
    at div
    at Dashboard (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/pages/dashboard.tsx:38:44)
    at div
    at div
    at main
    at div
    at div
    at AppLayout (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/layout/AppLayout.tsx:36:3)
    at Route (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=bde597cc:323:16)
    at Switch (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=bde597cc:379:17)
    at Provider (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…unner/workspace/node_modules/.vite/deps/chunk-OXZDJRWN.js?v=bde597cc:38:15)
    at TooltipProvider (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…rkspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=bde597cc:62:5)
    at App
    at QueryClientProvider (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=bde597cc:2805:3)
    at ThemeProvider (https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/src/components/ui/theme-provider.tsx:25:3)
printWarning	@	chunk-RPCDYKBN.js?v=bde597cc:521