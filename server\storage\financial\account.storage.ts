import { db } from '../../db';
import { eq, and, desc } from 'drizzle-orm';
import { accounts } from '@shared/schema';
import { Account, InsertAccount } from '@shared/schema';
import errorLogger from '../../utils/errorLogger';
import { IAccountStorage } from './interfaces';

export class AccountStorage implements IAccountStorage {
  async getAccount(id: number): Promise<Account | undefined> {
    try {
      const [account] = await db.select()
        .from(accounts)
        .where(eq(accounts.id, id));
      return account;
    } catch (error) {
      errorLogger.logError(`Error fetching account id=${id}`, 'account-fetch', error as Error);
      return undefined;
    }
  }

  async getAccountsByCompany(companyId: number): Promise<Account[]> {
    try {
      const companyAccounts = await db.select()
        .from(accounts)
        .where(eq(accounts.company_id, companyId))
        .orderBy(desc(accounts.created_at));
      return companyAccounts;
    } catch (error) {
      errorLogger.logError(`Error fetching accounts for company id=${companyId}`, 'account-fetch', error as Error);
      return [];
    }
  }

  async createAccount(accountData: InsertAccount): Promise<Account> {
    try {
      const [account] = await db.insert(accounts)
        .values(accountData)
        .returning();

      return account;
    } catch (error) {
      errorLogger.logError(`Error creating account`, 'account-create', error as Error);
      throw error;
    }
  }

  async updateAccount(id: number, companyId: number, accountData: Partial<InsertAccount>): Promise<Account> {
    try {
      // First check if the account exists and belongs to the company
      const [existingAccount] = await db.select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.company_id, companyId)
          )
        );

      if (!existingAccount) {
        throw new Error(`Account with id=${id} not found for company id=${companyId}`);
      }

      const [updatedAccount] = await db.update(accounts)
        .set(accountData)
        .where(eq(accounts.id, id))
        .returning();

      return updatedAccount;
    } catch (error) {
      errorLogger.logError(`Error updating account id=${id}`, 'account-update', error as Error);
      throw error;
    }
  }

  async deleteAccount(id: number, companyId: number): Promise<boolean> {
    try {
      // First check if the account exists and belongs to the company
      const [existingAccount] = await db.select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.company_id, companyId)
          )
        );

      if (!existingAccount) {
        return false;
      }

      // Check if the account has any transactions
      // This would require importing the transactions table and checking for references
      // For now, we'll assume it's safe to delete

      await db.delete(accounts)
        .where(eq(accounts.id, id));

      return true;
    } catch (error) {
      errorLogger.logError(`Error deleting account id=${id}`, 'account-delete', error as Error);
      return false;
    }
  }

  async getAccountBalance(id: number, companyId: number): Promise<string> {
    try {
      // First check if the account exists and belongs to the company
      const [existingAccount] = await db.select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.company_id, companyId)
          )
        );

      if (!existingAccount) {
        throw new Error(`Account with id=${id} not found for company id=${companyId}`);
      }

      // In a real implementation, we would calculate the balance based on transactions
      // For now, we'll just return the current balance from the account record
      return existingAccount.current_balance;
    } catch (error) {
      errorLogger.logError(`Error fetching balance for account id=${id}`, 'account-balance', error as Error);
      throw error;
    }
  }

  async updateAccountBalance(id: number, companyId: number, amount: string, isCredit: boolean): Promise<Account> {
    try {
      // First check if the account exists and belongs to the company
      const [existingAccount] = await db.select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.company_id, companyId)
          )
        );

      if (!existingAccount) {
        throw new Error(`Account with id=${id} not found for company id=${companyId}`);
      }

      // Calculate the new balance
      const currentBalance = parseFloat(existingAccount.current_balance);
      const transactionAmount = parseFloat(amount);
      
      let newBalance: number;
      if (isCredit) {
        newBalance = currentBalance + transactionAmount;
      } else {
        newBalance = currentBalance - transactionAmount;
      }

      // Update the account balance
      const [updatedAccount] = await db.update(accounts)
        .set({ current_balance: newBalance.toString() })
        .where(eq(accounts.id, id))
        .returning();

      return updatedAccount;
    } catch (error) {
      errorLogger.logError(`Error updating balance for account id=${id}`, 'account-balance-update', error as Error);
      throw error;
    }
  }
}
