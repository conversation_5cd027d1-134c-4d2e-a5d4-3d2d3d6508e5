-- Migration: Field-Level Security Schema
-- Task 3.2.1: Create field security database schema
-- Date: 2025-01-24

-- Create field access type enum
CREATE TYPE field_access_type AS ENUM (
  'read',
  'write',
  'none',
  'masked'
);

-- Create field sensitivity level enum
CREATE TYPE field_sensitivity_level AS ENUM (
  'public',
  'internal',
  'confidential',
  'restricted',
  'top_secret'
);

-- Create sensitive field definitions table
CREATE TABLE sensitive_field_definitions (
  id SERIAL PRIMARY KEY,
  table_name TEXT NOT NULL,
  field_name TEXT NOT NULL,
  sensitivity_level field_sensitivity_level NOT NULL,
  default_access_type field_access_type DEFAULT 'read' NOT NULL,
  masking_pattern TEXT, -- Pattern for masking (e.g., '***-**-####' for SSN)
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  UNIQUE (table_name, field_name)
);

-- Create field security rules table
CREATE TABLE field_security_rules (
  id SERIAL PRIMARY KEY,
  role_id INTEGER NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
  sensitive_field_id INTEGER NOT NULL REFERENCES sensitive_field_definitions(id) ON DELETE CASCADE,
  access_type field_access_type NOT NULL,
  condition_config JSONB, -- JSON conditions for conditional access
  override_masking_pattern TEXT, -- Override default masking pattern
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  priority INTEGER DEFAULT 0 NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  UNIQUE (role_id, sensitive_field_id)
);

-- Create indexes for performance
CREATE INDEX idx_sensitive_field_definitions_table_name ON sensitive_field_definitions(table_name);
CREATE INDEX idx_sensitive_field_definitions_sensitivity_level ON sensitive_field_definitions(sensitivity_level);
CREATE INDEX idx_sensitive_field_definitions_is_active ON sensitive_field_definitions(is_active);

CREATE INDEX idx_field_security_rules_role_id ON field_security_rules(role_id);
CREATE INDEX idx_field_security_rules_sensitive_field_id ON field_security_rules(sensitive_field_id);
CREATE INDEX idx_field_security_rules_access_type ON field_security_rules(access_type);
CREATE INDEX idx_field_security_rules_is_active ON field_security_rules(is_active);
CREATE INDEX idx_field_security_rules_priority ON field_security_rules(priority);

-- Insert sensitive field definitions for common sensitive data
INSERT INTO sensitive_field_definitions (table_name, field_name, sensitivity_level, default_access_type, masking_pattern, description) VALUES
-- Customer sensitive fields
('customers', 'email', 'internal', 'read', '***@***.***', 'Customer email address'),
('customers', 'phone', 'internal', 'read', '***-***-####', 'Customer phone number'),
('customers', 'address', 'confidential', 'read', '*** *** ***', 'Customer home address'),
('customers', 'credit_score', 'confidential', 'none', '***', 'Customer credit score'),
('customers', 'notes', 'internal', 'read', NULL, 'Customer notes and comments'),

-- User sensitive fields
('users', 'email', 'internal', 'read', '***@***.***', 'User email address'),
('users', 'phone', 'internal', 'read', '***-***-####', 'User phone number'),
('users', 'password', 'top_secret', 'none', '***', 'User password hash'),

-- Loan sensitive fields
('loans', 'amount', 'confidential', 'read', '$***,***', 'Loan amount'),
('loans', 'interest_rate', 'confidential', 'read', '**%', 'Loan interest rate'),
('loans', 'notes', 'internal', 'read', NULL, 'Loan notes and comments'),
('loans', 'guarantor_info', 'confidential', 'read', '*** *** ***', 'Guarantor information'),

-- Payment sensitive fields
('payments', 'amount', 'confidential', 'read', '$***,***', 'Payment amount'),
('payments', 'payment_method', 'internal', 'read', '***', 'Payment method details'),
('payments', 'reference_number', 'internal', 'read', '***-***-***', 'Payment reference number'),

-- Collection sensitive fields
('collections', 'amount', 'confidential', 'read', '$***,***', 'Collection amount'),
('collections', 'notes', 'internal', 'read', NULL, 'Collection notes'),

-- Agent sensitive fields
('agents', 'commission_rate', 'confidential', 'none', '**%', 'Agent commission rate'),
('agents', 'phone', 'internal', 'read', '***-***-####', 'Agent phone number'),

-- Financial management sensitive fields
('transactions', 'amount', 'confidential', 'read', '$***,***', 'Transaction amount'),
('transactions', 'description', 'internal', 'read', NULL, 'Transaction description'),
('account_balances', 'balance', 'confidential', 'read', '$***,***', 'Account balance'),
('shareholders', 'investment_amount', 'restricted', 'none', '$***,***', 'Shareholder investment amount'),
('profit_distributions', 'amount', 'restricted', 'none', '$***,***', 'Profit distribution amount');

-- Insert sample field security rules for different roles
INSERT INTO field_security_rules (role_id, sensitive_field_id, access_type, description) VALUES
-- Loan officers can read customer contact info but not credit scores
(1, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'email'), 'read', 'Loan officers can view customer emails'),
(1, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'phone'), 'read', 'Loan officers can view customer phone numbers'),
(1, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'credit_score'), 'none', 'Loan officers cannot view credit scores'),

-- Managers can read most customer data including credit scores
(2, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'email'), 'read', 'Managers can view customer emails'),
(2, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'phone'), 'read', 'Managers can view customer phone numbers'),
(2, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'address'), 'read', 'Managers can view customer addresses'),
(2, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'credit_score'), 'read', 'Managers can view credit scores'),

-- Senior managers have full access to loan and payment data
(3, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'loans' AND field_name = 'amount'), 'read', 'Senior managers can view loan amounts'),
(3, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'loans' AND field_name = 'interest_rate'), 'read', 'Senior managers can view interest rates'),
(3, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'payments' AND field_name = 'amount'), 'read', 'Senior managers can view payment amounts'),

-- Executives have full access to financial data
(4, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'transactions' AND field_name = 'amount'), 'read', 'Executives can view transaction amounts'),
(4, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'account_balances' AND field_name = 'balance'), 'read', 'Executives can view account balances'),
(4, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'shareholders' AND field_name = 'investment_amount'), 'read', 'Executives can view shareholder investments'),

-- Collection agents can view masked customer contact info
(5, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'phone'), 'masked', 'Collection agents see masked phone numbers'),
(5, (SELECT id FROM sensitive_field_definitions WHERE table_name = 'customers' AND field_name = 'address'), 'masked', 'Collection agents see masked addresses');

-- Add comments for documentation
COMMENT ON TABLE sensitive_field_definitions IS 'Defines which fields are considered sensitive and their default access levels';
COMMENT ON TABLE field_security_rules IS 'Role-based rules for accessing sensitive fields';
COMMENT ON COLUMN sensitive_field_definitions.masking_pattern IS 'Pattern used to mask field values (e.g., ***-**-#### for phone numbers)';
COMMENT ON COLUMN field_security_rules.condition_config IS 'JSON configuration for conditional field access based on context';
COMMENT ON COLUMN field_security_rules.override_masking_pattern IS 'Custom masking pattern that overrides the default for this role';
