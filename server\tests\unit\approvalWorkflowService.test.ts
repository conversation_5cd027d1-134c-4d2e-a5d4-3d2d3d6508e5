import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ApprovalWorkflowService, WorkflowConfig } from '../../services/approvalWorkflowService';

// Mock the database
vi.mock('../../db', () => ({
  db: {
    insert: vi.fn(),
    select: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  }
}));

// Mock error logger
vi.mock('../../utils/errorLogger', () => ({
  default: {
    logInfo: vi.fn(),
    logError: vi.fn(),
  }
}));

describe('ApprovalWorkflowService', () => {
  let service: ApprovalWorkflowService;

  beforeEach(() => {
    service = new ApprovalWorkflowService();
    vi.clearAllMocks();
  });

  describe('Workflow Configuration Validation', () => {
    it('should validate workflow config structure', () => {
      const validConfig: WorkflowConfig = {
        name: 'Test Workflow',
        description: 'A test workflow',
        workflowType: 'permission_elevation',
        autoEscalationHours: 24,
        maxEscalationLevels: 3,
        steps: [
          {
            stepName: 'Manager Approval',
            stepType: 'sequential',
            requiredApprovers: 1,
            approverRoles: [1, 2],
            stepTimeoutHours: 24,
          },
          {
            stepName: 'Director Approval',
            stepType: 'sequential',
            requiredApprovers: 1,
            approverRoles: [3],
            stepTimeoutHours: 48,
          }
        ]
      };

      expect(validConfig.name).toBe('Test Workflow');
      expect(validConfig.steps).toHaveLength(2);
      expect(validConfig.steps[0].stepName).toBe('Manager Approval');
      expect(validConfig.steps[1].stepName).toBe('Director Approval');
    });

    it('should handle different step types', () => {
      const stepTypes = ['sequential', 'parallel', 'any_one', 'majority', 'unanimous'] as const;
      
      stepTypes.forEach(stepType => {
        const config: WorkflowConfig = {
          name: `Test ${stepType} Workflow`,
          workflowType: 'permission_elevation',
          steps: [
            {
              stepName: 'Test Step',
              stepType,
              requiredApprovers: stepType === 'majority' ? 2 : 1,
              approverRoles: [1, 2, 3],
            }
          ]
        };

        expect(config.steps[0].stepType).toBe(stepType);
      });
    });
  });

  describe('Workflow Types', () => {
    it('should support all workflow types', () => {
      const workflowTypes = [
        'permission_elevation',
        'loan_approval',
        'customer_data_access',
        'emergency_access',
        'role_assignment',
        'custom'
      ] as const;

      workflowTypes.forEach(type => {
        const config: WorkflowConfig = {
          name: `Test ${type} Workflow`,
          workflowType: type,
          steps: [
            {
              stepName: 'Test Step',
              stepType: 'sequential',
              approverRoles: [1],
            }
          ]
        };

        expect(config.workflowType).toBe(type);
      });
    });
  });

  describe('Step Configuration', () => {
    it('should handle approver configuration', () => {
      const stepWithRoles = {
        stepName: 'Role-based Step',
        stepType: 'sequential' as const,
        approverRoles: [1, 2, 3],
      };

      const stepWithUsers = {
        stepName: 'User-based Step',
        stepType: 'sequential' as const,
        approverUsers: [10, 20, 30],
      };

      const stepWithBoth = {
        stepName: 'Mixed Step',
        stepType: 'sequential' as const,
        approverRoles: [1, 2],
        approverUsers: [10, 20],
      };

      expect(stepWithRoles.approverRoles).toEqual([1, 2, 3]);
      expect(stepWithUsers.approverUsers).toEqual([10, 20, 30]);
      expect(stepWithBoth.approverRoles).toEqual([1, 2]);
      expect(stepWithBoth.approverUsers).toEqual([10, 20]);
    });

    it('should handle escalation configuration', () => {
      const stepWithEscalation = {
        stepName: 'Escalation Step',
        stepType: 'sequential' as const,
        approverRoles: [1],
        escalationRoles: [2, 3],
        stepTimeoutHours: 12,
      };

      expect(stepWithEscalation.escalationRoles).toEqual([2, 3]);
      expect(stepWithEscalation.stepTimeoutHours).toBe(12);
    });

    it('should handle optional steps', () => {
      const optionalStep = {
        stepName: 'Optional Step',
        stepType: 'sequential' as const,
        approverRoles: [1],
        isOptional: true,
      };

      const requiredStep = {
        stepName: 'Required Step',
        stepType: 'sequential' as const,
        approverRoles: [1],
        isOptional: false,
      };

      expect(optionalStep.isOptional).toBe(true);
      expect(requiredStep.isOptional).toBe(false);
    });
  });

  describe('Approval Logic', () => {
    it('should handle majority approval requirements', () => {
      const majorityStep = {
        stepName: 'Majority Step',
        stepType: 'majority' as const,
        requiredApprovers: 3,
        approverRoles: [1, 2, 3, 4, 5], // 5 approvers, need 3
      };

      expect(majorityStep.stepType).toBe('majority');
      expect(majorityStep.requiredApprovers).toBe(3);
      expect(majorityStep.approverRoles?.length).toBe(5);
    });

    it('should handle unanimous approval requirements', () => {
      const unanimousStep = {
        stepName: 'Unanimous Step',
        stepType: 'unanimous' as const,
        requiredApprovers: 3,
        approverRoles: [1, 2, 3],
      };

      expect(unanimousStep.stepType).toBe('unanimous');
      expect(unanimousStep.requiredApprovers).toBe(3);
    });

    it('should handle any-one approval requirements', () => {
      const anyOneStep = {
        stepName: 'Any One Step',
        stepType: 'any_one' as const,
        requiredApprovers: 1,
        approverRoles: [1, 2, 3, 4], // Any one of these can approve
      };

      expect(anyOneStep.stepType).toBe('any_one');
      expect(anyOneStep.requiredApprovers).toBe(1);
    });
  });

  describe('Trigger Conditions', () => {
    it('should handle priority-based triggers', () => {
      const config: WorkflowConfig = {
        name: 'Priority Workflow',
        workflowType: 'permission_elevation',
        triggerConditions: {
          priority: ['high', 'emergency'],
          permission_types: ['loan_approve'],
        },
        steps: [
          {
            stepName: 'High Priority Step',
            stepType: 'sequential',
            approverRoles: [1],
          }
        ]
      };

      expect(config.triggerConditions?.priority).toEqual(['high', 'emergency']);
      expect(config.triggerConditions?.permission_types).toEqual(['loan_approve']);
    });

    it('should handle amount-based triggers', () => {
      const config: WorkflowConfig = {
        name: 'Amount-based Workflow',
        workflowType: 'loan_approval',
        triggerConditions: {
          amount_tiers: [
            { min: 0, max: 50000, steps: 1 },
            { min: 50001, max: 200000, steps: 2 },
            { min: 200001, max: null, steps: 3 }
          ]
        },
        steps: [
          {
            stepName: 'Loan Officer',
            stepType: 'sequential',
            approverRoles: [1],
          },
          {
            stepName: 'Manager',
            stepType: 'sequential',
            approverRoles: [2],
          },
          {
            stepName: 'Director',
            stepType: 'sequential',
            approverRoles: [3],
          }
        ]
      };

      expect(config.triggerConditions?.amount_tiers).toHaveLength(3);
      expect(config.steps).toHaveLength(3);
    });
  });

  describe('Escalation Rules', () => {
    it('should handle timeout escalation', () => {
      const escalationRule = {
        ruleName: 'Timeout Escalation',
        triggerCondition: 'timeout',
        triggerValue: { hours: 24 },
        escalationAction: 'notify',
        escalationTarget: { roles: [2, 3], users: [] }
      };

      expect(escalationRule.triggerCondition).toBe('timeout');
      expect(escalationRule.triggerValue.hours).toBe(24);
      expect(escalationRule.escalationAction).toBe('notify');
    });

    it('should handle priority escalation', () => {
      const escalationRule = {
        ruleName: 'Emergency Escalation',
        triggerCondition: 'priority',
        triggerValue: { priority: 'emergency', hours: 2 },
        escalationAction: 'reassign',
        escalationTarget: { roles: [3], users: [1] }
      };

      expect(escalationRule.triggerCondition).toBe('priority');
      expect(escalationRule.triggerValue.priority).toBe('emergency');
      expect(escalationRule.escalationAction).toBe('reassign');
    });
  });

  describe('Workflow Instance Management', () => {
    it('should create workflow trigger context', () => {
      const context = {
        requestType: 'permission_elevation',
        requestId: 'REQ-123',
        requesterId: 1,
        companyId: 1,
        requestData: {
          userId: 2,
          permissionId: 5,
          reason: 'Need access for urgent task',
        },
        priority: 'high' as const,
        metadata: {
          originalRequestType: 'permission_elevation',
        }
      };

      expect(context.requestType).toBe('permission_elevation');
      expect(context.requestId).toBe('REQ-123');
      expect(context.priority).toBe('high');
      expect(context.requestData.userId).toBe(2);
    });
  });
});
