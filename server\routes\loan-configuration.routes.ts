import { Express, Response } from 'express';
import { authMiddleware, requireCompanyAccess, requireRole, AuthRequest } from '../middleware/auth';
import { storage } from '../storage';
import { insertLoanConfigurationSchema } from '../../shared/schema';
import { ZodError } from 'zod';
import errorLogger from '../utils/errorLogger';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

export function registerLoanConfigurationRoutes(app: Express): void {
  // Get all loan configurations for a company
  app.get('/api/companies/:companyId/loan-configurations', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      console.log(`Fetching loan configurations for company ${companyId}`);
      
      const configurations = await storage.getLoanConfigurationsByCompany(companyId);
      console.log(`Found ${configurations.length} loan configurations for company ${companyId}`);
      
      return res.json(configurations);
    } catch (error) {
      console.error('Error fetching loan configurations:', error);
      errorLogger.logError(
        'Failed to fetch loan configurations',
        'loan-configurations-route',
        { error, companyId: req.params.companyId }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch loan configurations')
      );
    }
  });

  // Get active loan configurations (this is the endpoint that was failing)
  app.get('/api/companies/:companyId/loan-configurations/active', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      console.log(`Fetching active loan configurations for company ${companyId}`);
      errorLogger.logInfo(
        `Fetching active loan configurations for company ${companyId}`,
        'loan-configurations-route'
      );

      const configurations = await storage.getActiveLoanConfigurations(companyId);
      console.log(`Found ${configurations.length} active loan configurations for company ${companyId}`);
      
      return res.json(configurations);
    } catch (error) {
      console.error('Error fetching active loan configurations:', error);
      errorLogger.logError(
        'Failed to fetch active loan configurations',
        'active-loan-configurations-route',
        { error, companyId: req.params.companyId }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch active loan configurations')
      );
    }
  });

  // Get a specific loan configuration
  app.get('/api/companies/:companyId/loan-configurations/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      console.log(`Fetching loan configuration ${id} for company ${companyId}`);
      const configuration = await storage.getLoanConfiguration(id);

      if (!configuration || configuration.company_id !== companyId) {
        console.log(`Loan configuration ${id} not found or doesn't belong to company ${companyId}`);
        errorLogger.logWarning(
          `Loan configuration not found`,
          'get-loan-configuration-route',
          { id, companyId }
        );
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      return res.json(configuration);
    } catch (error) {
      console.error('Error fetching loan configuration:', error);
      errorLogger.logError(
        'Failed to fetch loan configuration',
        'get-loan-configuration-route',
        { error, id: req.params.id, companyId: req.params.companyId }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch loan configuration')
      );
    }
  });

  // Create a new loan configuration
  app.post('/api/companies/:companyId/loan-configurations', authMiddleware, requireCompanyAccess, requireRole(['owner', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      console.log(`Creating loan configuration for company ${companyId}:`, req.body);

      // Validate the request body
      const result = insertLoanConfigurationSchema.safeParse({
        ...req.body,
        company_id: companyId
      });

      if (!result.success) {
        console.log('Validation failed for loan configuration:', formatZodError(result.error));
        return res.status(400).json({ message: 'Invalid loan configuration data', errors: formatZodError(result.error) });
      }

      // Verify the template exists and belongs to the company
      const template = await storage.getFormTemplate(result.data.template_id);

      if (!template || template.company_id !== companyId) {
        console.log(`Form template ${result.data.template_id} not found or doesn't belong to company ${companyId}`);
        return res.status(400).json({ message: 'Form template not found or does not belong to this company' });
      }

      // Create the loan configuration
      const configuration = await storage.createLoanConfiguration(result.data);
      console.log(`Created loan configuration ${configuration.id} for company ${companyId}`);
      
      return res.status(201).json(configuration);
    } catch (error) {
      console.error('Error creating loan configuration:', error);
      return res.status(500).json({ message: 'Failed to create loan configuration' });
    }
  });

  // Update a loan configuration
  app.put('/api/companies/:companyId/loan-configurations/:id', authMiddleware, requireCompanyAccess, requireRole(['owner', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      console.log(`Updating loan configuration ${id} for company ${companyId}:`, req.body);

      // Make sure the configuration exists and belongs to the company
      const existingConfig = await storage.getLoanConfiguration(id);

      if (!existingConfig || existingConfig.company_id !== companyId) {
        console.log(`Loan configuration ${id} not found or doesn't belong to company ${companyId}`);
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      // Validate the request body
      const result = insertLoanConfigurationSchema.partial().safeParse({
        ...req.body,
        company_id: companyId
      });

      if (!result.success) {
        console.log('Validation failed for loan configuration update:', formatZodError(result.error));
        return res.status(400).json({ message: 'Invalid loan configuration data', errors: formatZodError(result.error) });
      }

      // If template_id is being updated, verify the new template exists and belongs to the company
      if (result.data.template_id) {
        const template = await storage.getFormTemplate(result.data.template_id);

        if (!template || template.company_id !== companyId) {
          console.log(`Form template ${result.data.template_id} not found or doesn't belong to company ${companyId}`);
          return res.status(400).json({ message: 'Form template not found or does not belong to this company' });
        }
      }

      // Update the loan configuration
      const updatedConfig = await storage.updateLoanConfiguration(id, result.data);
      console.log(`Updated loan configuration ${id} for company ${companyId}`);

      return res.json(updatedConfig);
    } catch (error) {
      console.error('Error updating loan configuration:', error);
      return res.status(500).json({ message: 'Failed to update loan configuration' });
    }
  });

  // Delete a loan configuration
  app.delete('/api/companies/:companyId/loan-configurations/:id', authMiddleware, requireCompanyAccess, requireRole(['owner', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      console.log(`Deleting loan configuration ${id} for company ${companyId}`);

      // Make sure the configuration exists and belongs to the company
      const existingConfig = await storage.getLoanConfiguration(id);

      if (!existingConfig || existingConfig.company_id !== companyId) {
        console.log(`Loan configuration ${id} not found or doesn't belong to company ${companyId}`);
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      // TODO: Check if the configuration is in use
      // const isInUse = await storage.isLoanConfigurationInUse(id, companyId);
      // For now, we'll skip this check and allow deletion
      // This should be implemented later for data integrity

      // Delete the loan configuration
      await storage.deleteLoanConfiguration(id);
      console.log(`Successfully deleted loan configuration ${id} for company ${companyId}`);

      console.log(`Successfully deleted loan configuration ${id} for company ${companyId}`);
      return res.json({ message: 'Loan configuration deleted successfully' });
    } catch (error) {
      console.error('Error deleting loan configuration:', error);
      return res.status(500).json({ message: 'Failed to delete loan configuration' });
    }
  });

  // Toggle a loan configuration's active status
  app.patch('/api/companies/:companyId/loan-configurations/:id/toggle-active', authMiddleware, requireCompanyAccess, requireRole(['owner', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      console.log(`Toggling active status for loan configuration ${id} in company ${companyId}`);

      // Make sure the configuration exists and belongs to the company
      const existingConfig = await storage.getLoanConfiguration(id);

      if (!existingConfig || existingConfig.company_id !== companyId) {
        console.log(`Loan configuration ${id} not found or doesn't belong to company ${companyId}`);
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      // Toggle the active status
      const updatedConfig = await storage.updateLoanConfiguration(id, {
        is_active: !existingConfig.is_active
      });

      console.log(`Toggled loan configuration ${id} active status to ${updatedConfig.is_active}`);
      return res.json(updatedConfig);
    } catch (error) {
      console.error('Error toggling loan configuration active status:', error);
      return res.status(500).json({ message: 'Failed to toggle loan configuration active status' });
    }
  });
}
