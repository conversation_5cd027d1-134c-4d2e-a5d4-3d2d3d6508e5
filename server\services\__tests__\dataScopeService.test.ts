import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { DataScopeService, DataScopeContext } from '../dataScopeService';

// Mock the database and dependencies
jest.mock('../../db');
jest.mock('../../utils/errorLogger');

describe('DataScopeService', () => {
  let dataScopeService: DataScopeService;

  beforeEach(() => {
    dataScopeService = new DataScopeService();
    jest.clearAllMocks();
  });

  describe('checkDataAccess', () => {
    it('should grant access for branch-based scope when user is in allowed branch', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'customers',
        accessLevel: 'read'
      };

      // Mock the service methods
      jest.spyOn(dataScopeService as any, 'getUserOrganizationalInfo').mockResolvedValue({
        userId: 1,
        companyId: 1,
        branchId: 1,
        departmentId: 1,
        managerId: null,
        groupIds: [],
        roleIds: [1]
      });

      jest.spyOn(dataScopeService as any, 'getUserDataScopeRules').mockResolvedValue([
        {
          id: 1,
          role_id: 1,
          scope_type: 'branch',
          scope_config: { branch_ids: [1] },
          resource_type: 'customers',
          access_level: 'read',
          is_active: true,
          priority: 0
        }
      ]);

      const result = await dataScopeService.checkDataAccess(context);

      expect(result.hasAccess).toBe(true);
      expect(result.reason).toBe('Access granted by scope rules');
    });

    it('should deny access for branch-based scope when user is not in allowed branch', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'customers',
        accessLevel: 'read'
      };

      jest.spyOn(dataScopeService as any, 'getUserOrganizationalInfo').mockResolvedValue({
        userId: 1,
        companyId: 1,
        branchId: 2, // User is in branch 2
        departmentId: 1,
        managerId: null,
        groupIds: [],
        roleIds: [1]
      });

      jest.spyOn(dataScopeService as any, 'getUserDataScopeRules').mockResolvedValue([
        {
          id: 1,
          role_id: 1,
          scope_type: 'branch',
          scope_config: { branch_ids: [1] }, // Rule allows only branch 1
          resource_type: 'customers',
          access_level: 'read',
          is_active: true,
          priority: 0
        }
      ]);

      const result = await dataScopeService.checkDataAccess(context);

      expect(result.hasAccess).toBe(false);
      expect(result.reason).toBe('Access denied by all scope rules');
    });

    it('should grant access for department-based scope when user is in allowed department', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'loans',
        accessLevel: 'write'
      };

      jest.spyOn(dataScopeService as any, 'getUserOrganizationalInfo').mockResolvedValue({
        userId: 1,
        companyId: 1,
        branchId: 1,
        departmentId: 2,
        managerId: null,
        groupIds: [],
        roleIds: [2]
      });

      jest.spyOn(dataScopeService as any, 'getUserDataScopeRules').mockResolvedValue([
        {
          id: 2,
          role_id: 2,
          scope_type: 'department',
          scope_config: { department_ids: [2, 3] },
          resource_type: 'loans',
          access_level: 'write',
          is_active: true,
          priority: 0
        }
      ]);

      const result = await dataScopeService.checkDataAccess(context);

      expect(result.hasAccess).toBe(true);
      expect(result.reason).toBe('Access granted by scope rules');
    });

    it('should grant access for hierarchy-based scope when user has subordinates', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'customers',
        accessLevel: 'read'
      };

      jest.spyOn(dataScopeService as any, 'getUserOrganizationalInfo').mockResolvedValue({
        userId: 1,
        companyId: 1,
        branchId: 1,
        departmentId: 1,
        managerId: null,
        groupIds: [],
        roleIds: [3]
      });

      jest.spyOn(dataScopeService as any, 'getUserDataScopeRules').mockResolvedValue([
        {
          id: 3,
          role_id: 3,
          scope_type: 'hierarchy',
          scope_config: { include_subordinates: true, max_depth: 2 },
          resource_type: 'customers',
          access_level: 'read',
          is_active: true,
          priority: 0
        }
      ]);

      jest.spyOn(dataScopeService as any, 'getSubordinateUsers').mockResolvedValue([2, 3, 4]);

      const result = await dataScopeService.checkDataAccess(context);

      expect(result.hasAccess).toBe(true);
      expect(result.reason).toBe('Access granted by scope rules');
    });

    it('should grant access for company-wide scope', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'reports',
        accessLevel: 'admin'
      };

      jest.spyOn(dataScopeService as any, 'getUserOrganizationalInfo').mockResolvedValue({
        userId: 1,
        companyId: 1,
        branchId: 1,
        departmentId: 1,
        managerId: null,
        groupIds: [],
        roleIds: [4]
      });

      jest.spyOn(dataScopeService as any, 'getUserDataScopeRules').mockResolvedValue([
        {
          id: 4,
          role_id: 4,
          scope_type: 'company',
          scope_config: { all_branches: true, all_departments: true },
          resource_type: 'reports',
          access_level: 'admin',
          is_active: true,
          priority: 0
        }
      ]);

      const result = await dataScopeService.checkDataAccess(context);

      expect(result.hasAccess).toBe(true);
      expect(result.reason).toBe('Access granted by scope rules');
    });
  });

  describe('getAccessibleEntityIds', () => {
    it('should return empty array when no access is granted', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'customers',
        accessLevel: 'read'
      };

      jest.spyOn(dataScopeService as any, 'getUserOrganizationalInfo').mockResolvedValue({
        userId: 1,
        companyId: 1,
        branchId: 2,
        departmentId: 1,
        managerId: null,
        groupIds: [],
        roleIds: [1]
      });

      jest.spyOn(dataScopeService as any, 'getUserDataScopeRules').mockResolvedValue([]);
      jest.spyOn(dataScopeService as any, 'hasCompanyWideAccess').mockResolvedValue(false);

      const result = await dataScopeService.getAccessibleEntityIds(context);

      expect(result).toEqual([]);
    });

    it('should return all entity IDs when company-wide access is granted', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'customers',
        accessLevel: 'read'
      };

      jest.spyOn(dataScopeService as any, 'getUserOrganizationalInfo').mockResolvedValue({
        userId: 1,
        companyId: 1,
        branchId: 1,
        departmentId: 1,
        managerId: null,
        groupIds: [],
        roleIds: [4]
      });

      jest.spyOn(dataScopeService as any, 'getUserDataScopeRules').mockResolvedValue([]);
      jest.spyOn(dataScopeService as any, 'hasCompanyWideAccess').mockResolvedValue(true);
      jest.spyOn(dataScopeService as any, 'getAllEntityIds').mockResolvedValue([1, 2, 3, 4, 5]);

      const result = await dataScopeService.getAccessibleEntityIds(context);

      expect(result).toEqual([1, 2, 3, 4, 5]);
    });
  });

  describe('filterQueryByScope', () => {
    it('should apply scope filters to database query', async () => {
      const context: DataScopeContext = {
        userId: 1,
        companyId: 1,
        resourceType: 'customers',
        accessLevel: 'read'
      };

      const mockQuery = {
        where: jest.fn().mockReturnThis()
      };

      jest.spyOn(dataScopeService, 'getAccessibleEntityIds').mockResolvedValue([1, 2, 3]);

      await dataScopeService.filterQueryByScope(mockQuery, context);

      expect(mockQuery.where).toHaveBeenCalled();
    });
  });
});
