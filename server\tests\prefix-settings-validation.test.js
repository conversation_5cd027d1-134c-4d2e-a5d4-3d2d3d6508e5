/**
 * Test script to verify the company_prefix_settings validation middleware
 * 
 * This script tests the validation that prevents creating loans, customers, partners, and agents
 * when company_prefix_settings are not configured for a company.
 */

const axios = require('axios');
const baseUrl = 'http://localhost:8080/api';

// Test data
const testCompanyId = 1; // Replace with a valid company ID from your database
const token = ''; // Replace with a valid JWT token

// Test creating a customer without prefix settings
async function testCustomerCreation() {
  try {
    const response = await axios.post(`${baseUrl}/customers`, {
      company_id: testCompanyId,
      full_name: 'Test Customer',
      phone: '+911234567890',
      email: '<EMAIL>',
      address: 'Test Address'
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('Customer creation response:', response.data);
    return response;
  } catch (error) {
    console.error('Customer creation error:', error.response?.data || error.message);
    return error.response;
  }
}

// Test creating a loan without prefix settings
async function testLoanCreation() {
  try {
    const response = await axios.post(`${baseUrl}/loans`, {
      company_id: testCompanyId,
      customer_id: 1, // Replace with a valid customer ID
      amount: '10000',
      interest_rate: '10',
      term: 12,
      start_date: new Date().toISOString(),
      status: 'pending'
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('Loan creation response:', response.data);
    return response;
  } catch (error) {
    console.error('Loan creation error:', error.response?.data || error.message);
    return error.response;
  }
}

// Test creating a partner without prefix settings
async function testPartnerCreation() {
  try {
    const response = await axios.post(`${baseUrl}/partners`, {
      company_id: testCompanyId,
      name: 'Test Partner',
      email: '<EMAIL>',
      phone: '+911234567890',
      type: 'investor'
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('Partner creation response:', response.data);
    return response;
  } catch (error) {
    console.error('Partner creation error:', error.response?.data || error.message);
    return error.response;
  }
}

// Test creating an agent without prefix settings
async function testAgentCreation() {
  try {
    const response = await axios.post(`${baseUrl}/agents`, {
      company_id: testCompanyId,
      full_name: 'Test Agent',
      email: '<EMAIL>',
      phone: '+911234567890',
      commission_rate: 5
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('Agent creation response:', response.data);
    return response;
  } catch (error) {
    console.error('Agent creation error:', error.response?.data || error.message);
    return error.response;
  }
}

// Run all tests
async function runTests() {
  console.log('Testing customer creation without prefix settings...');
  await testCustomerCreation();
  
  console.log('\nTesting loan creation without prefix settings...');
  await testLoanCreation();
  
  console.log('\nTesting partner creation without prefix settings...');
  await testPartnerCreation();
  
  console.log('\nTesting agent creation without prefix settings...');
  await testAgentCreation();
}

// Run the tests
runTests().catch(console.error);
