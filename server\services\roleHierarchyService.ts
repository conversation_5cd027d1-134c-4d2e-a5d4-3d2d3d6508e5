import { db } from '../db';
import {
  roleHierarchy,
  roleTemplates,
  customRoles,
  rolePermissions,
  permissions,
  userRoles,
  groupRoles,
  groupUsers,
  type RoleHierarchy,
  type InsertRoleHierarchy,
  type RoleTemplate,
  type InsertRoleTemplate,
  type CustomRole,
  type InheritanceType,
} from '@shared/schema';
import { eq, and, inArray, or, sql, desc } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';

export interface RoleHierarchyNode {
  role: CustomRole;
  children: RoleHierarchyNode[];
  parents: RoleHierarchyNode[];
  inheritanceType?: InheritanceType;
  depth: number;
}

export interface EffectivePermissions {
  roleId: number;
  roleName: string;
  directPermissions: string[];
  inheritedPermissions: string[];
  deniedPermissions: string[];
  effectivePermissions: string[];
  inheritancePath: Array<{
    roleId: number;
    roleName: string;
    inheritanceType: InheritanceType;
  }>;
}

export interface RoleTemplateConfig {
  permissions: string[];
  conditions?: Record<string, {
    type: string;
    config: any;
  }>;
  description: string;
}

export class RoleHierarchyService {
  /**
   * Create a new role hierarchy relationship
   * @param parentRoleId Parent role ID
   * @param childRoleId Child role ID
   * @param inheritanceType Type of inheritance (inherit, override, deny)
   * @returns Promise<RoleHierarchy> Created hierarchy relationship
   */
  async createRoleHierarchy(
    parentRoleId: number,
    childRoleId: number,
    inheritanceType: InheritanceType = 'inherit'
  ): Promise<RoleHierarchy> {
    try {
      // Validate that both roles exist
      await this.validateRolesExist([parentRoleId, childRoleId]);

      // Check for circular dependencies
      if (await this.wouldCreateCircularDependency(parentRoleId, childRoleId)) {
        throw new Error('Cannot create circular role hierarchy');
      }

      // Check if relationship already exists
      const existing = await this.getHierarchyRelationship(parentRoleId, childRoleId);
      if (existing) {
        throw new Error('Role hierarchy relationship already exists');
      }

      const [hierarchy] = await db
        .insert(roleHierarchy)
        .values({
          parent_role_id: parentRoleId,
          child_role_id: childRoleId,
          inheritance_type: inheritanceType,
        })
        .returning();

      errorLogger.logInfo(
        `Created role hierarchy: ${parentRoleId} -> ${childRoleId} (${inheritanceType})`,
        'role-hierarchy-service'
      );

      return hierarchy;
    } catch (error) {
      errorLogger.logError(
        `Failed to create role hierarchy: ${parentRoleId} -> ${childRoleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Remove a role hierarchy relationship
   * @param parentRoleId Parent role ID
   * @param childRoleId Child role ID
   * @returns Promise<boolean> True if relationship was removed
   */
  async removeRoleHierarchy(parentRoleId: number, childRoleId: number): Promise<boolean> {
    try {
      const result = await db
        .delete(roleHierarchy)
        .where(
          and(
            eq(roleHierarchy.parent_role_id, parentRoleId),
            eq(roleHierarchy.child_role_id, childRoleId)
          )
        );

      errorLogger.logInfo(
        `Removed role hierarchy: ${parentRoleId} -> ${childRoleId}`,
        'role-hierarchy-service'
      );

      return result.rowCount > 0;
    } catch (error) {
      errorLogger.logError(
        `Failed to remove role hierarchy: ${parentRoleId} -> ${childRoleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Update inheritance type for an existing hierarchy relationship
   * @param parentRoleId Parent role ID
   * @param childRoleId Child role ID
   * @param inheritanceType New inheritance type
   * @returns Promise<RoleHierarchy | null> Updated hierarchy or null if not found
   */
  async updateInheritanceType(
    parentRoleId: number,
    childRoleId: number,
    inheritanceType: InheritanceType
  ): Promise<RoleHierarchy | null> {
    try {
      const [updated] = await db
        .update(roleHierarchy)
        .set({
          inheritance_type: inheritanceType,
          updated_at: new Date()
        })
        .where(
          and(
            eq(roleHierarchy.parent_role_id, parentRoleId),
            eq(roleHierarchy.child_role_id, childRoleId)
          )
        )
        .returning();

      if (updated) {
        errorLogger.logInfo(
          `Updated inheritance type: ${parentRoleId} -> ${childRoleId} to ${inheritanceType}`,
          'role-hierarchy-service'
        );
      }

      return updated || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to update inheritance type: ${parentRoleId} -> ${childRoleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get all parent roles for a given role
   * @param roleId Role ID
   * @returns Promise<RoleHierarchy[]> Array of parent relationships
   */
  async getParentRoles(roleId: number): Promise<RoleHierarchy[]> {
    try {
      return await db
        .select()
        .from(roleHierarchy)
        .where(eq(roleHierarchy.child_role_id, roleId));
    } catch (error) {
      errorLogger.logError(
        `Failed to get parent roles for role ${roleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get all child roles for a given role
   * @param roleId Role ID
   * @returns Promise<RoleHierarchy[]> Array of child relationships
   */
  async getChildRoles(roleId: number): Promise<RoleHierarchy[]> {
    try {
      return await db
        .select()
        .from(roleHierarchy)
        .where(eq(roleHierarchy.parent_role_id, roleId));
    } catch (error) {
      errorLogger.logError(
        `Failed to get child roles for role ${roleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get all role hierarchies, optionally filtered by company
   * @param companyId Optional company filter
   * @returns Promise<RoleHierarchy[]> Array of all role hierarchy relationships
   */
  async getAllRoleHierarchies(companyId?: number): Promise<RoleHierarchy[]> {
    try {
      if (companyId) {
        // Filter by company - need to join with roles to get company info
        return await db
          .select({
            id: roleHierarchy.id,
            parent_role_id: roleHierarchy.parent_role_id,
            child_role_id: roleHierarchy.child_role_id,
            inheritance_type: roleHierarchy.inheritance_type,
            created_at: roleHierarchy.created_at,
            updated_at: roleHierarchy.updated_at
          })
          .from(roleHierarchy)
          .innerJoin(customRoles, eq(roleHierarchy.parent_role_id, customRoles.id))
          .where(eq(customRoles.company_id, companyId));
      } else {
        // Return all hierarchies
        return await db.select().from(roleHierarchy);
      }
    } catch (error) {
      errorLogger.logError(
        `Failed to get all role hierarchies`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Check if creating a hierarchy would create a circular dependency
   * @param parentRoleId Proposed parent role ID
   * @param childRoleId Proposed child role ID
   * @returns Promise<boolean> True if it would create a circular dependency
   */
  async wouldCreateCircularDependency(parentRoleId: number, childRoleId: number): Promise<boolean> {
    try {
      // If parent and child are the same, it's circular
      if (parentRoleId === childRoleId) {
        return true;
      }

      // Check if the proposed parent is already a descendant of the proposed child
      const descendants = await this.getAllDescendants(childRoleId);
      return descendants.some(desc => desc.child_role_id === parentRoleId);
    } catch (error) {
      errorLogger.logError(
        `Failed to check circular dependency: ${parentRoleId} -> ${childRoleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get all descendants of a role (recursive)
   * @param roleId Role ID
   * @param visited Set of visited role IDs to prevent infinite loops
   * @returns Promise<RoleHierarchy[]> Array of all descendant relationships
   */
  async getAllDescendants(roleId: number, visited: Set<number> = new Set()): Promise<RoleHierarchy[]> {
    try {
      if (visited.has(roleId)) {
        return []; // Prevent infinite loops
      }

      visited.add(roleId);
      const directChildren = await this.getChildRoles(roleId);
      const allDescendants = [...directChildren];

      for (const child of directChildren) {
        const childDescendants = await this.getAllDescendants(child.child_role_id, visited);
        allDescendants.push(...childDescendants);
      }

      return allDescendants;
    } catch (error) {
      errorLogger.logError(
        `Failed to get descendants for role ${roleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get all ancestors of a role (recursive)
   * @param roleId Role ID
   * @param visited Set of visited role IDs to prevent infinite loops
   * @returns Promise<RoleHierarchy[]> Array of all ancestor relationships
   */
  async getAllAncestors(roleId: number, visited: Set<number> = new Set()): Promise<RoleHierarchy[]> {
    try {
      if (visited.has(roleId)) {
        return []; // Prevent infinite loops
      }

      visited.add(roleId);
      const directParents = await this.getParentRoles(roleId);
      const allAncestors = [...directParents];

      for (const parent of directParents) {
        const parentAncestors = await this.getAllAncestors(parent.parent_role_id, visited);
        allAncestors.push(...parentAncestors);
      }

      return allAncestors;
    } catch (error) {
      errorLogger.logError(
        `Failed to get ancestors for role ${roleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Calculate effective permissions for a role considering inheritance
   * @param roleId Role ID
   * @returns Promise<EffectivePermissions> Effective permissions with inheritance details
   */
  async calculateEffectivePermissions(roleId: number): Promise<EffectivePermissions> {
    try {
      // Get role information
      const [role] = await db
        .select()
        .from(customRoles)
        .where(eq(customRoles.id, roleId))
        .limit(1);

      if (!role) {
        throw new Error(`Role ${roleId} not found`);
      }

      // Get direct permissions for this role
      const directPermissions = await this.getRolePermissions(roleId);

      // Get all ancestors with their inheritance types
      const ancestors = await this.getAllAncestors(roleId);

      // Build inheritance path and calculate inherited permissions
      const inheritancePath: Array<{
        roleId: number;
        roleName: string;
        inheritanceType: InheritanceType;
      }> = [];

      let inheritedPermissions: string[] = [];
      let deniedPermissions: string[] = [];

      // Process ancestors in order (closest first)
      for (const ancestor of ancestors) {
        const [ancestorRole] = await db
          .select()
          .from(customRoles)
          .where(eq(customRoles.id, ancestor.parent_role_id))
          .limit(1);

        if (ancestorRole) {
          inheritancePath.push({
            roleId: ancestorRole.id,
            roleName: ancestorRole.name,
            inheritanceType: ancestor.inheritance_type,
          });

          const ancestorPermissions = await this.getRolePermissions(ancestorRole.id);

          switch (ancestor.inheritance_type) {
            case 'inherit':
              // Add parent permissions that aren't already included
              inheritedPermissions = [
                ...inheritedPermissions,
                ...ancestorPermissions.filter(p => !inheritedPermissions.includes(p))
              ];
              break;

            case 'override':
              // Replace all inherited permissions with parent's permissions
              inheritedPermissions = ancestorPermissions;
              break;

            case 'deny':
              // Remove parent permissions from inherited set
              deniedPermissions = [
                ...deniedPermissions,
                ...ancestorPermissions.filter(p => !deniedPermissions.includes(p))
              ];
              inheritedPermissions = inheritedPermissions.filter(p => !ancestorPermissions.includes(p));
              break;
          }
        }
      }

      // Calculate final effective permissions
      const effectivePermissions = [
        ...directPermissions,
        ...inheritedPermissions.filter(p => !directPermissions.includes(p) && !deniedPermissions.includes(p))
      ];

      return {
        roleId: role.id,
        roleName: role.name,
        directPermissions,
        inheritedPermissions,
        deniedPermissions,
        effectivePermissions,
        inheritancePath,
      };
    } catch (error) {
      errorLogger.logError(
        `Failed to calculate effective permissions for role ${roleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get permission codes for a specific role
   * @param roleId Role ID
   * @returns Promise<string[]> Array of permission codes
   */
  private async getRolePermissions(roleId: number): Promise<string[]> {
    try {
      const rolePerms = await db
        .select({ code: permissions.code })
        .from(rolePermissions)
        .innerJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
        .where(eq(rolePermissions.role_id, roleId));

      return rolePerms.map(p => p.code);
    } catch (error) {
      errorLogger.logError(
        `Failed to get permissions for role ${roleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get a specific hierarchy relationship
   * @param parentRoleId Parent role ID
   * @param childRoleId Child role ID
   * @returns Promise<RoleHierarchy | null> Hierarchy relationship or null
   */
  private async getHierarchyRelationship(
    parentRoleId: number,
    childRoleId: number
  ): Promise<RoleHierarchy | null> {
    try {
      const [relationship] = await db
        .select()
        .from(roleHierarchy)
        .where(
          and(
            eq(roleHierarchy.parent_role_id, parentRoleId),
            eq(roleHierarchy.child_role_id, childRoleId)
          )
        )
        .limit(1);

      return relationship || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to get hierarchy relationship: ${parentRoleId} -> ${childRoleId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Validate that roles exist
   * @param roleIds Array of role IDs to validate
   * @throws Error if any role doesn't exist
   */
  private async validateRolesExist(roleIds: number[]): Promise<void> {
    try {
      const roles = await db
        .select({ id: customRoles.id })
        .from(customRoles)
        .where(inArray(customRoles.id, roleIds));

      const foundIds = roles.map(r => r.id);
      const missingIds = roleIds.filter(id => !foundIds.includes(id));

      if (missingIds.length > 0) {
        throw new Error(`Roles not found: ${missingIds.join(', ')}`);
      }
    } catch (error) {
      errorLogger.logError(
        `Failed to validate roles exist: ${roleIds.join(', ')}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  // ==================== ROLE TEMPLATE MANAGEMENT ====================

  /**
   * Get all role templates
   * @param industry Optional industry filter
   * @param isSystem Optional system template filter
   * @returns Promise<RoleTemplate[]> Array of role templates
   */
  async getRoleTemplates(industry?: string, isSystem?: boolean): Promise<RoleTemplate[]> {
    try {
      let query = db.select().from(roleTemplates);

      const conditions = [];
      if (industry) {
        conditions.push(eq(roleTemplates.industry, industry));
      }
      if (isSystem !== undefined) {
        conditions.push(eq(roleTemplates.is_system, isSystem));
      }

      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }

      return await query.orderBy(roleTemplates.name);
    } catch (error) {
      errorLogger.logError(
        `Failed to get role templates`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Get a specific role template by ID
   * @param templateId Template ID
   * @returns Promise<RoleTemplate | null> Role template or null if not found
   */
  async getRoleTemplate(templateId: number): Promise<RoleTemplate | null> {
    try {
      const [template] = await db
        .select()
        .from(roleTemplates)
        .where(eq(roleTemplates.id, templateId))
        .limit(1);

      return template || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to get role template ${templateId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Create a new role template
   * @param templateData Template data
   * @returns Promise<RoleTemplate> Created template
   */
  async createRoleTemplate(templateData: InsertRoleTemplate): Promise<RoleTemplate> {
    try {
      // Validate template configuration
      this.validateTemplateConfig(templateData.template_config);

      const [template] = await db
        .insert(roleTemplates)
        .values(templateData)
        .returning();

      errorLogger.logInfo(
        `Created role template: ${template.name}`,
        'role-hierarchy-service'
      );

      return template;
    } catch (error) {
      errorLogger.logError(
        `Failed to create role template: ${templateData.name}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Update a role template
   * @param templateId Template ID
   * @param updateData Update data
   * @returns Promise<RoleTemplate | null> Updated template or null if not found
   */
  async updateRoleTemplate(
    templateId: number,
    updateData: Partial<InsertRoleTemplate>
  ): Promise<RoleTemplate | null> {
    try {
      if (updateData.template_config) {
        this.validateTemplateConfig(updateData.template_config);
      }

      const [updated] = await db
        .update(roleTemplates)
        .set({
          ...updateData,
          updated_at: new Date()
        })
        .where(eq(roleTemplates.id, templateId))
        .returning();

      if (updated) {
        errorLogger.logInfo(
          `Updated role template: ${updated.name}`,
          'role-hierarchy-service'
        );
      }

      return updated || null;
    } catch (error) {
      errorLogger.logError(
        `Failed to update role template ${templateId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Delete a role template
   * @param templateId Template ID
   * @returns Promise<boolean> True if template was deleted
   */
  async deleteRoleTemplate(templateId: number): Promise<boolean> {
    try {
      const result = await db
        .delete(roleTemplates)
        .where(eq(roleTemplates.id, templateId));

      const deleted = result.rowCount > 0;
      if (deleted) {
        errorLogger.logInfo(
          `Deleted role template ${templateId}`,
          'role-hierarchy-service'
        );
      }

      return deleted;
    } catch (error) {
      errorLogger.logError(
        `Failed to delete role template ${templateId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Create a role from a template
   * @param templateId Template ID
   * @param roleName Name for the new role
   * @param companyId Company ID for the new role
   * @param description Optional description override
   * @returns Promise<CustomRole> Created role
   */
  async createRoleFromTemplate(
    templateId: number,
    roleName: string,
    companyId: number,
    description?: string
  ): Promise<CustomRole> {
    try {
      const template = await this.getRoleTemplate(templateId);
      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }

      const config = template.template_config as RoleTemplateConfig;

      // Create the role
      const [role] = await db
        .insert(customRoles)
        .values({
          name: roleName,
          description: description || config.description,
          company_id: companyId,
          is_system: false,
        })
        .returning();

      // Get permission IDs for the template permissions
      const permissionCodes = config.permissions;
      if (permissionCodes.length > 0) {
        const permissionRecords = await db
          .select({ id: permissions.id, code: permissions.code })
          .from(permissions)
          .where(inArray(permissions.code, permissionCodes));

        // Assign permissions to the role
        for (const permission of permissionRecords) {
          await db
            .insert(rolePermissions)
            .values({
              role_id: role.id,
              permission_id: permission.id,
            });
        }
      }

      errorLogger.logInfo(
        `Created role from template: ${roleName} (template: ${template.name})`,
        'role-hierarchy-service'
      );

      return role;
    } catch (error) {
      errorLogger.logError(
        `Failed to create role from template ${templateId}`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Validate template configuration structure
   * @param config Template configuration
   * @throws Error if configuration is invalid
   */
  private validateTemplateConfig(config: any): void {
    if (!config || typeof config !== 'object') {
      throw new Error('Template configuration must be an object');
    }

    if (!Array.isArray(config.permissions)) {
      throw new Error('Template configuration must include permissions array');
    }

    if (typeof config.description !== 'string') {
      throw new Error('Template configuration must include description string');
    }

    // Validate conditions if present
    if (config.conditions && typeof config.conditions !== 'object') {
      throw new Error('Template conditions must be an object');
    }
  }

  /**
   * Get role hierarchy tree for visualization
   * @param companyId Optional company filter
   * @returns Promise<RoleHierarchyNode[]> Array of root nodes with children
   */
  async getRoleHierarchyTree(companyId?: number): Promise<RoleHierarchyNode[]> {
    try {
      // Get all roles for the company
      let rolesQuery = db.select().from(customRoles);
      if (companyId) {
        rolesQuery = rolesQuery.where(eq(customRoles.company_id, companyId));
      }
      const roles = await rolesQuery;

      // Get all hierarchy relationships
      const hierarchies = await db.select().from(roleHierarchy);

      // Build the tree structure
      const roleMap = new Map<number, RoleHierarchyNode>();

      // Initialize all roles as nodes
      for (const role of roles) {
        roleMap.set(role.id, {
          role,
          children: [],
          parents: [],
          depth: 0,
        });
      }

      // Build parent-child relationships
      for (const hierarchy of hierarchies) {
        const parentNode = roleMap.get(hierarchy.parent_role_id);
        const childNode = roleMap.get(hierarchy.child_role_id);

        if (parentNode && childNode) {
          childNode.inheritanceType = hierarchy.inheritance_type;
          parentNode.children.push(childNode);
          childNode.parents.push(parentNode);
        }
      }

      // Calculate depths and find root nodes
      const rootNodes: RoleHierarchyNode[] = [];
      for (const node of roleMap.values()) {
        if (node.parents.length === 0) {
          this.calculateDepth(node, 0);
          rootNodes.push(node);
        }
      }

      return rootNodes;
    } catch (error) {
      errorLogger.logError(
        `Failed to get role hierarchy tree`,
        error,
        'role-hierarchy-service'
      );
      throw error;
    }
  }

  /**
   * Calculate depth for hierarchy tree nodes
   * @param node Node to calculate depth for
   * @param depth Current depth
   */
  private calculateDepth(node: RoleHierarchyNode, depth: number): void {
    node.depth = depth;
    for (const child of node.children) {
      this.calculateDepth(child, depth + 1);
    }
  }
}
