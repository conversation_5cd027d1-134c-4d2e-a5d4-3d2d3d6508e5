import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/auth';
import { Bug, User, Building } from 'lucide-react';

interface CompanyDebugInfoProps {
  currentCompany: any;
  roleForm: any;
  userForm: any;
  groupForm: any;
  roles?: any[];
  rolesError?: any;
  isLoadingRoles?: boolean;
}

export function CompanyDebugInfo({ currentCompany, roleForm, userForm, groupForm, roles, rolesError, isLoadingRoles }: CompanyDebugInfoProps) {
  const { user } = useAuth();

  // Only show in development mode
  if (!import.meta.env.DEV) {
    return null;
  }

  return (
    <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
          <Bug className="h-4 w-4" />
          Debug Information (Dev Mode Only)
        </CardTitle>
        <CardDescription className="text-orange-600 dark:text-orange-300">
          Company ID debugging information for role creation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current User Info */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-2">
            <User className="h-4 w-4" />
            Current User
          </h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="font-medium">ID:</span> {user?.id || 'N/A'}
            </div>
            <div>
              <span className="font-medium">Role:</span> 
              <Badge variant="outline" className="ml-1">{user?.role || 'N/A'}</Badge>
            </div>
            <div>
              <span className="font-medium">Company ID:</span> 
              <Badge variant="secondary" className="ml-1">{user?.company_id || 'N/A'}</Badge>
            </div>
            <div>
              <span className="font-medium">Email:</span> {user?.email || 'N/A'}
            </div>
          </div>
        </div>

        {/* Current Company Info */}
        <div>
          <h4 className="font-medium flex items-center gap-2 mb-2">
            <Building className="h-4 w-4" />
            Current Company Object
          </h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="font-medium">company_id:</span> 
              <Badge variant="secondary" className="ml-1">
                {currentCompany?.company_id || 'undefined'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">id:</span> 
              <Badge variant="secondary" className="ml-1">
                {currentCompany?.id || 'undefined'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">name:</span> {currentCompany?.name || 'N/A'}
            </div>
            <div>
              <span className="font-medium">Type:</span> {typeof currentCompany}
            </div>
          </div>
          
          {/* Raw object display */}
          <details className="mt-2">
            <summary className="cursor-pointer text-xs text-orange-600 dark:text-orange-300">
              Show Raw Object
            </summary>
            <pre className="text-xs bg-orange-100 dark:bg-orange-900 p-2 rounded mt-1 overflow-auto">
              {JSON.stringify(currentCompany, null, 2)}
            </pre>
          </details>
        </div>

        {/* Form Company IDs */}
        <div>
          <h4 className="font-medium mb-2">Form Company IDs</h4>
          <div className="grid grid-cols-3 gap-2 text-sm">
            <div>
              <span className="font-medium">Role Form:</span>
              <Badge 
                variant={roleForm.company_id === user?.company_id ? "default" : "destructive"} 
                className="ml-1"
              >
                {roleForm.company_id || 'null'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">User Form:</span>
              <Badge 
                variant={userForm.company_id === user?.company_id ? "default" : "destructive"} 
                className="ml-1"
              >
                {userForm.company_id || 'null'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Group Form:</span>
              <Badge 
                variant={groupForm.company_id === user?.company_id ? "default" : "destructive"} 
                className="ml-1"
              >
                {groupForm.company_id || 'null'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Validation Status */}
        <div>
          <h4 className="font-medium mb-2">Validation Status</h4>
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <span>User Company ID matches Role Form:</span>
              {user?.company_id === roleForm.company_id ? (
                <Badge variant="default">✓ Match</Badge>
              ) : (
                <Badge variant="destructive">✗ Mismatch</Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span>Current Company has company_id:</span>
              {currentCompany?.company_id ? (
                <Badge variant="default">✓ Yes</Badge>
              ) : (
                <Badge variant="secondary">✗ No</Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span>Current Company has id:</span>
              {currentCompany?.id ? (
                <Badge variant="default">✓ Yes</Badge>
              ) : (
                <Badge variant="secondary">✗ No</Badge>
              )}
            </div>
          </div>
        </div>

        {/* Recommendations */}
        {user?.company_id !== roleForm.company_id && (
          <div className="p-3 bg-red-100 dark:bg-red-900 rounded border border-red-200 dark:border-red-800">
            <h5 className="font-medium text-red-800 dark:text-red-200 mb-1">⚠️ Issue Detected</h5>
            <p className="text-sm text-red-700 dark:text-red-300">
              The role form company_id ({roleForm.company_id}) doesn't match the user's company_id ({user?.company_id}). 
              This will cause the "You can only create roles for your own company" error.
            </p>
          </div>
        )}

        {/* Roles Data Debug */}
        <div>
          <h4 className="font-medium mb-2">Roles Data</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="font-medium">Loading:</span>
              <Badge variant={isLoadingRoles ? "default" : "secondary"} className="ml-1">
                {isLoadingRoles ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Error:</span>
              <Badge variant={rolesError ? "destructive" : "default"} className="ml-1">
                {rolesError ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Roles Count:</span>
              <Badge variant="secondary" className="ml-1">
                {roles ? roles.length : 'undefined'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Data Type:</span>
              <Badge variant="secondary" className="ml-1">
                {Array.isArray(roles) ? 'Array' : typeof roles}
              </Badge>
            </div>
          </div>

          {rolesError && (
            <div className="mt-2 p-2 bg-red-100 dark:bg-red-900 rounded text-sm">
              <span className="font-medium text-red-800 dark:text-red-200">Error:</span>
              <span className="text-red-700 dark:text-red-300 ml-1">
                {rolesError instanceof Error ? rolesError.message : String(rolesError)}
              </span>
            </div>
          )}

          {roles && roles.length > 0 && (
            <details className="mt-2">
              <summary className="cursor-pointer text-xs text-orange-600 dark:text-orange-300">
                Show Roles Data ({roles.length} roles)
              </summary>
              <pre className="text-xs bg-orange-100 dark:bg-orange-900 p-2 rounded mt-1 overflow-auto max-h-32">
                {JSON.stringify(roles, null, 2)}
              </pre>
            </details>
          )}
        </div>

        {!currentCompany && (
          <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded border border-yellow-200 dark:border-yellow-800">
            <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">⚠️ Warning</h5>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              currentCompany is null or undefined. This might indicate an issue with company context.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
