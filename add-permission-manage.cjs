const { Client } = require('pg');

async function addPermissionManage() {
  const client = new Client({
    connectionString: '****************************************************/Trackfina-dev?sslmode=disable'
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Get the permission_manage permission ID
    const permissionResult = await client.query(`
      SELECT id FROM permissions WHERE code = 'permission_manage'
    `);

    if (permissionResult.rows.length === 0) {
      console.log('❌ permission_manage permission not found');
      return;
    }

    const permissionId = permissionResult.rows[0].id;
    console.log(`📋 Found permission_manage with ID: ${permissionId}`);

    // Get the user's current role (Admin, ID: 12)
    const roleId = 12;

    // Check if the role already has this permission
    const existingResult = await client.query(`
      SELECT * FROM role_permissions 
      WHERE role_id = $1 AND permission_id = $2
    `, [roleId, permissionId]);

    if (existingResult.rows.length > 0) {
      console.log('✅ Role already has permission_manage permission');
    } else {
      // Add the permission to the role
      await client.query(`
        INSERT INTO role_permissions (role_id, permission_id)
        VALUES ($1, $2)
      `, [roleId, permissionId]);

      console.log('✅ Added permission_manage to Admin role');
    }

    // Verify the user now has all required permissions
    const userPermissionsResult = await client.query(`
      SELECT DISTINCT p.code, p.name
      FROM users u
      JOIN user_roles ur ON u.id = ur.user_id
      JOIN role_permissions rp ON ur.role_id = rp.role_id
      JOIN permissions p ON rp.permission_id = p.id
      WHERE u.id = 17 AND p.code IN ('role_view', 'permission_view', 'permission_manage')
      ORDER BY p.code
    `);

    console.log('\n🔍 User now has these required permissions:');
    userPermissionsResult.rows.forEach(perm => {
      console.log(`  ✅ ${perm.code}: ${perm.name}`);
    });

    const hasAll = ['role_view', 'permission_view', 'permission_manage'].every(code =>
      userPermissionsResult.rows.some(p => p.code === code)
    );

    console.log(`\n🎯 All required permissions: ${hasAll ? '✅ YES' : '❌ NO'}`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

addPermissionManage();
