import React from 'react';
import { LoanCalculatorPreview } from './LoanCalculatorPreview';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Calculator, AlertCircle } from 'lucide-react';

// This component takes form data and renders a calculator preview
// if sufficient loan information is available
interface LoanFormCalculatorInlineProps {
  formData: any;
  onRecalculate?: () => void;
}

// Helper function to check all possible field patterns
function findFieldValue(formData: any, standardField: string, templates: string[]): any {
  // First check the standard field name
  if (formData[standardField] !== undefined) {
    const value = formData[standardField];
    // Handle case where value is an object with a "value" property (dropdown selection)
    if (typeof value === 'object' && value !== null && value.value !== undefined) {
      return value.value;
    }
    return value;
  }
  
  // Then check each template field ID pattern
  for (const fieldId of templates) {
    if (formData[fieldId] !== undefined) {
      const value = formData[fieldId];
      // Handle case where value is an object with a "value" property (dropdown selection)
      if (typeof value === 'object' && value !== null && value.value !== undefined) {
        return value.value;
      }
      return value;
    }
  }
  
  // Last resort: check if this might be in a nested field structure or passed differently
  if (formData.formData && typeof formData.formData === 'object') {
    return findFieldValue(formData.formData, standardField, templates);
  }
  
  return undefined;
}

export function LoanFormCalculatorInline({ 
  formData, 
  onRecalculate 
}: LoanFormCalculatorInlineProps) {
  // For debugging
  console.log("LoanFormCalculatorInline received formData:", formData);
  
  // Extract loan calculation values from formData using comprehensive field pattern matching
  // Amount - try all possible field patterns
  const amountValue = findFieldValue(formData, 'amount', [
    'field_121', 'field_101', 'field_92', 'amount', 'loan_amount'
  ]);
  const amount = amountValue ? parseFloat(String(amountValue)) : 0;
  
  // Interest Rate - try all possible field patterns
  const interestRateValue = findFieldValue(formData, 'interest_rate', [
    'field_122', 'field_100', 'field_91', 'interest', 'rate'
  ]);
  const interestRate = interestRateValue ? parseFloat(String(interestRateValue)) : 0;
  
  // Term - try all possible field patterns
  const termValue = findFieldValue(formData, 'term', [
    'field_125', 'field_103', 'field_93', 'term', 'duration'
  ]);
  const term = termValue ? parseInt(String(termValue)) : 0;
  
  // Interest Type - try all possible field patterns
  let interestTypeValue = findFieldValue(formData, 'interest_type', [
    'field_120', 'field_102', 'field_95', 'type'
  ]) || 'flat';
  
  // If it's an object (from the options format in database), extract value
  if (typeof interestTypeValue === 'object' && interestTypeValue !== null) {
    console.log('Converting interest_type object to string value:', interestTypeValue);
    interestTypeValue = interestTypeValue.value || 'flat';
  }
  let interestType = String(interestTypeValue).toLowerCase();
  
  // Map 'simple' to 'flat' to match database enum values
  if (interestType === 'simple') {
    console.log('Mapping interest type "simple" to "flat" in calculator');
    interestType = 'flat';
  }
  
  // Log extracted values for debugging
  console.log(`Loan calculator values - amount: ${amount}, interest: ${interestRate}, term: ${term}, type: ${interestType}`);
  
  // If we're missing required values, show a prompt
  if (amount <= 0 || interestRate <= 0 || term <= 0) {
    return (
      <Card className="mt-6 border border-dashed border-muted-foreground/50 bg-muted/30">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Loan Payment Calculator</CardTitle>
          </div>
          <CardDescription>
            Fill out loan amount, interest rate, and term to see payment calculations
          </CardDescription>
        </CardHeader>
        <CardContent className="py-4">
          <div className="flex items-center text-muted-foreground justify-center p-2">
            <AlertCircle className="h-4 w-4 mr-2" />
            <span>Complete the loan details above to preview payment information</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Extract payment frequency if available
  const paymentFrequencyValue = findFieldValue(formData, 'payment_frequency', [
    'field_130', 'frequency', 'schedule_type'
  ]) || 'monthly';
  
  // Determine if interest should be deducted upfront
  // Extract from formData directly, defaulting to true if not specified
  const upfrontInterestValue = findFieldValue(formData, 'is_upfront_interest', [
    'field_128', 'field_108', 'field_160', 'upfront_interest', 'interest_upfront', 'deduct_interest_upfront'
  ]);
  
  // Special debug for the upfront interest value - to be clear what's happening
  console.log(`Found upfront interest value: '${upfrontInterestValue}'`, 
    `(type: ${typeof upfrontInterestValue})`, 
    `formData has is_upfront_interest: ${formData.hasOwnProperty('is_upfront_interest')}`);
  
  // Convert possible string values like "true" or "false" to actual boolean
  // DEFAULT TO TRUE - this is the business model preference
  const deductInterestUpfront = upfrontInterestValue === undefined ? true : 
    (upfrontInterestValue === "true" || upfrontInterestValue === true || upfrontInterestValue === "1" || upfrontInterestValue === 1);
    
  console.log(`Final deductInterestUpfront value: ${deductInterestUpfront}`);
  
  // Extract the terms frequency if available - try many possible field names and variants
  const termsFrequencyValue = findFieldValue(formData, 'terms_frequency', [
    'field_132', 'field_115', 'field_155', 'field_200', 'terms_frequency', 'term_type', 
    'term_frequency', 'frequency', 'payment_terms_unit', 'term_unit', 'termType'
  ]) || 'monthly';
  
  // Log the terms frequency value for debugging
  console.log(`Using terms_frequency: ${termsFrequencyValue}`);
  console.log('All form data keys:', Object.keys(formData));
  console.log('Form data contains terms_frequency?', formData.hasOwnProperty('terms_frequency'));
  console.log('Terms frequency direct from form data:', formData.terms_frequency);

  // When we have the required values, show the calculator
  return (
    <LoanCalculatorPreview
      amount={amount}
      interestRate={interestRate}
      termMonths={term}
      interestType={interestType}
      paymentFrequency={termsFrequencyValue}
      deductInterestUpfront={deductInterestUpfront}
      onRecalculate={onRecalculate}
    />
  );
}