import React, { useEffect, useState } from 'react';
import { useBranches } from '@/lib/branches';
import { useAuth } from '@/lib/auth';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Building, Loader2 } from 'lucide-react';

interface BranchSelectorProps {
  className?: string;
  size?: 'default' | 'sm';
  showLabel?: boolean;
}

export const BranchSelector: React.FC<BranchSelectorProps> = ({
  className = '',
  size = 'default',
  showLabel = true
}) => {
  const { getCurrentUser } = useAuth();
  const { 
    branches, 
    currentBranchId, 
    fetchBranches, 
    switchBranch,
    isLoading
  } = useBranches();
  
  const [hasFetched, setHasFetched] = useState(false);
  const user = getCurrentUser();
  const companyId = user?.company_id;
  
  // Fetch branches on component mount only once
  useEffect(() => {
    if (companyId && !hasFetched) {
      fetchBranches(companyId);
      setHasFetched(true);
    }
  }, [companyId, fetchBranches, hasFetched]);

  // Handle branch selection
  const handleBranchChange = (value: string) => {
    const branchId = parseInt(value, 10);
    const selectedBranch = branches.find(branch => branch.id === branchId);
    
    if (selectedBranch) {
      switchBranch(branchId, selectedBranch.name);
    }
  };

  if (!companyId) {
    return null;
  }

  return (
    <div className={`flex items-center gap-1.5 ${className}`}>
      {showLabel && (
        <span className="text-sm font-medium text-muted-foreground">Branch:</span>
      )}
      <Select
        value={currentBranchId?.toString() || ''}
        onValueChange={handleBranchChange}
        disabled={isLoading || branches.length === 0}
      >
        <SelectTrigger 
          className={size === 'sm' ? 'h-8 text-xs' : ''} 
        >
          <SelectValue placeholder="Select branch">
            {isLoading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </div>
            ) : currentBranchId ? (
              branches.find(b => b.id === currentBranchId)?.name || 'Unknown Branch'
            ) : (
              branches.length === 0 ? 'No branches' : 'Select branch'
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {branches.length > 0 ? (
            branches.map((branch) => (
              <SelectItem 
                key={branch.id} 
                value={branch.id.toString()}
              >
                <div className="flex items-center">
                  <Building className="mr-2 h-4 w-4 opacity-70" />
                  <span>{branch.name}</span>
                </div>
              </SelectItem>
            ))
          ) : (
            <div className="p-2 text-sm text-muted-foreground text-center">
              {isLoading ? 'Loading branches...' : 'No branches available'}
            </div>
          )}
        </SelectContent>
      </Select>
    </div>
  );
};

export default BranchSelector;