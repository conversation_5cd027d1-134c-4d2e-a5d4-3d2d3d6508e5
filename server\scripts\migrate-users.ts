import { db } from "../db";
import { users, userCompanies } from "@shared/schema";
import { eq, isNotNull } from "drizzle-orm";

/**
 * This script will migrate all existing users to use the new user_companies association table.
 * It adds a user-company association for users who have a company_id set in the users table.
 */
async function migrateUsers() {
  try {
    console.log("Starting user migration...");
    
    // Get all users that have a company_id set
    const usersWithCompany = await db
      .select()
      .from(users)
      .where(isNotNull(users.company_id));
    
    console.log(`Found ${usersWithCompany.length} users with company associations to migrate`);
    
    // For each user, create a user-company association if their company_id is not null
    for (const user of usersWithCompany) {
      if (user.company_id) {
        // Check if association already exists
        const existingAssociations = await db
          .select()
          .from(userCompanies)
          .where(
            eq(userCompanies.user_id, user.id)
          );
        
        // If no associations exist for this user, create one
        if (existingAssociations.length === 0) {
          console.log(`Creating user-company association for user ${user.id} and company ${user.company_id}`);
          
          await db.insert(userCompanies).values({
            user_id: user.id,
            company_id: user.company_id,
            is_primary: true, // Set this as primary company
            role: user.role,
          });
          
          console.log(`User-company association created successfully for user ${user.id}`);
        } else {
          console.log(`User ${user.id} already has ${existingAssociations.length} company associations`);
        }
      }
    }
    
    console.log("User migration completed successfully");
  } catch (error) {
    console.error("Error migrating users:", error);
  } finally {
    process.exit(0);
  }
}

migrateUsers();