import { Express } from 'express';
import { createServer, type Server } from 'http';
// Import route modules
import { registerAuthRoutes } from './auth.routes';
import { registerUserRoutes } from './user.routes';
import { registerCompanyRoutes } from './company.routes';
import { registerBranchRoutes } from './branch.routes';
import { registerCustomerRoutes } from './customer.routes';
import { registerLoanRoutes } from './loan.routes';
import { registerLoanConfigurationRoutes } from './loan-configuration.routes';
import { registerDashboardRoutes } from './dashboard.routes';
import { registerPartnerRoutes } from './partner.routes';
import { registerCollectionRoutes } from './collection.routes';
import { registerPaymentRoutes } from './payment.routes';
import { registerAgentRoutes } from './agent.routes';
import { registerFinancialRoutes } from './financial';
import { registerPermissionRoutes } from './permission.routes';
import { registerEnhancedPermissionRoutes } from './enhanced-permission.routes';
import { registerPermissionConditionsRoutes } from './permissionConditions.routes';
import { registerRoleRoutes } from './role.routes';
import { registerRoleHierarchyRoutes } from './role-hierarchy.routes';
import { registerTemporaryPermissionRoutes } from './temporary-permissions.routes';
import { setupApprovalWorkflowRoutes } from './approval-workflow.routes';
import { registerGroupManagementRoutes } from './group-management.routes';
import { registerUserRoleRoutes } from './user-role.routes';
import { registerPasswordRoutes } from './password.routes';
import { registerCompanyPrefixSettingsRoutes } from './company-prefix-settings.routes';
import sessionManagementRoutes from './sessionManagement.routes';
import accessMonitoringRoutes from './accessMonitoring.routes';
import auditRoutes from './audit.routes';
import complianceRoutes from './compliance.routes';
import selfServicePortalRoutes from './selfServicePortal.routes';
import { registerManagerToolsRoutes } from './manager-tools.routes';
import { registerBulkOperationsRoutes } from './bulk-operations.routes';
import { registerAdvancedSearchRoutes } from './advanced-search.routes';
import { registerPermissionsDashboardRoutes } from './permissions-dashboard.routes';
import { registerMFARoutes } from './mfa.routes';
import { registerEmailVerificationRoutes } from './email-verification.routes';
import { registerReportRoutes } from './report.routes';
// Import other route modules

export async function registerRoutes(app: Express): Promise<Server> {
  console.log('Registering routes...');

  // Add a simple test route first
  app.get('/api/test', (req, res) => {
    res.json({ message: 'Test route working' });
  });
  console.log('Test route registered');

  // Demo roles route removed - using real database route instead

  // Remove the simple permissions test route - let the proper one handle it
  // The proper permissions route will be registered by registerPermissionRoutes(app)

  // Register all route modules with error handling
  try {
    console.log('Registering auth routes...');
    registerAuthRoutes(app);
    console.log('Auth routes registered');

    console.log('Registering user routes...');
    registerUserRoutes(app);
    console.log('User routes registered');

    // Register resource-specific routes before company routes to prevent masking
    console.log('Registering branch routes...');
    registerBranchRoutes(app);
    console.log('Branch routes registered');

    console.log('Registering company routes...');
    registerCompanyRoutes(app);
    console.log('Company routes registered');

    console.log('Registering permission routes...');
    registerPermissionRoutes(app);
    console.log('Permission routes registered');

    console.log('Registering role routes...');
    registerRoleRoutes(app);
    console.log('Role routes registered');

    // Group management and role hierarchy routes will be registered later with other permission routes

    // Enhanced permission and approval workflow routes will be registered later with other permission routes

    console.log('Registering session management routes...');
    try {
      app.use('/api/sessions', sessionManagementRoutes);
      console.log('Session management routes registered');
    } catch (error) {
      console.error('Error registering session management routes:', error);
    }

    console.log('Registering access monitoring routes...');
    try {
      app.use('/api/monitoring', accessMonitoringRoutes);
      console.log('Access monitoring routes registered');
    } catch (error) {
      console.error('Error registering access monitoring routes:', error);
    }

    console.log('Registering audit routes...');
    try {
      app.use('/api/audit', auditRoutes);
      console.log('Audit routes registered');
    } catch (error) {
      console.error('Error registering audit routes:', error);
    }

    console.log('Registering compliance routes...');
    try {
      app.use('/api/compliance', complianceRoutes);
      console.log('Compliance routes registered');
    } catch (error) {
      console.error('Error registering compliance routes:', error);
    }

    console.log('Registering self-service portal routes...');
    try {
      app.use('/api/self-service', selfServicePortalRoutes);
      console.log('Self-service portal routes registered');
    } catch (error) {
      console.error('Error registering self-service portal routes:', error);
    }

    console.log('Registering manager tools routes...');
    try {
      registerManagerToolsRoutes(app);
      console.log('Manager tools routes registered');
    } catch (error) {
      console.error('Error registering manager tools routes:', error);
    }

    console.log('Registering bulk operations routes...');
    try {
      registerBulkOperationsRoutes(app);
      console.log('Bulk operations routes registered');
    } catch (error) {
      console.error('Error registering bulk operations routes:', error);
    }

    console.log('Registering advanced search routes...');
    try {
      registerAdvancedSearchRoutes(app);
      console.log('Advanced search routes registered');
    } catch (error) {
      console.error('Error registering advanced search routes:', error);
    }

    console.log('Registering permissions dashboard routes...');
    try {
      registerPermissionsDashboardRoutes(app);
      console.log('Permissions dashboard routes registered');
    } catch (error) {
      console.error('Error registering permissions dashboard routes:', error);
    }

    console.log('Registering MFA routes...');
    try {
      registerMFARoutes(app);
      console.log('MFA routes registered');
    } catch (error) {
      console.error('Error registering MFA routes:', error);
    }

    console.log('Registering email verification routes...');
    try {
      registerEmailVerificationRoutes(app);
      console.log('Email verification routes registered');
    } catch (error) {
      console.error('Error registering email verification routes:', error);
    }

    // Register financial routes (needed for financial pages)
    console.log('Registering financial routes...');
    registerFinancialRoutes(app);
    console.log('Financial routes registered');

    // Register core business routes (needed for main application functionality)
    console.log('Registering customer routes...');
    registerCustomerRoutes(app);
    console.log('Customer routes registered');

    console.log('Registering loan routes...');
    registerLoanRoutes(app);
    console.log('Loan routes registered');

    console.log('Registering loan configuration routes...');
    registerLoanConfigurationRoutes(app);
    console.log('Loan configuration routes registered');

    console.log('Registering dashboard routes...');
    registerDashboardRoutes(app);
    console.log('Dashboard routes registered');

    console.log('Registering partner routes...');
    registerPartnerRoutes(app);
    console.log('Partner routes registered');

    console.log('Registering collection routes...');
    registerCollectionRoutes(app);
    console.log('Collection routes registered');

    console.log('Registering payment routes...');
    registerPaymentRoutes(app);
    console.log('Payment routes registered');

    console.log('Registering agent routes...');
    registerAgentRoutes(app);
    console.log('Agent routes registered');

    // Register enhanced permission and workflow routes (needed for user management)
    console.log('Registering enhanced permission routes...');
    try {
      registerEnhancedPermissionRoutes(app);
      console.log('Enhanced permission routes registered');
    } catch (error) {
      console.error('Error registering enhanced permission routes:', error);
    }

    console.log('Registering permission conditions routes...');
    try {
      registerPermissionConditionsRoutes(app);
      console.log('Permission conditions routes registered');
    } catch (error) {
      console.error('Error registering permission conditions routes:', error);
    }

    console.log('Registering role hierarchy routes...');
    registerRoleHierarchyRoutes(app);
    console.log('Role hierarchy routes registered');

    console.log('Registering temporary permission routes...');
    registerTemporaryPermissionRoutes(app);
    console.log('Temporary permission routes registered');

    console.log('Registering approval workflow routes...');
    setupApprovalWorkflowRoutes(app);
    console.log('Approval workflow routes registered');

    console.log('Registering group management routes...');
    try {
      registerGroupManagementRoutes(app);
      console.log('Group management routes registered');
    } catch (error) {
      console.error('Error registering group management routes:', error);
    }

    console.log('Registering user role routes...');
    registerUserRoleRoutes(app);
    console.log('User role routes registered');

    console.log('Registering password routes...');
    registerPasswordRoutes(app);
    console.log('Password routes registered');

    console.log('Registering report routes...');
    registerReportRoutes(app);
    console.log('Report routes registered');
  } catch (error) {
    console.error('Error during route registration:', error);
    throw error;
  }

  console.log('Registering company prefix settings routes...');
  try {
    registerCompanyPrefixSettingsRoutes(app);
    console.log('Company prefix settings routes registered successfully');
  } catch (error) {
    console.error('Error registering company prefix settings routes:', error);
  }

  // These will be implemented later
  // registerReportRoutes(app);
  // Register other route modules

  // For now, we'll keep using the original routes.ts file
  // We'll gradually replace it as we implement more modules

  // Create and return the HTTP server
  const server = createServer(app);
  return server;
}
