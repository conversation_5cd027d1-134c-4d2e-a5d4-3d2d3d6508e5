import { useState } from "react";
import { useLocation, Link } from "wouter";
import { useAuth } from "@/lib/auth";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Home, DollarSign, Users, User, FileText, Briefcase,
  Share2, Settings, Building2, CreditCard, Receipt,
  BarChart4, Wallet, Calculator, LineChart, Shield, UserCog
} from "lucide-react";

interface SidebarNavigationProps {
  closeSidebar?: () => void;
}

const SidebarNavigation = ({ closeSidebar }: SidebarNavigationProps) => {
  const [location] = useLocation();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const isMobile = useIsMobile();

  // Role-based menu visibility
  const isCompanyUser = user?.role === 'owner' || user?.role === 'employee';
  const isCompanyAdmin = user?.role === 'owner';
  const isAgent = user?.role === 'agent';
  const isReseller = user?.role === 'reseller';
  const isSaasAdmin = user?.role === 'saas_admin';
  const [partnerExpanded, setPartnerExpanded] = useState(false);

  // Create a custom NavLink component that handles sidebar closing on mobile
  const NavLink = ({ href, icon, label, isActive }: { href: string, icon: React.ReactNode, label: string, isActive: boolean }) => {
    const handleClick = () => {
      // Only close the sidebar if on mobile and closeSidebar function is provided
      if (isMobile && closeSidebar) {
        closeSidebar();
      }
    };

    return (
      <Link href={href}>
        <div
          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
            isActive
              ? 'bg-blue-800 text-white'
              : 'text-blue-100 hover:bg-blue-800 hover:text-white'
          }`}
          onClick={handleClick}
        >
          {icon}
          {label}
        </div>
      </Link>
    );
  };

  return (
    <nav className="px-2 space-y-1 overflow-y-auto overflow-x-hidden custom-scrollbar flex-1">
      <div className="px-3 mb-2 text-xs font-medium text-blue-200 uppercase">Main</div>

      <NavLink
        href="/dashboard"
        icon={<Home className="mr-3 h-5 w-5 text-blue-300" />}
        label="Dashboard"
        isActive={location === '/dashboard'}
      />

      {(isCompanyUser || isAgent || isSaasAdmin) && (
        <NavLink
          href="/collections"
          icon={<DollarSign className="mr-3 h-5 w-5 text-blue-300" />}
          label="Collections"
          isActive={location.startsWith('/collections')}
        />
      )}

      {(isCompanyUser || isSaasAdmin) && (
        <NavLink
          href="/agents"
          icon={<Users className="mr-3 h-5 w-5 text-blue-300" />}
          label="Agents"
          isActive={location.startsWith('/agents')}
        />
      )}

      {(isCompanyUser || isAgent || isSaasAdmin) && (
        <NavLink
          href="/customers"
          icon={<User className="mr-3 h-5 w-5 text-blue-300" />}
          label="Customers"
          isActive={location.startsWith('/customers')}
        />
      )}

      {(isCompanyUser || isAgent || isSaasAdmin) && (
        <NavLink
          href="/loans"
          icon={<Receipt className="mr-3 h-5 w-5 text-blue-300" />}
          label="Loans"
          isActive={location === '/loans' || !!location.match(/^\/loans\/\d+$/) || location.startsWith('/loan-form-builder')}
        />
      )}

      {(isCompanyUser || isSaasAdmin) && (
        <NavLink
          href="/partners"
          icon={<Briefcase className="mr-3 h-5 w-5 text-blue-300" />}
          label="Partners"
          isActive={location.startsWith('/partners')}
        />
      )}

      {/* Forms are now accessed through the Loans section */}

      {/* Financial Management Section */}
      {(isCompanyUser || isSaasAdmin) && (
        <>
          <div className="px-3 mt-6 mb-2 text-xs font-medium text-blue-200 uppercase">
            Financial Management
          </div>

          <NavLink
            href="/financial/accounts"
            icon={<BarChart4 className="mr-3 h-5 w-5 text-blue-300" />}
            label="Accounts"
            isActive={location.startsWith('/financial/accounts')}
          />

          <NavLink
            href="/financial/transactions"
            icon={<Wallet className="mr-3 h-5 w-5 text-blue-300" />}
            label="Transactions"
            isActive={location.startsWith('/financial/transactions')}
          />

          <NavLink
            href="/financial/expenses"
            icon={<Calculator className="mr-3 h-5 w-5 text-blue-300" />}
            label="Expenses"
            isActive={location.startsWith('/financial/expenses')}
          />

          <NavLink
            href="/financial/reports"
            icon={<LineChart className="mr-3 h-5 w-5 text-blue-300" />}
            label="Financial Reports"
            isActive={location.startsWith('/financial/reports')}
          />
        </>
      )}

      {(isCompanyUser || isSaasAdmin) && (
        <NavLink
          href="/reports"
          icon={<FileText className="mr-3 h-5 w-5 text-blue-300" />}
          label="Reports"
          isActive={location.startsWith('/reports')}
        />
      )}

      {/* User Management Section */}
      {(isCompanyAdmin || isSaasAdmin) && (
        <>
          <div className="px-3 mt-6 mb-2 text-xs font-medium text-blue-200 uppercase">
            Administration
          </div>

          <NavLink
            href="/user-management"
            icon={<UserCog className="mr-3 h-5 w-5 text-blue-300" />}
            label="User Management"
            isActive={location.startsWith('/user-management')}
          />
        </>
      )}

      {isSaasAdmin && (
        <>
          <NavLink
            href="/companies"
            icon={<Building2 className="mr-3 h-5 w-5 text-blue-300" />}
            label="Companies"
            isActive={location.startsWith('/companies')}
          />

          <NavLink
            href="/subscriptions"
            icon={<CreditCard className="mr-3 h-5 w-5 text-blue-300" />}
            label="Subscriptions"
            isActive={location.startsWith('/subscriptions')}
          />
        </>
      )}

      {(isReseller || isSaasAdmin) && (
        <>
          <div
            className="px-3 mt-6 mb-2 flex items-center justify-between text-xs font-medium text-blue-200 uppercase cursor-pointer"
            onClick={() => setPartnerExpanded(!partnerExpanded)}
          >
            <span>Partner Program</span>
            <span className="text-xs">{partnerExpanded ? "−" : "+"}</span>
          </div>

          {partnerExpanded && (
            <>
              <NavLink
                href="/reseller"
                icon={<Share2 className="mr-3 h-5 w-5 text-blue-300" />}
                label="Reseller Dashboard"
                isActive={location.startsWith('/reseller')}
              />

              <NavLink
                href="/referrals"
                icon={<Share2 className="mr-3 h-5 w-5 text-blue-300" />}
                label="Referrals"
                isActive={location.startsWith('/referrals')}
              />
            </>
          )}
        </>
      )}

      <div className="px-3 mt-6 mb-2 text-xs font-medium text-blue-200 uppercase">User</div>

      <NavLink
        href="/manager-dashboard"
        icon={<UserCog className="mr-3 h-5 w-5 text-blue-300" />}
        label="Manager Tools"
        isActive={location.startsWith('/manager-dashboard')}
      />

      <NavLink
        href="/self-service"
        icon={<Shield className="mr-3 h-5 w-5 text-blue-300" />}
        label="Self-Service Portal"
        isActive={location.startsWith('/self-service')}
      />

      <NavLink
        href="/settings"
        icon={<Settings className="mr-3 h-5 w-5 text-blue-300" />}
        label="Settings"
        isActive={location.startsWith('/settings')}
      />

      <NavLink
        href="/profile"
        icon={<User className="mr-3 h-5 w-5 text-blue-300" />}
        label="Profile"
        isActive={location.startsWith('/profile')}
      />
    </nav>
  );
};

export default SidebarNavigation;