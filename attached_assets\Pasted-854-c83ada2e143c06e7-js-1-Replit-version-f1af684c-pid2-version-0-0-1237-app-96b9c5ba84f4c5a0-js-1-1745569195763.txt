854-c83ada2e143c06e7.js:1 Replit version f1af684c, pid2 version 0.0.1237
_app-96b9c5ba84f4c5a0.js:16 
      /////////////////////
   ///////////////////////////
  ////////@@@@@@@//////////////
  ////////@@@@@@@//////////////
  ////////@@@@@@@//////////////        Curious and driven?
  ///////////////@@@@@@@///////        Work with us!
  ///////////////@@@@@@@///////
  ///////////////@@@@@@@///////        https://join.replit.com/hack.with.us
  ////////@@@@@@@//////////////
  ////////@@@@@@@//////////////
  ////////@@@@@@#//////////////
   ///////////////////////////
      /////////////////////
      
_app-96b9c5ba84f4c5a0.js:16 [LaunchDarkly] LaunchDarkly client initialized
analytics.tiktok.com/i18n/pixel/events.js?sdkid=D004GE3C77U8PIVDSDJG&lib=ttq:1 
            
            
           Failed to load resource: net::ERR_CONNECTION_RESET
/~:1 Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'ambient-light-sensor'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'battery'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'execution-while-not-rendered'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'execution-while-out-of-viewport'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'layout-animations'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'legacy-image-formats'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'navigation-override'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'oversized-images'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'publickey-credentials'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'speaker-selection'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'unoptimized-images'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'unsized-media'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Unrecognized feature: 'pointer-lock'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Allow attribute will take precedence over 'allowfullscreen'.
_ @ framework-d6e81bd519397a36.js:1
framework-d6e81bd519397a36.js:1 Allow attribute will take precedence over 'allowpaymentrequest'.
_ @ framework-d6e81bd519397a36.js:1
dotdevproxy.kirk.repl.co/__reachability:1 
            
            
           Failed to load resource: net::ERR_NETWORK_CHANGED
api.us-east-1.aws.neon.tech/sql:1 
            
            
           Failed to load resource: the server responded with a status of 400 ()
_app-96b9c5ba84f4c5a0.js:16 error NeonDbError: time zone "gmt+0530" not recognized
    at execute (8017.a18a62d4df002c8d.js:11:8215)
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
api.us-east-1.aws.neon.tech/sql:1 
            
            
           Failed to load resource: the server responded with a status of 400 ()
_app-96b9c5ba84f4c5a0.js:16 error NeonDbError: time zone "gmt+0530" not recognized
    at execute (8017.a18a62d4df002c8d.js:11:8215)
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
_app-96b9c5ba84f4c5a0.js:16 `DialogContent` requires a `DialogTitle` for the component to be accessible for screen reader users.

If you want to hide the `DialogTitle`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/dialog
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
_app-96b9c5ba84f4c5a0.js:16 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
api.us-east-1.aws.neon.tech/sql:1 
            
            
           Failed to load resource: the server responded with a status of 400 ()
_app-96b9c5ba84f4c5a0.js:16 error NeonDbError: null value in column "created_at" of relation "user_companies" violates not-null constraint
    at execute (8017.a18a62d4df002c8d.js:11:8215)
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
_app-96b9c5ba84f4c5a0.js:16 `DialogContent` requires a `DialogTitle` for the component to be accessible for screen reader users.

If you want to hide the `DialogTitle`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/dialog
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
_app-96b9c5ba84f4c5a0.js:16 Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
FinancialTracker:1 Access to fetch at 'https://sp.replit.com/v1/m' from origin 'https://replit.com' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource. If an opaque response serves your needs, set the request's mode to 'no-cors' to fetch the resource with CORS disabled.
sp.replit.com/v1/m:1 
            
            
           Failed to load resource: net::ERR_FAILED
_app-96b9c5ba84f4c5a0.js:16 Error sending segment performance metrics TypeError: Failed to fetch
    at _app-96b9c5ba84f4c5a0.js:16:9046
    at _app-96b9c5ba84f4c5a0.js:1:110253
    at o (_app-96b9c5ba84f4c5a0.js:1:488011)
    at o (sdk.min.js:1:23752)
    at t.<anonymous> (remote-metrics.ts:138:12)
    at tslib.es6.js:103:23
    at Object.next (tslib.es6.js:108:37)
    at tslib.es6.js:80:8
    at new Promise (<anonymous>)
    at u (tslib.es6.js:72:27)
    at t.send (sdk.min.js:1:22010)
    at t.<anonymous> (remote-metrics.ts:125:16)
    at tslib.es6.js:103:23
    at Object.next (tslib.es6.js:108:37)
    at tslib.es6.js:80:8
    at new Promise (<anonymous>)
    at u (tslib.es6.js:72:27)
    at t.flush (sdk.min.js:1:21743)
    at l (remote-metrics.ts:83:14)
    at r (_app-96b9c5ba84f4c5a0.js:16:2959)
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
_app-96b9c5ba84f4c5a0.js:16 onClick is deprecated, please use onPress
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
3922-1f67bfbf131624e6.js:1 WebSocket connection to 'wss://eval2.riker.platform.replit.com/wsv2/v2.public.Q2dZSWxvQ3N3QVlTQmdpMW5LekFCaUlGY21sclpYSXdBRHJWQVFva05EVmhNakU0T1RFdFl6WmhPQzAwWVRJeUxUa3lZVFF0TnpreVpHWm1NbU00WlROakVnTnVhWGdhRjNKbGNHeHBkQzF5WlhCc0xXWnBiR1Z6TFhKcGEyVnlJaEJHYVc1aGJtTnBZV3hVY21GamEyVnlLZzUyWlhSeWFYWmxiR1JsZG05d2MwSlJDaGh5WlhCc2FYUXRjbVZ3YkMxaWJHOWphM010Y21sclpYSVNGbkpsY0d4cGRDMXlaWEJzTFcxbGRHRXRjbWxyWlhJYUhYSmxjR3hwZEMxdFlYSm5ZWEpwYm1VdFlteHZZMnR6TFhKcGEyVnlTZ2NJbE5IM0V4QUJVQUJhRDJsdWRHVnVkRjl3WlhKemIyNWhiRklsQ0FFUWdJQ0FnQWdaQUFBQUFBQUE4RDhoQUFBQUFBQUE4RDhvZ0lDQWdNZ0JNQUU0QUdBQWFoVUlsTkgzRXhJT2RtVjBjbWwyWld4a1pYWnZjSE55R1hWelpTMXNZWFZ1WTJoa1lYSnJiSGt0WkdseVpXTjBiSG1DQVFJcUFJb0JJd2dCRUlDQWdJQWdHUUFBQUFBQUFCQkFJUUFBQUFBQUFCQkFLSUNBZ0lESUFUQUFtZ0VLQ2dnd0xqQXVNVEl6TjZBQkFRPT0nFXB4EPOpRT-HNPvmY167O4uBfUqHczK0ndDc_g1KhOQ9j8gdb_Yq3cDdY50G160FkgN0s3d9cKCq0hbwcDEG.Q2dad2NtOWtPak1pQ25KbGNHeHBkQzVqYjIwPQ' failed: WebSocket is closed before the connection is established.
cleanupSocket @ 3922-1f67bfbf131624e6.js:1
stallwart.build.js:1 stallwart: failed ping 1
logs.browser-intake-us5-datadoghq.com/api/v2/logs?ddsource=browser&ddtags=sdk_version%3A4.46.0%2Capi%3Afetch%2Cenv%3Aproduction%2Cservice%3Awebsite%2Cversion%3Af1af684c&dd-api-key=pub31a5047a3a4692afb84a423db984dc57&dd-evp-origin-version=4.46.0&dd-evp-origin=browser&dd-request-id=c734c3ef-285c-4e0e-9500-ec346fedc3a2:1 
            
            
           Failed to load resource: net::ERR_NAME_NOT_RESOLVED
events.launchdarkly.com/events/diagnostic/62b35a865152ab14c5942820:1 
            
            
           Failed to load resource: net::ERR_NAME_NOT_RESOLVED
api.sorryapp.com/v1/pages/27c7b5fc:1 
            
            
           Failed to load resource: net::ERR_NAME_NOT_RESOLVED
events.launchdarkly.com/events/diagnostic/62b35a865152ab14c5942820:1 
            
            
           Failed to load resource: net::ERR_NETWORK_CHANGED
_app-96b9c5ba84f4c5a0.js:16 onClick is deprecated, please use onPress
(anonymous) @ _app-96b9c5ba84f4c5a0.js:16
1243-24ce699a7ca73a5f.js:1 WebSocket is already in CLOSING or CLOSED state.
send @ 1243-24ce699a7ca73a5f.js:1
workspace_iframe.html:32 Unrecognized feature: 'ambient-light-sensor'.
workspace_iframe.html:32 Unrecognized feature: 'battery'.
workspace_iframe.html:32 Unrecognized feature: 'execution-while-not-rendered'.
workspace_iframe.html:32 Unrecognized feature: 'execution-while-out-of-viewport'.
workspace_iframe.html:32 Unrecognized feature: 'layout-animations'.
workspace_iframe.html:32 Unrecognized feature: 'legacy-image-formats'.
workspace_iframe.html:32 Unrecognized feature: 'navigation-override'.
workspace_iframe.html:32 Unrecognized feature: 'oversized-images'.
workspace_iframe.html:32 Unrecognized feature: 'publickey-credentials'.
workspace_iframe.html:32 Unrecognized feature: 'speaker-selection'.
workspace_iframe.html:32 Unrecognized feature: 'unoptimized-images'.
workspace_iframe.html:32 Unrecognized feature: 'unsized-media'.
workspace_iframe.html:32 Allow attribute will take precedence over 'allowfullscreen'.
workspace_iframe.html:32 Allow attribute will take precedence over 'allowpaymentrequest'.
45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/api/partners:1 
            
            
           Failed to load resource: the server responded with a status of 400 (Bad Request)
Console.js:61 Error creating partner: Error: 400: {"message":"Invalid input","errors":[{"code":"invalid_type","expected":"date","received":"string","path":["partnership_start_date"],"message":"Expected date, received string"},{"code":"invalid_type","expected":"date","received":"string","path":["partnership_end_date"],"message":"Expected date, received string"}]}
    at throwIfResNotOk (queryClient.ts:6:11)
    at async apiRequest (queryClient.ts:36:3)
    at async Object.mutationFn (partners.tsx:125:24)
Mt.forEach.n.<computed> @ Console.js:61
45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/api/partners:1 
            
            
           Failed to load resource: the server responded with a status of 400 (Bad Request)
Console.js:61 Error creating partner: Error: 400: {"message":"Invalid input","errors":[{"code":"invalid_type","expected":"date","received":"string","path":["partnership_start_date"],"message":"Expected date, received string"},{"code":"invalid_type","expected":"date","received":"string","path":["partnership_end_date"],"message":"Expected date, received string"}]}
    at throwIfResNotOk (queryClient.ts:6:11)
    at async apiRequest (queryClient.ts:36:3)
    at async Object.mutationFn (partners.tsx:125:24)
Mt.forEach.n.<computed> @ Console.js:61

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
