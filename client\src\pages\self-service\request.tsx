import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/lib/auth";
import { useLocation } from "wouter";
import { ArrowLeft, Plus, X } from "lucide-react";
import { apiRequest } from "@/lib/api";

interface PermissionOption {
  code: string;
  name: string;
  category: string;
}

interface RoleOption {
  id: number;
  name: string;
  description: string;
}

export default function PermissionRequestForm() {
  const { getCurrentUser } = useAuth();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const user = getCurrentUser();

  const [formData, setFormData] = useState({
    requestType: '',
    title: '',
    description: '',
    businessJustification: '',
    priority: 'medium',
    urgencyReason: '',
    requestedPermissions: [] as string[],
    requestedRoleId: '',
    temporaryAccess: false,
    accessStartDate: '',
    accessEndDate: '',
    department: '',
  });

  // Fetch available permissions
  const { data: availablePermissions } = useQuery<PermissionOption[]>({
    queryKey: ['/api/self-service/available-permissions'],
    enabled: !!user?.id,
  });

  // Fetch available roles
  const { data: availableRoles } = useQuery<RoleOption[]>({
    queryKey: ['/api/self-service/available-roles'],
    enabled: !!user?.id,
  });

  // Submit request mutation
  const submitRequestMutation = useMutation({
    mutationFn: async (requestData: any) => {
      const response = await apiRequest('POST', '/api/self-service/requests', requestData);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit request');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Request submitted",
        description: "Your permission request has been submitted successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/self-service/requests'] });
      queryClient.invalidateQueries({ queryKey: ['/api/self-service/access-status'] });
      navigate('/self-service');
    },
    onError: (error: Error) => {
      toast({
        title: "Submission failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.requestType || !formData.title || !formData.businessJustification) {
      toast({
        title: "Validation error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    const requestData = {
      ...formData,
      requestedRoleId: formData.requestedRoleId ? parseInt(formData.requestedRoleId) : undefined,
      accessStartDate: formData.accessStartDate || undefined,
      accessEndDate: formData.accessEndDate || undefined,
    };

    submitRequestMutation.mutate(requestData);
  };

  const handlePermissionToggle = (permissionCode: string) => {
    setFormData(prev => ({
      ...prev,
      requestedPermissions: prev.requestedPermissions.includes(permissionCode)
        ? prev.requestedPermissions.filter(p => p !== permissionCode)
        : [...prev.requestedPermissions, permissionCode]
    }));
  };

  const removePermission = (permissionCode: string) => {
    setFormData(prev => ({
      ...prev,
      requestedPermissions: prev.requestedPermissions.filter(p => p !== permissionCode)
    }));
  };

  const getPermissionName = (code: string) => {
    return availablePermissions?.find(p => p.code === code)?.name || code;
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/self-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">Submit Permission Request</h1>
      </div>

      <Card className="max-w-4xl">
        <CardHeader>
          <CardTitle>New Permission Request</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Request Type */}
            <div className="space-y-2">
              <Label htmlFor="requestType">Request Type *</Label>
              <Select
                value={formData.requestType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, requestType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select request type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="permission_grant">Grant Permissions</SelectItem>
                  <SelectItem value="permission_revoke">Revoke Permissions</SelectItem>
                  <SelectItem value="role_change">Role Change</SelectItem>
                  <SelectItem value="access_extension">Access Extension</SelectItem>
                  <SelectItem value="temporary_access">Temporary Access</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Request Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Brief description of what you're requesting"
                maxLength={255}
              />
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Detailed description of your request"
                rows={3}
              />
            </div>

            {/* Business Justification */}
            <div className="space-y-2">
              <Label htmlFor="businessJustification">Business Justification *</Label>
              <Textarea
                id="businessJustification"
                value={formData.businessJustification}
                onChange={(e) => setFormData(prev => ({ ...prev, businessJustification: e.target.value }))}
                placeholder="Explain why you need this access and how it relates to your job responsibilities"
                rows={4}
                minLength={10}
              />
            </div>

            {/* Priority and Urgency */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {(formData.priority === 'high' || formData.priority === 'urgent') && (
                <div className="space-y-2">
                  <Label htmlFor="urgencyReason">Urgency Reason</Label>
                  <Input
                    id="urgencyReason"
                    value={formData.urgencyReason}
                    onChange={(e) => setFormData(prev => ({ ...prev, urgencyReason: e.target.value }))}
                    placeholder="Why is this urgent?"
                  />
                </div>
              )}
            </div>

            {/* Requested Permissions */}
            {(formData.requestType === 'permission_grant' || formData.requestType === 'permission_revoke') && (
              <div className="space-y-4">
                <Label>Requested Permissions</Label>

                {/* Selected Permissions */}
                {formData.requestedPermissions.length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm">Selected Permissions:</Label>
                    <div className="flex flex-wrap gap-2">
                      {formData.requestedPermissions.map((permission) => (
                        <Badge key={permission} variant="secondary" className="flex items-center gap-1">
                          {getPermissionName(permission)}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removePermission(permission)}
                          />
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Available Permissions */}
                <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
                  <Label className="text-sm mb-2 block">Available Permissions:</Label>
                  {availablePermissions ? (
                    <div className="space-y-2">
                      {Object.entries(
                        availablePermissions.reduce((acc, permission) => {
                          if (!acc[permission.category]) acc[permission.category] = [];
                          acc[permission.category].push(permission);
                          return acc;
                        }, {} as Record<string, PermissionOption[]>)
                      ).map(([category, permissions]) => (
                        <div key={category} className="space-y-1">
                          <Label className="text-xs font-medium text-muted-foreground uppercase">
                            {category}
                          </Label>
                          {permissions.map((permission) => (
                            <div key={permission.code} className="flex items-center space-x-2">
                              <Checkbox
                                id={permission.code}
                                checked={formData.requestedPermissions.includes(permission.code)}
                                onCheckedChange={() => handlePermissionToggle(permission.code)}
                              />
                              <Label htmlFor={permission.code} className="text-sm">
                                {permission.name}
                              </Label>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Loading permissions...</p>
                  )}
                </div>
              </div>
            )}

            {/* Requested Role */}
            {formData.requestType === 'role_change' && (
              <div className="space-y-2">
                <Label htmlFor="requestedRole">Requested Role</Label>
                <Select
                  value={formData.requestedRoleId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, requestedRoleId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles?.map((role) => (
                      <SelectItem key={role.id} value={role.id.toString()}>
                        <div>
                          <div className="font-medium">{role.name}</div>
                          <div className="text-sm text-muted-foreground">{role.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Temporary Access */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="temporaryAccess"
                  checked={formData.temporaryAccess}
                  onCheckedChange={(checked) =>
                    setFormData(prev => ({ ...prev, temporaryAccess: checked as boolean }))
                  }
                />
                <Label htmlFor="temporaryAccess">This is a temporary access request</Label>
              </div>

              {formData.temporaryAccess && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="accessStartDate">Access Start Date</Label>
                    <Input
                      id="accessStartDate"
                      type="date"
                      value={formData.accessStartDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, accessStartDate: e.target.value }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="accessEndDate">Access End Date</Label>
                    <Input
                      id="accessEndDate"
                      type="date"
                      value={formData.accessEndDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, accessEndDate: e.target.value }))}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Department */}
            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Input
                id="department"
                value={formData.department}
                onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                placeholder="Your department or team"
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/self-service')}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={submitRequestMutation.isPending}
              >
                {submitRequestMutation.isPending ? 'Submitting...' : 'Submit Request'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
