import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TemporaryPermissionService } from '../temporaryPermissionService';

// Mock the database
vi.mock('../../db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
    leftJoin: vi.fn().mockReturnThis(),
  }
}));

// Mock error logger
vi.mock('../../utils/errorLogger', () => ({
  default: {
    logInfo: vi.fn(),
    logError: vi.fn(),
  }
}));

describe('TemporaryPermissionService', () => {
  let service: TemporaryPermissionService;
  let mockDb: any;

  beforeEach(() => {
    service = new TemporaryPermissionService();
    mockDb = vi.mocked(require('../../db').db);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('grantTemporaryPermission', () => {
    it('should grant temporary permission successfully', async () => {
      // Mock validateUsersAndPermission
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Users exist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists

      // Mock getActiveTemporaryPermission check
      mockDb.select.mockResolvedValueOnce([]); // No existing permission

      // Mock insert operation
      const mockTempPermission = {
        id: 1,
        user_id: 1,
        permission_id: 1,
        granted_by: 2,
        reason: 'Test reason',
        granted_at: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
        revoked_at: null,
        revoked_by: null,
        revoke_reason: null,
        is_emergency: false,
        created_at: new Date(),
        updated_at: new Date()
      };
      mockDb.insert.mockResolvedValueOnce([mockTempPermission]);

      const result = await service.grantTemporaryPermission(1, 1, 2, 'Test reason', 24, false);

      expect(result).toEqual(mockTempPermission);
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it('should throw error for existing active permission', async () => {
      // Mock validateUsersAndPermission
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Users exist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists

      // Mock existing active permission
      mockDb.select.mockResolvedValueOnce([{ id: 1, user_id: 1, permission_id: 1 }]);

      await expect(service.grantTemporaryPermission(1, 1, 2, 'Test reason')).rejects.toThrow('User already has active temporary permission for this permission');
    });

    it('should throw error for non-existent users', async () => {
      // Mock validateUsersAndPermission - only one user exists
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Only one user exists

      await expect(service.grantTemporaryPermission(1, 1, 2, 'Test reason')).rejects.toThrow('Users not found: 2');
    });

    it('should throw error for non-existent permission', async () => {
      // Mock validateUsersAndPermission - users exist but permission doesn't
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Users exist
      mockDb.select.mockResolvedValueOnce([]); // Permission doesn't exist

      await expect(service.grantTemporaryPermission(1, 1, 2, 'Test reason')).rejects.toThrow('Permission not found: 1');
    });
  });

  describe('revokeTemporaryPermission', () => {
    it('should revoke temporary permission successfully', async () => {
      const mockRevoked = {
        id: 1,
        user_id: 1,
        permission_id: 1,
        granted_by: 2,
        reason: 'Test reason',
        granted_at: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
        revoked_at: new Date(),
        revoked_by: 2,
        revoke_reason: 'Test revoke reason',
        is_emergency: false,
        created_at: new Date(),
        updated_at: new Date()
      };
      mockDb.update.mockResolvedValueOnce([mockRevoked]);

      const result = await service.revokeTemporaryPermission(1, 2, 'Test revoke reason');

      expect(result).toEqual(mockRevoked);
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('should return null when permission not found', async () => {
      mockDb.update.mockResolvedValueOnce([]);

      const result = await service.revokeTemporaryPermission(1, 2, 'Test revoke reason');

      expect(result).toBeNull();
    });
  });

  describe('getActiveTemporaryPermissions', () => {
    it('should return active temporary permissions', async () => {
      const mockPermissions = [
        {
          id: 1,
          user_id: 1,
          permission_id: 1,
          granted_by: 2,
          reason: 'Test reason',
          granted_at: new Date(),
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
          revoked_at: null,
          revoked_by: null,
          revoke_reason: null,
          is_emergency: false,
          created_at: new Date(),
          updated_at: new Date(),
          username: 'testuser',
          email: '<EMAIL>',
          permission_code: 'test_permission',
          permission_name: 'Test Permission',
          granted_by_username: 'admin'
        }
      ];
      mockDb.select.mockResolvedValueOnce(mockPermissions);

      const result = await service.getActiveTemporaryPermissions(1);

      expect(result).toEqual(mockPermissions);
    });

    it('should filter by permission ID when provided', async () => {
      const mockPermissions = [
        {
          id: 1,
          user_id: 1,
          permission_id: 1,
          granted_by: 2,
          reason: 'Test reason',
          granted_at: new Date(),
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
          revoked_at: null,
          revoked_by: null,
          revoke_reason: null,
          is_emergency: false,
          created_at: new Date(),
          updated_at: new Date(),
          username: 'testuser',
          email: '<EMAIL>',
          permission_code: 'test_permission',
          permission_name: 'Test Permission',
          granted_by_username: 'admin'
        }
      ];
      mockDb.select.mockResolvedValueOnce(mockPermissions);

      const result = await service.getActiveTemporaryPermissions(1, 1);

      expect(result).toEqual(mockPermissions);
    });
  });

  describe('hasTemporaryPermission', () => {
    it('should return true when user has active temporary permission', async () => {
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]);

      const result = await service.hasTemporaryPermission(1, 'test_permission');

      expect(result).toBe(true);
    });

    it('should return false when user does not have temporary permission', async () => {
      mockDb.select.mockResolvedValueOnce([]);

      const result = await service.hasTemporaryPermission(1, 'test_permission');

      expect(result).toBe(false);
    });

    it('should return false on database error', async () => {
      mockDb.select.mockRejectedValueOnce(new Error('Database error'));

      const result = await service.hasTemporaryPermission(1, 'test_permission');

      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredPermissions', () => {
    it('should cleanup expired permissions and return count', async () => {
      mockDb.update.mockResolvedValueOnce({ rowCount: 5 });

      const result = await service.cleanupExpiredPermissions();

      expect(result).toBe(5);
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('should return 0 when no permissions to cleanup', async () => {
      mockDb.update.mockResolvedValueOnce({ rowCount: 0 });

      const result = await service.cleanupExpiredPermissions();

      expect(result).toBe(0);
    });
  });

  describe('createElevationRequest', () => {
    it('should create elevation request successfully', async () => {
      const requestData = {
        user_id: 1,
        requested_by: 2,
        permission_id: 1,
        reason: 'Need access for urgent task',
        justification: 'Customer emergency',
        priority: 'high' as const,
        duration_hours: 8,
        is_emergency: false
      };

      // Mock validateUsersAndPermission
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Users exist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists

      // Mock getPendingElevationRequest check
      mockDb.select.mockResolvedValueOnce([]); // No existing request

      // Mock insert operation
      const mockRequest = {
        id: 1,
        ...requestData,
        status: 'pending',
        requested_at: new Date(),
        reviewed_at: null,
        reviewed_by: null,
        review_notes: null,
        approved_until: null,
        emergency_contact: null,
        created_at: new Date(),
        updated_at: new Date()
      };
      mockDb.insert.mockResolvedValueOnce([mockRequest]);

      const result = await service.createElevationRequest(requestData);

      expect(result).toEqual(mockRequest);
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it('should throw error for existing pending request', async () => {
      const requestData = {
        user_id: 1,
        requested_by: 2,
        permission_id: 1,
        reason: 'Need access for urgent task',
        priority: 'high' as const,
        duration_hours: 8
      };

      // Mock validateUsersAndPermission
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Users exist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists

      // Mock existing pending request
      mockDb.select.mockResolvedValueOnce([{ id: 1, user_id: 1, permission_id: 1, status: 'pending' }]);

      await expect(service.createElevationRequest(requestData)).rejects.toThrow('User already has pending elevation request for this permission');
    });
  });

  describe('reviewElevationRequest', () => {
    it('should approve elevation request and create temporary permission', async () => {
      const mockRequest = {
        id: 1,
        user_id: 1,
        requested_by: 2,
        permission_id: 1,
        reason: 'Need access for urgent task',
        duration_hours: 8,
        is_emergency: false,
        status: 'pending'
      };

      // Mock getElevationRequest
      mockDb.select.mockResolvedValueOnce([mockRequest]);

      // Mock validateUsersAndPermission for grantTemporaryPermission
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 3 }]); // Users exist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists

      // Mock getActiveTemporaryPermission check
      mockDb.select.mockResolvedValueOnce([]); // No existing permission

      // Mock temporary permission creation
      const mockTempPermission = {
        id: 1,
        user_id: 1,
        permission_id: 1,
        granted_by: 3,
        reason: 'Approved elevation request: Need access for urgent task',
        granted_at: new Date(),
        expires_at: new Date(Date.now() + 8 * 60 * 60 * 1000),
        revoked_at: null,
        revoked_by: null,
        revoke_reason: null,
        is_emergency: false,
        created_at: new Date(),
        updated_at: new Date()
      };
      mockDb.insert.mockResolvedValueOnce([mockTempPermission]);

      // Mock update operation
      const mockUpdated = {
        ...mockRequest,
        status: 'approved',
        reviewed_at: new Date(),
        reviewed_by: 3,
        review_notes: 'Approved for urgent task',
        approved_until: new Date(Date.now() + 8 * 60 * 60 * 1000),
        updated_at: new Date()
      };
      mockDb.update.mockResolvedValueOnce([mockUpdated]);

      const result = await service.reviewElevationRequest(1, 3, 'approved', 'Approved for urgent task');

      expect(result).toEqual(mockUpdated);
      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.insert).toHaveBeenCalled(); // Temporary permission created
    });

    it('should deny elevation request without creating temporary permission', async () => {
      const mockRequest = {
        id: 1,
        user_id: 1,
        requested_by: 2,
        permission_id: 1,
        reason: 'Need access for urgent task',
        duration_hours: 8,
        is_emergency: false,
        status: 'pending'
      };

      // Mock update operation
      const mockUpdated = {
        ...mockRequest,
        status: 'denied',
        reviewed_at: new Date(),
        reviewed_by: 3,
        review_notes: 'Request denied - insufficient justification',
        updated_at: new Date()
      };
      mockDb.update.mockResolvedValueOnce([mockUpdated]);

      const result = await service.reviewElevationRequest(1, 3, 'denied', 'Request denied - insufficient justification');

      expect(result).toEqual(mockUpdated);
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('should return null when request not found', async () => {
      mockDb.update.mockResolvedValueOnce([]);

      const result = await service.reviewElevationRequest(999, 3, 'approved');

      expect(result).toBeNull();
    });
  });

  describe('getPendingElevationRequests', () => {
    it('should return pending elevation requests ordered by priority', async () => {
      const mockRequests = [
        {
          id: 1,
          user_id: 1,
          requested_by: 2,
          permission_id: 1,
          reason: 'Emergency access needed',
          priority: 'emergency',
          status: 'pending',
          is_emergency: true,
          requested_at: new Date(),
          username: 'testuser',
          email: '<EMAIL>',
          requested_by_username: 'requester',
          permission_code: 'emergency_access',
          permission_name: 'Emergency Access'
        },
        {
          id: 2,
          user_id: 2,
          requested_by: 3,
          permission_id: 2,
          reason: 'High priority task',
          priority: 'high',
          status: 'pending',
          is_emergency: false,
          requested_at: new Date(),
          username: 'user2',
          email: '<EMAIL>',
          requested_by_username: 'requester2',
          permission_code: 'loan_approve',
          permission_name: 'Loan Approval'
        }
      ];
      mockDb.select.mockResolvedValueOnce(mockRequests);

      const result = await service.getPendingElevationRequests();

      expect(result).toEqual(mockRequests);
    });

    it('should filter by priority when provided', async () => {
      const mockRequests = [
        {
          id: 1,
          user_id: 1,
          requested_by: 2,
          permission_id: 1,
          reason: 'Emergency access needed',
          priority: 'emergency',
          status: 'pending',
          is_emergency: true,
          requested_at: new Date(),
          username: 'testuser',
          email: '<EMAIL>',
          requested_by_username: 'requester',
          permission_code: 'emergency_access',
          permission_name: 'Emergency Access'
        }
      ];
      mockDb.select.mockResolvedValueOnce(mockRequests);

      const result = await service.getPendingElevationRequests('emergency');

      expect(result).toEqual(mockRequests);
    });
  });

  describe('Emergency Access', () => {
    describe('grantEmergencyAccess', () => {
      it('should grant emergency access successfully', async () => {
        // Mock permission lookup
        mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists

        // Mock validateUsersAndPermission for grantTemporaryPermission
        mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Users exist
        mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Permission exists

        // Mock getActiveTemporaryPermission check
        mockDb.select.mockResolvedValueOnce([]); // No existing permission

        // Mock temporary permission creation
        const mockTempPermission = {
          id: 1,
          user_id: 1,
          permission_id: 1,
          granted_by: 2,
          reason: 'Emergency access: System outage',
          granted_at: new Date(),
          expires_at: new Date(Date.now() + 4 * 60 * 60 * 1000),
          revoked_at: null,
          revoked_by: null,
          revoke_reason: null,
          is_emergency: true,
          created_at: new Date(),
          updated_at: new Date()
        };
        mockDb.insert.mockResolvedValueOnce([mockTempPermission]);

        const result = await service.grantEmergencyAccess(1, 'emergency_system_access', 'System outage', 2, 4);

        expect(result).toEqual(mockTempPermission);
        expect(result.is_emergency).toBe(true);
      });

      it('should throw error for non-existent permission', async () => {
        // Mock permission lookup - permission not found
        mockDb.select.mockResolvedValueOnce([]);

        await expect(service.grantEmergencyAccess(1, 'non_existent_permission', 'Emergency', 2)).rejects.toThrow('Permission not found: non_existent_permission');
      });
    });

    describe('hasEmergencyAccess', () => {
      it('should return true when user has active emergency access', async () => {
        mockDb.select.mockResolvedValueOnce([{ id: 1 }]);

        const result = await service.hasEmergencyAccess(1, 'emergency_system_access');

        expect(result).toBe(true);
      });

      it('should return false when user does not have emergency access', async () => {
        mockDb.select.mockResolvedValueOnce([]);

        const result = await service.hasEmergencyAccess(1, 'emergency_system_access');

        expect(result).toBe(false);
      });
    });

    describe('logEmergencyAccess', () => {
      it('should log emergency access usage successfully', async () => {
        const context = {
          userId: 1,
          permissionCode: 'emergency_system_access',
          actionPerformed: 'System restart',
          emergencyReason: 'Critical system failure',
          sessionId: 'session123',
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0',
          resourceAccessed: '/admin/system/restart',
          additionalContext: { severity: 'critical' }
        };

        mockDb.insert.mockResolvedValueOnce([{}]);

        await service.logEmergencyAccess(context);

        expect(mockDb.insert).toHaveBeenCalled();
      });
    });

    describe('getEmergencyAccessLogs', () => {
      it('should return emergency access logs for user', async () => {
        const mockLogs = [
          {
            id: 1,
            user_id: 1,
            permission_code: 'emergency_system_access',
            action_performed: 'System restart',
            emergency_reason: 'Critical system failure',
            access_granted_at: new Date(),
            session_id: 'session123',
            ip_address: '***********',
            user_agent: 'Mozilla/5.0',
            resource_accessed: '/admin/system/restart',
            additional_context: { severity: 'critical' },
            created_at: new Date()
          }
        ];
        mockDb.select.mockResolvedValueOnce(mockLogs);

        const result = await service.getEmergencyAccessLogs(1, 50);

        expect(result).toEqual(mockLogs);
      });
    });

    describe('getAllEmergencyAccessLogs', () => {
      it('should return all emergency access logs with user details', async () => {
        const mockLogs = [
          {
            id: 1,
            user_id: 1,
            username: 'testuser',
            email: '<EMAIL>',
            permission_code: 'emergency_system_access',
            action_performed: 'System restart',
            emergency_reason: 'Critical system failure',
            access_granted_at: new Date(),
            session_id: 'session123',
            ip_address: '***********',
            user_agent: 'Mozilla/5.0',
            resource_accessed: '/admin/system/restart',
            additional_context: { severity: 'critical' },
            created_at: new Date()
          }
        ];
        mockDb.select.mockResolvedValueOnce(mockLogs);

        const result = await service.getAllEmergencyAccessLogs(100);

        expect(result).toEqual(mockLogs);
      });
    });
  });
});
