-- Migration: Rename phone column to mobile_number in users table
-- Description: Complete refactoring of phone field to mobile_number throughout the system
-- Date: 2025-01-27

BEGIN;

-- Step 1: Rename phone column to mobile_number in users table
ALTER TABLE "users" RENAME COLUMN "phone" TO "mobile_number";

-- Step 2: Add constraint for mobile number format validation (+91 followed by exactly 10 digits)
ALTER TABLE "users" ADD CONSTRAINT "users_mobile_number_format_check" 
CHECK (mobile_number IS NULL OR mobile_number ~ '^\+91[0-9]{10}$');

-- Step 3: Update any existing data to ensure it follows the format (if needed)
-- This will set invalid mobile numbers to NULL - adjust as needed for your data
UPDATE "users" 
SET mobile_number = NULL 
WHERE mobile_number IS NOT NULL 
AND mobile_number !~ '^\+91[0-9]{10}$';

-- Step 4: Add comment to document the format requirement
COMMENT ON COLUMN "users"."mobile_number" IS 'Mobile number in format +91 followed by exactly 10 digits (e.g., +919876543210)';

COMMIT;
