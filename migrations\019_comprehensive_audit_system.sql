-- Migration: Comprehensive Audit System
-- Description: Create comprehensive audit tables for permission usage, data access, and change tracking
-- Date: 2025-01-27

-- Create audit action enum
CREATE TYPE audit_action AS ENUM (
  'granted', 'revoked', 'used', 'denied', 'modified', 'viewed', 'created', 'updated', 'deleted', 'exported'
);

-- Create audit entity type enum
CREATE TYPE audit_entity_type AS ENUM (
  'permission', 'role', 'user', 'customer', 'loan', 'payment', 'collection', 'agent', 'company', 'session', 'data'
);

-- Permission audit logs table for tracking permission usage and changes
CREATE TABLE IF NOT EXISTS "permission_audit_logs" (
  "id" SERIAL PRIMARY KEY,
  "audit_id" varchar(255) NOT NULL UNIQUE,
  "user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  "session_id" varchar(255),
  
  -- Permission details
  "permission_code" varchar(100) NOT NULL,
  "permission_name" varchar(255),
  "action" audit_action NOT NULL,
  "result" varchar(50) NOT NULL, -- 'success', 'denied', 'error', 'expired'
  
  -- Context information
  "resource_type" varchar(100), -- 'loan', 'customer', 'payment', etc.
  "resource_id" varchar(100), -- ID of the resource being accessed
  "operation_type" varchar(100), -- 'create', 'read', 'update', 'delete', 'approve', etc.
  "operation_details" text, -- Description of what was done
  
  -- Request context
  "endpoint" varchar(255),
  "method" varchar(10),
  "ip_address" inet,
  "user_agent" text,
  "referer" text,
  
  -- Additional context
  "metadata" jsonb DEFAULT '{}', -- Additional context data
  "risk_score" integer DEFAULT 0, -- 0-100 risk assessment
  "is_sensitive_operation" boolean DEFAULT false,
  "compliance_flags" jsonb DEFAULT '[]', -- Compliance-related flags
  
  -- Timing
  "timestamp" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "response_time_ms" integer,
  
  -- Audit trail
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Data access audit logs table for tracking data access and modifications
CREATE TABLE IF NOT EXISTS "data_access_audit_logs" (
  "id" SERIAL PRIMARY KEY,
  "audit_id" varchar(255) NOT NULL UNIQUE,
  "user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  "session_id" varchar(255),
  
  -- Data access details
  "entity_type" audit_entity_type NOT NULL,
  "entity_id" varchar(100) NOT NULL,
  "action" audit_action NOT NULL,
  "table_name" varchar(100) NOT NULL,
  "field_names" jsonb DEFAULT '[]', -- Fields that were accessed/modified
  
  -- Data details
  "old_values" jsonb, -- Previous values for updates
  "new_values" jsonb, -- New values for creates/updates
  "query_filters" jsonb, -- Filters used in the query
  "record_count" integer DEFAULT 1, -- Number of records affected
  
  -- Access control
  "data_scope" varchar(100), -- 'branch', 'department', 'company', 'personal'
  "field_access_level" varchar(50), -- 'full', 'masked', 'restricted'
  "sensitive_fields_accessed" jsonb DEFAULT '[]', -- List of sensitive fields accessed
  
  -- Request context
  "endpoint" varchar(255),
  "method" varchar(10),
  "ip_address" inet,
  "user_agent" text,
  
  -- Additional context
  "metadata" jsonb DEFAULT '{}',
  "compliance_flags" jsonb DEFAULT '[]',
  "retention_period_days" integer, -- How long this audit log should be retained
  
  -- Timing
  "timestamp" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Permission change logs table for tracking permission grants, revokes, and modifications
CREATE TABLE IF NOT EXISTS "permission_change_logs" (
  "id" SERIAL PRIMARY KEY,
  "audit_id" varchar(255) NOT NULL UNIQUE,
  "changed_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "target_user_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "target_role_id" integer REFERENCES "custom_roles"("id") ON DELETE SET NULL,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Change details
  "change_type" varchar(50) NOT NULL, -- 'permission_grant', 'permission_revoke', 'role_assign', 'role_remove', 'role_modify'
  "permission_code" varchar(100),
  "permission_name" varchar(255),
  "role_name" varchar(255),
  
  -- Change context
  "action" audit_action NOT NULL,
  "reason" text, -- Reason for the change
  "approval_workflow_id" integer, -- If change required approval
  "is_temporary" boolean DEFAULT false,
  "expires_at" timestamp, -- For temporary permissions
  
  -- Before/after state
  "previous_state" jsonb, -- Previous permission/role state
  "new_state" jsonb, -- New permission/role state
  "change_summary" text, -- Human-readable summary of changes
  
  -- Request context
  "ip_address" inet,
  "user_agent" text,
  "session_id" varchar(255),
  
  -- Additional context
  "metadata" jsonb DEFAULT '{}',
  "compliance_flags" jsonb DEFAULT '[]',
  "risk_assessment" varchar(50), -- 'low', 'medium', 'high', 'critical'
  
  -- Timing
  "timestamp" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_permission_audit_logs_user_id ON permission_audit_logs(user_id);
CREATE INDEX idx_permission_audit_logs_company_id ON permission_audit_logs(company_id);
CREATE INDEX idx_permission_audit_logs_permission_code ON permission_audit_logs(permission_code);
CREATE INDEX idx_permission_audit_logs_timestamp ON permission_audit_logs(timestamp);
CREATE INDEX idx_permission_audit_logs_action ON permission_audit_logs(action);
CREATE INDEX idx_permission_audit_logs_result ON permission_audit_logs(result);
CREATE INDEX idx_permission_audit_logs_resource ON permission_audit_logs(resource_type, resource_id);
CREATE INDEX idx_permission_audit_logs_sensitive ON permission_audit_logs(is_sensitive_operation) WHERE is_sensitive_operation = true;

CREATE INDEX idx_data_access_audit_logs_user_id ON data_access_audit_logs(user_id);
CREATE INDEX idx_data_access_audit_logs_company_id ON data_access_audit_logs(company_id);
CREATE INDEX idx_data_access_audit_logs_entity ON data_access_audit_logs(entity_type, entity_id);
CREATE INDEX idx_data_access_audit_logs_timestamp ON data_access_audit_logs(timestamp);
CREATE INDEX idx_data_access_audit_logs_action ON data_access_audit_logs(action);
CREATE INDEX idx_data_access_audit_logs_table ON data_access_audit_logs(table_name);
CREATE INDEX idx_data_access_audit_logs_sensitive ON data_access_audit_logs USING GIN(sensitive_fields_accessed);

CREATE INDEX idx_permission_change_logs_changed_by ON permission_change_logs(changed_by);
CREATE INDEX idx_permission_change_logs_target_user ON permission_change_logs(target_user_id);
CREATE INDEX idx_permission_change_logs_target_role ON permission_change_logs(target_role_id);
CREATE INDEX idx_permission_change_logs_company_id ON permission_change_logs(company_id);
CREATE INDEX idx_permission_change_logs_timestamp ON permission_change_logs(timestamp);
CREATE INDEX idx_permission_change_logs_change_type ON permission_change_logs(change_type);
CREATE INDEX idx_permission_change_logs_permission ON permission_change_logs(permission_code);
CREATE INDEX idx_permission_change_logs_temporary ON permission_change_logs(is_temporary) WHERE is_temporary = true;

-- Add comments for documentation
COMMENT ON TABLE permission_audit_logs IS 'Comprehensive audit trail for permission usage and access attempts';
COMMENT ON TABLE data_access_audit_logs IS 'Detailed audit trail for data access and modifications';
COMMENT ON TABLE permission_change_logs IS 'Audit trail for permission and role changes';

COMMENT ON COLUMN permission_audit_logs.audit_id IS 'Unique identifier for this audit event';
COMMENT ON COLUMN permission_audit_logs.result IS 'Result of the permission check: success, denied, error, expired';
COMMENT ON COLUMN permission_audit_logs.is_sensitive_operation IS 'Whether this operation involved sensitive data or high-risk actions';
COMMENT ON COLUMN permission_audit_logs.compliance_flags IS 'Array of compliance-related flags for regulatory reporting';

COMMENT ON COLUMN data_access_audit_logs.field_access_level IS 'Level of access granted: full, masked, or restricted';
COMMENT ON COLUMN data_access_audit_logs.sensitive_fields_accessed IS 'List of sensitive fields that were accessed in this operation';
COMMENT ON COLUMN data_access_audit_logs.retention_period_days IS 'How long this audit record should be retained for compliance';

COMMENT ON COLUMN permission_change_logs.change_type IS 'Type of change: permission_grant, permission_revoke, role_assign, role_remove, role_modify';
COMMENT ON COLUMN permission_change_logs.approval_workflow_id IS 'ID of approval workflow if change required approval';
COMMENT ON COLUMN permission_change_logs.risk_assessment IS 'Risk level of this permission change';
