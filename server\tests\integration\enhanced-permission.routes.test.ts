import { describe, it, expect, beforeEach, vi } from 'vitest';
import express from 'express';
import request from 'supertest';
import { testUsers, testData, mockDatabase } from './setup';

// Mock the database
const mockDb = mockDatabase();
vi.mock('../../db', () => ({
  db: mockDb
}));

// Mock the schema
vi.mock('@shared/schema', () => ({
  permissions: { id: 'id', code: 'code', category: 'category' },
  customRoles: { id: 'id' },
  rolePermissions: { role_id: 'role_id', permission_id: 'permission_id' },
  userRoles: { user_id: 'user_id', role_id: 'role_id' },
  users: { id: 'id', role: 'role' }
}));

// Mock the enhanced permission service
vi.mock('../../services/enhancedPermissionService', () => ({
  EnhancedPermissionService: vi.fn().mockImplementation(() => ({
    getUserPermissions: vi.fn(),
    checkPermissionWithContext: vi.fn(),
    checkLoanCreationPermission: vi.fn(),
    checkCustomerDataAccess: vi.fn()
  }))
}));

describe('Enhanced Permission Routes Integration Tests', () => {
  let app: express.Express;

  beforeEach(async () => {
    // Create a fresh Express app for each test
    app = express();
    app.use(express.json());

    // Mock authentication middleware
    app.use((req: any, res, next) => {
      req.user = testUsers.saasAdmin; // Default to admin user
      next();
    });

    // Import and register routes after mocking
    const { registerEnhancedPermissionRoutes } = await import('../../routes/enhanced-permission.routes');
    registerEnhancedPermissionRoutes(app);
  });

  describe('GET /api/permissions/categories', () => {
    it('should return permission categories for authenticated admin user', async () => {
      // Mock database response
      mockDb.select.mockResolvedValueOnce([
        { id: 1, code: 'loan_create_basic', category: 'loan_management', name: 'Basic Loan Creation' },
        { id: 2, code: 'customer_view_basic', category: 'customer_management', name: 'Basic Customer View' },
        { id: 3, code: 'payment_process_manual', category: 'financial_management', name: 'Manual Payment Processing' }
      ]);

      const response = await request(app)
        .get('/api/permissions/categories')
        .expect(200);

      expect(response.body).toHaveProperty('loan_management');
      expect(response.body).toHaveProperty('customer_management');
      expect(response.body).toHaveProperty('financial_management');
      expect(response.body.loan_management).toHaveLength(1);
      expect(response.body.loan_management[0]).toMatchObject({
        id: 1,
        code: 'loan_create_basic',
        category: 'loan_management'
      });
    });

    it('should return 403 for non-admin user', async () => {
      // Override user for this test
      app.use((req: any, res, next) => {
        req.user = testUsers.limitedUser;
        next();
      });

      const response = await request(app)
        .get('/api/permissions/categories')
        .expect(403);

      expect(response.body).toHaveProperty('message');
    });

    it('should handle database errors gracefully', async () => {
      mockDb.select.mockRejectedValueOnce(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/permissions/categories')
        .expect(500);

      expect(response.body).toHaveProperty('message', 'Server error');
    });
  });

  describe('POST /api/permissions/check', () => {
    it('should check permission with context successfully', async () => {
      const { EnhancedPermissionService } = await import('../../services/enhancedPermissionService');
      const mockService = new EnhancedPermissionService();
      vi.mocked(mockService.checkPermissionWithContext).mockResolvedValueOnce({
        allowed: true,
        reason: 'Permission granted'
      });

      const requestBody = {
        user_id: 1,
        permission_type: 'loan_create',
        operation_type: 'basic',
        context: {
          amount: 5000,
          company_id: 1
        }
      };

      const response = await request(app)
        .post('/api/permissions/check')
        .send(requestBody)
        .expect(200);

      expect(response.body).toHaveProperty('allowed', true);
    });

    it('should return 400 for missing required fields', async () => {
      const requestBody = {
        permission_type: 'loan_create'
        // Missing user_id
      };

      const response = await request(app)
        .post('/api/permissions/check')
        .send(requestBody)
        .expect(400);

      expect(response.body).toHaveProperty('message', 'user_id and permission_type are required');
    });

    it('should handle permission denial with approval requirement', async () => {
      const { EnhancedPermissionService } = await import('../../services/enhancedPermissionService');
      const mockService = new EnhancedPermissionService();
      vi.mocked(mockService.checkPermissionWithContext).mockResolvedValueOnce({
        allowed: false,
        requiresApproval: true,
        approverRoles: ['manager', 'director'],
        reason: 'Amount exceeds user limit'
      });

      const requestBody = {
        user_id: 3,
        permission_type: 'loan_create',
        context: { amount: 50000 }
      };

      const response = await request(app)
        .post('/api/permissions/check')
        .send(requestBody)
        .expect(200);

      expect(response.body).toMatchObject({
        allowed: false,
        requiresApproval: true,
        approverRoles: ['manager', 'director'],
        reason: 'Amount exceeds user limit'
      });
    });
  });

  describe('POST /api/permissions/request', () => {
    it('should create permission request successfully', async () => {
      // Mock database operations for permission request creation
      mockDb.select.mockResolvedValueOnce([
        { id: 1, code: 'loan_create_advanced' },
        { id: 2, code: 'customer_view_financial' }
      ]);
      mockDb.insert.mockResolvedValueOnce([{
        id: 1,
        user_id: 1,
        permission_codes: ['loan_create_advanced', 'customer_view_financial'],
        justification: 'Need advanced permissions for Q4 loan processing',
        status: 'pending'
      }]);

      const requestBody = {
        permission_codes: ['loan_create_advanced', 'customer_view_financial'],
        justification: 'Need advanced permissions for Q4 loan processing',
        duration_hours: 24
      };

      const response = await request(app)
        .post('/api/permissions/request')
        .send(requestBody)
        .expect(201);

      expect(response.body).toHaveProperty('message', 'Permission request submitted successfully');
      expect(response.body).toHaveProperty('request_id');
    });

    it('should return 400 for invalid permission codes', async () => {
      const requestBody = {
        permission_codes: [], // Empty array
        justification: 'Test justification'
      };

      const response = await request(app)
        .post('/api/permissions/request')
        .send(requestBody)
        .expect(400);

      expect(response.body).toHaveProperty('message', 'permission_codes array is required');
    });

    it('should return 400 for insufficient justification', async () => {
      const requestBody = {
        permission_codes: ['loan_create_advanced'],
        justification: 'Short' // Less than 10 characters
      };

      const response = await request(app)
        .post('/api/permissions/request')
        .send(requestBody)
        .expect(400);

      expect(response.body).toHaveProperty('message', 'Justification must be at least 10 characters');
    });
  });

  describe('GET /api/permissions/analytics', () => {
    it('should return permission analytics for admin user', async () => {
      // Mock analytics data
      mockDb.select.mockResolvedValueOnce([
        { permission_code: 'loan_create_basic', usage_count: 150 },
        { permission_code: 'customer_view_basic', usage_count: 300 }
      ]);

      const response = await request(app)
        .get('/api/permissions/analytics')
        .expect(200);

      expect(response.body).toHaveProperty('permission_usage');
      expect(response.body.permission_usage).toHaveLength(2);
      expect(response.body.permission_usage[0]).toMatchObject({
        permission_code: 'loan_create_basic',
        usage_count: 150
      });
    });

    it('should return 403 for non-admin user', async () => {
      // Override user for this test
      app.use((req: any, res, next) => {
        req.user = testUsers.loanOfficer;
        next();
      });

      const response = await request(app)
        .get('/api/permissions/analytics')
        .expect(403);

      expect(response.body).toHaveProperty('message');
    });
  });
});
