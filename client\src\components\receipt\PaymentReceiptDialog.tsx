import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { PaymentReceipt } from './PaymentReceipt';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Collection, Customer, Payment } from '../../../../shared/schema';
import { Loader2 } from 'lucide-react';

interface PaymentReceiptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collectionId: number;
  companyId: number;
}

export function PaymentReceiptDialog({
  open,
  onOpenChange,
  collectionId,
  companyId
}: PaymentReceiptDialogProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [payment, setPayment] = useState<Payment | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [collection, setCollection] = useState<Collection | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (open && collectionId && companyId) {
      loadPaymentData();
    }
  }, [open, collectionId, companyId]);

  const loadPaymentData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch payments for the collection
      const paymentsResponse = await apiRequest(
        'GET', 
        `/api/collections/${collectionId}/payments?companyId=${companyId}`
      );
      
      if (!paymentsResponse.ok) {
        throw new Error('Failed to load payment data');
      }
      
      const payments = await paymentsResponse.json();
      
      if (!payments || payments.length === 0) {
        throw new Error('No payment records found for this collection');
      }
      
      // Use the most recent payment
      const latestPayment = payments[0];
      setPayment(latestPayment);
      
      // Fetch collection details
      const collectionResponse = await apiRequest(
        'GET',
        `/api/collections/${collectionId}?companyId=${companyId}`
      );
      
      if (collectionResponse.ok) {
        const collectionData = await collectionResponse.json();
        setCollection(collectionData);
        
        // Fetch customer details
        if (collectionData.customer_id) {
          const customerResponse = await apiRequest(
            'GET',
            `/api/customers/${collectionData.customer_id}?companyId=${companyId}`
          );
          
          if (customerResponse.ok) {
            const customerData = await customerResponse.json();
            setCustomer(customerData);
          }
        }
      }
      
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading payment data:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to load payment data',
        variant: 'destructive'
      });
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-lg">
        <DialogHeader>
          <DialogTitle>Payment Receipt</DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading receipt...</span>
          </div>
        ) : payment ? (
          <PaymentReceipt 
            payment={payment}
            customer={customer || undefined}
            collection={collection || undefined}
            onClose={() => onOpenChange(false)}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            No payment information found
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}