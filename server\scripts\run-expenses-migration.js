// <PERSON>ript to run the expenses table migration
import { pool } from '../db.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = path.resolve(__dirname, '../..');

async function runExpensesMigration() {
  console.log('Starting expenses table migration...');
  
  try {
    // Read the migration SQL file
    const migrationPath = path.join(rootDir, 'migrations', '004_create_expenses_table.sql');
    console.log(`Reading migration file from: ${migrationPath}`);
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    console.log('Executing migration...');
    await pool.query(migrationSQL);
    
    console.log('Migration completed successfully!');
    console.log('Expenses table and related types have been created.');
  } catch (error) {
    console.error('Migration failed:', error);
    console.error(error.stack);
  } finally {
    // Close the database connection
    await pool.end();
    process.exit(0);
  }
}

// Run the migration
runExpensesMigration();
