# TrackFina Development Best Practices

This document outlines the best practices for implementing new features and components in the TrackFina application. Following these guidelines will help prevent common issues and ensure consistent code quality.

## Component Implementation

### SelectItem Component Guidelines

- **Never use empty strings as values**: Always provide a meaningful string value for SelectItem components
  ```jsx
  // INCORRECT ❌
  <SelectItem value="">None</SelectItem>
  
  // CORRECT ✅
  <SelectItem value="none">None</SelectItem>
  ```
- When representing "null" or "empty" values, use a clear string like "none", "empty", or "default"
- Each SelectItem must have a unique value within its Select group
- Use consistent naming conventions for similar values across the application

### Form Field Handling

- Always initialize form values properly, especially for optional fields
- For fields that can be null/undefined:
  ```jsx
  // INCORRECT ❌
  value={field.value}
  
  // CORRECT ✅
  value={field.value || ''}
  ```
- Use controlled components consistently throughout the application
- Default values should be provided for all form fields

### Currency Formatting

- Always use the correct locale and currency code for formatting:
  ```js
  // INCORRECT ❌
  formatCurrency(amount, 'USD')
  
  // CORRECT ✅
  formatCurrency(amount, 'INR', 'en-IN')
  ```
- Don't use currency formatters for non-currency values (like counts)
- For consistency, use the utility function throughout the application

## Backend Implementation

### API Implementation Checklist

- Always implement all backend methods defined in the interface
- When writing a new frontend feature, verify that the corresponding backend methods exist
- For any new page that requires API access, check that:
  1. The storage method is implemented in `storage.ts`
  2. The API endpoint is properly defined in `routes.ts` 
  3. The types are correctly defined in `schema.ts`

### Database Queries

- Use SQL template strings for date comparisons and complex filters
- Always include proper error handling for database operations
- For complex queries, include comments explaining the logic
- Use descriptive variable names that clearly indicate what data is being queried

## Financial System Implementation

### Chart of Accounts Best Practices

- **Use standardized account coding**: Follow standard accounting practices with structured account codes
  ```
  1000-1999: Assets
  2000-2999: Liabilities
  3000-3999: Equity
  4000-4999: Income/Revenue
  5000-5999: Expenses
  ```

- **Include descriptive information**: All accounts should have clear descriptions to improve user understanding

- **System accounts protection**: System accounts should be protected from deletion and unauthorized modification

- **Initialize accounts properly**: Always ensure a company has the necessary system accounts before allowing financial operations

### Double-Entry Accounting Practices

- **Always balance debits and credits**: Ensure every transaction has equal debits and credits
- **Create corresponding journal entries**: Every financial action (loan, collection, expense) must generate proper journal entries
- **Use transaction references**: Always include clear reference IDs and types in journal entries

## Component Routing and Navigation

### Route Import Strategy ⚠️ CRITICAL

**Choose the correct import method based on component requirements:**

#### Use Direct Import When:
```typescript
// App.tsx - For parameter-dependent pages
import UserPermissionsPage from "@/pages/user-management/permissions/user-permissions";

<Route path="/user-management/permissions/user-permissions">
  <AppLayout allowedRoles={['saas_admin', 'owner']}>
    <UserPermissionsPage />
  </AppLayout>
</Route>
```

**Required for pages that:**
- ✅ Parse URL parameters on initial load (`?userId=17`)
- ✅ Have complex initialization logic
- ✅ Are frequently accessed (no lazy loading benefit)
- ✅ Need real-time parameter parsing

#### Use DynamicImport When:
```typescript
// App.tsx - For simple, self-contained pages
<Route path="/financial/reports">
  <AppLayout allowedRoles={['saas_admin', 'owner', 'employee']}>
    <DynamicImport path="/financial/reports/index" />
  </AppLayout>
</Route>
```

**Suitable for pages that:**
- ✅ Don't depend on URL parameters
- ✅ Are rarely accessed (lazy loading beneficial)
- ✅ Have simple, self-contained functionality
- ✅ Prioritize bundle size optimization

### URL Parameter Handling Best Practices

#### Robust Parameter Parsing Pattern:
```typescript
// Component with URL parameters
useEffect(() => {
  // Handle both wouter location and window.location for compatibility
  let searchParams;
  if (location.includes('?')) {
    searchParams = new URLSearchParams(location.split('?')[1]);
  } else {
    searchParams = new URLSearchParams(window.location.search);
  }

  const paramValue = searchParams.get('paramName');
  if (paramValue) {
    const parsedValue = parseInt(paramValue);
    if (!isNaN(parsedValue) && parsedValue > 0) {
      setStateValue(parsedValue);
    }
  } else {
    setStateValue(undefined);
  }
}, [location]);
```

#### Testing URL Parameter Pages:
- ✅ **Always test direct URL navigation**: `/page?param=value`
- ✅ **Test parameter changes**: Navigate between different parameter values
- ✅ **Test missing parameters**: Ensure graceful handling when parameters are absent
- ✅ **Test invalid parameters**: Handle malformed or invalid parameter values

### Dynamic Import Configuration

- Always update the DynamicImport component when adding new pages
- Follow the established pattern for route naming
- **⚠️ AVOID DynamicImport for parameter-dependent pages**

### Routing Configuration

- Register all new routes in App.tsx
- Maintain consistent route patterns:
  - List views: `/[module]/[entity]` or `/[module]/[entity]/index`
  - Detail views: `/[module]/[entity]/[id]`
  - Edit views: `/[module]/[entity]/[id]/edit`
  - Create views: `/[module]/[entity]/create`
- **Document which routes require direct imports vs. dynamic imports**

## Testing and Quality Assurance

### Pre-Submission Checklist

Before submitting new code, verify:
- [ ] No empty string values in SelectItem components
- [ ] All form fields properly handle null/undefined values
- [ ] Required backend methods are implemented
- [ ] All database queries use proper SQL templates for filtering
- [ ] Currency formatting is consistent (using 'INR' with 'en-IN' locale)
- [ ] Components are properly imported in DynamicImport.tsx
- [ ] Financial transactions generate proper journal entries
- [ ] Chart of accounts is properly structured with correct account codes

#### Routing and Navigation Checklist
- [ ] **URL Parameter Pages**: Use direct import instead of DynamicImport for parameter-dependent pages
- [ ] **Direct URL Navigation**: Test pages by navigating directly to URLs with parameters
- [ ] **Parameter Validation**: Ensure graceful handling of missing/invalid URL parameters
- [ ] **Route Documentation**: Document whether routes use direct import or DynamicImport and why

### Financial Testing Checklist

- [ ] Test creation of a loan and verify journal entries are correctly created
- [ ] Test loan repayment/collection and verify accounting entries match
- [ ] Verify trial balance shows balanced debits and credits
- [ ] Check that all required system accounts exist for the company
- [ ] Verify financial reports show the correct account structure and balances
- [ ] Test edge cases like zero-amount transactions and negative balances

## Error Handling

### Frontend Error Handling

- Use try/catch blocks for API requests and data processing
- Provide descriptive error messages to users
- Implement graceful fallbacks for failed data fetching
- Use the toast component for notifying users of errors

### Backend Error Handling

- Log detailed error information
- Return appropriate HTTP status codes
- Provide meaningful error messages in API responses
- Always handle potential exceptions in database operations

## Performance Considerations

- Use pagination for large datasets
- Implement appropriate caching strategies
- Optimize database queries for performance
- Lazy load components and resources when appropriate
- Consider the mobile experience - test on different screen sizes

## Pagination Best Practices

### Frontend Implementation
- Use consistent pagination components across all list views
- Implement proper loading states during pagination
- Maintain URL state for pagination parameters
- Provide clear navigation controls (first, previous, next, last)

### Backend Implementation
- Use LIMIT and OFFSET for database queries
- Return total count for proper pagination calculation
- Implement consistent pagination parameters across all endpoints
- Optimize queries with proper indexing for paginated results

## Toast Notification System

### Implementation Pattern
```typescript
import { useToast } from "@/hooks/use-toast";

const { toast } = useToast();

// Success notification
toast({
  title: "Success",
  description: "Operation completed successfully",
});

// Error notification
toast({
  title: "Error",
  description: "Operation failed",
  variant: "destructive",
});
```

### Form Validation with Toast Messages
```typescript
const validateForm = async () => {
  const isValid = await form.trigger();

  if (!isValid) {
    const errors = form.formState.errors;

    // Show specific error for each field
    if (errors.email) {
      toast({
        title: "Email Required",
        description: errors.email.message || "Please enter a valid email address",
        variant: "destructive",
      });
    }

    return false;
  }

  return true;
};
```

### Error Handling Best Practices
- Always extract meaningful error messages from API responses
- Use consistent error message patterns across the application
- Prevent duplicate error handling between components
- Handle different error object formats gracefully

### Toast Variants
- `default`: Standard styling for general notifications
- `destructive`: Red styling for errors and validation failures

By following these guidelines, we can prevent common issues and maintain a high-quality, consistent codebase.
