import React, { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Users, Shield, TrendingUp, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { TeamPermissionOverview } from './TeamPermissionOverview';
import { BulkPermissionManager } from './BulkPermissionManager';
import { PermissionAnalytics } from './PermissionAnalytics';

interface ManagerDashboardData {
  team_members: TeamMember[];
  summary: TeamPermissionSummary;
  analytics: PermissionAnalytics;
  manager_id: number;
  company_id: number;
}

interface TeamMember {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  department?: string;
  branch?: string;
  permissions: Permission[];
  roles: CustomRole[];
  last_login?: Date;
  active: boolean;
}

interface Permission {
  id: number;
  code: string;
  name: string;
  category: string;
}

interface CustomRole {
  id: number;
  name: string;
  description?: string;
}

interface TeamPermissionSummary {
  total_members: number;
  active_members: number;
  permission_distribution: Array<{
    permission_code: string;
    permission_name: string;
    user_count: number;
    percentage: number;
  }>;
  role_distribution: Array<{
    role_name: string;
    user_count: number;
    percentage: number;
  }>;
  recent_changes: Array<{
    user_name: string;
    action: string;
    permission_or_role: string;
    timestamp: Date;
  }>;
}

interface PermissionAnalytics {
  team_size: number;
  permission_coverage: {
    high_privilege_users: number;
    standard_users: number;
    limited_access_users: number;
  };
  permission_trends: Array<{
    date: string;
    new_permissions: number;
    removed_permissions: number;
    role_changes: number;
  }>;
  compliance_metrics: {
    users_with_excessive_permissions: number;
    users_needing_review: number;
    last_review_date?: Date;
  };
}

export const ManagerDashboard: React.FC = () => {
  const { getCurrentUser } = useAuth();
  const [dashboardData, setDashboardData] = useState<ManagerDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchDashboardData = async () => {
    try {
      const user = getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const response = await fetch('/api/manager-tools/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const data = await response.json();
      setDashboardData(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          {error}
          <Button variant="outline" size="sm" onClick={handleRefresh} className="ml-2">
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>No dashboard data available.</AlertDescription>
      </Alert>
    );
  }

  const { team_members, summary, analytics } = dashboardData;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Manager Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your team's permissions and access controls
          </p>
        </div>
        <Button onClick={handleRefresh} disabled={refreshing}>
          {refreshing ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <TrendingUp className="mr-2 h-4 w-4" />
          )}
          Refresh
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Size</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.total_members}</div>
            <p className="text-xs text-muted-foreground">
              {summary.active_members} active members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Privilege Users</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.permission_coverage.high_privilege_users}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.team_size > 0 ? Math.round((analytics.permission_coverage.high_privilege_users / analytics.team_size) * 100) : 0}% of team
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.compliance_metrics.users_needing_review}</div>
            <p className="text-xs text-muted-foreground">
              Users needing review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Excessive Permissions</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.compliance_metrics.users_with_excessive_permissions}</div>
            <p className="text-xs text-muted-foreground">
              Users with too many permissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Team Overview</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Management</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <TeamPermissionOverview 
            teamMembers={team_members}
            summary={summary}
            onRefresh={handleRefresh}
          />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <BulkPermissionManager 
            teamMembers={team_members}
            onSuccess={handleRefresh}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <PermissionAnalytics 
            analytics={analytics}
            summary={summary}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};
