import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from "../shared/schema";
import errorLogger from './utils/errorLogger';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Load environment variables manually if needed
const envPath = path.resolve(rootDir, '.env');
console.log('Checking for .env file at:', envPath);
console.log('File exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
  console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL);
}

// Verify environment configuration
if (!process.env.DATABASE_URL) {
  const error = new Error("DATABASE_URL must be set. Did you forget to provision a database?");
  errorLogger.logError('Missing database configuration', 'database-connection', error);
  throw error;
}

// Log database connection initialization
errorLogger.logInfo('Initializing database connection pool', 'database-connection');

// Configure the pool with proper settings for production environments
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 5000, // Maximum time to wait for a connection from the pool
});

// Add event listeners for connection issues
pool.on('error', (err) => {
  errorLogger.logError('Unexpected database pool error', 'database-connection', err);
});

// Create Drizzle ORM instance with schema
const db = drizzle(pool, { schema });

// Log successful connection
errorLogger.logInfo('Database connection initialized successfully', 'database-connection');

// Export the initialized pool and db
export { pool, db };
