import { db } from '../db';
import {
  users, userRoles, rolePermissions, permissions,
  groupUsers, groupRoles, customRoles
} from '@shared/schema';
import { eq, and, inArray, or } from 'drizzle-orm';
import { hasPermission } from '../middleware/permission';
import {
  conditionalPermissionService,
  PermissionEvaluationContext
} from './conditionalPermissionService';

export interface PermissionContext {
  userId: number;
  amount?: number;
  timestamp?: Date;
  ip_address?: string;
  resource_id?: number;
  company_id?: number;
}

export class EnhancedPermissionService {

  /**
   * Check if user has permission to create loans based on amount
   * @param userId User ID
   * @param loanAmount Loan amount to check
   * @returns Promise<boolean> True if user has permission
   */
  async checkLoanCreationPermission(userId: number, loanAmount: number): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      // Check unlimited permission first
      if (userPermissions.includes('loan_create_unlimited')) {
        return true;
      }

      // Check advanced permission (up to $50,000)
      if (loanAmount <= 50000 && userPermissions.includes('loan_create_advanced')) {
        return true;
      }

      // Check basic permission (up to $10,000)
      if (loanAmount <= 10000 && userPermissions.includes('loan_create_basic')) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking loan creation permission:', error);
      return false;
    }
  }

  /**
   * Check if user has permission to approve loans based on amount
   * @param userId User ID
   * @param loanAmount Loan amount to check
   * @returns Promise<boolean> True if user has permission
   */
  async checkLoanApprovalPermission(userId: number, loanAmount: number): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      // Check unlimited approval permission
      if (userPermissions.includes('loan_approve_unlimited')) {
        return true;
      }

      // Check tier 3 permission (up to $100,000)
      if (loanAmount <= 100000 && userPermissions.includes('loan_approve_tier3')) {
        return true;
      }

      // Check tier 2 permission (up to $50,000)
      if (loanAmount <= 50000 && userPermissions.includes('loan_approve_tier2')) {
        return true;
      }

      // Check tier 1 permission (up to $10,000)
      if (loanAmount <= 10000 && userPermissions.includes('loan_approve_tier1')) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking loan approval permission:', error);
      return false;
    }
  }

  /**
   * Check if user has permission to disburse loans based on amount
   * @param userId User ID
   * @param loanAmount Loan amount to check
   * @returns Promise<boolean> True if user has permission
   */
  async checkLoanDisbursementPermission(userId: number, loanAmount: number): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      // Check unlimited disbursement permission
      if (userPermissions.includes('loan_disburse_unlimited')) {
        return true;
      }

      // Check advanced disbursement permission (up to $100,000)
      if (loanAmount <= 100000 && userPermissions.includes('loan_disburse_advanced')) {
        return true;
      }

      // Check basic disbursement permission (up to $10,000)
      if (loanAmount <= 10000 && userPermissions.includes('loan_disburse_basic')) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking loan disbursement permission:', error);
      return false;
    }
  }

  /**
   * Check if user has access to specific customer data types
   * @param userId User ID
   * @param dataType Type of data to access ('basic', 'financial', 'sensitive', 'all')
   * @returns Promise<boolean> True if user has access
   */
  async checkCustomerDataAccess(userId: number, dataType: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      switch (dataType) {
        case 'basic':
          return userPermissions.includes('customer_view_basic') ||
                 userPermissions.includes('customer_view_financial') ||
                 userPermissions.includes('customer_view_sensitive') ||
                 userPermissions.includes('customer_view_all');

        case 'financial':
          return userPermissions.includes('customer_view_financial') ||
                 userPermissions.includes('customer_view_sensitive') ||
                 userPermissions.includes('customer_view_all');

        case 'sensitive':
          return userPermissions.includes('customer_view_sensitive') ||
                 userPermissions.includes('customer_view_all');

        case 'all':
          return userPermissions.includes('customer_view_all');

        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking customer data access:', error);
      return false;
    }
  }

  /**
   * Check if user has permission to export customer data
   * @param userId User ID
   * @param exportType Type of export ('basic', 'financial', 'sensitive', 'bulk')
   * @returns Promise<boolean> True if user has permission
   */
  async checkCustomerExportPermission(userId: number, exportType: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      switch (exportType) {
        case 'basic':
          return userPermissions.includes('customer_export_basic') ||
                 userPermissions.includes('customer_export_bulk');

        case 'financial':
          return userPermissions.includes('customer_export_financial') ||
                 userPermissions.includes('customer_export_bulk');

        case 'sensitive':
          return userPermissions.includes('customer_export_sensitive') ||
                 userPermissions.includes('customer_export_bulk');

        case 'bulk':
          return userPermissions.includes('customer_export_bulk');

        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking customer export permission:', error);
      return false;
    }
  }

  /**
   * Check if user has permission for customer communication
   * @param userId User ID
   * @param communicationType Type of communication ('email', 'sms', 'call', 'automated')
   * @returns Promise<boolean> True if user has permission
   */
  async checkCustomerCommunicationPermission(userId: number, communicationType: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      switch (communicationType) {
        case 'email':
          return userPermissions.includes('customer_contact_email');

        case 'sms':
          return userPermissions.includes('customer_contact_sms');

        case 'call':
          return userPermissions.includes('customer_contact_call');

        case 'automated':
          return userPermissions.includes('customer_contact_automated');

        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking customer communication permission:', error);
      return false;
    }
  }

  /**
   * Check if user has permission for payment operations
   * @param userId User ID
   * @param operationType Type of operation ('manual', 'automated', 'refund', 'void', 'adjust')
   * @returns Promise<boolean> True if user has permission
   */
  async checkPaymentOperationPermission(userId: number, operationType: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      switch (operationType) {
        case 'manual':
          return userPermissions.includes('payment_process_manual');

        case 'automated':
          return userPermissions.includes('payment_process_automated');

        case 'refund':
          return userPermissions.includes('payment_refund');

        case 'void':
          return userPermissions.includes('payment_void');

        case 'adjust':
          return userPermissions.includes('payment_adjust');

        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking payment operation permission:', error);
      return false;
    }
  }

  /**
   * Check if user has permission for report operations
   * @param userId User ID
   * @param reportType Type of report ('basic', 'detailed', 'executive', 'export', 'custom', 'schedule')
   * @returns Promise<boolean> True if user has permission
   */
  async checkReportPermission(userId: number, reportType: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);

      switch (reportType) {
        case 'basic':
          return userPermissions.includes('report_view_basic') ||
                 userPermissions.includes('report_view_detailed') ||
                 userPermissions.includes('report_view_executive');

        case 'detailed':
          return userPermissions.includes('report_view_detailed') ||
                 userPermissions.includes('report_view_executive');

        case 'executive':
          return userPermissions.includes('report_view_executive');

        case 'export':
          return userPermissions.includes('report_export');

        case 'custom':
          return userPermissions.includes('report_create_custom');

        case 'schedule':
          return userPermissions.includes('report_schedule');

        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking report permission:', error);
      return false;
    }
  }

  /**
   * Get all permissions for a user (including inherited from roles and groups)
   * @param userId User ID
   * @returns Promise<string[]> Array of permission codes
   */
  async getUserPermissions(userId: number): Promise<string[]> {
    try {
      // Use existing hasPermission function's logic but return all permissions
      const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);

      if (!user.length) {
        return [];
      }

      // System admins have all permissions
      if (user[0].role === 'saas_admin') {
        const allPermissions = await db.select({ code: permissions.code }).from(permissions);
        return allPermissions.map(p => p.code);
      }

      const permissionSet = new Set<string>();

      // Get directly assigned roles
      const userRolesData = await db
        .select({
          role_id: userRoles.role_id
        })
        .from(userRoles)
        .where(eq(userRoles.user_id, userId));

      const directRoleIds = userRolesData.map(r => r.role_id);

      // Get roles from groups
      const userGroups = await db
        .select({
          group_id: groupUsers.group_id
        })
        .from(groupUsers)
        .where(eq(groupUsers.user_id, userId));

      const groupIds = userGroups.map(g => g.group_id);

      let groupRoleIds: number[] = [];
      if (groupIds.length > 0) {
        const groupRolesData = await db
          .select({
            role_id: groupRoles.role_id
          })
          .from(groupRoles)
          .where(inArray(groupRoles.group_id, groupIds));

        groupRoleIds = groupRolesData.map(gr => gr.role_id);
      }

      // Combine all role IDs
      const allRoleIds = [...new Set([...directRoleIds, ...groupRoleIds])];

      if (allRoleIds.length > 0) {
        // Get all permissions for these roles
        const rolePermissionsData = await db
          .select({
            permission_code: permissions.code
          })
          .from(rolePermissions)
          .innerJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
          .where(inArray(rolePermissions.role_id, allRoleIds));

        rolePermissionsData.forEach(rp => permissionSet.add(rp.permission_code));
      }

      return Array.from(permissionSet);
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  /**
   * Check permission with conditional evaluation
   * @param context Enhanced permission context
   * @param permissionCode Specific permission code to check
   * @returns Promise<{allowed: boolean, reason?: string, requiresApproval?: boolean}> Permission result
   */
  async checkPermissionWithConditions(
    context: PermissionEvaluationContext
  ): Promise<{allowed: boolean, reason?: string, requiresApproval?: boolean, approverRoles?: string[]}> {
    try {
      // First check if user has the base permission
      const hasBasePermission = await hasPermission(context.userId, context.permissionCode);

      if (!hasBasePermission) {
        return {
          allowed: false,
          reason: `User does not have permission: ${context.permissionCode}`
        };
      }

      // Evaluate conditional permissions
      const conditionResult = await conditionalPermissionService.evaluatePermissionConditions(context);

      if (!conditionResult.passed) {
        return {
          allowed: false,
          reason: conditionResult.reason,
          requiresApproval: conditionResult.requiresApproval,
          approverRoles: conditionResult.approverRoles
        };
      }

      return { allowed: true };
    } catch (error) {
      console.error('Error checking permission with conditions:', error);
      return {
        allowed: false,
        reason: 'Permission evaluation error'
      };
    }
  }

  /**
   * Check comprehensive permission with context
   * @param context Permission context including user, amount, etc.
   * @param permissionType Type of permission to check
   * @param operationType Specific operation type
   * @returns Promise<boolean> True if user has permission
   */
  async checkPermissionWithContext(
    context: PermissionContext,
    permissionType: 'loan_create' | 'loan_approve' | 'loan_disburse' | 'customer_data' | 'customer_export' | 'customer_communication' | 'payment_operation' | 'report',
    operationType?: string
  ): Promise<boolean> {
    try {
      switch (permissionType) {
        case 'loan_create':
          return this.checkLoanCreationPermission(context.userId, context.amount || 0);

        case 'loan_approve':
          return this.checkLoanApprovalPermission(context.userId, context.amount || 0);

        case 'loan_disburse':
          return this.checkLoanDisbursementPermission(context.userId, context.amount || 0);

        case 'customer_data':
          return this.checkCustomerDataAccess(context.userId, operationType || 'basic');

        case 'customer_export':
          return this.checkCustomerExportPermission(context.userId, operationType || 'basic');

        case 'customer_communication':
          return this.checkCustomerCommunicationPermission(context.userId, operationType || 'email');

        case 'payment_operation':
          return this.checkPaymentOperationPermission(context.userId, operationType || 'manual');

        case 'report':
          return this.checkReportPermission(context.userId, operationType || 'basic');

        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking permission with context:', error);
      return false;
    }
  }
}
