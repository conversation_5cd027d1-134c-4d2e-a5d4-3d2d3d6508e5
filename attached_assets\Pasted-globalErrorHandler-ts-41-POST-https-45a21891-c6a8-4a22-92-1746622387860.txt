globalErrorHandler.ts:41 
            
            
           POST https://45a21891-c6a8-4a22-92a4-792dff2c8e3c-00-2f72q5fiz3glp.riker.replit.dev/api/collections/batch-complete 500 (Internal Server Error)
window.fetch @ Network.js:219
window.fetch @ globalErrorHandler.ts:41
apiRequest @ queryClient.ts:53
mutationFn @ quick-payment.tsx:167
fn @ @tanstack_react-query.js?v=6a97afa8:1189
run @ @tanstack_react-query.js?v=6a97afa8:494
start @ @tanstack_react-query.js?v=6a97afa8:536
execute @ @tanstack_react-query.js?v=6a97afa8:1225
await in execute
mutate @ @tanstack_react-query.js?v=6a97afa8:2630
(anonymous) @ @tanstack_react-query.js?v=6a97afa8:3295
handleConfirmPayment @ quick-payment.tsx:292
callCallback2 @ chunk-RPCDYKBN.js?v=6a97afa8:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=6a97afa8:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=6a97afa8:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=6a97afa8:3736
executeDispatch @ chunk-RPCDYKBN.js?v=6a97afa8:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=6a97afa8:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=6a97afa8:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=6a97afa8:7051
(anonymous) @ chunk-RPCDYKBN.js?v=6a97afa8:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=6a97afa8:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=6a97afa8:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=6a97afa8:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=6a97afa8:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5449
Console.js:61 [network] HTTP Error 500: Internal Server Error {url: '/api/collections/batch-complete', status: 500, statusText: 'Internal Server Error'}
Mt.forEach.n.<computed> @ Console.js:61
error @ errorLogger.ts:39
window.fetch @ globalErrorHandler.ts:51
await in window.fetch
apiRequest @ queryClient.ts:53
mutationFn @ quick-payment.tsx:167
fn @ @tanstack_react-query.js?v=6a97afa8:1189
run @ @tanstack_react-query.js?v=6a97afa8:494
start @ @tanstack_react-query.js?v=6a97afa8:536
execute @ @tanstack_react-query.js?v=6a97afa8:1225
await in execute
mutate @ @tanstack_react-query.js?v=6a97afa8:2630
(anonymous) @ @tanstack_react-query.js?v=6a97afa8:3295
handleConfirmPayment @ quick-payment.tsx:292
callCallback2 @ chunk-RPCDYKBN.js?v=6a97afa8:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=6a97afa8:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=6a97afa8:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=6a97afa8:3736
executeDispatch @ chunk-RPCDYKBN.js?v=6a97afa8:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=6a97afa8:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=6a97afa8:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=6a97afa8:7051
(anonymous) @ chunk-RPCDYKBN.js?v=6a97afa8:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=6a97afa8:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=6a97afa8:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=6a97afa8:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=6a97afa8:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5449
Console.js:61 [apiRequest] API request failed: 500: {"error":"Failed to process batch payment"} {method: 'POST', url: '/api/collections/batch-complete', error: Error: 500: {"error":"Failed to process batch payment"}
    at throwIfResNotOk (https://45a21891-c6…}
Mt.forEach.n.<computed> @ Console.js:61
error @ errorLogger.ts:39
apiRequest @ queryClient.ts:64
await in apiRequest
mutationFn @ quick-payment.tsx:167
fn @ @tanstack_react-query.js?v=6a97afa8:1189
run @ @tanstack_react-query.js?v=6a97afa8:494
start @ @tanstack_react-query.js?v=6a97afa8:536
execute @ @tanstack_react-query.js?v=6a97afa8:1225
await in execute
mutate @ @tanstack_react-query.js?v=6a97afa8:2630
(anonymous) @ @tanstack_react-query.js?v=6a97afa8:3295
handleConfirmPayment @ quick-payment.tsx:292
callCallback2 @ chunk-RPCDYKBN.js?v=6a97afa8:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=6a97afa8:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=6a97afa8:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=6a97afa8:3736
executeDispatch @ chunk-RPCDYKBN.js?v=6a97afa8:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=6a97afa8:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=6a97afa8:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=6a97afa8:7051
(anonymous) @ chunk-RPCDYKBN.js?v=6a97afa8:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=6a97afa8:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=6a97afa8:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=6a97afa8:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=6a97afa8:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5449
Console.js:61 [react-query] Mutation error for key: [] Error: 500: {"error":"Failed to process batch payment"}
    at throwIfResNotOk (queryClient.ts:29:11)
    at async apiRequest (queryClient.ts:60:5)
    at async Object.mutationFn (quick-payment.tsx:167:19)
Mt.forEach.n.<computed> @ Console.js:61
error @ errorLogger.ts:39
onError @ queryClient.ts:159
execute @ @tanstack_react-query.js?v=6a97afa8:1247
await in execute
mutate @ @tanstack_react-query.js?v=6a97afa8:2630
(anonymous) @ @tanstack_react-query.js?v=6a97afa8:3295
handleConfirmPayment @ quick-payment.tsx:292
callCallback2 @ chunk-RPCDYKBN.js?v=6a97afa8:3674
invokeGuardedCallbackDev @ chunk-RPCDYKBN.js?v=6a97afa8:3699
invokeGuardedCallback @ chunk-RPCDYKBN.js?v=6a97afa8:3733
invokeGuardedCallbackAndCatchFirstError @ chunk-RPCDYKBN.js?v=6a97afa8:3736
executeDispatch @ chunk-RPCDYKBN.js?v=6a97afa8:7014
processDispatchQueueItemsInOrder @ chunk-RPCDYKBN.js?v=6a97afa8:7034
processDispatchQueue @ chunk-RPCDYKBN.js?v=6a97afa8:7043
dispatchEventsForPlugins @ chunk-RPCDYKBN.js?v=6a97afa8:7051
(anonymous) @ chunk-RPCDYKBN.js?v=6a97afa8:7174
batchedUpdates$1 @ chunk-RPCDYKBN.js?v=6a97afa8:18913
batchedUpdates @ chunk-RPCDYKBN.js?v=6a97afa8:3579
dispatchEventForPluginEventSystem @ chunk-RPCDYKBN.js?v=6a97afa8:7173
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ chunk-RPCDYKBN.js?v=6a97afa8:5478
dispatchEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5472
dispatchDiscreteEvent @ chunk-RPCDYKBN.js?v=6a97afa8:5449
