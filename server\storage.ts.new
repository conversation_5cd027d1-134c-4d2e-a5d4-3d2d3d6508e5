import { 
  users, companies, customers, agents, loans, collections, userCompanies,
  resellers, resellerClients, resellerCommissions, referrals,
  subscriptionPlans, subscriptions, partners, formTemplates, formFields, formSubmissions,
  loanConfigurations, paymentSchedules, payments, expenses, fines,
  // Financial management tables
  accounts, transactions, accountBalances, accountingPeriods,
  shareholders, shareholdings, investmentTransactions, profitDistributions,
  balanceSheets, balanceSheetItems, fixedAssets, depreciationSchedules,
  // User types
  type User, type InsertUser, type Company, type InsertCompany,
  type Customer, type InsertCustomer, type Agent, type InsertAgent,
  type Loan, type InsertLoan, type Collection, type InsertCollection,
  type Reseller, type InsertReseller, type ResellerClient, type InsertResellerClient,
  type ResellerCommission, type InsertResellerCommission, type Referral, type InsertReferral,
  type SubscriptionPlan, type InsertSubscriptionPlan, type Subscription, type InsertSubscription,
  type UserCompany, type InsertUserCompany, type Partner, type InsertPartner,
  type FormTemplate, type InsertFormTemplate, type FormField, type InsertFormField,
  type FormSubmission, type InsertFormSubmission, type LoanConfiguration, type InsertLoanConfiguration,
  type PaymentSchedule, type InsertPaymentSchedule, type Payment, type InsertPayment,
  type Expense, type InsertExpense, type Fine, type InsertFine,
  // Financial management types
  type Account, type InsertAccount, type Transaction, type InsertTransaction,
  type AccountBalance, type InsertAccountBalance, type AccountingPeriod, type InsertAccountingPeriod,
  type Shareholder, type InsertShareholder, type Shareholding, type InsertShareholding,
  type InvestmentTransaction, type InsertInvestmentTransaction, 
  type ProfitDistribution, type InsertProfitDistribution,
  type BalanceSheet, type InsertBalanceSheet, type BalanceSheetItem, type InsertBalanceSheetItem,
  type FixedAsset, type InsertFixedAsset, type DepreciationSchedule, type InsertDepreciationSchedule,
  // Report interfaces
  type DailyCollectionReport, type DaySheetReport, type CustomerReport,
  type AgentReport, type ProfitLossReport, type AccountStatement, type AccountBalanceReport,
  type BalanceSheetReport, type BalanceSheetDetail, type CashFlowReport, type ShareholderReport
} from "@shared/schema";

// Import finance management functions
import * as financialManagement from './financialManagement';

// Import other dependencies
import { db } from './db';
import { eq, and, desc, sql, gte, lte, isNull, count, sum } from 'drizzle-orm';
import bcrypt from 'bcrypt';
import errorLogger from './utils/errorLogger';
import { generatePDF } from './utils/pdfGenerator';

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User>;
  getUsersByCompany(companyId: number): Promise<User[]>;
  updateUserPassword(id: number, newPassword: string): Promise<User>;
  
  // Company operations
  getCompany(id: number): Promise<Company | undefined>;
  getCompanies(): Promise<Company[]>;
  createCompany(company: InsertCompany): Promise<Company>;
  updateCompany(id: number, company: Partial<InsertCompany>): Promise<Company>;
  deleteCompany(id: number): Promise<void>;
  getCompanyUsers(companyId: number): Promise<User[]>;
  
  // UserCompany operations
  getUserCompanies(userId: number): Promise<(UserCompany & { company: Company })[]>;
  getUserCompanyByIds(userId: number, companyId: number): Promise<UserCompany | undefined>;
  createUserCompany(userCompany: InsertUserCompany): Promise<UserCompany>;
  updateUserCompany(id: number, userCompany: Partial<InsertUserCompany>): Promise<UserCompany>;
  deleteUserCompany(id: number): Promise<void>;
  setUserCompanyAsPrimary(id: number, userId: number): Promise<UserCompany>;
  
  // Customer operations
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomersByCompany(companyId: number): Promise<Customer[]>;
  getCustomersByBranch(branchId: number): Promise<Customer[]>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, customer: Partial<InsertCustomer>): Promise<Customer>;
  deleteCustomer(id: number): Promise<void>;
  
  // Agent operations
  getAgent(id: number): Promise<Agent | undefined>;
  getAgentsByCompany(companyId: number): Promise<Agent[]>;
  getAgentsByBranch(branchId: number): Promise<Agent[]>;
  createAgent(agent: InsertAgent): Promise<Agent>;
  updateAgent(id: number, agent: Partial<InsertAgent>): Promise<Agent>;
  deleteAgent(id: number): Promise<void>;
  
  // Partner operations
  getPartner(id: number): Promise<Partner | undefined>;
  getPartnersByCompany(companyId: number): Promise<Partner[]>;
  createPartner(partner: InsertPartner): Promise<Partner>;
  updatePartner(id: number, partner: Partial<InsertPartner>): Promise<Partner>;
  deletePartner(id: number): Promise<void>;
  
  // Loan operations
  getLoan(id: number): Promise<Loan | undefined>;
  getLoansByCustomer(customerId: number): Promise<Loan[]>;
  getLoansByCompany(companyId: number): Promise<Loan[]>;
  getLoansByBranch(branchId: number): Promise<Loan[]>;
  createLoan(loan: InsertLoan): Promise<Loan>;
  updateLoan(id: number, loan: Partial<InsertLoan>): Promise<Loan>;
  deleteLoan(id: number): Promise<void>;
  
  // Collection operations
  getCollection(id: number): Promise<Collection | undefined>;
  getCollectionsByCompany(companyId: number, status?: string, agentId?: number, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  getCollectionsByBranch(branchId: number, status?: string, agentId?: number, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  getCollectionsByLoan(loanId: number): Promise<Collection[]>;
  getCollectionsByAgent(agentId: number, status?: string, dateRange?: { startDate: string, endDate: string }): Promise<Collection[]>;
  createCollection(collection: InsertCollection): Promise<Collection>;
  updateCollection(id: number, collection: Partial<InsertCollection>): Promise<Collection>;
  updateCollectionStatus(id: number, status: string): Promise<Collection>;
  deleteCollection(id: number): Promise<void>;
  markCollectionsAsCompleted(collectionIds: number[]): Promise<void>;
  
  // Payment Schedule operations
  getPaymentSchedule(id: number): Promise<PaymentSchedule | undefined>;
  getPaymentSchedulesByLoan(loanId: number): Promise<PaymentSchedule[]>;
  createPaymentSchedule(paymentSchedule: InsertPaymentSchedule): Promise<PaymentSchedule>;
  updatePaymentSchedule(id: number, paymentSchedule: Partial<InsertPaymentSchedule>): Promise<PaymentSchedule>;
  deletePaymentSchedule(id: number): Promise<void>;
  
  // Payment operations
  getPayment(id: number): Promise<Payment | undefined>;
  getPaymentsByCollection(collectionId: number): Promise<Payment[]>;
  createPayment(payment: InsertPayment): Promise<Payment>;
  generatePaymentPdf(paymentId: number): Promise<Buffer>;
  
  // Expense operations
  getExpense(id: number): Promise<Expense | undefined>;
  getExpensesByCompany(companyId: number, filters?: any): Promise<Expense[]>;
  createExpense(expense: InsertExpense): Promise<Expense>;
  updateExpense(id: number, expense: Partial<InsertExpense>): Promise<Expense>;
  deleteExpense(id: number): Promise<void>;
  
  // Form Template operations
  getFormTemplate(id: number): Promise<FormTemplate | undefined>;
  getFormTemplatesByCompany(companyId: number): Promise<FormTemplate[]>;
  getActiveFormTemplates(companyId: number): Promise<FormTemplate[]>;
  createFormTemplate(formTemplate: InsertFormTemplate): Promise<FormTemplate>;
  updateFormTemplate(id: number, formTemplate: Partial<InsertFormTemplate>): Promise<FormTemplate>;
  deleteFormTemplate(id: number): Promise<void>;
  toggleFormTemplateActive(id: number): Promise<FormTemplate>;
  
  // Form Field operations
  getFormFieldsByTemplate(templateId: number): Promise<FormField[]>;
  createFormField(formField: InsertFormField): Promise<FormField>;
  updateFormField(id: number, formField: Partial<InsertFormField>): Promise<FormField>;
  deleteFormField(id: number): Promise<void>;
  
  // Form Submission operations
  getFormSubmissionsByLoan(loanId: number): Promise<FormSubmission[]>;
  getFormSubmission(id: number): Promise<FormSubmission | undefined>;
  createFormSubmission(formSubmission: InsertFormSubmission): Promise<FormSubmission>;
  updateFormSubmission(id: number, formSubmission: Partial<InsertFormSubmission>): Promise<FormSubmission>;
  deleteFormSubmission(id: number): Promise<void>;
  
  // Loan Configuration operations
  getLoanConfiguration(id: number): Promise<LoanConfiguration | undefined>;
  getLoanConfigurationsByCompany(companyId: number): Promise<LoanConfiguration[]>;
  getActiveLoanConfigurations(companyId: number): Promise<LoanConfiguration[]>;
  createLoanConfiguration(loanConfiguration: InsertLoanConfiguration): Promise<LoanConfiguration>;
  updateLoanConfiguration(id: number, loanConfiguration: Partial<InsertLoanConfiguration>): Promise<LoanConfiguration>;
  deleteLoanConfiguration(id: number): Promise<void>;
  toggleLoanConfigurationActive(id: number): Promise<LoanConfiguration>;
  
  // Subscription Plan operations
  getSubscriptionPlan(id: number): Promise<SubscriptionPlan | undefined>;
  getSubscriptionPlans(): Promise<SubscriptionPlan[]>;
  createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlan>;
  updateSubscriptionPlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan>;
  deleteSubscriptionPlan(id: number): Promise<void>;
  getPublicSubscriptionPlans(): Promise<SubscriptionPlan[]>;
  
  // Subscription operations
  getSubscription(id: number): Promise<Subscription | undefined>;
  getSubscriptionsByCompany(companyId: number): Promise<Subscription[]>;
  createSubscription(subscription: InsertSubscription): Promise<Subscription>;
  updateSubscription(id: number, subscription: Partial<InsertSubscription>): Promise<Subscription>;
  deleteSubscription(id: number): Promise<void>;
  
  // Reseller operations
  getReseller(id: number): Promise<Reseller | undefined>;
  getResellerByUserId(userId: number): Promise<Reseller | undefined>;
  createReseller(reseller: InsertReseller): Promise<Reseller>;
  updateReseller(id: number, reseller: Partial<InsertReseller>): Promise<Reseller>;
  getResellerClients(resellerId: number): Promise<(ResellerClient & { company: Company })[]>;
  createResellerCommission(commission: InsertResellerCommission): Promise<ResellerCommission>;
  getResellerCommissions(resellerId: number): Promise<ResellerCommission[]>;
  
  // Report operations
  getDailyCollectionsReport(
    companyId: number,
    startDate: string,
    endDate: string,
    status?: string,
    agentId?: number,
    branchId?: number,
    paymentMethod?: string
  ): Promise<DailyCollectionReport>;
  
  getDaySheetReport(
    companyId: number,
    date: string,
    branchId?: number
  ): Promise<DaySheetReport>;
  
  getCustomerReport(
    companyId: number,
    customerId: number,
    startDate?: string,
    endDate?: string
  ): Promise<CustomerReport>;
  
  getAgentReport(
    companyId: number,
    agentId: number,
    startDate?: string,
    endDate?: string
  ): Promise<AgentReport>;
  
  getProfitLossReport(
    companyId: number,
    startDate: string,
    endDate: string,
    branchId?: number
  ): Promise<ProfitLossReport>;
  
  // Financial Management operations
  // Account operations
  createAccount(account: InsertAccount): Promise<Account>;
  getAccount(id: number, companyId: number): Promise<Account | undefined>;
  getAccountsByCompany(companyId: number): Promise<Account[]>;
  getAccountHierarchy(companyId: number): Promise<Account[]>;
  updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account | undefined>;
  deleteAccount(id: number, companyId: number): Promise<boolean>;
  
  // Transaction operations
  createTransaction(transaction: InsertTransaction): Promise<Transaction>;
  getTransaction(id: number, companyId: number): Promise<Transaction | undefined>;
  getTransactionsByCompany(companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]>;
  getTransactionsByAccount(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]>;
  
  // Account Balance operations
  createAccountBalance(balance: InsertAccountBalance): Promise<AccountBalance>;
  getAccountBalances(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<AccountBalance[]>;
  getLatestAccountBalance(accountId: number, companyId: number): Promise<AccountBalance | undefined>;
  reconcileAccountBalance(id: number, userId: number, companyId: number): Promise<AccountBalance | undefined>;
  
  // Accounting Period operations
  createAccountingPeriod(period: InsertAccountingPeriod): Promise<AccountingPeriod>;
  getAccountingPeriods(companyId: number): Promise<AccountingPeriod[]>;
  getCurrentAccountingPeriod(companyId: number): Promise<AccountingPeriod | undefined>;
  closeAccountingPeriod(id: number, userId: number, companyId: number): Promise<AccountingPeriod | undefined>;
  
  // Shareholder operations
  createShareholder(shareholder: InsertShareholder): Promise<Shareholder>;
  getShareholder(id: number, companyId: number): Promise<Shareholder | undefined>;
  getShareholdersByCompany(companyId: number): Promise<Shareholder[]>;
  updateShareholder(id: number, companyId: number, shareholder: Partial<InsertShareholder>): Promise<Shareholder | undefined>;
  createShareholding(shareholding: InsertShareholding): Promise<Shareholding>;
  
  // Investment operations
  createInvestmentTransaction(transaction: InsertInvestmentTransaction): Promise<InvestmentTransaction>;
  getInvestmentTransactions(shareholderId: number): Promise<InvestmentTransaction[]>;
  
  // Financial Reports
  getAccountStatement(companyId: number, accountId: number, startDate: string, endDate: string): Promise<AccountStatement>;
  getBalanceSheetReport(companyId: number, asOfDate: string): Promise<BalanceSheetReport>;
  getCashFlowReport(companyId: number, startDate: string, endDate: string): Promise<CashFlowReport>;
  getShareholderReport(companyId: number, shareholderId: number): Promise<ShareholderReport>;
  
  // Fixed Asset operations
  createFixedAsset(asset: InsertFixedAsset): Promise<FixedAsset>;
  getFixedAssetsByCompany(companyId: number): Promise<FixedAsset[]>;
  updateFixedAsset(id: number, companyId: number, asset: Partial<InsertFixedAsset>): Promise<FixedAsset | undefined>;
  recordDepreciation(assetId: number, amount: number, companyId: number): Promise<FixedAsset | undefined>;
  disposeFixedAsset(id: number, disposalAmount: number, disposalDate: Date, companyId: number): Promise<FixedAsset | undefined>;
}

// Implementation of the IStorage interface for testing financial management features
export class MemStorage implements IStorage {
  private users: User[] = [];
  private companies: Company[] = [];
  private userCompanies: (UserCompany & { company: Company })[] = [];
  private customers: Customer[] = [];
  private agents: Agent[] = [];
  private loans: Loan[] = [];
  private collections: Collection[] = [];
  private partners: Partner[] = [];
  private paymentSchedules: PaymentSchedule[] = [];
  private payments: Payment[] = [];
  private expenses: Expense[] = [];
  private subscriptionPlans: SubscriptionPlan[] = [];
  private subscriptions: Subscription[] = [];
  private formTemplates: FormTemplate[] = [];
  private formFields: FormField[] = [];
  private formSubmissions: FormSubmission[] = [];
  private loanConfigurations: LoanConfiguration[] = [];
  private resellers: Reseller[] = [];
  private resellerClients: (ResellerClient & { company: Company })[] = [];
  private resellerCommissions: ResellerCommission[] = [];
  
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    try {
      const [user] = await db.select()
        .from(users)
        .where(eq(users.id, id));
      return user;
    } catch (error) {
      errorLogger.logError(`Error fetching user id=${id}`, 'user-fetch', error as Error);
      return undefined;
    }
  }
  
  // Other existing methods...
  
  // Implementing financial management methods
  
  // Account operations
  async createAccount(account: InsertAccount): Promise<Account> {
    return financialManagement.createAccount(account);
  }
  
  async getAccount(id: number, companyId: number): Promise<Account | undefined> {
    return financialManagement.getAccountById(id, companyId);
  }
  
  async getAccountsByCompany(companyId: number): Promise<Account[]> {
    return financialManagement.getAccountsByCompany(companyId);
  }
  
  async getAccountHierarchy(companyId: number): Promise<Account[]> {
    return financialManagement.getAccountHierarchy(companyId);
  }
  
  async updateAccount(id: number, companyId: number, account: Partial<InsertAccount>): Promise<Account | undefined> {
    return financialManagement.updateAccount(id, companyId, account);
  }
  
  async deleteAccount(id: number, companyId: number): Promise<boolean> {
    return financialManagement.deleteAccount(id, companyId);
  }
  
  // Transaction operations
  async createTransaction(transaction: InsertTransaction): Promise<Transaction> {
    return financialManagement.createTransaction(transaction);
  }
  
  async getTransaction(id: number, companyId: number): Promise<Transaction | undefined> {
    return financialManagement.getTransaction(id, companyId);
  }
  
  async getTransactionsByCompany(companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByCompany(companyId, startDate, endDate);
  }
  
  async getTransactionsByAccount(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<Transaction[]> {
    return financialManagement.getTransactionsByAccount(accountId, companyId, startDate, endDate);
  }
  
  // Account Balance operations
  async createAccountBalance(balance: InsertAccountBalance): Promise<AccountBalance> {
    return financialManagement.createAccountBalance(balance);
  }
  
  async getAccountBalances(accountId: number, companyId: number, startDate?: string, endDate?: string): Promise<AccountBalance[]> {
    return financialManagement.getAccountBalances(accountId, companyId, startDate, endDate);
  }
  
  async getLatestAccountBalance(accountId: number, companyId: number): Promise<AccountBalance | undefined> {
    return financialManagement.getLatestAccountBalance(accountId, companyId);
  }
  
  async reconcileAccountBalance(id: number, userId: number, companyId: number): Promise<AccountBalance | undefined> {
    return financialManagement.reconcileAccountBalance(id, userId, companyId);
  }
  
  // Accounting Period operations
  async createAccountingPeriod(period: InsertAccountingPeriod): Promise<AccountingPeriod> {
    return financialManagement.createAccountingPeriod(period);
  }
  
  async getAccountingPeriods(companyId: number): Promise<AccountingPeriod[]> {
    return financialManagement.getAccountingPeriods(companyId);
  }
  
  async getCurrentAccountingPeriod(companyId: number): Promise<AccountingPeriod | undefined> {
    return financialManagement.getCurrentAccountingPeriod(companyId);
  }
  
  async closeAccountingPeriod(id: number, userId: number, companyId: number): Promise<AccountingPeriod | undefined> {
    return financialManagement.closeAccountingPeriod(id, userId, companyId);
  }
  
  // Shareholder operations
  async createShareholder(shareholder: InsertShareholder): Promise<Shareholder> {
    return financialManagement.createShareholder(shareholder);
  }
  
  async getShareholder(id: number, companyId: number): Promise<Shareholder | undefined> {
    return financialManagement.getShareholder(id, companyId);
  }
  
  async getShareholdersByCompany(companyId: number): Promise<Shareholder[]> {
    return financialManagement.getShareholdersByCompany(companyId);
  }
  
  async updateShareholder(id: number, companyId: number, shareholder: Partial<InsertShareholder>): Promise<Shareholder | undefined> {
    return financialManagement.updateShareholder(id, companyId, shareholder);
  }
  
  async createShareholding(shareholding: InsertShareholding): Promise<Shareholding> {
    return financialManagement.createShareholding(shareholding);
  }
  
  // Investment operations
  async createInvestmentTransaction(transaction: InsertInvestmentTransaction): Promise<InvestmentTransaction> {
    return financialManagement.createInvestmentTransaction(transaction);
  }
  
  async getInvestmentTransactions(shareholderId: number): Promise<InvestmentTransaction[]> {
    return financialManagement.getInvestmentTransactions(shareholderId);
  }
  
  // Financial Reports
  async getAccountStatement(companyId: number, accountId: number, startDate: string, endDate: string): Promise<AccountStatement> {
    return financialManagement.getAccountStatement(companyId, accountId, startDate, endDate);
  }
  
  async getBalanceSheetReport(companyId: number, asOfDate: string): Promise<BalanceSheetReport> {
    return financialManagement.getBalanceSheetReport(companyId, asOfDate);
  }
  
  async getCashFlowReport(companyId: number, startDate: string, endDate: string): Promise<CashFlowReport> {
    return financialManagement.getCashFlowReport(companyId, startDate, endDate);
  }
  
  async getShareholderReport(companyId: number, shareholderId: number): Promise<ShareholderReport> {
    return financialManagement.getShareholderReport(companyId, shareholderId);
  }
  
  // Fixed Asset operations
  async createFixedAsset(asset: InsertFixedAsset): Promise<FixedAsset> {
    return financialManagement.createFixedAsset(asset);
  }
  
  async getFixedAssetsByCompany(companyId: number): Promise<FixedAsset[]> {
    return financialManagement.getFixedAssetsByCompany(companyId);
  }
  
  async updateFixedAsset(id: number, companyId: number, asset: Partial<InsertFixedAsset>): Promise<FixedAsset | undefined> {
    return financialManagement.updateFixedAsset(id, companyId, asset);
  }
  
  async recordDepreciation(assetId: number, amount: number, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.recordDepreciation(assetId, amount, companyId);
  }
  
  async disposeFixedAsset(id: number, disposalAmount: number, disposalDate: Date, companyId: number): Promise<FixedAsset | undefined> {
    return financialManagement.disposeFixedAsset(id, disposalAmount, disposalDate, companyId);
  }
  
  // Other original methods that need to be implemented...
  
  // We'd need to add all of the other methods required by IStorage interface
  // For brevity, we're not showing all of them here
  
  async getUserByUsername(username: string): Promise<User | undefined> {
    // Implementation
    return undefined;
  }
  
  async getUserByEmail(email: string): Promise<User | undefined> {
    // Implementation
    return undefined;
  }
  
  async createUser(user: InsertUser): Promise<User> {
    // Implementation
    return {} as User;
  }
  
  // ...and so on for all other methods
}

export const storage = new MemStorage();