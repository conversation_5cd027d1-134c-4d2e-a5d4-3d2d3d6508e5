<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>TrackFina - Financial Management Platform</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- Load Replit development banner only in development environment -->
    <script>
      // Only load Replit banner in development and when running on Replit
      if (window.location.hostname.includes('replit') ||
          window.location.hostname.includes('localhost') &&
          document.referrer.includes('replit')) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = 'https://replit.com/public/js/replit-dev-banner.js';
        script.onerror = function() {
          // Silently fail if banner script can't load
          console.debug('Replit banner script failed to load (this is normal outside Replit environment)');
        };
        document.head.appendChild(script);
      }
    </script>
  </body>
</html>