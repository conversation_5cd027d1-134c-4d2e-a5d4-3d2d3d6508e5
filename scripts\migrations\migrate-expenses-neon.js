#!/usr/bin/env node
/**
 * Migration Script: Migrate Expenses Table to Neon DB
 * Purpose: Create the expenses table and expense_type enum in Neon database
 * Usage: node scripts/migrations/migrate-expenses-neon.js
 */

import { runMigration, logMigrationStats } from '../utils/migration-runner.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

await runMigration('Migrate Expenses Table to Neon DB', async (pool, { dryRun }) => {
  // Read the migration SQL file
  const migrationPath = path.join(__dirname, '..', '..', 'migrations', '004_create_expenses_table.sql');
  console.log(`Reading migration file from: ${migrationPath}`);
  
  if (!fs.existsSync(migrationPath)) {
    throw new Error(`Migration file not found: ${migrationPath}`);
  }
  
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  console.log('Migration SQL loaded successfully');
  
  if (dryRun) {
    console.log('Would execute the following migration:');
    console.log('--- Migration SQL ---');
    console.log(migrationSQL);
    console.log('--- End Migration SQL ---');
    
    // Check current state
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables WHERE table_name = 'expenses'
      ) as table_exists;
    `);
    
    const enumCheck = await pool.query(`
      SELECT EXISTS (
        SELECT 1 FROM pg_type WHERE typname = 'expense_type'
      ) as enum_exists;
    `);
    
    logMigrationStats({
      'Migration file': 'Found',
      'Current expenses table exists': tableCheck.rows[0].table_exists ? 'Yes' : 'No',
      'Current expense_type enum exists': enumCheck.rows[0].enum_exists ? 'Yes' : 'No',
      'Action': 'Would create missing components'
    });
    return;
  }
  
  // Execute the migration
  console.log('Executing migration on Neon DB...');
  await pool.query(migrationSQL);
  
  // Verify the migration
  const tableCheck = await pool.query(`
    SELECT EXISTS (
      SELECT 1 FROM information_schema.tables WHERE table_name = 'expenses'
    ) as table_exists;
  `);
  
  const enumCheck = await pool.query(`
    SELECT EXISTS (
      SELECT 1 FROM pg_type WHERE typname = 'expense_type'
    ) as enum_exists;
  `);
  
  if (tableCheck.rows[0].table_exists && enumCheck.rows[0].enum_exists) {
    console.log('Migration verified successfully!');
    console.log('✅ expense_type enum created');
    console.log('✅ expenses table created');
    
    logMigrationStats({
      'expense_type enum': 'Created',
      'expenses table': 'Created',
      'Migration status': 'Success'
    });
  } else {
    console.log('Migration verification failed:');
    console.log(`- expense_type enum: ${enumCheck.rows[0].enum_exists ? 'Created' : 'Not created'}`);
    console.log(`- expenses table: ${tableCheck.rows[0].table_exists ? 'Created' : 'Not created'}`);
    
    logMigrationStats({
      'expense_type enum': enumCheck.rows[0].enum_exists ? 'Created' : 'Failed',
      'expenses table': tableCheck.rows[0].table_exists ? 'Created' : 'Failed',
      'Migration status': 'Partial failure'
    });
    
    throw new Error('Migration verification failed');
  }
});
