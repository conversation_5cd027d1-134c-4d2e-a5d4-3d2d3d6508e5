import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useContextData } from "@/lib/useContextData";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from "@/components/ui/sheet";
import { getInitials } from "@/lib/utils";
import { Loader2, Plus, Search } from "lucide-react";

interface User {
  id: number;
  full_name: string;
  email: string;
  phone: string;
}

interface Agent {
  id: number;
  user_id: number;
  company_id: number;
  commission_rate: number;
  territory: string;
  active: boolean;
  user: User;
  agent_reference_code?: string;
}

export default function Agents() {
  const { companyId } = useContextData();
  const [searchQuery, setSearchQuery] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { data: agentsData, isLoading } = useQuery<Agent[]>({
    queryKey: [`/api/companies/${companyId}/agents`],
    enabled: !!companyId,
  });

  const filteredAgents = agentsData?.filter(agent =>
    (agent.user && agent.user.full_name && agent.user.full_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (agent.territory && agent.territory.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || [];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by the filter above
  };

  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Agents</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your collection agents
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Sheet open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <SheetTrigger asChild>
              <Button className="flex items-center gap-1">
                <Plus size={16} />
                <span>Add Agent</span>
              </Button>
            </SheetTrigger>
            <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
              <SheetHeader className="mb-5">
                <SheetTitle>Add New Agent</SheetTitle>
                <SheetDescription>
                  Create a new agent account for your company.
                </SheetDescription>
              </SheetHeader>
              {/* Agent form would go here in a real implementation */}
              <SheetFooter className="pt-4">
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button>Save Agent</Button>
              </SheetFooter>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search agents..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button type="submit">Search</Button>
          </form>
        </CardContent>
      </Card>

      {/* Agents List */}
      <Card>
        <CardHeader>
          <CardTitle>Agents List</CardTitle>
          <CardDescription>
            View and manage all your collection agents
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : filteredAgents.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No agents found. Add your first agent to get started.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Agent</TableHead>
                    <TableHead>Territory</TableHead>
                    <TableHead>Commission Rate</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAgents.map((agent) => (
                    <TableRow key={agent.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <Avatar className="h-10 w-10 mr-4">
                            <AvatarFallback className="bg-primary text-white">
                              {agent.user && agent.user.full_name ? getInitials(agent.user.full_name) : 'AG'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{agent.user && agent.user.full_name ? agent.user.full_name : 'Agent'}</div>
                            <div className="text-sm text-muted-foreground">
                              {agent.user && agent.user.email ? agent.user.email : 'No email'}
                              {agent.agent_reference_code && (
                                <span className="ml-2 text-xs font-medium text-primary">
                                  Ref: {agent.agent_reference_code}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{agent.territory || "-"}</TableCell>
                      <TableCell>{agent.commission_rate}%</TableCell>
                      <TableCell>
                        <Badge variant={agent.active ? "success" : "secondary"}>
                          {agent.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" className="text-red-500">
                          Deactivate
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
