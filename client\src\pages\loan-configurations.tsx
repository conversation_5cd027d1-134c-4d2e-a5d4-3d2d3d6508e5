import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/lib/auth";


// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Loader2, ArrowLeft, Plus, Info, Trash2, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Type definitions
interface FormTemplate {
  id: number;
  name: string;
  description: string | null;
  company_id: number;
  branch_id: number | null;
  is_active: boolean;
  category: string | null;
  created_at: string;
  updated_at: string;
}

interface LoanConfiguration {
  id: number;
  company_id: number;
  template_id: number;
  branch_id: number | null;
  is_active: boolean;
  order: number;
  created_at: string;
  updated_at: string;
  template?: FormTemplate;
}

export default function LoanConfigurationsPage() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const [, navigate] = useLocation();
  const queryClient = useQueryClient();
  const user = getCurrentUser();
  const companyId = user?.company_id || 0;

  // State for dialogs
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const [configToDelete, setConfigToDelete] = useState<LoanConfiguration | null>(null);
  
  // Fetch loan configurations for the company
  const { 
    data: configurations = [], 
    isLoading: isLoadingConfigurations 
  } = useQuery<(LoanConfiguration & { template: FormTemplate })[]>({
    queryKey: [`/api/companies/${companyId}/loan-configurations`],
    enabled: companyId > 0,
  });

  // Fetch all available templates
  const { 
    data: templates = [], 
    isLoading: isLoadingTemplates 
  } = useQuery<FormTemplate[]>({
    queryKey: [`/api/companies/${companyId}/form-templates`],
    enabled: companyId > 0,
  });

  // Filter out templates that already have configurations
  const availableTemplates = templates.filter(
    template => !configurations.some(config => config.template_id === template.id)
  );

  // Create mutation for new configuration
  const createMutation = useMutation({
    mutationFn: async (templateId: number) => {
      const data = {
        template_id: templateId,
        is_active: true,
        order: configurations.length, // Place at the end
        branch_id: null
      };
      
      const response = await apiRequest(
        'POST', 
        `/api/companies/${companyId}/loan-configurations`,
        data
      );
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create loan configuration');
      }
      
      return await response.json();
    },
    onSuccess: () => {
      // Refresh configurations data
      queryClient.invalidateQueries({ 
        queryKey: [`/api/companies/${companyId}/loan-configurations`] 
      });
      
      // Show success message
      toast({
        title: "Configuration created",
        description: "Loan configuration has been successfully created",
      });
      
      // Close dialog
      setShowCreateDialog(false);
      setSelectedTemplateId(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create configuration",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Toggle active status mutation
  const toggleActiveMutation = useMutation({
    mutationFn: async (configId: number) => {
      const response = await apiRequest(
        'PATCH', 
        `/api/companies/${companyId}/loan-configurations/${configId}/toggle-active`
      );
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update status');
      }
      
      return await response.json();
    },
    onSuccess: () => {
      // Refresh configurations data
      queryClient.invalidateQueries({ 
        queryKey: [`/api/companies/${companyId}/loan-configurations`] 
      });
      
      // Also refresh active configs that might be used elsewhere
      queryClient.invalidateQueries({ 
        queryKey: [`/api/companies/${companyId}/loan-configurations/active`] 
      });
      
      // Show success message
      toast({
        title: "Status updated",
        description: "Loan configuration status has been toggled",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update status",
        description: error.message,
        variant: "destructive",
      });
    }
  });
  
  // Delete configuration mutation
  const deleteMutation = useMutation({
    mutationFn: async (configId: number) => {
      const response = await apiRequest(
        'DELETE', 
        `/api/companies/${companyId}/loan-configurations/${configId}`
      );
      
      if (!response.ok) {
        // Parse the error response
        const errorData = await response.json().catch(() => null);
        
        if (errorData && errorData.error === 'configuration_in_use') {
          throw new Error('configuration_in_use');
        }
        
        throw new Error(errorData?.message || 'Failed to delete configuration');
      }
      
      return configId;
    },
    onSuccess: (deletedId) => {
      // Refresh configurations data
      queryClient.invalidateQueries({ 
        queryKey: [`/api/companies/${companyId}/loan-configurations`] 
      });
      
      // Also refresh active configs that might be used elsewhere
      queryClient.invalidateQueries({ 
        queryKey: [`/api/companies/${companyId}/loan-configurations/active`] 
      });
      
      // Show success message
      toast({
        title: "Configuration deleted",
        description: "Loan configuration has been successfully deleted",
      });
      
      // Close dialog
      setShowDeleteDialog(false);
      setConfigToDelete(null);
    },
    onError: (error: Error) => {
      if (error.message === 'configuration_in_use') {
        toast({
          title: "Cannot delete configuration",
          description: "This configuration is currently in use by existing loans. You cannot delete it while it's in use.",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Failed to delete configuration",
          description: error.message,
          variant: "destructive",
        });
      }
      
      // Close dialog even on error
      setShowDeleteDialog(false);
    }
  });

  // Handle creation of a new configuration
  const handleCreateConfiguration = () => {
    if (!selectedTemplateId) {
      toast({
        title: "Template required",
        description: "Please select a template to create a configuration",
        variant: "destructive",
      });
      return;
    }
    
    createMutation.mutate(selectedTemplateId);
  };

  // Handle toggling the active status
  const handleToggleActive = (configId: number) => {
    toggleActiveMutation.mutate(configId);
  };
  
  // Handle configuration deletion
  const handleDeleteClick = (config: LoanConfiguration & { template: FormTemplate }) => {
    setConfigToDelete(config);
    setShowDeleteDialog(true);
  };
  
  // Handle confirmed deletion
  const handleConfirmDelete = () => {
    if (configToDelete) {
      deleteMutation.mutate(configToDelete.id);
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate("/loan-form-builder")} className="text-sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Button>
          <h1 className="text-2xl font-bold">Loan Configurations</h1>
        </div>
        
        <Button 
          onClick={() => setShowCreateDialog(true)}
          disabled={availableTemplates.length === 0}
        >
          <Plus className="h-4 w-4 mr-2" />
          New Configuration
        </Button>
      </div>
      
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>About Loan Configurations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg bg-blue-50 p-4 text-blue-800">
            <div className="flex items-start">
              <Info className="mr-2 mt-0.5 h-5 w-5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold">Important: How Templates Appear in Loan Creation</h3>
                <p className="mt-1 text-sm">
                  Templates must be linked to your company through a "loan configuration" and set as <strong>active</strong> to appear in the loan creation dropdown menu. 
                  Use this page to manage which templates are available when creating loans.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Available Configurations</CardTitle>
          <CardDescription>
            Manage which loan templates are available for creating loans
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {isLoadingConfigurations ? (
            <div className="flex justify-center items-center p-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading configurations...</span>
            </div>
          ) : configurations.length === 0 ? (
            <div className="text-center py-12 border rounded-lg bg-muted/10">
              <h3 className="text-lg font-medium mb-2">No configurations found</h3>
              <p className="text-muted-foreground mb-6">
                Create a configuration to make templates available for loan creation
              </p>
              <Button 
                onClick={() => setShowCreateDialog(true)}
                disabled={availableTemplates.length === 0}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Configuration
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Template Name</TableHead>
                    <TableHead>Description</TableHead> 
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {configurations.map((config) => (
                    <TableRow key={config.id}>
                      <TableCell className="font-medium">
                        {config.template?.name || `Template #${config.template_id}`}
                      </TableCell>
                      <TableCell>
                        {config.template?.description || "No description"}
                      </TableCell>
                      <TableCell>
                        <Badge variant={config.is_active ? "default" : "secondary"} className={config.is_active ? "bg-green-500" : ""}>
                          {config.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Switch 
                              id={`active-${config.id}`}
                              checked={config.is_active}
                              onCheckedChange={() => handleToggleActive(config.id)}
                            />
                            <Label htmlFor={`active-${config.id}`}>
                              {config.is_active ? "Active" : "Inactive"}
                            </Label>
                          </div>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            className="text-destructive hover:text-destructive hover:bg-destructive/10"
                            onClick={() => handleDeleteClick(config)}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Create Configuration Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Loan Configuration</DialogTitle>
            <DialogDescription>
              Select a template to make it available for loan creation
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="template">Template</Label>
              <Select 
                value={selectedTemplateId?.toString() || ""} 
                onValueChange={(value) => setSelectedTemplateId(Number(value))}
              >
                <SelectTrigger id="template" className="w-full">
                  <SelectValue placeholder="Select a template" />
                </SelectTrigger>
                <SelectContent>
                  {availableTemplates.map((template) => (
                    <SelectItem key={template.id} value={template.id.toString()}>
                      {template.name}
                    </SelectItem>
                  ))}
                  {availableTemplates.length === 0 && (
                    <div className="p-2 text-sm text-muted-foreground">
                      No available templates
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch id="active" defaultChecked />
              <Label htmlFor="active">Active</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowCreateDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleCreateConfiguration}
              disabled={!selectedTemplateId || createMutation.isPending}
            >
              {createMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Configuration
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Configuration Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Loan Configuration</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this loan configuration?
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            {configToDelete && (
              <div className="rounded-lg bg-amber-50 p-4 mb-4 text-amber-800">
                <div className="flex items-start">
                  <AlertCircle className="mr-2 mt-0.5 h-5 w-5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold">Warning: This action cannot be undone</h3>
                    <p className="mt-1 text-sm">
                      Deleting this configuration will remove "{configToDelete.template?.name || `Template #${configToDelete.template_id}`}" 
                      from the loan creation options.
                    </p>
                    <p className="mt-1 text-sm">
                      If this template is currently in use by existing loans, the deletion will be prevented.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={handleConfirmDelete}
              disabled={deleteMutation.isPending || !configToDelete}
            >
              {deleteMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete Configuration
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}