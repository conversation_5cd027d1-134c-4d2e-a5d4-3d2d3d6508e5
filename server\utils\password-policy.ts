import { db } from '../db';
import { users, passwordHistory } from '@shared/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcrypt';

// Password policy configuration
export const PASSWORD_POLICY = {
  MIN_LENGTH: 8,
  REQUIRE_UPPERCASE: true,
  REQUIRE_LOWERCASE: true,
  REQUIRE_NUMBER: true,
  REQUIRE_SPECIAL: true,
  SPECIAL_CHARS: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  HISTORY_SIZE: 5, // Number of previous passwords to remember
  EXPIRATION_DAYS: 90, // Password expiration in days
};

/**
 * Validate a password against the password policy
 * @param password Password to validate
 * @returns Object with validation result and error message
 */
export function validatePassword(password: string): { valid: boolean; message?: string } {
  // Check minimum length
  if (password.length < PASSWORD_POLICY.MIN_LENGTH) {
    return {
      valid: false,
      message: `Password must be at least ${PASSWORD_POLICY.MIN_LENGTH} characters long`,
    };
  }

  // Check for uppercase letters
  if (PASSWORD_POLICY.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one uppercase letter',
    };
  }

  // Check for lowercase letters
  if (PASSWORD_POLICY.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one lowercase letter',
    };
  }

  // Check for numbers
  if (PASSWORD_POLICY.REQUIRE_NUMBER && !/[0-9]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one number',
    };
  }

  // Check for special characters
  if (PASSWORD_POLICY.REQUIRE_SPECIAL) {
    const specialCharsRegex = new RegExp(`[${PASSWORD_POLICY.SPECIAL_CHARS.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}]`);
    if (!specialCharsRegex.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one special character',
      };
    }
  }

  return { valid: true };
}

/**
 * Check if a password is in the user's password history
 * @param userId User ID
 * @param password Plain text password to check
 * @returns Promise<boolean> True if password is in history, false otherwise
 */
export async function isPasswordInHistory(userId: number, password: string): Promise<boolean> {
  try {
    // Get user's password history
    const history = await db
      .select()
      .from(passwordHistory)
      .where(eq(passwordHistory.user_id, userId))
      .orderBy(passwordHistory.created_at);

    // Check if password matches any in history
    for (const entry of history) {
      const match = await bcrypt.compare(password, entry.password_hash);
      if (match) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking password history:', error);
    return false;
  }
}

/**
 * Add a password to the user's password history
 * @param userId User ID
 * @param passwordHash Hashed password to add to history
 */
export async function addPasswordToHistory(userId: number, passwordHash: string): Promise<void> {
  try {
    // Add new password to history
    await db
      .insert(passwordHistory)
      .values({
        user_id: userId,
        password_hash: passwordHash,
      });

    // Get user's password history
    const history = await db
      .select()
      .from(passwordHistory)
      .where(eq(passwordHistory.user_id, userId))
      .orderBy(passwordHistory.created_at);

    // If history exceeds limit, remove oldest entries
    if (history.length > PASSWORD_POLICY.HISTORY_SIZE) {
      const entriesToRemove = history.length - PASSWORD_POLICY.HISTORY_SIZE;
      const oldestEntries = history.slice(0, entriesToRemove);
      
      for (const entry of oldestEntries) {
        await db
          .delete(passwordHistory)
          .where(eq(passwordHistory.id, entry.id));
      }
    }
  } catch (error) {
    console.error('Error adding password to history:', error);
  }
}

/**
 * Check if a user's password has expired
 * @param userId User ID
 * @returns Promise<boolean> True if password has expired, false otherwise
 */
export async function hasPasswordExpired(userId: number): Promise<boolean> {
  try {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId));

    if (!user || !user.password_updated_at) {
      // If no password update date, assume expired
      return true;
    }

    const passwordUpdatedAt = new Date(user.password_updated_at);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - passwordUpdatedAt.getTime()) / (1000 * 60 * 60 * 24));

    return diffDays >= PASSWORD_POLICY.EXPIRATION_DAYS;
  } catch (error) {
    console.error('Error checking password expiration:', error);
    return false;
  }
}
