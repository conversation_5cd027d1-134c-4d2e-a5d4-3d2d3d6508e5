import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PaymentReceiptDialog } from "@/components/receipt/PaymentReceiptDialog";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency, formatDate } from "@/lib/utils";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { z } from "zod";

import {
  AlertCircle,
  Calendar,
  CreditCard,
  Loader2,
  Receipt,
  Search,
  User,
  UserCheck
} from "lucide-react";

// Interfaces and types
interface Customer {
  id: number;
  full_name: string;
  company_id: number;
  mobile?: string;
  email?: string;
  address?: string;
}

interface Collection {
  id: number;
  loan_id: number;
  company_id: number;
  customer_id: number;
  emi_number: number;
  scheduled_date: string;
  amount: string;
  status: string;
  collection_date?: string | null;
  payment_method?: string | null;
}

interface Loan {
  id: number;
  customer_id: number;
  company_id: number;
  amount: number;
  interest_rate: number;
  term: number;
  loan_date: string;
  status: string;
  customer?: Customer;
}

interface PaymentInfo {
  collection_ids: number[];
  paymentMethod: string;
  totalAmount: number;
  customerId: number;
  loanId: number;
  notes?: string;
}

export default function QuickPayment() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const user = getCurrentUser();
  const companyId = user?.company_id || 1;

  // State for UI control
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);
  const [selectedCollections, setSelectedCollections] = useState<number[]>([]);
  const [paymentMethod, setPaymentMethod] = useState<string>("cash");
  const [notes, setNotes] = useState<string>("");
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isReceiptDialogOpen, setIsReceiptDialogOpen] = useState(false);
  const [lastCompletedCollection, setLastCompletedCollection] = useState<number | null>(null);

  // Data fetching
  const { data: customers = [], isLoading: isLoadingCustomers } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${companyId}/customers`],
    enabled: !!companyId,
  });

  // Filter customers based on search query
  const filteredCustomers = customers.filter(customer => {
    if (!searchQuery) return false; // Don't show any customers unless searching

    return (
      customer.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (customer.mobile && customer.mobile.includes(searchQuery)) ||
      (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  // Get loans for selected customer
  const { data: customerLoans = [], isLoading: isLoadingLoans } = useQuery<Loan[]>({
    queryKey: [`/api/customers/${selectedCustomer?.id}/loans`],
    enabled: !!selectedCustomer?.id,
  });

  // Get pending collections for selected loan
  const { data: collections = [], isLoading: isLoadingCollections } = useQuery<Collection[]>({
    queryKey: [`/api/companies/${companyId}/collections`, selectedLoan?.id, 'pending'],
    queryFn: async () => {
      if (!selectedLoan?.id) return [];
      // Use the company-specific collections endpoint
      const response = await apiRequest('GET', `/api/companies/${companyId}/collections?loanId=${selectedLoan.id}&status=pending`);
      if (!response.ok) {
        throw new Error('Failed to fetch collections');
      }
      return response.json();
    },
    enabled: !!selectedLoan?.id && !!companyId,
  });

  // Filter to only show pending collections - this ensures completed ones don't appear
  const pendingCollections = collections.filter(collection =>
    collection.status.toLowerCase() === 'pending'
  );

  // Calculate total amount for selected collections
  const totalAmount = pendingCollections
    .filter(collection => selectedCollections.includes(collection.id))
    .reduce((sum, collection) => sum + Number(collection.amount), 0);

  // Mutation for processing payment
  const processPaymentMutation = useMutation({
    mutationFn: async (paymentInfo: PaymentInfo) => {
      // Process all selected collections as a single payment
      const res = await apiRequest('POST', '/api/collections/batch-complete', {
        company_id: companyId,
        collection_ids: paymentInfo.collection_ids,
        payment_method: paymentInfo.paymentMethod,
        amount: paymentInfo.totalAmount,
        notes: paymentInfo.notes,
        customer_id: paymentInfo.customerId,
        loan_id: paymentInfo.loanId
      });
      return res.json();
    },
    onSuccess: (data) => {
      // Invalidate relevant queries to refresh data
      if (selectedLoan?.id) {
        queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/collections`, selectedLoan.id, 'pending'] });
      }
      queryClient.invalidateQueries({ queryKey: [`/api/customers/${selectedCustomer?.id}/loans`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/collections`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/recent-collections`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/dashboard-metrics`] });

      // Reset selections
      setSelectedCollections([]);

      // Show success message
      toast({
        title: "Payment Processed",
        description: "The payment has been processed successfully. A receipt has been generated.",
      });

      // Open receipt dialog
      if (data && data.collectionId) {
        setLastCompletedCollection(data.collectionId);
        setIsReceiptDialogOpen(true);
      }

      // Close confirm dialog
      setIsConfirmDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to process payment",
        variant: "destructive",
      });
      setIsConfirmDialogOpen(false);
    },
  });

  // Handle search form submit
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by filtering customers in real-time
  };

  // Handle customer selection
  const handleSelectCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setSelectedLoan(null);
    setSelectedCollections([]);
  };

  // Handle loan selection
  const handleSelectLoan = (loan: Loan) => {
    setSelectedLoan(loan);
    setSelectedCollections([]);
  };

  // Function to check if a collection can be completed
  const canCompleteCollection = (collection: Collection): { canComplete: boolean; reason?: string } => {
    // If collection is already completed, it can't be completed again
    if (collection.status === 'completed') {
      return { canComplete: false, reason: 'Collection is already completed' };
    }

    // Get all collections for the same loan
    const loanCollections = pendingCollections || [];

    // Sort by EMI number if available, otherwise allow completion (backward compatibility)
    if (!collection.emi_number) {
      return { canComplete: true };
    }

    // Check if there are any incomplete collections with lower EMI numbers
    const incompleteEarlierCollections = loanCollections.filter(c =>
      c.emi_number &&
      c.emi_number < collection.emi_number! &&
      c.status !== 'completed'
    );

    if (incompleteEarlierCollections.length > 0) {
      // Find the earliest incomplete collection
      const earliestIncomplete = incompleteEarlierCollections.reduce((earliest, current) =>
        (current.emi_number! < earliest.emi_number!) ? current : earliest
      );

      return {
        canComplete: false,
        reason: `Please complete Collection ${earliestIncomplete.emi_number} before completing this one.`
      };
    }

    return { canComplete: true };
  };

  // Handle collection selection
  const handleToggleCollection = (id: number) => {
    const collection = pendingCollections.find(c => c.id === id);
    if (!collection) return;

    // Check if collection can be completed
    const { canComplete, reason } = canCompleteCollection(collection);

    if (!canComplete) {
      toast({
        title: "Cannot Select Collection",
        description: reason || "This collection cannot be completed at this time.",
        variant: "destructive",
      });
      return;
    }

    if (selectedCollections.includes(id)) {
      setSelectedCollections(selectedCollections.filter(collectionId => collectionId !== id));
    } else {
      setSelectedCollections([...selectedCollections, id]);
    }
  };

  // Handle select all collections
  const handleSelectAllCollections = () => {
    if (selectedCollections.length === pendingCollections.length) {
      // If all are selected, unselect all
      setSelectedCollections([]);
    } else {
      // Otherwise, select all
      setSelectedCollections(pendingCollections.map(collection => collection.id));
    }
  };

  // Handle process payment click
  const handleProcessPayment = () => {
    if (selectedCollections.length === 0) {
      toast({
        title: "No collections selected",
        description: "Please select at least one collection to process.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedCustomer || !selectedLoan) {
      toast({
        title: "Missing information",
        description: "Please select a customer and loan.",
        variant: "destructive",
      });
      return;
    }

    // Open confirmation dialog
    setIsConfirmDialogOpen(true);
  };

  // Handle confirm payment
  const handleConfirmPayment = () => {
    if (!selectedCustomer || !selectedLoan) return;

    const paymentInfo: PaymentInfo = {
      collection_ids: selectedCollections,
      paymentMethod,
      totalAmount,
      customerId: selectedCustomer.id,
      loanId: selectedLoan.id,
      notes
    };

    processPaymentMutation.mutate(paymentInfo);
  };

  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Quick Payment</h1>
          <p className="mt-1 text-sm text-gray-500">
            Process payments for loan installments
          </p>
        </div>
      </div>

      {/* Search Card */}
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Search Customer</CardTitle>
          <CardDescription>Find a customer by name, mobile or email</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search by name, mobile or email..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button type="submit">Search</Button>
          </form>

          {/* Customer Results */}
          {searchQuery && (
            <div className="mt-4">
              {isLoadingCustomers ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : filteredCustomers.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <UserCheck className="h-12 w-12 text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">No customers found</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Try searching with a different name, mobile or email
                  </p>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-0">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Mobile</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead className="text-right">Action</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredCustomers.map((customer) => (
                          <TableRow key={customer.id} className={selectedCustomer?.id === customer.id ? "bg-muted/50" : ""}>
                            <TableCell className="font-medium">{customer.full_name}</TableCell>
                            <TableCell>{customer.mobile || "-"}</TableCell>
                            <TableCell>{customer.email || "-"}</TableCell>
                            <TableCell className="text-right">
                              <Button
                                size="sm"
                                variant={selectedCustomer?.id === customer.id ? "default" : "outline"}
                                onClick={() => handleSelectCustomer(customer)}
                              >
                                {selectedCustomer?.id === customer.id ? "Selected" : "Select"}
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected Customer Card */}
      {selectedCustomer && (
        <Card className="mb-4">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Selected Customer</CardTitle>
            <CardDescription>Customer and loan details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-start space-x-3">
                  <div className="flex h-10 w-10 rounded-full items-center justify-center bg-primary/10">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">{selectedCustomer.full_name}</h3>
                    {selectedCustomer.mobile && (
                      <p className="text-sm text-muted-foreground">{selectedCustomer.mobile}</p>
                    )}
                    {selectedCustomer.email && (
                      <p className="text-sm text-muted-foreground">{selectedCustomer.email}</p>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Select Loan</Label>
                <Select value={selectedLoan ? selectedLoan.id.toString() : ""} onValueChange={(value) => {
                  const loan = customerLoans.find(l => l.id === parseInt(value));
                  if (loan) handleSelectLoan(loan);
                }}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a loan" />
                  </SelectTrigger>
                  <SelectContent>
                    {customerLoans.map(loan => (
                      <SelectItem key={loan.id} value={loan.id.toString()}>
                        Loan #{loan.id} - {formatCurrency(loan.amount)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {isLoadingLoans && (
                  <div className="flex items-center mt-2 text-sm text-muted-foreground">
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                    Loading loans...
                  </div>
                )}
                {!isLoadingLoans && customerLoans.length === 0 && (
                  <p className="text-sm text-muted-foreground mt-2">No loans found for this customer</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Collections Card */}
      {selectedLoan && (
        <Card className="mb-4">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">Collections</CardTitle>
                <CardDescription>Select collections to pay</CardDescription>
              </div>
              <div className="text-right">
                <div className="font-medium">Total Selected</div>
                <div className="text-2xl font-bold text-primary">
                  {formatCurrency(totalAmount)}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoadingCollections ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : pendingCollections.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Receipt className="h-12 w-12 text-muted-foreground mb-2" />
                <h3 className="text-lg font-medium">No pending collections</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  This loan has no pending collections to pay
                </p>
              </div>
            ) : (
              <div>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">
                          <Checkbox
                            checked={selectedCollections.length === pendingCollections.length && pendingCollections.length > 0}
                            onCheckedChange={handleSelectAllCollections}
                            aria-label="Select all"
                          />
                        </TableHead>
                        <TableHead>Installment #</TableHead>
                        <TableHead>Due Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pendingCollections.map((collection) => {
                        const { canComplete, reason } = canCompleteCollection(collection);
                        return (
                          <TableRow
                            key={collection.id}
                            className={!canComplete ? "opacity-50" : ""}
                          >
                            <TableCell>
                              <Checkbox
                                checked={selectedCollections.includes(collection.id)}
                                onCheckedChange={() => handleToggleCollection(collection.id)}
                                aria-label={`Select collection ${collection.emi_number}`}
                                disabled={!canComplete}
                                title={canComplete ? "Select collection" : reason}
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              #{collection.emi_number}
                              {!canComplete && (
                                <span className="ml-2 text-xs text-red-500" title={reason}>
                                  (Restricted)
                                </span>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                                {formatDate(collection.scheduled_date)}
                              </div>
                            </TableCell>
                            <TableCell>{formatCurrency(Number(collection.amount))}</TableCell>
                            <TableCell>
                              <Badge variant="outline" className="capitalize">{collection.status}</Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-6 space-y-4">
                  <div>
                    <Label htmlFor="payment-method">Payment Method</Label>
                    <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                      <SelectTrigger id="payment-method" className="mt-1">
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="upi">UPI</SelectItem>
                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="payment-notes">Notes (Optional)</Label>
                    <Input
                      id="payment-notes"
                      placeholder="Any additional notes..."
                      className="mt-1"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                    />
                  </div>

                  <Button
                    className="w-full"
                    size="lg"
                    disabled={selectedCollections.length === 0 || processPaymentMutation.isPending}
                    onClick={handleProcessPayment}
                  >
                    {processPaymentMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    {selectedCollections.length > 0
                      ? `Process Payment (${formatCurrency(totalAmount)})`
                      : "Select installments to pay"
                    }
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Payment</DialogTitle>
            <DialogDescription>
              Are you sure you want to process this payment?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Customer:</span>
              <span>{selectedCustomer?.full_name}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Loan ID:</span>
              <span>#{selectedLoan?.id}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Installments:</span>
              <span>{selectedCollections.length} selected</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Payment Method:</span>
              <span className="capitalize">{paymentMethod.replace('_', ' ')}</span>
            </div>
            <div className="flex justify-between items-center font-bold text-lg">
              <span>Total Amount:</span>
              <span className="text-primary">{formatCurrency(totalAmount)}</span>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmPayment}
              disabled={processPaymentMutation.isPending}
            >
              {processPaymentMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Confirm Payment
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Payment Receipt Dialog */}
      {lastCompletedCollection && (
        <PaymentReceiptDialog
          open={isReceiptDialogOpen}
          onOpenChange={setIsReceiptDialogOpen}
          collectionId={lastCompletedCollection}
          companyId={companyId}
        />
      )}
    </div>
  );
}