import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Collections from '../src/pages/collections/index';

// Mock the auth hook
vi.mock('@/lib/auth', () => ({
  useAuth: () => ({
    getCurrentUser: () => ({
      id: 1,
      company_id: 1,
      role: 'admin'
    })
  })
}));

// Mock the API request function
vi.mock('@/lib/queryClient', () => ({
  apiRequest: vi.fn(),
  queryClient: new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
}));

// Mock data
const mockCollections = Array.from({ length: 25 }, (_, i) => ({
  id: i + 1,
  loan_id: Math.floor(i / 5) + 1,
  customer_id: Math.floor(i / 3) + 1,
  company_id: 1,
  agent_id: null,
  amount: (1000 + i * 100).toString(),
  scheduled_date: new Date(2024, 0, i + 1).toISOString(),
  collection_date: null,
  status: i % 3 === 0 ? 'completed' : 'pending',
  payment_method: null,
  receipt_id: null,
  notes: `Collection ${i + 1}`,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
}));

const mockCustomers = Array.from({ length: 10 }, (_, i) => ({
  id: i + 1,
  full_name: `Customer ${i + 1}`,
  company_id: 1,
  mobile: `*********${i}`,
  email: `customer${i + 1}@example.com`
}));

const mockLoans = Array.from({ length: 5 }, (_, i) => ({
  id: i + 1,
  customer_id: Math.floor(i / 2) + 1,
  company_id: 1,
  amount: 10000 + i * 5000,
  interest_rate: 12,
  term: 12,
  loan_reference_code: `LOAN-${String(i + 1).padStart(3, '0')}`
}));

const mockAgents = Array.from({ length: 3 }, (_, i) => ({
  id: i + 1,
  user_id: i + 1,
  company_id: 1,
  full_name: `Agent ${i + 1}`
}));

describe('Collections Pagination', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    // Mock the fetch responses
    global.fetch = vi.fn().mockImplementation((url: string) => {
      if (url.includes('/customers')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockCustomers)
        });
      }
      if (url.includes('/loans')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockLoans)
        });
      }
      if (url.includes('/agents')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAgents)
        });
      }
      if (url.includes('/collections')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            collections: mockCollections,
            pagination: {
              page: 1,
              limit: 10,
              totalCount: mockCollections.length,
              totalPages: Math.ceil(mockCollections.length / 10)
            }
          })
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve([])
      });
    });
  });

  const renderCollections = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <Collections />
      </QueryClientProvider>
    );
  };

  it('should display pagination controls when there are more than 10 records', async () => {
    renderCollections();

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText(/Showing.*of.*collections/)).toBeInTheDocument();
    });

    // Check if page size selector is visible
    expect(screen.getByText('Show:')).toBeInTheDocument();
    expect(screen.getByText('per page')).toBeInTheDocument();
  });

  it('should allow changing page size', async () => {
    renderCollections();

    await waitFor(() => {
      expect(screen.getByText(/Showing.*of.*collections/)).toBeInTheDocument();
    });

    // Find and click the page size selector (the one with width w-20)
    const pageSizeSelects = screen.getAllByRole('combobox');
    const pageSizeSelect = pageSizeSelects.find(select =>
      select.className.includes('w-20')
    );
    expect(pageSizeSelect).toBeInTheDocument();
    fireEvent.click(pageSizeSelect!);

    // Wait for options to appear and select 25
    await waitFor(() => {
      const option25 = screen.getByRole('option', { name: '25' });
      expect(option25).toBeInTheDocument();
      fireEvent.click(option25);
    });

    // Verify the page size changed
    await waitFor(() => {
      expect(screen.getByDisplayValue('25')).toBeInTheDocument();
    });
  });

  it('should show "Show All" button and allow viewing all records', async () => {
    renderCollections();

    await waitFor(() => {
      expect(screen.getByText(/Showing.*of.*collections/)).toBeInTheDocument();
    });

    // Find and click the "Show All" button
    const showAllButton = screen.getByText(/Show All \(\d+\)/);
    expect(showAllButton).toBeInTheDocument();
    fireEvent.click(showAllButton);

    // Verify "Show Paginated" button appears
    await waitFor(() => {
      expect(screen.getByText('Show Paginated')).toBeInTheDocument();
    });

    // Verify the display text changes
    expect(screen.getByText(/Showing all \d+ collections/)).toBeInTheDocument();
  });

  it('should reset to paginated view when "Show Paginated" is clicked', async () => {
    renderCollections();

    await waitFor(() => {
      expect(screen.getByText(/Showing.*of.*collections/)).toBeInTheDocument();
    });

    // Click "Show All" first
    const showAllButton = screen.getByText(/Show All \(\d+\)/);
    fireEvent.click(showAllButton);

    await waitFor(() => {
      expect(screen.getByText('Show Paginated')).toBeInTheDocument();
    });

    // Click "Show Paginated"
    const showPaginatedButton = screen.getByText('Show Paginated');
    fireEvent.click(showPaginatedButton);

    // Verify we're back to paginated view
    await waitFor(() => {
      expect(screen.getByText(/Show All \(\d+\)/)).toBeInTheDocument();
    });
  });

  it('should reset showAll state when filters are applied', async () => {
    renderCollections();

    await waitFor(() => {
      expect(screen.getByText(/Showing.*of.*collections/)).toBeInTheDocument();
    });

    // Click "Show All" first
    const showAllButton = screen.getByText(/Show All \(\d+\)/);
    fireEvent.click(showAllButton);

    await waitFor(() => {
      expect(screen.getByText('Show Paginated')).toBeInTheDocument();
    });

    // Apply a status filter - find the status filter select (not the page size one)
    const statusFilter = screen.getByDisplayValue('All Status');
    fireEvent.click(statusFilter);

    await waitFor(() => {
      const pendingOption = screen.getByRole('option', { name: 'Pending' });
      fireEvent.click(pendingOption);
    });

    // Verify we're back to paginated view
    await waitFor(() => {
      expect(screen.getByText(/Show All \(\d+\)/)).toBeInTheDocument();
    });
  });
});
