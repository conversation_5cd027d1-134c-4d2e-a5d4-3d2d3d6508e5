import { Express, Response } from 'express';
import { authMiddleware, requirePermission, AuthRequest } from '../middleware/auth';
import { TemporaryPermissionService } from '../services/temporaryPermissionService';
import { insertTemporaryPermissionSchema, insertPermissionElevationRequestSchema } from '../../shared/schema';
import { z } from 'zod';

const tempPermissionService = new TemporaryPermissionService();

// Validation schemas
const grantTemporaryPermissionSchema = z.object({
  user_id: z.number().int().positive(),
  permission_id: z.number().int().positive(),
  reason: z.string().min(1).max(500),
  duration_hours: z.number().int().min(1).max(8760).default(24), // Max 1 year
  is_emergency: z.boolean().default(false)
});

const revokeTemporaryPermissionSchema = z.object({
  temp_permission_id: z.number().int().positive(),
  reason: z.string().min(1).max(500)
});

const reviewElevationRequestSchema = z.object({
  status: z.enum(['approved', 'denied']),
  review_notes: z.string().optional()
});

const emergencyAccessSchema = z.object({
  user_id: z.number().int().positive(),
  permission_code: z.string().min(1),
  emergency_reason: z.string().min(1).max(500),
  duration_hours: z.number().int().min(1).max(24).default(4) // Max 24 hours for emergency
});

const logEmergencyAccessSchema = z.object({
  permission_code: z.string().min(1),
  action_performed: z.string().min(1).max(500),
  emergency_reason: z.string().min(1).max(500),
  resource_accessed: z.string().optional(),
  additional_context: z.any().optional()
});

export function registerTemporaryPermissionRoutes(app: Express): void {

  // ==================== TEMPORARY PERMISSION MANAGEMENT ====================

  // Grant temporary permission
  app.post('/api/temporary-permissions', authMiddleware, requirePermission('permission_grant'), async (req: AuthRequest, res: Response) => {
    try {
      const result = grantTemporaryPermissionSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { user_id, permission_id, reason, duration_hours, is_emergency } = result.data;

      const tempPermission = await tempPermissionService.grantTemporaryPermission(
        user_id,
        permission_id,
        req.user!.id,
        reason,
        duration_hours,
        is_emergency
      );

      return res.status(201).json(tempPermission);
    } catch (error: any) {
      console.error('Error granting temporary permission:', error);
      return res.status(400).json({
        message: error.message || 'Failed to grant temporary permission'
      });
    }
  });

  // Revoke temporary permission
  app.delete('/api/temporary-permissions/:id', authMiddleware, requirePermission('permission_revoke'), async (req: AuthRequest, res: Response) => {
    try {
      const tempPermissionId = parseInt(req.params.id);
      const result = revokeTemporaryPermissionSchema.safeParse(req.body);

      if (isNaN(tempPermissionId)) {
        return res.status(400).json({ message: 'Invalid temporary permission ID' });
      }

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { reason } = result.data;

      const revoked = await tempPermissionService.revokeTemporaryPermission(
        tempPermissionId,
        req.user!.id,
        reason
      );

      if (!revoked) {
        return res.status(404).json({ message: 'Temporary permission not found or already revoked' });
      }

      return res.json(revoked);
    } catch (error: any) {
      console.error('Error revoking temporary permission:', error);
      return res.status(500).json({
        message: error.message || 'Failed to revoke temporary permission'
      });
    }
  });

  // Get active temporary permissions for a user
  app.get('/api/users/:userId/temporary-permissions', authMiddleware, requirePermission('permission_view'), async (req: AuthRequest, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const permissionId = req.query.permission_id ? parseInt(req.query.permission_id as string) : undefined;

      if (isNaN(userId)) {
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      // Users can only view their own temporary permissions unless they have admin permission
      if (userId !== req.user!.id && !req.user!.permissions.includes('user_admin')) {
        return res.status(403).json({ message: 'You can only view your own temporary permissions' });
      }

      const tempPermissions = await tempPermissionService.getActiveTemporaryPermissions(userId, permissionId);
      return res.json(tempPermissions);
    } catch (error: any) {
      console.error('Error getting temporary permissions:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get temporary permissions'
      });
    }
  });

  // Check if user has temporary permission
  app.get('/api/users/:userId/temporary-permissions/check/:permissionCode', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const permissionCode = req.params.permissionCode;

      if (isNaN(userId)) {
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      // Users can only check their own permissions unless they have admin permission
      if (userId !== req.user!.id && !req.user!.permissions.includes('user_admin')) {
        return res.status(403).json({ message: 'You can only check your own permissions' });
      }

      const hasPermission = await tempPermissionService.hasTemporaryPermission(userId, permissionCode);
      return res.json({ hasPermission });
    } catch (error: any) {
      console.error('Error checking temporary permission:', error);
      return res.status(500).json({
        message: error.message || 'Failed to check temporary permission'
      });
    }
  });

  // Cleanup expired temporary permissions (admin only)
  app.post('/api/temporary-permissions/cleanup', authMiddleware, requirePermission('system_admin'), async (req: AuthRequest, res: Response) => {
    try {
      const cleanedCount = await tempPermissionService.cleanupExpiredPermissions();
      return res.json({
        message: `Cleaned up ${cleanedCount} expired temporary permissions`,
        cleanedCount
      });
    } catch (error: any) {
      console.error('Error cleaning up expired permissions:', error);
      return res.status(500).json({
        message: error.message || 'Failed to cleanup expired permissions'
      });
    }
  });

  // ==================== PERMISSION ELEVATION REQUESTS ====================

  // Create elevation request
  app.post('/api/elevation-requests', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const result = insertPermissionElevationRequestSchema.safeParse({
        ...req.body,
        requested_by: req.user!.id
      });

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const request = await tempPermissionService.createElevationRequest(result.data);
      return res.status(201).json(request);
    } catch (error: any) {
      console.error('Error creating elevation request:', error);
      return res.status(400).json({
        message: error.message || 'Failed to create elevation request'
      });
    }
  });

  // Get pending elevation requests
  app.get('/api/elevation-requests/pending', authMiddleware, requirePermission('permission_approve'), async (req: AuthRequest, res: Response) => {
    try {
      const priority = req.query.priority as any;

      const requests = await tempPermissionService.getPendingElevationRequests(priority);
      return res.json(requests);
    } catch (error: any) {
      console.error('Error getting pending elevation requests:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get pending elevation requests'
      });
    }
  });

  // Review elevation request
  app.put('/api/elevation-requests/:id/review', authMiddleware, requirePermission('permission_approve'), async (req: AuthRequest, res: Response) => {
    try {
      const requestId = parseInt(req.params.id);
      const result = reviewElevationRequestSchema.safeParse(req.body);

      if (isNaN(requestId)) {
        return res.status(400).json({ message: 'Invalid request ID' });
      }

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { status, review_notes } = result.data;

      const reviewed = await tempPermissionService.reviewElevationRequest(
        requestId,
        req.user!.id,
        status,
        review_notes
      );

      if (!reviewed) {
        return res.status(404).json({ message: 'Elevation request not found' });
      }

      return res.json(reviewed);
    } catch (error: any) {
      console.error('Error reviewing elevation request:', error);
      return res.status(500).json({
        message: error.message || 'Failed to review elevation request'
      });
    }
  });

  // Get specific elevation request
  app.get('/api/elevation-requests/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const requestId = parseInt(req.params.id);

      if (isNaN(requestId)) {
        return res.status(400).json({ message: 'Invalid request ID' });
      }

      const request = await tempPermissionService.getElevationRequest(requestId);

      if (!request) {
        return res.status(404).json({ message: 'Elevation request not found' });
      }

      // Users can only view their own requests unless they have approval permission
      if (request.user_id !== req.user!.id && !req.user!.permissions.includes('permission_approve')) {
        return res.status(403).json({ message: 'You can only view your own elevation requests' });
      }

      return res.json(request);
    } catch (error: any) {
      console.error('Error getting elevation request:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get elevation request'
      });
    }
  });

  // ==================== EMERGENCY ACCESS ====================

  // Grant emergency access (requires emergency_access permission)
  app.post('/api/emergency-access', authMiddleware, requirePermission('emergency_access'), async (req: AuthRequest, res: Response) => {
    try {
      const result = emergencyAccessSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { user_id, permission_code, emergency_reason, duration_hours } = result.data;

      const emergencyPermission = await tempPermissionService.grantEmergencyAccess(
        user_id,
        permission_code,
        emergency_reason,
        req.user!.id,
        duration_hours
      );

      return res.status(201).json(emergencyPermission);
    } catch (error: any) {
      console.error('Error granting emergency access:', error);
      return res.status(400).json({
        message: error.message || 'Failed to grant emergency access'
      });
    }
  });

  // Log emergency access usage
  app.post('/api/emergency-access/log', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const result = logEmergencyAccessSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { permission_code, action_performed, emergency_reason, resource_accessed, additional_context } = result.data;

      await tempPermissionService.logEmergencyAccess({
        userId: req.user!.id,
        permissionCode: permission_code,
        actionPerformed: action_performed,
        emergencyReason: emergency_reason,
        sessionId: req.sessionID,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        resourceAccessed: resource_accessed,
        additionalContext: additional_context
      });

      return res.json({ message: 'Emergency access logged successfully' });
    } catch (error: any) {
      console.error('Error logging emergency access:', error);
      return res.status(500).json({
        message: error.message || 'Failed to log emergency access'
      });
    }
  });

  // Check if user has emergency access
  app.get('/api/users/:userId/emergency-access/:permissionCode', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const permissionCode = req.params.permissionCode;

      if (isNaN(userId)) {
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      // Users can only check their own emergency access unless they have admin permission
      if (userId !== req.user!.id && !req.user!.permissions.includes('user_admin')) {
        return res.status(403).json({ message: 'You can only check your own emergency access' });
      }

      const hasEmergencyAccess = await tempPermissionService.hasEmergencyAccess(userId, permissionCode);
      return res.json({ hasEmergencyAccess });
    } catch (error: any) {
      console.error('Error checking emergency access:', error);
      return res.status(500).json({
        message: error.message || 'Failed to check emergency access'
      });
    }
  });

  // Get emergency access logs for a user
  app.get('/api/users/:userId/emergency-access/logs', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const userId = parseInt(req.params.userId);
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;

      if (isNaN(userId)) {
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      // Users can only view their own logs unless they have admin permission
      if (userId !== req.user!.id && !req.user!.permissions.includes('audit_view')) {
        return res.status(403).json({ message: 'You can only view your own emergency access logs' });
      }

      const logs = await tempPermissionService.getEmergencyAccessLogs(userId, limit);
      return res.json(logs);
    } catch (error: any) {
      console.error('Error getting emergency access logs:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get emergency access logs'
      });
    }
  });

  // Get all emergency access logs (admin only)
  app.get('/api/emergency-access/logs', authMiddleware, requirePermission('audit_view'), async (req: AuthRequest, res: Response) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;

      const logs = await tempPermissionService.getAllEmergencyAccessLogs(limit);
      return res.json(logs);
    } catch (error: any) {
      console.error('Error getting all emergency access logs:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get emergency access logs'
      });
    }
  });
}
