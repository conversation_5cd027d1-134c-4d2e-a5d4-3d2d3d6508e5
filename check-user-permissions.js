const { Client } = require('pg');

async function checkUserPermissions() {
  const client = new Client({
    connectionString: '****************************************************/Trackfina-dev?sslmode=disable'
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check current user (vetrivel, ID 17)
    const userResult = await client.query(`
      SELECT id, email, full_name, role, company_id 
      FROM users 
      WHERE id = 17
    `);

    if (userResult.rows.length === 0) {
      console.log('User with ID 17 not found');
      return;
    }

    const user = userResult.rows[0];
    console.log('\n👤 Current User:', user);

    // Check user's roles
    const userRolesResult = await client.query(`
      SELECT ur.role_id, cr.name as role_name, cr.description
      FROM user_roles ur
      JOIN custom_roles cr ON ur.role_id = cr.id
      WHERE ur.user_id = 17
    `);

    console.log('\n🎭 User Roles:');
    if (userRolesResult.rows.length === 0) {
      console.log('  No roles assigned');
    } else {
      userRolesResult.rows.forEach(role => {
        console.log(`  - ${role.role_name} (ID: ${role.role_id}): ${role.description}`);
      });
    }

    // Check user's permissions
    const userPermissionsResult = await client.query(`
      SELECT DISTINCT p.code, p.name, p.category
      FROM users u
      JOIN user_roles ur ON u.id = ur.user_id
      JOIN role_permissions rp ON ur.role_id = rp.role_id
      JOIN permissions p ON rp.permission_id = p.id
      WHERE u.id = 17
      ORDER BY p.category, p.code
    `);

    console.log('\n🔐 User Permissions:');
    if (userPermissionsResult.rows.length === 0) {
      console.log('  No permissions found');
    } else {
      const permissionsByCategory = {};
      userPermissionsResult.rows.forEach(perm => {
        if (!permissionsByCategory[perm.category]) {
          permissionsByCategory[perm.category] = [];
        }
        permissionsByCategory[perm.category].push(perm);
      });

      Object.entries(permissionsByCategory).forEach(([category, perms]) => {
        console.log(`  ${category}:`);
        perms.forEach(perm => {
          console.log(`    - ${perm.code}: ${perm.name}`);
        });
      });
    }

    // Check if user has role_view permission
    const hasRoleView = userPermissionsResult.rows.some(p => p.code === 'role_view');
    console.log(`\n🔍 Has 'role_view' permission: ${hasRoleView ? '✅ YES' : '❌ NO'}`);

    // If user doesn't have role_view, let's see what roles have it
    if (!hasRoleView) {
      console.log('\n🔍 Roles that have role_view permission:');
      const roleViewRolesResult = await client.query(`
        SELECT DISTINCT cr.id, cr.name, cr.description
        FROM custom_roles cr
        JOIN role_permissions rp ON cr.id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE p.code = 'role_view'
        ORDER BY cr.name
      `);

      if (roleViewRolesResult.rows.length === 0) {
        console.log('  No roles have role_view permission');
      } else {
        roleViewRolesResult.rows.forEach(role => {
          console.log(`  - ${role.name} (ID: ${role.id}): ${role.description}`);
        });

        // Let's assign the user to the first role that has role_view permission
        const firstRole = roleViewRolesResult.rows[0];
        console.log(`\n🔧 Assigning user to role: ${firstRole.name}`);
        
        await client.query(`
          INSERT INTO user_roles (user_id, role_id, assigned_at)
          VALUES (17, $1, NOW())
          ON CONFLICT (user_id, role_id) DO NOTHING
        `, [firstRole.id]);

        console.log('✅ Role assigned successfully!');
      }
    }

    // Check available roles for the user's company
    console.log('\n🏢 Available roles for user\'s company:');
    const companyRolesResult = await client.query(`
      SELECT id, name, description, is_system
      FROM custom_roles
      WHERE company_id = $1 OR is_system = true
      ORDER BY is_system DESC, name
    `, [user.company_id]);

    companyRolesResult.rows.forEach(role => {
      console.log(`  - ${role.name} (ID: ${role.id})${role.is_system ? ' [SYSTEM]' : ''}: ${role.description}`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

checkUserPermissions();
