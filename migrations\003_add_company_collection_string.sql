-- Migration to add company_collection_string column to collections table
-- This column will store company-specific collection identifiers

-- Add company_collection_string column to collections table
ALTER TABLE "collections" 
  ADD COLUMN IF NOT EXISTS "company_collection_string" TEXT;

-- Create an index for faster lookups by company_collection_string
CREATE INDEX IF NOT EXISTS idx_collections_company_collection_string ON collections(company_collection_string);

-- Comment on the column to document its purpose
COMMENT ON COLUMN collections.company_collection_string IS 'Company-specific collection identifier string (e.g., GS001) that is unique within each company';
