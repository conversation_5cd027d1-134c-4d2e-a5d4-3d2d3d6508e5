/**
 * Company Prefix Settings Service
 * 
 * This service handles the creation, management, and validation of company prefix settings.
 * It ensures all companies have proper prefix configurations for entity reference codes.
 */

import { db } from '../db';
import { companyPrefixSettings, companies } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import { 
  DEFAULT_PREFIX_SETTINGS, 
  createPrefixSettingsForCompany,
  validatePrefixSettings,
  type DefaultPrefixTemplate 
} from '../config/defaultPrefixSettings';
import errorLogger from '../utils/errorLogger';

export class CompanyPrefixSettingsService {
  /**
   * Create default prefix settings for a new company
   */
  async createDefaultPrefixSettings(companyId: number, companyName?: string, templateName?: string): Promise<any> {
    try {
      console.log(`Creating default prefix settings for company ${companyId}`);
      
      // Get company name if not provided
      if (!companyName) {
        const company = await db.query.companies.findFirst({
          where: eq(companies.id, companyId),
        });
        companyName = company?.name || `Company ${companyId}`;
      }
      
      // Check if prefix settings already exist
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });
      
      if (existingSettings) {
        console.log(`Prefix settings already exist for company ${companyId}`);
        return existingSettings;
      }
      
      // Create prefix settings
      const prefixData = createPrefixSettingsForCompany(companyId, companyName, templateName);
      
      console.log(`Creating prefix settings for company ${companyId}:`, prefixData);
      
      const [result] = await db
        .insert(companyPrefixSettings)
        .values(prefixData)
        .returning();
      
      console.log(`✅ Prefix settings created successfully for company ${companyId}`);
      return result;
      
    } catch (error) {
      console.error(`❌ Error creating prefix settings for company ${companyId}:`, error);
      errorLogger.logError(
        `Failed to create prefix settings for company ${companyId}`,
        'prefix-settings-creation',
        error as Error
      );
      throw error;
    }
  }
  
  /**
   * Get prefix settings for a company, creating defaults if missing
   */
  async getOrCreatePrefixSettings(companyId: number): Promise<any> {
    try {
      // Try to get existing settings
      const settings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });
      
      if (settings) {
        return settings;
      }
      
      // Create default settings if missing
      console.log(`No prefix settings found for company ${companyId}, creating defaults`);
      return await this.createDefaultPrefixSettings(companyId);
      
    } catch (error) {
      console.error(`Error getting/creating prefix settings for company ${companyId}:`, error);
      throw error;
    }
  }
  
  /**
   * Update prefix settings for a company
   */
  async updatePrefixSettings(companyId: number, updates: Partial<DefaultPrefixTemplate>): Promise<any> {
    try {
      // Validate the updates
      const validationErrors = validatePrefixSettings(updates);
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }
      
      // Check if settings exist
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });
      
      if (!existingSettings) {
        throw new Error(`No prefix settings found for company ${companyId}`);
      }
      
      // Update settings
      const [result] = await db
        .update(companyPrefixSettings)
        .set({
          ...updates,
          updated_at: new Date(),
        })
        .where(eq(companyPrefixSettings.company_id, companyId))
        .returning();
      
      console.log(`✅ Prefix settings updated for company ${companyId}`);
      return result;
      
    } catch (error) {
      console.error(`❌ Error updating prefix settings for company ${companyId}:`, error);
      throw error;
    }
  }
  
  /**
   * Check if a company has prefix settings
   */
  async hasPrefixSettings(companyId: number): Promise<boolean> {
    try {
      const settings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });
      
      return !!settings;
    } catch (error) {
      console.error(`Error checking prefix settings for company ${companyId}:`, error);
      return false;
    }
  }
  
  /**
   * Get companies without prefix settings
   */
  async getCompaniesWithoutPrefixSettings(): Promise<any[]> {
    try {
      // Get all companies
      const allCompanies = await db.query.companies.findMany();
      
      // Get companies with prefix settings
      const companiesWithSettings = await db.query.companyPrefixSettings.findMany();
      const companyIdsWithSettings = new Set(companiesWithSettings.map(s => s.company_id));
      
      // Filter companies without settings
      const companiesWithoutSettings = allCompanies.filter(
        company => !companyIdsWithSettings.has(company.id)
      );
      
      return companiesWithoutSettings;
    } catch (error) {
      console.error('Error getting companies without prefix settings:', error);
      throw error;
    }
  }
  
  /**
   * Create prefix settings for all companies that don't have them
   */
  async createPrefixSettingsForAllCompanies(): Promise<{ success: number; failed: number; errors: string[] }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };
    
    try {
      const companiesWithoutSettings = await this.getCompaniesWithoutPrefixSettings();
      
      console.log(`Found ${companiesWithoutSettings.length} companies without prefix settings`);
      
      for (const company of companiesWithoutSettings) {
        try {
          await this.createDefaultPrefixSettings(company.id, company.name);
          results.success++;
          console.log(`✅ Created prefix settings for company ${company.id} (${company.name})`);
        } catch (error) {
          results.failed++;
          const errorMessage = `Failed to create prefix settings for company ${company.id} (${company.name}): ${error}`;
          results.errors.push(errorMessage);
          console.error(`❌ ${errorMessage}`);
        }
      }
      
      console.log(`Migration completed: ${results.success} success, ${results.failed} failed`);
      return results;
      
    } catch (error) {
      console.error('Error in bulk prefix settings creation:', error);
      throw error;
    }
  }
  
  /**
   * Validate prefix uniqueness across companies (optional feature)
   */
  async validatePrefixUniqueness(companyId: number, prefixType: string, prefix: string): Promise<boolean> {
    try {
      // This is an optional feature - for now, we'll allow duplicate prefixes across companies
      // In the future, this could be enhanced to check for conflicts
      return true;
    } catch (error) {
      console.error('Error validating prefix uniqueness:', error);
      return false;
    }
  }
  
  /**
   * Get prefix settings statistics
   */
  async getPrefixSettingsStats(): Promise<{
    totalCompanies: number;
    companiesWithSettings: number;
    companiesWithoutSettings: number;
    coveragePercentage: number;
  }> {
    try {
      const allCompanies = await db.query.companies.findMany();
      const companiesWithSettings = await db.query.companyPrefixSettings.findMany();
      
      const totalCompanies = allCompanies.length;
      const companiesWithSettingsCount = companiesWithSettings.length;
      const companiesWithoutSettingsCount = totalCompanies - companiesWithSettingsCount;
      const coveragePercentage = totalCompanies > 0 ? (companiesWithSettingsCount / totalCompanies) * 100 : 0;
      
      return {
        totalCompanies,
        companiesWithSettings: companiesWithSettingsCount,
        companiesWithoutSettings: companiesWithoutSettingsCount,
        coveragePercentage: Math.round(coveragePercentage * 100) / 100
      };
    } catch (error) {
      console.error('Error getting prefix settings stats:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const companyPrefixSettingsService = new CompanyPrefixSettingsService();
