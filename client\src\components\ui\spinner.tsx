import React from 'react';
import { cn } from '@/lib/utils';

type SpinnerProps = {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
};

export const Spinner = ({ className, size = 'md' }: SpinnerProps) => {
  const sizeClass = {
    sm: 'h-4 w-4 border-2',
    md: 'h-8 w-8 border-3',
    lg: 'h-12 w-12 border-4',
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-t-transparent',
        sizeClass[size],
        'border-primary',
        className
      )}
      aria-label="Loading"
    />
  );
};

export default Spinner;