import { useState } from "react";
import { useAuth } from "@/lib/auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Helmet } from "react-helmet";
import {
  Plus,
  FileEdit,
  Trash2,
  MoreHorizontal,
  User,
  Search,
  BadgeCheck,
  Phone,
  Mail,
  MapPin,
  Edit,
  MoreVertical,
  Pie<PERSON>hart
} from "lucide-react";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useIsMobile } from "@/hooks/use-mobile";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { format } from "date-fns";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

// Customer schema
const customerSchema = z.object({
  company_id: z.number(),
  full_name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  phone: z.string().refine(
    (value) => {
      // Accept only format with country code (+91) followed by exactly 10 digits
      return /^\+91\d{10}$/.test(value);
    },
    { message: "Phone number must be exactly 10 digits with +91 country code" }
  ),
  email: z.string()
    .email({ message: "Please enter a valid email address (e.g., <EMAIL>)" })
    .optional()
    .or(z.literal("")),
  address: z.string().optional().or(z.literal("")),
  credit_score: z.coerce.number().min(0).max(1000).optional(),
  active: z.boolean().default(true),
  kyc_verified: z.boolean().default(false),
  notes: z.string().optional().or(z.literal(""))
});

type Customer = z.infer<typeof customerSchema>;

export default function CustomersPage() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const user = getCurrentUser();
  const isMobile = useIsMobile();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentCustomer, setCurrentCustomer] = useState<Customer | null>(null);
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Create form
  const createForm = useForm<Customer>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      company_id: user?.company_id, // Use the actual company ID
      full_name: "",
      phone: "",
      email: "",
      address: "",
      credit_score: undefined,
      active: true,
      kyc_verified: false,
      notes: ""
    }
  });

  // Edit form
  const editForm = useForm<Customer & { id?: number }>({
    resolver: zodResolver(customerSchema.extend({ id: z.number().optional() })),
    defaultValues: {
      company_id: user?.company_id,
      full_name: "",
      phone: "",
      email: "",
      address: "",
      credit_score: undefined,
      active: true,
      kyc_verified: false,
      notes: ""
    }
  });

  // Fetch customers
  const { data: customers = [], isLoading } = useQuery({
    queryKey: ['/api/companies', user?.company_id, 'customers'],
    queryFn: async () => {
      if (!user?.company_id) return [];
      const response = await apiRequest("GET", `/api/companies/${user.company_id}/customers`);
      return await response.json();
    },
    enabled: !!user?.company_id
  });

  // Create customer mutation
  const createCustomerMutation = useMutation({
    mutationFn: async (data: Customer) => {
      console.log("Creating customer with data:", data);

      // Make the API request directly
      const response = await fetch("/api/customers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(data)
      });

      // If the response is not ok, parse the error
      if (!response.ok) {
        const errorData = await response.json();
        console.log("Error response:", errorData);
        throw errorData; // This will be caught in onError
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'customers'] });
      setIsCreateDialogOpen(false);
      createForm.reset();
      toast({
        title: "Success",
        description: "Customer created successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error creating customer:", error);
      console.log("Error details:", JSON.stringify(error, null, 2));

      // Normalize error object for all possible shapes
      let title = 'Error';
      let description = 'Failed to create customer. Please try again.';
      if (typeof error === 'string') {
        title = error;
      } else if (error && typeof error === 'object') {
        if (error.message) title = error.message;
        if (error.error) description = error.error;
        else if (error.message) description = error.message;
      }

      toast({
        title,
        description,
        variant: 'destructive',
        duration: 5000,
      });

      // Set form error for the phone field if it's a phone error
      if (error && typeof error === 'object' && error.field === 'phone') {
        createForm.setError('phone', {
          type: 'manual',
          message: error.message || error.error || 'Phone number already in usesdfghj'
        });
      }
    }
  });

  // Update customer mutation
  const updateCustomerMutation = useMutation({
    mutationFn: async (data: Customer & { id: number }) => {
      const { id, ...customerData } = data;
      console.log("Updating customer with data:", { id, ...customerData });

      // Make the API request directly
      const response = await fetch(`/api/customers/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(customerData)
      });

      // If the response is not ok, parse the error
      if (!response.ok) {
        const errorData = await response.json();
        console.log("Error response:", errorData);
        throw errorData; // This will be caught in onError
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'customers'] });
      setIsEditDialogOpen(false);
      setCurrentCustomer(null);
      toast({
        title: "Success",
        description: "Customer updated successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error updating customer:", error);
      console.log("Error details:", JSON.stringify(error, null, 2));

      // Check if it's a phone number error
      if (error && error.field === 'phone') {
        // Set form error for the phone field
        editForm.setError('phone', {
          type: 'manual',
          message: error.error || 'Phone number is already in use'
        });

        // Show the phone number error toast
        toast({
          title: error.message || "Phone Number Already In Use",
          description: error.error || "This phone number is already registered with another customer. Please use a different phone number.",
          variant: "destructive",
          duration: 5000,
        });
      } else {
        // For other errors
        toast({
          title: "Error",
          description: error.message || "Failed to update customer. Please try again.",
          variant: "destructive",
        });
      }
    }
  });

  // Delete customer mutation
  const deleteCustomerMutation = useMutation({
    mutationFn: async (id: number) => {
      try {
        const response = await apiRequest("DELETE", `/api/customers/${id}?companyId=${user?.company_id}`);
        const data = await response.json();

        // If there's an error message in the response
        if (!response.ok) {
          // Enhanced error handling with more details
          const errorDetails = {
            message: data.message || "Failed to delete customer",
            status: response.status,
            hasLoans: Boolean(data.loansCount),
            loansCount: data.loansCount || 0,
            originalInput: { id }
          };

          // Create a detailed error object
          const customError = new Error(errorDetails.message);
          (customError as any).details = errorDetails;
          throw customError;
        }

        return data;
      } catch (error: any) {
        // If it's already a properly structured error from our API handler above, just rethrow
        if (error instanceof Error && (error as any).details) {
          throw error;
        }

        // Handle network errors or unexpected errors
        const newError = new Error(
          error.message || "Failed to connect to the server. Please check your connection."
        );
        (newError as any).details = {
          message: error.message || "Network error",
          originalInput: { id }
        };
        throw newError;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'customers'] });
      setIsDeleteDialogOpen(false);
      setCurrentCustomer(null);
      toast({
        title: "Success",
        description: "Customer deleted successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error deleting customer:", error);

      const errorDetails = (error as any).details || {};

      // Handle specific error cases
      if (errorDetails.hasLoans) {
        toast({
          title: "Cannot Delete Customer",
          description: `This customer has ${errorDetails.loansCount} associated loans. Please delete all loans first before deleting this customer.`,
          variant: "destructive",
          duration: 5000,
        });
      } else {
        toast({
          title: "Error",
          description: errorDetails.message || "Failed to delete customer. Please try again.",
          variant: "destructive",
        });
      }
    }
  });

  // Handle create customer
  const handleCreateCustomer = async (data: Customer) => {
    try {
      // Make the API request directly
      const response = await fetch("/api/customers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(data)
      });

      // If the response is not ok, handle the error
      if (!response.ok) {
        try {
          // Get the exact error response from the server
          const errorData = await response.json();
          console.log("Server error response:", errorData);

          // Display the exact error message from the API
          toast({
            title: errorData.message || "Error",
            description: errorData.error || "Failed to create customer",
            variant: "destructive",
            duration: 5000,
          });

          // If there's a field error, set it on the form
          if (errorData.field) {
            createForm.setError(errorData.field, {
              type: 'manual',
              message: errorData.error || `Invalid ${errorData.field}`
            });
          }
        } catch (e) {
          console.error("Failed to parse error response:", e);
          toast({
            title: "Error",
            description: "Failed to create customer. Please try again.",
            variant: "destructive",
          });
        }
        return;
      }

      // Success case - parse the response but we don't need to use it
      await response.json();

      // Update the customer list
      queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'customers'] });

      // Close the dialog and reset the form
      setIsCreateDialogOpen(false);
      createForm.reset();

      // Show success toast
      toast({
        title: "Success",
        description: "Customer created successfully",
      });
    } catch (error) {
      console.error("Error creating customer:", error);

      // Show generic error toast
      toast({
        title: "Error",
        description: "Failed to create customer. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle edit customer
  const handleEditCustomer = async (data: any) => {
    if (data.id) {
      try {
        const { id, ...customerData } = data;

        // Make the API request directly
        const response = await fetch(`/api/customers/${id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem('auth_token')}`
          },
          body: JSON.stringify(customerData)
        });

        // If the response is not ok, handle the error
        if (!response.ok) {
          try {
            // Get the exact error response from the server
            const errorData = await response.json();
            console.log("Server error response:", errorData);

            // Display the exact error message from the API
            toast({
              title: errorData.message || "Error",
              description: errorData.error || "Failed to update customer",
              variant: "destructive",
              duration: 5000,
            });

            // If there's a field error, set it on the form
            if (errorData.field) {
              editForm.setError(errorData.field, {
                type: 'manual',
                message: errorData.error || `Invalid ${errorData.field}`
              });
            }
          } catch (e) {
            console.error("Failed to parse error response:", e);
            toast({
              title: "Error",
              description: "Failed to update customer. Please try again.",
              variant: "destructive",
            });
          }
          return;
        }

        // Success case - parse the response but we don't need to use it
        await response.json();

        // Update the customer list
        queryClient.invalidateQueries({ queryKey: ['/api/companies', user?.company_id, 'customers'] });

        // Close the dialog and reset the form
        setIsEditDialogOpen(false);
        setCurrentCustomer(null);

        // Show success toast
        toast({
          title: "Success",
          description: "Customer updated successfully",
        });
      } catch (error) {
        console.error("Error updating customer:", error);

        // Show generic error toast
        toast({
          title: "Error",
          description: "Failed to update customer. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  // Handle delete customer
  const handleDeleteCustomer = () => {
    if (currentCustomer && 'id' in currentCustomer) {
      deleteCustomerMutation.mutate(currentCustomer.id as number);
    }
  };

  // Open edit dialog
  const openEditDialog = (customer: any) => {
    setCurrentCustomer(customer);
    editForm.reset({
      id: customer.id,
      company_id: customer.company_id,
      full_name: customer.full_name,
      phone: customer.phone,
      email: customer.email || "",
      address: customer.address || "",
      credit_score: customer.credit_score || undefined,
      active: customer.active !== false,
      kyc_verified: customer.kyc_verified === true,
      notes: customer.notes || ""
    });
    setIsEditDialogOpen(true);
  };

  // Open delete dialog
  const openDeleteDialog = (customer: any) => {
    setCurrentCustomer(customer);
    setIsDeleteDialogOpen(true);
  };

  // Filter and search customers
  const filteredCustomers = customers
    .filter((customer: any) => {
      if (activeTab === "all") return true;
      if (activeTab === "active") return customer.active !== false;
      if (activeTab === "inactive") return customer.active === false;
      if (activeTab === "verified") return customer.kyc_verified === true;
      return true;
    })
    .filter((customer: any) => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        (customer.full_name && customer.full_name.toLowerCase().includes(query)) ||
        (customer.phone && customer.phone.toLowerCase().includes(query)) ||
        (customer.email && customer.email.toLowerCase().includes(query)) ||
        (customer.address && customer.address.toLowerCase().includes(query))
      );
    });

  return (
    <>
      <Helmet>
        <title>Customers Management | TrackFina</title>
      </Helmet>

      <div className="container mx-auto py-6 px-4 md:px-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customers Management</h1>
            <p className="text-muted-foreground mt-1">
              Manage your customers and their information
            </p>
          </div>
          <Button
            className="mt-4 md:mt-0"
            onClick={() => {
              createForm.reset({
                company_id: user?.company_id,
                full_name: "",
                phone: "",
                email: "",
                address: "",
                credit_score: undefined,
                active: true,
                kyc_verified: false,
                notes: ""
              });
              setIsCreateDialogOpen(true);
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Customer
          </Button>
        </div>

        <div className="mb-6 flex flex-col md:flex-row gap-4 items-center justify-between">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
            <TabsList>
              <TabsTrigger value="all">All Customers</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
              <TabsTrigger value="verified">KYC Verified</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="relative w-full md:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search customers..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex justify-center items-center p-8">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
              </div>
            ) : filteredCustomers.length === 0 ? (
              <div className="text-center p-8">
                <User className="mx-auto h-12 w-12 text-muted-foreground/50" />
                <h3 className="mt-4 text-lg font-semibold">No customers found</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  {searchQuery
                    ? "No customers match your search criteria"
                    : "You don't have any customers yet. Click 'Add Customer' to create one."}
                </p>
              </div>
            ) : (
              <>
                {/* Desktop View - Table */}
                <div className="hidden md:block">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Customer Name</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Address</TableHead>
                        <TableHead>Credit Score</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCustomers.map((customer: any) => (
                        <TableRow key={customer.id}>
                          <TableCell>
                            <div className="font-medium">{customer.full_name}</div>
                            {customer.kyc_verified && (
                              <span className="inline-flex items-center text-xs text-blue-700">
                                <BadgeCheck className="h-3.5 w-3.5 mr-1" />
                                KYC Verified
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{customer.phone}</div>
                            {customer.email && <div className="text-sm text-muted-foreground">{customer.email}</div>}
                          </TableCell>
                          <TableCell className="max-w-[200px] truncate">
                            {customer.address || "-"}
                          </TableCell>
                          <TableCell>
                            {customer.credit_score ? (
                              <div className="flex items-center gap-2">
                                <div
                                  className={`h-2.5 w-12 rounded-full ${
                                    customer.credit_score >= 700 ? "bg-green-500" :
                                    customer.credit_score >= 500 ? "bg-yellow-500" :
                                    "bg-red-500"
                                  }`}
                                >
                                  <div
                                    className="h-full rounded-full bg-primary"
                                    style={{ width: `${(customer.credit_score / 1000) * 100}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm">{customer.credit_score}</span>
                              </div>
                            ) : (
                              <span className="text-sm text-muted-foreground">Not available</span>
                            )}
                          </TableCell>
                          <TableCell>
                            {customer.active !== false ? (
                              <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                Active
                              </span>
                            ) : (
                              <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                Inactive
                              </span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => openEditDialog(customer)}>
                                  <FileEdit className="mr-2 h-4 w-4" />
                                  Edit Customer
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive"
                                  onClick={() => openDeleteDialog(customer)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Customer
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Mobile View - Cards */}
                <div className="md:hidden grid grid-cols-1 gap-4 p-4">
                  {filteredCustomers.map((customer: any) => (
                    <Card key={customer.id} className="overflow-hidden">
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between items-start">
                          <div className="space-y-1">
                            <CardTitle className="text-lg flex items-center">
                              {customer.full_name}
                              {customer.kyc_verified && (
                                <BadgeCheck className="h-4 w-4 ml-1 text-blue-600" />
                              )}
                            </CardTitle>
                            <div className="flex flex-wrap gap-2">
                              {customer.active !== false ? (
                                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                  Active
                                </span>
                              ) : (
                                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                  Inactive
                                </span>
                              )}
                            </div>
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => openEditDialog(customer)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => openDeleteDialog(customer)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="grid grid-cols-1 gap-3 mt-2">
                          <div className="flex items-start">
                            <Phone className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Phone</p>
                              <p className="text-sm text-muted-foreground">{customer.phone}</p>
                            </div>
                          </div>

                          {customer.email && (
                            <div className="flex items-start">
                              <Mail className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                              <div className="space-y-1">
                                <p className="text-sm font-medium">Email</p>
                                <p className="text-sm text-muted-foreground">{customer.email}</p>
                              </div>
                            </div>
                          )}

                          {customer.address && (
                            <div className="flex items-start">
                              <MapPin className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                              <div className="space-y-1">
                                <p className="text-sm font-medium">Address</p>
                                <p className="text-sm text-muted-foreground">{customer.address}</p>
                              </div>
                            </div>
                          )}

                          {customer.credit_score && (
                            <div className="flex items-start">
                              <PieChart className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                              <div className="space-y-1">
                                <p className="text-sm font-medium">Credit Score</p>
                                <div className="flex items-center gap-2">
                                  <div
                                    className={`h-2.5 w-16 rounded-full ${
                                      customer.credit_score >= 700 ? "bg-green-500" :
                                      customer.credit_score >= 500 ? "bg-yellow-500" :
                                      "bg-red-500"
                                    }`}
                                  >
                                    <div
                                      className="h-full rounded-full bg-primary"
                                      style={{ width: `${(customer.credit_score / 1000) * 100}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-sm">{customer.credit_score}</span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Create Customer Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Add New Customer</DialogTitle>
            <DialogDescription>
              Add a new customer to your organization. Fill in the details below.
            </DialogDescription>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit((data) => createCustomerMutation.mutate(data))} className="space-y-4">
              <FormField
                control={createForm.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="E.g., John Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Phone Number*
                        <span className="ml-1 text-xs text-muted-foreground">(must be unique)</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex">
                          <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                            +91
                          </span>
                          <Input
                            className="rounded-l-none"
                            placeholder="Enter 10 digit mobile number"
                            maxLength={10}
                            {...field}
                            value={field.value?.replace('+91', '')}
                            onChange={(e) => {
                              // Allow only digits
                              const value = e.target.value.replace(/[^0-9]/g, '');

                              // Ensure it's at most 10 digits
                              if (value.length <= 10) {
                                // Always store with +91 prefix
                                field.onChange(value ? `+91${value}` : '');
                              }
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Enter exactly 10 digits. Country code (+91) will be added automatically.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={createForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="E.g., <EMAIL>"
                          type="email"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter a valid email address (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={createForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea placeholder="E.g., 123 Main St, Mumbai" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="credit_score"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Credit Score</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="E.g., 750"
                          min={0}
                          max={1000}
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value) : undefined;
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Score between 0-1000
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex flex-col gap-4">
                  <FormField
                    control={createForm.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Active Status</FormLabel>
                          <FormDescription>
                            Is this customer active?
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={createForm.control}
                    name="kyc_verified"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>KYC Verified</FormLabel>
                          <FormDescription>
                            Is customer's KYC verified?
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={createForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this customer"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={createCustomerMutation.isPending}>
                  {createCustomerMutation.isPending && (
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                  )}
                  Create Customer
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Customer Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Edit Customer</DialogTitle>
            <DialogDescription>
              Update the details of your customer.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditCustomer)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="E.g., John Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        Phone Number*
                        <span className="ml-1 text-xs text-muted-foreground">(must be unique)</span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex">
                          <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground">
                            +91
                          </span>
                          <Input
                            className="rounded-l-none"
                            placeholder="Enter 10 digit mobile number"
                            maxLength={10}
                            {...field}
                            value={field.value?.replace('+91', '')}
                            onChange={(e) => {
                              // Allow only digits
                              const value = e.target.value.replace(/[^0-9]/g, '');

                              // Ensure it's at most 10 digits
                              if (value.length <= 10) {
                                // Always store with +91 prefix
                                field.onChange(value ? `+91${value}` : '');
                              }
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        Enter exactly 10 digits. Country code (+91) will be added automatically.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="E.g., <EMAIL>"
                          type="email"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter a valid email address (optional)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea placeholder="E.g., 123 Main St, Mumbai" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="credit_score"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Credit Score</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="E.g., 750"
                          min={0}
                          max={1000}
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value ? parseInt(e.target.value) : undefined;
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Score between 0-1000
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex flex-col gap-4">
                  <FormField
                    control={editForm.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Active Status</FormLabel>
                          <FormDescription>
                            Is this customer active?
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={editForm.control}
                    name="kyc_verified"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>KYC Verified</FormLabel>
                          <FormDescription>
                            Is customer's KYC verified?
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <FormField
                control={editForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this customer"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={updateCustomerMutation.isPending}>
                  {updateCustomerMutation.isPending && (
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                  )}
                  Update Customer
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this customer? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-4 mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCustomer}
              disabled={deleteCustomerMutation.isPending}
            >
              {deleteCustomerMutation.isPending && (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
              )}
              Delete Customer
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}