/**
 * Default Prefix Settings Configuration
 * 
 * This module provides default prefix settings templates for new companies
 * and fallback configurations for existing companies without prefix settings.
 */

export interface DefaultPrefixTemplate {
  loan_prefix: string;
  loan_start_number: number;
  collection_prefix: string;
  collection_start_number: number;
  customer_prefix: string;
  customer_start_number: number;
  partner_prefix: string;
  partner_start_number: number;
  agent_prefix: string;
  agent_start_number: number;
}

/**
 * Standard default prefix settings for new companies
 */
export const DEFAULT_PREFIX_SETTINGS: DefaultPrefixTemplate = {
  loan_prefix: 'LOAN',
  loan_start_number: 1,
  collection_prefix: 'COL',
  collection_start_number: 1,
  customer_prefix: 'CUST',
  customer_start_number: 1,
  partner_prefix: 'PART',
  partner_start_number: 1,
  agent_prefix: 'AGT',
  agent_start_number: 1
};

/**
 * Alternative prefix templates for different business types
 */
export const PREFIX_TEMPLATES: Record<string, DefaultPrefixTemplate> = {
  // Standard template (same as default)
  standard: DEFAULT_PREFIX_SETTINGS,
  
  // Microfinance institution template
  microfinance: {
    loan_prefix: 'MFL',
    loan_start_number: 1000,
    collection_prefix: 'MFC',
    collection_start_number: 1000,
    customer_prefix: 'MFM',
    customer_start_number: 1000,
    partner_prefix: 'MFP',
    partner_start_number: 100,
    agent_prefix: 'MFA',
    agent_start_number: 100
  },
  
  // Bank template
  bank: {
    loan_prefix: 'BNK',
    loan_start_number: 10000,
    collection_prefix: 'BNC',
    collection_start_number: 10000,
    customer_prefix: 'BNM',
    customer_start_number: 100000,
    partner_prefix: 'BNP',
    partner_start_number: 1000,
    agent_prefix: 'BNA',
    agent_start_number: 1000
  },
  
  // Credit union template
  credit_union: {
    loan_prefix: 'CUL',
    loan_start_number: 1000,
    collection_prefix: 'CUC',
    collection_start_number: 1000,
    customer_prefix: 'CUM',
    customer_start_number: 10000,
    partner_prefix: 'CUP',
    partner_start_number: 100,
    agent_prefix: 'CUA',
    agent_start_number: 100
  },
  
  // NBFC (Non-Banking Financial Company) template
  nbfc: {
    loan_prefix: 'NBF',
    loan_start_number: 5000,
    collection_prefix: 'NBC',
    collection_start_number: 5000,
    customer_prefix: 'NBM',
    customer_start_number: 50000,
    partner_prefix: 'NBP',
    partner_start_number: 500,
    agent_prefix: 'NBA',
    agent_start_number: 500
  }
};

/**
 * Generate company-specific prefix settings based on company name
 * This is used as a fallback when no template is specified
 */
export function generateCompanySpecificPrefixes(companyName: string): DefaultPrefixTemplate {
  // Clean and process company name
  const cleanName = companyName.trim().toUpperCase();
  const words = cleanName.split(/\s+/).filter(word => word.length > 0);
  
  let prefix = '';
  
  if (words.length === 0) {
    prefix = 'COMP';
  } else if (words.length === 1) {
    // Single word: use first 3-4 characters
    const word = words[0];
    prefix = word.length >= 4 ? word.substring(0, 4) : word;
  } else if (words.length === 2) {
    // Two words: use first 2 chars of each
    prefix = words[0].substring(0, 2) + words[1].substring(0, 2);
  } else {
    // Multiple words: use first char of first 3-4 words
    prefix = words.slice(0, Math.min(4, words.length))
      .map(word => word.charAt(0))
      .join('');
  }
  
  // Ensure prefix is at least 2 characters and at most 4
  if (prefix.length < 2) {
    prefix = prefix.padEnd(2, 'X');
  } else if (prefix.length > 4) {
    prefix = prefix.substring(0, 4);
  }
  
  return {
    loan_prefix: `${prefix}L`,
    loan_start_number: 1,
    collection_prefix: `${prefix}C`,
    collection_start_number: 1,
    customer_prefix: `${prefix}M`,
    customer_start_number: 1,
    partner_prefix: `${prefix}P`,
    partner_start_number: 1,
    agent_prefix: `${prefix}A`,
    agent_start_number: 1
  };
}

/**
 * Get prefix template by name
 */
export function getPrefixTemplate(templateName: string): DefaultPrefixTemplate {
  return PREFIX_TEMPLATES[templateName] || DEFAULT_PREFIX_SETTINGS;
}

/**
 * Get all available template names
 */
export function getAvailableTemplates(): string[] {
  return Object.keys(PREFIX_TEMPLATES);
}

/**
 * Validate prefix settings
 */
export function validatePrefixSettings(settings: Partial<DefaultPrefixTemplate>): string[] {
  const errors: string[] = [];
  
  // Check required fields
  const requiredFields: (keyof DefaultPrefixTemplate)[] = [
    'loan_prefix', 'collection_prefix', 'customer_prefix', 
    'partner_prefix', 'agent_prefix'
  ];
  
  for (const field of requiredFields) {
    if (!settings[field] || typeof settings[field] !== 'string') {
      errors.push(`${field} is required and must be a string`);
    } else {
      const prefix = settings[field] as string;
      
      // Validate prefix format
      if (prefix.length < 2 || prefix.length > 6) {
        errors.push(`${field} must be between 2 and 6 characters`);
      }
      
      if (!/^[A-Z0-9]+$/.test(prefix)) {
        errors.push(`${field} must contain only uppercase letters and numbers`);
      }
    }
  }
  
  // Check start numbers
  const numberFields: (keyof DefaultPrefixTemplate)[] = [
    'loan_start_number', 'collection_start_number', 'customer_start_number',
    'partner_start_number', 'agent_start_number'
  ];
  
  for (const field of numberFields) {
    if (settings[field] !== undefined) {
      const num = settings[field] as number;
      if (!Number.isInteger(num) || num < 1 || num > 999999) {
        errors.push(`${field} must be an integer between 1 and 999999`);
      }
    }
  }
  
  return errors;
}

/**
 * Create prefix settings for a company
 */
export function createPrefixSettingsForCompany(
  companyId: number,
  companyName: string,
  templateName?: string
): DefaultPrefixTemplate & { company_id: number } {
  let template: DefaultPrefixTemplate;
  
  if (templateName && PREFIX_TEMPLATES[templateName]) {
    template = PREFIX_TEMPLATES[templateName];
  } else if (companyName) {
    template = generateCompanySpecificPrefixes(companyName);
  } else {
    template = DEFAULT_PREFIX_SETTINGS;
  }
  
  return {
    company_id: companyId,
    ...template
  };
}
