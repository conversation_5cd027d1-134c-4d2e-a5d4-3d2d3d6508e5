#!/usr/bin/env npx tsx

/**
 * Migration Script: Company Prefix Settings
 * 
 * This script creates default prefix settings for all existing companies
 * that don't have prefix settings configured.
 * 
 * Usage:
 *   npx tsx scripts/migrate-company-prefix-settings.ts
 *   npx tsx scripts/migrate-company-prefix-settings.ts --dry-run
 *   npx tsx scripts/migrate-company-prefix-settings.ts --force
 */

import { db } from '../server/db';
import { companyPrefixSettingsService } from '../server/services/companyPrefixSettingsService';
import { companies, companyPrefixSettings } from '../shared/schema';

interface MigrationOptions {
  dryRun: boolean;
  force: boolean;
  verbose: boolean;
}

class PrefixSettingsMigration {
  private options: MigrationOptions;

  constructor(options: MigrationOptions) {
    this.options = options;
  }

  async run(): Promise<void> {
    console.log('🚀 COMPANY PREFIX SETTINGS MIGRATION');
    console.log('====================================\n');

    if (this.options.dryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made\n');
    }

    try {
      // Step 1: Get current statistics
      await this.showCurrentStats();

      // Step 2: Identify companies without prefix settings
      const companiesWithoutSettings = await this.getCompaniesWithoutSettings();

      if (companiesWithoutSettings.length === 0) {
        console.log('✅ All companies already have prefix settings configured!');
        return;
      }

      // Step 3: Show migration plan
      await this.showMigrationPlan(companiesWithoutSettings);

      // Step 4: Execute migration (unless dry run)
      if (!this.options.dryRun) {
        await this.executeMigration(companiesWithoutSettings);
      }

      // Step 5: Show final statistics
      if (!this.options.dryRun) {
        console.log('\n📊 FINAL STATISTICS');
        console.log('===================');
        await this.showCurrentStats();
      }

    } catch (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
  }

  private async showCurrentStats(): Promise<void> {
    try {
      const stats = await companyPrefixSettingsService.getPrefixSettingsStats();
      
      console.log('📊 CURRENT STATISTICS');
      console.log('---------------------');
      console.log(`Total Companies: ${stats.totalCompanies}`);
      console.log(`Companies with Prefix Settings: ${stats.companiesWithSettings}`);
      console.log(`Companies without Prefix Settings: ${stats.companiesWithoutSettings}`);
      console.log(`Coverage: ${stats.coveragePercentage}%\n`);
    } catch (error) {
      console.error('Error getting statistics:', error);
    }
  }

  private async getCompaniesWithoutSettings(): Promise<any[]> {
    return await companyPrefixSettingsService.getCompaniesWithoutPrefixSettings();
  }

  private async showMigrationPlan(companiesWithoutSettings: any[]): Promise<void> {
    console.log('📋 MIGRATION PLAN');
    console.log('-----------------');
    console.log(`Companies to process: ${companiesWithoutSettings.length}\n`);

    if (this.options.verbose) {
      console.log('Companies without prefix settings:');
      companiesWithoutSettings.forEach((company, index) => {
        console.log(`${index + 1}. ID: ${company.id}, Name: "${company.name}"`);
      });
      console.log('');
    }

    if (!this.options.dryRun && !this.options.force) {
      // In a real implementation, you might want to add user confirmation here
      console.log('⚠️  This will create prefix settings for all companies listed above.');
      console.log('   Use --dry-run to preview changes without making them.');
      console.log('   Use --force to skip this confirmation.\n');
    }
  }

  private async executeMigration(companiesWithoutSettings: any[]): Promise<void> {
    console.log('🔧 EXECUTING MIGRATION');
    console.log('======================\n');

    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (let i = 0; i < companiesWithoutSettings.length; i++) {
      const company = companiesWithoutSettings[i];
      const progress = `[${i + 1}/${companiesWithoutSettings.length}]`;
      
      try {
        console.log(`${progress} Processing company ${company.id} (${company.name})...`);
        
        const prefixSettings = await companyPrefixSettingsService.createDefaultPrefixSettings(
          company.id, 
          company.name
        );
        
        results.success++;
        console.log(`${progress} ✅ Created prefix settings for company ${company.id}`);
        
        if (this.options.verbose) {
          console.log(`     - Loan prefix: ${prefixSettings.loan_prefix}`);
          console.log(`     - Customer prefix: ${prefixSettings.customer_prefix}`);
          console.log(`     - Collection prefix: ${prefixSettings.collection_prefix}`);
        }
        
      } catch (error) {
        results.failed++;
        const errorMessage = `Failed to create prefix settings for company ${company.id} (${company.name}): ${error}`;
        results.errors.push(errorMessage);
        console.log(`${progress} ❌ ${errorMessage}`);
      }
    }

    // Show migration results
    console.log('\n📈 MIGRATION RESULTS');
    console.log('====================');
    console.log(`✅ Successful: ${results.success}`);
    console.log(`❌ Failed: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (results.success > 0) {
      console.log(`\n🎉 Successfully created prefix settings for ${results.success} companies!`);
    }
  }

  private async createBackup(): Promise<void> {
    // In a production environment, you might want to create a backup
    // of the current state before running the migration
    console.log('💾 Creating backup... (skipped in this implementation)');
  }
}

// Parse command line arguments
function parseArgs(): MigrationOptions {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    force: args.includes('--force'),
    verbose: args.includes('--verbose') || args.includes('-v')
  };
}

// Main execution
async function main() {
  const options = parseArgs();
  
  if (options.dryRun && options.force) {
    console.error('❌ Cannot use --dry-run and --force together');
    process.exit(1);
  }

  const migration = new PrefixSettingsMigration(options);
  await migration.run();
}

// Run the migration
main().catch((error) => {
  console.error('❌ Migration script failed:', error);
  process.exit(1);
});

export { PrefixSettingsMigration };
