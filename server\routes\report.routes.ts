import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { requirePermissionWithContext } from '../middleware/enhancedPermission';

export function registerReportRoutes(app: Express): void {
  console.log('🔍 [REPORT_ROUTES] Registering report routes...');

  // Daily Collections Report
  app.get('/api/companies/:companyId/reports/daily-collections', 
    authMiddleware, 
    requireCompanyAccess, 
    requirePermissionWithContext('reports', 'basic', { logAccess: true }),
    async (req: AuthRequest, res: Response) => {
      try {
        const companyId = parseInt(req.params.companyId);
        const date = req.query.date as string || new Date().toISOString().split('T')[0];
        
        console.log(`Generating daily collections report for company ${companyId}, date: ${date}`);
        
        // Get collections for the specified date
        const collections = await storage.getCollectionsByCompany(companyId);
        
        // Filter collections by date
        const targetDate = new Date(date);
        const dailyCollections = collections.filter(collection => {
          const collectionDate = new Date(collection.created_at);
          return collectionDate.toDateString() === targetDate.toDateString();
        });
        
        // Calculate summary metrics
        const totalAmount = dailyCollections.reduce((sum, collection) => sum + parseFloat(collection.amount), 0);
        const completedCollections = dailyCollections.filter(c => c.status === 'completed');
        const pendingCollections = dailyCollections.filter(c => c.status === 'pending');
        
        const report = {
          date,
          companyId,
          summary: {
            totalCollections: dailyCollections.length,
            completedCollections: completedCollections.length,
            pendingCollections: pendingCollections.length,
            totalAmount: totalAmount.toFixed(2),
            completedAmount: completedCollections.reduce((sum, c) => sum + parseFloat(c.amount), 0).toFixed(2),
            pendingAmount: pendingCollections.reduce((sum, c) => sum + parseFloat(c.amount), 0).toFixed(2)
          },
          collections: dailyCollections
        };
        
        return res.json(report);
      } catch (error) {
        console.error('Error generating daily collections report:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Customer Report
  app.get('/api/companies/:companyId/reports/customers', 
    authMiddleware, 
    requireCompanyAccess, 
    requirePermissionWithContext('reports', 'basic', { logAccess: true }),
    async (req: AuthRequest, res: Response) => {
      try {
        const companyId = parseInt(req.params.companyId);
        const startDate = req.query.start_date as string;
        const endDate = req.query.end_date as string;
        
        console.log(`Generating customer report for company ${companyId}`);
        
        // Get customers and their related data
        const customers = await storage.getCustomersByCompany(companyId);
        const loans = await storage.getLoansByCompany(companyId);
        const collections = await storage.getCollectionsByCompany(companyId);
        
        // Build customer report with loan and collection data
        const customerReport = customers.map(customer => {
          const customerLoans = loans.filter(loan => loan.customer_id === customer.id);
          const customerCollections = collections.filter(collection => collection.customer_id === customer.id);
          
          const totalLoanAmount = customerLoans.reduce((sum, loan) => sum + parseFloat(loan.amount), 0);
          const totalCollected = customerCollections
            .filter(c => c.status === 'completed')
            .reduce((sum, collection) => sum + parseFloat(collection.amount), 0);
          
          return {
            ...customer,
            loanCount: customerLoans.length,
            totalLoanAmount: totalLoanAmount.toFixed(2),
            totalCollected: totalCollected.toFixed(2),
            outstandingAmount: (totalLoanAmount - totalCollected).toFixed(2),
            lastLoanDate: customerLoans.length > 0 ? 
              Math.max(...customerLoans.map(l => new Date(l.created_at).getTime())) : null,
            lastPaymentDate: customerCollections.length > 0 ? 
              Math.max(...customerCollections.map(c => new Date(c.created_at).getTime())) : null
          };
        });
        
        return res.json({
          companyId,
          reportDate: new Date().toISOString(),
          totalCustomers: customers.length,
          customers: customerReport
        });
      } catch (error) {
        console.error('Error generating customer report:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Agent Performance Report
  app.get('/api/companies/:companyId/reports/agents', 
    authMiddleware, 
    requireCompanyAccess, 
    requirePermissionWithContext('reports', 'basic', { logAccess: true }),
    async (req: AuthRequest, res: Response) => {
      try {
        const companyId = parseInt(req.params.companyId);
        const days = parseInt(req.query.days as string) || 30;
        
        console.log(`Generating agent performance report for company ${companyId}, last ${days} days`);
        
        // Get agents and collections
        const agents = await storage.getAgentsByCompany(companyId);
        const collections = await storage.getCollectionsByCompany(companyId);
        
        // Filter collections by date range
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        
        const recentCollections = collections.filter(collection => 
          new Date(collection.created_at) >= startDate
        );
        
        // Build agent performance report
        const agentReport = agents.map(agent => {
          const agentCollections = recentCollections.filter(collection => collection.agent_id === agent.id);
          const completedCollections = agentCollections.filter(c => c.status === 'completed');
          
          const totalAmount = agentCollections.reduce((sum, c) => sum + parseFloat(c.amount), 0);
          const completedAmount = completedCollections.reduce((sum, c) => sum + parseFloat(c.amount), 0);
          const successRate = agentCollections.length > 0 ? 
            (completedCollections.length / agentCollections.length * 100) : 0;
          
          return {
            ...agent,
            performance: {
              totalCollections: agentCollections.length,
              completedCollections: completedCollections.length,
              totalAmount: totalAmount.toFixed(2),
              completedAmount: completedAmount.toFixed(2),
              successRate: successRate.toFixed(1),
              averageCollectionAmount: agentCollections.length > 0 ? 
                (totalAmount / agentCollections.length).toFixed(2) : '0.00'
            }
          };
        });
        
        // Sort by performance (completed amount)
        agentReport.sort((a, b) => 
          parseFloat(b.performance.completedAmount) - parseFloat(a.performance.completedAmount)
        );
        
        return res.json({
          companyId,
          reportPeriod: `${days} days`,
          reportDate: new Date().toISOString(),
          totalAgents: agents.length,
          agents: agentReport
        });
      } catch (error) {
        console.error('Error generating agent performance report:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Financial Summary Report
  app.get('/api/companies/:companyId/reports/financial-summary', 
    authMiddleware, 
    requireCompanyAccess, 
    requirePermissionWithContext('reports', 'financial', { logAccess: true }),
    async (req: AuthRequest, res: Response) => {
      try {
        const companyId = parseInt(req.params.companyId);
        const startDate = req.query.start_date as string;
        const endDate = req.query.end_date as string;
        
        console.log(`Generating financial summary report for company ${companyId}`);
        
        // Get financial data
        const loans = await storage.getLoansByCompany(companyId);
        const collections = await storage.getCollectionsByCompany(companyId);
        const expenses = await storage.getExpensesByCompany(companyId);
        
        // Calculate financial metrics
        const totalLoansIssued = loans.reduce((sum, loan) => sum + parseFloat(loan.amount), 0);
        const totalCollected = collections
          .filter(c => c.status === 'completed')
          .reduce((sum, collection) => sum + parseFloat(collection.amount), 0);
        const totalExpenses = expenses.reduce((sum, expense) => sum + parseFloat(expense.amount), 0);
        const outstandingAmount = totalLoansIssued - totalCollected;
        
        // Calculate collection rate
        const collectionRate = totalLoansIssued > 0 ? 
          (totalCollected / totalLoansIssued * 100) : 0;
        
        const report = {
          companyId,
          reportDate: new Date().toISOString(),
          period: { startDate, endDate },
          summary: {
            totalLoansIssued: totalLoansIssued.toFixed(2),
            totalCollected: totalCollected.toFixed(2),
            totalExpenses: totalExpenses.toFixed(2),
            outstandingAmount: outstandingAmount.toFixed(2),
            collectionRate: collectionRate.toFixed(1),
            netIncome: (totalCollected - totalExpenses).toFixed(2),
            activeLoans: loans.filter(l => l.status === 'active').length,
            totalCustomers: new Set(loans.map(l => l.customer_id)).size
          }
        };
        
        return res.json(report);
      } catch (error) {
        console.error('Error generating financial summary report:', error);
        return res.status(500).json({ message: 'Server error' });
      }
    }
  );

  // Test route to verify report routes are working
  app.get('/api/reports/test', (req, res) => {
    console.log('🔍 [REPORT_TEST] Test route called');
    res.json({ 
      message: 'Report routes are working!', 
      timestamp: new Date().toISOString(),
      availableReports: [
        'daily-collections',
        'customers', 
        'agents',
        'financial-summary'
      ]
    });
  });

  console.log('🔍 [REPORT_ROUTES] Report routes registered successfully');
}
