import { db } from '../db.js';
import { companySettings } from '../../shared/schema.js';
import { eq } from 'drizzle-orm';

export async function registerCompanySettingsRoutes(app) {
  // GET /api/companies/:id/settings
  app.get('/api/companies/:id/settings', async (req, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = parseInt(req.params.id);

      // Check if user has access to this company
      if (req.user.company_id !== companyId && req.user.role !== 'saas_admin') {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      // Get company settings
      const settings = await db.query.companySettings.findFirst({
        where: eq(companySettings.company_id, companyId),
      });

      // If settings don't exist, return default values
      if (!settings) {
        return res.json({
          company_id: companyId,
          date_format: 'dd-MM-yyyy',
          currency_symbol: '₹',
        });
      }

      return res.json(settings);
    } catch (error) {
      console.error('Error fetching company settings:', error);
      return res.status(500).json({ message: 'Failed to fetch company settings' });
    }
  });

  // PUT /api/companies/:id/settings
  app.put('/api/companies/:id/settings', async (req, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = parseInt(req.params.id);

      // Check if user has access to this company
      if (req.user.company_id !== companyId && req.user.role !== 'saas_admin') {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      const { date_format, currency_symbol } = req.body;

      // Validate input
      if (!date_format || !currency_symbol) {
        return res.status(400).json({ message: 'Date format and currency symbol are required' });
      }

      // Check if settings already exist
      const existingSettings = await db.query.companySettings.findFirst({
        where: eq(companySettings.company_id, companyId),
      });

      let result;

      if (existingSettings) {
        // Update existing settings
        result = await db
          .update(companySettings)
          .set({
            date_format,
            currency_symbol,
            updated_at: new Date(),
          })
          .where(eq(companySettings.company_id, companyId))
          .returning();
      } else {
        // Create new settings
        result = await db
          .insert(companySettings)
          .values({
            company_id: companyId,
            date_format,
            currency_symbol,
          })
          .returning();
      }

      return res.json(result[0]);
    } catch (error) {
      console.error('Error updating company settings:', error);
      return res.status(500).json({ message: 'Failed to update company settings' });
    }
  });
}
