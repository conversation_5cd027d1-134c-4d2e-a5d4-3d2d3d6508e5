import { IStorage } from './interfaces';
import { UserStorage } from './user.storage';
import { CompanyStorage } from './company.storage';
import { UserCompanyStorage } from './user-company.storage';
import { CustomerStorage } from './customer.storage';
import { LoanStorage } from './loan.storage';
import { CollectionStorage } from './collection.storage';
import { PaymentStorage } from './payment.storage';
import { PartnerStorage } from './partner.storage';
import { AgentStorage } from './agent.storage';
import { CompanyPrefixSettingsStorage } from './company-prefix-settings.storage';
import { financialStorage } from './financial';
import { db } from '../db';
import { eq } from 'drizzle-orm';
import { expenses } from '@shared/schema';
import { Expense, InsertExpense } from '@shared/schema';
// Import other storage implementations

// Create a class that implements the IStorage interface by combining all domain-specific storage implementations
class Storage implements IStorage {
  private userStorage: UserStorage;
  private companyStorage: CompanyStorage;
  private userCompanyStorage: UserCompanyStorage;
  private customerStorage: CustomerStorage;
  private loanStorage: LoanStorage;
  private collectionStorage: CollectionStorage;
  private paymentStorage: PaymentStorage;
  private partnerStorage: PartnerStorage;
  private agentStorage: AgentStorage;
  private companyPrefixSettingsStorage: CompanyPrefixSettingsStorage;
  // Add other storage implementations

  constructor() {
    this.userStorage = new UserStorage();
    this.companyStorage = new CompanyStorage();
    this.userCompanyStorage = new UserCompanyStorage();
    this.customerStorage = new CustomerStorage();
    this.loanStorage = new LoanStorage();
    this.collectionStorage = new CollectionStorage();
    this.paymentStorage = new PaymentStorage();
    this.partnerStorage = new PartnerStorage();
    this.agentStorage = new AgentStorage();
    this.companyPrefixSettingsStorage = new CompanyPrefixSettingsStorage();

    // Bind methods after storage instances are created
    this.bindMethods();
  }

  private bindMethods() {
    // User methods
    this.getUser = this.userStorage.getUser.bind(this.userStorage);
    this.getUserByUsername = this.userStorage.getUserByUsername.bind(this.userStorage);
    this.getUserByEmail = this.userStorage.getUserByEmail.bind(this.userStorage);
    this.createUser = this.userStorage.createUser.bind(this.userStorage);
    this.updateUser = this.userStorage.updateUser.bind(this.userStorage);
    this.getUsersByCompany = this.userStorage.getUsersByCompany.bind(this.userStorage);
    this.updateUserPassword = this.userStorage.updateUserPassword.bind(this.userStorage);

    // Company methods
    this.getCompany = this.companyStorage.getCompany.bind(this.companyStorage);
    this.getCompanies = this.companyStorage.getCompanies.bind(this.companyStorage);
    this.createCompany = this.companyStorage.createCompany.bind(this.companyStorage);
    this.updateCompany = this.companyStorage.updateCompany.bind(this.companyStorage);
    this.deleteCompany = this.companyStorage.deleteCompany.bind(this.companyStorage);
    this.getCompanyUsers = this.companyStorage.getCompanyUsers.bind(this.companyStorage);

    // UserCompany methods
    this.getUserCompanies = this.userCompanyStorage.getUserCompanies.bind(this.userCompanyStorage);
    this.getUserCompanyByIds = this.userCompanyStorage.getUserCompanyByIds.bind(this.userCompanyStorage);
    this.createUserCompany = this.userCompanyStorage.createUserCompany.bind(this.userCompanyStorage);
    this.updateUserCompany = this.userCompanyStorage.updateUserCompany.bind(this.userCompanyStorage);
    this.deleteUserCompany = this.userCompanyStorage.deleteUserCompany.bind(this.userCompanyStorage);
    this.setUserCompanyAsPrimary = this.userCompanyStorage.setUserCompanyAsPrimary.bind(this.userCompanyStorage);
    this.updateUserCompanyPrimary = this.userCompanyStorage.updateUserCompanyPrimary.bind(this.userCompanyStorage);

    // Customer methods
    this.getCustomer = this.customerStorage.getCustomer.bind(this.customerStorage);
    this.getCustomersByCompany = this.customerStorage.getCustomersByCompany.bind(this.customerStorage);
    this.getCustomersByBranch = this.customerStorage.getCustomersByBranch.bind(this.customerStorage);
    this.createCustomer = this.customerStorage.createCustomer.bind(this.customerStorage);
    this.updateCustomer = this.customerStorage.updateCustomer.bind(this.customerStorage);
    this.deleteCustomer = this.customerStorage.deleteCustomer.bind(this.customerStorage);

    // Loan methods
    this.getLoan = this.loanStorage.getLoan.bind(this.loanStorage);
    this.getLoansByCustomer = this.loanStorage.getLoansByCustomer.bind(this.loanStorage);
    this.getLoansByCompany = this.loanStorage.getLoansByCompany.bind(this.loanStorage);
    this.getLoansByBranch = this.loanStorage.getLoansByBranch.bind(this.loanStorage);
    this.createLoan = this.loanStorage.createLoan.bind(this.loanStorage);
    this.updateLoan = this.loanStorage.updateLoan.bind(this.loanStorage);
    this.deleteLoan = this.loanStorage.deleteLoan.bind(this.loanStorage);
    this.deleteLoanWithCollections = this.loanStorage.deleteLoanWithCollections.bind(this.loanStorage);

    // Collection methods
    this.getCollection = this.collectionStorage.getCollection.bind(this.collectionStorage);
    this.getCollectionsByCompany = this.collectionStorage.getCollectionsByCompany.bind(this.collectionStorage);
    this.getCollectionsByBranch = this.collectionStorage.getCollectionsByBranch.bind(this.collectionStorage);
    this.getCollectionsByLoan = this.collectionStorage.getCollectionsByLoan.bind(this.collectionStorage);
    this.getCollectionsByAgent = this.collectionStorage.getCollectionsByAgent.bind(this.collectionStorage);
    this.createCollection = this.collectionStorage.createCollection.bind(this.collectionStorage);
    this.updateCollection = this.collectionStorage.updateCollection.bind(this.collectionStorage);
    this.updateCollectionStatus = this.collectionStorage.updateCollectionStatus.bind(this.collectionStorage);
    this.deleteCollection = this.collectionStorage.deleteCollection.bind(this.collectionStorage);
    this.markCollectionsAsCompleted = this.collectionStorage.markCollectionsAsCompleted.bind(this.collectionStorage);
    this.getPendingCollections = this.collectionStorage.getPendingCollections.bind(this.collectionStorage);
    this.getOverdueCollections = this.collectionStorage.getOverdueCollections.bind(this.collectionStorage);
    this.getPaginatedCollections = this.collectionStorage.getPaginatedCollections.bind(this.collectionStorage);

    // Payment methods
    this.getPayment = this.paymentStorage.getPayment.bind(this.paymentStorage);
    this.getPaymentsByCompany = this.paymentStorage.getPaymentsByCompany.bind(this.paymentStorage);
    this.getPaymentsByLoan = this.paymentStorage.getPaymentsByLoan.bind(this.paymentStorage);
    this.getPaymentsByCustomer = this.paymentStorage.getPaymentsByCustomer.bind(this.paymentStorage);
    this.getPaymentsByCollection = this.paymentStorage.getPaymentsByCollection.bind(this.paymentStorage);
    this.createPayment = this.paymentStorage.createPayment.bind(this.paymentStorage);
    this.updatePayment = this.paymentStorage.updatePayment.bind(this.paymentStorage);
    this.deletePayment = this.paymentStorage.deletePayment.bind(this.paymentStorage);
    this.getPaymentReceipt = this.paymentStorage.getPaymentReceipt.bind(this.paymentStorage);

    // Agent methods
    this.getAgent = this.agentStorage.getAgent.bind(this.agentStorage);
    this.getAgentsByCompany = this.agentStorage.getAgentsByCompany.bind(this.agentStorage);
    this.getAgentByUserAndCompany = this.agentStorage.getAgentByUserAndCompany.bind(this.agentStorage);
    this.createAgent = this.agentStorage.createAgent.bind(this.agentStorage);
    this.updateAgent = this.agentStorage.updateAgent.bind(this.agentStorage);
    this.deleteAgent = this.agentStorage.deleteAgent.bind(this.agentStorage);
    this.getHighestAgentSerial = this.agentStorage.getHighestAgentSerial.bind(this.agentStorage);

    // Company Prefix Settings methods
    this.getCompanyPrefixSettings = this.companyPrefixSettingsStorage.getCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);
    this.createCompanyPrefixSettings = this.companyPrefixSettingsStorage.createCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);
    this.updateCompanyPrefixSettings = this.companyPrefixSettingsStorage.updateCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);
    this.deleteCompanyPrefixSettings = this.companyPrefixSettingsStorage.deleteCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);

    // Expense methods (implemented directly)
    this.getExpenseById = this.getExpenseByIdImpl.bind(this);
    this.getExpenses = this.getExpensesImpl.bind(this);
    this.createExpense = this.createExpenseImpl.bind(this);
    this.updateExpense = this.updateExpenseImpl.bind(this);
    this.deleteExpense = this.deleteExpenseImpl.bind(this);
  }

  // Expense method implementations
  private async getExpenseByIdImpl(id: number): Promise<Expense | undefined> {
    try {
      const [result] = await db.select().from(expenses).where(eq(expenses.id, id));
      return result;
    } catch (error) {
      console.error('Error in getExpenseById', error);
      return undefined;
    }
  }

  private async getExpensesImpl(companyId: number, filters?: any): Promise<Expense[]> {
    try {
      let query = db.select().from(expenses).where(eq(expenses.company_id, companyId));

      // Add filters if provided
      if (filters?.startDate) {
        // Add date filtering logic here if needed
      }

      const results = await query;
      return results;
    } catch (error) {
      console.error('Error in getExpenses', error);
      return [];
    }
  }

  private async createExpenseImpl(expense: InsertExpense): Promise<Expense> {
    try {
      // Ensure expense_date is a proper Date object
      if (expense.expense_date && typeof expense.expense_date === 'string') {
        expense.expense_date = new Date(expense.expense_date);
      }

      const [result] = await db.insert(expenses).values(expense).returning();
      return result;
    } catch (error) {
      console.error('Error in createExpense', error);
      throw error;
    }
  }

  private async updateExpenseImpl(id: number, expenseData: Partial<InsertExpense>): Promise<Expense> {
    try {
      // Ensure expense_date is a proper Date object if it exists
      if (expenseData.expense_date && typeof expenseData.expense_date === 'string') {
        expenseData.expense_date = new Date(expenseData.expense_date);
      }

      const [result] = await db.update(expenses)
        .set(expenseData)
        .where(eq(expenses.id, id))
        .returning();
      return result;
    } catch (error) {
      console.error('Error in updateExpense', error);
      throw error;
    }
  }

  private async deleteExpenseImpl(id: number): Promise<void> {
    try {
      await db.delete(expenses).where(eq(expenses.id, id));
    } catch (error) {
      console.error('Error in deleteExpense', error);
      throw error;
    }
  }

  // Delegate methods to the appropriate storage implementation (declared as properties)
  getUser!: UserStorage['getUser'];
  getUserByUsername!: UserStorage['getUserByUsername'];
  getUserByEmail!: UserStorage['getUserByEmail'];
  createUser!: UserStorage['createUser'];
  updateUser!: UserStorage['updateUser'];
  getUsersByCompany!: UserStorage['getUsersByCompany'];
  updateUserPassword!: UserStorage['updateUserPassword'];

  // Company methods
  getCompany!: CompanyStorage['getCompany'];
  getCompanies!: CompanyStorage['getCompanies'];
  createCompany!: CompanyStorage['createCompany'];
  updateCompany!: CompanyStorage['updateCompany'];
  deleteCompany!: CompanyStorage['deleteCompany'];
  getCompanyUsers!: CompanyStorage['getCompanyUsers'];

  // UserCompany methods
  getUserCompanies!: UserCompanyStorage['getUserCompanies'];
  getUserCompanyByIds!: UserCompanyStorage['getUserCompanyByIds'];
  createUserCompany!: UserCompanyStorage['createUserCompany'];
  updateUserCompany!: UserCompanyStorage['updateUserCompany'];
  deleteUserCompany!: UserCompanyStorage['deleteUserCompany'];
  setUserCompanyAsPrimary!: UserCompanyStorage['setUserCompanyAsPrimary'];
  updateUserCompanyPrimary!: UserCompanyStorage['updateUserCompanyPrimary'];

  // Customer methods
  getCustomer!: CustomerStorage['getCustomer'];
  getCustomersByCompany!: CustomerStorage['getCustomersByCompany'];
  getCustomersByBranch!: CustomerStorage['getCustomersByBranch'];
  createCustomer!: CustomerStorage['createCustomer'];
  updateCustomer!: CustomerStorage['updateCustomer'];
  deleteCustomer!: CustomerStorage['deleteCustomer'];

  // Loan methods
  getLoan!: LoanStorage['getLoan'];
  getLoansByCustomer!: LoanStorage['getLoansByCustomer'];
  getLoansByCompany!: LoanStorage['getLoansByCompany'];
  getLoansByBranch!: LoanStorage['getLoansByBranch'];
  createLoan!: LoanStorage['createLoan'];
  updateLoan!: LoanStorage['updateLoan'];
  deleteLoan!: LoanStorage['deleteLoan'];
  deleteLoanWithCollections!: LoanStorage['deleteLoanWithCollections'];

  // Collection methods
  getCollection!: CollectionStorage['getCollection'];
  getCollectionsByCompany!: CollectionStorage['getCollectionsByCompany'];
  getCollectionsByBranch!: CollectionStorage['getCollectionsByBranch'];
  getCollectionsByLoan!: CollectionStorage['getCollectionsByLoan'];
  getCollectionsByAgent!: CollectionStorage['getCollectionsByAgent'];
  createCollection!: CollectionStorage['createCollection'];
  updateCollection!: CollectionStorage['updateCollection'];
  updateCollectionStatus!: CollectionStorage['updateCollectionStatus'];
  deleteCollection!: CollectionStorage['deleteCollection'];
  markCollectionsAsCompleted!: CollectionStorage['markCollectionsAsCompleted'];
  getPendingCollections!: CollectionStorage['getPendingCollections'];
  getOverdueCollections!: CollectionStorage['getOverdueCollections'];
  getPaginatedCollections!: CollectionStorage['getPaginatedCollections'];

  // Payment methods
  getPayment!: PaymentStorage['getPayment'];
  getPaymentsByCompany!: PaymentStorage['getPaymentsByCompany'];
  getPaymentsByLoan!: PaymentStorage['getPaymentsByLoan'];
  getPaymentsByCustomer!: PaymentStorage['getPaymentsByCustomer'];
  getPaymentsByCollection!: PaymentStorage['getPaymentsByCollection'];
  createPayment!: PaymentStorage['createPayment'];
  updatePayment!: PaymentStorage['updatePayment'];
  deletePayment!: PaymentStorage['deletePayment'];
  getPaymentReceipt!: PaymentStorage['getPaymentReceipt'];

  // Partner methods with error handling
  getPartner = async (id: number) => {
    try {
      return await this.partnerStorage.getPartner(id);
    } catch (error) {
      console.error('Error in Storage.getPartner:', error);
      throw error;
    }
  };

  getPartnersByCompany = async (companyId: number) => {
    try {
      console.log(`Storage.getPartnersByCompany called with companyId=${companyId}`);
      const result = await this.partnerStorage.getPartnersByCompany(companyId);
      console.log(`Storage.getPartnersByCompany result:`, result);
      return result;
    } catch (error) {
      console.error('Error in Storage.getPartnersByCompany:', error);
      return [];
    }
  };

  createPartner = async (partner: any) => {
    try {
      return await this.partnerStorage.createPartner(partner);
    } catch (error) {
      console.error('Error in Storage.createPartner:', error);
      throw error;
    }
  };

  updatePartner = async (id: number, companyId: number, partner: any) => {
    try {
      return await this.partnerStorage.updatePartner(id, companyId, partner);
    } catch (error) {
      console.error('Error in Storage.updatePartner:', error);
      throw error;
    }
  };

  deletePartner = async (id: number, companyId: number) => {
    try {
      return await this.partnerStorage.deletePartner(id, companyId);
    } catch (error) {
      console.error('Error in Storage.deletePartner:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // Agent methods
  getAgent!: AgentStorage['getAgent'];
  getAgentsByCompany!: AgentStorage['getAgentsByCompany'];
  getAgentByUserAndCompany!: AgentStorage['getAgentByUserAndCompany'];
  createAgent!: AgentStorage['createAgent'];
  updateAgent!: AgentStorage['updateAgent'];
  deleteAgent!: AgentStorage['deleteAgent'];
  getHighestAgentSerial!: AgentStorage['getHighestAgentSerial'];

  // Company Prefix Settings methods
  getCompanyPrefixSettings!: CompanyPrefixSettingsStorage['getCompanyPrefixSettings'];
  createCompanyPrefixSettings!: CompanyPrefixSettingsStorage['createCompanyPrefixSettings'];
  updateCompanyPrefixSettings!: CompanyPrefixSettingsStorage['updateCompanyPrefixSettings'];
  deleteCompanyPrefixSettings!: CompanyPrefixSettingsStorage['deleteCompanyPrefixSettings'];

  // Account methods
  getAccount = financialStorage.getAccount;
  getAccountsByCompany = financialStorage.getAccountsByCompany;
  createAccount = financialStorage.createAccount;
  updateAccount = financialStorage.updateAccount;
  deleteAccount = financialStorage.deleteAccount;
  getAccountBalance = financialStorage.getAccountBalance;
  updateAccountBalance = financialStorage.updateAccountBalance;

  // Transaction methods
  getTransaction = financialStorage.getTransaction;
  getTransactionsByCompany = financialStorage.getTransactionsByCompany;
  getTransactionsByAccount = financialStorage.getTransactionsByAccount;
  createTransaction = financialStorage.createTransaction;
  updateTransaction = financialStorage.updateTransaction;
  deleteTransaction = financialStorage.deleteTransaction;
  getTransactionSummary = financialStorage.getTransactionSummary;

  // Report methods
  getProfitLossReport = financialStorage.getProfitLossReport;
  getCashFlowReport = financialStorage.getCashFlowReport;
  getCollectionReport = financialStorage.getCollectionReport;

  // Expense methods (delegated to old storage implementation for now)
  getExpenseById!: (id: number) => Promise<any>;
  getExpenses!: (companyId: number, filters?: any) => Promise<any[]>;
  createExpense!: (expense: any) => Promise<any>;
  updateExpense!: (id: number, expense: any) => Promise<any>;
  deleteExpense!: (id: number) => Promise<void>;

  // Add other delegated methods for remaining interfaces
}

// Export a singleton instance of the Storage class
export const storage = new Storage();
