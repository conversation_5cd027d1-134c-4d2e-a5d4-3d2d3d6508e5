import errorLogger from './errorLogger';

/**
 * Make an API request
 * @param method HTTP method
 * @param url URL to request
 * @param data Optional data to send
 * @param headers Optional headers
 * @returns Promise with fetch response
 */
export async function apiRequest(
  method: string,
  url: string,
  data?: any,
  headers: HeadersInit = {}
): Promise<Response> {
  // Get auth token from localStorage
  const token = localStorage.getItem('auth_token');

  const options: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    credentials: 'include',
  };

  // Add auth token if we have it
  if (token) {
    options.headers = {
      ...options.headers,
      'Authorization': `Bearer ${token}`
    };
  }

  if (data && method !== 'GET') {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    // Log non-successful responses
    if (!response.ok) {
      errorLogger.error(
        `API request failed: ${response.status} ${response.statusText}`,
        'apiRequest',
        { method, url, status: response.status, statusText: response.statusText }
      );
    }

    return response;
  } catch (error) {
    // Log network errors
    errorLogger.error(
      `API request network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'apiRequest',
      { method, url, error }
    );
    throw error;
  }
}
