/**
 * Database Connection Utility
 * 
 * Provides standardized database connection setup for scripts and utilities
 * Handles Neon database configuration and connection pooling
 */

import { Pool, neonConfig } from '@neondatabase/serverless';
import ws from 'ws';
import { setupEnvironment } from './env-loader.js';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

/**
 * Create a database connection pool with standard configuration
 * @param {Object} options - Pool configuration options
 * @param {number} options.max - Maximum number of connections in pool (default: 5)
 * @returns {Pool} Configured database pool
 */
export function createDatabasePool(options = {}) {
  setupEnvironment(['DATABASE_URL']);
  
  const { max = 5 } = options;
  
  return new Pool({
    connectionString: process.env.DATABASE_URL,
    max
  });
}

/**
 * Execute a database operation with automatic connection management
 * @param {Function} operation - Async function that receives the pool as parameter
 * @param {Object} poolOptions - Pool configuration options
 * @returns {Promise<any>} Result of the operation
 */
export async function withDatabasePool(operation, poolOptions = {}) {
  const pool = createDatabasePool(poolOptions);
  
  try {
    return await operation(pool);
  } finally {
    await pool.end();
  }
}

/**
 * Execute a simple query with automatic connection management
 * @param {string} query - SQL query string
 * @param {Array} params - Query parameters
 * @returns {Promise<any>} Query result
 */
export async function executeQuery(query, params = []) {
  return withDatabasePool(async (pool) => {
    return await pool.query(query, params);
  });
}

/**
 * Check if a table exists in the database
 * @param {string} tableName - Name of the table to check
 * @param {string} schema - Database schema (default: 'public')
 * @returns {Promise<boolean>} True if table exists
 */
export async function tableExists(tableName, schema = 'public') {
  const result = await executeQuery(`
    SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = $1 
      AND table_name = $2
    )
  `, [schema, tableName]);
  
  return result.rows[0].exists;
}

/**
 * Get table column information
 * @param {string} tableName - Name of the table
 * @param {string} schema - Database schema (default: 'public')
 * @returns {Promise<Array>} Array of column information objects
 */
export async function getTableColumns(tableName, schema = 'public') {
  const result = await executeQuery(`
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns 
    WHERE table_schema = $1 
    AND table_name = $2
    ORDER BY ordinal_position
  `, [schema, tableName]);
  
  return result.rows;
}

/**
 * Count rows in a table
 * @param {string} tableName - Name of the table
 * @returns {Promise<number>} Number of rows
 */
export async function countTableRows(tableName) {
  const result = await executeQuery(`SELECT COUNT(*) FROM ${tableName}`);
  return parseInt(result.rows[0].count);
}
