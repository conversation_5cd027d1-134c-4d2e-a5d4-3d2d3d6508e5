import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

export type WorkflowType = 'permission_elevation' | 'loan_approval' | 'customer_data_access' | 'emergency_access' | 'role_assignment' | 'custom';
export type StepType = 'sequential' | 'parallel' | 'any_one' | 'majority' | 'unanimous';
export type ApprovalAction = 'approve' | 'deny' | 'delegate' | 'escalate' | 'request_info';
export type WorkflowStatus = 'pending' | 'in_progress' | 'approved' | 'denied' | 'escalated' | 'cancelled' | 'expired';

export interface WorkflowStep {
  stepName: string;
  stepType: StepType;
  requiredApprovers: number;
  approverRoles?: number[];
  approverUsers?: number[];
  escalationRoles?: number[];
  stepTimeoutHours: number;
  isOptional: boolean;
  conditions?: Record<string, any>;
}

export interface ApprovalWorkflow {
  id: number;
  name: string;
  description?: string;
  workflow_type: WorkflowType;
  company_id: number;
  created_by: number;
  trigger_conditions?: Record<string, any>;
  auto_escalation_hours: number;
  max_escalation_levels: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  steps?: WorkflowStep[];
}

export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  workflowType: WorkflowType;
  triggerConditions?: Record<string, any>;
  autoEscalationHours?: number;
  maxEscalationLevels?: number;
  steps: WorkflowStep[];
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  workflow_type?: WorkflowType;
  trigger_conditions?: Record<string, any>;
  auto_escalation_hours?: number;
  max_escalation_levels?: number;
  is_active?: boolean;
}

export interface WorkflowInstance {
  id: number;
  workflow_id: number;
  company_id: number;
  request_id: string;
  request_type: string;
  requester_id: number;
  request_data: Record<string, any>;
  priority: 'low' | 'medium' | 'high' | 'emergency';
  status: WorkflowStatus;
  current_step_order: number;
  started_at: string;
  completed_at?: string;
  expires_at?: string;
  escalation_level: number;
  final_decision?: ApprovalAction;
  final_decision_by?: number;
  final_decision_at?: string;
  final_decision_notes?: string;
}

export function useApprovalWorkflows(companyId?: number) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all workflows for company
  const {
    data: workflows,
    isLoading: isLoadingWorkflows,
    error: workflowsError,
  } = useQuery({
    queryKey: ['approval-workflows', companyId],
    queryFn: async (): Promise<ApprovalWorkflow[]> => {
      const params = companyId ? `?company_id=${companyId}` : '';
      const response = await apiRequest('GET', `/api/approval-workflows${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch approval workflows');
      }
      return response.json();
    },
    enabled: true,
  });

  // Get workflow by ID
  const getWorkflow = async (workflowId: number): Promise<ApprovalWorkflow> => {
    const response = await apiRequest('GET', `/api/approval-workflows/${workflowId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch workflow');
    }
    return response.json();
  };

  // Create workflow
  const createWorkflowMutation = useMutation({
    mutationFn: async (data: CreateWorkflowRequest): Promise<ApprovalWorkflow> => {
      const response = await apiRequest('/api/approval-workflows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create workflow');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['approval-workflows'] });
      toast({
        title: 'Success',
        description: 'Approval workflow created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Update workflow
  const updateWorkflowMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateWorkflowRequest }): Promise<ApprovalWorkflow> => {
      const response = await apiRequest(`/api/approval-workflows/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update workflow');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['approval-workflows'] });
      toast({
        title: 'Success',
        description: 'Approval workflow updated successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Delete workflow
  const deleteWorkflowMutation = useMutation({
    mutationFn: async (id: number): Promise<void> => {
      const response = await apiRequest(`/api/approval-workflows/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete workflow');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['approval-workflows'] });
      toast({
        title: 'Success',
        description: 'Approval workflow deleted successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Get workflow instances
  const {
    data: workflowInstances,
    isLoading: isLoadingInstances,
    error: instancesError,
  } = useQuery({
    queryKey: ['workflow-instances', companyId],
    queryFn: async (): Promise<WorkflowInstance[]> => {
      const params = companyId ? `?company_id=${companyId}` : '';
      const response = await apiRequest('GET', `/api/approval-workflows/instances${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch workflow instances');
      }
      return response.json();
    },
    enabled: true,
  });

  // Get pending approvals for current user
  const {
    data: pendingApprovals,
    isLoading: isLoadingPending,
    error: pendingError,
  } = useQuery({
    queryKey: ['pending-approvals'],
    queryFn: async (): Promise<any[]> => {
      const response = await apiRequest('GET', '/api/approval-workflows/pending');
      if (!response.ok) {
        throw new Error('Failed to fetch pending approvals');
      }
      return response.json();
    },
    enabled: true,
  });

  // Start workflow
  const startWorkflowMutation = useMutation({
    mutationFn: async (data: {
      requestType: string;
      requestId: string;
      requestData: Record<string, any>;
      priority?: 'low' | 'medium' | 'high' | 'emergency';
      metadata?: Record<string, any>;
    }) => {
      const response = await apiRequest('/api/approval-workflows/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to start workflow');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-instances'] });
      queryClient.invalidateQueries({ queryKey: ['pending-approvals'] });
      toast({
        title: 'Success',
        description: 'Workflow started successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  return {
    // Data
    workflows,
    workflowInstances,
    pendingApprovals,
    
    // Loading states
    isLoading: isLoadingWorkflows || isLoadingInstances || isLoadingPending,
    isLoadingWorkflows,
    isLoadingInstances,
    isLoadingPending,
    
    // Errors
    error: workflowsError || instancesError || pendingError,
    workflowsError,
    instancesError,
    pendingError,
    
    // Mutations
    createWorkflow: createWorkflowMutation.mutate,
    updateWorkflow: updateWorkflowMutation.mutate,
    deleteWorkflow: deleteWorkflowMutation.mutate,
    startWorkflow: startWorkflowMutation.mutate,
    
    // Mutation states
    isCreating: createWorkflowMutation.isPending,
    isUpdating: updateWorkflowMutation.isPending,
    isDeleting: deleteWorkflowMutation.isPending,
    isStarting: startWorkflowMutation.isPending,
    
    // Utilities
    getWorkflow,
  };
}
