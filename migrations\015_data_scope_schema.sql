-- Migration: Data Scope & Field-Level Security Schema
-- Task 3.1.1: Create data scope database schema
-- Date: 2025-01-24

-- Create data scope type enum
CREATE TYPE data_scope_type AS ENUM (
  'branch',
  'department', 
  'hierarchy',
  'group',
  'company',
  'custom'
);

-- Create departments table for organizational structure
CREATE TABLE departments (
  id SERIAL PRIMARY KEY,
  company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  branch_id INTEGER REFERENCES branches(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  manager_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  parent_department_id INTEGER REFERENCES departments(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'active' NOT NULL,
  budget NUMERIC(15, 2),
  cost_center TEXT,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create data scope rules table for organizational access control
CREATE TABLE data_scope_rules (
  id SERIAL PRIMARY KEY,
  role_id INTEGER NOT NULL REFERENCES custom_roles(id) ON DELETE CASCADE,
  scope_type data_scope_type NOT NULL,
  scope_config JSONB NOT NULL,
  resource_type TEXT NOT NULL, -- 'customers', 'loans', 'payments', 'reports', etc.
  access_level TEXT DEFAULT 'read' NOT NULL, -- 'read', 'write', 'delete', 'admin'
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  priority INTEGER DEFAULT 0 NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Add organizational structure columns to users table
ALTER TABLE users 
ADD COLUMN branch_id INTEGER REFERENCES branches(id) ON DELETE SET NULL,
ADD COLUMN department_id INTEGER REFERENCES departments(id) ON DELETE SET NULL,
ADD COLUMN manager_id INTEGER REFERENCES users(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX idx_departments_company_id ON departments(company_id);
CREATE INDEX idx_departments_branch_id ON departments(branch_id);
CREATE INDEX idx_departments_manager_id ON departments(manager_id);
CREATE INDEX idx_departments_parent_department_id ON departments(parent_department_id);

CREATE INDEX idx_data_scope_rules_role_id ON data_scope_rules(role_id);
CREATE INDEX idx_data_scope_rules_scope_type ON data_scope_rules(scope_type);
CREATE INDEX idx_data_scope_rules_resource_type ON data_scope_rules(resource_type);
CREATE INDEX idx_data_scope_rules_is_active ON data_scope_rules(is_active);

CREATE INDEX idx_users_branch_id ON users(branch_id);
CREATE INDEX idx_users_department_id ON users(department_id);
CREATE INDEX idx_users_manager_id ON users(manager_id);

-- Add constraints to prevent circular department hierarchy
ALTER TABLE departments 
ADD CONSTRAINT check_no_self_reference 
CHECK (id != parent_department_id);

-- Insert sample departments for demonstration
INSERT INTO departments (company_id, name, description, status) VALUES
(1, 'Human Resources', 'Manages employee relations and policies', 'active'),
(1, 'Finance', 'Handles financial operations and accounting', 'active'),
(1, 'Operations', 'Manages day-to-day business operations', 'active'),
(1, 'IT', 'Information Technology and systems management', 'active'),
(1, 'Sales', 'Customer acquisition and relationship management', 'active'),
(1, 'Collections', 'Loan collection and recovery operations', 'active');

-- Insert sample data scope rules for common scenarios
INSERT INTO data_scope_rules (role_id, scope_type, scope_config, resource_type, access_level, description) VALUES
-- Branch-based access for loan officers
(1, 'branch', '{"branch_ids": [1]}', 'customers', 'read', 'Loan officers can view customers in their branch'),
(1, 'branch', '{"branch_ids": [1]}', 'loans', 'write', 'Loan officers can manage loans in their branch'),

-- Department-based access for managers
(2, 'department', '{"department_ids": [1, 2]}', 'customers', 'admin', 'Department managers have full access to customers in their departments'),
(2, 'department', '{"department_ids": [1, 2]}', 'reports', 'read', 'Department managers can view reports for their departments'),

-- Hierarchical access for senior managers
(3, 'hierarchy', '{"include_subordinates": true, "max_depth": 2}', 'customers', 'read', 'Senior managers can view data for their subordinates'),
(3, 'hierarchy', '{"include_subordinates": true, "max_depth": 2}', 'loans', 'read', 'Senior managers can view loans managed by their subordinates'),

-- Company-wide access for executives
(4, 'company', '{"all_branches": true, "all_departments": true}', 'customers', 'admin', 'Executives have full access to all customer data'),
(4, 'company', '{"all_branches": true, "all_departments": true}', 'reports', 'admin', 'Executives have full access to all reports');

-- Add comments for documentation
COMMENT ON TABLE departments IS 'Organizational departments for data scope and access control';
COMMENT ON TABLE data_scope_rules IS 'Rules defining data access scope based on organizational structure';
COMMENT ON COLUMN data_scope_rules.scope_config IS 'JSON configuration for scope rules (branch_ids, department_ids, hierarchy settings, etc.)';
COMMENT ON COLUMN data_scope_rules.resource_type IS 'Type of resource this rule applies to (customers, loans, payments, reports, etc.)';
COMMENT ON COLUMN data_scope_rules.access_level IS 'Level of access granted (read, write, delete, admin)';
