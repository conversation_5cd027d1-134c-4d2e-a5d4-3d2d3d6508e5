#!/usr/bin/env node
/**
 * Client Tool: Update Loan Reference Codes via API
 * Purpose: Call the API endpoint to update all loan reference codes to company-specific format
 * Usage: node tools/update-loan-reference-codes-client.js
 * 
 * This tool uses the API endpoint rather than direct database access,
 * making it safer for production use and respecting application logic.
 */

import fetch from 'node-fetch';
import readline from 'readline';
import { setupEnvironment } from '../scripts/utils/env-loader.js';

// Load environment variables
setupEnvironment(['API_URL']);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for input
function prompt(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function updateLoanReferenceCodes() {
  console.log('=== LOAN REFERENCE CODES UPDATE TOOL ===\n');
  
  try {
    // Get API URL from environment or use default
    const API_URL = process.env.API_URL || 'http://localhost:3000';
    console.log(`Using API URL: ${API_URL}`);

    // Prompt for auth token
    const authToken = await prompt('\nEnter your authentication token: ');
    
    if (!authToken.trim()) {
      console.log('❌ Authentication token is required');
      return;
    }

    // Prompt for company ID
    const companyIdInput = await prompt('Enter company ID (or press Enter to use your default company): ');
    const companyId = companyIdInput.trim() ? parseInt(companyIdInput, 10) : undefined;

    console.log('\n🔄 Updating loan reference codes...');

    // Call the API endpoint
    const response = await fetch(`${API_URL}/api/loans/update-reference-codes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        company_id: companyId
      })
    });

    // Parse the response
    const result = await response.json();

    if (response.ok) {
      console.log('\n✅ SUCCESS!');
      console.log(`📊 Total loans: ${result.totalLoans}`);
      console.log(`🔄 Updated loans: ${result.updatedLoans}`);
      console.log(`⏭️  Skipped loans: ${result.totalLoans - result.updatedLoans}`);

      if (result.errors && result.errors.length > 0) {
        console.log(`\n⚠️  ERRORS ENCOUNTERED (${result.errors.length}):`);
        result.errors.forEach(error => {
          console.log(`   - Loan ID ${error.loanId}: ${error.error}`);
        });
      }

      if (result.companyId) {
        console.log(`\n🏢 Company ID: ${result.companyId}`);
      }

      console.log('\n📋 SUMMARY:');
      console.log(`   - Operation completed successfully`);
      console.log(`   - ${result.updatedLoans} loans updated`);
      console.log(`   - ${result.errors?.length || 0} errors encountered`);
      
    } else {
      console.log('\n❌ ERROR:');
      console.log(`   Status: ${response.status} ${response.statusText}`);
      console.log(`   Message: ${result.message || 'Unknown error'}`);
      
      if (result.error) {
        console.log(`   Details: ${result.error}`);
      }

      if (response.status === 401) {
        console.log('\n💡 TIP: Check if your authentication token is valid and not expired');
      } else if (response.status === 403) {
        console.log('\n💡 TIP: Check if you have permission to update loan reference codes');
      } else if (response.status === 404) {
        console.log('\n💡 TIP: Check if the API endpoint exists and the server is running');
      }
    }
  } catch (error) {
    console.log('\n❌ NETWORK/CONNECTION ERROR:');
    console.log(`   ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 TIP: Make sure the server is running and accessible');
    } else if (error.code === 'ENOTFOUND') {
      console.log('\n💡 TIP: Check the API_URL in your .env file');
    }
  } finally {
    rl.close();
  }
}

// Run the script
updateLoanReferenceCodes();
