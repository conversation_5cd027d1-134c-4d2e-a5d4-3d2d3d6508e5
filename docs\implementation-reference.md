# TrackFina Implementation Reference

This document provides comprehensive implementation guides and references for key TrackFina features.

## Reference ID System

### Overview
TrackFina uses a comprehensive reference ID system to provide user-friendly identifiers for all entities instead of exposing internal database IDs.

### Implementation Pattern

All entities follow this pattern:
- **Loans**: `LN-[COMPANY_PREFIX]-[SEQUENCE]` (e.g., `LN-ABC-001`)
- **Customers**: `CU-[COMPANY_PREFIX]-[SEQUENCE]` (e.g., `CU-ABC-001`)
- **Agents**: `AG-[COMPANY_PREFIX]-[SEQUENCE]` (e.g., `AG-ABC-001`)
- **Partners**: `PT-[COMPANY_PREFIX]-[SEQUENCE]` (e.g., `PT-ABC-001`)
- **Transactions**: `TX-[COMPANY_PREFIX]-[SEQUENCE]` (e.g., `TX-ABC-001`)
- **Collections**: `CL-[COMPANY_PREFIX]-[SEQUENCE]` (e.g., `CL-ABC-001`)

### Database Schema
```sql
-- Each entity table includes:
reference_code VARCHAR(50) UNIQUE NOT NULL,
company_id INTEGER NOT NULL,
-- ... other fields
```

### Backend Implementation
```typescript
// Generate reference code
const generateReferenceCode = async (
  prefix: string, 
  companyId: number, 
  companyPrefix: string
): Promise<string> => {
  const sequence = await getNextSequence(prefix, companyId);
  return `${prefix}-${companyPrefix}-${sequence.toString().padStart(3, '0')}`;
};
```

### Frontend Display
- Always show reference codes in UI instead of internal IDs
- Use reference codes in search and filtering
- Display as "Loan ID", "Customer ID", etc. for better UX

## Toast Validation System

### Implementation
Complete toast notification system for form validation and error handling.

### Usage Pattern
```typescript
import { toast } from '@/components/ui/use-toast';

// Success notification
toast({
  title: "Success",
  description: "Operation completed successfully",
});

// Error notification
toast({
  title: "Error", 
  description: "Operation failed",
  variant: "destructive",
});

// Validation error
toast({
  title: "Validation Error",
  description: "Please check the form fields",
  variant: "destructive",
});
```

### Form Integration
```typescript
const onSubmit = async (data: FormData) => {
  try {
    await submitForm(data);
    toast({
      title: "Success",
      description: "Form submitted successfully",
    });
  } catch (error) {
    toast({
      title: "Error",
      description: error.message,
      variant: "destructive",
    });
  }
};
```

## Company Switching System

### Implementation
Multi-company support with seamless switching capabilities.

### Backend Support
```typescript
// Company context middleware
const companyContext = (req: Request, res: Response, next: NextFunction) => {
  const companyId = req.headers['x-company-id'] || req.user.default_company_id;
  req.companyId = companyId;
  next();
};
```

### Frontend Implementation
```typescript
// Company selector component
const CompanySelector = () => {
  const { currentCompany, setCurrentCompany, companies } = useCompany();
  
  return (
    <Select value={currentCompany.id} onValueChange={setCurrentCompany}>
      {companies.map(company => (
        <SelectItem key={company.id} value={company.id}>
          {company.name}
        </SelectItem>
      ))}
    </Select>
  );
};
```

## Loan Collection Process

### Workflow Overview
1. **Loan Creation**: Generate payment schedule
2. **Payment Tracking**: Monitor due dates and payments
3. **Collection Management**: Handle overdue payments
4. **Receipt Generation**: Create payment receipts

### Payment Schedule Generation
```typescript
const generatePaymentSchedule = (
  loanAmount: number,
  interestRate: number,
  termMonths: number,
  startDate: Date
) => {
  const monthlyPayment = calculateEMI(loanAmount, interestRate, termMonths);
  const schedule = [];
  
  for (let i = 1; i <= termMonths; i++) {
    const dueDate = addMonths(startDate, i);
    schedule.push({
      installment_number: i,
      due_date: dueDate,
      amount: monthlyPayment,
      status: 'pending'
    });
  }
  
  return schedule;
};
```

### Collection Status Tracking
- **Pending**: Payment not yet due
- **Due**: Payment is due today
- **Overdue**: Payment is past due date
- **Paid**: Payment completed
- **Partial**: Partial payment received

## System Settings Implementation

### Company Prefix Configuration
```typescript
// System settings schema
interface SystemSettings {
  company_prefix: string;
  loan_prefix: string;
  customer_prefix: string;
  // ... other settings
}

// Settings management
const updateCompanySettings = async (
  companyId: number, 
  settings: Partial<SystemSettings>
) => {
  return await db.update(company_settings)
    .set(settings)
    .where(eq(company_settings.company_id, companyId));
};
```

### Configuration Management
- Company-specific settings
- Prefix customization
- Feature toggles
- Business rule configuration

## Financial Integration

### Chart of Accounts
Standard account structure:
```
1000-1999: Assets
  1000: Cash
  1100: Bank
  1200: Loan Receivable
  
2000-2999: Liabilities
  2000: Accounts Payable
  2100: Accrued Expenses
  
3000-3999: Equity
  3000: Owner's Equity
  3100: Retained Earnings
  
4000-4999: Income
  4000: Interest Income
  4100: Fee Income
  
5000-5999: Expenses
  5000: Operating Expenses
  5100: Administrative Expenses
```

### Journal Entry Creation
```typescript
const createLoanJournalEntry = async (loan: Loan) => {
  const entries = [
    {
      account_id: LOAN_RECEIVABLE_ACCOUNT,
      debit_amount: loan.amount,
      credit_amount: 0,
      description: `Loan disbursement: ${loan.reference_code}`
    },
    {
      account_id: CASH_ACCOUNT,
      debit_amount: 0,
      credit_amount: loan.amount,
      description: `Loan disbursement: ${loan.reference_code}`
    }
  ];
  
  return await createJournalEntry({
    company_id: loan.company_id,
    reference_id: loan.id,
    reference_type: 'loan',
    entries
  });
};
```

## Data Migration Guidelines

### Reference Code Migration
When migrating existing data to use reference codes:

1. **Backup existing data**
2. **Generate reference codes for existing records**
3. **Update foreign key references**
4. **Verify data integrity**
5. **Update application code to use reference codes**

### Migration Script Example
```typescript
const migrateToReferenceCodes = async () => {
  const companies = await getAllCompanies();
  
  for (const company of companies) {
    // Migrate loans
    const loans = await getLoansWithoutReference(company.id);
    for (const loan of loans) {
      const referenceCode = await generateReferenceCode('LN', company.id, company.prefix);
      await updateLoanReference(loan.id, referenceCode);
    }
    
    // Migrate customers
    const customers = await getCustomersWithoutReference(company.id);
    for (const customer of customers) {
      const referenceCode = await generateReferenceCode('CU', company.id, company.prefix);
      await updateCustomerReference(customer.id, referenceCode);
    }
  }
};
```

## Testing Guidelines

### Unit Testing
- Test reference code generation
- Test validation logic
- Test business rule enforcement
- Test error handling

### Integration Testing
- Test API endpoints
- Test database operations
- Test cross-module interactions
- Test company isolation

### End-to-End Testing
- Test complete user workflows
- Test multi-company scenarios
- Test error scenarios
- Test performance under load

## Performance Optimization

### Database Optimization
- Index reference code columns
- Optimize queries with proper joins
- Use pagination for large datasets
- Implement query caching

### Frontend Optimization
- Lazy load components
- Implement virtual scrolling for large lists
- Use React.memo for expensive components
- Optimize bundle size

## Security Considerations

### Data Protection
- Validate all inputs
- Sanitize user data
- Implement proper authentication
- Use HTTPS for all communications

### Access Control
- Implement role-based permissions
- Validate company access
- Audit sensitive operations
- Implement session management

## System Architecture Overview

### Frontend Architecture
- **Framework**: React + TypeScript
- **State Management**: React Query + Context API
- **UI Components**: shadcn/ui + Tailwind CSS
- **Routing**: Wouter

### Backend Architecture
- **Framework**: Node.js + Express
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT with HTTP-only cookies
- **API Style**: RESTful

### Key Components

#### Authentication System
- JWT-based authentication with HTTP-only cookies
- Role-based access control
- Company context switching
- Session management

#### Company Management
- Multi-tenant support with data isolation
- Company switching capabilities
- Company settings management
- Branch and group management

#### Loan Management
- Loan creation with templates
- Interest calculation (flat, reducing, compound)
- Payment scheduling and amortization
- Status tracking and workflow management

#### Collections Management
- Payment tracking and processing
- Receipt generation
- Collection scheduling
- Agent assignment and performance tracking

#### Dynamic Forms
- Custom form templates
- Field validation and conditional logic
- Form submission handling
- Template versioning

### Database Schema Overview

The database schema includes:
- **Companies**: Multi-tenant support
- **Users**: Authentication and roles
- **Customers**: Customer information and relationships
- **Loans**: Loan records and terms
- **Collections**: Payment tracking
- **Payment Schedules**: Amortization schedules
- **Form Templates**: Dynamic form definitions
- **Form Submissions**: Form data storage
- **Branches and Groups**: Organizational structure

### Security Features

- JWT authentication with HTTP-only cookies
- Role-based access control (RBAC)
- Data isolation between companies
- Input validation and sanitization
- Error logging and monitoring
- SQL injection prevention through ORM
- HTTPS enforcement

### Deployment Architecture

The application supports:
- Automatic HTTPS
- Database persistence
- Horizontal scaling capabilities
- Error monitoring and logging
- Performance optimization
- Container-based deployment

This reference guide provides the foundation for implementing and maintaining TrackFina features consistently across the application.
