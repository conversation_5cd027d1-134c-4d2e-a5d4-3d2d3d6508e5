import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  <PERSON>Axis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { 
  TrendingUp, 
  Shield, 
  AlertTriangle, 
  Users, 
  CheckCircle,
  XCircle,
  Eye,
  Clock
} from 'lucide-react';

interface PermissionAnalytics {
  team_size: number;
  permission_coverage: {
    high_privilege_users: number;
    standard_users: number;
    limited_access_users: number;
  };
  permission_trends: Array<{
    date: string;
    new_permissions: number;
    removed_permissions: number;
    role_changes: number;
  }>;
  compliance_metrics: {
    users_with_excessive_permissions: number;
    users_needing_review: number;
    last_review_date?: Date;
  };
}

interface TeamPermissionSummary {
  total_members: number;
  active_members: number;
  permission_distribution: Array<{
    permission_code: string;
    permission_name: string;
    user_count: number;
    percentage: number;
  }>;
  role_distribution: Array<{
    role_name: string;
    user_count: number;
    percentage: number;
  }>;
  recent_changes: Array<{
    user_name: string;
    action: string;
    permission_or_role: string;
    timestamp: Date;
  }>;
}

interface PermissionAnalyticsProps {
  analytics: PermissionAnalytics;
  summary: TeamPermissionSummary;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const PermissionAnalytics: React.FC<PermissionAnalyticsProps> = ({
  analytics,
  summary
}) => {
  // Prepare data for permission coverage pie chart
  const permissionCoverageData = [
    {
      name: 'High Privilege',
      value: analytics.permission_coverage.high_privilege_users,
      color: '#FF8042'
    },
    {
      name: 'Standard',
      value: analytics.permission_coverage.standard_users,
      color: '#00C49F'
    },
    {
      name: 'Limited Access',
      value: analytics.permission_coverage.limited_access_users,
      color: '#0088FE'
    }
  ];

  // Prepare data for role distribution chart
  const roleDistributionData = summary.role_distribution.slice(0, 5).map(role => ({
    name: role.role_name,
    users: role.user_count,
    percentage: role.percentage
  }));

  // Prepare data for permission distribution chart
  const permissionDistributionData = summary.permission_distribution.slice(0, 8).map(permission => ({
    name: permission.permission_name.length > 20 
      ? permission.permission_name.substring(0, 20) + '...' 
      : permission.permission_name,
    users: permission.user_count,
    percentage: permission.percentage
  }));

  const getComplianceStatus = () => {
    const totalIssues = analytics.compliance_metrics.users_with_excessive_permissions + 
                       analytics.compliance_metrics.users_needing_review;
    
    if (totalIssues === 0) return { status: 'Good', color: 'text-green-600', icon: CheckCircle };
    if (totalIssues <= 2) return { status: 'Fair', color: 'text-yellow-600', icon: AlertTriangle };
    return { status: 'Needs Attention', color: 'text-red-600', icon: XCircle };
  };

  const complianceStatus = getComplianceStatus();

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Size</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.team_size}</div>
            <p className="text-xs text-muted-foreground">
              Total team members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Privilege Users</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.permission_coverage.high_privilege_users}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.team_size > 0 ? Math.round((analytics.permission_coverage.high_privilege_users / analytics.team_size) * 100) : 0}% of team
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Status</CardTitle>
            <complianceStatus.icon className={`h-4 w-4 ${complianceStatus.color}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${complianceStatus.color}`}>
              {complianceStatus.status}
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics.compliance_metrics.users_needing_review} users need review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Excessive Permissions</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.compliance_metrics.users_with_excessive_permissions}</div>
            <p className="text-xs text-muted-foreground">
              Users with too many permissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Permission Coverage Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Permission Coverage Distribution</CardTitle>
            <CardDescription>
              Distribution of users by permission level
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={permissionCoverageData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {permissionCoverageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Role Distribution Bar Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Role Distribution</CardTitle>
            <CardDescription>
              Most common roles in your team
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={roleDistributionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="users" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Permission Distribution Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Distribution</CardTitle>
          <CardDescription>
            Most commonly assigned permissions across your team
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={permissionDistributionData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="name" type="category" width={150} />
              <Tooltip />
              <Bar dataKey="users" fill="#00C49F" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Compliance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Compliance Metrics</CardTitle>
          <CardDescription>
            Security and compliance overview for your team
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Users with Excessive Permissions</span>
                <Badge variant={analytics.compliance_metrics.users_with_excessive_permissions > 0 ? 'destructive' : 'default'}>
                  {analytics.compliance_metrics.users_with_excessive_permissions}
                </Badge>
              </div>
              <Progress 
                value={analytics.team_size > 0 ? (analytics.compliance_metrics.users_with_excessive_permissions / analytics.team_size) * 100 : 0} 
                className="h-2" 
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Users Needing Review</span>
                <Badge variant={analytics.compliance_metrics.users_needing_review > 0 ? 'secondary' : 'default'}>
                  {analytics.compliance_metrics.users_needing_review}
                </Badge>
              </div>
              <Progress 
                value={analytics.team_size > 0 ? (analytics.compliance_metrics.users_needing_review / analytics.team_size) * 100 : 0} 
                className="h-2" 
              />
            </div>
          </div>

          {analytics.compliance_metrics.last_review_date && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>
                Last review: {new Date(analytics.compliance_metrics.last_review_date).toLocaleDateString()}
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Changes */}
      {summary.recent_changes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Permission Changes</CardTitle>
            <CardDescription>
              Latest permission and role changes in your team
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {summary.recent_changes.slice(0, 5).map((change, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{change.user_name}</div>
                    <div className="text-sm text-muted-foreground">
                      {change.action} {change.permission_or_role}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(change.timestamp).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
