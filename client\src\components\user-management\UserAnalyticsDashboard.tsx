import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, Shield, AlertTriangle, Activity, TrendingUp, TrendingDown,
  UserCheck, UserX, Clock, BarChart3
} from 'lucide-react';
import { apiRequest } from '@/lib/api';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell, LineChart, Line } from 'recharts';

interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  highRiskUsers: number;
  averagePermissions: number;
  permissionDistribution: Array<{ range: string; count: number }>;
  activityTrends: Array<{ date: string; logins: number; permissionUsage: number }>;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function UserAnalyticsDashboard() {
  const { data: analytics, isLoading } = useQuery({
    queryKey: ['/api/advanced-search/user-analytics'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/advanced-search/user-analytics');
      return response.json() as Promise<UserAnalytics>;
    },
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No analytics data available</p>
        </CardContent>
      </Card>
    );
  }

  const activeUserPercentage = analytics.totalUsers > 0 
    ? Math.round((analytics.activeUsers / analytics.totalUsers) * 100) 
    : 0;

  const highRiskPercentage = analytics.totalUsers > 0 
    ? Math.round((analytics.highRiskUsers / analytics.totalUsers) * 100) 
    : 0;

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                <p className="text-2xl font-bold">{analytics.totalUsers}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold text-green-600">{analytics.activeUsers}</p>
                <p className="text-xs text-muted-foreground">
                  {activeUserPercentage}% of total
                </p>
              </div>
              <UserCheck className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <Progress value={activeUserPercentage} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Inactive Users</p>
                <p className="text-2xl font-bold text-orange-600">{analytics.inactiveUsers}</p>
                <p className="text-xs text-muted-foreground">
                  {100 - activeUserPercentage}% of total
                </p>
              </div>
              <UserX className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">High Risk Users</p>
                <p className="text-2xl font-bold text-red-600">{analytics.highRiskUsers}</p>
                <p className="text-xs text-muted-foreground">
                  {highRiskPercentage}% of total
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            {highRiskPercentage > 0 && (
              <div className="mt-2">
                <Badge variant="destructive" className="text-xs">
                  Requires Attention
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Permission Overview
            </CardTitle>
            <CardDescription>
              Average permissions per user and distribution
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Average Permissions</span>
                <Badge variant="outline" className="text-lg font-bold">
                  {analytics.averagePermissions}
                </Badge>
              </div>
              
              <div>
                <p className="text-sm font-medium mb-2">Permission Distribution</p>
                <div className="space-y-2">
                  {analytics.permissionDistribution.map((item, index) => (
                    <div key={item.range} className="flex items-center justify-between">
                      <span className="text-sm">{item.range} permissions</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full" 
                            style={{ 
                              width: `${(item.count / analytics.totalUsers) * 100}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8">{item.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Activity Trends
            </CardTitle>
            <CardDescription>
              User activity over the last 7 days
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analytics.activityTrends.length > 0 ? (
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={analytics.activityTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="logins" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    name="Logins"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="permissionUsage" 
                    stroke="#82ca9d" 
                    strokeWidth={2}
                    name="Permission Usage"
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-48 text-muted-foreground">
                <div className="text-center">
                  <Clock className="h-8 w-8 mx-auto mb-2" />
                  <p>No activity data available</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Permission Distribution Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Permission Distribution Analysis
          </CardTitle>
          <CardDescription>
            Visual breakdown of permission distribution across users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Bar Chart */}
            <div>
              <h4 className="text-sm font-medium mb-4">Permission Range Distribution</h4>
              <ResponsiveContainer width="100%" height={250}>
                <BarChart data={analytics.permissionDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" tick={{ fontSize: 12 }} />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Pie Chart */}
            <div>
              <h4 className="text-sm font-medium mb-4">User Status Distribution</h4>
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={[
                      { name: 'Active Users', value: analytics.activeUsers, color: '#00C49F' },
                      { name: 'Inactive Users', value: analytics.inactiveUsers, color: '#FFBB28' },
                      { name: 'High Risk Users', value: analytics.highRiskUsers, color: '#FF8042' },
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {[
                      { name: 'Active Users', value: analytics.activeUsers, color: '#00C49F' },
                      { name: 'Inactive Users', value: analytics.inactiveUsers, color: '#FFBB28' },
                      { name: 'High Risk Users', value: analytics.highRiskUsers, color: '#FF8042' },
                    ].map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Insights</CardTitle>
          <CardDescription>
            Key observations from your user data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <TrendingUp className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium">User Engagement</p>
                <p className="text-xs text-muted-foreground">
                  {activeUserPercentage}% of users are active (logged in within 30 days)
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <Shield className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Permission Health</p>
                <p className="text-xs text-muted-foreground">
                  Average of {analytics.averagePermissions} permissions per user
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <AlertTriangle className={`h-5 w-5 mt-0.5 ${highRiskPercentage > 10 ? 'text-red-500' : 'text-yellow-500'}`} />
              <div>
                <p className="text-sm font-medium">Security Status</p>
                <p className="text-xs text-muted-foreground">
                  {highRiskPercentage}% of users are high risk
                  {highRiskPercentage > 10 && ' - Review recommended'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
