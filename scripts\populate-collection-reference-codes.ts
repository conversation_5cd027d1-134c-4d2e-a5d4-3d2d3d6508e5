#!/usr/bin/env npx tsx

/**
 * Migration Script: Populate Collection Reference Codes
 * 
 * This script populates the company_collection_string field for existing collections
 * that don't have reference codes assigned.
 * 
 * Usage:
 *   npx tsx scripts/populate-collection-reference-codes.ts
 *   npx tsx scripts/populate-collection-reference-codes.ts --dry-run
 *   npx tsx scripts/populate-collection-reference-codes.ts --company-id=13
 */

import { db } from '../server/db';
import { collections, companyPrefixSettings } from '../shared/schema';
import { eq, and, isNull, or, desc } from 'drizzle-orm';

interface MigrationOptions {
  dryRun: boolean;
  companyId?: number;
  verbose: boolean;
}

async function parseArgs(): Promise<MigrationOptions> {
  const args = process.argv.slice(2);
  
  return {
    dryRun: args.includes('--dry-run'),
    companyId: args.find(arg => arg.startsWith('--company-id='))?.split('=')[1] ? 
      parseInt(args.find(arg => arg.startsWith('--company-id='))!.split('=')[1]) : undefined,
    verbose: args.includes('--verbose') || args.includes('-v')
  };
}

async function getCompanyPrefix(companyId: number): Promise<{ prefix: string; startNumber: number }> {
  try {
    // Try to get prefix from company_prefix_settings
    const [prefixSettings] = await db.select()
      .from(companyPrefixSettings)
      .where(eq(companyPrefixSettings.company_id, companyId));

    if (prefixSettings) {
      return {
        prefix: prefixSettings.collection_prefix || 'CN',
        startNumber: prefixSettings.collection_start_number || 1
      };
    }

    // Fallback to default
    return { prefix: 'CN', startNumber: 1 };
  } catch (error) {
    console.warn(`Warning: Could not get prefix settings for company ${companyId}, using default`);
    return { prefix: 'CN', startNumber: 1 };
  }
}

async function getHighestSerial(companyId: number, prefix: string): Promise<number> {
  try {
    const result = await db.select()
      .from(collections)
      .where(
        and(
          eq(collections.company_id, companyId),
          // Only look at collections that already have reference codes with this prefix
          eq(collections.company_collection_string, `${prefix}%`)
        )
      )
      .orderBy(desc(collections.company_collection_string));

    let highestSerial = 0;
    for (const collection of result) {
      if (collection.company_collection_string) {
        const match = collection.company_collection_string.match(new RegExp(`^${prefix}-(\\d+)$`));
        if (match) {
          const serial = parseInt(match[1], 10);
          if (serial > highestSerial) {
            highestSerial = serial;
          }
        }
      }
    }

    return highestSerial;
  } catch (error) {
    console.warn(`Warning: Could not get highest serial for company ${companyId}, starting from 0`);
    return 0;
  }
}

async function populateCollectionReferenceCodes(options: MigrationOptions): Promise<void> {
  console.log('🚀 Starting Collection Reference Code Population...');
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }

  try {
    // Get collections that need reference codes
    let whereCondition = or(
      isNull(collections.company_collection_string),
      eq(collections.company_collection_string, '')
    );

    if (options.companyId) {
      whereCondition = and(
        eq(collections.company_id, options.companyId),
        whereCondition
      );
    }

    const collectionsToUpdate = await db.select()
      .from(collections)
      .where(whereCondition)
      .orderBy(collections.company_id, collections.id);

    console.log(`📊 Found ${collectionsToUpdate.length} collections that need reference codes`);

    if (collectionsToUpdate.length === 0) {
      console.log('✅ All collections already have reference codes!');
      return;
    }

    // Group by company
    const companiesMap = new Map<number, typeof collectionsToUpdate>();
    for (const collection of collectionsToUpdate) {
      if (!companiesMap.has(collection.company_id)) {
        companiesMap.set(collection.company_id, []);
      }
      companiesMap.get(collection.company_id)!.push(collection);
    }

    console.log(`🏢 Processing ${companiesMap.size} companies`);

    let totalUpdated = 0;

    for (const [companyId, companyCollections] of companiesMap) {
      console.log(`\n🏢 Processing Company ID: ${companyId} (${companyCollections.length} collections)`);
      
      // Get company prefix settings
      const { prefix, startNumber } = await getCompanyPrefix(companyId);
      console.log(`   📝 Using prefix: "${prefix}" with start number: ${startNumber}`);

      // Get highest existing serial for this company and prefix
      const highestSerial = await getHighestSerial(companyId, prefix);
      let nextSerial = Math.max(highestSerial + 1, startNumber);
      
      console.log(`   🔢 Starting from serial: ${nextSerial} (highest existing: ${highestSerial})`);

      // Update each collection
      for (const collection of companyCollections) {
        const referenceCode = `${prefix}-${nextSerial.toString().padStart(3, '0')}`;
        
        if (options.verbose) {
          console.log(`   📋 Collection ${collection.id} → ${referenceCode}`);
        }

        if (!options.dryRun) {
          await db.update(collections)
            .set({ 
              company_collection_string: referenceCode,
              updated_at: new Date()
            })
            .where(eq(collections.id, collection.id));
        }

        nextSerial++;
        totalUpdated++;
      }

      console.log(`   ✅ ${options.dryRun ? 'Would update' : 'Updated'} ${companyCollections.length} collections for company ${companyId}`);
    }

    console.log(`\n🎉 Migration completed! ${options.dryRun ? 'Would update' : 'Updated'} ${totalUpdated} collections total`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    const options = await parseArgs();
    
    console.log('📋 Migration Options:', {
      dryRun: options.dryRun,
      companyId: options.companyId || 'all',
      verbose: options.verbose
    });

    await populateCollectionReferenceCodes(options);
    
    console.log('\n✅ Migration script completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Migration script failed:', error);
    process.exit(1);
  }
}

// Run the migration
main();
