/**
 * Loan Calculation Utilities (Custom Version)
 * 
 * This file contains the core utility functions for calculating loan payment information,
 * including monthly payments, amortization schedules, and total interest.
 * 
 * UPFRONT INTEREST BUSINESS MODEL:
 * - When interest is deducted upfront, the customer receives (principal - interest)
 * - The full principal amount is then repaid in equal installments
 * - Example: ₹100,000 loan at 10% interest for 10 months
 *   - Customer receives: ₹90,000 (₹100,000 - ₹10,000 interest)
 *   - Customer repays: ₹10,000 per month for 10 months (₹100,000 total)
 */

/**
 * Calculate payment for a loan based on frequency
 * 
 * @param principal The loan amount
 * @param annualInterestRate Annual interest rate as a percentage (e.g. 5 for 5%)
 * @param termInPaymentUnits Term in the payment frequency units (e.g., 10 weeks for weekly payment)
 * @param interestType Type of interest calculation ('flat', 'reducing', or 'compound')
 * @param paymentFrequency Payment frequency ('daily', 'weekly', 'monthly')
 * @param deductInterestUpfront Whether interest is deducted upfront (true) or included in installments (false)
 * @returns Payment amount per frequency period
 */
export function calculateLoanPayment(
  principal: number,
  annualInterestRate: number,
  termInPaymentUnits: number,
  interestType: string = 'flat',
  paymentFrequency: string = 'monthly',
  deductInterestUpfront: boolean = true
): number {
  // Input validation
  if (principal <= 0 || termInPaymentUnits <= 0) {
    return 0;
  }

  // Ensure principal is a proper number (not a string)
  principal = Number(principal);
  
  console.log(`calculateLoanPayment: principal=${principal}, interest=${annualInterestRate}%, ` +
    `term=${termInPaymentUnits}, type=${interestType}, frequency=${paymentFrequency}, ` +
    `upfront=${deductInterestUpfront}`);
  
  // For upfront interest model, we need to handle differently
  if (deductInterestUpfront) {
    // With upfront interest, the payment is principal/term (interest already deducted)
    const payment = Math.round(principal / termInPaymentUnits * 100) / 100;
    console.log(`UPFRONT INTEREST MODEL: Payment = ${principal} / ${termInPaymentUnits} = ${payment}`);
    return payment;
  } else {
    // Without upfront interest, we need to include interest in each payment
    const interestAmount = calculateTotalInterestAmount(
      principal, 
      annualInterestRate, 
      termInPaymentUnits, 
      interestType, 
      paymentFrequency
    );
    const totalRepayable = principal + interestAmount;
    const payment = Math.round(totalRepayable / termInPaymentUnits * 100) / 100;
    console.log(`STANDARD INTEREST MODEL: Payment = (${principal} + ${interestAmount}) / ${termInPaymentUnits} = ${payment}`);
    return payment;
  }
}

/**
 * Calculate total interest paid over the life of the loan
 * 
 * @param principal The loan amount
 * @param annualInterestRate Annual interest rate as a percentage
 * @param termInPaymentUnits Term in the payment frequency units
 * @param interestType Type of interest calculation ('flat', 'reducing', or 'compound')
 * @param paymentFrequency Payment frequency ('daily', 'weekly', 'monthly')
 * @returns Total interest to be paid
 */
export function calculateTotalInterestAmount(
  principal: number,
  annualInterestRate: number,
  termInPaymentUnits: number,
  interestType: string = 'flat',
  paymentFrequency: string = 'monthly'
): number {
  // Input validation
  if (principal <= 0 || termInPaymentUnits <= 0 || annualInterestRate <= 0) {
    return 0;
  }
  
  // Ensure principal is a proper number (not a string)
  principal = Number(principal);

  // FIXED CALCULATION FOR UPFRONT INTEREST
  // For 10,000 loan at 10% interest for 10 weeks, interest should be 1,000
  
  // Business model for flat interest:
  // For flat interest rate, we're applying the full interest rate to the principal,
  // regardless of the term. This is different from time-based calculations.
  let totalInterest = 0;
  
  console.log(`calculateTotalInterestAmount: principal=${principal}, rate=${annualInterestRate}%, term=${termInPaymentUnits}, frequency=${paymentFrequency}`);
  
  if (interestType.toLowerCase() === 'flat' || interestType.toLowerCase() === 'simple') {
    // Flat interest = principal * rate (regardless of term)
    // In our business model, 10% interest on 10,000 is always 1,000
    totalInterest = principal * (annualInterestRate / 100);
    console.log(`FIXED Flat interest calculation: ${principal} * ${annualInterestRate/100} = ${totalInterest}`);
  } else if (interestType.toLowerCase() === 'reducing' || interestType.toLowerCase() === 'diminishing') {
    // For reducing balance, we need to calculate based on amortization
    // This is a simplified calculation for reducing balance
    const ratePerPeriod = (annualInterestRate / 100) / (paymentFrequency === 'monthly' ? 12 : 
                         paymentFrequency === 'weekly' ? 52 : 
                         paymentFrequency === 'biweekly' ? 26 : 
                         paymentFrequency === 'daily' ? 365 : 12);
    
    const paymentFactor = ratePerPeriod * Math.pow(1 + ratePerPeriod, termInPaymentUnits) / 
                         (Math.pow(1 + ratePerPeriod, termInPaymentUnits) - 1);
    
    const payment = principal * paymentFactor;
    totalInterest = (payment * termInPaymentUnits) - principal;
    console.log(`Reducing interest calculation: payment=${payment}, total=${payment * termInPaymentUnits}, interest=${totalInterest}`);
  } else if (interestType.toLowerCase() === 'compound') {
    // Compound interest = P(1+r)^t - P
    const periods = paymentFrequency === 'monthly' ? 12 : 
                    paymentFrequency === 'weekly' ? 52 : 
                    paymentFrequency === 'biweekly' ? 26 : 
                    paymentFrequency === 'daily' ? 365 : 12;
    
    const ratePerPeriod = (annualInterestRate / 100) / periods;
    totalInterest = principal * Math.pow(1 + ratePerPeriod, termInPaymentUnits) - principal;
    console.log(`Compound interest calculation: ${principal} * (1 + ${ratePerPeriod})^${termInPaymentUnits} - ${principal} = ${totalInterest}`);
  }
  
  // Round to 2 decimal places
  totalInterest = Math.round(totalInterest * 100) / 100;
  console.log(`Final totalInterest: ${totalInterest}`);
  
  return totalInterest;
}

/**
 * Amortization schedule entry interface
 */
interface AmortizationEntry {
  paymentNumber: number;
  paymentDate: string;
  paymentAmount: number;
  principalPayment: number;
  interestPayment: number;
  remainingBalance: number;
}

/**
 * Generate a complete amortization schedule for the loan
 *
 * @param principal The loan amount
 * @param annualInterestRate Annual interest rate as a percentage
 * @param termInPaymentUnits Term in the payment frequency units
 * @param interestType Type of interest calculation ('flat', 'reducing', or 'compound')
 * @param startDate Start date of the loan (ISO format YYYY-MM-DD)
 * @param paymentFrequency Payment frequency ('daily', 'weekly', 'monthly')
 * @param deductInterestUpfront Whether interest is deducted upfront (true) or included in installments (false)
 * @returns Array of amortization schedule entries
 */
export function generateAmortizationSchedule(
  principal: number,
  annualInterestRate: number,
  termInPaymentUnits: number,
  interestType: string = 'flat',
  startDate: string = new Date().toISOString().split('T')[0],
  paymentFrequency: string = 'monthly',
  deductInterestUpfront: boolean = true
): AmortizationEntry[] {
  // Input validation
  if (principal <= 0 || termInPaymentUnits <= 0) {
    return [];
  }
  
  const schedule: AmortizationEntry[] = [];
  const periodPayment = calculateLoanPayment(
    principal, 
    annualInterestRate, 
    termInPaymentUnits,
    interestType,
    paymentFrequency,
    deductInterestUpfront
  );
  
  // Calculate total interest (only relevant for the first payment in upfront model)
  const totalInterest = calculateTotalInterestAmount(
    principal,
    annualInterestRate,
    termInPaymentUnits,
    interestType,
    paymentFrequency
  );
  
  // BUSINESS MODEL FIX: Determine the total amount to be repaid in installments
  let totalToRepay;
  
  console.log(`Calculating amortization with upfront interest = ${deductInterestUpfront}`);
  console.log(`Principal amount: ${principal}, Total interest: ${totalInterest}`);
  
  if (deductInterestUpfront) {
    // In upfront interest model, we only repay the principal
    // The customer receives (principal - interest)
    // But must repay the full principal amount in installments
    totalToRepay = principal;
    console.log(`UPFRONT INTEREST MODEL: customer gets ${principal - totalInterest} and repays ${principal} over ${termInPaymentUnits} ${paymentFrequency} periods`);
  } else {
    // In standard model, we repay principal + interest
    // The customer receives the full principal
    // And repays principal + interest in installments
    totalToRepay = principal + totalInterest;
    console.log(`STANDARD INTEREST MODEL: customer gets ${principal} and repays ${totalToRepay} over ${termInPaymentUnits} ${paymentFrequency} periods`);
  }
  
  let remainingBalance = totalToRepay;
  const principalPayment = periodPayment; // Payment amount per period
  
  // Start date as Date object
  const startDateObj = new Date(startDate);
  
  for (let i = 1; i <= termInPaymentUnits; i++) {
    // Calculate payment date based on frequency
    const paymentDate = new Date(startDateObj);
    
    switch(paymentFrequency.toLowerCase()) {
      case 'daily':
        paymentDate.setDate(paymentDate.getDate() + i);
        break;
      case 'weekly':
        paymentDate.setDate(paymentDate.getDate() + (i * 7));
        break;
      case 'biweekly':
        paymentDate.setDate(paymentDate.getDate() + (i * 14));
        break;
      case 'monthly':
        paymentDate.setMonth(paymentDate.getMonth() + i);
        break;
      case 'yearly':
        paymentDate.setFullYear(paymentDate.getFullYear() + i);
        break;
    }
    
    // Calculate remaining balance after this payment
    remainingBalance -= principalPayment;
    if (remainingBalance < 0) remainingBalance = 0; // Avoid negative balances due to rounding
    
    // Add entry to schedule
    schedule.push({
      paymentNumber: i,
      paymentDate: paymentDate.toISOString().split('T')[0],
      paymentAmount: periodPayment,
      // In upfront model, all payments are principal-only
      principalPayment: principalPayment,
      interestPayment: 0, // Interest is paid upfront, not in installments
      remainingBalance: remainingBalance
    });
  }
  
  // Special case: if interest is deducted upfront, add it as a separate first entry
  if (deductInterestUpfront && totalInterest > 0) {
    schedule.unshift({
      paymentNumber: 0,
      paymentDate: startDateObj.toISOString().split('T')[0],
      paymentAmount: totalInterest,
      principalPayment: 0,
      interestPayment: totalInterest,
      remainingBalance: principal
    });
  }
  
  return schedule;
}

/**
 * Convert a term from payment units to years
 * 
 * @param termInPaymentUnits Term in payment units (days, weeks, months)
 * @param paymentFrequency Payment frequency ('daily', 'weekly', 'monthly')
 * @returns Equivalent term in years
 */
function convertToYears(termInPaymentUnits: number, paymentFrequency: string): number {
  switch(paymentFrequency.toLowerCase()) {
    case 'daily':
      return termInPaymentUnits / 365; // Approximate days in a year
    case 'weekly':
      return termInPaymentUnits / 52; // Weeks in a year
    case 'biweekly':
      return termInPaymentUnits / 26; // Bi-weekly periods in a year
    case 'monthly':
      return termInPaymentUnits / 12; // Months in a year
    case 'yearly':
      return termInPaymentUnits; // Already in years
    default:
      return termInPaymentUnits / 12; // Default to monthly
  }
}