import { describe, it, expect, beforeEach, vi } from 'vitest';
import express from 'express';
import request from 'supertest';
import { testUsers, testData, mockDatabase } from './setup';

// Mock the database
const mockDb = mockDatabase();
vi.mock('../../db', () => ({
  db: mockDb
}));

// Mock the storage
vi.mock('../../storage', () => ({
  storage: {
    getCustomer: vi.fn(),
    getCustomersByCompany: vi.fn(),
    createCustomer: vi.fn(),
    updateCustomer: vi.fn()
  }
}));

// Mock the enhanced permission service
const mockPermissionService = {
  checkCustomerDataAccess: vi.fn(),
  checkCustomerExportPermission: vi.fn(),
  checkCustomerCommunicationPermission: vi.fn()
};

vi.mock('../../services/enhancedPermissionService', () => ({
  EnhancedPermissionService: vi.fn().mockImplementation(() => mockPermissionService)
}));

describe('Customer Routes Integration Tests', () => {
  let app: express.Express;

  beforeEach(async () => {
    // Create a fresh Express app for each test
    app = express();
    app.use(express.json());

    // Mock authentication middleware
    app.use((req: any, res, next) => {
      req.user = testUsers.loanOfficer; // Default to loan officer
      next();
    });

    // Import and register routes after mocking
    const { registerCustomerRoutes } = await import('../../routes/customer.routes');
    registerCustomerRoutes(app);
  });

  describe('GET /api/customers/:id - Customer Data Access with Field-Level Security', () => {
    const customerId = 1;
    const mockCustomer = {
      id: customerId,
      full_name: 'John Doe',
      email: '<EMAIL>',
      phone: '+911234567890',
      address: '123 Main St',
      company_id: 1,
      // Sensitive data
      ssn: '***********',
      date_of_birth: '1990-01-01',
      // Financial data
      annual_income: '75000',
      credit_score: 720,
      employment_status: 'employed'
    };

    it('should return basic customer data for users with basic access', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomer).mockResolvedValueOnce(mockCustomer);

      // Mock permission checks
      mockPermissionService.checkCustomerDataAccess
        .mockResolvedValueOnce(false) // sensitive
        .mockResolvedValueOnce(false) // financial
        .mockResolvedValueOnce(true);  // basic

      const response = await request(app)
        .get(`/api/customers/${customerId}`)
        .expect(200);

      // Should include basic fields
      expect(response.body).toHaveProperty('full_name', 'John Doe');
      expect(response.body).toHaveProperty('email', '<EMAIL>');
      expect(response.body).toHaveProperty('phone', '+911234567890');

      // Should exclude sensitive fields
      expect(response.body).not.toHaveProperty('ssn');
      expect(response.body).not.toHaveProperty('date_of_birth');

      // Should exclude financial fields
      expect(response.body).not.toHaveProperty('annual_income');
      expect(response.body).not.toHaveProperty('credit_score');
    });

    it('should return financial data for users with financial access', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomer).mockResolvedValueOnce(mockCustomer);

      // Mock permission checks
      mockPermissionService.checkCustomerDataAccess
        .mockResolvedValueOnce(false) // sensitive
        .mockResolvedValueOnce(true)  // financial
        .mockResolvedValueOnce(true); // basic

      const response = await request(app)
        .get(`/api/customers/${customerId}`)
        .expect(200);

      // Should include basic and financial fields
      expect(response.body).toHaveProperty('full_name', 'John Doe');
      expect(response.body).toHaveProperty('annual_income', '75000');
      expect(response.body).toHaveProperty('credit_score', 720);

      // Should exclude sensitive fields
      expect(response.body).not.toHaveProperty('ssn');
      expect(response.body).not.toHaveProperty('date_of_birth');
    });

    it('should return all data for users with sensitive access', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomer).mockResolvedValueOnce(mockCustomer);

      // Mock permission checks
      mockPermissionService.checkCustomerDataAccess
        .mockResolvedValueOnce(true) // sensitive
        .mockResolvedValueOnce(true) // financial
        .mockResolvedValueOnce(true); // basic

      const response = await request(app)
        .get(`/api/customers/${customerId}`)
        .expect(200);

      // Should include all fields
      expect(response.body).toHaveProperty('full_name', 'John Doe');
      expect(response.body).toHaveProperty('ssn', '***********');
      expect(response.body).toHaveProperty('annual_income', '75000');
      expect(response.body).toHaveProperty('credit_score', 720);
    });

    it('should return 403 when user lacks basic customer data access', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomer).mockResolvedValueOnce(mockCustomer);

      // Mock permission checks to deny all access
      mockPermissionService.checkCustomerDataAccess
        .mockResolvedValueOnce(false) // sensitive
        .mockResolvedValueOnce(false) // financial
        .mockResolvedValueOnce(false); // basic

      const response = await request(app)
        .get(`/api/customers/${customerId}`)
        .expect(403);

      expect(response.body).toHaveProperty('message', 'Access denied to customer data');
    });

    it('should return 404 for non-existent customer', async () => {
      // Mock storage to return null
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomer).mockResolvedValueOnce(null);

      const response = await request(app)
        .get('/api/customers/999')
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Customer not found');
    });
  });

  describe('GET /api/companies/:companyId/customers - Customer List with Permissions', () => {
    const companyId = 1;
    const mockCustomers = [
      {
        id: 1,
        full_name: 'John Doe',
        email: '<EMAIL>',
        phone: '+911234567890',
        ssn: '***********',
        annual_income: '75000',
        company_id: companyId
      },
      {
        id: 2,
        full_name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+911234567891',
        ssn: '***********',
        annual_income: '85000',
        company_id: companyId
      }
    ];

    it('should return filtered customer list based on permissions', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomersByCompany).mockResolvedValueOnce(mockCustomers);

      // Mock permission checks for basic access only
      mockPermissionService.checkCustomerDataAccess
        .mockResolvedValueOnce(false) // sensitive
        .mockResolvedValueOnce(false) // financial
        .mockResolvedValueOnce(true);  // basic

      const response = await request(app)
        .get(`/api/companies/${companyId}/customers`)
        .expect(200);

      expect(response.body).toHaveLength(2);
      
      // Check first customer
      expect(response.body[0]).toHaveProperty('full_name', 'John Doe');
      expect(response.body[0]).toHaveProperty('email', '<EMAIL>');
      expect(response.body[0]).not.toHaveProperty('ssn');
      expect(response.body[0]).not.toHaveProperty('annual_income');

      // Check second customer
      expect(response.body[1]).toHaveProperty('full_name', 'Jane Smith');
      expect(response.body[1]).not.toHaveProperty('ssn');
    });

    it('should handle pagination parameters', async () => {
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomersByCompany).mockResolvedValueOnce(mockCustomers.slice(0, 1));

      // Mock permission checks
      mockPermissionService.checkCustomerDataAccess
        .mockResolvedValueOnce(true) // sensitive
        .mockResolvedValueOnce(true) // financial
        .mockResolvedValueOnce(true); // basic

      const response = await request(app)
        .get(`/api/companies/${companyId}/customers`)
        .query({ page: 1, limit: 1 })
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toHaveProperty('full_name', 'John Doe');
    });

    it('should handle search parameters', async () => {
      const searchResults = [mockCustomers[0]]; // Only John Doe
      
      // Mock storage operations
      const { storage } = await import('../../storage');
      vi.mocked(storage.getCustomersByCompany).mockResolvedValueOnce(searchResults);

      // Mock permission checks
      mockPermissionService.checkCustomerDataAccess
        .mockResolvedValueOnce(false) // sensitive
        .mockResolvedValueOnce(true)  // financial
        .mockResolvedValueOnce(true); // basic

      const response = await request(app)
        .get(`/api/companies/${companyId}/customers`)
        .query({ search: 'John' })
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toHaveProperty('full_name', 'John Doe');
      expect(response.body[0]).toHaveProperty('annual_income', '75000');
      expect(response.body[0]).not.toHaveProperty('ssn');
    });
  });

  describe('Customer Export and Communication Permissions', () => {
    it('should allow customer export with proper permissions', async () => {
      // Mock permission check to pass
      mockPermissionService.checkCustomerExportPermission.mockResolvedValueOnce(true);

      // This would be tested if there was an export endpoint
      // For now, we test the permission check logic
      expect(mockPermissionService.checkCustomerExportPermission).toBeDefined();
    });

    it('should allow customer communication with proper permissions', async () => {
      // Mock permission check to pass
      mockPermissionService.checkCustomerCommunicationPermission.mockResolvedValueOnce(true);

      // This would be tested if there was a communication endpoint
      // For now, we test the permission check logic
      expect(mockPermissionService.checkCustomerCommunicationPermission).toBeDefined();
    });
  });
});
