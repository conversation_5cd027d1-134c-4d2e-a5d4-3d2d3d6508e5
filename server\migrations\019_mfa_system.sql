-- Migration: Add MFA system tables
-- Description: Add multi-factor authentication support with TOTP and backup codes
-- Date: 2025-01-24

-- MFA settings table for user MFA configuration
CREATE TABLE IF NOT EXISTS "user_mfa_settings" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "is_enabled" BOOLEAN DEFAULT false,
  "secret" TEXT, -- TOTP secret (base32 encoded)
  "backup_codes" JSONB DEFAULT '[]', -- Array of backup codes
  "last_used_at" TIMESTAMP,
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW(),
  UNIQUE("user_id")
);

-- MFA verification attempts for security monitoring
CREATE TABLE IF NOT EXISTS "mfa_verification_attempts" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "attempt_type" VARCHAR(20) NOT NULL, -- 'totp', 'backup_code'
  "success" BOOLEAN NOT NULL,
  "ip_address" INET,
  "user_agent" TEXT,
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Email verification tokens table
CREATE TABLE IF NOT EXISTS "email_verification_tokens" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "token" VARCHAR(64) NOT NULL UNIQUE,
  "expires_at" TIMESTAMP NOT NULL,
  "created_at" TIMESTAMP DEFAULT NOW()
);

-- Add MFA policy columns to companies table
ALTER TABLE "companies" ADD COLUMN IF NOT EXISTS "mfa_required" BOOLEAN DEFAULT false;
ALTER TABLE "companies" ADD COLUMN IF NOT EXISTS "mfa_grace_period_days" INTEGER DEFAULT 30;

-- Add password reset and email verification columns to users table
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "password_reset_token" VARCHAR(64);
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "password_reset_expires" TIMESTAMP;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "password_updated_at" TIMESTAMP DEFAULT NOW();
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "email_verified" BOOLEAN DEFAULT false;

-- Add account lockout columns to users table
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "failed_login_attempts" INTEGER DEFAULT 0;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "locked_until" TIMESTAMP;
ALTER TABLE "users" ADD COLUMN IF NOT EXISTS "last_login_attempt" TIMESTAMP;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_user_mfa_settings_user_id" ON "user_mfa_settings"("user_id");
CREATE INDEX IF NOT EXISTS "idx_mfa_verification_attempts_user_id" ON "mfa_verification_attempts"("user_id");
CREATE INDEX IF NOT EXISTS "idx_mfa_verification_attempts_created_at" ON "mfa_verification_attempts"("created_at");
CREATE INDEX IF NOT EXISTS "idx_email_verification_tokens_token" ON "email_verification_tokens"("token");
CREATE INDEX IF NOT EXISTS "idx_email_verification_tokens_expires_at" ON "email_verification_tokens"("expires_at");
CREATE INDEX IF NOT EXISTS "idx_users_password_reset_token" ON "users"("password_reset_token");
CREATE INDEX IF NOT EXISTS "idx_users_email_verified" ON "users"("email_verified");
CREATE INDEX IF NOT EXISTS "idx_users_locked_until" ON "users"("locked_until");

-- Insert sample MFA policies for existing companies
UPDATE "companies" SET "mfa_required" = true WHERE "name" LIKE '%Admin%' OR "name" LIKE '%Enterprise%';

-- Add comments for documentation
COMMENT ON TABLE "user_mfa_settings" IS 'Stores MFA configuration for users including TOTP secrets and backup codes';
COMMENT ON TABLE "mfa_verification_attempts" IS 'Logs all MFA verification attempts for security monitoring';
COMMENT ON TABLE "email_verification_tokens" IS 'Stores email verification tokens for new user registration';
COMMENT ON COLUMN "user_mfa_settings"."secret" IS 'Base32 encoded TOTP secret for authenticator apps';
COMMENT ON COLUMN "user_mfa_settings"."backup_codes" IS 'JSON array of one-time backup codes for account recovery';
COMMENT ON COLUMN "companies"."mfa_required" IS 'Whether MFA is mandatory for all users in this company';
COMMENT ON COLUMN "companies"."mfa_grace_period_days" IS 'Number of days users have to set up MFA after it becomes required';
