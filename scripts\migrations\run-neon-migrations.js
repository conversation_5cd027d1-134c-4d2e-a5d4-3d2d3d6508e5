#!/usr/bin/env node
/**
 * Migration Script: Run Multiple Neon DB Migrations
 * Purpose: Execute multiple database migrations in sequence on Neon database
 * Usage: node scripts/migrations/run-neon-migrations.js
 */

import { runMigrations, validateMigrationPrerequisites } from '../utils/migration-runner.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Define migrations to run in order
const migrations = [
  {
    name: 'Add Company Collection String',
    function: async (pool, { dryRun }) => {
      const migrationPath = path.join(__dirname, '..', '..', 'migrations', '003_add_company_collection_string.sql');
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`);
      }
      
      if (dryRun) {
        console.log('Would add company_collection_string column to collections table');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);
      
      // Verify
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'collections' AND column_name = 'company_collection_string'
      `);
      
      if (result.rows.length === 0) {
        throw new Error('company_collection_string column was not created');
      }
      
      console.log('✅ company_collection_string column added to collections table');
    }
  },
  {
    name: 'Create Expenses Table',
    function: async (pool, { dryRun }) => {
      const migrationPath = path.join(__dirname, '..', '..', 'migrations', '004_create_expenses_table.sql');
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`);
      }
      
      if (dryRun) {
        console.log('Would create expenses table and expense_type enum');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);
      
      // Verify table and enum
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables WHERE table_name = 'expenses'
        ) as table_exists;
      `);
      
      const enumCheck = await pool.query(`
        SELECT EXISTS (
          SELECT 1 FROM pg_type WHERE typname = 'expense_type'
        ) as enum_exists;
      `);
      
      if (!tableCheck.rows[0].table_exists || !enumCheck.rows[0].enum_exists) {
        throw new Error('Expenses table or expense_type enum was not created');
      }
      
      console.log('✅ expenses table and expense_type enum created');
    }
  },
  {
    name: 'Add Loan Reference Code',
    function: async (pool, { dryRun }) => {
      const migrationPath = path.join(__dirname, '..', '..', 'migrations', '006_add_loan_reference_code.sql');
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`);
      }
      
      if (dryRun) {
        console.log('Would add loan_reference_code column to loans table');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);
      
      // Verify
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'loans' AND column_name = 'loan_reference_code'
      `);
      
      if (result.rows.length === 0) {
        throw new Error('loan_reference_code column was not created');
      }
      
      console.log('✅ loan_reference_code column added to loans table');
    }
  },
  {
    name: 'Add Customer Reference Code',
    function: async (pool, { dryRun }) => {
      const migrationPath = path.join(__dirname, '..', '..', 'migrations', '007_add_customer_reference_code.sql');
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`);
      }
      
      if (dryRun) {
        console.log('Would add customer_reference_code column to customers table');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);
      
      // Verify
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'customers' AND column_name = 'customer_reference_code'
      `);
      
      if (result.rows.length === 0) {
        throw new Error('customer_reference_code column was not created');
      }
      
      console.log('✅ customer_reference_code column added to customers table');
    }
  },
  {
    name: 'Add Partner Reference Code',
    function: async (pool, { dryRun }) => {
      const migrationPath = path.join(__dirname, '..', '..', 'migrations', '008_add_partner_reference_code.sql');
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`);
      }
      
      if (dryRun) {
        console.log('Would add partner_reference_code column to partners table');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);
      
      // Verify
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'partners' AND column_name = 'partner_reference_code'
      `);
      
      if (result.rows.length === 0) {
        throw new Error('partner_reference_code column was not created');
      }
      
      console.log('✅ partner_reference_code column added to partners table');
    }
  },
  {
    name: 'Create Company Prefix Settings',
    function: async (pool, { dryRun }) => {
      const migrationPath = path.join(__dirname, '..', '..', 'migrations', '009_create_company_prefix_settings.sql');
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`);
      }
      
      if (dryRun) {
        console.log('Would create company_prefix_settings table');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);
      
      // Verify
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT 1 FROM information_schema.tables WHERE table_name = 'company_prefix_settings'
        ) as table_exists;
      `);
      
      if (!tableCheck.rows[0].table_exists) {
        throw new Error('company_prefix_settings table was not created');
      }
      
      console.log('✅ company_prefix_settings table created');
    }
  },
  {
    name: 'Add Transaction Reference Code',
    function: async (pool, { dryRun }) => {
      const migrationPath = path.join(__dirname, '..', '..', 'migrations', '010_add_transaction_reference_code.sql');
      
      if (!fs.existsSync(migrationPath)) {
        throw new Error(`Migration file not found: ${migrationPath}`);
      }
      
      if (dryRun) {
        console.log('Would add transaction_reference_code column to transactions table');
        return;
      }
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);
      
      // Verify
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'transactions' AND column_name = 'transaction_reference_code'
      `);
      
      if (result.rows.length === 0) {
        throw new Error('transaction_reference_code column was not created');
      }
      
      console.log('✅ transaction_reference_code column added to transactions table');
    }
  }
];

// Validate prerequisites before running migrations
await validateMigrationPrerequisites(async (pool) => {
  // Check if database connection is working
  const result = await pool.query('SELECT 1 as test');
  if (result.rows[0].test !== 1) {
    throw new Error('Database connection test failed');
  }
  
  // Check if migrations directory exists
  const migrationsDir = path.join(__dirname, '..', '..', 'migrations');
  if (!fs.existsSync(migrationsDir)) {
    throw new Error(`Migrations directory not found: ${migrationsDir}`);
  }
});

// Run all migrations
await runMigrations(migrations);
