import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useLocation } from "wouter";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { formatCurrency } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import {
  ArrowLeft,
  Loader2,
  AlertTriangle,
  RefreshCw,
  Pencil,
  Calculator,
} from "lucide-react";

// Form components
// Removed StaticLoanFormRenderer - no longer using template-based rendering

// Loan calculation components
import LoanCalculator from "@/components/loan/LoanCalculator";
import AmortizationSchedule from "@/components/loan/AmortizationSchedule";
import { LoanFormCalculatorInline } from "@/components/loan/LoanFormCalculatorInline";

// Payment schedule components
import { PaymentScheduleTable } from "@/components/payment-schedule/PaymentScheduleTable";

// Interface definitions
interface Loan {
  id: number;
  customer_id: number;
  company_id: number;
  amount: string | number;
  interest_rate: string | number;
  interest_type: 'flat' | 'reducing' | 'compound';
  term: number;
  terms_frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'yearly';
  is_upfront_interest?: boolean;
  start_date: string;
  end_date: string;
  status: string;
  created_at: string;
  updated_at: string;
  disbursed_amount?: string | number;
  total_repayable?: string | number;
  installment_amount?: string | number;
  payment_frequency?: 'daily' | 'weekly' | 'monthly';
  customer?: {
    id: number;
    full_name: string;
    email?: string;
    phone?: string;
  };
  notes?: string | null;
}

interface FormTemplate {
  id: number;
  name: string;
  description: string;
  company_id: number;
  branch_id: number | null;
  is_active: boolean;
  category: string | null;
  created_at: string;
  updated_at: string;
}

interface LoanConfiguration {
  id: number;
  company_id: number;
  template_id: number;
  branch_id: number | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  template?: FormTemplate;
}

export default function LoanDetailPage() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const companyId = user?.company_id || 1;
  const loanId = parseInt(params.id, 10);
  
  // State for tracking the loan template for details view
  const [loanTemplateId, setLoanTemplateId] = useState<number | null>(null);
  
  // Initialize form data with empty object but we'll populate it when loan data loads
  const [formData, setFormData] = useState<Record<string, any>>({});
  
  // Fetch loan data
  const { 
    data: loan, 
    isLoading: isLoadingLoan, 
    error: loanError 
  } = useQuery<Loan>({
    queryKey: [`/api/loans/${loanId}`],
    enabled: !isNaN(loanId),
  });
  
  // Fetch form submission data to get custom fields
  const {
    data: formSubmissionData,
    isLoading: isLoadingFormSubmission,
    refetch: refetchFormSubmissions
  } = useQuery<any[]>({
    queryKey: [`/api/companies/${companyId}/loans/${loanId}/form-submissions`],
    enabled: !isNaN(loanId) && !isNaN(companyId),
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    staleTime: 0, // Always consider data stale to force refetch
    gcTime: 0  // Don't cache the data
  });

  // Fetch active loan configurations and their templates
  const { 
    data: loanConfigurations, 
    isLoading: isLoadingConfigurations 
  } = useQuery<(LoanConfiguration & { template: FormTemplate })[]>({
    queryKey: [`/api/companies/${companyId}/loan-configurations/active`],
    enabled: !isNaN(companyId),
  });

  // Extract just the templates from the loan configurations 
  const isLoadingTemplates = isLoadingConfigurations;

  const handleBackToLoans = () => {
    setLocation("/loans");
  };
  
  const handleFormDataChange = (data: Record<string, any>) => {
    setFormData(data);
  };
  
  // Access query client for direct cache manipulation
  const queryClient = useQueryClient();
  
  // State to track if refresh is in progress
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Function to directly fetch form submission data bypassing cache
  const forceRefreshFormData = async () => {
    setIsRefreshing(true);
    try {
      const response = await apiRequest('GET', `/api/companies/${companyId}/loans/${loanId}/form-submissions`);
      if (response.ok) {
        const data = await response.json();
        // Manually update the cache with fresh data
        queryClient.setQueryData(
          [`/api/companies/${companyId}/loans/${loanId}/form-submissions`], 
          data
        );
        // Force a refetch to be sure
        await refetchFormSubmissions();
        console.log("Form submission data manually refreshed", data);
      }
    } catch (error) {
      console.error("Error manually refreshing form data:", error);
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // Set default template when configurations load
  useEffect(() => {
    if (loanConfigurations && loanConfigurations.length > 0) {
      const firstTemplate = loanConfigurations[0].template;
      setLoanTemplateId(firstTemplate.id);
      console.log("Selected loan template ID:", firstTemplate.id);
    }
  }, [loanConfigurations]);
  
  // Check if this loan was just created and force a refresh of the form data
  useEffect(() => {
    const justCreatedLoanId = sessionStorage.getItem('just_created_loan');
    if (justCreatedLoanId && justCreatedLoanId === loanId.toString()) {
      console.log("This loan was just created, forcing data refresh...");
      // Clear the flag so we don't refresh again
      sessionStorage.removeItem('just_created_loan');
      
      // Set a small delay to ensure database writes are complete
      const timer = setTimeout(() => {
        forceRefreshFormData();
      }, 800);
      
      return () => clearTimeout(timer);
    }
  }, [loanId]);
  
  // Initialize form data when loan data is loaded
  useEffect(() => {
    if (loan) {
      // Prepare base form data with loan values
      const baseFormData: Record<string, any> = {
        // Standard fields
        customer_id: loan.customer_id,
        amount: typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount),
        interest_rate: typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate),
        // TypeScript fix: Use type assertion to allow comparison with 'simple'
        interest_type: (loan.interest_type as string) === 'simple' ? 'flat' : loan.interest_type,
        term: typeof loan.term === 'string' ? parseInt(loan.term.toString()) : loan.term,
        terms_frequency: loan.terms_frequency || 'monthly',
        is_upfront_interest: loan.is_upfront_interest !== undefined ? loan.is_upfront_interest : true,
        start_date: typeof loan.start_date === 'string' ? loan.start_date.split('T')[0] : loan.start_date,
        end_date: typeof loan.end_date === 'string' ? loan.end_date.split('T')[0] : loan.end_date,
        
        // Field mappings for calculators to work
        field_101: typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount),
        field_100: typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate),
        // TypeScript fix: Use type assertion to allow comparison with 'simple'
        field_102: (loan.interest_type as string) === 'simple' ? 'flat' : loan.interest_type,
        field_103: typeof loan.term === 'string' ? parseInt(loan.term.toString()) : loan.term,
        field_104: typeof loan.start_date === 'string' ? loan.start_date.split('T')[0] : loan.start_date,
        field_105: typeof loan.end_date === 'string' ? loan.end_date.split('T')[0] : loan.end_date,
        field_109: loan.customer_id,
      };
      
      // Extract form submission data if available
      let submissionData: Record<string, any> = {};
      if (formSubmissionData && formSubmissionData.length > 0) {
        const rawFormData = formSubmissionData[0]?.form_data;
        
        if (rawFormData) {
          // Parse form data if it's a string
          const parsedFormData: Record<string, any> = typeof rawFormData === 'string' ? 
            JSON.parse(rawFormData) : rawFormData;
            
          // Process the form data
          submissionData = parsedFormData;
          
          // Map 'simple' to 'flat' in form data
          if (parsedFormData.interest_type === 'simple' || parsedFormData.field_120 === 'simple') {
            submissionData.interest_type = 'flat';
            submissionData.field_120 = 'flat';
          }
          
          // Process any special fields
          if (parsedFormData.field_123 && typeof parsedFormData.field_123 === 'string') {
            // If customer ID is stored as "1 - Customer Name", extract just the ID
            const matches = parsedFormData.field_123.match(/^(\d+)/);
            if (matches && matches[1]) {
              submissionData.field_123 = parseInt(matches[1], 10);
            }
          }
          
          // Process numeric fields to ensure they're properly formatted
          ['field_121', 'field_122', 'field_125'].forEach(field => {
            if (parsedFormData[field]) {
              if (typeof parsedFormData[field] === 'string') {
                // Clean any non-numeric characters except decimal point
                const cleanedValue = parsedFormData[field].replace(/[^0-9.-]/g, '');
                submissionData[field] = field === 'field_125' ? 
                  parseInt(cleanedValue, 10) : parseFloat(cleanedValue);
              }
            }
          });
          
          // Process date fields to ensure consistency
          ['field_124', 'field_126'].forEach(field => {
            if (parsedFormData[field] && typeof parsedFormData[field] === 'string') {
              if (parsedFormData[field].includes('/')) {
                // Convert MM/DD/YYYY to YYYY-MM-DD
                const parts = parsedFormData[field].split('/');
                if (parts.length === 3) {
                  submissionData[field] = `${parts[2]}-${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}`;
                }
              }
            }
          });
        }
      }
      
      // Merge base data with submission data, giving priority to submission data
      const initialFormData = {
        ...baseFormData,
        ...submissionData
      };
      
      console.log("Initializing form data in detail page:", initialFormData);
      setFormData(initialFormData);
    }
  }, [loan, formSubmissionData]);
  
  if (isLoadingLoan || isLoadingFormSubmission) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (loanError || !loan) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load loan details. Please try again.
          </AlertDescription>
        </Alert>
        <Button variant="outline" onClick={handleBackToLoans}>
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Loans
        </Button>
      </div>
    );
  }

  const getInterestTypeLabel = (type: string) => {
    // Normalize the type to handle legacy 'simple' values
    const normalizedType = type?.toLowerCase() === 'simple' ? 'flat' : type;
    
    switch (normalizedType) {
      case 'flat': return 'Flat Rate';
      case 'reducing': return 'Reducing Balance';
      case 'compound': return 'Compound Interest';
      case 'simple': return 'Flat Rate'; // Legacy support 
      default: return type || 'Unknown';
    }
  };
  
  const getStatusBadgeVariant = (status?: string) => {
    if (!status) return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'overdue': return 'bg-red-100 text-red-800 hover:bg-red-100';
      case 'completed': return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'pending': return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'rejected': return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      default: return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };
  
  const getStatusIcon = (status?: string) => {
    if (!status) return null;
    
    switch (status.toLowerCase()) {
      case 'active': 
        return <div className="h-2 w-2 rounded-full bg-green-500 mr-1.5" />;
      case 'overdue': 
        return <div className="h-2 w-2 rounded-full bg-red-500 mr-1.5" />;
      case 'completed': 
        return <div className="h-2 w-2 rounded-full bg-blue-500 mr-1.5" />;
      case 'pending': 
        return <div className="h-2 w-2 rounded-full bg-yellow-500 mr-1.5" />;
      default:
        return <div className="h-2 w-2 rounded-full bg-gray-500 mr-1.5" />;
    }
  };
  
  // Format the term based on payment frequency
  const formatLoanTerm = (termMonths: number, paymentFrequency?: string): string => {
    if (!paymentFrequency) return `${termMonths} months`;
    
    switch(paymentFrequency.toLowerCase()) {
      case 'daily':
        // Convert from months to days (approximate)
        const days = Math.round(termMonths * 30);
        return `${days} days`;
      case 'weekly':
        // Convert from months to weeks (approximate)
        const weeks = Math.round(termMonths * 4.33);
        return `${weeks} weeks`;
      case 'monthly':
        return `${termMonths} months`;
      default:
        return `${termMonths} months`;
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="outline" onClick={handleBackToLoans} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Loans
        </Button>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
              Loan #{loan.id}
            </h1>
            <p className="text-muted-foreground mt-1">
              {loan.customer?.full_name || `Customer ${loan.customer_id}`}
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <Badge variant="outline" className="text-base py-1 px-3">
              {formatCurrency(
                typeof loan.amount === 'string' 
                  ? parseFloat(loan.amount) 
                  : loan.amount, 
                'INR', 
                'en-IN'
              )}
            </Badge>
            <Badge className={getStatusBadgeVariant(loan.status)}>
              <div className="flex items-center">
                {getStatusIcon(loan.status)}
                <span>{loan.status}</span>
              </div>
            </Badge>
          </div>
        </div>
      </div>

      {/* Mobile Quick Summary Card */}
      <div className="md:hidden mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center py-2 border-b">
              <span className="text-sm font-medium">Status</span>
              <Badge className={getStatusBadgeVariant(loan.status)}>
                <div className="flex items-center">
                  {getStatusIcon(loan.status)}
                  <span>{loan.status}</span>
                </div>
              </Badge>
            </div>
            <div className="flex justify-between items-center py-2 border-b">
              <span className="text-sm font-medium">Interest Rate</span>
              <span className="text-sm">
                {typeof loan.interest_rate === 'string' 
                  ? parseFloat(loan.interest_rate) 
                  : loan.interest_rate}%
                <span className="text-xs text-muted-foreground ml-1">
                  ({getInterestTypeLabel(loan.interest_type)})
                </span>
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b">
              <span className="text-sm font-medium">Term</span>
              <span className="text-sm">{formatLoanTerm(loan.term, loan.payment_frequency)}</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-sm font-medium">Start - End</span>
              <span className="text-sm">
                {new Date(loan.start_date).toLocaleDateString()} - {new Date(loan.end_date).toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed loan information */}
      <div className="space-y-6">
        <Card>
          <CardHeader className="md:flex md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle>Loan Information</CardTitle>
              <CardDescription>
                View detailed information about this loan
              </CardDescription>
            </div>
            {/* Force Refresh Button - Desktop Version */}
            <div className="hidden md:block">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={forceRefreshFormData}
                disabled={isRefreshing}
                className="h-8"
              >
                {isRefreshing ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-1" />
                )}
                Force Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isLoadingConfigurations ? (
              <div className="flex justify-center items-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading loan details...</span>
              </div>
            ) : loan ? (
              <div className="space-y-6">
                {/* Summary Information - Only visible on desktop */}
                <div className="hidden md:grid md:grid-cols-2 gap-6 pb-4 border-b">
                  <div>
                    <h3 className="text-sm font-semibold mb-2">Customer Information</h3>
                    <div>
                      <span className="text-sm text-muted-foreground">Name:</span>
                      <p className="font-medium">{loan.customer?.full_name || `Customer ${loan.customer_id}`}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold mb-2">Loan Summary</h3>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm text-muted-foreground">Amount:</span>
                      <Badge variant="outline" className="font-medium">
                        {formatCurrency(
                          typeof loan.amount === 'string' 
                            ? parseFloat(loan.amount) 
                            : loan.amount, 
                          'INR', 
                          'en-IN'
                        )}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">Status:</span>
                      <Badge className={getStatusBadgeVariant(loan.status)}>
                        <div className="flex items-center">
                          {getStatusIcon(loan.status)}
                          <span>{loan.status}</span>
                        </div>
                      </Badge>
                    </div>
                  </div>
                </div>
                
                {/* Standard Loan Information - No templates required */}
                <div className="pt-2">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-semibold">Loan Details</h3>
                    {/* Mobile Refresh Button */}
                    <div className="md:hidden">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={forceRefreshFormData}
                        disabled={isRefreshing}
                      >
                        {isRefreshing ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4 mr-1" />
                        )}
                        Refresh
                      </Button>
                    </div>
                  </div>
                  
                  {/* Display loan information in a simple grid */}
                  <div className="rounded-lg border p-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {/* Principal Amount */}
                      <div>
                        <h4 className="text-xs text-muted-foreground mb-1">Principal Amount</h4>
                        <p className="font-medium">{formatCurrency(
                            typeof loan.amount === 'string' 
                              ? parseFloat(loan.amount) 
                              : loan.amount, 
                            'INR', 
                            'en-IN'
                          )}</p>
                      </div>
                      
                      {/* Interest Rate */}
                      <div>
                        <h4 className="text-xs text-muted-foreground mb-1">Interest Rate</h4>
                        <p className="font-medium">{loan.interest_rate}% ({getInterestTypeLabel(loan.interest_type)})</p>
                      </div>

                      {/* Disbursed Amount (if available) */}
                      {loan.disbursed_amount && (
                        <div>
                          <h4 className="text-xs text-muted-foreground mb-1">Disbursed Amount</h4>
                          <p className="font-medium text-green-600">{formatCurrency(
                              typeof loan.disbursed_amount === 'string' 
                                ? parseFloat(loan.disbursed_amount) 
                                : loan.disbursed_amount, 
                              'INR', 
                              'en-IN'
                            )}</p>
                          <p className="text-xs text-muted-foreground">
                            {parseFloat(loan.amount.toString()) !== parseFloat(loan.disbursed_amount?.toString() || '0') 
                              ? "Interest deducted upfront"
                              : "Full amount disbursed"}
                          </p>
                        </div>
                      )}
                      
                      {/* Total Repayable */}
                      {loan.total_repayable && (
                        <div>
                          <h4 className="text-xs text-muted-foreground mb-1">Total Repayable</h4>
                          <p className="font-medium">{formatCurrency(
                              typeof loan.total_repayable === 'string' 
                                ? parseFloat(loan.total_repayable) 
                                : loan.total_repayable, 
                              'INR', 
                              'en-IN'
                            )}</p>
                        </div>
                      )}
                      
                      {/* Installment Amount */}
                      {loan.installment_amount && (
                        <div>
                          <h4 className="text-xs text-muted-foreground mb-1">Installment Amount</h4>
                          <p className="font-medium">{formatCurrency(
                              typeof loan.installment_amount === 'string' 
                                ? parseFloat(loan.installment_amount) 
                                : loan.installment_amount, 
                              'INR', 
                              'en-IN'
                            )}</p>
                        </div>
                      )}
                      
                      {/* Term */}
                      <div>
                        <h4 className="text-xs text-muted-foreground mb-1">Term</h4>
                        <p className="font-medium">{formatLoanTerm(loan.term, loan.payment_frequency)}</p>
                      </div>
                      
                      {/* Payment Frequency */}
                      {loan.payment_frequency && (
                        <div>
                          <h4 className="text-xs text-muted-foreground mb-1">Payment Frequency</h4>
                          <p className="font-medium capitalize">{loan.payment_frequency}</p>
                        </div>
                      )}
                      
                      {/* Dates */}
                      <div>
                        <h4 className="text-xs text-muted-foreground mb-1">Dates</h4>
                        <p className="font-medium">
                          {new Date(loan.start_date).toLocaleDateString()} - {new Date(loan.end_date).toLocaleDateString()}
                        </p>
                      </div>
                      
                      {/* Customer */}
                      {loan.customer && (
                        <div>
                          <h4 className="text-xs text-muted-foreground mb-1">Customer</h4>
                          <p className="font-medium">{loan.customer?.full_name || `Customer ${loan.customer_id}`}</p>
                        </div>
                      )}
                      
                      {/* Notes */}
                      {loan.notes && (
                        <div className="col-span-2">
                          <h4 className="text-xs text-muted-foreground mb-1">Notes</h4>
                          <p className="text-sm">{loan.notes}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Set formData for calculations section */}
                  {loan && (
                    <script dangerouslySetInnerHTML={{
                      __html: `
                        (() => {
                          // Set form data for calculators
                          window.setTimeout(() => {
                            console.log("Initializing form data in detail page:", ${JSON.stringify({
                              // Use the exact field names as defined in our database schema and form fields
                              customer_id: loan.customer_id,
                              amount: typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount),
                              interest_rate: typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate),
                              interest_type: loan.interest_type,
                              term: typeof loan.term === 'string' ? parseInt(loan.term.toString()) : loan.term,
                              // Format date fields to YYYY-MM-DD (HTML date input format)
                              start_date: typeof loan.start_date === 'string' ? loan.start_date.split('T')[0] : loan.start_date,
                              end_date: typeof loan.end_date === 'string' ? loan.end_date.split('T')[0] : loan.end_date,
                              
                              // Map field IDs for calculators to work properly
                              field_101: typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount),
                              field_100: typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate),
                              field_102: loan.interest_type,
                              field_103: typeof loan.term === 'string' ? parseInt(loan.term.toString()) : loan.term,
                              field_104: typeof loan.start_date === 'string' ? loan.start_date.split('T')[0] : loan.start_date,
                              field_105: typeof loan.end_date === 'string' ? loan.end_date.split('T')[0] : loan.end_date,
                              field_109: loan.customer_id,
                              
                              // Include any custom fields from form_submissions
                              ...(formSubmissionData && formSubmissionData[0]?.form_data ? 
                                (typeof formSubmissionData[0].form_data === 'string' ? 
                                  JSON.parse(formSubmissionData[0].form_data) : 
                                  formSubmissionData[0].form_data) : 
                                {})
                            })});
                          }, 100);
                        })();
                      `
                    }} />
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No loan information available.</p>
              </div>
            )}
          </CardContent>
        </Card>
        
        {/* Loan Calculation Components */}
        {loan && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Loan Calculations
              </CardTitle>
              <CardDescription>
                View payment information and amortization schedule
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Quick Payment Preview */}
              <div className="mb-6 border-b pb-6">
                <h3 className="text-sm font-medium mb-4">Payment Preview</h3>
                {/* Use the formData state which is initialized by our useEffect */}
                <LoanFormCalculatorInline formData={formData} />
              </div>
              
              {/* Detailed Loan Calculator Component - Prefer formData */}
              <LoanCalculator
                amount={
                  formData.amount || formData.field_101 || 
                  (typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount))
                }
                interestRate={
                  formData.interest_rate || formData.field_100 || 
                  (typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate))
                }
                termMonths={
                  formData.term_months || formData.field_103 || 
                  (typeof loan.term_months === 'string' ? parseInt(loan.term_months.toString()) : loan.term_months)
                }
                interestType={
                  (typeof formData.interest_type === 'object' ? formData.interest_type?.value : formData.interest_type) || 
                  (typeof formData.field_102 === 'object' ? formData.field_102?.value : formData.field_102) || 
                  loan.interest_type
                }
                startDate={formData.start_date || formData.field_104 || loan.start_date}
                paymentFrequency={loan.payment_frequency || 'monthly'}
                deductInterestUpfront={true}
                currencyCode="INR"
                locale="en-IN"
              />
              
              {/* Amortization Schedule Component - Prefer formData */}
              <AmortizationSchedule
                amount={
                  formData.amount || formData.field_101 || 
                  (typeof loan.amount === 'string' ? parseFloat(loan.amount) : Number(loan.amount))
                }
                interestRate={
                  formData.interest_rate || formData.field_100 || 
                  (typeof loan.interest_rate === 'string' ? parseFloat(loan.interest_rate) : Number(loan.interest_rate))
                }
                termMonths={
                  formData.term_months || formData.field_103 || 
                  (typeof loan.term_months === 'string' ? parseInt(loan.term_months.toString()) : loan.term_months)
                }
                interestType={
                  (typeof formData.interest_type === 'object' ? formData.interest_type?.value : formData.interest_type) || 
                  (typeof formData.field_102 === 'object' ? formData.field_102?.value : formData.field_102) || 
                  loan.interest_type
                }
                startDate={formData.start_date || formData.field_104 || loan.start_date}
                paymentFrequency={loan.payment_frequency || 'monthly'}
                deductInterestUpfront={true}
                currencyCode="INR"
                locale="en-IN"
              />
            </CardContent>
          </Card>
        )}
        
        {/* Payment Schedule Card */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Payment Schedules</CardTitle>
            <CardDescription>
              Manage payment schedules for this loan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PaymentScheduleTable 
              loanId={loanId} 
              companyId={companyId} 
            />
          </CardContent>
        </Card>
        
        {/* Mobile Action Bar - Fixed above bottom navigation with significantly increased padding */}
        <div className="fixed left-0 w-full bg-background border-t p-3 md:hidden shadow-lg" 
             style={{ zIndex: 9999, bottom: '90px' }}>
          <div className="flex items-center justify-between gap-3 w-full max-w-md mx-auto">
            <Button 
              onClick={handleBackToLoans} 
              variant="outline" 
              className="flex-1 h-14"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back
            </Button>
            <Button 
              onClick={() => setLocation(`/loans/edit/${loan.id}`)} 
              className="flex-1 h-14 bg-primary hover:bg-primary/90"
            >
              <Pencil className="h-5 w-5 mr-2" />
              Edit Loan
            </Button>
          </div>
        </div>
        
        {/* Spacer for mobile fixed action bar - much taller to avoid overlap with bottom navigation */}
        <div className="h-48 md:hidden"></div>
      </div>
    </div>
  );
}