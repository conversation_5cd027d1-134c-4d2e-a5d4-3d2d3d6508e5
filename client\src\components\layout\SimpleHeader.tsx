import { BranchSelector } from "@/components/branch/BranchSelector";
import UserProfileMenu from "@/components/user/UserProfileMenu";
import { useLocation } from "wouter";

interface HeaderProps {
  openMobileSidebar: () => void;
}

export default function SimpleHeader({ openMobileSidebar }: HeaderProps) {
  const [, navigate] = useLocation();

  return (
    <header className="sticky top-0 z-30 flex h-16 bg-white shadow-sm">
      <div className="flex flex-1 items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* Mobile sidebar toggle button */}
        <button
          type="button"
          className="lg:hidden p-2 rounded-md text-gray-400"
          onClick={openMobileSidebar}
        >
          <span className="sr-only">Open sidebar</span>
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        {/* Logo - visible only on mobile */}
        <div
          className="lg:hidden flex items-center cursor-pointer"
          onClick={() => navigate("/dashboard")}
        >
          <svg className="h-8 w-8 text-blue-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
              stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className="ml-2 text-xl font-bold text-blue-600">TrackFina</span>
        </div>

        {/* Branch Selector - visible on all devices */}
        <div className="flex ml-4">
          <BranchSelector showLabel={false} size="sm" />
        </div>

        {/* Right-side user profile with dropdown */}
        <div className="ml-auto flex items-center">
          <UserProfileMenu />
        </div>
      </div>
    </header>
  );
}