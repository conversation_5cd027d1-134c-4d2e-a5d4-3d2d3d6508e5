import { useState, useEffect } from "react";
import { Bell, Building, ChevronDown, Search } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CompanyWithAccess, UserData } from "@/lib/auth";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/lib/auth";
import { useLocation } from "wouter";

interface HeaderProps {
  openMobileSidebar: () => void;
}

export default function Header({ openMobileSidebar }: HeaderProps) {
  const { logout, getUserCompanies, switchCompany } = useAuth();
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [userCompanies, setUserCompanies] = useState<CompanyWithAccess[]>([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [userData, setUserData] = useState<UserData | null>(null);

  // Function to load user data from localStorage
  const loadUserData = () => {
    try {
      const storedData = localStorage.getItem("user_data");
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        console.log("User data from localStorage:", parsedData);
        setUserData(parsedData);
      }
    } catch (error) {
      console.error("Error loading user data:", error);
    }
  };

  // Load user data on component mount
  useEffect(() => {
    loadUserData();

    // Poll for changes every second
    const interval = setInterval(loadUserData, 1000);
    return () => clearInterval(interval);
  }, []);

  // Fetch user companies when userData changes
  useEffect(() => {
    const fetchCompanies = async () => {
      if (userData?.id) {
        setIsLoadingCompanies(true);
        try {
          const companies = await getUserCompanies(userData.id);
          console.log("User companies:", companies);
          setUserCompanies(companies);
        } catch (error) {
          console.error("Error fetching companies:", error);
        } finally {
          setIsLoadingCompanies(false);
        }
      }
    };

    fetchCompanies();
  }, [userData?.id, getUserCompanies]);

  // Handler for logout button
  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  // Handler for search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Searching for:", searchQuery);
  };

  // Generate user initials for the avatar
  const userInitials = userData?.full_name
    ? userData.full_name
        .split(" ")
        .map((name: string) => name[0])
        .join("")
        .substring(0, 2)
        .toUpperCase()
    : "U";

  return (
    <header className="sticky top-0 z-30 flex h-16 bg-white shadow-sm">
      <div className="flex flex-1 items-center justify-between px-4 sm:px-6 lg:px-8">
        {/* Mobile sidebar toggle button - only shown on mobile */}
        <button
          type="button"
          className="lg:hidden p-2 rounded-md text-gray-400"
          onClick={openMobileSidebar}
        >
          <span className="sr-only">Open sidebar</span>
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        {/* Search form */}
        <div className="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end">
          <form onSubmit={handleSearch} className="max-w-lg w-full relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            <Input
              id="search"
              name="search"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
              placeholder="Search..."
              type="search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </form>
        </div>

        {/* Right-side elements */}
        <div className="ml-4 flex items-center md:ml-6">
          {/* Notifications button */}
          <Button
            variant="ghost"
            size="icon"
            className="p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <span className="sr-only">View notifications</span>
            <Bell className="h-6 w-6" aria-hidden="true" />
          </Button>

          {/* User profile dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center gap-2 hover:bg-transparent ml-3"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src="" alt={userData?.full_name || "User"} />
                  <AvatarFallback className="bg-primary text-white">
                    {userInitials}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:flex flex-col">
                  <span className="text-sm font-medium text-gray-700">
                    {userData?.full_name ? userData.full_name.split(" ")[0] : "User"}
                  </span>
                  {/* Company name display */}
                  <span className="text-xs text-gray-500">
                    {userData?.company_name || "(No company name)"}
                  </span>
                </div>
                <ChevronDown className="hidden md:block h-4 w-4 text-gray-400" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                {userData?.company_name ? `${userData.company_name} - My Account` : 'My Account'}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => navigate("/profile")}
                className="cursor-pointer"
              >
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => navigate("/settings")}
                className="cursor-pointer"
              >
                Settings
              </DropdownMenuItem>

              {/* Company Switcher - only shown if user has multiple companies */}
              {userCompanies.length > 1 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger className="cursor-pointer">
                      <Building className="mr-2 h-4 w-4" />
                      <span>Switch Company</span>
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent className="p-0">
                      {isLoadingCompanies ? (
                        <DropdownMenuItem disabled>
                          Loading...
                        </DropdownMenuItem>
                      ) : (
                        userCompanies.map((company) => (
                          <DropdownMenuItem
                            key={company.id}
                            className={`cursor-pointer ${company.id === userData?.company_id ? 'bg-muted' : ''}`}
                            onClick={() => {
                              if (company.id !== userData?.company_id) {
                                switchCompany(company.id, company.name || company.company?.name || "Unknown Company");
                              }
                            }}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span>{company.name}</span>
                              {company.id === userData?.company_id && (
                                <span className="text-primary text-xs font-semibold">Current</span>
                              )}
                            </div>
                          </DropdownMenuItem>
                        ))
                      )}
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                </>
              )}

              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleLogout}
                className="cursor-pointer text-red-500 hover:text-red-600"
              >
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}