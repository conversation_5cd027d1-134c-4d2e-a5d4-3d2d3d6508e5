import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Plus,
  Search,
  Workflow,
  Play,
  Pause,
  Edit,
  Trash2,
  Copy,
  MoreVertical,
  Clock,
  Users,
  CheckCircle,
  AlertTriangle,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ApprovalWorkflowDesigner } from '@/components/workflows/ApprovalWorkflowDesigner';
import { useApprovalWorkflows, type ApprovalWorkflow } from '@/hooks/useApprovalWorkflows';
import { useAuth } from '@/lib/auth';

export default function ApprovalWorkflowsPage() {
  const [location, navigate] = useLocation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('list');
  const [searchQuery, setSearchQuery] = useState('');
  const [editingWorkflowId, setEditingWorkflowId] = useState<number | null>(null);

  const currentCompany = user?.companies?.[0];
  const companyId = currentCompany?.company_id || currentCompany?.id;

  const {
    workflows,
    workflowInstances,
    pendingApprovals,
    isLoading,
    error,
    deleteWorkflow,
    updateWorkflow,
    isDeleting,
  } = useApprovalWorkflows(companyId);

  // Filter workflows based on search
  const filteredWorkflows = workflows?.filter(workflow =>
    workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    workflow.description?.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const handleCreateWorkflow = () => {
    setEditingWorkflowId(null);
    setActiveTab('designer');
  };

  const handleEditWorkflow = (workflowId: number) => {
    setEditingWorkflowId(workflowId);
    setActiveTab('designer');
  };

  const handleDuplicateWorkflow = async (workflow: ApprovalWorkflow) => {
    // Implementation would create a copy of the workflow
    console.log('Duplicate workflow:', workflow.id);
  };

  const handleToggleWorkflow = async (workflowId: number, isActive: boolean) => {
    await updateWorkflow({
      id: workflowId,
      data: { is_active: !isActive }
    });
  };

  const handleDeleteWorkflow = async (workflowId: number) => {
    if (confirm('Are you sure you want to delete this workflow?')) {
      await deleteWorkflow(workflowId);
    }
  };

  const handleWorkflowSaved = (workflowId: number) => {
    setEditingWorkflowId(null);
    setActiveTab('list');
  };

  const handleCancelEdit = () => {
    setEditingWorkflowId(null);
    setActiveTab('list');
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading approval workflows...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load approval workflows: {error.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => navigate('/user-management')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Approval Workflows</h1>
            <p className="text-muted-foreground">
              Design and manage approval workflows for your organization
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => navigate('/user-management')}>
            Back to User Management
          </Button>
          <Button onClick={handleCreateWorkflow}>
            <Plus className="h-4 w-4 mr-2" />
            Create Workflow
          </Button>
        </div>
      </div>

      {/* Company Context */}
      {currentCompany && (
        <div className="mb-6">
          <Card>
            <CardContent className="flex items-center justify-between p-4">
              <div className="flex items-center gap-2">
                <Badge variant="outline">Current Company</Badge>
                <span className="font-medium">{currentCompany.name}</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Company ID: {companyId}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="list">Workflows</TabsTrigger>
          <TabsTrigger value="instances">Active Instances</TabsTrigger>
          <TabsTrigger value="pending">Pending Approvals</TabsTrigger>
          <TabsTrigger value="designer">Designer</TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Approval Workflows</CardTitle>
                  <CardDescription>
                    Manage your organization's approval workflows
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search workflows..."
                      className="pl-8 w-64"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Button onClick={handleCreateWorkflow}>
                    <Plus className="h-4 w-4 mr-1" />
                    Create
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {filteredWorkflows.length === 0 ? (
                <div className="text-center py-12">
                  <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {searchQuery ? 'No workflows found' : 'No workflows created'}
                  </h3>
                  <p className="text-gray-500 mb-4">
                    {searchQuery
                      ? 'Try adjusting your search terms'
                      : 'Create your first approval workflow to get started'
                    }
                  </p>
                  {!searchQuery && (
                    <Button onClick={handleCreateWorkflow}>
                      <Plus className="h-4 w-4 mr-1" />
                      Create First Workflow
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredWorkflows.map((workflow) => (
                    <Card key={workflow.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold">{workflow.name}</h3>
                              <Badge variant={workflow.is_active ? 'default' : 'secondary'}>
                                {workflow.is_active ? 'Active' : 'Inactive'}
                              </Badge>
                              <Badge variant="outline">
                                {workflow.workflow_type.replace('_', ' ')}
                              </Badge>
                            </div>
                            {workflow.description && (
                              <p className="text-sm text-muted-foreground mb-2">
                                {workflow.description}
                              </p>
                            )}
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Users className="h-4 w-4" />
                                <span>{workflow.steps?.length || 0} steps</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>{workflow.auto_escalation_hours}h escalation</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <span>Created {new Date(workflow.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleWorkflow(workflow.id, workflow.is_active)}
                            >
                              {workflow.is_active ? (
                                <Pause className="h-4 w-4" />
                              ) : (
                                <Play className="h-4 w-4" />
                              )}
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEditWorkflow(workflow.id)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDuplicateWorkflow(workflow)}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  Duplicate
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteWorkflow(workflow.id)}
                                  className="text-red-600"
                                  disabled={isDeleting}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="instances">
          <Card>
            <CardHeader>
              <CardTitle>Active Workflow Instances</CardTitle>
              <CardDescription>
                Currently running approval workflows
              </CardDescription>
            </CardHeader>
            <CardContent>
              {workflowInstances?.length === 0 ? (
                <div className="text-center py-12">
                  <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Instances</h3>
                  <p className="text-gray-500">
                    No workflow instances are currently running
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {workflowInstances?.map((instance) => (
                    <Card key={instance.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-semibold">{instance.request_type}</h3>
                            <p className="text-sm text-muted-foreground">
                              Request ID: {instance.request_id}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline">{instance.status}</Badge>
                              <Badge variant="secondary">{instance.priority}</Badge>
                            </div>
                          </div>
                          <div className="text-right text-sm text-muted-foreground">
                            <p>Step {instance.current_step_order}</p>
                            <p>Started {new Date(instance.started_at).toLocaleDateString()}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending">
          <Card>
            <CardHeader>
              <CardTitle>Pending Approvals</CardTitle>
              <CardDescription>
                Approvals waiting for your action
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pendingApprovals?.length === 0 ? (
                <div className="text-center py-12">
                  <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Pending Approvals</h3>
                  <p className="text-gray-500">
                    You have no pending approval requests
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {pendingApprovals?.map((approval) => (
                    <Card key={approval.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-semibold">{approval.stepName}</h3>
                            <p className="text-sm text-muted-foreground">
                              {approval.workflowName}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline">{approval.priority}</Badge>
                              <span className="text-sm text-muted-foreground">
                                Due in {approval.hoursRemaining}h
                              </span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                            <Button size="sm">
                              Review
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="designer">
          <ApprovalWorkflowDesigner
            workflowId={editingWorkflowId || undefined}
            companyId={companyId}
            onSave={handleWorkflowSaved}
            onCancel={handleCancelEdit}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
