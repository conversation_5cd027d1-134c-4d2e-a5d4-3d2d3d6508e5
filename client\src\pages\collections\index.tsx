import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useToast } from "@/hooks/use-toast";
import { formatCurrency, formatDate, getStatusBadgeVariant, getInitials } from "@/lib/utils";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { PaymentReceiptDialog } from "@/components/receipt/PaymentReceiptDialog";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import {
  AlertCircle,
  Calendar,
  ClipboardCheck,
  Clock,
  CreditCard,
  FileCheck,
  Filter,
  Grid3x3,
  List,
  Loader2,
  Plus,
  Receipt,
  Search,
  Table as TableIcon,
  UserCheck
} from "lucide-react";

// Helper function to get the appropriate status icon
// Helper function to capitalize status
const capitalizeStatus = (status?: string) => {
  if (!status) return '';
  return status.charAt(0).toUpperCase() + status.slice(1);
};

const getStatusIcon = (status: string) => {
  const statusLower = status.toLowerCase();

  if (statusLower === "completed") {
    return <FileCheck className="h-3 w-3 mr-1 text-green-500" />;
  } else if (statusLower === "pending") {
    return <Clock className="h-3 w-3 mr-1 text-amber-500" />;
  } else if (statusLower === "overdue") {
    return <AlertCircle className="h-3 w-3 mr-1 text-destructive" />;
  } else {
    return <Clock className="h-3 w-3 mr-1 text-muted-foreground" />;
  }
};

// Interfaces and types
interface Collection {
  id: number;
  loan_id: number;
  customer_id: number;
  company_id: number;
  agent_id: number | null;
  amount: number | string;
  scheduled_date: string;
  collection_date: string | null;
  status: string;
  payment_method: string | null;
  receipt_id: string | null;
  notes: string | null;
  fine_amount?: number | null;
  company_collection_string?: string | null;
  created_at: string;
  updated_at: string;
  customer?: Customer;
  loan?: Loan;
}

interface Customer {
  id: number;
  full_name: string;
  company_id: number;
  mobile?: string;
  email?: string;
  address?: string;
}

interface Loan {
  id: number;
  customer_id: number;
  company_id: number;
  amount: number;
  interest_rate: number;
  term: number;
  loan_reference_code?: string | null;
}

interface Agent {
  id: number;
  user_id: number;
  company_id: number;
  full_name: string;
}

// Form schemas
const collectionFormSchema = z.object({
  company_id: z.number(),
  loan_id: z.number().int().positive({ message: "Please select a valid loan" }),
  customer_id: z.number().int().positive({ message: "Please select a customer" }),
  agent_id: z.number().nullable(),
  amount: z.string().min(1, "Amount is required"),
  scheduled_date: z.string().min(1, "Scheduled date is required"),
  status: z.string().min(1, "Status is required"),
  payment_method: z.string().nullable(),
  receipt_id: z.string().nullable(),
  notes: z.string().nullable(),
});

const completeCollectionSchema = z.object({
  company_id: z.number(),
  status: z.string(),
  payment_method: z.string().min(1, "Payment method is required"),
  fine_amount: z.string().optional(),
  notes: z.string().nullable(),
});

type CollectionFormValues = z.infer<typeof collectionFormSchema>;
type CompleteCollectionFormValues = z.infer<typeof completeCollectionSchema>;

export default function Collections() {
  const { getCurrentUser } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const user = getCurrentUser();
  const companyId = user?.company_id || 1; // Default to 1 for SaaS admin if no company_id

  // State for UI control
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [timeFilter, setTimeFilter] = useState("all"); // Default to "all" to show all collections
  const [viewMode, setViewMode] = useState<"grid" | "table">("grid");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isCompleteDialogOpen, setIsCompleteDialogOpen] = useState(false);
  const [isReceiptDialogOpen, setIsReceiptDialogOpen] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [showAll, setShowAll] = useState(false);

  // Define the paginated response type
  interface PaginatedCollectionsResponse {
    collections: Collection[];
    pagination: {
      page: number;
      limit: number;
      totalCount: number;
      totalPages: number;
    };
  }

  // Fetch customers first
  const { data: customers = [] } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${companyId}/customers`],
    enabled: !!companyId,
  });

  // Data fetching with pagination and search
  const { data: collectionsResponse, isLoading } = useQuery<{ collections: Collection[], pagination: any }>({
    queryKey: [`/api/companies/${companyId}/collections`, statusFilter],
    enabled: !!companyId,
    retry: 1, // Only retry once to avoid too many failed requests
    queryFn: async () => {
      // Add a random query parameter to prevent caching
      const timestamp = Date.now();
      console.log(`Fetching collections for company ${companyId} with status filter ${statusFilter} at ${new Date().toISOString()}`);

      // Build the URL with appropriate query parameters
      let url = `/api/companies/${companyId}/collections?_nocache=${timestamp}`;

      // Add status filter if not "all"
      if (statusFilter !== "all") {
        url += `&status=${statusFilter}`;
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch collections');
      }
      const data = await response.json();
      console.log(`Received ${data.collections?.length || 0} collections from API`);
      // Log the first few collections to help with debugging
      if (data.collections && data.collections.length > 0) {
        console.log('Sample collections:', data.collections.slice(0, 3));
      }
      return data;
    }
  });

  // Extract collections array from the response
  const collectionsData = collectionsResponse?.collections || [];

  // Helper functions for date checking
  const isToday = (dateString: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day

    const date = new Date(dateString);
    date.setHours(0, 0, 0, 0); // Set to beginning of day

    return date.getTime() === today.getTime();
  };

  const isThisWeek = (dateString: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const date = new Date(dateString);
    date.setHours(0, 0, 0, 0);

    // Calculate the start of the week (Sunday)
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());

    // Calculate the end of the week (Saturday)
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return date >= startOfWeek && date <= endOfWeek;
  };

  const isThisMonth = (dateString: string) => {
    const today = new Date();
    const date = new Date(dateString);

    return date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  const isNext30Days = (dateString: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const date = new Date(dateString);
    date.setHours(0, 0, 0, 0);

    const thirtyDaysFromNow = new Date(today);
    thirtyDaysFromNow.setDate(today.getDate() + 30);

    return date >= today && date <= thirtyDaysFromNow;
  };

  const isOverdue = (dateString: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const date = new Date(dateString);
    date.setHours(0, 0, 0, 0);

    return date < today;
  };

  // Client-side pagination and filtering
  const filteredCollections = collectionsData ? collectionsData.filter((collection: Collection) => {
    // First find the customer for this collection
    const customer = customers.find(c => c.id === collection.customer_id);

    const matchesSearch = searchQuery === '' || (
      // Search by collection properties
      (collection.receipt_id?.toLowerCase().includes(searchQuery.toLowerCase()) || false) ||
      collection.id.toString().includes(searchQuery) ||
      // Search by loan ID
      collection.loan_id.toString().includes(searchQuery) ||
      // Search by customer properties (if customer exists)
      (customer && (
        customer.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (customer.mobile && customer.mobile.includes(searchQuery)) ||
        (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase()))
      ))
    );

    // Check if collection matches the selected status filter
    const matchesStatus = statusFilter === "all" ||
                          collection.status.toLowerCase() === statusFilter.toLowerCase();

    // Check if collection matches the selected time filter
    let matchesTimeFilter = false;
    switch (timeFilter) {
      case "today":
        matchesTimeFilter = isToday(collection.scheduled_date);
        break;
      case "week":
        matchesTimeFilter = isThisWeek(collection.scheduled_date);
        break;
      case "month":
        matchesTimeFilter = isThisMonth(collection.scheduled_date);
        break;
      case "next30":
        matchesTimeFilter = isNext30Days(collection.scheduled_date);
        break;
      case "overdue":
        matchesTimeFilter = isOverdue(collection.scheduled_date) &&
                           collection.status.toLowerCase() !== "completed";
        break;
      case "all":
        matchesTimeFilter = true;
        break;
      default:
        matchesTimeFilter = true;
    }

    return matchesSearch && matchesStatus && matchesTimeFilter;
  }) : [];

  // Client-side pagination
  const paginatedCollections = showAll
    ? filteredCollections
    : filteredCollections.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
      );

  // Calculate pagination metadata
  const totalCount = filteredCollections.length;
  const totalPages = showAll ? 1 : Math.ceil(totalCount / itemsPerPage);

  // Create a structure similar to the server-side pagination response
  const paginatedData = {
    collections: paginatedCollections,
    pagination: {
      page: showAll ? 1 : currentPage,
      limit: showAll ? totalCount : itemsPerPage,
      totalCount,
      totalPages
    }
  };

  const { data: agents = [] } = useQuery<Agent[]>({
    queryKey: [`/api/companies/${companyId}/agents`],
    enabled: !!companyId,
  });

  // Fetch loans for the company
  const { data: loans = [] } = useQuery<Loan[]>({
    queryKey: [`/api/companies/${companyId}/loans`],
    enabled: !!companyId,
  });

  // Collection form will be defined below, we'll use customer ID from it later

  // Extract collections and pagination data from the response
  console.log("Raw paginatedData:", paginatedData);
  console.log("Filtered collections:", filteredCollections.length);

  const collections = paginatedData.collections;
  const pagination = paginatedData.pagination;

  console.log("Paginated collections:", collections.length);
  console.log("Pagination:", pagination);

  // Collection form
  const collectionForm = useForm<CollectionFormValues>({
    resolver: zodResolver(collectionFormSchema),
    defaultValues: {
      company_id: companyId,
      loan_id: 0,
      customer_id: 0,
      agent_id: null,
      amount: "",
      scheduled_date: new Date().toISOString().split('T')[0],
      status: "pending",
      payment_method: null,
      receipt_id: null,
      notes: null,
    },
  });

  // Complete collection form
  const completeForm = useForm<CompleteCollectionFormValues>({
    resolver: zodResolver(completeCollectionSchema),
    defaultValues: {
      company_id: companyId,
      status: "completed",
      payment_method: "",
      fine_amount: "0",
      notes: null,
    },
  });

  // Edit collection form
  const editForm = useForm<CollectionFormValues>({
    resolver: zodResolver(collectionFormSchema),
    defaultValues: {
      company_id: companyId,
      loan_id: 0,
      customer_id: 0,
      agent_id: null,
      amount: "",
      scheduled_date: "",
      status: "pending",
      payment_method: null,
      receipt_id: null,
      notes: null,
    },
  });

  // Track selected customer for loan fetching
  const selectedCustomerId = collectionForm.watch("customer_id");

  // Fetch loans for the selected customer
  const { data: customerLoans = [] } = useQuery<Loan[]>({
    queryKey: [`/api/customers/${selectedCustomerId}/loans`],
    enabled: !!selectedCustomerId && selectedCustomerId > 0,
  });

  // Mutations
  const createCollectionMutation = useMutation({
    mutationFn: async (data: CollectionFormValues) => {
      // Important: The server expects amount as string and scheduled_date as a Date
      const parsedData = {
        ...data,
        // Keep amount as string
        scheduled_date: new Date(data.scheduled_date),
      };
      console.log("Sending collection data:", parsedData);
      const res = await apiRequest('POST', '/api/collections', parsedData);
      return res.json();
    },
    onSuccess: () => {
      // Invalidate all collection queries
      queryClient.invalidateQueries({
        queryKey: [`/api/companies/${companyId}/collections`],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/recent-collections`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/dashboard-metrics`] });
      setIsAddDialogOpen(false);
      collectionForm.reset();
      // Reset to first page to see the new collection
      setCurrentPage(1);
      setShowAll(false);
      toast({
        title: "Collection created",
        description: "The collection has been scheduled successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create collection",
        variant: "destructive",
      });
    },
  });

  const updateCollectionMutation = useMutation({
    mutationFn: async (data: CollectionFormValues & { id: number }) => {
      const { id, ...restData } = data;
      const parsedData = {
        ...restData,
        // Keep amount as string, convert date to Date
        scheduled_date: new Date(restData.scheduled_date),
      };
      console.log("Updating collection data:", parsedData);
      const res = await apiRequest('PATCH', `/api/collections/${id}`, parsedData);
      return res.json();
    },
    onSuccess: () => {
      // Invalidate all collection queries
      queryClient.invalidateQueries({
        queryKey: [`/api/companies/${companyId}/collections`],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/recent-collections`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/dashboard-metrics`] });
      setIsEditDialogOpen(false);
      editForm.reset();
      toast({
        title: "Collection updated",
        description: "The collection has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update collection",
        variant: "destructive",
      });
    },
  });

  const completeCollectionMutation = useMutation({
    mutationFn: async (data: CompleteCollectionFormValues & { id: number }) => {
      const { id, ...restData } = data;
      const res = await apiRequest('PATCH', `/api/collections/${id}/status`, restData);
      return res.json();
    },
    onSuccess: () => {
      // Invalidate all collection queries
      queryClient.invalidateQueries({
        queryKey: [`/api/companies/${companyId}/collections`],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/recent-collections`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/dashboard-metrics`] });
      setIsCompleteDialogOpen(false);
      completeForm.reset();

      // Show success toast
      toast({
        title: "Collection completed",
        description: "The collection has been marked as completed. A receipt has been generated.",
      });

      // Show receipt dialog
      if (selectedCollection) {
        setIsReceiptDialogOpen(true);
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to complete collection",
        variant: "destructive",
      });
    },
  });

  const deleteCollectionMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest('DELETE', `/api/collections/${id}?companyId=${companyId}`);
      return res.json();
    },
    onSuccess: (data, deletedId) => {
      // Use more specific query invalidation to prevent race conditions
      queryClient.setQueryData(
        [`/api/companies/${companyId}/collections`, statusFilter],
        (oldData: any) => {
          if (!oldData?.collections) return oldData;
          return {
            ...oldData,
            collections: oldData.collections.filter((c: Collection) => c.id !== deletedId)
          };
        }
      );

      // Invalidate related queries but not the main collections query to prevent refetch
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/recent-collections`] });
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/dashboard-metrics`] });

      toast({
        title: "Collection deleted",
        description: "The collection has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete collection",
        variant: "destructive",
      });
    },
  });

  // Form handlers
  const onSubmitAdd = (data: CollectionFormValues) => {
    createCollectionMutation.mutate(data);
  };

  const onSubmitEdit = (data: CollectionFormValues) => {
    if (selectedCollection) {
      updateCollectionMutation.mutate({ ...data, id: selectedCollection.id });
    }
  };

  const onSubmitComplete = (data: CompleteCollectionFormValues) => {
    if (selectedCollection) {
      completeCollectionMutation.mutate({ ...data, id: selectedCollection.id });
    }
  };

  // Helper functions
  const handleOpenEditDialog = (collection: Collection) => {
    setSelectedCollection(collection);
    editForm.reset({
      company_id: collection.company_id,
      loan_id: collection.loan_id,
      customer_id: collection.customer_id,
      agent_id: collection.agent_id,
      amount: collection.amount.toString(),
      scheduled_date: new Date(collection.scheduled_date).toISOString().split('T')[0],
      status: collection.status,
      payment_method: collection.payment_method,
      receipt_id: collection.receipt_id,
      notes: collection.notes,
    });
    setIsEditDialogOpen(true);
  };

  // Function to check if a collection can be completed
  const canCompleteCollection = (collection: Collection): { canComplete: boolean; reason?: string } => {
    // If collection is already completed, it can't be completed again
    if (collection.status === 'completed') {
      return { canComplete: false, reason: 'Collection is already completed' };
    }

    // Get all collections for the same loan
    const loanCollections = collectionsData?.filter(c => c.loan_id === collection.loan_id) || [];

    // Sort by EMI number if available, otherwise allow completion (backward compatibility)
    if (!collection.emi_number) {
      return { canComplete: true };
    }

    // Check if there are any incomplete collections with lower EMI numbers
    const incompleteEarlierCollections = loanCollections.filter(c =>
      c.emi_number &&
      c.emi_number < collection.emi_number! &&
      c.status !== 'completed'
    );

    if (incompleteEarlierCollections.length > 0) {
      // Find the earliest incomplete collection
      const earliestIncomplete = incompleteEarlierCollections.reduce((earliest, current) =>
        (current.emi_number! < earliest.emi_number!) ? current : earliest
      );

      return {
        canComplete: false,
        reason: `Please complete Collection ${earliestIncomplete.emi_number} before completing this one.`
      };
    }

    return { canComplete: true };
  };

  const handleOpenCompleteDialog = (collection: Collection) => {
    // Check if collection can be completed
    const { canComplete, reason } = canCompleteCollection(collection);

    if (!canComplete) {
      toast({
        title: "Cannot Complete Collection",
        description: reason || "This collection cannot be completed at this time.",
        variant: "destructive",
      });
      return;
    }

    setSelectedCollection(collection);
    completeForm.reset({
      company_id: collection.company_id,
      status: "completed",
      payment_method: "",
      fine_amount: "0",
      notes: collection.notes,
    });
    setIsCompleteDialogOpen(true);
  };

  const handleDeleteCollection = (id: number) => {
    if (window.confirm("Are you sure you want to delete this collection?")) {
      deleteCollectionMutation.mutate(id);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Reset to first page when searching
    setCurrentPage(1);
    setShowAll(false);
    // The search is handled by the client-side filtering
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setItemsPerPage(newPageSize);
    setCurrentPage(1);
    setShowAll(false);
  };

  const handleShowAll = () => {
    setShowAll(true);
    setCurrentPage(1);
  };

  const handleShowPaginated = () => {
    setShowAll(false);
    setCurrentPage(1);
  };

  // UI components and rendering
  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Collections</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage loan collections and payment schedules
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
          >
            <Filter size={16} />
            <span className="hidden sm:inline">Filter</span>
          </Button>

          <div className="hidden sm:flex border rounded-md overflow-hidden">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              className="rounded-none border-0"
              onClick={() => setViewMode("grid")}
            >
              <Grid3x3 size={16} className="mr-1" />
              <span className="sr-only sm:not-sr-only">Grid</span>
            </Button>
            <Button
              variant={viewMode === "table" ? "default" : "ghost"}
              size="sm"
              className="rounded-none border-0"
              onClick={() => setViewMode("table")}
            >
              <TableIcon size={16} className="mr-1" />
              <span className="sr-only sm:not-sr-only">Table</span>
            </Button>
          </div>

          <Button
            onClick={() => navigate("/collections/quick-payment")}
            size="sm"
            className="flex items-center gap-1"
          >
            <CreditCard size={16} />
            <span className="hidden sm:inline">Quick Payment</span>
            <span className="inline sm:hidden">Pay</span>
          </Button>

          {/* Add Collection Sheet */}
          <Sheet open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
              <SheetHeader className="mb-5">
                <SheetTitle>Schedule New Collection</SheetTitle>
                <SheetDescription>
                  Create a new collection schedule for a customer.
                </SheetDescription>
              </SheetHeader>
              <Form {...collectionForm}>
                <form onSubmit={collectionForm.handleSubmit(onSubmitAdd)} className="space-y-4">
                  {/* Customer Selection */}
                  <FormField
                    control={collectionForm.control}
                    name="customer_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer*</FormLabel>
                        <Select
                          value={field.value ? field.value.toString() : ""}
                          onValueChange={(value) => field.onChange(parseInt(value, 10))}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select customer" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {customers.map((customer) => (
                              <SelectItem key={customer.id} value={customer.id.toString()}>
                                {customer.full_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Loan Selection */}
                  <FormField
                    control={collectionForm.control}
                    name="loan_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Loan*</FormLabel>
                        <Select
                          value={field.value ? field.value.toString() : ""}
                          onValueChange={(value) => field.onChange(parseInt(value, 10))}
                          disabled={!selectedCustomerId || customerLoans.length === 0}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={
                                selectedCustomerId && customerLoans.length === 0
                                  ? "No loans available for this customer"
                                  : "Select a loan"
                              } />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {customerLoans.length > 0 ? (
                              customerLoans.map((loan) => (
                                <SelectItem key={loan.id} value={loan.id.toString()}>
                                  ₹{parseFloat(loan.amount.toString()).toLocaleString()} (ID: {loan.id})
                                </SelectItem>
                              ))
                            ) : (
                              <div className="px-2 py-4 text-center">
                                <AlertCircle className="h-6 w-6 text-amber-500 mx-auto mb-2" />
                                <p className="text-sm font-medium">No loans found for this customer</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  Please create a loan for this customer first
                                </p>
                              </div>
                            )}
                          </SelectContent>
                        </Select>
                        {selectedCustomerId && customerLoans.length === 0 && (
                          <div className="mt-2">
                            <div className="flex items-center gap-2 text-amber-600 mb-2">
                              <AlertCircle className="h-4 w-4" />
                              <FormDescription className="text-amber-600 m-0">
                                You need to create a loan for this customer before adding a collection
                              </FormDescription>
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="w-full"
                              onClick={() => {
                                setIsAddDialogOpen(false);
                                // Navigate to loans page
                                window.location.href = '/loans';
                              }}
                            >
                              Go to Loans Page
                            </Button>
                          </div>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Agent Selection */}
                  <FormField
                    control={collectionForm.control}
                    name="agent_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Agent (Optional)</FormLabel>
                        <Select
                          value={field.value ? field.value.toString() : "null"}
                          onValueChange={(value) =>
                            field.onChange(value === "null" ? null : parseInt(value, 10))
                          }
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select agent" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="null">None</SelectItem>
                            {agents.map((agent) => (
                              <SelectItem key={agent.id} value={agent.id.toString()}>
                                {agent.full_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Amount */}
                  <FormField
                    control={collectionForm.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount (₹)*</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="E.g., 5000"
                            {...field}
                            type="number"
                            step="0.01"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Scheduled Date */}
                  <FormField
                    control={collectionForm.control}
                    name="scheduled_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Scheduled Date*</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Status */}
                  <FormField
                    control={collectionForm.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status*</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                            <SelectItem value="overdue">Overdue</SelectItem>
                            <SelectItem value="cancelled">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Notes */}
                  <FormField
                    control={collectionForm.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Additional details about this collection"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <SheetFooter className="pt-4">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => setIsAddDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={createCollectionMutation.isPending}>
                      {createCollectionMutation.isPending && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Schedule Collection
                    </Button>
                  </SheetFooter>
                </form>
              </Form>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col gap-4">
            <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search by customer name, mobile, loan or collection ID..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button type="submit" className="flex-1 sm:flex-none">Search</Button>
                <div className="w-full sm:w-48">
                  <Select
                    value={statusFilter}
                    onValueChange={(value) => {
                      setStatusFilter(value);
                      setCurrentPage(1);
                      setShowAll(false);
                      // Invalidate the query to refetch with new status filter
                      queryClient.invalidateQueries({
                        queryKey: [`/api/companies/${companyId}/collections`],
                        refetchType: 'all'
                      });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Time filter buttons */}
                <div className="flex gap-1 flex-wrap">
                  <Button
                    variant={timeFilter === "all" ? "default" : "outline"}
                    onClick={() => {
                      setTimeFilter("all");
                      setCurrentPage(1);
                      setShowAll(false);
                    }}
                    size="sm"
                    className="text-xs"
                  >
                    All Time
                  </Button>
                  <Button
                    variant={timeFilter === "today" ? "default" : "outline"}
                    onClick={() => {
                      setTimeFilter("today");
                      setCurrentPage(1);
                      setShowAll(false);
                    }}
                    size="sm"
                    className="text-xs"
                  >
                    Today
                  </Button>
                  <Button
                    variant={timeFilter === "week" ? "default" : "outline"}
                    onClick={() => {
                      setTimeFilter("week");
                      setCurrentPage(1);
                      setShowAll(false);
                    }}
                    size="sm"
                    className="text-xs"
                  >
                    This Week
                  </Button>
                  <Button
                    variant={timeFilter === "month" ? "default" : "outline"}
                    onClick={() => {
                      setTimeFilter("month");
                      setCurrentPage(1);
                      setShowAll(false);
                    }}
                    size="sm"
                    className="text-xs"
                  >
                    This Month
                  </Button>
                  <Button
                    variant={timeFilter === "next30" ? "default" : "outline"}
                    onClick={() => {
                      setTimeFilter("next30");
                      setCurrentPage(1);
                      setShowAll(false);
                    }}
                    size="sm"
                    className="text-xs"
                  >
                    Next 30 Days
                  </Button>
                  <Button
                    variant={timeFilter === "overdue" ? "default" : "outline"}
                    onClick={() => {
                      setTimeFilter("overdue");
                      setCurrentPage(1);
                      setShowAll(false);
                    }}
                    size="sm"
                    className="text-xs text-destructive border-destructive"
                  >
                    Overdue
                  </Button>
                </div>
              </div>
            </form>

            {/* Advanced Search Panel */}
            {showAdvancedSearch && (
              <div className="border rounded-md p-4 bg-muted/20 mt-4">
                <h3 className="text-sm font-medium mb-3">Advanced Search</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="customerSearch" className="text-xs text-gray-500 mb-1 block">
                      Customer Name
                    </label>
                    <Input
                      id="customerSearch"
                      placeholder="Search by customer name"
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div>
                    <label htmlFor="mobileSearch" className="text-xs text-gray-500 mb-1 block">
                      Mobile Number
                    </label>
                    <Input
                      id="mobileSearch"
                      placeholder="Search by mobile number"
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div>
                    <label htmlFor="loanSearch" className="text-xs text-gray-500 mb-1 block">
                      Loan ID
                    </label>
                    <Input
                      id="loanSearch"
                      placeholder="Search by loan ID"
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Collections Management */}
      <Card>
        <CardHeader>
          <CardTitle>Collections Management</CardTitle>
          <CardDescription>
            View and manage all collection transactions
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0 md:p-6">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <div>
              <div className="flex flex-col gap-4 mb-6">
                {/* Status filters */}
                <div className="flex justify-center">
                  <div className="grid w-full max-w-md grid-cols-4 gap-2">
                    <Button
                      variant={statusFilter === "all" ? "default" : "outline"}
                      onClick={() => {
                        setStatusFilter("all");
                        setCurrentPage(1);
                        setShowAll(false);
                        // Invalidate the query to refetch with new status filter
                        queryClient.invalidateQueries({
                          queryKey: [`/api/companies/${companyId}/collections`],
                          refetchType: 'all'
                        });
                      }}
                      className="w-full"
                    >
                      All Status
                    </Button>
                    <Button
                      variant={statusFilter === "pending" ? "default" : "outline"}
                      onClick={() => {
                        setStatusFilter("pending");
                        setCurrentPage(1);
                        setShowAll(false);
                        // Invalidate the query to refetch with new status filter
                        queryClient.invalidateQueries({
                          queryKey: [`/api/companies/${companyId}/collections`],
                          refetchType: 'all'
                        });
                      }}
                      className="w-full"
                    >
                      Pending
                    </Button>
                    <Button
                      variant={statusFilter === "completed" ? "default" : "outline"}
                      onClick={() => {
                        setStatusFilter("completed");
                        setCurrentPage(1);
                        setShowAll(false);
                        // Invalidate the query to refetch with new status filter
                        queryClient.invalidateQueries({
                          queryKey: [`/api/companies/${companyId}/collections`],
                          refetchType: 'all'
                        });
                      }}
                      className="w-full"
                    >
                      Completed
                    </Button>
                    <Button
                      variant={statusFilter === "overdue" ? "default" : "outline"}
                      onClick={() => {
                        setStatusFilter("overdue");
                        setCurrentPage(1);
                        setShowAll(false);
                        // Invalidate the query to refetch with new status filter
                        queryClient.invalidateQueries({
                          queryKey: [`/api/companies/${companyId}/collections`],
                          refetchType: 'all'
                        });
                      }}
                      className="w-full text-destructive border-destructive"
                    >
                      Overdue
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-4 mt-4">
                {collections.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {(() => {
                      // Generate appropriate message based on filters
                      if (statusFilter !== "all") {
                        return `No collections found with "${statusFilter}" status. Try a different filter or schedule a new collection.`;
                      }

                      switch (timeFilter) {
                        case "today":
                          return "No collections scheduled for today. Try a different time period or schedule a new collection.";
                        case "week":
                          return "No collections scheduled for this week. Try a different time period or schedule a new collection.";
                        case "month":
                          return "No collections scheduled for this month. Try a different time period or schedule a new collection.";
                        case "next30":
                          return "No collections scheduled for the next 30 days. Try a different time period or schedule a new collection.";
                        case "overdue":
                          return "No overdue collections found. All your collections are on schedule!";
                        case "all":
                          return "No collections found in the system. Try creating a new loan or collection.";
                        default:
                          return "No collections found with the current filters. Try different filters or schedule a new collection.";
                      }
                    })()}
                  </div>
                ) : viewMode === "grid" ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 p-3 md:p-0">
                    {collections.map((collection: Collection) => (
                      <Card key={collection.id} className="overflow-hidden h-full flex flex-col shadow-sm">
                        <CardContent className="p-0 flex-1 flex flex-col">
                          <div className="p-3 flex-1">
                            <div className="flex justify-between items-start mb-1">
                              <div className="flex flex-col">
                                <div className="flex items-center gap-1">
                                  <span className="text-xs text-muted-foreground">ID: {collection.company_collection_string || `#${collection.id}`}</span>
                                  {collection.receipt_id && (
                                    <span className="text-xs text-muted-foreground">| Receipt: {collection.receipt_id}</span>
                                  )}
                                </div>

                                {/* Display customer info */}                                {(customers.find(c => c.id === collection.customer_id)) && (
                                  <div className="text-xs text-primary font-medium">
                                    {customers.find(c => c.id === collection.customer_id)?.full_name}
                                  </div>
                                )}                                <div className="text-xs text-muted-foreground">
                                  Loan: {collection.loan?.loan_reference_code || `#${collection.loan_id}`}
                                </div>
                              </div>
                              <Badge variant={getStatusBadgeVariant(collection.status)} className="text-xs px-2 py-0">
                                <span className="flex items-center">
                                  {getStatusIcon(collection.status)}
                                  {capitalizeStatus(collection.status)}
                                </span>
                              </Badge>
                            </div>

                            <div className="flex justify-between items-center mt-2 mb-2">
                              <div>
                                <div className="text-xs text-muted-foreground">Amount</div>
                                <div className="text-base font-bold">
                                  {formatCurrency(
                                    typeof collection.amount === 'string'
                                      ? parseFloat(collection.amount)
                                      : collection.amount
                                  )}
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="text-xs text-muted-foreground">Scheduled</div>
                                <div className="flex items-center justify-end text-xs">
                                  <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                                  {formatDate(collection.scheduled_date)}
                                </div>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
                              <div>
                                <div className="text-muted-foreground">Collection Date</div>
                                <div className="font-medium">
                                  {collection.collection_date
                                    ? formatDate(collection.collection_date)
                                    : "Not collected"}
                                </div>
                              </div>

                              <div>
                                <div className="text-muted-foreground">Payment Method</div>
                                <div className="font-medium">
                                  {collection.payment_method
                                    ? collection.payment_method.replace("_", " ").toUpperCase()
                                    : "Not specified"}
                                </div>
                              </div>
                            </div>

                            {/* View Receipt button - only for completed collections */}
                            {collection.status === 'completed' && collection.receipt_id && (
                              <div className="mt-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full flex items-center justify-center text-green-600 h-7 text-xs"
                                  onClick={() => {
                                    setSelectedCollection(collection);
                                    setIsReceiptDialogOpen(true);
                                  }}
                                >
                                  <Receipt className="h-3 w-3 mr-1" />
                                  View Receipt
                                </Button>
                              </div>
                            )}

                            {/* Notes section with smaller min-height */}
                            <div className="mt-2 min-h-[30px]">
                              <div className="text-xs text-muted-foreground">Notes</div>
                              <div className="text-xs truncate">
                                {collection.notes || "Auto-generated for payment"}
                              </div>
                            </div>
                          </div>

                          <div className="mt-auto">
                            {/* For pending cards, show Complete, Edit, Delete buttons */}
                            {collection.status === 'pending' ? (
                              <div className="flex border-t divide-x">
                                {(() => {
                                  const { canComplete, reason } = canCompleteCollection(collection);
                                  return (
                                    <Button
                                      variant="ghost"
                                      className={`flex-1 rounded-none h-7 text-xs px-1 ${
                                        canComplete
                                          ? "text-green-600 hover:bg-green-50"
                                          : "text-gray-400 cursor-not-allowed opacity-50"
                                      }`}
                                      onClick={() => canComplete && handleOpenCompleteDialog(collection)}
                                      disabled={!canComplete}
                                      title={canComplete ? "Complete collection" : reason}
                                    >
                                      <ClipboardCheck className="h-3 w-3 mr-1" />
                                      <span className="truncate">Complete</span>
                                    </Button>
                                  );
                                })()}
                                <Button
                                  variant="ghost"
                                  className="flex-1 rounded-none h-7 text-xs px-1"
                                  onClick={() => handleOpenEditDialog(collection)}
                                >
                                  <svg className="h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/></svg>
                                  <span className="truncate">Edit</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  className="flex-1 rounded-none h-7 text-red-600 text-xs px-1"
                                  onClick={() => handleDeleteCollection(collection.id)}
                                >
                                  <svg className="h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                                  <span className="truncate">Delete</span>
                                </Button>
                              </div>
                            ) : (
                              /* For completed cards, only show Edit and Delete buttons */
                              <div className="flex border-t divide-x">
                                <Button
                                  variant="ghost"
                                  className="flex-1 rounded-none h-7 text-xs px-1"
                                  onClick={() => handleOpenEditDialog(collection)}
                                >
                                  <svg className="h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/></svg>
                                  <span className="truncate">Edit</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  className="flex-1 rounded-none h-7 text-red-600 text-xs px-1"
                                  onClick={() => handleDeleteCollection(collection.id)}
                                >
                                  <svg className="h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                                  <span className="truncate">Delete</span>
                                </Button>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="whitespace-nowrap">ID</TableHead>
                          <TableHead className="whitespace-nowrap">Customer</TableHead>
                          <TableHead className="whitespace-nowrap">Loan Reference</TableHead>
                          <TableHead className="whitespace-nowrap">Amount</TableHead>
                          <TableHead className="whitespace-nowrap">Scheduled Date</TableHead>
                          <TableHead className="whitespace-nowrap">Collection Date</TableHead>
                          <TableHead className="whitespace-nowrap">Status</TableHead>
                          <TableHead className="whitespace-nowrap">Payment Method</TableHead>
                          <TableHead className="whitespace-nowrap">Notes</TableHead>
                          <TableHead className="whitespace-nowrap text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {collections.map((collection: Collection) => (
                          <TableRow key={collection.id}>
                            <TableCell>
                              <div className="font-medium">{collection.company_collection_string || `#${collection.id}`}</div>
                              <div className="text-sm text-muted-foreground">
                                {collection.receipt_id || "No receipt"}
                              </div>
                            </TableCell>
                            <TableCell>
                              {customers.find(c => c.id === collection.customer_id)?.full_name ||
                                <span className="text-muted-foreground">Unknown</span>}
                            </TableCell>
                            <TableCell>
                              {collection.loan?.loan_reference_code || `#${collection.loan_id}`}
                            </TableCell>
                            <TableCell>
                              {formatCurrency(
                                typeof collection.amount === 'string'
                                  ? parseFloat(collection.amount)
                                  : collection.amount
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                                {formatDate(collection.scheduled_date)}
                              </div>
                            </TableCell>
                            <TableCell>
                              {collection.collection_date
                                ? formatDate(collection.collection_date)
                                : "Not collected"}
                            </TableCell>
                            <TableCell>
                              <Badge variant={getStatusBadgeVariant(collection.status)}>
                                <span className="flex items-center">
                                  {getStatusIcon(collection.status)}
                                  {capitalizeStatus(collection.status)}
                                </span>
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {collection.payment_method
                                ? collection.payment_method.replace("_", " ").toUpperCase()
                                : "N/A"}
                            </TableCell>
                            <TableCell className="whitespace-nowrap max-w-[200px] truncate">
                              {collection.notes || "—"}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                {collection.status === "pending" && (() => {
                                  const { canComplete, reason } = canCompleteCollection(collection);
                                  return (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => canComplete && handleOpenCompleteDialog(collection)}
                                      disabled={!canComplete}
                                      className={canComplete ? "" : "opacity-50 cursor-not-allowed"}
                                      title={canComplete ? "Complete collection" : reason}
                                    >
                                      Complete
                                    </Button>
                                  );
                                })()}
                                {collection.status === "completed" && collection.receipt_id && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-green-600"
                                    onClick={() => {
                                      setSelectedCollection(collection);
                                      setIsReceiptDialogOpen(true);
                                    }}
                                  >
                                    <Receipt className="h-4 w-4 mr-1" />
                                    Receipt
                                  </Button>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleOpenEditDialog(collection)}
                                >
                                  Edit
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-500 hover:bg-red-50"
                                  onClick={() => handleDeleteCollection(collection.id)}
                                >
                                  Delete
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>

              {/* Page Size Controls and Show All */}
              {pagination.totalCount > 10 && (
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6 mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Show:</span>
                    <Select
                      value={showAll ? "all" : itemsPerPage.toString()}
                      onValueChange={(value) => {
                        if (value === "all") {
                          handleShowAll();
                        } else {
                          handlePageSizeChange(parseInt(value));
                        }
                      }}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="25">25</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                        <SelectItem value="all">All</SelectItem>
                      </SelectContent>
                    </Select>
                    <span className="text-sm text-muted-foreground">per page</span>
                  </div>

                  {!showAll && (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleShowAll}
                        className="text-xs"
                      >
                        Show All ({pagination.totalCount})
                      </Button>
                    </div>
                  )}

                  {showAll && (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleShowPaginated}
                        className="text-xs"
                      >
                        Show Paginated
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {/* Pagination Controls */}
              {!showAll && pagination.totalPages > 1 && (
                <div className="flex justify-center mt-6">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => currentPage > 1 && setCurrentPage(prev => Math.max(prev - 1, 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {/* First page */}
                      {currentPage > 2 && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(1)}>1</PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Ellipsis if needed */}
                      {currentPage > 3 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Previous page if not on first page */}
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(currentPage - 1)}>
                            {currentPage - 1}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Current page */}
                      <PaginationItem>
                        <PaginationLink isActive>{currentPage}</PaginationLink>
                      </PaginationItem>

                      {/* Next page if not on last page */}
                      {currentPage < pagination.totalPages && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(currentPage + 1)}>
                            {currentPage + 1}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Ellipsis if needed */}
                      {currentPage < pagination.totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Last page if not already shown */}
                      {currentPage < pagination.totalPages - 1 && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(pagination.totalPages)}>
                            {pagination.totalPages}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => currentPage < pagination.totalPages && setCurrentPage(prev => Math.min(prev + 1, pagination.totalPages))}
                          className={currentPage === pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}

              {/* Page info */}
              <div className="text-center text-sm text-muted-foreground mt-2">
                {showAll ? (
                  <>Showing all {pagination.totalCount} collections</>
                ) : (
                  <>Showing {collections.length} of {pagination.totalCount} collections (Page {pagination.page} of {pagination.totalPages})</>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Collection Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
          <SheetHeader className="mb-5">
            <SheetTitle>Edit Collection</SheetTitle>
            <SheetDescription>
              Update the details of this collection.
            </SheetDescription>
          </SheetHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onSubmitEdit)} className="space-y-4">
              {/* Customer Selection */}
              <FormField
                control={editForm.control}
                name="customer_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer*</FormLabel>
                    <Select
                      value={field.value ? field.value.toString() : ""}
                      onValueChange={(value) => field.onChange(parseInt(value, 10))}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select customer" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id.toString()}>
                            {customer.full_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Loan Selection */}
              <FormField
                control={editForm.control}
                name="loan_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Loan Reference*</FormLabel>
                    <Select
                      value={field.value ? field.value.toString() : ""}
                      onValueChange={(value) => field.onChange(parseInt(value, 10))}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select loan" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loans?.map((loan) => (
                          <SelectItem key={loan.id} value={loan.id.toString()}>
                            {loan.loan_reference_code || `#${loan.id}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Agent Selection */}
              <FormField
                control={editForm.control}
                name="agent_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Agent (Optional)</FormLabel>
                    <Select
                      value={field.value ? field.value.toString() : "null"}
                      onValueChange={(value) =>
                        field.onChange(value === "null" ? null : parseInt(value, 10))
                      }
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select agent" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="null">None</SelectItem>
                        {agents.map((agent) => (
                          <SelectItem key={agent.id} value={agent.id.toString()}>
                            {agent.full_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Amount */}
              <FormField
                control={editForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount (₹)*</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="E.g., 5000"
                        {...field}
                        type="number"
                        step="0.01"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Scheduled Date */}
              <FormField
                control={editForm.control}
                name="scheduled_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Scheduled Date*</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status*</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Payment Method (Only show if completed) */}
              {editForm.watch("status") === "completed" && (
                <FormField
                  control={editForm.control}
                  name="payment_method"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Method</FormLabel>
                      <Select
                        value={field.value || "null"}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="upi">UPI</SelectItem>
                          <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Receipt ID (Only show if completed) */}
              {editForm.watch("status") === "completed" && (
                <FormField
                  control={editForm.control}
                  name="receipt_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Receipt ID</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Receipt number or reference"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Notes */}
              <FormField
                control={editForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details about this collection"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={updateCollectionMutation.isPending}>
                  {updateCollectionMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Update Collection
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Complete Collection Sheet */}
      <Sheet open={isCompleteDialogOpen} onOpenChange={setIsCompleteDialogOpen}>
        <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
          <SheetHeader className="mb-5">
            <SheetTitle>Mark Collection as Complete</SheetTitle>
            <SheetDescription>
              Enter payment details for this collection.
            </SheetDescription>
          </SheetHeader>
          <Form {...completeForm}>
            <form onSubmit={completeForm.handleSubmit(onSubmitComplete)} className="space-y-4">
              {/* Payment Method */}
              <FormField
                control={completeForm.control}
                name="payment_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Method*</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="upi">UPI</SelectItem>
                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Fine Amount */}
              <FormField
                control={completeForm.control}
                name="fine_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fine Amount (₹)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="E.g., 100"
                        {...field}
                        type="number"
                        step="0.01"
                      />
                    </FormControl>
                    <FormDescription>
                      Add any late payment fees or penalties (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Notes */}
              <FormField
                control={completeForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional payment details"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsCompleteDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={completeCollectionMutation.isPending}>
                  {completeCollectionMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Complete Collection
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Payment Receipt Dialog */}
      {selectedCollection && (
        <PaymentReceiptDialog
          open={isReceiptDialogOpen}
          onOpenChange={setIsReceiptDialogOpen}
          collectionId={selectedCollection.id}
          companyId={companyId}
        />
      )}
    </div>
  );
}
