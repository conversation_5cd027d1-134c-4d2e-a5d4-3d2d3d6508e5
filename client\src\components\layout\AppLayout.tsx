import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/lib/auth";
import { Loader2 } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import SimpleHeader from "./SimpleHeader";
import MobileBottomNav from "./MobileBottomNav";
import SidebarHeader from "./SidebarHeader";
import SidebarNavigation from "./SidebarNavigation";
import CompanySelector from "@/components/company/CompanySelector";

interface AppLayoutProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedRoles?: string[];
}

export default function AppLayout({
  children,
  requireAuth = true,
  allowedRoles = [],
}: AppLayoutProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { isAuthenticated, isAuthorized } = useAuth();
  const [location, navigate] = useLocation();
  const isMobile = useIsMobile();

  useEffect(() => {
    // Check authentication and authorization
    if (requireAuth) {
      if (!isAuthenticated()) {
        navigate("/login");
        return;
      }

      if (allowedRoles.length > 0 && !isAuthorized(allowedRoles)) {
        navigate("/dashboard");
        return;
      }
    }

    setIsLoading(false);
  }, [requireAuth, isAuthenticated, isAuthorized, allowedRoles, navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center">
        <Loader2 className="h-10 w-10 text-primary animate-spin" />
        <p className="mt-4 text-lg text-gray-600">Loading...</p>
      </div>
    );
  }

  if (!requireAuth) {
    // For login, register pages that don't need the full layout
    return <main className="min-h-screen bg-background">{children}</main>;
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-gray-900/70 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-30 w-64 bg-blue-900 transform transition-transform duration-300 ease-in-out flex flex-col ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`}>
        {/* Sidebar header with logo */}
        <SidebarHeader closeSidebar={() => setSidebarOpen(false)} />

        {/* Company Selector */}
        <CompanySelector />

        {/* Navigation links */}
        <SidebarNavigation closeSidebar={() => setSidebarOpen(false)} />
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col lg:pl-64">
        {/* Mobile header with menu button and search */}
        <SimpleHeader openMobileSidebar={() => setSidebarOpen(true)} />

        {/* Main content */}
        <main className="flex-1 pb-16 md:pb-6 px-3 md:px-6 pt-4 overflow-y-auto">
          {children}
        </main>

        {/* Mobile bottom navigation */}
        {isMobile && <MobileBottomNav />}
      </div>
    </div>
  );
}