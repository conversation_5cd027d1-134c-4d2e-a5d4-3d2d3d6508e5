import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Loader2, Save, AlertTriangle, CheckCircle, Info, ArrowLeft } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function ResetPassword() {
  const { toast } = useToast();
  const [location, navigate] = useLocation();
  const [activeTab, setActiveTab] = useState('request');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Form states
  const [requestForm, setRequestForm] = useState({
    email: '',
  });

  const [resetForm, setResetForm] = useState({
    token: '',
    password: '',
    confirmPassword: '',
  });

  // Get token from URL if present
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const token = params.get('token');

    if (token) {
      setResetForm(prev => ({ ...prev, token }));
      setActiveTab('reset');
    }
  }, []);

  // Handle request form changes
  const handleRequestChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRequestForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle reset form changes
  const handleResetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setResetForm(prev => ({ ...prev, [name]: value }));

    if (name === 'password') {
      validatePassword(value);
    }
  };

  // Validate password
  const validatePassword = (password: string) => {
    const errors: string[] = [];
    let strength = 0;

    // Check minimum length
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    } else {
      strength += 20;
    }

    // Check for uppercase letters
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    } else {
      strength += 20;
    }

    // Check for lowercase letters
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    } else {
      strength += 20;
    }

    // Check for numbers
    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    } else {
      strength += 20;
    }

    // Check for special characters
    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    } else {
      strength += 20;
    }

    setValidationErrors(errors);
    setPasswordStrength(strength);
  };

  // Handle request form submission
  const handleRequestSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const res = await apiRequest('POST', '/api/auth/forgot-password', {
        email: requestForm.email,
      });

      if (res.ok) {
        setIsSuccess(true);
        toast({
          title: 'Reset Link Sent',
          description: 'If your email is registered, you will receive a password reset link.',
        });

        // In development, get the token from the response
        if (process.env.NODE_ENV !== 'production') {
          const data = await res.json();
          if (data.token) {
            setResetForm(prev => ({ ...prev, token: data.token }));
            setActiveTab('reset');
          }
        }
      } else {
        const errorData = await res.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to send reset link.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error requesting password reset:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle reset form submission
  const handleResetSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validationErrors.length > 0) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the password validation errors before submitting.',
        variant: 'destructive',
      });
      return;
    }

    if (resetForm.password !== resetForm.confirmPassword) {
      toast({
        title: 'Passwords do not match',
        description: 'New password and confirmation password must match.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const res = await apiRequest('POST', '/api/auth/reset-password', {
        token: resetForm.token,
        password: resetForm.password,
        confirmPassword: resetForm.confirmPassword,
      });

      if (res.ok) {
        setIsSuccess(true);
        toast({
          title: 'Password Reset Successful',
          description: 'Your password has been reset successfully. You can now log in with your new password.',
        });

        // Redirect to login page after a short delay
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      } else {
        const errorData = await res.json();
        toast({
          title: 'Error',
          description: errorData.message || 'Failed to reset password.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get strength color
  const getStrengthColor = () => {
    if (passwordStrength < 40) return 'bg-red-500';
    if (passwordStrength < 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Get strength label
  const getStrengthLabel = () => {
    if (passwordStrength < 40) return 'Weak';
    if (passwordStrength < 80) return 'Medium';
    return 'Strong';
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center text-2xl font-bold">Reset Password</CardTitle>
          <CardDescription className="text-center">
            {activeTab === 'request'
              ? 'Enter your email to receive a password reset link'
              : 'Enter your new password'}
          </CardDescription>
        </CardHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="request">Request Reset</TabsTrigger>
            <TabsTrigger value="reset" disabled={!resetForm.token && activeTab !== 'reset'}>Reset Password</TabsTrigger>
          </TabsList>

          <TabsContent value="request">
            {isSuccess ? (
              <CardContent className="space-y-4 pt-6">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Reset Link Sent</AlertTitle>
                  <AlertDescription>
                    If your email is registered, you will receive a password reset link.
                    Please check your email and follow the instructions.
                  </AlertDescription>
                </Alert>

                <div className="flex justify-center">
                  <Button variant="outline" onClick={() => navigate('/login')}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Login
                  </Button>
                </div>
              </CardContent>
            ) : (
              <form onSubmit={handleRequestSubmit}>
                <CardContent className="space-y-4 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={requestForm.email}
                      onChange={handleRequestChange}
                      required
                    />
                  </div>
                </CardContent>

                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => navigate('/login')}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Login
                  </Button>

                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      'Send Reset Link'
                    )}
                  </Button>
                </CardFooter>
              </form>
            )}
          </TabsContent>

          <TabsContent value="reset">
            {isSuccess ? (
              <CardContent className="space-y-4 pt-6">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Password Reset Successful</AlertTitle>
                  <AlertDescription>
                    Your password has been reset successfully. You can now log in with your new password.
                    Redirecting to login page...
                  </AlertDescription>
                </Alert>

                <div className="flex justify-center">
                  <Button onClick={() => navigate('/login')}>
                    Go to Login
                  </Button>
                </div>
              </CardContent>
            ) : (
              <form onSubmit={handleResetSubmit}>
                <CardContent className="space-y-4 pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="token">Reset Token</Label>
                    <Input
                      id="token"
                      name="token"
                      value={resetForm.token}
                      onChange={handleResetChange}
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      This should be automatically filled from your reset link.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">New Password</Label>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      value={resetForm.password}
                      onChange={handleResetChange}
                      required
                    />

                    {resetForm.password && (
                      <div className="mt-2">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-xs">Password Strength: {getStrengthLabel()}</span>
                          <span className="text-xs">{passwordStrength}%</span>
                        </div>
                        <Progress value={passwordStrength} className={getStrengthColor()} />
                      </div>
                    )}

                    {validationErrors.length > 0 && (
                      <div className="mt-2 space-y-1">
                        {validationErrors.map((error, index) => (
                          <p key={index} className="text-xs text-red-500 flex items-center">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {error}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      value={resetForm.confirmPassword}
                      onChange={handleResetChange}
                      required
                    />

                    {resetForm.confirmPassword && resetForm.password !== resetForm.confirmPassword && (
                      <p className="text-xs text-red-500 flex items-center mt-1">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Passwords do not match
                      </p>
                    )}
                  </div>

                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>Password Requirements</AlertTitle>
                    <AlertDescription>
                      <ul className="text-xs list-disc pl-5 mt-2">
                        <li>At least 8 characters long</li>
                        <li>At least one uppercase letter</li>
                        <li>At least one lowercase letter</li>
                        <li>At least one number</li>
                        <li>At least one special character</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </CardContent>

                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => navigate('/login')}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Login
                  </Button>

                  <Button
                    type="submit"
                    disabled={isLoading || validationErrors.length > 0 || resetForm.password !== resetForm.confirmPassword}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Resetting...
                      </>
                    ) : (
                      'Reset Password'
                    )}
                  </Button>
                </CardFooter>
              </form>
            )}
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
}
