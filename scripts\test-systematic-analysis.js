#!/usr/bin/env node

/**
 * Systematic Codebase Analysis Verification Script
 * 
 * This script tests all the findings from the systematic codebase analysis
 * and verifies that the identified issues are correctly diagnosed.
 * 
 * Usage: node scripts/test-systematic-analysis.js
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Configuration
const BASE_URL = 'http://localhost:8080';
const TEST_COMPANY_ID = 13;
const TIMEOUT = 5000;

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`${title}`, 'bold');
  log(`${'='.repeat(60)}`, 'cyan');
}

function logSubsection(title) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`${title}`, 'blue');
  log(`${'-'.repeat(40)}`, 'blue');
}

// Test results tracking
const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  warnings: 0,
  issues: []
};

function recordTest(name, status, details = '') {
  testResults.total++;
  if (status === 'PASS') {
    testResults.passed++;
    log(`✅ ${name}`, 'green');
  } else if (status === 'FAIL') {
    testResults.failed++;
    log(`❌ ${name}`, 'red');
    testResults.issues.push({ name, details, severity: 'ERROR' });
  } else if (status === 'WARN') {
    testResults.warnings++;
    log(`⚠️  ${name}`, 'yellow');
    testResults.issues.push({ name, details, severity: 'WARNING' });
  }
  if (details) {
    log(`   ${details}`, 'reset');
  }
}

// 1. Test for duplicate storage methods
function testDuplicateStorageMethods() {
  logSubsection('Testing Duplicate Storage Methods');
  
  try {
    const storageFile = fs.readFileSync('server/storage.ts', 'utf8');
    const lines = storageFile.split('\n');
    
    // Check for specific duplicate methods identified in analysis
    const duplicates = [
      { method: 'getAccountsByCompany', lines: [2184, 3005] },
      { method: 'createAccount', lines: [2176, 3013] },
      { method: 'updateAccount', lines: [2188, 3017] },
      { method: 'deleteAccount', lines: [2192, 3029] },
      { method: 'getTransactionsByAccount', lines: [2198, 3075] },
      { method: 'updateTransaction', lines: [2202, 3079] }
    ];
    
    let duplicatesFound = 0;
    
    duplicates.forEach(({ method, lines: [line1, line2] }) => {
      const line1Content = lines[line1 - 1] || '';
      const line2Content = lines[line2 - 1] || '';
      
      if (line1Content.includes(method) && line2Content.includes(method)) {
        duplicatesFound++;
        recordTest(
          `Duplicate method: ${method}`,
          'FAIL',
          `Found at lines ${line1} and ${line2}`
        );
      }
    });
    
    if (duplicatesFound === 0) {
      recordTest('No duplicate storage methods found', 'PASS');
    } else {
      recordTest(
        `Found ${duplicatesFound} duplicate storage methods`,
        'FAIL',
        'These duplicates cause build warnings and potential runtime conflicts'
      );
    }
    
  } catch (error) {
    recordTest('Error reading storage.ts file', 'FAIL', error.message);
  }
}

// 2. Test build warnings
function testBuildWarnings() {
  logSubsection('Testing Build Warnings');
  
  try {
    const buildOutput = execSync('npm run build', { 
      encoding: 'utf8',
      timeout: 60000,
      stdio: 'pipe'
    });
    
    // Check for duplicate member warnings
    const duplicateWarnings = (buildOutput.match(/Duplicate member/g) || []).length;
    
    if (duplicateWarnings > 0) {
      recordTest(
        `Build has ${duplicateWarnings} duplicate member warnings`,
        'FAIL',
        'These warnings indicate code quality issues'
      );
    } else {
      recordTest('No duplicate member warnings in build', 'PASS');
    }
    
    // Check for other warnings
    const totalWarnings = (buildOutput.match(/WARNING/g) || []).length;
    if (totalWarnings > duplicateWarnings) {
      recordTest(
        `Build has ${totalWarnings - duplicateWarnings} other warnings`,
        'WARN',
        'Non-critical warnings found'
      );
    }
    
  } catch (error) {
    recordTest('Build test failed', 'FAIL', error.message);
  }
}

// 3. Test route coverage
function testRouteCoverage() {
  logSubsection('Testing Route Coverage');
  
  try {
    const routesDir = 'server/routes';
    const files = fs.readdirSync(routesDir);
    
    // Expected core route files
    const expectedRoutes = [
      'auth.routes.ts',
      'user.routes.ts',
      'company.routes.ts',
      'branch.routes.ts',
      'customer.routes.ts',
      'loan.routes.ts',
      'collection.routes.ts',
      'payment.routes.ts',
      'agent.routes.ts',
      'partner.routes.ts'
    ];
    
    let missingRoutes = [];
    expectedRoutes.forEach(route => {
      if (!files.includes(route)) {
        missingRoutes.push(route);
      }
    });
    
    if (missingRoutes.length === 0) {
      recordTest('All core route files present', 'PASS');
    } else {
      recordTest(
        `Missing ${missingRoutes.length} core route files`,
        'FAIL',
        `Missing: ${missingRoutes.join(', ')}`
      );
    }
    
    // Check for financial routes subdirectory
    const financialDir = path.join(routesDir, 'financial');
    if (fs.existsSync(financialDir)) {
      const financialFiles = fs.readdirSync(financialDir);
      const expectedFinancialRoutes = [
        'account.routes.ts',
        'transaction.routes.ts',
        'report.routes.ts',
        'expense.routes.ts',
        'index.ts'
      ];
      
      const missingFinancialRoutes = expectedFinancialRoutes.filter(
        route => !financialFiles.includes(route)
      );
      
      if (missingFinancialRoutes.length === 0) {
        recordTest('All financial route files present', 'PASS');
      } else {
        recordTest(
          `Missing ${missingFinancialRoutes.length} financial route files`,
          'WARN',
          `Missing: ${missingFinancialRoutes.join(', ')}`
        );
      }
    } else {
      recordTest('Financial routes directory missing', 'FAIL');
    }
    
  } catch (error) {
    recordTest('Route coverage test failed', 'FAIL', error.message);
  }
}

// 4. Test Zustand store cleanup functions
function testZustandStoreCleanup() {
  logSubsection('Testing Zustand Store Cleanup Functions');
  
  try {
    const branchStoreFile = fs.readFileSync('client/src/lib/branches.ts', 'utf8');
    
    if (branchStoreFile.includes('clearBranchData')) {
      recordTest('Branch store has cleanup function', 'PASS');
    } else {
      recordTest('Branch store missing cleanup function', 'FAIL');
    }
    
    // Check if cleanup is called in logout
    const authFile = fs.readFileSync('client/src/lib/auth.ts', 'utf8');
    if (authFile.includes('clearBranchData')) {
      recordTest('Logout function calls store cleanup', 'PASS');
    } else {
      recordTest('Logout function missing store cleanup', 'FAIL');
    }
    
  } catch (error) {
    recordTest('Zustand store test failed', 'FAIL', error.message);
  }
}

// 5. Test route registration order
function testRouteRegistrationOrder() {
  logSubsection('Testing Route Registration Order');
  
  try {
    const indexFile = fs.readFileSync('server/routes/index.ts', 'utf8');
    const lines = indexFile.split('\n');
    
    // Find registration order
    const registrations = [];
    lines.forEach((line, index) => {
      if (line.includes('registerCompanyRoutes(app)')) {
        registrations.push({ type: 'company', line: index + 1 });
      }
      if (line.includes('registerBranchRoutes(app)')) {
        registrations.push({ type: 'branch', line: index + 1 });
      }
    });
    
    // Check if branch routes are registered before company routes (optimal order)
    const companyReg = registrations.find(r => r.type === 'company');
    const branchReg = registrations.find(r => r.type === 'branch');

    if (companyReg && branchReg) {
      if (branchReg.line < companyReg.line) {
        recordTest('Route registration order is optimized', 'PASS');
      } else {
        recordTest(
          'Company routes registered before branch routes',
          'WARN',
          'This order could potentially cause route conflicts'
        );
      }
    } else {
      recordTest('Could not determine route registration order', 'WARN');
    }
    
  } catch (error) {
    recordTest('Route registration order test failed', 'FAIL', error.message);
  }
}

// 6. Test React Query configuration
function testReactQueryConfig() {
  logSubsection('Testing React Query Configuration');
  
  try {
    const queryClientFile = fs.readFileSync('client/src/lib/queryClient.ts', 'utf8');
    
    // Check for financial data optimizations
    const hasStaleTime0 = queryClientFile.includes('staleTime: 0');
    const hasCacheTime0 = queryClientFile.includes('cacheTime: 0');
    const hasRefetchAlways = queryClientFile.includes("refetchOnMount: 'always'");
    
    if (hasStaleTime0 && hasCacheTime0 && hasRefetchAlways) {
      recordTest('React Query optimized for financial data', 'PASS');
    } else {
      recordTest('React Query configuration may not be optimal', 'WARN');
    }
    
  } catch (error) {
    recordTest('React Query config test failed', 'FAIL', error.message);
  }
}

// Main execution
async function runSystematicAnalysisTests() {
  logSection('🔍 SYSTEMATIC CODEBASE ANALYSIS VERIFICATION');
  
  log('This script verifies all findings from the systematic codebase analysis report.', 'cyan');
  log('Testing identified issues and confirming current system state.\n', 'cyan');
  
  // Run all tests
  testDuplicateStorageMethods();
  testBuildWarnings();
  testRouteCoverage();
  testZustandStoreCleanup();
  testRouteRegistrationOrder();
  testReactQueryConfig();
  
  // Summary
  logSection('📊 TEST RESULTS SUMMARY');
  
  log(`Total Tests: ${testResults.total}`, 'bold');
  log(`Passed: ${testResults.passed}`, 'green');
  log(`Failed: ${testResults.failed}`, 'red');
  log(`Warnings: ${testResults.warnings}`, 'yellow');
  
  if (testResults.issues.length > 0) {
    logSubsection('Issues Found');
    testResults.issues.forEach(issue => {
      const color = issue.severity === 'ERROR' ? 'red' : 'yellow';
      log(`${issue.severity}: ${issue.name}`, color);
      if (issue.details) {
        log(`  ${issue.details}`, 'reset');
      }
    });
  }
  
  logSubsection('Recommendations');
  
  if (testResults.failed > 0) {
    log('❌ CRITICAL: Address failed tests immediately', 'red');
    log('   These issues can cause runtime problems or build failures', 'red');
  }
  
  if (testResults.warnings > 0) {
    log('⚠️  MODERATE: Review warnings for optimization opportunities', 'yellow');
    log('   These issues may impact performance or maintainability', 'yellow');
  }
  
  if (testResults.failed === 0 && testResults.warnings === 0) {
    log('✅ EXCELLENT: All tests passed! System is in good health.', 'green');
  }
  
  log('\n📋 Next Steps:', 'bold');
  log('1. Fix any failed tests using the systematic analysis report', 'cyan');
  log('2. Address warnings based on priority and available time', 'cyan');
  log('3. Re-run this script after making changes', 'cyan');
  log('4. Update documentation with any new findings', 'cyan');
  
  logSection('🔗 RELATED DOCUMENTATION');
  log('• docs/troubleshooting/systematic-codebase-analysis.md', 'blue');
  log('• docs/troubleshooting/route-conflicts-and-caching-issues.md', 'blue');
  log('• docs/api-404-fixes-summary.md', 'blue');
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run the tests
runSystematicAnalysisTests().catch(error => {
  log(`\n❌ Script execution failed: ${error.message}`, 'red');
  process.exit(1);
});
