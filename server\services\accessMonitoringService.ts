import { db } from '../db';
import {
  securityEvents, securityRules, securityAlerts, securityResponses, 
  userBehaviorBaselines, rateLimitViolations, userSessions,
  type SecurityEvent, type InsertSecurityEvent, type SecurityRule,
  type SecurityAlert, type InsertSecurityAlert, type SecurityResponse,
  type InsertSecurityResponse, type UserBehaviorBaseline,
  type SecurityEventContext, type AnomalyDetectionResult, type SecurityRuleEvaluation
} from '@shared/schema';
import { eq, and, or, desc, count, gte, lte, inArray, sql } from 'drizzle-orm';
import crypto from 'crypto';
import { enhancedSessionService } from './enhancedSessionService';

export class AccessMonitoringService {
  /**
   * Record a security event and trigger monitoring rules
   */
  async recordSecurityEvent(params: {
    eventType: string;
    eventDescription: string;
    context: SecurityEventContext;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    eventSource?: string;
    resourceAccessed?: string;
    operationPerformed?: string;
  }): Promise<SecurityEvent> {
    const eventId = this.generateEventId();
    
    // Detect anomalies
    const anomalyResult = await this.detectAnomalies(params.context);
    
    // Calculate risk score
    const riskScore = this.calculateRiskScore(params.eventType, params.context, anomalyResult);
    
    // Determine if this is business hours and weekend
    const now = new Date();
    const isWeekend = now.getDay() === 0 || now.getDay() === 6;
    const hour = now.getHours();
    const isBusinessHours = hour >= 9 && hour <= 17 && !isWeekend;
    
    // Create security event
    const eventData: InsertSecurityEvent = {
      event_id: eventId,
      user_id: params.context.userId,
      company_id: params.context.companyId,
      session_id: params.context.sessionId,
      event_type: params.eventType as any,
      severity: params.severity || this.determineSeverity(riskScore),
      risk_score: riskScore,
      event_description: params.eventDescription,
      event_source: params.eventSource || 'api',
      resource_accessed: params.resourceAccessed,
      operation_performed: params.operationPerformed,
      ip_address: params.context.ipAddress,
      user_agent: params.context.userAgent,
      device_fingerprint: params.context.deviceFingerprint,
      device_type: params.context.deviceType,
      endpoint: params.context.endpoint,
      method: params.context.method,
      status_code: params.context.statusCode,
      response_time_ms: params.context.responseTime,
      location_country: params.context.location?.country,
      location_region: params.context.location?.region,
      location_city: params.context.location?.city,
      is_geo_anomaly: anomalyResult.isAnomaly && anomalyResult.anomalyType === 'geo',
      is_time_anomaly: anomalyResult.isAnomaly && anomalyResult.anomalyType === 'time',
      is_weekend: isWeekend,
      is_business_hours: isBusinessHours,
      anomaly_indicators: anomalyResult.isAnomaly ? {
        type: anomalyResult.anomalyType,
        confidence: anomalyResult.confidence,
        indicators: anomalyResult.indicators
      } : {},
      metadata: params.context.metadata || {}
    };
    
    const [event] = await db.insert(securityEvents).values(eventData).returning();
    
    // Evaluate security rules and trigger responses
    await this.evaluateSecurityRules(event);
    
    return event;
  }
  
  /**
   * Detect anomalies based on user behavior baseline
   */
  async detectAnomalies(context: SecurityEventContext): Promise<AnomalyDetectionResult> {
    if (!context.userId) {
      return {
        isAnomaly: false,
        anomalyType: 'behavior',
        confidence: 0,
        indicators: [],
        riskScore: 0
      };
    }
    
    // Get user behavior baseline
    const baseline = await this.getUserBehaviorBaseline(context.userId, context.companyId);
    if (!baseline) {
      return {
        isAnomaly: false,
        anomalyType: 'behavior',
        confidence: 0,
        indicators: [],
        riskScore: 0
      };
    }
    
    const indicators: string[] = [];
    let maxConfidence = 0;
    let primaryAnomalyType: 'geo' | 'time' | 'device' | 'behavior' | 'volume' = 'behavior';
    
    // Geographic anomaly detection
    if (context.location?.country) {
      const typicalLocations = baseline.typical_locations as string[] || [];
      if (!typicalLocations.includes(context.location.country)) {
        indicators.push(`Unusual location: ${context.location.country}`);
        maxConfidence = Math.max(maxConfidence, 0.8);
        primaryAnomalyType = 'geo';
      }
    }
    
    // Time-based anomaly detection
    const now = new Date();
    const hour = now.getHours();
    const dayOfWeek = now.getDay();
    
    const typicalHours = baseline.typical_login_hours as Record<string, number> || {};
    const typicalDays = baseline.typical_login_days as Record<string, number> || {};
    
    const hourFrequency = typicalHours[hour.toString()] || 0;
    const dayFrequency = typicalDays[dayOfWeek.toString()] || 0;
    
    if (hourFrequency < 0.1) { // Less than 10% of typical activity
      indicators.push(`Unusual time: ${hour}:00`);
      maxConfidence = Math.max(maxConfidence, 0.6);
      primaryAnomalyType = 'time';
    }
    
    if (dayFrequency < 0.1) {
      indicators.push(`Unusual day: ${['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayOfWeek]}`);
      maxConfidence = Math.max(maxConfidence, 0.5);
      primaryAnomalyType = 'time';
    }
    
    // Device anomaly detection
    if (context.deviceFingerprint) {
      const knownDevices = baseline.known_devices as string[] || [];
      if (!knownDevices.includes(context.deviceFingerprint)) {
        indicators.push('Unknown device');
        maxConfidence = Math.max(maxConfidence, 0.7);
        primaryAnomalyType = 'device';
      }
    }
    
    // Calculate risk score based on anomalies
    const riskScore = Math.min(100, Math.floor(maxConfidence * 100) + indicators.length * 10);
    
    return {
      isAnomaly: maxConfidence > 0.5,
      anomalyType: primaryAnomalyType,
      confidence: maxConfidence,
      indicators,
      riskScore,
      baseline: {
        typical_locations: baseline.typical_locations,
        typical_hours: baseline.typical_login_hours,
        known_devices: baseline.known_devices
      },
      current: {
        location: context.location,
        hour,
        dayOfWeek,
        deviceFingerprint: context.deviceFingerprint
      }
    };
  }
  
  /**
   * Evaluate security rules against an event
   */
  async evaluateSecurityRules(event: SecurityEvent): Promise<void> {
    // Get active security rules for the company
    const rules = await db.select()
      .from(securityRules)
      .where(and(
        eq(securityRules.is_active, true),
        or(
          eq(securityRules.company_id, event.company_id),
          eq(securityRules.company_id, null) // Global rules
        )
      ))
      .orderBy(desc(securityRules.priority));
    
    for (const rule of rules) {
      const evaluation = await this.evaluateRule(rule, event);
      
      if (evaluation.triggered) {
        // Update rule trigger count
        await db.update(securityRules)
          .set({
            last_triggered: new Date(),
            trigger_count: sql`${securityRules.trigger_count} + 1`
          })
          .where(eq(securityRules.id, rule.id));
        
        // Create security alert
        await this.createSecurityAlert(event, rule, evaluation);
        
        // Execute automated responses
        await this.executeAutomatedResponses(event, rule, evaluation);
      }
    }
  }
  
  /**
   * Evaluate a single security rule against an event
   */
  private async evaluateRule(rule: SecurityRule, event: SecurityEvent): Promise<SecurityRuleEvaluation> {
    // Check if event type matches rule
    if (!rule.event_types.includes(event.event_type)) {
      return {
        ruleId: rule.id,
        ruleName: rule.rule_name,
        triggered: false,
        conditions: rule.conditions,
        thresholds: rule.threshold_config,
        actualValues: {},
        severity: rule.severity,
        recommendedActions: []
      };
    }
    
    const conditions = rule.conditions as any;
    const thresholds = rule.threshold_config as any;
    let triggered = true;
    const actualValues: any = {};
    
    // Evaluate conditions
    for (const [key, condition] of Object.entries(conditions)) {
      const eventValue = this.getEventValue(event, key);
      actualValues[key] = eventValue;
      
      if (!this.evaluateCondition(eventValue, condition)) {
        triggered = false;
        break;
      }
    }
    
    // Check thresholds (e.g., rate limiting)
    if (triggered && thresholds.max_attempts) {
      const recentEvents = await this.getRecentEvents(
        event.user_id,
        event.event_type,
        thresholds.time_window || 300 // 5 minutes default
      );
      
      actualValues.recent_attempts = recentEvents.length;
      
      if (recentEvents.length < thresholds.max_attempts) {
        triggered = false;
      }
    }
    
    return {
      ruleId: rule.id,
      ruleName: rule.rule_name,
      triggered,
      conditions,
      thresholds,
      actualValues,
      severity: rule.severity,
      recommendedActions: rule.auto_response_actions as string[] || []
    };
  }
  
  /**
   * Create a security alert for a triggered rule
   */
  private async createSecurityAlert(
    event: SecurityEvent, 
    rule: SecurityRule, 
    evaluation: SecurityRuleEvaluation
  ): Promise<SecurityAlert> {
    const alertId = this.generateAlertId();
    
    const alertData: InsertSecurityAlert = {
      alert_id: alertId,
      security_event_id: event.id,
      user_id: event.user_id,
      company_id: event.company_id,
      alert_type: rule.rule_name,
      severity: evaluation.severity,
      title: `Security Alert: ${rule.rule_name}`,
      description: `${rule.rule_description}\n\nEvent: ${event.event_description}`,
      recommendation: this.generateRecommendation(evaluation),
      manual_response_required: evaluation.severity === 'high' || evaluation.severity === 'critical'
    };
    
    const [alert] = await db.insert(securityAlerts).values(alertData).returning();
    
    return alert;
  }
  
  /**
   * Execute automated security responses
   */
  private async executeAutomatedResponses(
    event: SecurityEvent,
    rule: SecurityRule,
    evaluation: SecurityRuleEvaluation
  ): Promise<void> {
    const actions = rule.auto_response_actions as string[] || [];
    
    for (const action of actions) {
      await this.executeSecurityResponse(event, rule, action, evaluation);
    }
  }
  
  /**
   * Execute a specific security response action
   */
  private async executeSecurityResponse(
    event: SecurityEvent,
    rule: SecurityRule,
    action: string,
    evaluation: SecurityRuleEvaluation
  ): Promise<void> {
    const responseId = this.generateResponseId();
    
    const responseData: InsertSecurityResponse = {
      response_id: responseId,
      security_event_id: event.id,
      triggered_by_rule_id: rule.id,
      response_action: action as any,
      action_description: `Automated response: ${action} triggered by ${rule.rule_name}`,
      target_user_id: event.user_id,
      target_session_id: event.session_id,
      target_ip_address: event.ip_address,
      action_parameters: evaluation.actualValues,
      executed: false
    };
    
    const [response] = await db.insert(securityResponses).values(responseData).returning();
    
    // Execute the actual response
    try {
      await this.performSecurityAction(action, event, response);
      
      // Mark as executed
      await db.update(securityResponses)
        .set({
          executed: true,
          executed_at: new Date(),
          execution_result: `Successfully executed ${action}`
        })
        .where(eq(securityResponses.id, response.id));
        
    } catch (error) {
      // Mark as failed
      await db.update(securityResponses)
        .set({
          executed: false,
          execution_error: error instanceof Error ? error.message : 'Unknown error'
        })
        .where(eq(securityResponses.id, response.id));
    }
  }
  
  /**
   * Perform the actual security action
   */
  private async performSecurityAction(
    action: string,
    event: SecurityEvent,
    response: SecurityResponse
  ): Promise<void> {
    switch (action) {
      case 'terminate_session':
        if (event.session_id) {
          await enhancedSessionService.terminateSession(
            event.session_id,
            `Terminated by security rule: ${response.action_description}`
          );
        }
        break;
        
      case 'block_ip':
        // TODO: Implement IP blocking logic
        console.log(`Would block IP: ${event.ip_address}`);
        break;
        
      case 'require_mfa':
        // TODO: Implement MFA requirement logic
        console.log(`Would require MFA for user: ${event.user_id}`);
        break;
        
      case 'require_fresh_auth':
        // TODO: Implement fresh auth requirement logic
        console.log(`Would require fresh auth for user: ${event.user_id}`);
        break;
        
      case 'alert_admin':
        // TODO: Implement admin notification logic
        console.log(`Would alert admin about event: ${event.event_id}`);
        break;
        
      case 'log_only':
        // Already logged, no additional action needed
        break;
        
      default:
        throw new Error(`Unknown security action: ${action}`);
    }
  }
  
  /**
   * Get user behavior baseline
   */
  private async getUserBehaviorBaseline(
    userId: number, 
    companyId?: number
  ): Promise<UserBehaviorBaseline | null> {
    const baselines = await db.select()
      .from(userBehaviorBaselines)
      .where(and(
        eq(userBehaviorBaselines.user_id, userId),
        companyId ? eq(userBehaviorBaselines.company_id, companyId) : sql`true`
      ))
      .limit(1);
    
    return baselines.length > 0 ? baselines[0] : null;
  }
  
  /**
   * Get recent events for threshold checking
   */
  private async getRecentEvents(
    userId?: number,
    eventType?: string,
    timeWindowSeconds: number = 300
  ): Promise<SecurityEvent[]> {
    const since = new Date(Date.now() - (timeWindowSeconds * 1000));
    
    let whereCondition = gte(securityEvents.timestamp, since);
    
    if (userId) {
      whereCondition = and(whereCondition, eq(securityEvents.user_id, userId));
    }
    
    if (eventType) {
      whereCondition = and(whereCondition, eq(securityEvents.event_type, eventType as any));
    }
    
    return await db.select()
      .from(securityEvents)
      .where(whereCondition)
      .orderBy(desc(securityEvents.timestamp));
  }
  
  /**
   * Helper methods
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
  }
  
  private generateAlertId(): string {
    return `alert_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
  }
  
  private generateResponseId(): string {
    return `resp_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
  }
  
  private calculateRiskScore(
    eventType: string,
    context: SecurityEventContext,
    anomaly: AnomalyDetectionResult
  ): number {
    let baseScore = 0;
    
    // Base score by event type
    switch (eventType) {
      case 'login_failure': baseScore = 20; break;
      case 'permission_denied': baseScore = 30; break;
      case 'suspicious_activity': baseScore = 60; break;
      case 'brute_force_attempt': baseScore = 80; break;
      case 'privilege_escalation': baseScore = 85; break;
      default: baseScore = 10;
    }
    
    // Add anomaly score
    if (anomaly.isAnomaly) {
      baseScore += anomaly.riskScore;
    }
    
    return Math.min(100, baseScore);
  }
  
  private determineSeverity(riskScore: number): 'low' | 'medium' | 'high' | 'critical' {
    if (riskScore >= 90) return 'critical';
    if (riskScore >= 70) return 'high';
    if (riskScore >= 40) return 'medium';
    return 'low';
  }
  
  private getEventValue(event: SecurityEvent, key: string): any {
    return (event as any)[key];
  }
  
  private evaluateCondition(value: any, condition: any): boolean {
    if (typeof condition === 'object') {
      if (condition.exists !== undefined) {
        return condition.exists ? value !== null && value !== undefined : value === null || value === undefined;
      }
      if (condition.gte !== undefined) {
        return value >= condition.gte;
      }
      if (condition.lte !== undefined) {
        return value <= condition.lte;
      }
      if (condition.in !== undefined) {
        return condition.in.includes(value);
      }
    }
    
    return value === condition;
  }
  
  private generateRecommendation(evaluation: SecurityRuleEvaluation): string {
    const actions = evaluation.recommendedActions;
    if (actions.length === 0) {
      return 'Monitor the situation and investigate if necessary.';
    }
    
    return `Recommended actions: ${actions.join(', ')}. Review the event details and take appropriate action.`;
  }
}

// Export singleton instance
export const accessMonitoringService = new AccessMonitoringService();
