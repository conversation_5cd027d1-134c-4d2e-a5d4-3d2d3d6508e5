import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  HelpCircle,
  Info,
  Lightbulb,
  BookOpen,
  Video,
  ExternalLink,
  Users,
  Shield,
  UserCheck,
  Workflow
} from 'lucide-react';

interface HelpContent {
  title: string;
  description: string;
  tips?: string[];
  examples?: string[];
  relatedTopics?: string[];
  videoUrl?: string;
  docsUrl?: string;
}

interface ContextualHelpProps {
  topic: string;
  userExperienceLevel: 'basic' | 'advanced' | 'expert';
  variant?: 'icon' | 'button' | 'inline';
  size?: 'sm' | 'md' | 'lg';
}

const helpContent: Record<string, Record<string, HelpContent>> = {
  'user-management': {
    basic: {
      title: 'User Management Basics',
      description: 'Add and manage team members who can access your system.',
      tips: [
        'Start by adding users with their email addresses',
        'Assign roles that match their job responsibilities',
        'Users will receive an email invitation to set up their account'
      ],
      examples: [
        'Add a "Manager" role for supervisors',
        'Create an "Employee" role for regular staff',
        'Assign multiple roles if someone has multiple responsibilities'
      ]
    },
    advanced: {
      title: 'Advanced User Management',
      description: 'Comprehensive user and permission management with role-based access control.',
      tips: [
        'Use the Permission Matrix to fine-tune role capabilities',
        'Create custom roles for specific organizational needs',
        'Monitor user activity through the audit logs'
      ],
      examples: [
        'Create department-specific roles (Sales Manager, HR Specialist)',
        'Set up temporary permissions for project-based access',
        'Use groups to manage permissions for teams'
      ]
    },
    expert: {
      title: 'Expert User Management',
      description: 'Full-featured user management with hierarchies, workflows, and automation.',
      tips: [
        'Design role hierarchies to inherit permissions automatically',
        'Set up approval workflows for sensitive permission changes',
        'Use bulk operations for managing large user bases',
        'Implement conditional permissions based on time or location'
      ],
      examples: [
        'Create a hierarchy: CEO → Department Head → Manager → Employee',
        'Set up approval workflows for admin role assignments',
        'Use temporary permissions for contractor access'
      ]
    }
  },
  'permission-matrix': {
    basic: {
      title: 'Understanding Permissions',
      description: 'Control what each role can do in your system.',
      tips: [
        'Think of permissions as specific actions (like "View Reports")',
        'Roles are collections of related permissions',
        'Start with broad permissions and refine as needed'
      ],
      examples: [
        'Give managers permission to view all reports',
        'Allow employees to view only their own data',
        'Grant admin roles system configuration access'
      ]
    },
    advanced: {
      title: 'Permission Matrix Management',
      description: 'Visual interface for managing role permissions across your organization.',
      tips: [
        'Use the grid view for quick permission assignments',
        'Switch to role-focused view when managing many roles',
        'Look for permission source indicators (direct, inherited, temporary)',
        'Use search to quickly find specific permissions'
      ],
      examples: [
        'Bulk assign all customer-related permissions to Sales role',
        'Review inherited permissions from parent roles',
        'Set temporary permissions for special projects'
      ]
    },
    expert: {
      title: 'Advanced Permission Management',
      description: 'Comprehensive permission control with inheritance, conditions, and automation.',
      tips: [
        'Use role hierarchies to automatically inherit permissions',
        'Set up conditional permissions based on context',
        'Monitor permission usage through analytics',
        'Implement least-privilege principles'
      ],
      examples: [
        'Create time-based permissions for shift workers',
        'Set location-based access for remote employees',
        'Use permission templates for consistent role creation'
      ]
    }
  },
  'roles': {
    basic: {
      title: 'Understanding Roles',
      description: 'Roles represent job functions and determine what users can access.',
      tips: [
        'Create roles that match your organizational structure',
        'Keep role names simple and descriptive',
        'Assign users to roles rather than individual permissions'
      ],
      examples: [
        'Manager: Can view reports and manage team',
        'Employee: Can access basic features',
        'Admin: Can configure system settings'
      ]
    },
    advanced: {
      title: 'Role Management',
      description: 'Create and manage custom roles with specific permission sets.',
      tips: [
        'Design roles around job functions, not individuals',
        'Use role descriptions to clarify purpose',
        'Regularly review and update role permissions',
        'Consider creating role templates for consistency'
      ]
    },
    expert: {
      title: 'Advanced Role Architecture',
      description: 'Design complex role structures with hierarchies and inheritance.',
      tips: [
        'Plan role hierarchies to minimize permission duplication',
        'Use inheritance types (inherit, override, deny) strategically',
        'Document role relationships for future reference',
        'Implement role lifecycle management'
      ]
    }
  }
};

const getHelpIcon = (topic: string) => {
  switch (topic) {
    case 'user-management': return Users;
    case 'permission-matrix': return Shield;
    case 'roles': return UserCheck;
    case 'workflows': return Workflow;
    default: return HelpCircle;
  }
};

export function ContextualHelp({
  topic,
  userExperienceLevel,
  variant = 'icon',
  size = 'md'
}: ContextualHelpProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const content = helpContent[topic]?.[userExperienceLevel];
  if (!content) return null;

  const IconComponent = getHelpIcon(topic);
  
  const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';
  
  const trigger = variant === 'button' ? (
    <Button variant="outline" size="sm">
      <HelpCircle className={`${iconSize} mr-1`} />
      Help
    </Button>
  ) : variant === 'inline' ? (
    <Button variant="ghost" size="sm" className="h-auto p-1">
      <Info className={iconSize} />
    </Button>
  ) : (
    <Button variant="ghost" size="sm" className="h-auto p-1">
      <HelpCircle className={iconSize} />
    </Button>
  );

  if (variant === 'inline') {
    return (
      <Popover>
        <PopoverTrigger asChild>
          {trigger}
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <IconComponent className="h-4 w-4 text-blue-500" />
              <h4 className="font-semibold">{content.title}</h4>
            </div>
            <p className="text-sm text-muted-foreground">{content.description}</p>
            {content.tips && content.tips.length > 0 && (
              <div>
                <h5 className="text-sm font-medium mb-1">Quick Tips:</h5>
                <ul className="text-sm text-muted-foreground space-y-1">
                  {content.tips.slice(0, 2).map((tip, index) => (
                    <li key={index} className="flex items-start gap-1">
                      <span className="text-blue-500 mt-1">•</span>
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <Button variant="outline" size="sm" className="w-full" onClick={() => setIsOpen(true)}>
              <BookOpen className="h-3 w-3 mr-1" />
              Learn More
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <IconComponent className="h-5 w-5 text-white" />
            </div>
            <div>
              <DialogTitle>{content.title}</DialogTitle>
              <DialogDescription>{content.description}</DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {content.tips && content.tips.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Lightbulb className="h-4 w-4 text-yellow-500" />
                  Tips & Best Practices
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {content.tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-500 mt-1">•</span>
                      <span className="text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {content.examples && content.examples.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Info className="h-4 w-4 text-green-500" />
                  Examples
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {content.examples.map((example, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-green-500 mt-1">•</span>
                      <span className="text-sm">{example}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {(content.videoUrl || content.docsUrl) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <BookOpen className="h-4 w-4 text-purple-500" />
                  Additional Resources
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2">
                  {content.videoUrl && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={content.videoUrl} target="_blank" rel="noopener noreferrer">
                        <Video className="h-3 w-3 mr-1" />
                        Watch Video
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </Button>
                  )}
                  {content.docsUrl && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={content.docsUrl} target="_blank" rel="noopener noreferrer">
                        <BookOpen className="h-3 w-3 mr-1" />
                        Documentation
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex items-center justify-between pt-4 border-t">
            <Badge variant="outline">
              {userExperienceLevel.charAt(0).toUpperCase() + userExperienceLevel.slice(1)} Level
            </Badge>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Got it!
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
