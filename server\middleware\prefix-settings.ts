import { Response, NextFunction } from 'express';
import { db } from '../db';
import { companyPrefixSettings } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { AuthRequest } from './auth';
import errorLogger from '../utils/errorLogger';

/**
 * Middleware to check if company prefix settings exist before allowing entity creation
 * This prevents creating entities without proper reference code configuration
 */
export async function requirePrefixSettings(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    // Extract company ID from request body or URL parameters
    let companyId: number;

    if (req.body.company_id) {
      companyId = parseInt(req.body.company_id, 10);
    } else if (req.params.companyId) {
      companyId = parseInt(req.params.companyId, 10);
    } else {
      return res.status(400).json({
        message: 'Company ID not found in request',
        code: 'MISSING_COMPANY_ID'
      });
    }

    if (isNaN(companyId)) {
      return res.status(400).json({
        message: 'Invalid company ID',
        code: 'INVALID_COMPANY_ID'
      });
    }

    // Check if prefix settings exist for this company
    const settings = await db.query.companyPrefixSettings.findFirst({
      where: eq(companyPrefixSettings.company_id, companyId),
    });

    if (!settings) {
      return res.status(403).json({
        message: 'Company prefix settings not configured. Please configure prefix settings before creating this entity.',
        code: 'PREFIX_SETTINGS_REQUIRED'
      });
    }

    // If settings exist, proceed
    next();
  } catch (error) {
    errorLogger.logError('Error checking company prefix settings', 'prefix-settings-check', error as Error);
    return res.status(500).json({
      message: 'Error checking company prefix settings',
      code: 'SERVER_ERROR'
    });
  }
}
