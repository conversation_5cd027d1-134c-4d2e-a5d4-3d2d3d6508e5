import React, { ReactNode } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Loader2 } from 'lucide-react';
import { useContextData } from '@/lib/useContextData';

// Try to import the Spinner component, but provide a fallback if it's not available
let Spinner: React.ComponentType<{ className?: string; size?: 'sm' | 'md' | 'lg' }>;
try {
  Spinner = require('@/components/ui/spinner').Spinner;
} catch (error) {
  // Fallback spinner component if the import fails
  Spinner = ({ className, size = 'md' }: { className?: string; size?: 'sm' | 'md' | 'lg' }) => {
    const sizeMap = {
      sm: 'h-4 w-4',
      md: 'h-8 w-8',
      lg: 'h-12 w-12',
    };
    return <Loader2 className={`animate-spin ${sizeMap[size]} ${className || ''}`} />;
  };
}

interface ReportCardProps {
  title: string;
  description?: string;
  isLoading?: boolean;
  isError?: boolean;
  errorMessage?: string;
  children: ReactNode;
  footerContent?: ReactNode;
  onRetry?: () => void;
}

export function ReportCard({
  title,
  description,
  isLoading = false,
  isError = false,
  errorMessage = 'Failed to load report data',
  children,
  footerContent,
  onRetry,
}: ReportCardProps) {
  const { companyId, isAuthenticated } = useContextData();

  // Determine the appropriate message based on authentication and company context
  let message = errorMessage;
  let showRetry = !!onRetry;

  if (!isAuthenticated) {
    message = 'Please log in to view this report';
    showRetry = false;
  } else if (!companyId) {
    message = 'No company selected. Please select a company to view this report';
    showRetry = false;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex flex-col justify-center items-center h-64">
            <Spinner size="lg" className="mb-4" />
            <p className="text-gray-500">Loading report data...</p>
          </div>
        ) : isError ? (
          <div className="flex flex-col justify-center items-center h-64">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-500 mb-4 text-center">{message}</p>
            {showRetry && (
              <Button onClick={onRetry} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            )}
          </div>
        ) : (
          children
        )}
      </CardContent>
      {footerContent && <CardFooter>{footerContent}</CardFooter>}
    </Card>
  );
}
