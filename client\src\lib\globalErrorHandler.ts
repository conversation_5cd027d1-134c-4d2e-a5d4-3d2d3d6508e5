import errorLogger from './errorLogger';
import { shouldFilterError, setupConsoleFiltering, logExtensionDetection } from './browserExtensionFilter';

/**
 * Check if an error should be ignored based on known patterns
 */
function shouldIgnoreError(message: string, source?: string, error?: any): boolean {
  const stack = error?.stack || '';
  return shouldFilterError(String(message), String(source || ''), stack);
}

/**
 * Sets up global error handling for unhandled errors and rejections
 * This allows us to catch errors that aren't handled by components
 */
export function setupGlobalErrorHandlers(): void {
  // Setup console filtering for browser extensions
  setupConsoleFiltering();

  // Log extension detection for debugging
  logExtensionDetection();
  // Handle uncaught errors
  const originalOnError = window.onerror;
  window.onerror = (message, source, lineno, colno, error) => {
    // Filter out external script errors
    if (shouldIgnoreError(String(message), source, error)) {
      return false;
    }

    errorLogger.error(
      `Global error: ${String(message)}`,
      'global',
      { message, source, lineno, colno, error }
    );

    // Call original handler if it exists
    if (originalOnError) {
      return originalOnError(message, source, lineno, colno, error);
    }

    return false;
  };

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
    const error = event.reason;
    const message = error instanceof Error ? error.message : 'Unhandled Promise Rejection';

    // Filter out external script promise rejections
    if (shouldIgnoreError(message, '', error)) {
      return;
    }

    errorLogger.error(
      `Unhandled rejection: ${message}`,
      'global',
      error
    );
  });

  // Log network errors
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);

      // Log HTTP errors (non-2xx responses)
      if (!response.ok) {
        const urlString = typeof args[0] === 'string'
          ? args[0]
          : args[0] instanceof Request
            ? args[0].url
            : String(args[0]);

        // Filter out external service errors
        if (shouldIgnoreError('', urlString)) {
          return response;
        }

        errorLogger.error(
          `HTTP Error ${response.status}: ${response.statusText}`,
          'network',
          { url: urlString, status: response.status, statusText: response.statusText }
        );
      }

      return response;
    } catch (error) {
      // Log network failures
      const urlString = typeof args[0] === 'string'
        ? args[0]
        : args[0] instanceof Request
          ? args[0].url
          : String(args[0]);

      // Filter out external service network errors
      if (shouldIgnoreError(error instanceof Error ? error.message : '', urlString, error)) {
        throw error;
      }

      errorLogger.error(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'network',
        { url: urlString, error }
      );

      throw error;
    }
  };

  // Global error handlers installed (logging removed for cleaner console)
}

export default setupGlobalErrorHandlers;