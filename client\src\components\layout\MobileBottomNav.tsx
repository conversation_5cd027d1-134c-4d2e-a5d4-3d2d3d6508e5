import { useLocation, Link } from "wouter";
import {
  Home,
  DollarSign,
  Users,
  User,
  Receipt,
  Settings,
  Building2,
  FileText,
  Building
} from "lucide-react";

export default function MobileBottomNav() {
  const [location] = useLocation();

  // Define the type for nav items
  type NavItem = {
    name: string;
    icon: React.ComponentType<any>;
    href: string;
    active: boolean;
    highlight?: boolean;
  };

  // Navigation items for the bottom nav - only essential items
  const navItems: NavItem[] = [
    {
      name: "Home",
      icon: Home,
      href: "/dashboard",
      active: location === "/dashboard"
    },
    {
      name: "Collections",
      icon: DollarSign,
      href: "/collections",
      active: location.startsWith("/collections")
    },
    {
      name: "Customers",
      icon: User,
      href: "/customers",
      active: location.startsWith("/customers")
    },
    {
      name: "Loans",
      icon: Receipt,
      href: "/loans",
      active: location.startsWith("/loans")
    }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 lg:hidden">
      <div className="bg-white border-t border-gray-200 flex items-center justify-around p-2 shadow-lg">
        {navItems.map((item) => (
          <Link key={item.name} href={item.href}>
            <div
              className="flex flex-col items-center p-2 cursor-pointer transition-all duration-200"
              style={{
                transform: item.active ? 'translateY(-4px)' : 'translateY(0)',
              }}
            >
              <div
                className={`p-2 rounded-full ${
                  item.active
                    ? 'bg-blue-600 shadow-md shadow-blue-300'
                    : ''
                }`}
                style={{
                  transition: 'all 0.2s ease'
                }}
              >
                <item.icon
                  className={`h-5 w-5 ${
                    item.active
                      ? 'text-white'
                      : 'text-gray-500'
                  }`}
                />
              </div>
              <span
                className={`text-xs mt-1 ${
                  item.active
                    ? 'text-blue-600 font-medium'
                    : 'text-gray-500'
                }`}
                style={{
                  opacity: item.active ? 1 : 0.8,
                  transition: 'all 0.2s ease'
                }}
              >
                {item.name}
              </span>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}