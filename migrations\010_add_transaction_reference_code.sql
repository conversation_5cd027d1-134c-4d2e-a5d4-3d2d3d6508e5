-- Migration to add transaction_reference_code column to transactions table
-- This column will store company-specific transaction identifiers

-- Add transaction_reference_code column to transactions table
ALTER TABLE "transactions"
  ADD COLUMN IF NOT EXISTS "transaction_reference_code" TEXT;

-- Create an index for faster lookups by transaction_reference_code
CREATE INDEX IF NOT EXISTS idx_transactions_reference_code ON transactions(transaction_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN transactions.transaction_reference_code IS 'Company-specific transaction identifier string (e.g., GS-T-001) that is unique within each company';
