import { describe, it, expect } from 'vitest';
import { formatCurrency, formatDate, formatDateWithPattern } from './utils';

describe('Formatting Utilities', () => {
  describe('formatCurrency', () => {
    it('should format currency with default settings', () => {
      const result = formatCurrency(1000);
      expect(result).toBe('₹1,000');
    });

    it('should format currency with custom currency', () => {
      const result = formatCurrency(1000, 'USD');
      expect(result).toBe('$1,000');
    });

    it('should format currency with custom symbol', () => {
      const result = formatCurrency(1000, 'INR', 'en-IN', '₹');
      expect(result).toBe('₹1,000');
    });

    it('should format currency with different locale', () => {
      const result = formatCurrency(1000, 'EUR', 'de-DE');
      expect(result).toBe('1.000 €');
    });
  });

  describe('formatDate', () => {
    const testDate = new Date('2023-05-15T12:00:00Z');
    const testDateString = testDate.toISOString();

    it('should format date with default format', () => {
      const result = formatDate(testDateString);
      expect(result).toContain('2023');
      expect(result).toContain('May');
      expect(result).toContain('15');
    });

    it('should format date with custom format dd-MM-yyyy', () => {
      const result = formatDate(testDateString, 'dd-MM-yyyy');
      expect(result).toBe('15-05-2023');
    });

    it('should format date with custom format MM/dd/yyyy', () => {
      const result = formatDate(testDateString, 'MM/dd/yyyy');
      expect(result).toBe('05/15/2023');
    });

    it('should format date with custom format yyyy-MM-dd', () => {
      const result = formatDate(testDateString, 'yyyy-MM-dd');
      expect(result).toBe('2023-05-15');
    });
  });

  describe('formatDateWithPattern', () => {
    const testDate = new Date('2023-05-15T12:00:00Z');

    it('should format date with dd-MM-yyyy pattern', () => {
      const result = formatDateWithPattern(testDate, 'dd-MM-yyyy');
      expect(result).toBe('15-05-2023');
    });

    it('should format date with MM/dd/yyyy pattern', () => {
      const result = formatDateWithPattern(testDate, 'MM/dd/yyyy');
      expect(result).toBe('05/15/2023');
    });

    it('should format date with yyyy-MM-dd pattern', () => {
      const result = formatDateWithPattern(testDate, 'yyyy-MM-dd');
      expect(result).toBe('2023-05-15');
    });

    it('should format date with dd MMM yyyy pattern', () => {
      const result = formatDateWithPattern(testDate, 'dd MMM yyyy');
      expect(result).toBe('15 May 2023');
    });
  });
});
