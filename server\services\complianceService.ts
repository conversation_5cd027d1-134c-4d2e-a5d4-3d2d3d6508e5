import { db } from '../db';
import {
  complianceFrameworks, complianceRequirements, complianceAssessments,
  accessCertifications, complianceViolations,
  type ComplianceFramework, type InsertComplianceFramework,
  type ComplianceRequirement, type InsertComplianceRequirement,
  type ComplianceAssessment, type InsertComplianceAssessment,
  type AccessCertification, type InsertAccessCertification,
  type ComplianceViolation, type InsertComplianceViolation,
  permissionAuditLogs, dataAccessAuditLogs, users, customRoles
} from '@shared/schema';
import { eq, and, desc, count, gte, lte, inArray, sql, or } from 'drizzle-orm';
import crypto from 'crypto';
import { auditService } from './auditService';

export class ComplianceService {
  /**
   * Generate unique compliance ID
   */
  private generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${crypto.randomBytes(6).toString('hex')}`;
  }

  /**
   * Create compliance framework
   */
  async createFramework(data: InsertComplianceFramework): Promise<ComplianceFramework> {
    const [framework] = await db.insert(complianceFrameworks).values(data).returning();
    return framework;
  }

  /**
   * Get compliance frameworks
   */
  async getFrameworks(filters: {
    frameworkType?: string;
    isActive?: boolean;
  } = {}): Promise<ComplianceFramework[]> {
    const conditions = [];

    if (filters.frameworkType) {
      conditions.push(eq(complianceFrameworks.framework_type, filters.frameworkType as any));
    }
    if (filters.isActive !== undefined) {
      conditions.push(eq(complianceFrameworks.is_active, filters.isActive));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    return await db
      .select()
      .from(complianceFrameworks)
      .where(whereClause)
      .orderBy(complianceFrameworks.framework_name);
  }

  /**
   * Create compliance requirement
   */
  async createRequirement(data: InsertComplianceRequirement): Promise<ComplianceRequirement> {
    const [requirement] = await db.insert(complianceRequirements).values(data).returning();
    return requirement;
  }

  /**
   * Get compliance requirements
   */
  async getRequirements(filters: {
    frameworkId?: number;
    category?: string;
    riskLevel?: string;
    automatedCheck?: boolean;
    isActive?: boolean;
  } = {}): Promise<ComplianceRequirement[]> {
    const conditions = [];

    if (filters.frameworkId) {
      conditions.push(eq(complianceRequirements.framework_id, filters.frameworkId));
    }
    if (filters.category) {
      conditions.push(eq(complianceRequirements.category, filters.category));
    }
    if (filters.riskLevel) {
      conditions.push(eq(complianceRequirements.risk_level, filters.riskLevel as any));
    }
    if (filters.automatedCheck !== undefined) {
      conditions.push(eq(complianceRequirements.automated_check, filters.automatedCheck));
    }
    if (filters.isActive !== undefined) {
      conditions.push(eq(complianceRequirements.is_active, filters.isActive));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    return await db
      .select()
      .from(complianceRequirements)
      .where(whereClause)
      .orderBy(complianceRequirements.requirement_name);
  }

  /**
   * Create compliance assessment
   */
  async createAssessment(data: Omit<InsertComplianceAssessment, 'assessment_id'>): Promise<ComplianceAssessment> {
    const assessmentData: InsertComplianceAssessment = {
      ...data,
      assessment_id: this.generateId('assess'),
    };

    const [assessment] = await db.insert(complianceAssessments).values(assessmentData).returning();
    return assessment;
  }

  /**
   * Get compliance assessments
   */
  async getAssessments(filters: {
    companyId?: number;
    frameworkId?: number;
    requirementId?: number;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{ assessments: ComplianceAssessment[]; totalCount: number }> {
    const conditions = [];

    if (filters.companyId) {
      conditions.push(eq(complianceAssessments.company_id, filters.companyId));
    }
    if (filters.frameworkId) {
      conditions.push(eq(complianceAssessments.framework_id, filters.frameworkId));
    }
    if (filters.requirementId) {
      conditions.push(eq(complianceAssessments.requirement_id, filters.requirementId));
    }
    if (filters.status) {
      conditions.push(eq(complianceAssessments.status, filters.status as any));
    }
    if (filters.startDate) {
      conditions.push(gte(complianceAssessments.assessment_date, filters.startDate));
    }
    if (filters.endDate) {
      conditions.push(lte(complianceAssessments.assessment_date, filters.endDate));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(complianceAssessments)
      .where(whereClause);

    // Get assessments with pagination
    const assessments = await db
      .select()
      .from(complianceAssessments)
      .where(whereClause)
      .orderBy(desc(complianceAssessments.assessment_date))
      .limit(filters.limit || 50)
      .offset(filters.offset || 0);

    return { assessments, totalCount };
  }

  /**
   * Update compliance assessment
   */
  async updateAssessment(id: number, data: Partial<InsertComplianceAssessment>): Promise<ComplianceAssessment> {
    const [assessment] = await db
      .update(complianceAssessments)
      .set({ ...data, updated_at: new Date() })
      .where(eq(complianceAssessments.id, id))
      .returning();

    return assessment;
  }

  /**
   * Create access certification
   */
  async createAccessCertification(data: Omit<InsertAccessCertification, 'certification_id'>): Promise<AccessCertification> {
    const certificationData: InsertAccessCertification = {
      ...data,
      certification_id: this.generateId('cert'),
    };

    const [certification] = await db.insert(accessCertifications).values(certificationData).returning();
    return certification;
  }

  /**
   * Get access certifications
   */
  async getAccessCertifications(filters: {
    companyId?: number;
    status?: string;
    certificationType?: string;
    targetUserId?: number;
    primaryReviewer?: number;
    dueBefore?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{ certifications: AccessCertification[]; totalCount: number }> {
    const conditions = [];

    if (filters.companyId) {
      conditions.push(eq(accessCertifications.company_id, filters.companyId));
    }
    if (filters.status) {
      conditions.push(eq(accessCertifications.status, filters.status as any));
    }
    if (filters.certificationType) {
      conditions.push(eq(accessCertifications.certification_type, filters.certificationType));
    }
    if (filters.targetUserId) {
      conditions.push(eq(accessCertifications.target_user_id, filters.targetUserId));
    }
    if (filters.primaryReviewer) {
      conditions.push(eq(accessCertifications.primary_reviewer, filters.primaryReviewer));
    }
    if (filters.dueBefore) {
      conditions.push(lte(accessCertifications.due_date, filters.dueBefore));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(accessCertifications)
      .where(whereClause);

    // Get certifications with pagination
    const certifications = await db
      .select()
      .from(accessCertifications)
      .where(whereClause)
      .orderBy(accessCertifications.due_date)
      .limit(filters.limit || 50)
      .offset(filters.offset || 0);

    return { certifications, totalCount };
  }

  /**
   * Update access certification
   */
  async updateAccessCertification(id: number, data: Partial<InsertAccessCertification>): Promise<AccessCertification> {
    const [certification] = await db
      .update(accessCertifications)
      .set({ ...data, updated_at: new Date() })
      .where(eq(accessCertifications.id, id))
      .returning();

    return certification;
  }

  /**
   * Create compliance violation
   */
  async createViolation(data: Omit<InsertComplianceViolation, 'violation_id'>): Promise<ComplianceViolation> {
    const violationData: InsertComplianceViolation = {
      ...data,
      violation_id: this.generateId('viol'),
    };

    const [violation] = await db.insert(complianceViolations).values(violationData).returning();
    return violation;
  }

  /**
   * Get compliance violations
   */
  async getViolations(filters: {
    companyId?: number;
    frameworkId?: number;
    severity?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  }): Promise<{ violations: ComplianceViolation[]; totalCount: number }> {
    const conditions = [];

    if (filters.companyId) {
      conditions.push(eq(complianceViolations.company_id, filters.companyId));
    }
    if (filters.frameworkId) {
      conditions.push(eq(complianceViolations.framework_id, filters.frameworkId));
    }
    if (filters.severity) {
      conditions.push(eq(complianceViolations.severity, filters.severity as any));
    }
    if (filters.status) {
      conditions.push(eq(complianceViolations.status, filters.status));
    }
    if (filters.startDate) {
      conditions.push(gte(complianceViolations.detected_at, filters.startDate));
    }
    if (filters.endDate) {
      conditions.push(lte(complianceViolations.detected_at, filters.endDate));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(complianceViolations)
      .where(whereClause);

    // Get violations with pagination
    const violations = await db
      .select()
      .from(complianceViolations)
      .where(whereClause)
      .orderBy(desc(complianceViolations.detected_at))
      .limit(filters.limit || 50)
      .offset(filters.offset || 0);

    return { violations, totalCount };
  }

  /**
   * Update compliance violation
   */
  async updateViolation(id: number, data: Partial<InsertComplianceViolation>): Promise<ComplianceViolation> {
    const [violation] = await db
      .update(complianceViolations)
      .set({ ...data, updated_at: new Date() })
      .where(eq(complianceViolations.id, id))
      .returning();

    return violation;
  }

  /**
   * Get compliance dashboard data
   */
  async getComplianceDashboard(companyId: number): Promise<{
    overallScore: number;
    frameworkCompliance: Array<{ framework: string; score: number; status: string }>;
    recentAssessments: ComplianceAssessment[];
    pendingCertifications: AccessCertification[];
    activeViolations: ComplianceViolation[];
    upcomingDeadlines: Array<{ type: string; description: string; dueDate: Date; priority: string }>;
  }> {
    // Get framework compliance scores
    const frameworkScores = await db
      .select({
        frameworkId: complianceAssessments.framework_id,
        frameworkName: complianceFrameworks.framework_name,
        avgScore: sql<number>`AVG(${complianceAssessments.score})`.as('avg_score'),
        totalAssessments: count(),
        compliantCount: sql<number>`COUNT(CASE WHEN ${complianceAssessments.status} = 'compliant' THEN 1 END)`.as('compliant_count'),
      })
      .from(complianceAssessments)
      .innerJoin(complianceFrameworks, eq(complianceAssessments.framework_id, complianceFrameworks.id))
      .where(eq(complianceAssessments.company_id, companyId))
      .groupBy(complianceAssessments.framework_id, complianceFrameworks.framework_name);

    const frameworkCompliance = frameworkScores.map(fs => ({
      framework: fs.frameworkName,
      score: Math.round(fs.avgScore || 0),
      status: fs.compliantCount === fs.totalAssessments ? 'compliant' : 'non_compliant',
    }));

    const overallScore = frameworkScores.length > 0
      ? Math.round(frameworkScores.reduce((sum, fs) => sum + (fs.avgScore || 0), 0) / frameworkScores.length)
      : 0;

    // Get recent assessments
    const recentAssessments = await db
      .select()
      .from(complianceAssessments)
      .where(eq(complianceAssessments.company_id, companyId))
      .orderBy(desc(complianceAssessments.assessment_date))
      .limit(5);

    // Get pending certifications
    const pendingCertifications = await db
      .select()
      .from(accessCertifications)
      .where(and(
        eq(accessCertifications.company_id, companyId),
        inArray(accessCertifications.status, ['pending', 'in_progress'])
      ))
      .orderBy(accessCertifications.due_date)
      .limit(5);

    // Get active violations
    const activeViolations = await db
      .select()
      .from(complianceViolations)
      .where(and(
        eq(complianceViolations.company_id, companyId),
        inArray(complianceViolations.status, ['open', 'investigating', 'remediating'])
      ))
      .orderBy(desc(complianceViolations.detected_at))
      .limit(5);

    // Get upcoming deadlines
    const upcomingAssessments = await db
      .select({
        type: sql<string>`'assessment'`.as('type'),
        description: sql<string>`CONCAT('Assessment due: ', ${complianceRequirements.requirement_name})`.as('description'),
        dueDate: complianceAssessments.next_assessment_due,
        priority: sql<string>`CASE WHEN ${complianceRequirements.risk_level} IN ('high', 'very_high', 'critical') THEN 'high' ELSE 'medium' END`.as('priority'),
      })
      .from(complianceAssessments)
      .innerJoin(complianceRequirements, eq(complianceAssessments.requirement_id, complianceRequirements.id))
      .where(and(
        eq(complianceAssessments.company_id, companyId),
        gte(complianceAssessments.next_assessment_due, new Date()),
        lte(complianceAssessments.next_assessment_due, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // Next 30 days
      ))
      .orderBy(complianceAssessments.next_assessment_due);

    const upcomingCertifications = await db
      .select({
        type: sql<string>`'certification'`.as('type'),
        description: sql<string>`CONCAT('Access certification due: ', ${accessCertifications.certification_type})`.as('description'),
        dueDate: accessCertifications.due_date,
        priority: sql<string>`CASE WHEN ${accessCertifications.escalation_level} > 0 THEN 'high' ELSE 'medium' END`.as('priority'),
      })
      .from(accessCertifications)
      .where(and(
        eq(accessCertifications.company_id, companyId),
        inArray(accessCertifications.status, ['pending', 'in_progress']),
        lte(accessCertifications.due_date, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // Next 30 days
      ))
      .orderBy(accessCertifications.due_date);

    const upcomingDeadlines = [
      ...upcomingAssessments.map(a => ({
        type: a.type,
        description: a.description,
        dueDate: a.dueDate!,
        priority: a.priority,
      })),
      ...upcomingCertifications.map(c => ({
        type: c.type,
        description: c.description,
        dueDate: c.dueDate,
        priority: c.priority,
      })),
    ].sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime());

    return {
      overallScore,
      frameworkCompliance,
      recentAssessments,
      pendingCertifications,
      activeViolations,
      upcomingDeadlines,
    };
  }

  /**
   * Generate regulatory compliance report
   */
  async generateRegulatoryReport(companyId: number, frameworkId: number, startDate: Date, endDate: Date): Promise<{
    framework: ComplianceFramework;
    assessmentSummary: {
      totalAssessments: number;
      compliantAssessments: number;
      nonCompliantAssessments: number;
      averageScore: number;
    };
    requirementDetails: Array<{
      requirement: ComplianceRequirement;
      latestAssessment: ComplianceAssessment | null;
      complianceHistory: ComplianceAssessment[];
    }>;
    violations: ComplianceViolation[];
    recommendations: string[];
  }> {
    // Get framework details
    const [framework] = await db
      .select()
      .from(complianceFrameworks)
      .where(eq(complianceFrameworks.id, frameworkId));

    if (!framework) {
      throw new Error('Framework not found');
    }

    // Get assessment summary
    const assessmentStats = await db
      .select({
        totalAssessments: count(),
        compliantAssessments: sql<number>`COUNT(CASE WHEN ${complianceAssessments.status} = 'compliant' THEN 1 END)`.as('compliant_count'),
        nonCompliantAssessments: sql<number>`COUNT(CASE WHEN ${complianceAssessments.status} = 'non_compliant' THEN 1 END)`.as('non_compliant_count'),
        averageScore: sql<number>`AVG(${complianceAssessments.score})`.as('avg_score'),
      })
      .from(complianceAssessments)
      .where(and(
        eq(complianceAssessments.company_id, companyId),
        eq(complianceAssessments.framework_id, frameworkId),
        gte(complianceAssessments.assessment_date, startDate),
        lte(complianceAssessments.assessment_date, endDate)
      ));

    const assessmentSummary = {
      totalAssessments: assessmentStats[0]?.totalAssessments || 0,
      compliantAssessments: assessmentStats[0]?.compliantAssessments || 0,
      nonCompliantAssessments: assessmentStats[0]?.nonCompliantAssessments || 0,
      averageScore: Math.round(assessmentStats[0]?.averageScore || 0),
    };

    // Get requirements with their latest assessments
    const requirements = await db
      .select()
      .from(complianceRequirements)
      .where(and(
        eq(complianceRequirements.framework_id, frameworkId),
        eq(complianceRequirements.is_active, true)
      ))
      .orderBy(complianceRequirements.requirement_code);

    const requirementDetails = await Promise.all(
      requirements.map(async (requirement) => {
        // Get latest assessment for this requirement
        const latestAssessment = await db
          .select()
          .from(complianceAssessments)
          .where(and(
            eq(complianceAssessments.company_id, companyId),
            eq(complianceAssessments.requirement_id, requirement.id)
          ))
          .orderBy(desc(complianceAssessments.assessment_date))
          .limit(1);

        // Get compliance history for this requirement
        const complianceHistory = await db
          .select()
          .from(complianceAssessments)
          .where(and(
            eq(complianceAssessments.company_id, companyId),
            eq(complianceAssessments.requirement_id, requirement.id),
            gte(complianceAssessments.assessment_date, startDate),
            lte(complianceAssessments.assessment_date, endDate)
          ))
          .orderBy(desc(complianceAssessments.assessment_date));

        return {
          requirement,
          latestAssessment: latestAssessment[0] || null,
          complianceHistory,
        };
      })
    );

    // Get violations for this framework
    const violations = await db
      .select()
      .from(complianceViolations)
      .where(and(
        eq(complianceViolations.company_id, companyId),
        eq(complianceViolations.framework_id, frameworkId),
        gte(complianceViolations.detected_at, startDate),
        lte(complianceViolations.detected_at, endDate)
      ))
      .orderBy(desc(complianceViolations.detected_at));

    // Generate recommendations based on assessment results
    const recommendations: string[] = [];

    if (assessmentSummary.averageScore < 70) {
      recommendations.push('Overall compliance score is below acceptable threshold. Immediate attention required.');
    }

    const nonCompliantRequirements = requirementDetails.filter(rd =>
      rd.latestAssessment?.status === 'non_compliant'
    );

    if (nonCompliantRequirements.length > 0) {
      recommendations.push(`${nonCompliantRequirements.length} requirements are non-compliant and require remediation.`);
    }

    if (violations.length > 0) {
      const openViolations = violations.filter(v => v.status === 'open');
      if (openViolations.length > 0) {
        recommendations.push(`${openViolations.length} open violations require immediate attention.`);
      }
    }

    const overdueAssessments = requirementDetails.filter(rd =>
      rd.latestAssessment?.next_assessment_due &&
      rd.latestAssessment.next_assessment_due < new Date()
    );

    if (overdueAssessments.length > 0) {
      recommendations.push(`${overdueAssessments.length} assessments are overdue and need to be completed.`);
    }

    return {
      framework,
      assessmentSummary,
      requirementDetails,
      violations,
      recommendations,
    };
  }
}

// Create singleton instance
export const complianceService = new ComplianceService();
