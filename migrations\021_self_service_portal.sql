-- Migration: Self-Service Portal
-- Description: Create tables for user self-service portal functionality
-- Date: 2025-01-27

-- Create request status enum
CREATE TYPE request_status AS ENUM (
  'pending', 'approved', 'rejected', 'cancelled', 'expired'
);

-- Create request type enum
CREATE TYPE request_type AS ENUM (
  'permission_grant', 'permission_revoke', 'role_change', 'access_extension', 'temporary_access'
);

-- Create priority enum
CREATE TYPE request_priority AS ENUM (
  'low', 'medium', 'high', 'urgent'
);

-- Permission requests table
CREATE TABLE IF NOT EXISTS "permission_requests" (
  "id" SERIAL PRIMARY KEY,
  "request_id" varchar(255) NOT NULL UNIQUE,
  "requester_id" integer REFERENCES "users"("id") ON DELETE CASCADE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Request details
  "request_type" request_type NOT NULL,
  "title" varchar(255) NOT NULL,
  "description" text,
  "business_justification" text NOT NULL,
  "priority" request_priority DEFAULT 'medium',
  "urgency_reason" text,
  
  -- Requested permissions/access
  "requested_permissions" jsonb DEFAULT '[]', -- Array of permission codes
  "requested_role_id" integer REFERENCES "custom_roles"("id") ON DELETE SET NULL,
  "requested_resources" jsonb DEFAULT '[]', -- Array of resource identifiers
  "access_scope" varchar(100), -- 'branch', 'department', 'company', 'specific_resources'
  "temporary_access" boolean DEFAULT false,
  "access_start_date" timestamp,
  "access_end_date" timestamp,
  
  -- Request workflow
  "status" request_status DEFAULT 'pending',
  "submitted_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "required_approvers" jsonb DEFAULT '[]', -- Array of required approver user IDs
  "current_approver" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "approval_workflow_id" integer, -- Reference to approval workflow if exists
  
  -- Approval tracking
  "approved_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "approved_at" timestamp,
  "rejected_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "rejected_at" timestamp,
  "rejection_reason" text,
  "cancelled_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "cancelled_at" timestamp,
  "cancellation_reason" text,
  
  -- Implementation tracking
  "implemented_by" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "implemented_at" timestamp,
  "implementation_notes" text,
  "expires_at" timestamp, -- For temporary access requests
  
  -- Additional context
  "manager_id" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "department" varchar(100),
  "cost_center" varchar(100),
  "project_code" varchar(100),
  "compliance_requirements" jsonb DEFAULT '[]', -- Array of compliance requirements
  "risk_assessment" varchar(50), -- 'low', 'medium', 'high', 'critical'
  
  -- Attachments and evidence
  "attachments" jsonb DEFAULT '[]', -- Array of attachment references
  "supporting_documents" jsonb DEFAULT '[]', -- Array of supporting document references
  
  -- Audit and metadata
  "metadata" jsonb DEFAULT '{}',
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Request approvals table for multi-step approval workflows
CREATE TABLE IF NOT EXISTS "request_approvals" (
  "id" SERIAL PRIMARY KEY,
  "request_id" integer REFERENCES "permission_requests"("id") ON DELETE CASCADE,
  "approver_id" integer REFERENCES "users"("id") ON DELETE CASCADE,
  "approval_step" integer NOT NULL, -- Order of approval in workflow
  "approval_level" varchar(50), -- 'manager', 'security', 'compliance', 'admin'
  
  -- Approval details
  "status" varchar(50) NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'delegated'
  "decision_date" timestamp,
  "comments" text,
  "conditions" text, -- Any conditions attached to approval
  "delegated_to" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "delegation_reason" text,
  
  -- Timing
  "due_date" timestamp,
  "reminder_sent" boolean DEFAULT false,
  "escalated" boolean DEFAULT false,
  "escalated_to" integer REFERENCES "users"("id") ON DELETE SET NULL,
  "escalation_date" timestamp,
  
  -- Audit trail
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE("request_id", "approver_id", "approval_step")
);

-- Request comments/history table
CREATE TABLE IF NOT EXISTS "request_comments" (
  "id" SERIAL PRIMARY KEY,
  "request_id" integer REFERENCES "permission_requests"("id") ON DELETE CASCADE,
  "user_id" integer REFERENCES "users"("id") ON DELETE CASCADE,
  
  -- Comment details
  "comment_type" varchar(50) NOT NULL, -- 'comment', 'status_change', 'approval', 'rejection', 'question'
  "comment" text NOT NULL,
  "is_internal" boolean DEFAULT false, -- Internal comments not visible to requester
  "visibility" varchar(50) DEFAULT 'all', -- 'all', 'approvers_only', 'admins_only'
  
  -- Attachments
  "attachments" jsonb DEFAULT '[]',
  
  -- Audit trail
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- User access dashboard view for quick access status
CREATE TABLE IF NOT EXISTS "user_access_summary" (
  "id" SERIAL PRIMARY KEY,
  "user_id" integer REFERENCES "users"("id") ON DELETE CASCADE,
  "company_id" integer REFERENCES "companies"("id") ON DELETE CASCADE,
  
  -- Access summary
  "total_permissions" integer DEFAULT 0,
  "active_permissions" integer DEFAULT 0,
  "temporary_permissions" integer DEFAULT 0,
  "pending_requests" integer DEFAULT 0,
  "recent_access_changes" integer DEFAULT 0,
  
  -- Risk and compliance
  "risk_score" integer DEFAULT 0, -- 0-100 risk score
  "compliance_status" varchar(50) DEFAULT 'compliant',
  "last_access_review" timestamp,
  "next_access_review" timestamp,
  
  -- Activity summary
  "last_login" timestamp,
  "last_permission_use" timestamp,
  "failed_access_attempts" integer DEFAULT 0,
  
  -- Metadata
  "summary_data" jsonb DEFAULT '{}', -- Additional summary information
  "last_updated" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE("user_id", "company_id")
);

-- Create indexes for performance
CREATE INDEX idx_permission_requests_requester ON permission_requests(requester_id);
CREATE INDEX idx_permission_requests_company ON permission_requests(company_id);
CREATE INDEX idx_permission_requests_status ON permission_requests(status);
CREATE INDEX idx_permission_requests_type ON permission_requests(request_type);
CREATE INDEX idx_permission_requests_submitted ON permission_requests(submitted_at);
CREATE INDEX idx_permission_requests_approver ON permission_requests(current_approver);
CREATE INDEX idx_permission_requests_priority ON permission_requests(priority);
CREATE INDEX idx_permission_requests_expires ON permission_requests(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX idx_request_approvals_request ON request_approvals(request_id);
CREATE INDEX idx_request_approvals_approver ON request_approvals(approver_id);
CREATE INDEX idx_request_approvals_status ON request_approvals(status);
CREATE INDEX idx_request_approvals_step ON request_approvals(approval_step);
CREATE INDEX idx_request_approvals_due ON request_approvals(due_date) WHERE due_date IS NOT NULL;

CREATE INDEX idx_request_comments_request ON request_comments(request_id);
CREATE INDEX idx_request_comments_user ON request_comments(user_id);
CREATE INDEX idx_request_comments_type ON request_comments(comment_type);
CREATE INDEX idx_request_comments_created ON request_comments(created_at);

CREATE INDEX idx_user_access_summary_user ON user_access_summary(user_id);
CREATE INDEX idx_user_access_summary_company ON user_access_summary(company_id);
CREATE INDEX idx_user_access_summary_updated ON user_access_summary(last_updated);

-- Add comments for documentation
COMMENT ON TABLE permission_requests IS 'User-submitted permission and access requests';
COMMENT ON TABLE request_approvals IS 'Multi-step approval workflow for permission requests';
COMMENT ON TABLE request_comments IS 'Comments and history for permission requests';
COMMENT ON TABLE user_access_summary IS 'Cached summary of user access status for dashboard';

COMMENT ON COLUMN permission_requests.request_type IS 'Type of access request being made';
COMMENT ON COLUMN permission_requests.business_justification IS 'Business reason for the access request';
COMMENT ON COLUMN permission_requests.temporary_access IS 'Whether this is a temporary access request';
COMMENT ON COLUMN permission_requests.access_scope IS 'Scope of access being requested';
COMMENT ON COLUMN permission_requests.risk_assessment IS 'Risk level assessment for the request';

COMMENT ON COLUMN request_approvals.approval_step IS 'Order of approval in multi-step workflow';
COMMENT ON COLUMN request_approvals.approval_level IS 'Level/type of approver (manager, security, etc.)';
COMMENT ON COLUMN request_approvals.conditions IS 'Any conditions attached to the approval';

COMMENT ON COLUMN request_comments.comment_type IS 'Type of comment (comment, status_change, approval, etc.)';
COMMENT ON COLUMN request_comments.is_internal IS 'Whether comment is internal and not visible to requester';
COMMENT ON COLUMN request_comments.visibility IS 'Who can see this comment';

COMMENT ON COLUMN user_access_summary.risk_score IS 'User risk score from 0-100 based on permissions and activity';
COMMENT ON COLUMN user_access_summary.compliance_status IS 'Current compliance status of user access';
