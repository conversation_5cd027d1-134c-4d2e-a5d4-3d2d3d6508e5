#!/usr/bin/env node
/**
 * Tool: Update Loan Reference Codes Programmatically
 * Purpose: Update all existing loans using the application's storage layer
 * Usage: node tools/update-loan-reference-codes-programmatic.js
 * 
 * This tool uses the application's storage layer rather than direct SQL queries,
 * ensuring business logic and validation rules are respected.
 */

import { db } from '../server/db.js';
import { loans } from '../shared/schema.js';
import { LoanStorage } from '../server/storage/loan.storage.js';
import { setupEnvironment } from '../scripts/utils/env-loader.js';

// Load environment variables
setupEnvironment(['DATABASE_URL']);

// Initialize the loan storage
const loanStorage = new LoanStorage();

async function updateLoanReferenceCodes() {
  console.log('=== PROGRAMMATIC LOAN REFERENCE CODES UPDATE ===\n');

  try {
    console.log('🔍 Fetching all loans from database...');
    
    // Get all loans from the database
    const allLoans = await db.select().from(loans);
    console.log(`📊 Found ${allLoans.length} loans in the database`);

    if (allLoans.length === 0) {
      console.log('ℹ️  No loans to update. Exiting.');
      return;
    }

    // Analyze current state
    const loansWithoutRefCode = allLoans.filter(loan => !loan.loan_reference_code);
    const loansWithRefCode = allLoans.filter(loan => loan.loan_reference_code);
    
    console.log(`📈 Analysis:`);
    console.log(`   - Loans without reference code: ${loansWithoutRefCode.length}`);
    console.log(`   - Loans with reference code: ${loansWithRefCode.length}`);

    if (loansWithoutRefCode.length === 0) {
      console.log('✅ All loans already have reference codes. No updates needed.');
      return;
    }

    console.log(`\n🔄 Updating ${loansWithoutRefCode.length} loans...`);

    // Update each loan to have an empty value in the loan_reference_code column
    let updatedCount = 0;
    let errorCount = 0;
    const errors = [];

    for (const loan of loansWithoutRefCode) {
      try {
        // Use the storage layer to update the loan
        await loanStorage.updateLoan(
          loan.id,
          loan.company_id,
          { loan_reference_code: '' }
        );

        updatedCount++;
        
        // Log progress every 10 updates
        if (updatedCount % 10 === 0) {
          console.log(`   ✅ Updated ${updatedCount}/${loansWithoutRefCode.length} loans...`);
        }
        
      } catch (error) {
        errorCount++;
        const errorInfo = {
          loanId: loan.id,
          companyId: loan.company_id,
          error: error.message
        };
        errors.push(errorInfo);
        
        console.log(`   ❌ Error updating loan ID ${loan.id}: ${error.message}`);
      }
    }

    console.log('\n📋 UPDATE SUMMARY:');
    console.log(`   ✅ Successfully updated: ${updatedCount} loans`);
    console.log(`   ❌ Errors encountered: ${errorCount} loans`);
    console.log(`   📊 Total processed: ${loansWithoutRefCode.length} loans`);

    if (errors.length > 0) {
      console.log('\n⚠️  DETAILED ERROR REPORT:');
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. Loan ID ${error.loanId} (Company ${error.companyId}): ${error.error}`);
      });
    }

    // Final verification
    console.log('\n🔍 Performing final verification...');
    const finalLoans = await db.select().from(loans);
    const finalLoansWithoutRefCode = finalLoans.filter(loan => !loan.loan_reference_code);
    
    console.log(`📊 Final state:`);
    console.log(`   - Total loans: ${finalLoans.length}`);
    console.log(`   - Loans without reference code: ${finalLoansWithoutRefCode.length}`);
    console.log(`   - Loans with reference code: ${finalLoans.length - finalLoansWithoutRefCode.length}`);

    if (finalLoansWithoutRefCode.length === 0) {
      console.log('\n🎉 SUCCESS: All loans now have reference codes!');
    } else {
      console.log(`\n⚠️  WARNING: ${finalLoansWithoutRefCode.length} loans still without reference codes`);
    }

  } catch (error) {
    console.log('\n❌ CRITICAL ERROR:');
    console.log(`   ${error.message}`);
    console.log('\n📋 Stack trace:');
    console.log(error.stack);
  } finally {
    console.log('\n🔚 Update process completed.');
    process.exit(0);
  }
}

// Run the update
updateLoanReferenceCodes();
