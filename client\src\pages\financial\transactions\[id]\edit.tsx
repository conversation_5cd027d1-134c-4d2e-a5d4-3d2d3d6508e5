import { useState, useEffect } from 'react';
import { useRoute, useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useContextData } from '@/lib/useContextData';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// UI Components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

// Icons
import {
  ArrowLeft, Check, CalendarIcon, AlertCircle
} from 'lucide-react';

interface Account {
  id: number;
  account_code: string;
  account_name: string;
  account_type: string;
}

interface Transaction {
  id: number;
  account_id: number;
  company_id: number;
  transaction_date: string;
  transaction_type: 'debit' | 'credit';
  amount: number;
  description: string;
  reference_type?: string | null;
  reference_id?: number | null;
  created_at: string;
  updated_at: string;
  // Additional fields from joins
  account?: {
    id: number;
    account_code: string;
    account_name: string;
    account_type: string;
  };
}

// Form validation schema
const formSchema = z.object({
  transaction_date: z.date({
    required_error: "Transaction date is required",
  }),
  description: z.string().min(1, "Description is required"),
  account_id: z.number({
    required_error: "Account is required",
  }),
  amount: z.coerce.number().positive("Amount must be greater than zero"),
  transaction_type: z.enum(['debit', 'credit'], {
    required_error: "Transaction type is required",
  }),
  reference_type: z.string().optional().nullable(),
  reference_id: z.coerce.number().optional().nullable(),
});

type FormValues = z.infer<typeof formSchema>;

export default function EditTransaction() {
  const [, navigate] = useLocation();
  const [, params] = useRoute('/financial/transactions/:id/edit');
  const { companyId } = useContextData();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const transactionId = params?.id ? parseInt(params.id, 10) : null;

  // Fetch transaction details
  const {
    data: transaction,
    isLoading: isLoadingTransaction,
    isError: isTransactionError,
    error: transactionError
  } = useQuery<Transaction>({
    queryKey: ['/api/companies', companyId, 'transactions', transactionId],
    queryFn: async () => {
      if (!companyId || !transactionId) return null;

      const response = await apiRequest('GET', `/api/companies/${companyId}/transactions/${transactionId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch transaction details');
      }

      return response.json();
    },
    enabled: !!companyId && !!transactionId
  });

  // Fetch accounts for the dropdown
  const {
    data: accounts = [],
    isLoading: isLoadingAccounts
  } = useQuery<Account[]>({
    queryKey: ['/api/companies', companyId, 'accounts'],
    queryFn: async () => {
      if (!companyId) return [];

      const response = await apiRequest('GET', `/api/companies/${companyId}/accounts`);

      if (!response.ok) {
        throw new Error('Failed to fetch accounts');
      }

      return response.json();
    },
    enabled: !!companyId
  });

  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      transaction_date: undefined,
      description: '',
      account_id: undefined,
      amount: undefined,
      transaction_type: undefined,
      reference_type: null,
      reference_id: null,
    },
  });

  // Update form values when transaction data is loaded
  useEffect(() => {
    if (transaction) {
      form.reset({
        transaction_date: new Date(transaction.transaction_date),
        description: transaction.description,
        account_id: transaction.account_id,
        amount: transaction.amount,
        transaction_type: transaction.transaction_type,
        reference_type: transaction.reference_type,
        reference_id: transaction.reference_id,
      });
    }
  }, [transaction, form]);

  // Update transaction mutation
  const updateMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      if (!companyId || !transactionId) {
        throw new Error('Missing required parameters');
      }

      const response = await apiRequest(
        'PUT',
        `/api/companies/${companyId}/transactions/${transactionId}`,
        data
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update transaction');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'transactions'] });
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'transactions', transactionId] });

      toast({
        title: "Transaction updated",
        description: "The transaction has been successfully updated.",
        variant: "default",
      });

      // Navigate back to transaction details page
      navigate(`/financial/transactions/${transactionId}`);
    },
    onError: (error: any) => {
      // Use the error message from the API response
      const errorMessage = error.message || "Failed to update transaction";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Form submission handler
  const onSubmit = (values: FormValues) => {
    updateMutation.mutate(values);
  };

  // Handle loading state
  if (isLoadingTransaction || isLoadingAccounts) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center min-h-[60vh]">
        <Spinner size="lg" />
      </div>
    );
  }

  // Handle error state
  if (isTransactionError || !transaction) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>Error Loading Transaction</CardTitle>
            <CardDescription>
              {transactionError instanceof Error ? transactionError.message : "Failed to load transaction details"}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center p-6">
            <Button onClick={() => navigate('/financial/transactions')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Transactions
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => navigate(`/financial/transactions/${transactionId}`)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Transaction
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Edit Transaction</CardTitle>
          <CardDescription>Update transaction details for transaction #{transaction.id}</CardDescription>
        </CardHeader>

        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Transaction Date */}
                <FormField
                  control={form.control}
                  name="transaction_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Transaction Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={`
                                w-full pl-3 text-left font-normal
                                ${!field.value && "text-muted-foreground"}
                              `}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date()
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Account */}
                <FormField
                  control={form.control}
                  name="account_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(parseInt(value, 10))}
                        defaultValue={field.value?.toString()}
                        value={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an account" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {accounts.map((account) => (
                            <SelectItem key={account.id} value={account.id.toString()}>
                              {account.account_name} ({account.account_code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Amount */}
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="Enter amount"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Transaction Type */}
                <FormField
                  control={form.control}
                  name="transaction_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Transaction Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select transaction type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="debit">Debit</SelectItem>
                          <SelectItem value="credit">Credit</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter transaction description"
                          className="h-24"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Reference Type and ID */}
                <FormField
                  control={form.control}
                  name="reference_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference Type (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value || undefined}
                        value={field.value || undefined}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select reference type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="loan">Loan</SelectItem>
                          <SelectItem value="collection">Collection</SelectItem>
                          <SelectItem value="expense">Expense</SelectItem>
                          <SelectItem value="payment">Payment</SelectItem>
                          <SelectItem value="invoice">Invoice</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch('reference_type') && (
                  <FormField
                    control={form.control}
                    name="reference_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reference ID (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter reference ID"
                            {...field}
                            value={field.value || ''}
                            onChange={(e) =>
                              field.onChange(e.target.value ? parseInt(e.target.value, 10) : null)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate(`/financial/transactions/${transactionId}`)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateMutation.isPending}
                >
                  {updateMutation.isPending ? (
                    <>
                      <Spinner className="mr-2 h-4 w-4" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Update Transaction
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}