// List of standard field IDs (these are fixed fields that cannot be modified)
export const STANDARD_FIELD_IDS = [
  'customer_id',
  'amount',
  'interest_rate',
  'interest_type',
  'term',
  'start_date',
  'end_date',
  'notes'
];

// Standard loan fields with their default definitions
export function getStandardLoanFields() {
  return [
    {
      id: 'customer_id',
      type: 'select',
      label: 'Customer',
      required: true,
      isSystem: true,
      placeholder: 'Select Customer',
      helpText: 'Select the customer for this loan'
    },
    {
      id: 'amount',
      type: 'number',
      label: 'Loan Amount',
      required: true,
      isSystem: true,
      placeholder: 'Enter loan amount',
      min: 0
    },
    {
      id: 'interest_rate',
      type: 'number',
      label: 'Interest Rate (%)',
      required: true,
      isSystem: true,
      placeholder: 'Enter interest rate',
      min: 0,
      max: 100
    },
    {
      id: 'interest_type',
      type: 'select',
      label: 'Interest Type',
      required: true,
      isSystem: true,
      options: [
        { label: 'Simple Interest', value: 'simple' },
        { label: 'Compound Interest', value: 'compound' }
      ]
    },
    {
      id: 'term',
      type: 'number',
      label: 'Term (Months)',
      required: true,
      isSystem: true,
      placeholder: 'Enter term in months',
      min: 1
    },
    {
      id: 'start_date',
      type: 'date',
      label: 'Start Date',
      required: true,
      isSystem: true,
      placeholder: 'Select start date'
    },
    {
      id: 'end_date',
      type: 'date',
      label: 'End Date',
      required: true,
      isSystem: true,
      placeholder: 'Select end date'
    },
    {
      id: 'notes',
      type: 'textarea',
      label: 'Notes',
      required: false,
      isSystem: true,
      placeholder: 'Enter any additional notes'
    }
  ];
};

// Function to check if a field ID corresponds to a standard field
export function isStandardField(fieldId: string): boolean {
  if (!fieldId) return false;
  return STANDARD_FIELD_IDS.includes(fieldId);
}

// Function to check if a field should be treated as a specific type based on its ID or label
export function detectFieldType(field: { id: string; label?: string; type?: string }): string | null {
  const id = field.id?.toLowerCase();
  const label = field.label?.toLowerCase() || '';
  
  // Date fields detection
  if (
    id === 'start_date' ||
    id === 'end_date' ||
    id === 'due_date' ||
    label.includes('date') ||
    label.includes('deadline')
  ) {
    return 'date';
  }
  
  // Numeric fields detection
  if (
    id === 'amount' ||
    id === 'interest_rate' ||
    id === 'term' ||
    label.includes('amount') ||
    label.includes('rate') ||
    label.includes('term') ||
    label.includes('months') ||
    label.includes('years')
  ) {
    return 'number';
  }
  
  // Selection fields detection
  if (
    id === 'customer_id' ||
    id === 'agent_id' ||
    id === 'interest_type' ||
    label.includes('select') ||
    label.includes('type')
  ) {
    return 'select';
  }
  
  // Text area fields detection
  if (
    id === 'notes' ||
    id === 'description' ||
    label.includes('notes') ||
    label.includes('description') ||
    label.includes('comments')
  ) {
    return 'textarea';
  }
  
  // Default to null (use the field's own type)
  return null;
}

// Function to get standard field definition by name
export function getStandardFieldByName(fieldName: string) {
  const standardFields = getStandardLoanFields();
  return standardFields.find(field => field.id === fieldName);
}