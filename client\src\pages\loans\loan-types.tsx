import React, { useState, useEffect, useMemo } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { useContextData } from "@/lib/useContextData";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { ArrowLeft } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";

interface FormTemplate {
  id: number;
  name: string;
  description?: string;
  company_id: number;
  branch_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface LoanConfiguration {
  id: number;
  company_id: number;
  template_id: number;
  branch_id: number | null;
  is_active: boolean;
  order: number;
  created_at: string;
  updated_at: string;
  template?: FormTemplate;
}

export default function LoanTypeSelection() {
  const { companyId } = useContextData(); // Default to 1 for SaaS admin if no company_id
  const [, navigate] = useLocation();

  // Fetch active templates directly (new simplified approach)
  const { data: formTemplates = [], isLoading: isLoadingTemplates } = useQuery<FormTemplate[]>({
    queryKey: [`/api/companies/${companyId}/active-templates`],
    enabled: !!companyId,
  });

  // For backward compatibility - still fetch configurations too
  const { data: loanConfigurations = [], isLoading: isLoadingConfigurations } = useQuery<(LoanConfiguration & { template: FormTemplate })[]>({
    queryKey: [`/api/companies/${companyId}/loan-configurations/active`],
    enabled: !!companyId,
  });

  // Merge templates from both sources to ensure we don't miss any during transition
  const allTemplates = useMemo(() => {
    // Get templates from configurations (legacy approach)
    const templatesFromConfigs = loanConfigurations
      ?.filter(config => config.is_active && config.template?.is_active)
      ?.map(config => config.template) || [];

    // Combine with directly fetched templates, avoiding duplicates by ID
    const templateMap = new Map<number, FormTemplate>();

    // Add templates from direct query first
    formTemplates.forEach(template => {
      templateMap.set(template.id, template);
    });

    // Add templates from configurations if not already present
    templatesFromConfigs.forEach(template => {
      if (!templateMap.has(template.id)) {
        templateMap.set(template.id, template);
      }
    });

    return Array.from(templateMap.values());
  }, [formTemplates, loanConfigurations]);

  // Debug: Log template data
  useEffect(() => {
    console.log("Active templates:", formTemplates);
    console.log("All available templates:", allTemplates);
  }, [formTemplates, allTemplates]);

  return (
    <div className="container mx-auto py-6 max-w-5xl">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate("/loans")} className="text-sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Loans
          </Button>
          <h1 className="text-2xl font-bold">Select Loan Type</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Available Loan Types</CardTitle>
          <CardDescription>
            Choose a loan type to continue
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {isLoadingTemplates || isLoadingConfigurations ? (
              <div className="flex flex-col items-center justify-center p-12">
                <Spinner size="lg" />
                <p className="mt-4 text-muted-foreground">Loading loan types...</p>
              </div>
            ) : allTemplates.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-12 border rounded-lg bg-muted/10">
                <h3 className="text-lg font-medium mb-2">No loan types found</h3>
                <p className="text-muted-foreground text-center mb-4">
                  No loan types are configured or active.
                </p>
                <Button onClick={() => navigate("/loan-form-builder")}>
                  Create Loan Type
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {allTemplates.map((template) => (
                  <Card
                    key={template.id}
                    className="hover:shadow-md transition-all hover:border-blue-300 hover:scale-[1.02] cursor-pointer"
                    onClick={() => {
                      console.log("Navigating to create loan with type", template.id);
                      navigate(`/loans/create?templateId=${template.id}`);
                    }}
                  >
                    <CardHeader className="pb-4">
                      <div className="flex justify-between items-start gap-2">
                        <CardTitle className="text-xl truncate">{template.name}</CardTitle>
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full whitespace-nowrap">
                          Select
                        </span>
                      </div>
                      {template.description && (
                        <CardDescription>{template.description}</CardDescription>
                      )}
                    </CardHeader>
                    <CardFooter className="pt-0">
                      <Button
                        className="w-full"
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent duplicate navigation
                          console.log("Button clicked, navigating to create loan");
                          navigate(`/loans/create?templateId=${template.id}`);
                        }}
                      >
                        Create Loan
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}