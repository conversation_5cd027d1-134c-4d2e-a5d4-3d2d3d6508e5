import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { RoleHierarchyService } from '../roleHierarchyService';

// Mock the database
vi.mock('../../db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    orderBy: vi.fn().mockReturnThis(),
    innerJoin: vi.fn().mockReturnThis(),
  }
}));

// Mock error logger
vi.mock('../../utils/errorLogger', () => ({
  default: {
    logInfo: vi.fn(),
    logError: vi.fn(),
  }
}));

describe('RoleHierarchyService', () => {
  let service: RoleHierarchyService;
  let mockDb: any;

  beforeEach(() => {
    service = new RoleHierarchyService();
    mockDb = vi.mocked(require('../../db').db);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('createRoleHierarchy', () => {
    it('should create role hierarchy successfully', async () => {
      // Mock validateRolesExist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Both roles exist

      // Mock wouldCreateCircularDependency check
      mockDb.select.mockResolvedValueOnce([]); // No descendants

      // Mock getHierarchyRelationship check
      mockDb.select.mockResolvedValueOnce([]); // No existing relationship

      // Mock insert operation
      const mockHierarchy = {
        id: 1,
        parent_role_id: 1,
        child_role_id: 2,
        inheritance_type: 'inherit',
        created_at: new Date(),
        updated_at: new Date()
      };
      mockDb.insert.mockResolvedValueOnce([mockHierarchy]);

      const result = await service.createRoleHierarchy(1, 2, 'inherit');

      expect(result).toEqual(mockHierarchy);
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it('should throw error for circular dependency', async () => {
      // Mock validateRolesExist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Both roles exist

      // Mock circular dependency detection
      mockDb.select.mockResolvedValueOnce([{ child_role_id: 1 }]); // Would create circular dependency

      await expect(service.createRoleHierarchy(1, 2)).rejects.toThrow('Cannot create circular role hierarchy');
    });

    it('should throw error for existing relationship', async () => {
      // Mock validateRolesExist
      mockDb.select.mockResolvedValueOnce([{ id: 1 }, { id: 2 }]); // Both roles exist

      // Mock wouldCreateCircularDependency check
      mockDb.select.mockResolvedValueOnce([]); // No circular dependency

      // Mock existing relationship
      mockDb.select.mockResolvedValueOnce([{ id: 1, parent_role_id: 1, child_role_id: 2 }]);

      await expect(service.createRoleHierarchy(1, 2)).rejects.toThrow('Role hierarchy relationship already exists');
    });

    it('should throw error for non-existent roles', async () => {
      // Mock validateRolesExist - only one role exists
      mockDb.select.mockResolvedValueOnce([{ id: 1 }]); // Only parent role exists

      await expect(service.createRoleHierarchy(1, 2)).rejects.toThrow('Roles not found: 2');
    });
  });

  describe('removeRoleHierarchy', () => {
    it('should remove role hierarchy successfully', async () => {
      mockDb.delete.mockResolvedValueOnce({ rowCount: 1 });

      const result = await service.removeRoleHierarchy(1, 2);

      expect(result).toBe(true);
      expect(mockDb.delete).toHaveBeenCalled();
    });

    it('should return false when relationship does not exist', async () => {
      mockDb.delete.mockResolvedValueOnce({ rowCount: 0 });

      const result = await service.removeRoleHierarchy(1, 2);

      expect(result).toBe(false);
    });
  });

  describe('updateInheritanceType', () => {
    it('should update inheritance type successfully', async () => {
      const mockUpdated = {
        id: 1,
        parent_role_id: 1,
        child_role_id: 2,
        inheritance_type: 'override',
        created_at: new Date(),
        updated_at: new Date()
      };
      mockDb.update.mockResolvedValueOnce([mockUpdated]);

      const result = await service.updateInheritanceType(1, 2, 'override');

      expect(result).toEqual(mockUpdated);
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('should return null when relationship not found', async () => {
      mockDb.update.mockResolvedValueOnce([]);

      const result = await service.updateInheritanceType(1, 2, 'override');

      expect(result).toBeNull();
    });
  });

  describe('getParentRoles', () => {
    it('should return parent roles', async () => {
      const mockParents = [
        { id: 1, parent_role_id: 1, child_role_id: 2, inheritance_type: 'inherit' }
      ];
      mockDb.select.mockResolvedValueOnce(mockParents);

      const result = await service.getParentRoles(2);

      expect(result).toEqual(mockParents);
    });
  });

  describe('getChildRoles', () => {
    it('should return child roles', async () => {
      const mockChildren = [
        { id: 1, parent_role_id: 1, child_role_id: 2, inheritance_type: 'inherit' }
      ];
      mockDb.select.mockResolvedValueOnce(mockChildren);

      const result = await service.getChildRoles(1);

      expect(result).toEqual(mockChildren);
    });
  });

  describe('wouldCreateCircularDependency', () => {
    it('should return true for same parent and child', async () => {
      const result = await service.wouldCreateCircularDependency(1, 1);
      expect(result).toBe(true);
    });

    it('should return true when parent is descendant of child', async () => {
      // Mock getAllDescendants to return parent as descendant
      mockDb.select.mockResolvedValueOnce([{ child_role_id: 1 }]);

      const result = await service.wouldCreateCircularDependency(1, 2);
      expect(result).toBe(true);
    });

    it('should return false when no circular dependency', async () => {
      // Mock getAllDescendants to return empty array
      mockDb.select.mockResolvedValueOnce([]);

      const result = await service.wouldCreateCircularDependency(1, 2);
      expect(result).toBe(false);
    });
  });

  describe('calculateEffectivePermissions', () => {
    it('should calculate effective permissions with inheritance', async () => {
      const mockRole = { id: 1, name: 'Child Role', company_id: 1 };
      const mockDirectPermissions = ['permission1', 'permission2'];
      const mockAncestors = [
        { parent_role_id: 2, child_role_id: 1, inheritance_type: 'inherit' }
      ];
      const mockParentRole = { id: 2, name: 'Parent Role', company_id: 1 };
      const mockParentPermissions = ['permission3', 'permission4'];

      // Mock role lookup
      mockDb.select.mockResolvedValueOnce([mockRole]);

      // Mock direct permissions
      mockDb.select.mockResolvedValueOnce(
        mockDirectPermissions.map(code => ({ code }))
      );

      // Mock ancestors
      mockDb.select.mockResolvedValueOnce(mockAncestors);

      // Mock parent role lookup
      mockDb.select.mockResolvedValueOnce([mockParentRole]);

      // Mock parent permissions
      mockDb.select.mockResolvedValueOnce(
        mockParentPermissions.map(code => ({ code }))
      );

      const result = await service.calculateEffectivePermissions(1);

      expect(result.roleId).toBe(1);
      expect(result.roleName).toBe('Child Role');
      expect(result.directPermissions).toEqual(mockDirectPermissions);
      expect(result.inheritedPermissions).toEqual(mockParentPermissions);
      expect(result.effectivePermissions).toEqual([
        ...mockDirectPermissions,
        ...mockParentPermissions
      ]);
    });

    it('should handle override inheritance type', async () => {
      const mockRole = { id: 1, name: 'Child Role', company_id: 1 };
      const mockDirectPermissions = ['permission1', 'permission2'];
      const mockAncestors = [
        { parent_role_id: 2, child_role_id: 1, inheritance_type: 'override' }
      ];
      const mockParentRole = { id: 2, name: 'Parent Role', company_id: 1 };
      const mockParentPermissions = ['permission3', 'permission4'];

      // Mock role lookup
      mockDb.select.mockResolvedValueOnce([mockRole]);

      // Mock direct permissions
      mockDb.select.mockResolvedValueOnce(
        mockDirectPermissions.map(code => ({ code }))
      );

      // Mock ancestors
      mockDb.select.mockResolvedValueOnce(mockAncestors);

      // Mock parent role lookup
      mockDb.select.mockResolvedValueOnce([mockParentRole]);

      // Mock parent permissions
      mockDb.select.mockResolvedValueOnce(
        mockParentPermissions.map(code => ({ code }))
      );

      const result = await service.calculateEffectivePermissions(1);

      expect(result.inheritedPermissions).toEqual(mockParentPermissions);
      expect(result.effectivePermissions).toEqual([
        ...mockDirectPermissions,
        ...mockParentPermissions
      ]);
    });

    it('should handle deny inheritance type', async () => {
      const mockRole = { id: 1, name: 'Child Role', company_id: 1 };
      const mockDirectPermissions = ['permission1', 'permission2'];
      const mockAncestors = [
        { parent_role_id: 2, child_role_id: 1, inheritance_type: 'deny' }
      ];
      const mockParentRole = { id: 2, name: 'Parent Role', company_id: 1 };
      const mockParentPermissions = ['permission1', 'permission3']; // permission1 should be denied

      // Mock role lookup
      mockDb.select.mockResolvedValueOnce([mockRole]);

      // Mock direct permissions
      mockDb.select.mockResolvedValueOnce(
        mockDirectPermissions.map(code => ({ code }))
      );

      // Mock ancestors
      mockDb.select.mockResolvedValueOnce(mockAncestors);

      // Mock parent role lookup
      mockDb.select.mockResolvedValueOnce([mockParentRole]);

      // Mock parent permissions
      mockDb.select.mockResolvedValueOnce(
        mockParentPermissions.map(code => ({ code }))
      );

      const result = await service.calculateEffectivePermissions(1);

      expect(result.deniedPermissions).toEqual(mockParentPermissions);
      expect(result.effectivePermissions).toEqual(['permission2']); // permission1 denied
    });

    it('should throw error for non-existent role', async () => {
      mockDb.select.mockResolvedValueOnce([]); // Role not found

      await expect(service.calculateEffectivePermissions(999)).rejects.toThrow('Role 999 not found');
    });
  });

  describe('getAllDescendants', () => {
    it('should return all descendants recursively', async () => {
      const mockDirectChildren = [
        { child_role_id: 2, parent_role_id: 1, inheritance_type: 'inherit' }
      ];
      const mockGrandChildren = [
        { child_role_id: 3, parent_role_id: 2, inheritance_type: 'inherit' }
      ];

      // First call for direct children
      mockDb.select.mockResolvedValueOnce(mockDirectChildren);
      // Second call for grandchildren
      mockDb.select.mockResolvedValueOnce(mockGrandChildren);
      // Third call for great-grandchildren (empty)
      mockDb.select.mockResolvedValueOnce([]);

      const result = await service.getAllDescendants(1);

      expect(result).toHaveLength(2);
      expect(result).toEqual([...mockDirectChildren, ...mockGrandChildren]);
    });

    it('should prevent infinite loops with visited set', async () => {
      const visited = new Set([1]);

      const result = await service.getAllDescendants(1, visited);

      expect(result).toEqual([]);
      expect(mockDb.select).not.toHaveBeenCalled();
    });
  });

  describe('getAllAncestors', () => {
    it('should return all ancestors recursively', async () => {
      const mockDirectParents = [
        { parent_role_id: 2, child_role_id: 1, inheritance_type: 'inherit' }
      ];
      const mockGrandParents = [
        { parent_role_id: 3, child_role_id: 2, inheritance_type: 'inherit' }
      ];

      // First call for direct parents
      mockDb.select.mockResolvedValueOnce(mockDirectParents);
      // Second call for grandparents
      mockDb.select.mockResolvedValueOnce(mockGrandParents);
      // Third call for great-grandparents (empty)
      mockDb.select.mockResolvedValueOnce([]);

      const result = await service.getAllAncestors(1);

      expect(result).toHaveLength(2);
      expect(result).toEqual([...mockDirectParents, ...mockGrandParents]);
    });

    it('should prevent infinite loops with visited set', async () => {
      const visited = new Set([1]);

      const result = await service.getAllAncestors(1, visited);

      expect(result).toEqual([]);
      expect(mockDb.select).not.toHaveBeenCalled();
    });
  });

  describe('Role Template Management', () => {
    describe('getRoleTemplates', () => {
      it('should return all role templates', async () => {
        const mockTemplates = [
          { id: 1, name: 'Loan Officer', industry: 'financial_services' },
          { id: 2, name: 'Manager', industry: 'financial_services' }
        ];
        mockDb.select.mockResolvedValueOnce(mockTemplates);

        const result = await service.getRoleTemplates();

        expect(result).toEqual(mockTemplates);
      });

      it('should filter by industry', async () => {
        const mockTemplates = [
          { id: 1, name: 'Loan Officer', industry: 'financial_services' }
        ];
        mockDb.select.mockResolvedValueOnce(mockTemplates);

        const result = await service.getRoleTemplates('financial_services');

        expect(result).toEqual(mockTemplates);
      });

      it('should filter by system templates', async () => {
        const mockTemplates = [
          { id: 1, name: 'System Admin', is_system: true }
        ];
        mockDb.select.mockResolvedValueOnce(mockTemplates);

        const result = await service.getRoleTemplates(undefined, true);

        expect(result).toEqual(mockTemplates);
      });
    });

    describe('getRoleTemplate', () => {
      it('should return specific role template', async () => {
        const mockTemplate = { id: 1, name: 'Loan Officer', industry: 'financial_services' };
        mockDb.select.mockResolvedValueOnce([mockTemplate]);

        const result = await service.getRoleTemplate(1);

        expect(result).toEqual(mockTemplate);
      });

      it('should return null for non-existent template', async () => {
        mockDb.select.mockResolvedValueOnce([]);

        const result = await service.getRoleTemplate(999);

        expect(result).toBeNull();
      });
    });

    describe('createRoleTemplate', () => {
      it('should create role template successfully', async () => {
        const templateData = {
          name: 'Test Template',
          description: 'Test description',
          template_config: {
            permissions: ['loan_create', 'customer_view'],
            description: 'Test role'
          },
          industry: 'financial_services',
          is_system: false
        };
        const mockCreated = { id: 1, ...templateData, created_at: new Date(), updated_at: new Date() };
        mockDb.insert.mockResolvedValueOnce([mockCreated]);

        const result = await service.createRoleTemplate(templateData);

        expect(result).toEqual(mockCreated);
      });

      it('should throw error for invalid template config', async () => {
        const templateData = {
          name: 'Test Template',
          template_config: { invalid: 'config' }, // Missing permissions and description
          industry: 'financial_services'
        };

        await expect(service.createRoleTemplate(templateData)).rejects.toThrow('Template configuration must include permissions array');
      });
    });

    describe('updateRoleTemplate', () => {
      it('should update role template successfully', async () => {
        const updateData = { name: 'Updated Template' };
        const mockUpdated = { id: 1, ...updateData, updated_at: new Date() };
        mockDb.update.mockResolvedValueOnce([mockUpdated]);

        const result = await service.updateRoleTemplate(1, updateData);

        expect(result).toEqual(mockUpdated);
      });

      it('should return null for non-existent template', async () => {
        mockDb.update.mockResolvedValueOnce([]);

        const result = await service.updateRoleTemplate(999, { name: 'Updated' });

        expect(result).toBeNull();
      });
    });

    describe('deleteRoleTemplate', () => {
      it('should delete role template successfully', async () => {
        mockDb.delete.mockResolvedValueOnce({ rowCount: 1 });

        const result = await service.deleteRoleTemplate(1);

        expect(result).toBe(true);
      });

      it('should return false for non-existent template', async () => {
        mockDb.delete.mockResolvedValueOnce({ rowCount: 0 });

        const result = await service.deleteRoleTemplate(999);

        expect(result).toBe(false);
      });
    });

    describe('createRoleFromTemplate', () => {
      it('should create role from template successfully', async () => {
        const mockTemplate = {
          id: 1,
          name: 'Loan Officer Template',
          template_config: {
            permissions: ['loan_create', 'customer_view'],
            description: 'Standard loan officer role'
          }
        };
        const mockPermissions = [
          { id: 1, code: 'loan_create' },
          { id: 2, code: 'customer_view' }
        ];
        const mockRole = {
          id: 1,
          name: 'New Loan Officer',
          description: 'Standard loan officer role',
          company_id: 1,
          is_system: false
        };

        // Mock template lookup
        mockDb.select.mockResolvedValueOnce([mockTemplate]);
        // Mock role creation
        mockDb.insert.mockResolvedValueOnce([mockRole]);
        // Mock permission lookup
        mockDb.select.mockResolvedValueOnce(mockPermissions);
        // Mock permission assignments
        mockDb.insert.mockResolvedValue([{}]);

        const result = await service.createRoleFromTemplate(1, 'New Loan Officer', 1);

        expect(result).toEqual(mockRole);
      });

      it('should throw error for non-existent template', async () => {
        mockDb.select.mockResolvedValueOnce([]); // Template not found

        await expect(service.createRoleFromTemplate(999, 'New Role', 1)).rejects.toThrow('Template 999 not found');
      });
    });
  });

  describe('getRoleHierarchyTree', () => {
    it('should build role hierarchy tree correctly', async () => {
      const mockRoles = [
        { id: 1, name: 'Manager', company_id: 1 },
        { id: 2, name: 'Senior Officer', company_id: 1 },
        { id: 3, name: 'Officer', company_id: 1 }
      ];
      const mockHierarchies = [
        { parent_role_id: 1, child_role_id: 2, inheritance_type: 'inherit' },
        { parent_role_id: 2, child_role_id: 3, inheritance_type: 'inherit' }
      ];

      mockDb.select.mockResolvedValueOnce(mockRoles);
      mockDb.select.mockResolvedValueOnce(mockHierarchies);

      const result = await service.getRoleHierarchyTree(1);

      expect(result).toHaveLength(1); // One root node (Manager)
      expect(result[0].role.name).toBe('Manager');
      expect(result[0].children).toHaveLength(1); // Senior Officer
      expect(result[0].children[0].role.name).toBe('Senior Officer');
      expect(result[0].children[0].children).toHaveLength(1); // Officer
      expect(result[0].children[0].children[0].role.name).toBe('Officer');
    });

    it('should handle roles without hierarchy', async () => {
      const mockRoles = [
        { id: 1, name: 'Standalone Role', company_id: 1 }
      ];
      const mockHierarchies: any[] = [];

      mockDb.select.mockResolvedValueOnce(mockRoles);
      mockDb.select.mockResolvedValueOnce(mockHierarchies);

      const result = await service.getRoleHierarchyTree(1);

      expect(result).toHaveLength(1);
      expect(result[0].role.name).toBe('Standalone Role');
      expect(result[0].children).toHaveLength(0);
      expect(result[0].parents).toHaveLength(0);
    });
  });
});
