import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { z } from 'zod';
import { format, addMonths } from 'date-fns';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/lib/auth';
import { formatLoanTerm } from '@/utils/format-utils';
// Static form renderer removed
import {
  Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link } from 'wouter';
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, FileText, MoreVertical, Pencil, Trash2, Eye, Filter, Search, AlertTriangle } from 'lucide-react';
// LoanFormRenderer removed

// Types
interface Loan {
  id: number;
  customer_id: number;
  company_id: number;
  amount: string | number;
  interest_rate: string | number;
  interest_type: 'flat' | 'reducing' | 'compound';
  term: number;
  terms_frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'yearly' | null;
  payment_frequency?: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'yearly' | null;
  start_date: string;
  end_date: string;
  status: string;
  created_at: string;
  updated_at: string;
  loan_reference_code?: string | null;
  customer?: {
    id: number;
    full_name: string;
  };
  notes?: string | null;
}

interface Customer {
  id: number;
  full_name: string;
  company_id: number;
}

interface FormTemplate {
  id: number;
  name: string;
  description?: string;
  company_id: number;
  branch_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface FormField {
  id: string;
  template_id: number;
  name: string;
  label: string;
  type: string;
  options?: string[];
  required: boolean;
  order: number;
}

interface LoanConfiguration {
  id: number;
  company_id: number;
  template_id: number;
  branch_id: number | null;
  is_active: boolean;
  order: number;
  created_at: string;
  updated_at: string;
  template?: FormTemplate;
}

interface LoanFormData {
  [key: string]: any;
  _validate?: () => boolean;
}

// Schema for loan form validation
const loanFormSchema = z.object({
  company_id: z.number(),
  customer_id: z.number({
    required_error: "Customer is required",
    invalid_type_error: "Customer must be a number",
  }),
  amount: z.string().min(1, {
    message: "Loan amount is required",
  }),
  interest_rate: z.string().min(1, {
    message: "Interest rate is required",
  }),
  interest_type: z.string().min(1, {
    message: "Interest type is required",
  }),
  term: z.string().min(1, {
    message: "Loan term is required",
  }),
  start_date: z.string().min(1, {
    message: "Start date is required",
  }),
  end_date: z.string().min(1, {
    message: "End date is required",
  }),
  notes: z.string().optional().nullable(),
});

export default function Loans() {
  // Router and global state
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const { user, getCurrentUser } = useAuth();
  const currentUser = getCurrentUser();
  const companyId = currentUser?.company_id;
  const queryClient = useQueryClient();

  // Local state
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editCurrentStep, setEditCurrentStep] = useState(1);
  const [currentStep, setCurrentStep] = useState(1);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);
  const [formData, setFormData] = useState<Record<string, LoanFormData>>({});
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);

  // Items per page for pagination
  const itemsPerPage = 10;

  // Queries
  const { data: customers = [], isLoading: isLoadingCustomers } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${companyId}/customers`],
    enabled: !!companyId,
  });

  const { data: loans = [], isLoading: isLoadingLoans } = useQuery<Loan[]>({
    queryKey: [`/api/companies/${companyId}/loans`],
    enabled: !!companyId,
  });

  // Handle active loan configurations
  const { data: loanConfigurations = [], isLoading: isLoadingConfigurations } = useQuery<(LoanConfiguration & { template: FormTemplate })[]>({
    queryKey: [`/api/companies/${companyId}/loan-configurations/active`],
    enabled: !!companyId,
  });

  // Active form templates
  const formTemplatesForLoanType = loanConfigurations
    .sort((a, b) => a.order - b.order)
    .map(config => ({ ...config.template, id: config.template_id }));

  // Set default template when templates are loaded
  useEffect(() => {
    if (formTemplatesForLoanType && formTemplatesForLoanType.length > 0) {
      const firstActiveTemplate = formTemplatesForLoanType.find(t => t.is_active);
      if (firstActiveTemplate) {
        setSelectedTemplateId(firstActiveTemplate.id);
      }
    }
  }, [formTemplatesForLoanType]);

  // Calculate pagination
  const filteredLoans = loans
    // Sort loans in descending order by ID (newest first)
    .sort((a, b) => b.id - a.id)
    .filter(loan => {
      // Search by customer name, loan amount, loan ID, loan reference code
      const searchMatch = !searchTerm ||
        (loan.customer?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
         loan.amount.toString().includes(searchTerm) ||
         loan.id.toString().includes(searchTerm) ||
         (loan.loan_reference_code && loan.loan_reference_code.toLowerCase().includes(searchTerm.toLowerCase())));

      return searchMatch;
    });

  const paginatedLoans = filteredLoans
    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  const totalPages = Math.ceil(filteredLoans.length / itemsPerPage);

  // Helper functions
  const getStatusBadgeVariant = (status?: string) => {
    if (!status) return 'bg-gray-100 text-gray-800 hover:bg-gray-100';

    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'overdue': return 'bg-red-100 text-red-800 hover:bg-red-100';
      case 'completed': return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'pending': return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'rejected': return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      default: return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  // Helper function to capitalize loan status
  const capitalizeStatus = (status?: string) => {
    if (!status) return '';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const getStatusIcon = (status?: string) => {
    if (!status) return null;

    switch (status.toLowerCase()) {
      case 'active':
        return <div className="h-2 w-2 rounded-full bg-green-500 mr-1.5" />;
      case 'overdue':
        return <div className="h-2 w-2 rounded-full bg-red-500 mr-1.5" />;
      case 'completed':
        return <div className="h-2 w-2 rounded-full bg-blue-500 mr-1.5" />;
      case 'pending':
        return <div className="h-2 w-2 rounded-full bg-yellow-500 mr-1.5" />;
      default:
        return <div className="h-2 w-2 rounded-full bg-gray-500 mr-1.5" />;
    }
  };

  // Form handling
  const selectedInterestType = selectedLoan?.interest_type;

  // Create Loan Form
  const loanForm = useForm<z.infer<typeof loanFormSchema>>({
    resolver: zodResolver(loanFormSchema),
    defaultValues: {
      company_id: companyId,
      customer_id: undefined,
      amount: '',
      interest_rate: '',
      interest_type: 'flat',
      term: '12',
      start_date: format(new Date(), 'yyyy-MM-dd'),
      end_date: format(addMonths(new Date(), 12), 'yyyy-MM-dd'),
      notes: '',
    }
  });

  // Edit Loan Form
  const editForm = useForm<z.infer<typeof loanFormSchema>>({
    resolver: zodResolver(loanFormSchema),
    defaultValues: {
      company_id: companyId,
      customer_id: undefined,
      amount: '',
      interest_rate: '',
      interest_type: 'flat',
      term: '12',
      start_date: format(new Date(), 'yyyy-MM-dd'),
      end_date: format(addMonths(new Date(), 12), 'yyyy-MM-dd'),
      notes: '',
    }
  });

  // Watch for form value changes
  const selectedInterestTypeAdd = loanForm.watch('interest_type');

  // Update end date based on start date and term
  const updateEndDate = () => {
    const startDateString = loanForm.getValues('start_date');
    const termMonths = parseInt(loanForm.getValues('term'));

    if (startDateString && !isNaN(termMonths)) {
      const startDate = new Date(startDateString);
      const endDate = addMonths(startDate, termMonths);
      loanForm.setValue('end_date', format(endDate, 'yyyy-MM-dd'));
    }
  };

  // Mutations
  const createLoanMutation = useMutation({
    mutationFn: async (data: z.infer<typeof loanFormSchema>) => {
      const res = await apiRequest('POST', '/api/loans', {
        ...data,
        amount: parseFloat(data.amount.toString()),
        interest_rate: parseFloat(data.interest_rate.toString()),
        term: parseInt(data.term.toString()),
      });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
      toast({
        title: "Loan created",
        description: "The loan has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create loan",
        variant: "destructive",
      });
    },
  });

  const updateLoanMutation = useMutation({
    mutationFn: async (data: z.infer<typeof loanFormSchema>) => {
      if (!selectedLoan) throw new Error("No loan selected for update");

      const res = await apiRequest('PATCH', `/api/loans/${selectedLoan.id}`, {
        ...data,
        amount: parseFloat(data.amount.toString()),
        interest_rate: parseFloat(data.interest_rate.toString()),
        term: parseInt(data.term.toString()),
      });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
      toast({
        title: "Loan updated",
        description: "The loan has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update loan",
        variant: "destructive",
      });
    },
  });

  const deleteLoanMutation = useMutation({
    mutationFn: async ({ id, forceDelete = false }: { id: number, forceDelete?: boolean }) => {
      try {
        const res = await apiRequest('DELETE', `/api/loans/${id}?companyId=${companyId}&forceDelete=${forceDelete}`);
        const data = await res.json();

        // If there's an error message in the response
        if (!res.ok) {
          // Enhanced error handling with more details
          const errorDetails = {
            message: data.message || "Failed to delete loan",
            status: res.status,
            hasCollections: Boolean(data.hasCollections),
            collectionsCount: data.collectionsCount || 0,
            originalInput: { id, forceDelete }
          };

          // Create a detailed error object
          const customError = new Error(errorDetails.message);
          (customError as any).details = errorDetails;
          throw customError;
        }

        return data;
      } catch (error: any) {
        // If it's already a properly structured error from our API handler above, just rethrow
        if (error instanceof Error && (error as any).details) {
          throw error;
        }

        // Handle network errors or unexpected errors
        const newError = new Error(
          error.message || "Failed to connect to the server. Please check your connection."
        );
        (newError as any).details = {
          message: error.message || "Network error",
          originalInput: { id, forceDelete }
        };
        throw newError;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });
      toast({
        title: "Loan Deleted Successfully",
        description: data.message || "The loan has been deleted successfully.",
        variant: "default",
      });
    },
    onError: (error: any) => {
      const errorDetails = (error as any).details || {};

      // Handle specific HTTP status codes
      if (errorDetails.status === 409 || errorDetails.hasCollections || errorDetails.paymentSchedulesCount || errorDetails.transactionsCount) {
        // Check if there are non-pending collections
        if (errorDetails.nonPendingCollections) {
          // Loan has non-pending collections
          toast({
            title: "Cannot Delete Loan",
            description: (
              <div className="space-y-3">
                <p>
                  {errorDetails.message ||
                   `This loan has ${errorDetails.nonPendingCollections} non-pending collections. All collections must have "pending" status before deletion is allowed.`}
                </p>
                <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 text-blue-500 hover:bg-blue-50"
                    onClick={() => {
                      // Navigate to collections page for this loan
                      const loanId = errorDetails.originalInput?.id;
                      if (loanId) {
                        window.location.href = `/collections?loanId=${loanId}`;
                      }
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Manage Collections First
                  </Button>
                </div>
              </div>
            ),
            duration: 15000, // Show for longer
          });
        } else if (errorDetails.allPending || errorDetails.paymentSchedulesCount || errorDetails.transactionsCount) {
          // All collections are pending or there are payment schedules/transactions
          // Build a descriptive message about what's preventing deletion
          let dependenciesMessage = "";
          if (errorDetails.collectionsCount) {
            dependenciesMessage += `${errorDetails.collectionsCount} collections, `;
          }
          if (errorDetails.paymentSchedulesCount) {
            dependenciesMessage += `${errorDetails.paymentSchedulesCount} payment schedules, `;
          }
          if (errorDetails.transactionsCount) {
            dependenciesMessage += `${errorDetails.transactionsCount} financial transactions, `;
          }
          // Remove trailing comma and space
          dependenciesMessage = dependenciesMessage.replace(/, $/, "");

          toast({
            title: "Cannot Delete Loan",
            description: (
              <div className="space-y-3">
                <p>
                  {errorDetails.message ||
                   `This loan has ${dependenciesMessage}. You can force delete the loan with all its associated records.`}
                </p>
                <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 text-blue-500 hover:bg-blue-50"
                    onClick={() => {
                      // Navigate to collections page for this loan
                      const loanId = errorDetails.originalInput?.id;
                      if (loanId) {
                        window.location.href = `/collections?loanId=${loanId}`;
                      }
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Manage Collections First
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-500 text-red-500 hover:bg-red-50"
                    onClick={() => {
                      // Show a confirmation dialog with specific details
                      if (window.confirm(
                        `CAUTION: You are about to delete this loan AND all its associated records (${dependenciesMessage}). This action cannot be undone and will permanently remove all payment records and financial transactions. Are you sure you want to proceed?`
                      )) {
                        const { id } = (errorDetails.originalInput || {}) as { id?: number };
                        if (id) {
                          deleteLoanMutation.mutate({ id, forceDelete: true });
                        }
                      }
                    }}
                  >
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    Force Delete Everything
                  </Button>
                </div>
              </div>
            ),
            duration: 15000, // Show for longer
          });
        } else {
          // Generic error
          toast({
            title: "Cannot Delete Loan",
            description: (
              <div className="space-y-3">
                <p>
                  {errorDetails.message ||
                   `This loan has associated records and cannot be deleted directly.`}
                </p>
                <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 text-blue-500 hover:bg-blue-50"
                    onClick={() => {
                      // Navigate to collections page for this loan
                      const loanId = errorDetails.originalInput?.id;
                      if (loanId) {
                        window.location.href = `/collections?loanId=${loanId}`;
                      }
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Manage Collections First
                  </Button>
                </div>
              </div>
            ),
            duration: 15000, // Show for longer
          });
        }
      } else if (errorDetails.status === 404) {
        // Not found
        toast({
          title: "Loan Not Found",
          description: "The loan you're trying to delete may have been already removed.",
          variant: "destructive",
        });
      } else if (errorDetails.status === 403) {
        // Forbidden
        toast({
          title: "Access Denied",
          description: "You don't have permission to delete this loan.",
          variant: "destructive",
        });      } else {
        // Generic error
        let errorMessage = "An unexpected error occurred. Please try again later.";

        // Extract just the message from error response if available
        if (error.message) {
          try {
            // Check if the error message is a JSON string containing a message property
            if (error.message.includes('{') && error.message.includes('"message"')) {
              // Try to parse it and extract just the message property
              const match = error.message.match(/"message":"([^"]+)"/);
              if (match && match[1]) {
                errorMessage = match[1];
              } else {
                errorMessage = error.message;
              }
            } else {
              errorMessage = error.message;
            }
          } catch (e) {
            errorMessage = error.message;
          }
        }

        toast({
          title: "Error Deleting Loan",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
  });

  // Multi-step form handlers
  const submitFormWithData = async (data: z.input<typeof loanFormSchema>) => {
    setIsFormSubmitting(true);
    try {
      // First, create the loan
      const response = await createLoanMutation.mutateAsync(data);
      const newLoanId = response.id;

      // Then submit all associated form submissions if we have any
      const formSubmissionPromises = Object.entries(formData).map(async ([templateId, data]) => {
        // Skip the form data validation function
        const { _validate, ...formValues } = data;

        // Submit the form data
        return await fetch(`/api/companies/${companyId}/loans/${newLoanId}/form-submissions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            company_id: companyId,
            loan_id: newLoanId,
            template_id: parseInt(templateId),
            form_data: JSON.stringify(formValues)
          })
        });
      });

      // Wait for all form submissions to complete
      await Promise.all(formSubmissionPromises);

      // Reset the current step and form data
      setCurrentStep(1);
      setFormData({});

      // Navigate to the loan detail page
      navigateToLoanDetail(newLoanId);
    } catch (error) {
      console.error("Error creating loan with forms:", error);
      toast({
        title: "Error",
        description: "Failed to create loan with forms. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsFormSubmitting(false);
    }
  };

  // Handle step navigation
  const goToNextStep = async () => {
    if (currentStep === 1) {
      // Validate the loan form data
      const valid = await loanForm.trigger();
      if (!valid) return;

      setCurrentStep(2);
    } else if (currentStep === 2) {
      // Check if forms need to be validated
      const allFormsValid = formTemplatesForLoanType.length === 0 ||
        formTemplatesForLoanType.every(template => {
          const templateData = formData[template.id];
          return templateData && templateData._validate ? templateData._validate() : true;
        });

      if (!allFormsValid) {
        toast({
          title: "Form Validation Error",
          description: "Please fill out all required fields in the form(s).",
          variant: "destructive",
        });
        return;
      }

      setCurrentStep(3);
    }
  };

  const goToPrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form data changes
  const handleFormDataChange = (templateId: number, data: LoanFormData) => {
    setFormData(prev => ({
      ...prev,
      [templateId]: data
    }));
  };

  // Form handlers
  const onSubmitAdd = (data: z.input<typeof loanFormSchema>) => {
    if (currentStep === 3) {
      // Final step - submit everything
      submitFormWithData(data);
    } else {
      // Just validate and go to next step
      goToNextStep();
    }
  };

  const onSubmitEdit = async (data: z.input<typeof loanFormSchema>) => {
    if (selectedLoan) {
      try {
        setIsFormSubmitting(true);

        // First, update the loan (we don't need to add the id as it's handled in the mutation)
        await updateLoanMutation.mutateAsync(data);

        // Then submit all associated form submissions if we have any
        if (Object.keys(formData).length > 0) {
          const formSubmissionPromises = Object.entries(formData).map(async ([templateId, data]) => {
            // Skip the form data validation function
            const { _validate, ...formValues } = data as any;

            // Submit the form data
            try {
              // Check if there's already a submission for this template
              const existingSubmissions = await fetch(
                `/api/companies/${companyId}/loans/${selectedLoan.id}/form-submissions`
              ).then(res => res.json());

              const existingSubmission = existingSubmissions.find(
                (sub: any) => sub.template_id === parseInt(templateId)
              );

              if (existingSubmission) {
                // Update existing submission
                return await fetch(`/api/companies/${companyId}/form-submissions/${existingSubmission.id}`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    company_id: companyId,
                    loan_id: selectedLoan.id,
                    template_id: parseInt(templateId),
                    form_data: JSON.stringify(formValues)
                  })
                });
              } else {
                // Create new submission
                return await fetch(`/api/companies/${companyId}/loans/${selectedLoan.id}/form-submissions`, {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    company_id: companyId,
                    loan_id: selectedLoan.id,
                    template_id: parseInt(templateId),
                    form_data: JSON.stringify(formValues)
                  })
                });
              }
            } catch (error) {
              console.error("Error with form submission:", error);
              throw error;
            }
          });

          // Wait for all form submissions to complete
          await Promise.all(formSubmissionPromises);
        }

        // Reset form state
        setEditCurrentStep(1);
        setFormData({});

        // Close the dialog
        setIsEditDialogOpen(false);

        toast({
          title: "Loan Updated",
          description: "Loan and associated forms have been updated successfully.",
          duration: 3000,
        });
      } catch (error) {
        console.error("Error updating loan with forms:", error);
        toast({
          title: "Error",
          description: "Failed to update loan with forms. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsFormSubmitting(false);
      }
    }
  };

  // Helper functions
  const handleNavigateToEditPage = (loan: Loan) => {
    // Navigate to the edit page for the loan
    setLocation(`/loans/edit/${loan.id}`);
  };

  const handleOpenEditDialog = (loan: Loan) => {
    setSelectedLoan(loan);

    // Reset the form data for dynamic forms
    setFormData({});
    // Reset edit step to 1
    setEditCurrentStep(1);

    editForm.reset({
      company_id: loan.company_id,
      customer_id: loan.customer_id,
      amount: loan.amount.toString(),
      interest_rate: loan.interest_rate.toString(),
      interest_type: loan.interest_type,
      term: loan.term.toString(),
      start_date: new Date(loan.start_date).toISOString().split('T')[0],
      end_date: new Date(loan.end_date).toISOString().split('T')[0],
      notes: loan.notes || null,
    });

    // Load active form templates
    queryClient.invalidateQueries({
      queryKey: [`/api/companies/${companyId}/loan-configurations/active`]
    });

    // Find the first active template to use for editing
    if (formTemplatesForLoanType && formTemplatesForLoanType.length > 0) {
      const firstActiveTemplate = formTemplatesForLoanType.find(t => t.is_active);
      if (firstActiveTemplate) {
        setSelectedTemplateId(firstActiveTemplate.id);
      }
    }

    // Fetch existing form submissions for this loan
    fetchFormSubmissionsForLoan(loan.id);

    setIsEditDialogOpen(true);
  };

  // Fetch form submissions for a loan to populate edit form
  const fetchFormSubmissionsForLoan = async (loanId: number) => {
    try {
      const response = await fetch(`/api/companies/${companyId}/loans/${loanId}/form-submissions`);
      if (response.ok) {
        const submissions = await response.json();

        // Organize form data by template ID
        const formDataByTemplate: Record<string, any> = {};
        submissions.forEach((submission: any) => {
          if (submission.template_id && submission.form_data) {
            try {
              // Parse the form data if it's a string
              const parsedData = typeof submission.form_data === 'string'
                ? JSON.parse(submission.form_data)
                : submission.form_data;

              formDataByTemplate[submission.template_id] = parsedData;
            } catch (e) {
              console.error("Error parsing form data:", e);
            }
          }
        });

        // Update form data state with retrieved submissions
        setFormData(formDataByTemplate);
      }
    } catch (error) {
      console.error("Error fetching form submissions:", error);
    }
  };

  const handleDeleteLoan = (id: number) => {
    // First check if loan has existing collections
    const confirmDelete = window.confirm("Are you sure you want to delete this loan? This cannot be undone.");
    if (confirmDelete) {
      // Using our improved mutation that handles API response errors
      deleteLoanMutation.mutate({ id, forceDelete: false });
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by the filter above
  };

  // Get label for interest type
  const getInterestTypeLabel = (type: string) => {
    switch (type) {
      case 'flat': return 'Flat Rate';
      case 'reducing': return 'Reducing Balance';
      case 'compound': return 'Compound Interest';
      default: return type;
    }
  };

  // Get display name for loan type
  const getLoanTypeDisplay = (type: string) => {
    switch (type) {
      case 'personal': return 'Personal Loan';
      case 'business': return 'Business Loan';
      case 'education': return 'Education Loan';
      case 'housing': return 'Housing Loan';
      case 'vehicle': return 'Vehicle Loan';
      case 'agriculture': return 'Agriculture Loan';
      case 'microfinance': return 'Microfinance Loan';
      case 'other': return 'Other Loan';
      default: return type;
    }
  };

  const navigateToLoanDetail = (loanId: number) => {
    setLocation(`/loans/${loanId}`);
  };

  // UI components and rendering
  return (
    <div>
      {/* Page Header */}
      <div className="flex flex-row items-center justify-between mb-6">
        <div className="flex items-center">
          <h1 className="text-2xl font-semibold text-gray-900">Loans</h1>
          <p className="ml-4 text-sm text-gray-500 whitespace-nowrap">
            Manage customer loans and financing
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            className="flex items-center justify-center text-xs md:text-sm px-2 md:px-3"
            size="sm"
            onClick={() => {
              setLocation(`/loans/quick-create?frequency=daily`);
            }}
          >
            <Plus size={14} className="mr-1" />
            <span>Daily</span>
          </Button>

          <Button
            className="flex items-center justify-center text-xs md:text-sm px-2 md:px-3"
            size="sm"
            onClick={() => {
              setLocation(`/loans/quick-create?frequency=weekly`);
            }}
          >
            <Plus size={14} className="mr-1" />
            <span>Weekly</span>
          </Button>

          <Button
            className="flex items-center justify-center text-xs md:text-sm px-2 md:px-3"
            size="sm"
            onClick={() => {
              setLocation(`/loans/quick-create?frequency=monthly`);
            }}
          >
            <Plus size={14} className="mr-1" />
            <span>Monthly</span>
          </Button>

          {/* <Button
            className="flex items-center gap-1"
            variant="outline"
            onClick={() => {
              // Go to the standard loan form
              setLocation(`/loans/create`);
            }}
          >
            <Plus size={16} />
            <span>Custom Loan</span>
          </Button> */}
        </div>
      </div>

      {/* Search */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="col-span-2">
              <form onSubmit={handleSearch} className="flex w-full space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="search"
                    placeholder="Search loans by customer, amount, ID, or reference code"
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button type="submit">Search</Button>
              </form>
            </div>
            {/* Filter section removed as part of loan type simplification */}
          </div>
        </CardContent>
      </Card>

      {/* Responsive Loans Display */}
      <Card>
        <CardContent className="p-0">
          {/* Loading state */}
          {isLoadingLoans ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : paginatedLoans.length === 0 ? (
            <div className="text-center py-12 text-gray-500 px-4">
              {searchTerm ?
                "No loans match your search criteria." :
                "No loans found. Create your first loan by clicking 'Quick Loan' or 'Standard Loan'."}
            </div>
          ) : (
            <>
              {/* Mobile view - Cards */}
              <div className="md:hidden">
                <div className="space-y-4 p-4">
                  {paginatedLoans.map(loan => (
                    <Card key={loan.id} className="overflow-hidden border">
                      <CardHeader className="p-4 pb-0 flex flex-row justify-between items-center">
                        <div>
                          <CardTitle className="text-base flex items-center gap-1">
                            <span>{loan.loan_reference_code || `Loan #${loan.id}`}</span>
                          </CardTitle>
                          <CardDescription className="text-sm mt-1">

                            {loan.customer?.full_name}
                          </CardDescription>
                        </div>
                        <div className="text-right">
                          <span className="text-base font-medium">₹{Number(loan.amount).toLocaleString('en-IN')}</span>
                          <div className="mt-1">
                            <Badge className={getStatusBadgeVariant(loan.status)}>
                              <div className="flex items-center">
                                {getStatusIcon(loan.status)}
                                <span>{capitalizeStatus(loan.status)}</span>
                              </div>
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-3">
                        <div className="grid grid-cols-2 gap-x-2 gap-y-3 text-sm">
                          <div>
                            <div className="text-gray-500 text-xs">Interest</div>
                            <div>
                              {Number(loan.interest_rate).toFixed(2)}%
                              <span className="text-gray-500 text-xs block">
                                {getInterestTypeLabel(loan.interest_type)}
                              </span>
                            </div>
                          </div>
                          <div>
                            <div className="text-gray-500 text-xs">Term</div>
                            <div>{formatLoanTerm(loan.term, loan.terms_frequency || loan.payment_frequency || 'monthly')}</div>
                          </div>
                          <div>
                            <div className="text-gray-500 text-xs">Start Date</div>
                            <div>{new Date(loan.start_date).toLocaleDateString()}</div>
                          </div>
                          <div>
                            <div className="text-gray-500 text-xs">End Date</div>
                            <div>{new Date(loan.end_date).toLocaleDateString()}</div>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="p-4 pt-0 flex justify-end gap-2 border-t mt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigateToLoanDetail(loan.id)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleNavigateToEditPage(loan)}
                        >
                          <Pencil className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-destructive hover:bg-destructive/10"
                          onClick={() => handleDeleteLoan(loan.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Desktop view - Table */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Loan ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Interest</TableHead>
                      <TableHead>Term</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>End Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedLoans.map(loan => (
                      <TableRow key={loan.id}>
                        <TableCell className="font-medium">{loan.loan_reference_code || `#${loan.id}`}</TableCell>
                        <TableCell>{loan.customer?.full_name}</TableCell>
                        <TableCell>₹{Number(loan.amount).toLocaleString('en-IN')}</TableCell>
                        <TableCell>
                          {Number(loan.interest_rate).toFixed(2)}%
                          <span className="text-gray-500 text-xs block">
                            {getInterestTypeLabel(loan.interest_type)}
                          </span>
                        </TableCell>
                        <TableCell>{formatLoanTerm(loan.term, loan.terms_frequency || loan.payment_frequency || 'monthly')}</TableCell>
                        <TableCell>{new Date(loan.start_date).toLocaleDateString()}</TableCell>
                        <TableCell>{new Date(loan.end_date).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Badge className={getStatusBadgeVariant(loan.status)}>
                            <div className="flex items-center">
                              {getStatusIcon(loan.status)}
                              <span>{capitalizeStatus(loan.status)}</span>
                            </div>
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => navigateToLoanDetail(loan.id)}>
                                <Eye className="mr-2 h-4 w-4" />
                                <span>View Details</span>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleNavigateToEditPage(loan)}>
                                <Pencil className="mr-2 h-4 w-4" />
                                <span>Edit</span>
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleDeleteLoan(loan.id)}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                <span>Delete</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </>
          )}
        </CardContent>

        {/* Pagination */}
        {totalPages > 1 && (
          <CardFooter className="flex items-center justify-center border-t p-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    className={currentPage === 1 ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    className={currentPage === totalPages ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </CardFooter>
        )}
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Loan</DialogTitle>
            <DialogDescription>
              Update loan details for this loan
            </DialogDescription>
          </DialogHeader>

          {selectedTemplateId && (
            <div className="space-y-6">
              <div className="border rounded-lg p-4">
                <h3 className="text-lg font-medium mb-4">
                  {formTemplatesForLoanType.find(t => t.id === selectedTemplateId)?.name || "Loan Type"}
                </h3>
                {/* Form renderer replaced with standard form */}
                <Form {...editForm}>
                  <div className="space-y-4">
                    <FormField
                      control={editForm.control}
                      name="customer_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Customer</FormLabel>
                          <Select
                            onValueChange={(value) => field.onChange(parseInt(value))}
                            defaultValue={field.value?.toString()}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select customer" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {customers.map((customer) => (
                                <SelectItem key={customer.id} value={customer.id.toString()}>
                                  {customer.full_name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={editForm.control}
                        name="amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Loan Amount</FormLabel>
                            <FormControl>
                              <Input {...field} type="text" placeholder="Enter loan amount" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={editForm.control}
                        name="interest_rate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Interest Rate (%)</FormLabel>
                            <FormControl>
                              <Input {...field} type="text" placeholder="Enter interest rate" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={editForm.control}
                      name="interest_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Interest Type</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select interest type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="flat">Flat</SelectItem>
                              <SelectItem value="reducing">Reducing</SelectItem>
                              <SelectItem value="compound">Compound</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={editForm.control}
                        name="term"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Term (Months)</FormLabel>
                            <FormControl>
                              <Input {...field} type="text" placeholder="Enter loan term" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={editForm.control}
                        name="start_date"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Start Date</FormLabel>
                            <FormControl>
                              <Input {...field} type="date" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </Form>
              </div>
            </div>
          )}

          <DialogFooter className="pt-4">
            <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              type="button"
              disabled={updateLoanMutation.isPending}
              onClick={() => {
                // Validate the form data
                const templateData = formData[selectedTemplateId?.toString() || ""];
                if (templateData && templateData._validate && templateData._validate()) {
                  // Extract the form data
                  const { _validate, ...formValues } = templateData;

                  // Prepare the loan data
                  const loanData = {
                    company_id: companyId || 0,
                    customer_id: formValues.customer_id,
                    amount: formValues.amount?.toString() || "0",
                    interest_rate: formValues.interest_rate?.toString() || "0",
                    interest_type: formValues.interest_type || 'flat',
                    term: formValues.term?.toString() || "0",
                    start_date: formValues.start_date || new Date().toISOString().split('T')[0],
                    end_date: formValues.end_date || new Date().toISOString().split('T')[0],
                    notes: formValues.notes || null,
                  };

                  // Update the loan
                  onSubmitEdit(loanData);
                } else {
                  toast({
                    title: "Validation Error",
                    description: "Please complete all required form fields",
                    variant: "destructive",
                  });
                }
              }}
            >
              {updateLoanMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Update Loan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}