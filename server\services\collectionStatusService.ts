/**
 * Collection Status Service
 * 
 * This service handles automated status transitions for collections based on dates.
 * It provides functionality to:
 * 1. Update "Pending" collections to "Due" when the scheduled date arrives
 * 2. Update "Due" collections to "Overdue" when they are past due
 * 3. Batch update collection statuses
 */

import { db } from '../db';
import { collections } from '@shared/schema';
import { eq, and, lt, ne } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';

export class CollectionStatusService {
  /**
   * Updates collection statuses based on dates
   * - Pending → Due when scheduled date is today
   * - Due → Overdue when scheduled date is in the past
   */
  async updateCollectionStatuses(): Promise<{ updated: number, errors: number }> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Start of today
      
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      let updatedCount = 0;
      let errorCount = 0;
      
      // 1. Update pending collections to due when the scheduled date is today
      try {
        const pendingResult = await db.update(collections)
          .set({ 
            status: 'due',
            updated_at: new Date()
          })
          .where(
            and(
              eq(collections.status, 'pending'),
              lt(collections.scheduled_date, tomorrow),
              ne(collections.status, 'completed')
            )
          );
        
        updatedCount += pendingResult.rowCount || 0;
        console.log(`Updated ${pendingResult.rowCount} pending collections to due`);
      } catch (error) {
        errorLogger.logError('Error updating pending collections to due', 'collection-status-update', error as Error);
        errorCount++;
      }
      
      // 2. Update due collections to overdue when they are past due
      // We can configure the grace period (e.g., 1 day after scheduled date)
      const gracePeriod = 1; // 1 day grace period
      const gracePeriodDate = new Date(today);
      gracePeriodDate.setDate(gracePeriodDate.getDate() - gracePeriod);
      
      try {
        const overdueResult = await db.update(collections)
          .set({ 
            status: 'overdue',
            updated_at: new Date()
          })
          .where(
            and(
              eq(collections.status, 'due'),
              lt(collections.scheduled_date, gracePeriodDate),
              ne(collections.status, 'completed')
            )
          );
        
        updatedCount += overdueResult.rowCount || 0;
        console.log(`Updated ${overdueResult.rowCount} due collections to overdue`);
      } catch (error) {
        errorLogger.logError('Error updating due collections to overdue', 'collection-status-update', error as Error);
        errorCount++;
      }
      
      return { updated: updatedCount, errors: errorCount };
    } catch (error) {
      errorLogger.logError('Error in updateCollectionStatuses', 'collection-status-update', error as Error);
      return { updated: 0, errors: 1 };
    }
  }
  
  /**
   * Manually updates a collection's status
   */
  async updateCollectionStatus(id: number, status: string): Promise<boolean> {
    try {
      const result = await db.update(collections)
        .set({ 
          status: status as any,
          updated_at: new Date()
        })
        .where(eq(collections.id, id));
      
      return result.rowCount > 0;
    } catch (error) {
      errorLogger.logError(`Error updating status for collection id=${id}`, 'collection-status-update', error as Error);
      return false;
    }
  }
  
  /**
   * Batch updates collection statuses
   */
  async batchUpdateCollectionStatus(ids: number[], status: string): Promise<{ success: number, failed: number }> {
    let successCount = 0;
    let failedCount = 0;
    
    for (const id of ids) {
      try {
        const success = await this.updateCollectionStatus(id, status);
        if (success) {
          successCount++;
        } else {
          failedCount++;
        }
      } catch (error) {
        failedCount++;
        errorLogger.logError(`Error in batch update for collection id=${id}`, 'collection-batch-update', error as Error);
      }
    }
    
    return { success: successCount, failed: failedCount };
  }
}

// Create and export a singleton instance
export const collectionStatusService = new CollectionStatusService();
