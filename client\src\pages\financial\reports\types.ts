// Report interface definitions
export interface AccountBalanceReport {
  accountId: number;
  accountName: string;
  accountCode: string;
  accountType: string;
  openingBalance: number;
  totalDebits: number;
  totalCredits: number;
  closingBalance: number;
}

export interface BalanceSheetDetail {
  categoryName: string;
  accounts: {
    id: number;
    name: string;
    code: string;
    balance: number;
  }[];
  totalAmount: number;
}

export interface BalanceSheetReport {
  asOfDate: string;
  assets: BalanceSheetDetail[];
  liabilities: BalanceSheetDetail[];
  equity: BalanceSheetDetail[];
  totalAssets: number;
  totalLiabilities: number;
  totalEquity: number;
}

export interface ProfitLossCategory {
  categoryName: string;
  accounts: {
    id: number;
    name: string;
    code: string;
    amount: number;
  }[];
  totalAmount: number;
}

export interface ProfitLossReport {
  startDate: string;
  endDate: string;
  income: ProfitLossCategory[];
  expenses: ProfitLossCategory[];
  totalIncome: number;
  totalExpenses: number;
  netProfit: number;
}

export interface CashFlowReport {
  startDate: string;
  endDate: string;
  openingBalance: number;
  closingBalance: number;
  operatingActivities: {
    inflows: {
      category: string;
      amount: number;
    }[];
    outflows: {
      category: string;
      amount: number;
    }[];
    netOperatingCashflow: number;
  };
  investingActivities: {
    inflows: {
      category: string;
      amount: number;
    }[];
    outflows: {
      category: string;
      amount: number;
    }[];
    netInvestingCashflow: number;
  };
  financingActivities: {
    inflows: {
      category: string;
      amount: number;
    }[];
    outflows: {
      category: string;
      amount: number;
    }[];
    netFinancingCashflow: number;
  };
  netCashflow: number;
}

// Chart colors
export const COLORS = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A288E3',
  '#FF6B6B', '#4ECDC4', '#FF9F1C', '#F9C80E', '#662E9B',
];

// Report tab types
export type ReportTabType = 'profit-loss' | 'balance-sheet' | 'cash-flow' | 'accounts';
