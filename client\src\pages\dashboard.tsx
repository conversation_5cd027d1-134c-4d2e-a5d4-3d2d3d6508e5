import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/lib/auth";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronRight, Download } from "lucide-react";
import MetricsCards from "@/components/dashboard/MetricsCards";
import CollectionChart from "@/components/dashboard/CollectionChart";
import RecentCollections from "@/components/dashboard/RecentCollections";
import TopAgents from "@/components/dashboard/TopAgents";
import PartnerMetrics from "@/components/dashboard/PartnerMetrics";

// Helper function to get the time period label
const getTimePeriodLabel = (period: string) => {
  switch (period) {
    case "7":
      return "Last 7 Days";
    case "30":
      return "Last 30 Days";
    case "90":
      return "Last 3 Months";
    default:
      return "Last 30 Days";
  }
};

export default function Dashboard() {
  const { getCurrentUser, isAuthorized } = useAuth();
  const [, navigate] = useLocation();
  const user = getCurrentUser();
  const [timePeriod, setTimePeriod] = useState("30");  const companyId = user?.company_id || 1; // Default to 1 for SaaS admin if no company_id  

  // Fetch dashboard metrics
  const { data: metricsData, isLoading: metricsLoading, refetch: refetchMetrics } = useQuery({
    queryKey: [`/api/companies/${companyId}/dashboard-metrics?period=${timePeriod}`],
    enabled: !!companyId,
  });

  // Fetch recent collections
  const { data: recentCollectionsData, isLoading: collectionsLoading, refetch: refetchCollections } = useQuery({
    queryKey: [`/api/companies/${companyId}/recent-collections?limit=5&period=${timePeriod}`],
    enabled: !!companyId,
  });

  // Fetch top agents
  const { data: topAgentsData, isLoading: agentsLoading, refetch: refetchAgents } = useQuery({
    queryKey: [`/api/companies/${companyId}/top-agents?limit=5&period=${timePeriod}`],
    enabled: !!companyId,
  });
  
  // Refetch all data when time period changes
  useEffect(() => {
    if (companyId) {
      refetchMetrics();
      refetchCollections();
      refetchAgents();
      // Chart data is automatically refetched because timePeriod is in its queryKey
    }
  }, [timePeriod, companyId, refetchMetrics, refetchCollections, refetchAgents]);

  // Fetch partner program metrics (only for SaaS admin)
  const { data: partnerMetricsData, isLoading: partnerMetricsLoading } = useQuery({
    queryKey: ['/api/partner-program-metrics'],
    enabled: isAuthorized(['saas_admin']),
  });

  // Fetch collection chart data
  const { data: chartRawData, isLoading: chartLoading } = useQuery({
    queryKey: [`/api/companies/${companyId}/collection-trends?period=${timePeriod}`],
    enabled: !!companyId,
  });

  // Process chart data
  const generateChartData = () => {
    if (!chartRawData || !Array.isArray(chartRawData)) {
      // Return empty array if no data
      const days = parseInt(timePeriod);
      const data = [];
      const today = new Date();
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(today.getDate() - i);
        const formattedDate = date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
        data.push({
          date: formattedDate,
          amount: 0, 
        });
      }
      
      return data;
    }
    
    // Map API data to chart format
    return chartRawData;
  };

  // Prepare chart data
  const chartData = generateChartData();

  // Default metrics if data is not loaded yet
  const defaultMetrics = {
    totalCollections: 0,
    pendingCollections: 0,
    activeAgentsCount: 0,
    totalCustomersCount: 0,
  };

  return (
    <div>
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
            <p className="mt-1 text-sm text-gray-500">
              Welcome back, {user?.full_name || "User"}
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex space-x-3">            <Select value={timePeriod} onValueChange={setTimePeriod}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Time Period">{getTimePeriodLabel(timePeriod)}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 Days</SelectItem>
                <SelectItem value="30">Last 30 Days</SelectItem>
                <SelectItem value="90">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>
            <Button 
              variant="default" 
              className="flex items-center gap-1"
              onClick={() => {
                const data = {
                  metrics: metricsData || defaultMetrics,
                  collections: recentCollectionsData || [],
                  topAgents: topAgentsData || [],
                  chartData: chartData || []
                };
                
                const csvContent = [
                  ["Dashboard Export", new Date().toLocaleDateString()],
                  ["Metrics"],
                  ["Total Collections", data.metrics.totalCollections],
                  ["Pending Collections", data.metrics.pendingCollections],
                  ["Active Agents", data.metrics.activeAgentsCount],
                  ["Total Customers", data.metrics.totalCustomersCount],
                  [],
                  ["Collection Trends"],
                  ["Date", "Amount"],
                  ...data.chartData.map(item => [item.date, item.amount])
                ].map(row => row.join(',')).join('\n');

                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `dashboard_export_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              <Download size={16} />
              <span>Export</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Metrics Cards */}
      <MetricsCards 
        metrics={metricsData || defaultMetrics} 
        trends={metricsData?.trends || null}
        isLoading={metricsLoading} 
      />      {/* Collection Trend Chart */}
      <CollectionChart 
        data={chartData} 
        isLoading={chartLoading} 
        period={timePeriod}
        onPeriodChange={setTimePeriod}
      />

      {/* Recent Collections & Top Agents Tables */}
      <div className="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
        <RecentCollections 
          collections={recentCollectionsData || []} 
          isLoading={collectionsLoading} 
          onViewAll={() => navigate("/collections")} 
        />
        <TopAgents 
          agents={topAgentsData || []} 
          isLoading={agentsLoading} 
          onViewAll={() => navigate("/agents")} 
        />
      </div>

      {/* Partner Program Metrics (only visible to SaaS Admin) */}
      {isAuthorized(['saas_admin']) && (
        <PartnerMetrics 
          metrics={partnerMetricsData || {
            activeResellers: 0,
            resellerRevenue: 0,
            activeReferrals: 0,
            referralCommissions: 0,
            topResellers: []
          }} 
          isLoading={partnerMetricsLoading} 
        />
      )}
    </div>
  );
}
