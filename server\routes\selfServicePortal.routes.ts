import { Router } from 'express';
import { selfServicePortalService } from '../services/selfServicePortalService';
import { enhancedAuthMiddleware, type EnhancedAuthRequest } from '../middleware/enhancedAuth';
import { requirePermission } from '../middleware/permission';
import { auditAdminOperation } from '../middleware/auditMiddleware';
import { z } from 'zod';

const router = Router();

// Apply enhanced auth middleware to all routes
router.use(enhancedAuthMiddleware);

// Validation schemas
const submitRequestSchema = z.object({
  requestType: z.enum(['permission_grant', 'permission_revoke', 'role_change', 'access_extension', 'temporary_access']),
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  businessJustification: z.string().min(10),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  urgencyReason: z.string().optional(),
  requestedPermissions: z.array(z.string()).optional(),
  requestedRoleId: z.number().optional(),
  temporaryAccess: z.boolean().optional(),
  accessStartDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  accessEndDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  managerId: z.number().optional(),
  department: z.string().optional(),
});

const requestFiltersSchema = z.object({
  status: z.string().optional(),
  requestType: z.string().optional(),
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
});

const addCommentSchema = z.object({
  commentType: z.string(),
  comment: z.string().min(1),
  isInternal: z.boolean().optional(),
  visibility: z.string().optional(),
  attachments: z.array(z.any()).optional(),
});

// Submit a permission request
router.post('/requests',
  requirePermission('self_service_request'),
  auditAdminOperation('self_service', 'submit_request'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const requestData = submitRequestSchema.parse(req.body);

      const request = await selfServicePortalService.submitPermissionRequest({
        userId: req.user!.id,
        companyId: req.user!.company_id,
        ...requestData,
      });

      res.status(201).json(request);
    } catch (error) {
      console.error('Error submitting permission request:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid request data', errors: error.errors });
      }
      res.status(500).json({ message: 'Failed to submit permission request' });
    }
  }
);

// Get user's permission requests - allow any authenticated user
router.get('/requests',
  auditAdminOperation('self_service', 'view_requests'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const filters = requestFiltersSchema.parse(req.query);

      const result = await selfServicePortalService.getUserRequests(
        req.user!.id,
        req.user!.company_id,
        filters
      );

      res.json(result);
    } catch (error) {
      console.error('Error fetching user requests:', error);
      res.status(500).json({ message: 'Failed to fetch permission requests' });
    }
  }
);

// Get specific request details
router.get('/requests/:id',
  requirePermission('self_service_view'),
  auditAdminOperation('self_service', 'view_request_details'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const requestId = parseInt(req.params.id);

      const { requests } = await selfServicePortalService.getUserRequests(
        req.user!.id,
        req.user!.company_id,
        { limit: 1 }
      );

      const request = requests.find(r => r.id === requestId);

      if (!request) {
        return res.status(404).json({ message: 'Request not found' });
      }

      res.json(request);
    } catch (error) {
      console.error('Error fetching request details:', error);
      res.status(500).json({ message: 'Failed to fetch request details' });
    }
  }
);

// Cancel a pending request
router.post('/requests/:id/cancel',
  requirePermission('self_service_request'),
  auditAdminOperation('self_service', 'cancel_request'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const requestId = parseInt(req.params.id);
      const { reason } = req.body;

      if (!reason || typeof reason !== 'string') {
        return res.status(400).json({ message: 'Cancellation reason is required' });
      }

      const request = await selfServicePortalService.cancelRequest(
        requestId,
        req.user!.id,
        reason
      );

      res.json(request);
    } catch (error) {
      console.error('Error cancelling request:', error);
      res.status(500).json({ message: 'Failed to cancel request' });
    }
  }
);

// Get user's access status summary - allow any authenticated user
router.get('/access-status',
  auditAdminOperation('self_service', 'view_access_status'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const accessStatus = await selfServicePortalService.getUserAccessStatus(
        req.user!.id,
        req.user!.company_id
      );

      res.json(accessStatus);
    } catch (error) {
      console.error('Error fetching access status:', error);
      res.status(500).json({ message: 'Failed to fetch access status' });
    }
  }
);

// Add comment to a request
router.post('/requests/:id/comments',
  requirePermission('self_service_request'),
  auditAdminOperation('self_service', 'add_comment'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const requestId = parseInt(req.params.id);
      const commentData = addCommentSchema.parse(req.body);

      const comment = await selfServicePortalService.addRequestComment(
        requestId,
        req.user!.id,
        commentData
      );

      res.status(201).json(comment);
    } catch (error) {
      console.error('Error adding comment:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: 'Invalid comment data', errors: error.errors });
      }
      res.status(500).json({ message: 'Failed to add comment' });
    }
  }
);

// Get request comments
router.get('/requests/:id/comments',
  requirePermission('self_service_view'),
  auditAdminOperation('self_service', 'view_comments'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const requestId = parseInt(req.params.id);

      const comments = await selfServicePortalService.getRequestComments(
        requestId,
        req.user!.id
      );

      res.json(comments);
    } catch (error) {
      console.error('Error fetching comments:', error);
      res.status(500).json({ message: 'Failed to fetch comments' });
    }
  }
);

// Get user's permission history (from audit logs)
router.get('/permission-history',
  requirePermission('self_service_view'),
  auditAdminOperation('self_service', 'view_permission_history'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const { startDate, endDate, limit = '50', offset = '0' } = req.query;

      // This would integrate with the audit service to get permission history
      // For now, return a placeholder response
      const history = {
        entries: [],
        totalCount: 0,
        message: 'Permission history feature coming soon'
      };

      res.json(history);
    } catch (error) {
      console.error('Error fetching permission history:', error);
      res.status(500).json({ message: 'Failed to fetch permission history' });
    }
  }
);

// Get available permissions for request
router.get('/available-permissions',
  requirePermission('self_service_view'),
  auditAdminOperation('self_service', 'view_available_permissions'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      // This would return a list of permissions that the user can request
      // For now, return a basic list
      const permissions = [
        { code: 'loan_view', name: 'View Loans', category: 'loans' },
        { code: 'loan_create', name: 'Create Loans', category: 'loans' },
        { code: 'customer_view', name: 'View Customers', category: 'customers' },
        { code: 'customer_create', name: 'Create Customers', category: 'customers' },
        { code: 'collection_view', name: 'View Collections', category: 'collections' },
        { code: 'collection_manage', name: 'Manage Collections', category: 'collections' },
        { code: 'report_view', name: 'View Reports', category: 'reports' },
        { code: 'report_export', name: 'Export Reports', category: 'reports' },
      ];

      res.json(permissions);
    } catch (error) {
      console.error('Error fetching available permissions:', error);
      res.status(500).json({ message: 'Failed to fetch available permissions' });
    }
  }
);

// Get available roles for request
router.get('/available-roles',
  requirePermission('self_service_view'),
  auditAdminOperation('self_service', 'view_available_roles'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      // This would return a list of roles that the user can request
      // For now, return a basic list based on company
      const roles = [
        { id: 1, name: 'Loan Officer', description: 'Can manage loans and customers' },
        { id: 2, name: 'Collection Agent', description: 'Can manage collections and payments' },
        { id: 3, name: 'Report Viewer', description: 'Can view and export reports' },
        { id: 4, name: 'Customer Service', description: 'Can view customers and basic loan information' },
      ];

      res.json(roles);
    } catch (error) {
      console.error('Error fetching available roles:', error);
      res.status(500).json({ message: 'Failed to fetch available roles' });
    }
  }
);

// Get user's current permissions - allow any authenticated user
router.get('/current-permissions',
  auditAdminOperation('self_service', 'view_current_permissions'),
  async (req: EnhancedAuthRequest, res) => {
    try {
      const user = req.user!;

      const currentPermissions = {
        role: user.role,
        permissions: user.permissions || [],
        rolePermissions: [], // TODO: Get role-based permissions
        temporaryPermissions: [], // TODO: Get temporary permissions
        lastUpdated: new Date(),
      };

      res.json(currentPermissions);
    } catch (error) {
      console.error('Error fetching current permissions:', error);
      res.status(500).json({ message: 'Failed to fetch current permissions' });
    }
  }
);

export default router;
