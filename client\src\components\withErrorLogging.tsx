import React from 'react';
import ErrorBoundary from './ErrorBoundary';
import { useErrorLogger } from '@/hooks/use-error-logger';

/**
 * Higher-order component to wrap a page or component with error logging
 * This adds both an error boundary and error logging hooks
 * 
 * @param Component - The component to wrap
 * @param sourceName - The name/identifier for error logging
 */
export function withErrorLogging<P extends object>(
  Component: React.ComponentType<P>,
  sourceName: string
): React.FC<P> {
  const WithErrorLogging: React.FC<P> = (props) => {
    const { logError } = useErrorLogger(sourceName);
    
    // Handle ErrorBoundary errors
    const handleError = (error: Error) => {
      logError(error, true);
    };
    
    return (
      <ErrorBoundary source={sourceName} onError={handleError}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
  
  // Set display name for debugging
  const displayName = Component.displayName || Component.name || 'Component';
  WithErrorLogging.displayName = `withErrorLogging(${displayName})`;
  
  return WithErrorLogging;
}

export default withErrorLogging;