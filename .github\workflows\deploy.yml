name: Docker Deployment

on:
  push:
    branches: [ development ]

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: <PERSON><PERSON> <PERSON>gin
        uses: docker/login-action@v3.4.0
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          tags: ${{ vars.DOCKER_REPOSITORY }}/financial-tracker:v1
          push: true
          dockerfile: Dockerfile

      - name: Deploy Docker image
        uses: actions/checkout@v3
        with:
          path: deploy-script

      - name: Set up SSH and Deploy
        uses: appleboy/ssh-action@v0.1.10
        with:
          host: ${{ vars.SSH_HOST }}
          port: ${{ vars.SSH_PORT }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            IMAGE="${{ vars.DOCKER_REPOSITORY }}/financial-tracker:v1"
            CONTAINER="${{ vars.CONTAINER_NAME }}"
            PORT="${{ vars.CONTAINER_PORT }}"
      
            # Use sudo if Docker requires it
            DOCKER_CMD="docker"
            if ! command -v docker &> /dev/null; then
              echo "Docker not found"
              exit 1
            fi
            if ! docker ps &> /dev/null; then
              DOCKER_CMD="sudo docker"
            fi
      
            echo "Stopping and removing existing container..."
            $DOCKER_CMD stop $CONTAINER || true
            $DOCKER_CMD rm $CONTAINER || true
      
            echo "Removing old Docker image..."
            $DOCKER_CMD rmi $IMAGE || true
      
            echo "Pulling latest Docker image..."
            $DOCKER_CMD pull $IMAGE
      
            echo "Starting container..."
            $DOCKER_CMD run -d --name $CONTAINER -p $PORT:8080 $IMAGE
      
            echo "Container status:"
            $DOCKER_CMD ps
