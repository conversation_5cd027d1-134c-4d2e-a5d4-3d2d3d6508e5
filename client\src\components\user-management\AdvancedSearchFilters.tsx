import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { 
  Search, Filter, X, ChevronDown, ChevronUp, Calendar as CalendarIcon,
  Users, Shield, Building, MapPin, AlertTriangle, Save, RotateCcw
} from 'lucide-react';
import { format } from 'date-fns';
import { apiRequest } from '@/lib/api';

export interface AdvancedSearchFilters {
  searchQuery?: string;
  roles?: string[];
  departments?: number[];
  branches?: number[];
  hasPermissions?: string[];
  lacksPermissions?: string[];
  permissionCount?: { min?: number; max?: number };
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
  createdAfter?: Date;
  createdBefore?: Date;
  riskScoreMin?: number;
  riskScoreMax?: number;
  complianceStatus?: string[];
  needsReview?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface FilterOptions {
  roles: Array<{ value: string; label: string }>;
  departments: Array<{ value: number; label: string }>;
  branches: Array<{ value: number; label: string }>;
  complianceStatuses: Array<{ value: string; label: string }>;
  permissions: Array<{ value: string; label: string }>;
}

interface AdvancedSearchFiltersProps {
  filters: AdvancedSearchFilters;
  onFiltersChange: (filters: AdvancedSearchFilters) => void;
  onSearch: () => void;
  onReset: () => void;
  isLoading?: boolean;
}

export default function AdvancedSearchFilters({
  filters,
  onFiltersChange,
  onSearch,
  onReset,
  isLoading = false
}: AdvancedSearchFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState<AdvancedSearchFilters>(filters);

  // Fetch filter options
  const { data: filterOptions } = useQuery({
    queryKey: ['/api/advanced-search/filter-options'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/advanced-search/filter-options');
      return response.json() as Promise<FilterOptions>;
    },
  });

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const updateFilter = (key: keyof AdvancedSearchFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const toggleArrayFilter = (key: keyof AdvancedSearchFilters, value: string | number) => {
    const currentArray = (localFilters[key] as any[]) || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFilter(key, newArray.length > 0 ? newArray : undefined);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (localFilters.searchQuery) count++;
    if (localFilters.roles?.length) count++;
    if (localFilters.departments?.length) count++;
    if (localFilters.branches?.length) count++;
    if (localFilters.hasPermissions?.length) count++;
    if (localFilters.lacksPermissions?.length) count++;
    if (localFilters.permissionCount?.min || localFilters.permissionCount?.max) count++;
    if (localFilters.lastLoginAfter || localFilters.lastLoginBefore) count++;
    if (localFilters.createdAfter || localFilters.createdBefore) count++;
    if (localFilters.riskScoreMin || localFilters.riskScoreMax) count++;
    if (localFilters.complianceStatus?.length) count++;
    if (localFilters.needsReview) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Advanced Search & Filters
            </CardTitle>
            <CardDescription>
              Search and filter users with advanced criteria
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {activeFilterCount > 0 && (
              <Badge variant="secondary">
                {activeFilterCount} filter{activeFilterCount !== 1 ? 's' : ''} active
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Search */}
        <div className="flex gap-4">
          <div className="flex-1">
            <Label htmlFor="search">Search Users</Label>
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search by name, username, email, or phone..."
                value={localFilters.searchQuery || ''}
                onChange={(e) => updateFilter('searchQuery', e.target.value || undefined)}
                className="pl-8"
              />
            </div>
          </div>
          <div className="flex items-end gap-2">
            <Button onClick={onSearch} disabled={isLoading}>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            <Button variant="outline" onClick={onReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-6">
            {/* Role and Organization Filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Roles
                </Label>
                <div className="space-y-2 mt-2">
                  {filterOptions?.roles.map(role => (
                    <div key={role.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`role-${role.value}`}
                        checked={localFilters.roles?.includes(role.value) || false}
                        onCheckedChange={() => toggleArrayFilter('roles', role.value)}
                      />
                      <Label htmlFor={`role-${role.value}`} className="text-sm">
                        {role.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Departments
                </Label>
                <div className="space-y-2 mt-2">
                  {filterOptions?.departments.map(dept => (
                    <div key={dept.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`dept-${dept.value}`}
                        checked={localFilters.departments?.includes(dept.value) || false}
                        onCheckedChange={() => toggleArrayFilter('departments', dept.value)}
                      />
                      <Label htmlFor={`dept-${dept.value}`} className="text-sm">
                        {dept.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Branches
                </Label>
                <div className="space-y-2 mt-2">
                  {filterOptions?.branches.map(branch => (
                    <div key={branch.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`branch-${branch.value}`}
                        checked={localFilters.branches?.includes(branch.value) || false}
                        onCheckedChange={() => toggleArrayFilter('branches', branch.value)}
                      />
                      <Label htmlFor={`branch-${branch.value}`} className="text-sm">
                        {branch.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Permission Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Has Permissions
                </Label>
                <Select
                  value=""
                  onValueChange={(value) => {
                    if (value && !localFilters.hasPermissions?.includes(value)) {
                      toggleArrayFilter('hasPermissions', value);
                    }
                  }}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Select permissions..." />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions?.permissions.map(permission => (
                      <SelectItem key={permission.value} value={permission.value}>
                        {permission.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="flex flex-wrap gap-1 mt-2">
                  {localFilters.hasPermissions?.map(permission => (
                    <Badge key={permission} variant="secondary" className="text-xs">
                      {filterOptions?.permissions.find(p => p.value === permission)?.label || permission}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 ml-1"
                        onClick={() => toggleArrayFilter('hasPermissions', permission)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label>Permission Count Range</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={localFilters.permissionCount?.min || ''}
                    onChange={(e) => updateFilter('permissionCount', {
                      ...localFilters.permissionCount,
                      min: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    value={localFilters.permissionCount?.max || ''}
                    onChange={(e) => updateFilter('permissionCount', {
                      ...localFilters.permissionCount,
                      max: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                  />
                </div>
              </div>
            </div>

            {/* Date Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Created Date Range</Label>
                <div className="flex gap-2 mt-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.createdAfter ? format(localFilters.createdAfter, 'PPP') : 'From'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={localFilters.createdAfter}
                        onSelect={(date) => updateFilter('createdAfter', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.createdBefore ? format(localFilters.createdBefore, 'PPP') : 'To'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={localFilters.createdBefore}
                        onSelect={(date) => updateFilter('createdBefore', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div>
                <Label>Last Login Range</Label>
                <div className="flex gap-2 mt-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.lastLoginAfter ? format(localFilters.lastLoginAfter, 'PPP') : 'From'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={localFilters.lastLoginAfter}
                        onSelect={(date) => updateFilter('lastLoginAfter', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.lastLoginBefore ? format(localFilters.lastLoginBefore, 'PPP') : 'To'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={localFilters.lastLoginBefore}
                        onSelect={(date) => updateFilter('lastLoginBefore', date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>

            {/* Risk and Compliance Filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Risk Score Range
                </Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    type="number"
                    placeholder="Min (0-100)"
                    min="0"
                    max="100"
                    value={localFilters.riskScoreMin || ''}
                    onChange={(e) => updateFilter('riskScoreMin', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                  <Input
                    type="number"
                    placeholder="Max (0-100)"
                    min="0"
                    max="100"
                    value={localFilters.riskScoreMax || ''}
                    onChange={(e) => updateFilter('riskScoreMax', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                </div>
              </div>

              <div>
                <Label>Compliance Status</Label>
                <div className="space-y-2 mt-2">
                  {filterOptions?.complianceStatuses.map(status => (
                    <div key={status.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`compliance-${status.value}`}
                        checked={localFilters.complianceStatus?.includes(status.value) || false}
                        onCheckedChange={() => toggleArrayFilter('complianceStatus', status.value)}
                      />
                      <Label htmlFor={`compliance-${status.value}`} className="text-sm">
                        {status.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label>Special Flags</Label>
                <div className="space-y-2 mt-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="needs-review"
                      checked={localFilters.needsReview || false}
                      onCheckedChange={(checked) => updateFilter('needsReview', checked || undefined)}
                    />
                    <Label htmlFor="needs-review" className="text-sm">
                      Needs Review
                    </Label>
                  </div>
                </div>
              </div>
            </div>

            {/* Sorting */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Sort By</Label>
                <Select
                  value={localFilters.sortBy || 'full_name'}
                  onValueChange={(value) => updateFilter('sortBy', value)}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="full_name">Full Name</SelectItem>
                    <SelectItem value="username">Username</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="role">Role</SelectItem>
                    <SelectItem value="created_at">Created Date</SelectItem>
                    <SelectItem value="updated_at">Updated Date</SelectItem>
                    <SelectItem value="risk_score">Risk Score</SelectItem>
                    <SelectItem value="total_permissions">Permission Count</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Sort Order</Label>
                <Select
                  value={localFilters.sortOrder || 'asc'}
                  onValueChange={(value) => updateFilter('sortOrder', value as 'asc' | 'desc')}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Ascending</SelectItem>
                    <SelectItem value="desc">Descending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
