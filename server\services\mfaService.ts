import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import crypto from 'crypto';
import { db } from '../db';
import { 
  userMfaSettings, 
  mfaVerificationAttempts, 
  users, 
  companies,
  type InsertUserMfaSettings,
  type InsertMfaVerificationAttempt,
  type MFASetupData 
} from '@shared/schema';
import { eq, and } from 'drizzle-orm';

export class MFAService {
  /**
   * Generate MFA secret and QR code for user setup
   */
  async generateMFASecret(userId: number, userEmail: string): Promise<MFASetupData> {
    try {
      const secret = speakeasy.generateSecret({
        name: `TrackFina (${userEmail})`,
        issuer: 'TrackFina',
        length: 32
      });

      const backupCodes = this.generateBackupCodes();
      
      // Store in database (upsert)
      await db.insert(userMfaSettings).values({
        user_id: userId,
        secret: secret.base32,
        backup_codes: backupCodes,
        is_enabled: false
      }).onConflictDoUpdate({
        target: userMfaSettings.user_id,
        set: {
          secret: secret.base32,
          backup_codes: backupCodes,
          updated_at: new Date()
        }
      });

      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

      return {
        secret: secret.base32,
        qrCodeUrl,
        backupCodes
      };
    } catch (error) {
      console.error('Error generating MFA secret:', error);
      throw new Error('Failed to generate MFA secret');
    }
  }

  /**
   * Verify MFA token (TOTP or backup code)
   */
  async verifyMFAToken(
    userId: number, 
    token: string, 
    ipAddress?: string, 
    userAgent?: string
  ): Promise<boolean> {
    try {
      const [mfaSettings] = await db
        .select()
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, userId));

      if (!mfaSettings || !mfaSettings.secret) {
        await this.logVerificationAttempt(userId, 'totp', false, ipAddress, userAgent);
        return false;
      }

      let isValid = false;
      let attemptType: 'totp' | 'backup_code' = 'totp';

      // Try TOTP verification first (6 digits)
      if (token.length === 6 && /^\d{6}$/.test(token)) {
        isValid = speakeasy.totp.verify({
          secret: mfaSettings.secret,
          encoding: 'base32',
          token,
          window: 2 // Allow 2 time steps of variance (±60 seconds)
        });
      }

      // Try backup code verification if TOTP failed (8 characters)
      if (!isValid && token.length === 8 && /^[A-F0-9]{8}$/.test(token.toUpperCase())) {
        const backupCodes = mfaSettings.backup_codes as string[];
        if (backupCodes && backupCodes.includes(token.toUpperCase())) {
          isValid = true;
          attemptType = 'backup_code';
          
          // Remove used backup code
          const updatedCodes = backupCodes.filter(code => code !== token.toUpperCase());
          await db.update(userMfaSettings)
            .set({ 
              backup_codes: updatedCodes,
              updated_at: new Date()
            })
            .where(eq(userMfaSettings.user_id, userId));
        }
      }

      // Log verification attempt
      await this.logVerificationAttempt(userId, attemptType, isValid, ipAddress, userAgent);

      // Update last used timestamp if successful
      if (isValid) {
        await db.update(userMfaSettings)
          .set({ 
            last_used_at: new Date(),
            updated_at: new Date()
          })
          .where(eq(userMfaSettings.user_id, userId));
      }

      return isValid;
    } catch (error) {
      console.error('Error verifying MFA token:', error);
      await this.logVerificationAttempt(userId, 'totp', false, ipAddress, userAgent);
      return false;
    }
  }

  /**
   * Enable MFA for user after successful verification
   */
  async enableMFA(userId: number): Promise<void> {
    try {
      await db.update(userMfaSettings)
        .set({ 
          is_enabled: true, 
          updated_at: new Date() 
        })
        .where(eq(userMfaSettings.user_id, userId));
    } catch (error) {
      console.error('Error enabling MFA:', error);
      throw new Error('Failed to enable MFA');
    }
  }

  /**
   * Disable MFA for user
   */
  async disableMFA(userId: number): Promise<void> {
    try {
      await db.update(userMfaSettings)
        .set({ 
          is_enabled: false, 
          updated_at: new Date() 
        })
        .where(eq(userMfaSettings.user_id, userId));
    } catch (error) {
      console.error('Error disabling MFA:', error);
      throw new Error('Failed to disable MFA');
    }
  }

  /**
   * Check if MFA is required for user based on company policy
   */
  async isMFARequired(userId: number): Promise<boolean> {
    try {
      const [user] = await db
        .select({
          mfa_required: companies.mfa_required
        })
        .from(users)
        .leftJoin(companies, eq(users.company_id, companies.id))
        .where(eq(users.id, userId));

      return user?.mfa_required || false;
    } catch (error) {
      console.error('Error checking MFA requirement:', error);
      return false;
    }
  }

  /**
   * Check if user has MFA enabled
   */
  async isMFAEnabled(userId: number): Promise<boolean> {
    try {
      const [mfaSettings] = await db
        .select({ is_enabled: userMfaSettings.is_enabled })
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, userId));

      return mfaSettings?.is_enabled || false;
    } catch (error) {
      console.error('Error checking MFA status:', error);
      return false;
    }
  }

  /**
   * Get MFA status for user
   */
  async getMFAStatus(userId: number): Promise<{
    enabled: boolean;
    required: boolean;
    hasBackupCodes: boolean;
    backupCodesCount: number;
  }> {
    try {
      const [mfaSettings] = await db
        .select()
        .from(userMfaSettings)
        .where(eq(userMfaSettings.user_id, userId));

      const isRequired = await this.isMFARequired(userId);
      const backupCodes = (mfaSettings?.backup_codes as string[]) || [];

      return {
        enabled: mfaSettings?.is_enabled || false,
        required: isRequired,
        hasBackupCodes: backupCodes.length > 0,
        backupCodesCount: backupCodes.length
      };
    } catch (error) {
      console.error('Error getting MFA status:', error);
      return {
        enabled: false,
        required: false,
        hasBackupCodes: false,
        backupCodesCount: 0
      };
    }
  }

  /**
   * Generate new backup codes for user
   */
  async regenerateBackupCodes(userId: number): Promise<string[]> {
    try {
      const newBackupCodes = this.generateBackupCodes();
      
      await db.update(userMfaSettings)
        .set({ 
          backup_codes: newBackupCodes,
          updated_at: new Date()
        })
        .where(eq(userMfaSettings.user_id, userId));

      return newBackupCodes;
    } catch (error) {
      console.error('Error regenerating backup codes:', error);
      throw new Error('Failed to regenerate backup codes');
    }
  }

  /**
   * Generate backup codes (10 codes, 8 characters each)
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return codes;
  }

  /**
   * Log MFA verification attempt
   */
  private async logVerificationAttempt(
    userId: number,
    attemptType: 'totp' | 'backup_code',
    success: boolean,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      await db.insert(mfaVerificationAttempts).values({
        user_id: userId,
        attempt_type: attemptType,
        success,
        ip_address: ipAddress,
        user_agent: userAgent
      });
    } catch (error) {
      console.error('Error logging MFA verification attempt:', error);
      // Don't throw error here as it's just logging
    }
  }
}

export const mfaService = new MFAService();
