#!/usr/bin/env node
/**
 * Debug Script: Test API Endpoints
 * Purpose: Test the API endpoints that were causing 404 errors
 * Usage: node scripts/debug/test-api-endpoints.js
 */

import fetch from 'node-fetch';
import { setupEnvironment } from '../utils/env-loader.js';

// Load environment variables
setupEnvironment(['API_URL']);

const API_URL = process.env.API_URL || 'http://localhost:5000';

// List of endpoints that were causing 404 errors
const endpointsToTest = [
  {
    url: '/api/group-management/groups?company_id=13',
    method: 'GET',
    description: 'Group management groups'
  },
  {
    url: '/api/dashboard/permissions/categories',
    method: 'GET',
    description: 'Permission categories'
  },
  {
    url: '/api/role-hierarchy',
    method: 'GET',
    description: 'Role hierarchy'
  },
  {
    url: '/api/dashboard/permissions/analytics',
    method: 'GET',
    description: 'Permission analytics'
  },
  {
    url: '/api/roles/1/permissions',
    method: 'GET',
    description: 'Role permissions'
  },
  {
    url: '/api/roles/1/effective-permissions',
    method: 'GET',
    description: 'Role effective permissions'
  },
  {
    url: '/api/roles/1/temporary-permissions',
    method: 'GET',
    description: 'Role temporary permissions'
  }
];

async function testEndpoint(endpoint) {
  try {
    console.log(`\n🔍 Testing: ${endpoint.description}`);
    console.log(`   ${endpoint.method} ${endpoint.url}`);
    
    const response = await fetch(`${API_URL}${endpoint.url}`, {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        // Note: These endpoints should work without auth for demo purposes
      }
    });
    
    const status = response.status;
    const statusText = response.statusText;
    
    if (status === 200) {
      console.log(`   ✅ SUCCESS: ${status} ${statusText}`);
      
      try {
        const data = await response.json();
        if (data.note && data.note.includes('Demo data')) {
          console.log(`   📝 Note: ${data.note}`);
        }
        if (data.message) {
          console.log(`   💬 Message: ${data.message}`);
        }
      } catch (e) {
        console.log(`   📄 Response received (non-JSON)`);
      }
    } else if (status === 401) {
      console.log(`   🔐 AUTH REQUIRED: ${status} ${statusText} (Expected for some endpoints)`);
    } else if (status === 403) {
      console.log(`   🚫 FORBIDDEN: ${status} ${statusText} (Expected for some endpoints)`);
    } else if (status === 404) {
      console.log(`   ❌ NOT FOUND: ${status} ${statusText} (This should be fixed!)`);
    } else {
      console.log(`   ⚠️  OTHER: ${status} ${statusText}`);
    }
    
    return { endpoint, status, statusText, success: status < 400 };
    
  } catch (error) {
    console.log(`   💥 ERROR: ${error.message}`);
    return { endpoint, status: 'ERROR', statusText: error.message, success: false };
  }
}

async function testAllEndpoints() {
  console.log('=== API ENDPOINTS TEST ===');
  console.log(`Testing against: ${API_URL}`);
  console.log(`Total endpoints to test: ${endpointsToTest.length}`);
  
  const results = [];
  
  for (const endpoint of endpointsToTest) {
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n=== TEST SUMMARY ===');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  const notFound = results.filter(r => r.status === 404);
  const authRequired = results.filter(r => r.status === 401 || r.status === 403);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  console.log(`🔍 Not Found (404): ${notFound.length}/${results.length}`);
  console.log(`🔐 Auth Required: ${authRequired.length}/${results.length}`);
  
  if (notFound.length > 0) {
    console.log('\n🚨 ENDPOINTS STILL MISSING (404):');
    notFound.forEach(result => {
      console.log(`   - ${result.endpoint.method} ${result.endpoint.url}`);
      console.log(`     ${result.endpoint.description}`);
    });
  }
  
  if (authRequired.length > 0) {
    console.log('\n🔐 ENDPOINTS REQUIRING AUTH (Expected):');
    authRequired.forEach(result => {
      console.log(`   - ${result.endpoint.method} ${result.endpoint.url} (${result.status})`);
    });
  }
  
  if (successful.length > 0) {
    console.log('\n✅ WORKING ENDPOINTS:');
    successful.forEach(result => {
      console.log(`   - ${result.endpoint.method} ${result.endpoint.url} (${result.status})`);
    });
  }
  
  console.log('\n=== RECOMMENDATIONS ===');
  if (notFound.length === 0) {
    console.log('🎉 All endpoints are now available! No more 404 errors expected.');
  } else {
    console.log('⚠️  Some endpoints are still missing. Check the route registration.');
  }
  
  if (authRequired.length > 0) {
    console.log('🔐 Some endpoints require authentication. This is expected behavior.');
    console.log('   Use a valid JWT token in the Authorization header for authenticated requests.');
  }
  
  console.log('\n📋 NEXT STEPS:');
  console.log('1. If any endpoints are still 404, check route registration in server/routes/index.ts');
  console.log('2. For auth-required endpoints, implement proper authentication in the frontend');
  console.log('3. Test the frontend application to verify the 404 errors are resolved');
}

// Run the test
testAllEndpoints().catch(console.error);
