# Tools Directory

This directory contains development tools, test scripts, and utilities for the TrackFina application.

## Purpose

The tools directory is for scripts that:
- Test specific functionality or behavior
- Provide development utilities
- Help with debugging and troubleshooting
- Perform one-off analysis or validation tasks

## Scripts

### `test-collection-id.js`
Tests the collection ID generation behavior to ensure uniqueness across companies.

**Usage:**
```bash
node tools/test-collection-id.js
```

**Purpose:**
- Validates collection ID prefix generation logic
- Tests for potential conflicts between companies
- Provides recommendations for improving uniqueness

## Guidelines

### When to Add Scripts Here

Add scripts to the tools directory when they:
- Test or validate specific application behavior
- Provide development utilities that don't modify data
- Help with debugging or analysis
- Are used occasionally rather than as part of regular operations

### When NOT to Use This Directory

Don't add scripts here if they:
- Modify database schema or data (use `scripts/migrations/` instead)
- Are meant for production deployment (use appropriate deployment scripts)
- Are debugging browser-specific issues (use `scripts/debug/` instead)
- Are part of the build or CI/CD process (use appropriate build directories)

### Script Standards

All tools should:
1. Use ES modules (`import/export`)
2. Include proper documentation headers
3. Use shared utilities from `scripts/utils/`
4. Provide clear output and error messages
5. Be safe to run multiple times
6. Not modify production data without explicit confirmation

### Example Script Template

```javascript
#!/usr/bin/env node
/**
 * Tool Script: [Name]
 * Purpose: [What this tool does]
 * Usage: node tools/[script-name].js
 */

import { withDatabasePool } from '../scripts/utils/database-connection.js';

async function runTool() {
  await withDatabasePool(async (pool) => {
    console.log('=== [TOOL NAME] ===\n');
    
    // Tool logic here
    
    console.log('\n=== RESULTS ===');
    // Display results
    
    console.log('\n=== RECOMMENDATIONS ===');
    // Provide actionable insights
  });
}

runTool().catch(console.error);
```

## Contributing

When adding new tools:
1. Follow the established patterns and templates
2. Use descriptive names that clearly indicate the tool's purpose
3. Include comprehensive documentation
4. Test the tool thoroughly before committing
5. Update this README with information about the new tool
